{"version": 3, "file": "static/js/6117.2ac7b586.chunk.js", "mappings": "mPAGA,MA6PA,EA7PiBA,IAAmB,IAAlB,SAAEC,GAAUD,EAC1B,MAAM,EAAEE,IAAMC,EAAAA,EAAAA,MAERC,GAAqBC,EAAAA,EAAAA,SAAQ,KAAO,IAADC,EAoJrC,IAAIF,EAAqB,CACrBG,eAAgC,QAAlBD,EAPH,CACXE,MA7IO,CACH,SACA,WACA,CACIC,MAAO,SACPC,iBAAkB,QAyI1BC,IAnIO,CACH,SACA,WACA,CACIF,MAAO,OACPG,WAAW,GAEf,CACIH,MAAO,QACPG,WAAW,GAEf,CACIH,MAAO,SACPC,iBAAkB,QAuH1BG,OAjHO,CACH,SACA,WACA,CACIJ,MAAO,OACPG,WAAW,GAEf,CACIH,MAAO,QACPG,WAAW,GAEf,YACA,CACIH,MAAO,SACPK,WAAW,KAuGIb,UAAS,IAAAK,EAAAA,EAAI,GACpCS,gBAnGmBC,MAAO,IAADC,EAAAC,EACzB,MAWMC,EAAc,WACdC,EAA2C,QAAxBH,EAZL,CAChBT,MAAO,GACPG,IAAK,IAU+BV,UAAS,IAAAgB,EAAAA,EAAI,KAC/CI,EAAyC,QAAvBH,EARL,CACfV,MAAO,GACPG,IAAK,GACLE,OAAQ,KAK0BZ,UAAS,IAAAiB,EAAAA,EAAI,KACnD,IAAII,EAAS,GAuBb,MArBiB,WAAbrB,EAC4B,OAAxBmB,GACAE,EAAOC,KAAK,CACRd,MAAOU,EACPK,iBAAkBJ,IAI1BE,EAAOC,KAAK,CACRd,MAAOU,EACPL,WAAW,IAIQ,OAAvBO,GACAC,EAAOC,KAAK,CACRd,MAAO,OACPe,iBAAkBH,IAInBC,GA6DUN,GACjBS,gBA3DmBC,MAAO,IAADC,EACzB,IAAIL,EAAS,GAEb,MAKMM,EAA+C,QAA1BD,EALL,CAClBhB,IAAK,EACLE,OAAQ,IAGgCZ,UAAS,IAAA0B,EAAAA,EAAI,KASzD,OAP8B,OAA1BC,GACAN,EAAOC,KAAK,CACRd,MAAO,UACPoB,iBAAkBD,IAInBN,GA0CUI,GACjBI,SAxCgBC,MAAO,IAADC,EACtB,MAKMC,EAAc,CAChBC,cAAehC,EAAE,0CAGfiC,EAAiD,QAA3BH,EATL,CACnBxB,MAAO,IACPG,IAAK,KAOqCV,UAAS,IAAA+B,EAAAA,EAAI,KAC3D,IAAIF,EAAW,GAgBf,MAdiB,WAAb7B,EAC+B,OAA3BkC,GACAL,EAASP,KAAK,CACVa,KAAMH,EAAYC,cAClBxB,iBAAkByB,IAI1BL,EAASP,KAAK,CACVa,KAAMH,EAAYC,cAClBpB,WAAW,IAIZgB,GAaGC,IAGG,UAAb9B,SACOG,EAAmBqB,gBACnB,CAAC,aAAc,YAAYY,SAASpC,KAC3CG,EAAqB,IAGzB,MAAM6B,EAAc,CAChBrB,UAAWV,EAAE,oCACbY,UAAWZ,EAAE,qCACboC,OAAQpC,EAAE,kCACVqC,OAAQrC,EAAE,kCACVsC,OAAQtC,EAAE,kCACVuC,SAAUvC,EAAE,qCAGVwC,EAAiB,CAAC,YAAa,aAC/BC,EAAkB,CAAC,mBAAoB,mBAAoB,oBAEjE,OAAOC,OAAOC,YACVD,OAAOE,QAAQ1C,GAAoB2C,IAAIC,IAA+B,IAA7BC,EAASC,GAAcF,EAC5D,MAAMG,EAAoBjD,EAAE,uBAAuB+C,KA6BnD,OA3BAC,EAAgBA,EAAcH,IAAKK,IAC/B,GAAsB,iBAAXA,EAAqB,CAC5B,MAAMC,EAAmBX,EAAeY,KAAMC,GAAkBH,EAAOI,eAAeD,IAChFE,EAAoBd,EAAgBW,KAAMC,GAAkBH,EAAOI,eAAeD,IAExF,GAAIF,EAAkB,CAClB,MAAMK,EAAuBhB,EAAeiB,KAAMJ,GAAkBH,EAAOI,eAAeD,IAEtFH,EAAOM,KACPN,EAAOM,GAAwBzB,EAAYyB,GAEnD,MAAO,GAAID,EAAmB,CAC1B,MAAMG,EAAwBjB,EAAgBgB,KAAMJ,GAAkBH,EAAOI,eAAeD,IAExFH,EAAOQ,KACPR,EAAOQ,GAAyB,GAAGR,EAAOQ,MAA0B3B,EAAY2B,KAExF,EAEKR,EAAOI,eAAe,SAAWJ,EAAOI,eAAe,WACxDJ,EAAa,KAAIA,EAAO3C,MAEhC,CAEA,OAAO2C,IAGJ,CAACD,EAAmBD,OAGpC,CAACjD,EAAUC,IAEd,OACI2D,EAAAA,EAAAA,KAAAC,EAAAA,SAAA,CAAAC,SACKnB,OAAOE,QAAQ1C,GAAoB2C,IAAI,CAAAiB,EAAqCC,KAAK,IAAxCd,EAAmBD,GAAcc,EAAA,OACvEE,EAAAA,EAAAA,MAAA,MAAgBC,UAAU,SAAQJ,SAAA,EAC9BF,EAAAA,EAAAA,KAAA,MAAIM,UAAU,mCAAkCJ,SAAEZ,IAEjDD,EAAckB,OAAS,IACpBP,EAAAA,EAAAA,KAAA,MAAAE,SACKb,EAAcH,IAAI,CAACsB,EAAOJ,KACvBC,EAAAA,EAAAA,MAAA,MAAgBC,UAAU,oBAAmBJ,SAAA,EACzCF,EAAAA,EAAAA,KAAA,QAAMM,UAAU,qBAAoBJ,SAAC,MACnB,iBAAVM,GACJR,EAAAA,EAAAA,KAAA,QAAMM,UAAU,gBAAeJ,SAAEM,KAEjCR,EAAAA,EAAAA,KAAAC,EAAAA,SAAA,CAAAC,SACK,SAAUM,IACPH,EAAAA,EAAAA,MAAAJ,EAAAA,SAAA,CAAAC,SAAA,EACIG,EAAAA,EAAAA,MAAA,QAAMC,UAAU,gBAAeJ,SAAA,CAC1BM,EAAMjC,KACNiC,EAAMvD,YACHoD,EAAAA,EAAAA,MAAAJ,EAAAA,SAAA,CAAAC,SAAA,CACK,MACDF,EAAAA,EAAAA,KAAA,QAAMM,UAAU,4BAA2BJ,SAAEM,EAAMvD,kBAI9DuD,EAAMzD,YAAaiD,EAAAA,EAAAA,KAAA,QAAMM,UAAU,6DAA4DJ,SAAEM,EAAMzD,mBAjBnHqD,QANhBA,QC5MnBK,EAAYC,EAAAA,KAAW,IAAM,6EAo1BnC,QAl1BA,SAAkBC,GAChB,MAAMC,EAAOD,EAAMC,KAAOD,EAAMC,KAAO,KACjCC,EAAaF,EAAME,WAAaF,EAAME,WAAa,OACzD,IAAIC,GAAMC,EAAAA,EAAAA,IAAU,QAChBA,EAAAA,EAAAA,IAAU,OAERC,KAEFC,GAAYF,EAAAA,EAAAA,IAAU,cAAeA,EAAAA,EAAAA,IAAU,aAAe,SAElE,MACMG,KADqB,CAAC,KAAM,KAAM,KAAM,KAAM,MAAO,MAAO,OAC5B1C,SAASsC,KAAQH,EAAMO,aAAaP,EAAMO,WAC1EC,GAAYJ,EAAAA,EAAAA,IAAU,eAAgBA,EAAAA,EAAAA,IAAU,cAAgB,GAChEK,GAAaL,EAAAA,EAAAA,IAAU,eAAgBA,EAAAA,EAAAA,IAAU,cAAgB,MACjEM,GAAUN,EAAAA,EAAAA,IAAU,aAAcA,EAAAA,EAAAA,IAAU,YAAc,IACzDO,IAAcC,EAAAA,EAAAA,WAASR,EAAAA,EAAAA,IAAU,gBACjCS,EAAUC,IAAeF,EAAAA,EAAAA,WAAS,IACnC,SAAEG,IAAaC,EAAAA,EAAAA,MACfC,EAAiBhB,IAASA,EAAK,GAAGiB,UAAUC,cAAcC,WAAW,UAAY,CAAC,KAAM,MAAMvD,SAASsC,KACtGkB,IAAWT,EAAAA,EAAAA,WAAUR,EAAAA,EAAAA,IAAU,YAAaA,EAAAA,EAAAA,IAAU,WAAa,gBACnEkB,EAAaC,IAAkBX,EAAAA,EAAAA,UAAS,gCAAgCS,MACxEG,EAAkBC,IAAuBb,EAAAA,EAAAA,WAAS,IAClDc,EAAcC,IAAmBf,EAAAA,EAAAA,UAAS,YAG1CgB,EAAaC,IAAkBjB,EAAAA,EAAAA,UAAS,GACzCkB,GAAcC,EAAAA,EAAAA,QAAO,OACpBC,EAAYC,IAAiBrB,EAAAA,EAAAA,UAAS,OACtCsB,EAAUC,IAAevB,EAAAA,EAAAA,UAAS,OAClCwB,EAAWC,IAAgBzB,EAAAA,EAAAA,WAAS,IACpC0B,EAAcC,IAAmB3B,EAAAA,EAAAA,UAAS,IAEjD,IAAI4B,GAAsB,EAC1B,MAAM,EAAE9G,IAAMC,EAAAA,EAAAA,OAEd8G,EAAAA,EAAAA,WAAU,KACR,MAAMC,EAAkBA,KACtB5B,EAAY6B,OAAOC,YAAc,MAMnC,OAHAF,IACAC,OAAOE,iBAAiB,SAAUH,GAE3B,IAAMC,OAAOG,oBAAoB,SAAUJ,MAGpDD,EAAAA,EAAAA,WAAU,KACR,MAAMM,EAAWC,SAASD,SACpBE,EAAeF,EAAW,IAAIG,IAAIH,GAAUI,SAAW,IAQ3B,UAAhC/C,EAAAA,EAAAA,IAAU,mBANW,CACrB,+BACA,wBAKevC,SAASoF,MAExB1B,EAAe,2CACfE,GAAoB,KAErB,IAEH,MAAM2B,GAAoBC,EAAAA,EAAAA,aAAaC,IAChC/C,GACD+C,EAAKC,iBAAiBpC,gBAAkBO,EAE3C,CAACnB,EAAYmB,KAGhBe,EAAAA,EAAAA,WAAU,KACR,GAAIxC,EAAM,CACR,MAAMuD,EAAWvD,EAAKwD,OAAQH,GAASF,EAAkBE,IACzDf,EAAgBiB,GAEhBnB,EAAaM,OAAOC,YAAc,KAAOY,EAAS5D,OAAS,EAC7D,GACC,CAACK,EAAMyB,EAAcb,EAAUuC,KAGlCX,EAAAA,EAAAA,WAAU,KACR,MAAMiB,EAAeA,KACnB,GAAIzD,EAAM,CACR,MAAMuD,EAAWvD,EAAKwD,OAAQH,GAASF,EAAkBE,IACzDjB,EAAaM,OAAOC,YAAc,KAAOY,EAAS5D,OAAS,EAC7D,GAIF,OADA+C,OAAOE,iBAAiB,SAAUa,GAC3B,IAAMf,OAAOG,oBAAoB,SAAUY,IACjD,CAACzD,EAAMyB,EAAc0B,IAEZ,OAARjD,IACFqC,GAAsB,GAGR,OAAZ9B,GAAgC,OAAZA,IACtB8B,GAAsB,GAGxB,MAAOmB,EAAmBC,IAAwBhD,EAAAA,EAAAA,UAAS4B,EAAsB,SAAW,YAE5FC,EAAAA,EAAAA,WAAU,KACRd,EAAgBgC,IACf,CAACA,IAEJ,MA+CME,EAAgBA,KACpBxE,EAAAA,EAAAA,KAAA,OAAKM,UAAU,mEAAkEJ,UAC/EF,EAAAA,EAAAA,KAAA,OAAKM,UAAU,gDAA+CJ,UAC5DG,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCJ,SAAA,EAChDF,EAAAA,EAAAA,KAAA,MAAIM,UAAU,0BAAyBJ,SAAE7D,EAAE,8CAC3C2D,EAAAA,EAAAA,KAAA,KAAGM,UAAW,sCAAsD,WAAjB+B,GAAqC,OAARvB,EAAe,GAAK,QAASZ,SAC1G7D,EAAE,6CAEa,WAAjBgG,GAAqC,OAARvB,GAAuC,QAAvBC,EAAAA,EAAAA,IAAU,UACtDf,EAAAA,EAAAA,KAAA,OAAKM,UAAU,eAAcJ,SAAC,oBAE9B,IAEFF,EAAAA,EAAAA,KAAA,OAAKM,UAAU,OAAMJ,UACnBF,EAAAA,EAAAA,KAACyE,EAAAA,EAAOC,OAAM,CACZpE,UAAU,4CACVqE,MAAO,CAAEC,iBAAiBC,EAAAA,EAAAA,IAAQ5D,IAClC6D,WAAY,CAAEC,MAAO,KAAMH,iBAAiBI,EAAAA,EAAAA,IAAY/D,IACxDgE,SAAU,CAAEF,MAAO,IACnBG,QAASA,IAAMrE,EAAW,IAAIX,SAE7B7D,EAAE,8CAGP2D,EAAAA,EAAAA,KAAA,OAAKM,UAAW,4BAA2C,SAAfgB,EAAwB,YAAc,qBAAqBR,IAAMZ,UAC3GG,EAAAA,EAAAA,MAAA,MAAIC,UAAU,wBAAuBJ,SAAA,EACnCF,EAAAA,EAAAA,KAAA,MAAIM,UAAU,iBAAgBJ,UAC5BF,EAAAA,EAAAA,KAAA,OAAAE,SAAM7D,EAAE,gDAEV2D,EAAAA,EAAAA,KAAA,MAAIM,UAAU,GAAEJ,UACdF,EAAAA,EAAAA,KAAA,OAAAE,SAAM7D,EAAE,2DAgItB,OACE2D,EAAAA,EAAAA,KAAA,OAAKM,UAAU,iDAAgDJ,UAC7DF,EAAAA,EAAAA,KAAA,OAAKM,UAAW,0BAA0BJ,UACxCG,EAAAA,EAAAA,MAAA,OAAKC,UAAU,SAAQJ,SAAA,EACrBG,EAAAA,EAAAA,MAAA,OAAKC,UAAU,2BAA0BJ,SAAA,EACrC0B,IACA5B,EAAAA,EAAAA,KAAA,OAAKM,UAAW,sBAAsBJ,UACpCG,EAAAA,EAAAA,MAAA,OAAKC,UAAU,0BAAyBJ,SAAA,EACtCF,EAAAA,EAAAA,KAAA,MAAIM,UAAU,0IAAyIJ,SAAC,kBAGxJF,EAAAA,EAAAA,KAAA,OAAKM,UAAW,4BAA2B6B,EAAmB,sBAAwB,IAAKjC,SACxF+B,SAKRf,IACClB,EAAAA,EAAAA,KAAA,OAAKM,UAAU,MAAKJ,UAClBF,EAAAA,EAAAA,KAAA,OAAKM,UAAU,+CAA8CJ,UAC3DG,EAAAA,EAAAA,MAAA,SAAO8E,QAAQ,UAAU7E,UAAU,mCAAkCJ,SAAA,EACnEF,EAAAA,EAAAA,KAAA,OACEM,WAAoC,YAAtBgE,EAAkC,0BAA4B,iBAAjE,kBAAkGpE,SAC9G,aAGDG,EAAAA,EAAAA,MAAA,OAAKC,UAAU,WAAUJ,SAAA,EACvBF,EAAAA,EAAAA,KAAA,SACEoF,KAAK,WACLC,GAAG,UACH/E,UAAU,iBACVgF,SA5OCC,KAEnBhB,EADwB,YAAtBD,EACmB,SAEA,WAEvB9B,EAAe,IAuOKgD,eAAgBrC,KAElBnD,EAAAA,EAAAA,KAAA,OAAKM,UAAU,8EACfN,EAAAA,EAAAA,KAAA,OACEM,UAAW,6FACa,WAAtBgE,EAAiC,gBAAkB,gBAIzDtE,EAAAA,EAAAA,KAAA,OACEM,WAAoC,WAAtBgE,EAAiC,0BAA4B,iBAAhE,kBAAiGpE,SAC7G,sBAQXF,EAAAA,EAAAA,KAAA,OACEM,UAAW,gEAA+D6B,EAAmB,aAAe,IAAKjC,UAEjHF,EAAAA,EAAAA,KAAA,OAAKM,UAAW,2BAA0BsB,EAAiB,wBAA0B,cAAe1B,UAChGF,EAAAA,EAAAA,KAAAC,EAAAA,SAAA,CAAAC,SACG6C,GACC1C,EAAAA,EAAAA,MAAA,OAAKC,UAAU,WAAUJ,SAAA,EAEvBF,EAAAA,EAAAA,KAAA,UACEkF,QAtODO,KACflD,EAAc,GAChBC,EAAgBkD,GAASA,EAAO,IAqOdpF,UAAW,gGACO,IAAhBiC,EAAoB,gCAAkC,eAExDoD,SAA0B,IAAhBpD,EACV,aAAW,gBAAerC,UAE1BF,EAAAA,EAAAA,KAAA,OACE4F,MAAM,6BACNC,MAAM,KACNC,OAAO,KACPC,QAAQ,YACRC,KAAK,OACLC,OAAO,eACPC,YAAY,IACZC,cAAc,QACdC,eAAe,QACf9F,UAAU,gBAAeJ,UAEzBF,EAAAA,EAAAA,KAAA,YAAUqG,OAAO,yBAGrBrG,EAAAA,EAAAA,KAAA,UACEkF,QAvPDoB,KACf/D,EAAcU,EAAa1C,OAAS,GACtCiC,EAAgBkD,GAASA,EAAO,IAsPdpF,UAAW,iGACTiC,IAAgBU,EAAa1C,OAAS,EAAI,gCAAkC,eAE9EoF,SAAUpD,IAAgBU,EAAa1C,OAAS,EAChD,aAAW,YAAWL,UAEtBF,EAAAA,EAAAA,KAAA,OACE4F,MAAM,6BACNC,MAAM,KACNC,OAAO,KACPC,QAAQ,YACRC,KAAK,OACLC,OAAO,eACPC,YAAY,IACZC,cAAc,QACdC,eAAe,QACf9F,UAAU,gBAAeJ,UAEzBF,EAAAA,EAAAA,KAAA,YAAUqG,OAAO,wBAKrBrG,EAAAA,EAAAA,KAAA,OACEuG,IAAK9D,EACLnC,UAAU,kBACVkG,aA/SAC,IACpB3D,EAAY,MACZF,EAAc6D,EAAEC,cAAc,GAAGC,UA8SbC,YA3SDH,IACnB3D,EAAY2D,EAAEC,cAAc,GAAGC,UA2SXE,WAxSHA,KACjB,IAAKlE,IAAeE,EAAU,OAC9B,MAAMiE,EAAWnE,EAAaE,EAExBkE,EAAeD,GAfE,GAcHA,EAdG,IAiBJvE,EAAcU,EAAa1C,OAAS,GACrDiC,EAAgBkD,GAASA,EAAO,GAE9BqB,GAAgBxE,EAAc,GAChCC,EAAgBkD,GAASA,EAAO,IA8RSxF,UAEvBF,EAAAA,EAAAA,KAAA,OACEM,UAAU,qDACVqE,MAAO,CAAEqC,UAAW,eAA6B,IAAdzE,OAAwBrC,SAE1D+C,EAAa/D,IAAI,CAAC+E,EAAM7D,KACvBJ,EAAAA,EAAAA,KAAA,OAAiBM,UAAU,uBAAsBJ,UAC/CG,EAAAA,EAAAA,MAAA,OAAKC,UAAU,mCAAkCJ,SAAA,EAC/CF,EAAAA,EAAAA,KAAA,OAAKM,UAAU,uEAAsEJ,UACnFG,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6CAA4CJ,SAAA,CAE1D0B,IACC5B,EAAAA,EAAAA,KAAA,OAAKM,UAAW,sBAAsBJ,UACpCG,EAAAA,EAAAA,MAAA,OAAKC,UAAU,0BAAyBJ,SAAA,EACtCF,EAAAA,EAAAA,KAAA,MAAIM,UAAW,8HAA8HJ,SAAC,4BAG9IF,EAAAA,EAAAA,KAAA,OAAKM,UAAW,4BAA2B6B,EAAmB,sBAAwB,IAAKjC,SACxF+B,UAMPjC,EAAAA,EAAAA,KAAA,MAAIM,UAAU,2CAA0CJ,SACrD+D,EAAKgD,QAWgB,QAAvBlG,EAAAA,EAAAA,IAAU,UACTf,EAAAA,EAAAA,KAACkH,EAAAA,GAAU,CAACjD,KAAMA,KAChBd,GACwB,WAA1Bc,EAAKC,kBACI,OAARpD,GAA4B,OAAZO,EAKf4C,EAAKkD,aACPnH,EAAAA,EAAAA,KAAA,KAAGM,UAAU,6BAA4BJ,UACtCkH,EAAAA,EAAAA,IAAanD,EAAKoD,SAAUpD,EAAKkD,gBAGpC9G,EAAAA,EAAAA,MAAA,KAAGC,UAAU,6BAA4BJ,SAAA,CACzB,OAAZmB,GAAgC,OAAZA,GAA+C,WAA1B4C,EAAKC,kBAE5CkD,EAAAA,EAAAA,IAAanD,EAAKoD,SAAUpD,EAAKqD,QADjCF,EAAAA,EAAAA,IAAanD,EAAKoD,SAAUE,OAAOC,WAAWvD,EAAKqD,MAAQ,IAAIG,QAAQ,KAE3EpH,EAAAA,EAAAA,MAAA,QAAMC,UAAU,wBAAuBJ,SAAA,CAAC,IAExB,OAAZmB,GAAgC,OAAZA,GAA+C,WAA1B4C,EAAKC,iBAElB,YAA1BD,EAAKC,iBACH,SACA,QAHF,gBAhBR7D,EAAAA,EAAAA,MAAA,KAAGC,UAAU,6BAA4BJ,SAAA,EACtCkH,EAAAA,EAAAA,IAAanD,EAAKoD,SAAUE,OAAOC,WAAWvD,EAAKqD,MAAQ,IAAIG,QAAQ,KACxEzH,EAAAA,EAAAA,KAAA,QAAMM,UAAU,wBAAuBJ,SAAC,gBAsB5CF,EAAAA,EAAAA,KAACyE,EAAAA,EAAOC,OAAM,CACZpE,UAAU,kJACVwE,WAAY,CAAEC,MAAO,MACrBE,SAAU,CAAEF,MAAO,KACnBG,QAASA,IAAMrE,EAAWoD,EAAKyD,SAASxH,SACzC,cAIa,OAAZmB,GAAgC,OAAZA,IACM,WAA1B4C,EAAKC,kBACkB,QAAvBnD,EAAAA,EAAAA,IAAU,WACRV,EAAAA,EAAAA,MAAA,QAAMC,UAAU,mCAAkCJ,SAAA,CAAC,OAC7CkH,EAAAA,EAAAA,IAAanD,EAAKoD,SAAUpD,EAAKqD,OAAO,cAG/CnE,GACyB,WAA1Bc,EAAKC,mBACJ,CAAC,KAAM,KAAM,MAAM1F,SAAS6C,IACN,QAAvBN,EAAAA,EAAAA,IAAU,WACR,CAAC,KAAM,KAAM,MAAMvC,SAAS6C,IACpB,KAARP,GACuB,QAAvBC,EAAAA,EAAAA,IAAU,YACZf,EAAAA,EAAAA,KAAA,OAAKM,UAAU,eAAcJ,SAAC,qBAGhCF,EAAAA,EAAAA,KAAA,OAAKM,UAAU,oCAAmCJ,UAEhDG,EAAAA,EAAAA,MAAA,MAAIC,UAAU,mBAAkBJ,SAAA,CACV,UAAnB+D,EAAK0D,YACJtH,EAAAA,EAAAA,MAAAJ,EAAAA,SAAA,CAAAC,SAAA,EAEEG,EAAAA,EAAAA,MAAA,MAAIC,UAAU,SAAQJ,SAAA,EACpBF,EAAAA,EAAAA,KAAA,MAAIM,UAAU,mCAAkCJ,SAAC,qBACjDG,EAAAA,EAAAA,MAAA,MAAIC,UAAU,UAASJ,SAAA,EACrBG,EAAAA,EAAAA,MAAA,MAAIC,UAAU,oBAAmBJ,SAAA,EAC/BF,EAAAA,EAAAA,KAAA,QAAMM,UAAU,qBAAoBJ,SAAC,OACrCF,EAAAA,EAAAA,KAAA,QAAMM,UAAU,gBAAeJ,SAAC,eAElCG,EAAAA,EAAAA,MAAA,MAAIC,UAAU,oBAAmBJ,SAAA,EAC/BF,EAAAA,EAAAA,KAAA,QAAMM,UAAU,qBAAoBJ,SAAC,OACrCF,EAAAA,EAAAA,KAAA,QAAMM,UAAU,gBAAeJ,SAAC,iBAElCG,EAAAA,EAAAA,MAAA,MAAIC,UAAU,oBAAmBJ,SAAA,EAC/BF,EAAAA,EAAAA,KAAA,QAAMM,UAAU,qBAAoBJ,SAAC,OACrCG,EAAAA,EAAAA,MAAA,QAAMC,UAAU,gBAAeJ,SAAA,CAAC,UACtB,KACRG,EAAAA,EAAAA,MAAA,QAAMC,UAAU,gBAAeJ,SAAA,CAAC,aACnBwB,IAAaE,EAAiB,OAAS,yBAQ5DvB,EAAAA,EAAAA,MAAA,MAAIC,UAAU,SAAQJ,SAAA,EACpBF,EAAAA,EAAAA,KAAA,MAAIM,UAAU,mCAAkCJ,SAAC,sBACjDG,EAAAA,EAAAA,MAAA,MAAIC,UAAU,UAASJ,SAAA,EACrBG,EAAAA,EAAAA,MAAA,MAAIC,UAAU,oBAAmBJ,SAAA,EAC/BF,EAAAA,EAAAA,KAAA,QAAMM,UAAU,qBAAoBJ,SAAC,OACrCG,EAAAA,EAAAA,MAAA,QAAMC,UAAU,gBAAeJ,SAAA,CAAC,YACpB,KACVG,EAAAA,EAAAA,MAAA,QAAMC,UAAU,gBAAeJ,SAAA,CAAC,YACpBwB,IAAaE,EAAiB,OAAS,mBAIvDvB,EAAAA,EAAAA,MAAA,MAAIC,UAAU,oBAAmBJ,SAAA,EAC/BF,EAAAA,EAAAA,KAAA,QAAMM,UAAU,qBAAoBJ,SAAC,OACrCG,EAAAA,EAAAA,MAAA,QAAMC,UAAU,gBAAeJ,SAAA,CAAC,QACxB,KACNG,EAAAA,EAAAA,MAAA,QAAMC,UAAU,gBAAeJ,SAAA,CAAC,YACpBwB,IAAaE,EAAiB,OAAS,yBAQ3DvB,EAAAA,EAAAA,MAAA,MAAIC,UAAU,SAAQJ,SAAA,EACpBF,EAAAA,EAAAA,KAAA,MAAIM,UAAU,yCAAwCJ,SAAC,cACvDG,EAAAA,EAAAA,MAAA,MAAIC,UAAU,UAASJ,SAAA,EACrBG,EAAAA,EAAAA,MAAA,MAAIC,UAAU,oBAAmBJ,SAAA,EAC/BF,EAAAA,EAAAA,KAAA,QAAMM,UAAU,qBAAoBJ,SAAC,OACrCG,EAAAA,EAAAA,MAAA,QAAMC,UAAU,gBAAeJ,SAAA,CAAC,kBACd,KAChBG,EAAAA,EAAAA,MAAA,QAAMC,UAAU,uBAAsBJ,SAAA,CAAC,iBACtBwB,IAAaE,EAAiB,GAAK,kBAIvDA,IACCvB,EAAAA,EAAAA,MAAA,MAAIC,UAAU,oBAAmBJ,SAAA,EAC/BF,EAAAA,EAAAA,KAAA,QAAMM,UAAU,qBAAoBJ,SAAC,OACrCG,EAAAA,EAAAA,MAAA,QAAMC,UAAU,gBAAeJ,SAAA,CAAE+D,EAAK2D,WAAW,0BAAuBR,EAAAA,EAAAA,IAAanD,EAAKoD,SAAUpD,EAAKqD,OAAO,wBAQxG,QAAnBrD,EAAK0D,YACJtH,EAAAA,EAAAA,MAAAJ,EAAAA,SAAA,CAAAC,SAAA,EAEEG,EAAAA,EAAAA,MAAA,MAAIC,UAAU,SAAQJ,SAAA,EACpBF,EAAAA,EAAAA,KAAA,MAAIM,UAAU,mCAAkCJ,SAAC,qBACjDG,EAAAA,EAAAA,MAAA,MAAIC,UAAU,UAASJ,SAAA,EACrBG,EAAAA,EAAAA,MAAA,MAAIC,UAAU,oBAAmBJ,SAAA,EAC/BF,EAAAA,EAAAA,KAAA,QAAMM,UAAU,qBAAoBJ,SAAC,OACrCF,EAAAA,EAAAA,KAAA,QAAMM,UAAU,gBAAeJ,SAAC,eAElCG,EAAAA,EAAAA,MAAA,MAAIC,UAAU,oBAAmBJ,SAAA,EAC/BF,EAAAA,EAAAA,KAAA,QAAMM,UAAU,qBAAoBJ,SAAC,OACrCF,EAAAA,EAAAA,KAAA,QAAMM,UAAU,gBAAeJ,SAAC,iBAElCG,EAAAA,EAAAA,MAAA,MAAIC,UAAU,oBAAmBJ,SAAA,EAC/BF,EAAAA,EAAAA,KAAA,QAAMM,UAAU,qBAAoBJ,SAAC,OACrCF,EAAAA,EAAAA,KAAA,QAAMM,UAAU,gBAAeJ,SAAC,UAChCF,EAAAA,EAAAA,KAAA,QAAMM,UAAU,6DAA4DJ,SAAC,YAI/EG,EAAAA,EAAAA,MAAA,MAAIC,UAAU,oBAAmBJ,SAAA,EAC/BF,EAAAA,EAAAA,KAAA,QAAMM,UAAU,qBAAoBJ,SAAC,OACrCF,EAAAA,EAAAA,KAAA,QAAMM,UAAU,gBAAeJ,SAAC,WAChCF,EAAAA,EAAAA,KAAA,QAAMM,UAAU,6DAA4DJ,SAAC,YAI/EG,EAAAA,EAAAA,MAAA,MAAIC,UAAU,oBAAmBJ,SAAA,EAC/BF,EAAAA,EAAAA,KAAA,QAAMM,UAAU,qBAAoBJ,SAAC,OACrCG,EAAAA,EAAAA,MAAA,QAAMC,UAAU,gBAAeJ,SAAA,CAAC,UACtB,KACRG,EAAAA,EAAAA,MAAA,QAAMC,UAAU,gBAAeJ,SAAA,CAAC,aACnBwB,IAAaE,EAAiB,OAAS,yBAQ5DvB,EAAAA,EAAAA,MAAA,MAAIC,UAAU,SAAQJ,SAAA,EACpBF,EAAAA,EAAAA,KAAA,MAAIM,UAAU,mCAAkCJ,SAAC,sBACjDG,EAAAA,EAAAA,MAAA,MAAIC,UAAU,UAASJ,SAAA,EACrBG,EAAAA,EAAAA,MAAA,MAAIC,UAAU,oBAAmBJ,SAAA,EAC/BF,EAAAA,EAAAA,KAAA,QAAMM,UAAU,qBAAoBJ,SAAC,OACrCG,EAAAA,EAAAA,MAAA,QAAMC,UAAU,gBAAeJ,SAAA,CAAC,YACpB,KACVG,EAAAA,EAAAA,MAAA,QAAMC,UAAU,gBAAeJ,SAAA,CAAC,YACpBwB,IAAaE,EAAiB,OAAS,mBAIvDvB,EAAAA,EAAAA,MAAA,MAAIC,UAAU,oBAAmBJ,SAAA,EAC/BF,EAAAA,EAAAA,KAAA,QAAMM,UAAU,qBAAoBJ,SAAC,OACrCG,EAAAA,EAAAA,MAAA,QAAMC,UAAU,gBAAeJ,SAAA,CAAC,QACxB,KACNG,EAAAA,EAAAA,MAAA,QAAMC,UAAU,gBAAeJ,SAAA,CAAC,YACpBwB,IAAaE,EAAiB,OAAS,yBAQ3DvB,EAAAA,EAAAA,MAAA,MAAIC,UAAU,SAAQJ,SAAA,EACpBF,EAAAA,EAAAA,KAAA,MAAIM,UAAU,mCAAkCJ,SAAC,sBACjDF,EAAAA,EAAAA,KAAA,MAAIM,UAAU,UAASJ,UACrBG,EAAAA,EAAAA,MAAA,MAAIC,UAAU,oBAAmBJ,SAAA,EAC/BF,EAAAA,EAAAA,KAAA,QAAMM,UAAU,qBAAoBJ,SAAC,OACrCG,EAAAA,EAAAA,MAAA,QAAMC,UAAU,gBAAeJ,SAAA,CAAC,WACrB,KACTG,EAAAA,EAAAA,MAAA,QAAMC,UAAU,gBAAeJ,SAAA,CAAC,WACrBwB,IAAaE,EAAiB,OAAS,wBAQ1DvB,EAAAA,EAAAA,MAAA,MAAIC,UAAU,SAAQJ,SAAA,EACpBF,EAAAA,EAAAA,KAAA,MAAIM,UAAU,yCAAwCJ,SAAC,cACvDG,EAAAA,EAAAA,MAAA,MAAIC,UAAU,UAASJ,SAAA,EACrBG,EAAAA,EAAAA,MAAA,MAAIC,UAAU,oBAAmBJ,SAAA,EAC/BF,EAAAA,EAAAA,KAAA,QAAMM,UAAU,qBAAoBJ,SAAC,OACrCG,EAAAA,EAAAA,MAAA,QAAMC,UAAU,gBAAeJ,SAAA,CAAC,kBACd,KAChBG,EAAAA,EAAAA,MAAA,QAAMC,UAAU,gBAAeJ,SAAA,CAAC,mBACbwB,IAAaE,EAAiB,GAAK,kBAIzDA,IACCvB,EAAAA,EAAAA,MAAA,MAAIC,UAAU,oBAAmBJ,SAAA,EAC/BF,EAAAA,EAAAA,KAAA,QAAMM,UAAU,qBAAoBJ,SAAC,OACrCG,EAAAA,EAAAA,MAAA,QAAMC,UAAU,gBAAeJ,SAAA,CAAE+D,EAAK2D,WAAW,0BAAuBR,EAAAA,EAAAA,IAAanD,EAAKoD,SAAUpD,EAAKqD,OAAO,wBAQxG,WAAnBrD,EAAK0D,YACJtH,EAAAA,EAAAA,MAAAJ,EAAAA,SAAA,CAAAC,SAAA,EAEEG,EAAAA,EAAAA,MAAA,MAAIC,UAAU,SAAQJ,SAAA,EACpBF,EAAAA,EAAAA,KAAA,MAAIM,UAAU,mCAAkCJ,SAAC,qBACjDG,EAAAA,EAAAA,MAAA,MAAIC,UAAU,UAASJ,SAAA,EACrBG,EAAAA,EAAAA,MAAA,MAAIC,UAAU,oBAAmBJ,SAAA,EAC/BF,EAAAA,EAAAA,KAAA,QAAMM,UAAU,qBAAoBJ,SAAC,OACrCF,EAAAA,EAAAA,KAAA,QAAMM,UAAU,gBAAeJ,SAAC,eAElCG,EAAAA,EAAAA,MAAA,MAAIC,UAAU,oBAAmBJ,SAAA,EAC/BF,EAAAA,EAAAA,KAAA,QAAMM,UAAU,qBAAoBJ,SAAC,OACrCF,EAAAA,EAAAA,KAAA,QAAMM,UAAU,gBAAeJ,SAAC,iBAElCG,EAAAA,EAAAA,MAAA,MAAIC,UAAU,oBAAmBJ,SAAA,EAC/BF,EAAAA,EAAAA,KAAA,QAAMM,UAAU,qBAAoBJ,SAAC,OACrCF,EAAAA,EAAAA,KAAA,QAAMM,UAAU,gBAAeJ,SAAC,UAChCF,EAAAA,EAAAA,KAAA,QAAMM,UAAU,6DAA4DJ,SAAC,YAI/EG,EAAAA,EAAAA,MAAA,MAAIC,UAAU,oBAAmBJ,SAAA,EAC/BF,EAAAA,EAAAA,KAAA,QAAMM,UAAU,qBAAoBJ,SAAC,OACrCF,EAAAA,EAAAA,KAAA,QAAMM,UAAU,gBAAeJ,SAAC,WAChCF,EAAAA,EAAAA,KAAA,QAAMM,UAAU,6DAA4DJ,SAAC,YAI/EG,EAAAA,EAAAA,MAAA,MAAIC,UAAU,oBAAmBJ,SAAA,EAC/BF,EAAAA,EAAAA,KAAA,QAAMM,UAAU,qBAAoBJ,SAAC,OACrCF,EAAAA,EAAAA,KAAA,QAAMM,UAAU,gBAAeJ,SAAC,kBAElCG,EAAAA,EAAAA,MAAA,MAAIC,UAAU,oBAAmBJ,SAAA,EACjCF,EAAAA,EAAAA,KAAA,QAAMM,UAAU,qBAAoBJ,SAAC,OACnCG,EAAAA,EAAAA,MAAA,QAAMC,UAAU,gBAAeJ,SAAA,CAAC,YACtBF,EAAAA,EAAAA,KAAA,QAAMM,UAAU,4BAA2BJ,SAAC,2BAO5DG,EAAAA,EAAAA,MAAA,MAAIC,UAAU,SAAQJ,SAAA,EACpBF,EAAAA,EAAAA,KAAA,MAAIM,UAAU,mCAAkCJ,SAAC,sBACjDG,EAAAA,EAAAA,MAAA,MAAIC,UAAU,UAASJ,SAAA,EACrBG,EAAAA,EAAAA,MAAA,MAAIC,UAAU,oBAAmBJ,SAAA,EAC/BF,EAAAA,EAAAA,KAAA,QAAMM,UAAU,qBAAoBJ,SAAC,OACrCG,EAAAA,EAAAA,MAAA,QAAMC,UAAU,gBAAeJ,SAAA,CAAC,YACpB,KACVF,EAAAA,EAAAA,KAAA,QAAMM,UAAU,4BAA2BJ,SAAC,qBAGhDG,EAAAA,EAAAA,MAAA,MAAIC,UAAU,oBAAmBJ,SAAA,EAC/BF,EAAAA,EAAAA,KAAA,QAAMM,UAAU,qBAAoBJ,SAAC,OACrCG,EAAAA,EAAAA,MAAA,QAAMC,UAAU,gBAAeJ,SAAA,CAAC,QACxB,KACNG,EAAAA,EAAAA,MAAA,QAAMC,UAAU,gBAAeJ,SAAA,CAAC,aACnBwB,IAAaE,EAAiB,OAAS,yBAQ5DvB,EAAAA,EAAAA,MAAA,MAAIC,UAAU,SAAQJ,SAAA,EACpBF,EAAAA,EAAAA,KAAA,MAAIM,UAAU,mCAAkCJ,SAAC,sBACjDF,EAAAA,EAAAA,KAAA,MAAIM,UAAU,UAASJ,UACrBG,EAAAA,EAAAA,MAAA,MAAIC,UAAU,oBAAmBJ,SAAA,EAC/BF,EAAAA,EAAAA,KAAA,QAAMM,UAAU,qBAAoBJ,SAAC,OACrCG,EAAAA,EAAAA,MAAA,QAAMC,UAAU,gBAAeJ,SAAA,CAAC,WACrB,KACTG,EAAAA,EAAAA,MAAA,QAAMC,UAAU,gBAAeJ,SAAA,CAAC,YACpBwB,IAAaE,EAAiB,OAAS,wBAQ3DvB,EAAAA,EAAAA,MAAA,MAAIC,UAAU,SAAQJ,SAAA,EACpBF,EAAAA,EAAAA,KAAA,MAAIM,UAAU,yCAAwCJ,SAAC,cACvDF,EAAAA,EAAAA,KAAA,MAAIM,UAAU,UAASJ,UACrBG,EAAAA,EAAAA,MAAA,MAAIC,UAAU,oBAAmBJ,SAAA,EAC/BF,EAAAA,EAAAA,KAAA,QAAMM,UAAU,qBAAoBJ,SAAC,OACrCG,EAAAA,EAAAA,MAAA,QAAMC,UAAU,gBAAeJ,SAAA,CAAC,kBACd,KAChBF,EAAAA,EAAAA,KAAA,QAAMM,UAAU,4BAA2BJ,SAAC,sCAW5C,QAAnB+D,EAAK0D,WAAwB/F,EAK1B,MAJFvB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6OAA4OJ,SAAA,EACzPF,EAAAA,EAAAA,KAAC6H,EAAAA,IAAc,CAACvH,UAAU,2BAA2B,gBAAc,KACnEN,EAAAA,EAAAA,KAAC6H,EAAAA,IAAc,CAACvH,UAAU,kCAlXxBF,SA4XhBJ,EAAAA,EAAAA,KAAA,OAAKM,UAAU,qCAAoCJ,SAChD+C,EAAa/D,IAAI,CAAC4I,EAAG1H,KACpBJ,EAAAA,EAAAA,KAAA,UAEEM,UAAW,yBAAwBF,IAAUmC,EAAc,cAAgB,eAC3E2C,QAASA,IAAM1C,EAAepC,GAC9B,aAAY,cAAcA,EAAQ,KAH7BA,UASbC,EAAAA,EAAAA,MAAA,OAAKC,UAAW,iEAAiEJ,SAAA,CAC9EU,aAAI,EAAJA,EAAM1B,IAAI,CAAC+E,EAAM7D,IAAW2D,EAAkBE,GA9lB5C8D,EAAC9D,EAAM7D,KAC5BC,EAAAA,EAAAA,MAAA,OAAiBC,UAAW,0CAA0CsB,EAAgE,gCAA/C,8CAAiF1B,SAAA,EACtKF,EAAAA,EAAAA,KAAA,OAAKM,UAAU,uEAAsEJ,UACnFG,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6CAA4CJ,SAAA,CAExD0B,IACC5B,EAAAA,EAAAA,KAAA,OAAKM,UAAW,sBAAsBJ,UACpCG,EAAAA,EAAAA,MAAA,OAAKC,UAAU,0BAAyBJ,SAAA,EACtCF,EAAAA,EAAAA,KAAA,MAAIM,UAAW,oIAAoIJ,SAAC,4BAGpJF,EAAAA,EAAAA,KAAA,OAAKM,UAAW,iDAAgD6B,EAAmB,sBAAwB,IAAKjC,SAC7G+B,UAKTjC,EAAAA,EAAAA,KAAA,MAAIM,UAAW,SAASsB,EAAyD,sCAAxC,uCAAgF1B,SACtH+D,EAAKgD,QAWgB,QAAvBlG,EAAAA,EAAAA,IAAU,UACTf,EAAAA,EAAAA,KAACkH,EAAAA,GAAU,CAACjD,KAAMA,KAChBd,GAAiD,WAA1Bc,EAAKC,kBAA0C,OAARpD,GAA4B,OAAZO,EAK9E4C,EAAKkD,aACPnH,EAAAA,EAAAA,KAAA,KAAGM,UAAU,6BAA4BJ,UAAEkH,EAAAA,EAAAA,IAAanD,EAAKoD,SAAUpD,EAAKkD,gBAE5E9G,EAAAA,EAAAA,MAAA,KAAGC,UAAU,6BAA4BJ,SAAA,CACzB,OAAZmB,GAAgC,OAAZA,GAA+C,WAA1B4C,EAAKC,kBAE5CkD,EAAAA,EAAAA,IAAanD,EAAKoD,SAAUpD,EAAKqD,QADjCF,EAAAA,EAAAA,IAAanD,EAAKoD,SAAUE,OAAOC,WAAWvD,EAAKqD,MAAQ,IAAIG,QAAQ,KAE3EpH,EAAAA,EAAAA,MAAA,QAAMC,UAAU,wBAAuBJ,SAAA,CAAC,IAExB,OAAZmB,GAAgC,OAAZA,GAA+C,WAA1B4C,EAAKC,iBAElB,YAA1BD,EAAKC,iBACH,SACA,QAHF,gBAdR7D,EAAAA,EAAAA,MAAA,KAAGC,UAAU,6BAA4BJ,SAAA,EACtCkH,EAAAA,EAAAA,IAAanD,EAAKoD,SAAUE,OAAOC,WAAWvD,EAAKqD,MAAQ,IAAIG,QAAQ,KACxEzH,EAAAA,EAAAA,KAAA,QAAMM,UAAU,wBAAuBJ,SAAC,gBAoB5CF,EAAAA,EAAAA,KAACyE,EAAAA,EAAOC,OAAM,CACZpE,UAAU,kJACVwE,WAAY,CAAEC,MAAO,MACrBE,SAAU,CAAEF,MAAO,KACnBG,QAASA,IAAMrE,EAAWoD,EAAKyD,SAASxH,SACzC,cAIa,OAAZmB,GAAgC,OAAZA,IACM,WAA1B4C,EAAKC,kBACkB,QAAvBnD,EAAAA,EAAAA,IAAU,WACRV,EAAAA,EAAAA,MAAA,QAAMC,UAAU,mCAAkCJ,SAAA,CAAC,OAC7CkH,EAAAA,EAAAA,IAAanD,EAAKoD,SAAUpD,EAAKqD,OAAO,cAG/CnE,GACyB,WAA1Bc,EAAKC,mBACJ,CAAC,KAAM,KAAM,MAAM1F,SAAS6C,IACN,QAAvBN,EAAAA,EAAAA,IAAU,WACR,CAAC,KAAM,KAAM,MAAMvC,SAAS6C,IAAoB,KAARP,GAAqC,QAAvBC,EAAAA,EAAAA,IAAU,YAClEf,EAAAA,EAAAA,KAAA,OAAKM,UAAU,eAAcJ,SAAC,qBAGhCF,EAAAA,EAAAA,KAAA,OAAKM,UAAW,qBAAoBsB,EAAiB,GAAK,uEAAwE1B,UAChIF,EAAAA,EAAAA,KAAA,MAAIM,UAAU,mBAAkBJ,UAC9BF,EAAAA,EAAAA,KAACgI,EAAQ,CAAC5L,SAAU6H,EAAK0D,qBAKb,QAAnB1D,EAAK0D,WAAwB/F,EAK1B,MAJFvB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,qNAAoNJ,SAAA,EACjOF,EAAAA,EAAAA,KAAC6H,EAAAA,IAAc,CAACvH,UAAU,2BAA2B,gBAAc,KACnEN,EAAAA,EAAAA,KAAC6H,EAAAA,IAAc,CAACvH,UAAU,gCAvFtBF,GA6lB+D2H,CAAe9D,EAAM7D,GAAS,IACrE,OAAfgB,GACO,OAARN,GACiB,WAAjBuB,GACqB,QAArBzB,EAAK,GAAGyG,UACNrH,EAAAA,EAAAA,KAAAC,EAAAA,SAAA,CAAAC,SAAGsE,MACD,cAOD,OAAdrD,GACCnB,EAAAA,EAAAA,KAAAC,EAAAA,SAAA,CAAAC,UACEF,EAAAA,EAAAA,KAACiI,EAAAA,SAAQ,CAACC,SAAU,KAAKhI,UACvBF,EAAAA,EAAAA,KAACS,EAAS,QAGZ,WAKd,C", "sources": ["pricing/components/PPEcho02.jsx", "pricing/components/vprice_04.jsx"], "sourcesContent": ["import { useTranslation } from 'react-i18next'\r\nimport { useMemo } from 'react'\r\n\r\nconst PPEcho02 = ({ planType }) => {\r\n    const { t } = useTranslation()\r\n\r\n    const pricingDescription = useMemo(() => {\r\n        const getBasicLanguageModels = () => {\r\n            return [\r\n                'GPT-4o',\r\n                'DeepSeek',\r\n                {\r\n                    model: 'Claude',\r\n                    tokens_per_month: '25k',\r\n                },\r\n            ]\r\n        }\r\n\r\n        const getProLanguageModels = () => {\r\n            return [\r\n                'GPT-4o',\r\n                'DeepSeek',\r\n                {\r\n                    model: 'Grok',\r\n                    new_badge: true,\r\n                },\r\n                {\r\n                    model: 'GPT-5',\r\n                    new_badge: true,\r\n                },\r\n                {\r\n                    model: '<PERSON>',\r\n                    tokens_per_month: '50k',\r\n                },\r\n            ]\r\n        }\r\n\r\n        const getProMaxLanguageModels = () => {\r\n            return [\r\n                'GPT-4o',\r\n                'DeepSeek',\r\n                {\r\n                    model: 'Grok',\r\n                    new_badge: true,\r\n                },\r\n                {\r\n                    model: 'GPT-5',\r\n                    new_badge: true,\r\n                },\r\n                'OpenAI o1',\r\n                {\r\n                    model: '<PERSON>',\r\n                    unlimited: true,\r\n                },\r\n            ]\r\n        }\r\n\r\n        const getImageModels = () => {\r\n            const dalle_limit = {\r\n                Basic: 20,\r\n                Pro: 50,\r\n            }\r\n\r\n            const flux_limit = {\r\n                Basic: 15,\r\n                Pro: 30,\r\n                ProMax: 160,\r\n            }\r\n\r\n            const dalle_model = 'DALL-E 3'\r\n            const current_dalle_limit = dalle_limit[planType] ?? null\r\n            const current_flux_limit = flux_limit[planType] ?? null\r\n            let models = []\r\n\r\n            if (planType !== 'ProMax') {\r\n                if (current_dalle_limit !== null) {\r\n                    models.push({\r\n                        model: dalle_model,\r\n                        images_per_month: current_dalle_limit,\r\n                    })\r\n                }\r\n            } else {\r\n                models.push({\r\n                    model: dalle_model,\r\n                    unlimited: true,\r\n                })\r\n            }\r\n\r\n            if (current_flux_limit !== null) {\r\n                models.push({\r\n                    model: 'Flux',\r\n                    images_per_month: current_flux_limit,\r\n                })\r\n            }\r\n\r\n            return models\r\n        }\r\n\r\n        const getVideoModels = () => {\r\n            let models = []\r\n\r\n            const klingai_limit = {\r\n                Pro: 3,\r\n                ProMax: 15,\r\n            }\r\n\r\n            const current_klingai_limit = klingai_limit[planType] ?? null\r\n\r\n            if (current_klingai_limit !== null) {\r\n                models.push({\r\n                    model: 'KlingAI',\r\n                    videos_per_month: current_klingai_limit,\r\n                })\r\n            }\r\n\r\n            return models\r\n        }\r\n\r\n        const getFeatures = () => {\r\n            const dialouge_limit = {\r\n                Basic: 500000,\r\n                Pro: 1000000,\r\n            }\r\n\r\n            const translation = {\r\n                dialougeLimit: t('echo.pricing.vprice.dialogueLimitText'),\r\n            }\r\n\r\n            const current_dialouge_limit = dialouge_limit[planType] ?? null\r\n            let features = []\r\n\r\n            if (planType !== 'ProMax') {\r\n                if (current_dialouge_limit !== null) {\r\n                    features.push({\r\n                        text: translation.dialougeLimit,\r\n                        tokens_per_month: current_dialouge_limit,\r\n                    })\r\n                }\r\n            } else {\r\n                features.push({\r\n                    text: translation.dialougeLimit,\r\n                    unlimited: true,\r\n                })\r\n            }\r\n\r\n            return features\r\n        }\r\n\r\n        const models = {\r\n            Basic: getBasicLanguageModels(),\r\n            Pro: getProLanguageModels(),\r\n            ProMax: getProMaxLanguageModels(),\r\n        }\r\n\r\n        let pricingDescription = {\r\n            languageModels: models[planType] ?? [],\r\n            imageGeneration: getImageModels(),\r\n            videoGeneration: getVideoModels(),\r\n            features: getFeatures(),\r\n        }\r\n\r\n        if (planType === 'Basic') {\r\n            delete pricingDescription.videoGeneration\r\n        } else if (['Enterprise', 'Advanced'].includes(planType)) {\r\n            pricingDescription = []\r\n        }\r\n\r\n        const translation = {\r\n            new_badge: t('echo.pricing.vprice.newBadgeText'),\r\n            unlimited: t('echo.pricing.vprice.unlimitedText'),\r\n            tokens: t('echo.pricing.vprice.tokensText'),\r\n            images: t('echo.pricing.vprice.imagesText'),\r\n            videos: t('echo.pricing.vprice.videosText'),\r\n            perMonth: t('echo.pricing.vprice.perMonthText'),\r\n        }\r\n\r\n        const booleanOptions = ['new_badge', 'unlimited']\r\n        const perMonthOptions = ['tokens_per_month', 'images_per_month', 'videos_per_month']\r\n\r\n        return Object.fromEntries(\r\n            Object.entries(pricingDescription).map(([section, sectionValues]) => {\r\n                const translatedSection = t(`echo.pricing.vprice.${section}`)\r\n\r\n                sectionValues = sectionValues.map((option) => {\r\n                    if (typeof option === 'object') {\r\n                        const hasBooleanOption = booleanOptions.some((currentOption) => option.hasOwnProperty(currentOption))\r\n                        const hasPerMonthOption = perMonthOptions.some((currentOption) => option.hasOwnProperty(currentOption))\r\n\r\n                        if (hasBooleanOption) {\r\n                            const currentBooleanOption = booleanOptions.find((currentOption) => option.hasOwnProperty(currentOption))\r\n\r\n                            if (option[currentBooleanOption]) {\r\n                                option[currentBooleanOption] = translation[currentBooleanOption]\r\n                            }\r\n                        } else if (hasPerMonthOption) {\r\n                            const currentPerMonthOption = perMonthOptions.find((currentOption) => option.hasOwnProperty(currentOption))\r\n\r\n                            if (option[currentPerMonthOption]) {\r\n                                option[currentPerMonthOption] = `${option[currentPerMonthOption]} ${translation[currentPerMonthOption]}`\r\n                            }\r\n                        }\r\n\r\n                        if (!option.hasOwnProperty('text') && option.hasOwnProperty('model')) {\r\n                            option['text'] = option.model\r\n                        }\r\n                    }\r\n\r\n                    return option\r\n                })\r\n\r\n                return [translatedSection, sectionValues]\r\n            })\r\n        )\r\n    }, [planType, t])\r\n\r\n    return (\r\n        <>\r\n            {Object.entries(pricingDescription).map(([translatedSection, sectionValues], index) => (\r\n                <li key={index} className=\"w-full\">\r\n                    <h3 className=\"font-semibold text-gray-800 mb-3\">{translatedSection}</h3>\r\n\r\n                    {sectionValues.length > 0 && (\r\n                        <ul>\r\n                            {sectionValues.map((value, index) => (\r\n                                <li key={index} className=\"flex items-center\">\r\n                                    <span className=\"text-blue-600 mr-2\">✓</span>\r\n                                    {typeof value !== 'object' ? (\r\n                                        <span className=\"text-gray-700\">{value}</span>\r\n                                    ) : (\r\n                                        <>\r\n                                            {'text' in value && (\r\n                                                <>\r\n                                                    <span className=\"text-gray-700\">\r\n                                                        {value.text}\r\n                                                        {value.unlimited && (\r\n                                                            <>\r\n                                                                {': '}\r\n                                                                <span className=\"text-blue-600 font-medium\">{value.unlimited}</span>\r\n                                                            </>\r\n                                                        )}\r\n                                                    </span>\r\n                                                    {value.new_badge && <span className=\"ml-2 bg-blue-100 text-blue-600 text-xs px-2 py-0.5 rounded\">{value.new_badge}</span>}\r\n                                                </>\r\n                                            )}\r\n                                        </>\r\n                                    )}\r\n                                </li>\r\n                            ))}\r\n                        </ul>\r\n                    )}\r\n                </li>\r\n            ))}\r\n        </>\r\n    )\r\n}\r\n\r\nexport default PPEcho02\r\n", "\"use client\"\r\n\r\nimport React, { lazy, Suspense, useState, useEffect, useRef, useCallback } from \"react\"\r\nimport { motion } from \"framer-motion\"\r\nimport { getPricePlan, DailyPrice } from \"../../core/utils/main\"\r\nimport { GetCookie } from \"../../core/utils/cookies\"\r\nimport { hexHash, hoverDarken, useDeviceSize } from \"../../core/utils/helper\"\r\nimport \"../css/style.css\"\r\nimport { useTranslation } from \"react-i18next\"\r\nimport CheckIcon from \"../../assets/images/check-icon.svg\"\r\nimport { HiMiniSparkles } from \"react-icons/hi2\"\r\nimport VPriceTrial from \"./vprice_trial\"\r\nimport PPEcho02 from \"./PPEcho02\"\r\n\r\nconst TpReviews = React.lazy(() => import(\"../../features/tpreviews\"))\r\n\r\nfunction VPrice04(props) {\r\n  const data = props.data ? props.data : null\r\n  const setPricing = props.setPricing ? props.setPricing : () => {}\r\n  var ppg = GetCookie(\"ppg\")\r\n    ? GetCookie(\"ppg\")\r\n    : process.env.REACT_APP_DEFAULT_PPG\r\n      ? process.env.REACT_APP_DEFAULT_PPG\r\n      : \"14\"\r\n  var pp_ctaclr = GetCookie(\"pp_ctaclr\") ? GetCookie(\"pp_ctaclr\") : \"1559ED\"\r\n\r\n  const ppgArrayWithToggle = [\"40\", \"48\", \"52\", \"97\", \"101\", \"109\", \"110\"]\r\n  const showToggle = ppgArrayWithToggle.includes(ppg) && props.showToggle ? props.showToggle : false\r\n  const tpreviews = GetCookie(\"tp_reviews\") ? GetCookie(\"tp_reviews\") : \"\"\r\n  const enterprise = GetCookie(\"enterprise\") ? GetCookie(\"enterprise\") : \"off\"\r\n  const ptoggle = GetCookie(\"p_toggle\") ? GetCookie(\"p_toggle\") : \"\"\r\n  const [desc_align] = useState(GetCookie(\"desc_align\"))\r\n  const [isMobile, setIsMobile] = useState(false)\r\n  const { isTablet } = useDeviceSize()\r\n  const allowTrialPlan = data && (data[0].plan_name.toLowerCase().startsWith(\"trial\") || [\"60\", \"62\"].includes(ppg))\r\n  const [appName] = useState((GetCookie('appName') ? GetCookie('appName') : \"ChatBot Pro\"))\r\n  const [pricingText, setPricingText] = useState(`Unlock the full potential of ${appName}`)\r\n  const [isChatPDFContext, setIsChatPDFContext] = useState(false)\r\n  const [planInterval, setPlanInterval] = useState(\"monthly\")\r\n\r\n  // Swipe functionality\r\n  const [currentPlan, setCurrentPlan] = useState(0)\r\n  const carouselRef = useRef(null)\r\n  const [touchStart, setTouchStart] = useState(null)\r\n  const [touchEnd, setTouchEnd] = useState(null)\r\n  const [showSwipe, setShowSwipe] = useState(false)\r\n  const [visiblePlans, setVisiblePlans] = useState([])\r\n\r\n  var billedAnnualDisplay = false\r\n  const { t } = useTranslation()\r\n\r\n  useEffect(() => {\r\n    const checkScreenSize = () => {\r\n      setIsMobile(window.innerWidth <= 729)\r\n    }\r\n\r\n    checkScreenSize()\r\n    window.addEventListener(\"resize\", checkScreenSize)\r\n\r\n    return () => window.removeEventListener(\"resize\", checkScreenSize)\r\n  })\r\n\r\n  useEffect(() => {\r\n    const referrer = document.referrer;\r\n    const parentDomain = referrer ? new URL(referrer).hostname : \"\";\r\n\r\n    const allowedDomains = [\r\n      \"staging.chatpdfv2.ai-pro.org\",\r\n      \"chatpdfv2.ai-pro.org\"\r\n    ];\r\n\r\n    if (\r\n      GetCookie(\"chatpdfv2modal\") === \"true\" ||\r\n      allowedDomains.includes(parentDomain)\r\n    ) {\r\n      setPricingText(\"Unlock the full potential of ChatPDF v2\");\r\n      setIsChatPDFContext(true);\r\n    }\r\n  }, []);\r\n\r\n  const checkPlanInterval = useCallback((plan) => {\r\n    if (!showToggle) return true;\r\n    if (plan.payment_interval.toLowerCase() === planInterval) return true;\r\n    return false;\r\n  }, [showToggle, planInterval]);\r\n\r\n  // Set up visible plans and determine if swipe should be shown\r\n  useEffect(() => {\r\n    if (data) {\r\n      const filtered = data.filter((plan) => checkPlanInterval(plan))\r\n      setVisiblePlans(filtered)\r\n      // Only show swipe on mobile when there are 2+ plans\r\n      setShowSwipe(window.innerWidth <= 768 && filtered.length > 1)\r\n    }\r\n  }, [data, planInterval, isMobile, checkPlanInterval]);\r\n\r\n  // Update showSwipe on window resize\r\n  useEffect(() => {\r\n    const handleResize = () => {\r\n      if (data) {\r\n        const filtered = data.filter((plan) => checkPlanInterval(plan))\r\n        setShowSwipe(window.innerWidth <= 768 && filtered.length > 1)\r\n      }\r\n    }\r\n\r\n    window.addEventListener(\"resize\", handleResize)\r\n    return () => window.removeEventListener(\"resize\", handleResize)\r\n  }, [data, planInterval, checkPlanInterval]);\r\n\r\n  if (ppg === \"48\") {\r\n    billedAnnualDisplay = true\r\n  }\r\n\r\n  if (ptoggle === \"01\" || ptoggle === \"03\") {\r\n    billedAnnualDisplay = true\r\n  }\r\n\r\n  const [planIntervalState, setPlanIntervalState] = useState(billedAnnualDisplay ? \"yearly\" : \"monthly\")\r\n\r\n  useEffect(() => {\r\n    setPlanInterval(planIntervalState)\r\n  }, [planIntervalState])\r\n\r\n  const intervalChange = () => {\r\n    if (planIntervalState === \"monthly\") {\r\n      setPlanIntervalState(\"yearly\")\r\n    } else {\r\n      setPlanIntervalState(\"monthly\")\r\n    }\r\n    setCurrentPlan(0) // Reset to first plan when changing interval\r\n  }\r\n\r\n  // Swipe handlers\r\n  const minSwipeDistance = 50\r\n\r\n  const onTouchStart = (e) => {\r\n    setTouchEnd(null)\r\n    setTouchStart(e.targetTouches[0].clientX)\r\n  }\r\n\r\n  const onTouchMove = (e) => {\r\n    setTouchEnd(e.targetTouches[0].clientX)\r\n  }\r\n\r\n  const onTouchEnd = () => {\r\n    if (!touchStart || !touchEnd) return\r\n    const distance = touchStart - touchEnd\r\n    const isLeftSwipe = distance > minSwipeDistance\r\n    const isRightSwipe = distance < -minSwipeDistance\r\n\r\n    if (isLeftSwipe && currentPlan < visiblePlans.length - 1) {\r\n      setCurrentPlan((prev) => prev + 1)\r\n    }\r\n    if (isRightSwipe && currentPlan > 0) {\r\n      setCurrentPlan((prev) => prev - 1)\r\n    }\r\n  }\r\n\r\n  const goToPrevPlan = () => {\r\n    if (currentPlan > 0) {\r\n      setCurrentPlan((prev) => prev - 1)\r\n    }\r\n  }\r\n\r\n  const goToNextPlan = () => {\r\n    if (currentPlan < visiblePlans.length - 1) {\r\n      setCurrentPlan((prev) => prev + 1)\r\n    }\r\n  }\r\n\r\n  const enterpriseTab = () => (\r\n    <div className=\" w-full lg:w-[330px] xl:w-[380px] text-center px-4 mb-8 relative\">\r\n      <div className=\"bg-white rounded-lg shadow-lg overflow-hidden\">\r\n        <div className=\"px-6 py-20 md:py-10 price-content\">\r\n          <h3 className=\"text-2xl font-bold mb-4\">{t(\"echo.pricing.vprice_02.enterprise.title\")}</h3>\r\n          <p className={`text-4xl font-bold text-[#4285F4] ${planInterval === \"yearly\" && ppg === \"48\" ? \"\" : \"mb-4\"}`}>\r\n            {t(\"echo.pricing.vprice_02.enterprise.price\")}\r\n          </p>\r\n          {planInterval === \"yearly\" && ppg === \"48\" && GetCookie(\"daily\") !== \"on\" ? (\r\n            <div className=\"text-xs mb-4\">(billed yearly)</div>\r\n          ) : (\r\n            \"\"\r\n          )}\r\n          <div className=\"py-4\">\r\n            <motion.button\r\n              className=\"text-white font-bold py-3 px-3 rounded-lg\"\r\n              style={{ backgroundColor: hexHash(pp_ctaclr) }}\r\n              whileHover={{ scale: 1.05, backgroundColor: hoverDarken(pp_ctaclr) }}\r\n              whileTap={{ scale: 0.9 }}\r\n              onClick={() => setPricing(62)}\r\n            >\r\n              {t(\"echo.pricing.vprice_02.enterprise.cta\")}\r\n            </motion.button>\r\n          </div>\r\n          <div className={`mb-6 pricing-description ${desc_align === \"left\" ? \"text-left\" : \"text-center\"} ppg-${ppg}`}>\r\n            <ul className=\"text-sm text-gray-600\">\r\n              <li className=\"mb-2 font-bold\">\r\n                <div>{t(\"echo.pricing.vprice_02.enterprise.desc1\")}</div>\r\n              </li>\r\n              <li className=\"\">\r\n                <div>{t(\"echo.pricing.vprice_02.enterprise.desc2\")}</div>\r\n              </li>\r\n            </ul>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  )\r\n\r\n  const trialPlanUseCustomComponent = false //Set to true if want to use the separate component\r\n  if (allowTrialPlan && trialPlanUseCustomComponent) {\r\n    return (\r\n      <VPriceTrial\r\n        data={data}\r\n        checkPlanInterval={checkPlanInterval}\r\n        getPricePlan={getPricePlan}\r\n        planInterval={planInterval}\r\n        ppg={ppg}\r\n        billedAnnualDisplay={billedAnnualDisplay}\r\n        ptoggle={ptoggle}\r\n        pp_ctaclr={pp_ctaclr}\r\n        enterpriseTab={enterpriseTab}\r\n        enterprise={enterprise}\r\n        tpreviews={tpreviews}\r\n        setPricing={setPricing}\r\n        isMobile={isMobile}\r\n        desc_align={desc_align}\r\n        CheckIcon={CheckIcon}\r\n        TpReviews={TpReviews}\r\n      />\r\n    )\r\n  }\r\n\r\n  // Render a single plan card\r\n  const renderPlanCard = (plan, index) => (\r\n    <div key={index} className={`w-full text-center px-2 mb-8 relative ${!allowTrialPlan ? \"lg:w-[340px] xl:w-[410px] lg:min-w-[300px]\" : \"md:w-[450px] md:min-w-[455px]\"}`}>\r\n      <div className=\"bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden\">\r\n        <div className=\"px-6 py-10 md:py-8 price-content shadow-md\">\r\n\r\n          {allowTrialPlan && (\r\n            <div className={`flex justify-center`}>\r\n              <div className=\"flex flex-col mb-[20px]\">\r\n                <h1 className={`text-center mb-2 text-[30px] bg-gradient-to-r from-[#305EE9] to-[#474DE7] text-transparent bg-clip-text lg:text-[30px] font-black`}>\r\n                  Upgrade to Chatbot Pro\r\n                </h1>\r\n                <div className={`text-center text-[16px] text-gray-700 m-auto ${isChatPDFContext ? \"max-w-500:w-[235px]\" : \"\"}`}>\r\n                  {pricingText}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )}\r\n          <h3 className={`mb-2 ${!allowTrialPlan ? \"text-[22px] font-bold text-blue-600\" : \"text-[14px] text-gray-500 font-bold\"}`}>\r\n            {plan.label}\r\n            {/* {plan.payment_interval === \"Yearly\" ? (\r\n              <>\r\n                {plan.label.split(' ')[0]}<br />\r\n                {plan.label.split(' ').slice(1).join(' ')}\r\n              </>\r\n            ) : (\r\n              plan.label\r\n            )} */}\r\n          </h3>\r\n\r\n          {GetCookie(\"daily\") === \"on\" ? (\r\n            <DailyPrice plan={plan} />\r\n          ) : billedAnnualDisplay && plan.payment_interval === \"Yearly\" && (ppg === \"48\" || ptoggle === \"02\") ? (\r\n            <p className=\"text-[36px] font-bold mb-6\">\r\n              {getPricePlan(plan.currency, Number.parseFloat(plan.price / 12).toFixed(2))}\r\n              <span className=\"text-lg text-gray-400\"> /month</span>\r\n            </p>\r\n          ) : plan.trial_price ? (\r\n            <p className=\"text-[36px] font-bold mb-6\">{getPricePlan(plan.currency, plan.trial_price)}</p>\r\n          ) : (\r\n            <p className=\"text-[36px] font-bold mb-6\">\r\n              {(ptoggle === \"02\" || ptoggle === \"03\") && plan.payment_interval === \"Yearly\"\r\n                ? getPricePlan(plan.currency, Number.parseFloat(plan.price / 12).toFixed(2))\r\n                : getPricePlan(plan.currency, plan.price)}\r\n              <span className=\"text-lg text-gray-400\">\r\n                /\r\n                {(ptoggle === \"02\" || ptoggle === \"03\") && plan.payment_interval === \"Yearly\"\r\n                  ? \" month\"\r\n                  : plan.payment_interval === \"Monthly\"\r\n                    ? \" month\"\r\n                    : \" year\"}\r\n              </span>\r\n            </p>\r\n          )}\r\n\r\n          <motion.button\r\n            className=\"w-full py-3 bg-gradient-to-r from-[#305EE9] to-[#474DE7] text-white rounded-md font-medium hover:bg-blue-700 transition-colors mb-6 text-[18px]\"\r\n            whileHover={{ scale: 1.05 }}\r\n            whileTap={{ scale: 0.95 }}\r\n            onClick={() => setPricing(plan.plan_id)}\r\n          >\r\n            Continue\r\n          </motion.button>\r\n\r\n          {(ptoggle === \"02\" || ptoggle === \"03\") &&\r\n            plan.payment_interval === \"Yearly\" &&\r\n            GetCookie(\"daily\") !== \"on\" && (\r\n              <span className=\"text-sm text-gray-500 block mb-4\">\r\n                or {getPricePlan(plan.currency, plan.price)} annual\r\n              </span>\r\n            )}\r\n          {((billedAnnualDisplay &&\r\n            plan.payment_interval === \"Yearly\" &&\r\n            ![\"01\", \"02\", \"03\"].includes(ptoggle) &&\r\n            GetCookie(\"daily\") !== \"on\") ||\r\n            (![\"01\", \"02\", \"03\"].includes(ptoggle) && ppg === 48 && GetCookie(\"daily\") !== \"on\")) && (\r\n            <div className=\"text-xs mb-4\">(billed yearly)</div>\r\n          )}\r\n\r\n          <div className={`w-full text-left ${allowTrialPlan ? \"\" : \"sm:min-h-[440px] md:min-h-[500px] lg:min-h-[480px] xl:min-h-[460px]\"}`}>\r\n            <ul className=\"space-y-4 w-full\">\r\n              <PPEcho02 planType={plan.plan_type} />\r\n            </ul>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      {plan.plan_type === \"Pro\" && !allowTrialPlan ? (\r\n        <div className=\"absolute top-0 left-1/2 transform rounded-full min-w-[130px] max-w-[130px] max-w-768:min-w-[142px] -translate-x-1/2 -translate-y-1/2 bg-blue-600 text-white font-bold text-[16px] py-1 px-2 text-xs span-highlight\">\r\n          <HiMiniSparkles className=\"inline text-yellow-400\" /> Most Popular{\" \"}\r\n          <HiMiniSparkles className=\"inline text-yellow-400\" />\r\n        </div>\r\n      ) : null}\r\n    </div>\r\n  )\r\n\r\n  return (\r\n    <div className=\"v-pricing pricing md:min-h-[90vh] bg-[#F6F7F8]\">\r\n      <div className={`pricing_columns mx-auto`}>\r\n        <div className=\"w-full\">\r\n          <div className=\"pricing_header mb-[10px]\">\r\n            {!allowTrialPlan && (\r\n              <div className={`flex justify-center`}>\r\n                <div className=\"flex flex-col mb-[20px]\">\r\n                  <h1 className=\"text-4xl bg-gradient-to-r from-[#305EE9] to-[#474DE7] text-transparent bg-clip-text lg:text-4xl font-bold text-center mb-2 min-h-[44px]\">\r\n                    Pricing Plan\r\n                  </h1>\r\n                  <div className={`text-center text-[16px] ${isChatPDFContext ? \"max-w-500:w-[235px]\" : \"\"}`}>\r\n                    {pricingText}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            )}\r\n            {showToggle && (\r\n              <div className=\"p-4\">\r\n                <div className=\"flex items-center justify-center w-full mb-4\">\r\n                  <label htmlFor=\"toggleB\" className=\"flex items-center cursor-pointer\">\r\n                    <div\r\n                      className={`${planIntervalState === \"monthly\" ? \"text-gray-800 font-bold\" : \"text-gray-700\"} mr-3 uppercase`}\r\n                    >\r\n                      Monthly\r\n                    </div>\r\n                    <div className=\"relative\">\r\n                      <input\r\n                        type=\"checkbox\"\r\n                        id=\"toggleB\"\r\n                        className=\"sr-only toggle\"\r\n                        onChange={intervalChange}\r\n                        defaultChecked={billedAnnualDisplay}\r\n                      />\r\n                      <div className=\"block bg-gradient-to-r from-[#268FED] to-[#3D56BA] w-12 h-6 rounded-full\"></div>\r\n                      <div\r\n                        className={`dot absolute top-1 bg-white w-4 h-4 rounded-full transition-all duration-300 ease-in-out ${\r\n                          planIntervalState === \"yearly\" ? \"left-[0.3rem]\" : \"left-1\"\r\n                        }`}\r\n                      ></div>\r\n                    </div>\r\n                    <div\r\n                      className={`${planIntervalState === \"yearly\" ? \"text-gray-800 font-bold\" : \"text-gray-700\"} ml-3 uppercase`}\r\n                    >\r\n                      Yearly\r\n                    </div>\r\n                  </label>\r\n                </div>\r\n              </div>\r\n            )}\r\n          </div>\r\n          <div\r\n            className={`flex flex-col max-w-768:flex-row justify-center items-start ${isChatPDFContext ? \"lg:ml-[5%]\" : \"\"}`}\r\n          >\r\n            <div className={`pricing_columns w-full ${allowTrialPlan ? \"md:w-[60%] sm:px-[5%]\" : \"lg:w-[78%]\"}`}>\r\n                <>\r\n                  {showSwipe ? (\r\n                    <div className=\"relative\">\r\n                      {/* Swipe Navigation - using simple HTML instead of lucide-react */}\r\n                      <button\r\n                        onClick={goToPrevPlan}\r\n                        className={`absolute left-0 top-1/2 transform -translate-y-1/2 z-10 bg-white/80 p-2 rounded-full shadow ${\r\n                          currentPlan === 0 ? \"opacity-50 cursor-not-allowed\" : \"opacity-100\"\r\n                        }`}\r\n                        disabled={currentPlan === 0}\r\n                        aria-label=\"Previous plan\"\r\n                      >\r\n                        <svg\r\n                          xmlns=\"http://www.w3.org/2000/svg\"\r\n                          width=\"24\"\r\n                          height=\"24\"\r\n                          viewBox=\"0 0 24 24\"\r\n                          fill=\"none\"\r\n                          stroke=\"currentColor\"\r\n                          strokeWidth=\"2\"\r\n                          strokeLinecap=\"round\"\r\n                          strokeLinejoin=\"round\"\r\n                          className=\"text-blue-600\"\r\n                        >\r\n                          <polyline points=\"15 18 9 12 15 6\"></polyline>\r\n                        </svg>\r\n                      </button>\r\n                      <button\r\n                        onClick={goToNextPlan}\r\n                        className={`absolute right-0 top-1/2 transform -translate-y-1/2 z-10 bg-white/80 p-2 rounded-full shadow ${\r\n                          currentPlan === visiblePlans.length - 1 ? \"opacity-50 cursor-not-allowed\" : \"opacity-100\"\r\n                        }`}\r\n                        disabled={currentPlan === visiblePlans.length - 1}\r\n                        aria-label=\"Next plan\"\r\n                      >\r\n                        <svg\r\n                          xmlns=\"http://www.w3.org/2000/svg\"\r\n                          width=\"24\"\r\n                          height=\"24\"\r\n                          viewBox=\"0 0 24 24\"\r\n                          fill=\"none\"\r\n                          stroke=\"currentColor\"\r\n                          strokeWidth=\"2\"\r\n                          strokeLinecap=\"round\"\r\n                          strokeLinejoin=\"round\"\r\n                          className=\"text-blue-600\"\r\n                        >\r\n                          <polyline points=\"9 18 15 12 9 6\"></polyline>\r\n                        </svg>\r\n                      </button>\r\n\r\n                      {/* Swipeable Content */}\r\n                      <div\r\n                        ref={carouselRef}\r\n                        className=\"overflow-hidden\"\r\n                        onTouchStart={onTouchStart}\r\n                        onTouchMove={onTouchMove}\r\n                        onTouchEnd={onTouchEnd}\r\n                      >\r\n                        <div\r\n                          className=\"flex transition-transform duration-300 ease-in-out\"\r\n                          style={{ transform: `translateX(-${currentPlan * 100}%)` }}\r\n                        >\r\n                          {visiblePlans.map((plan, index) => (\r\n                            <div key={index} className=\"w-full flex-shrink-0\">\r\n                              <div className=\"w-full text-center px-4 relative\">\r\n                                <div className=\"bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden\">\r\n                                  <div className=\"px-6 py-10 md:py-8 price-content shadow-md\">\r\n\r\n                                  {allowTrialPlan && (\r\n                                    <div className={`flex justify-center`}>\r\n                                      <div className=\"flex flex-col mb-[20px]\">\r\n                                        <h1 className={`text-center mb-2 text-2xl bg-gradient-to-r from-[#305EE9] to-[#474DE7] text-transparent bg-clip-text lg:text-2xl font-black`}>\r\n                                          Upgrade to Chatbot Pro\r\n                                        </h1>\r\n                                        <div className={`text-center text-[16px] ${isChatPDFContext ? \"max-w-500:w-[235px]\" : \"\"}`}>\r\n                                          {pricingText}\r\n                                        </div>\r\n                                      </div>\r\n                                    </div>\r\n                                  )}\r\n\r\n                                    <h3 className=\"text-[22px] font-bold text-blue-600 mb-4\">\r\n                                      {plan.label}\r\n                                      {/* {plan.payment_interval === \"Yearly\" ? (\r\n                                        <>\r\n                                          {plan.label.split(' ')[0]}<br />\r\n                                          {plan.label.split(' ').slice(1).join(' ')}\r\n                                        </>\r\n                                      ) : (\r\n                                        plan.label\r\n                                      )} */}\r\n                                    </h3>\r\n\r\n                                    {GetCookie(\"daily\") === \"on\" ? (\r\n                                      <DailyPrice plan={plan} />\r\n                                    ) : billedAnnualDisplay &&\r\n                                      plan.payment_interval === \"Yearly\" &&\r\n                                      (ppg === \"48\" || ptoggle === \"02\") ? (\r\n                                      <p className=\"text-[36px] font-bold mb-6\">\r\n                                        {getPricePlan(plan.currency, Number.parseFloat(plan.price / 12).toFixed(2))}\r\n                                        <span className=\"text-lg text-gray-400\"> /month</span>\r\n                                      </p>\r\n                                    ) : plan.trial_price ? (\r\n                                      <p className=\"text-[36px] font-bold mb-6\">\r\n                                        {getPricePlan(plan.currency, plan.trial_price)}\r\n                                      </p>\r\n                                    ) : (\r\n                                      <p className=\"text-[36px] font-bold mb-6\">\r\n                                        {(ptoggle === \"02\" || ptoggle === \"03\") && plan.payment_interval === \"Yearly\"\r\n                                          ? getPricePlan(plan.currency, Number.parseFloat(plan.price / 12).toFixed(2))\r\n                                          : getPricePlan(plan.currency, plan.price)}\r\n                                        <span className=\"text-lg text-gray-400\">\r\n                                          /\r\n                                          {(ptoggle === \"02\" || ptoggle === \"03\") && plan.payment_interval === \"Yearly\"\r\n                                            ? \" month\"\r\n                                            : plan.payment_interval === \"Monthly\"\r\n                                              ? \" month\"\r\n                                              : \" year\"}\r\n                                        </span>\r\n                                      </p>\r\n                                    )}\r\n\r\n                                    <motion.button\r\n                                      className=\"w-full py-3 bg-gradient-to-r from-[#4F46E5] to-[#3B82F6] text-white rounded-md font-medium hover:bg-blue-700 transition-colors mb-6 text-[16px]\"\r\n                                      whileHover={{ scale: 1.05 }}\r\n                                      whileTap={{ scale: 0.95 }}\r\n                                      onClick={() => setPricing(plan.plan_id)}\r\n                                    >\r\n                                      Continue\r\n                                    </motion.button>\r\n\r\n                                    {(ptoggle === \"02\" || ptoggle === \"03\") &&\r\n                                      plan.payment_interval === \"Yearly\" &&\r\n                                      GetCookie(\"daily\") !== \"on\" && (\r\n                                        <span className=\"text-sm text-gray-500 block mb-4\">\r\n                                          or {getPricePlan(plan.currency, plan.price)} annual\r\n                                        </span>\r\n                                      )}\r\n                                    {((billedAnnualDisplay &&\r\n                                      plan.payment_interval === \"Yearly\" &&\r\n                                      ![\"01\", \"02\", \"03\"].includes(ptoggle) &&\r\n                                      GetCookie(\"daily\") !== \"on\") ||\r\n                                      (![\"01\", \"02\", \"03\"].includes(ptoggle) &&\r\n                                        ppg === 48 &&\r\n                                        GetCookie(\"daily\") !== \"on\")) && (\r\n                                      <div className=\"text-xs mb-4\">(billed yearly)</div>\r\n                                    )}\r\n\r\n                                    <div className=\"w-full text-left md:min-h-[500px]\">\r\n                                      {/* Plan content - same as original */}\r\n                                      <ul className=\"space-y-4 w-full\">\r\n                                        {plan.plan_type === \"Basic\" && (\r\n                                          <>\r\n                                            {/* Language Models Section */}\r\n                                            <li className=\"w-full\">\r\n                                              <h3 className=\"font-semibold text-gray-800 mb-3\">Language Models</h3>\r\n                                              <ul className=\"space-y\">\r\n                                                <li className=\"flex items-center\">\r\n                                                  <span className=\"text-blue-600 mr-2\">✓</span>\r\n                                                  <span className=\"text-gray-700\">GPT-4o</span>\r\n                                                </li>\r\n                                                <li className=\"flex items-center\">\r\n                                                  <span className=\"text-blue-600 mr-2\">✓</span>\r\n                                                  <span className=\"text-gray-700\">DeepSeek</span>\r\n                                                </li>\r\n                                                <li className=\"flex items-center\">\r\n                                                  <span className=\"text-blue-600 mr-2\">✓</span>\r\n                                                  <span className=\"text-gray-700\">\r\n                                                    Claude:{\" \"}\r\n                                                    <span className=\"text-gray-500\">\r\n                                                      25k tokens{isTablet && !allowTrialPlan ? \"/mo.\" : \"/month\"}\r\n                                                    </span>\r\n                                                  </span>\r\n                                                </li>\r\n                                              </ul>\r\n                                            </li>\r\n\r\n                                            {/* Image Generation Section */}\r\n                                            <li className=\"w-full\">\r\n                                              <h3 className=\"font-semibold text-gray-800 mb-3\">Image Generation</h3>\r\n                                              <ul className=\"space-y\">\r\n                                                <li className=\"flex items-center\">\r\n                                                  <span className=\"text-blue-600 mr-2\">✓</span>\r\n                                                  <span className=\"text-gray-700\">\r\n                                                    DALL-E 3:{\" \"}\r\n                                                    <span className=\"text-gray-500\">\r\n                                                      20 images{isTablet && !allowTrialPlan ? \"/mo.\" : \"/month\"}\r\n                                                    </span>\r\n                                                  </span>\r\n                                                </li>\r\n                                                <li className=\"flex items-center\">\r\n                                                  <span className=\"text-blue-600 mr-2\">✓</span>\r\n                                                  <span className=\"text-gray-700\">\r\n                                                    Flux:{\" \"}\r\n                                                    <span className=\"text-gray-500\">\r\n                                                      15 images{isTablet && !allowTrialPlan ? \"/mo.\" : \"/month\"}\r\n                                                    </span>\r\n                                                  </span>\r\n                                                </li>\r\n                                              </ul>\r\n                                            </li>\r\n\r\n                                            {/* Features Section */}\r\n                                            <li className=\"w-full\">\r\n                                              <h3 className=\"font-semibold text-gray-800 mb-3 feat4\">Features</h3>\r\n                                              <ul className=\"space-y\">\r\n                                                <li className=\"flex items-center\">\r\n                                                  <span className=\"text-blue-600 mr-2\">✓</span>\r\n                                                  <span className=\"text-gray-700\">\r\n                                                    Dialogue Limit:{\" \"}\r\n                                                    <span className=\"text-gray-500 samppp\">\r\n                                                      500,000 tokens{isTablet && !allowTrialPlan ? \"\" : \"/month\"}\r\n                                                    </span>\r\n                                                  </span>\r\n                                                </li>\r\n                                                {allowTrialPlan && (\r\n                                                  <li className=\"flex items-center\">\r\n                                                    <span className=\"text-blue-600 mr-2\">✓</span>\r\n                                                    <span className=\"text-gray-500\">{plan.trial_days}-Day Trial, then only {getPricePlan(plan.currency, plan.price)}/month</span>\r\n                                                  </li>\r\n                                                )}\r\n                                              </ul>\r\n                                            </li>\r\n                                          </>\r\n                                        )}\r\n\r\n                                        {plan.plan_type === \"Pro\" && (\r\n                                          <>\r\n                                            {/* Language Models Section */}\r\n                                            <li className=\"w-full\">\r\n                                              <h3 className=\"font-semibold text-gray-800 mb-3\">Language Models</h3>\r\n                                              <ul className=\"space-y\">\r\n                                                <li className=\"flex items-center\">\r\n                                                  <span className=\"text-blue-600 mr-2\">✓</span>\r\n                                                  <span className=\"text-gray-700\">GPT-4o</span>\r\n                                                </li>\r\n                                                <li className=\"flex items-center\">\r\n                                                  <span className=\"text-blue-600 mr-2\">✓</span>\r\n                                                  <span className=\"text-gray-700\">DeepSeek</span>\r\n                                                </li>\r\n                                                <li className=\"flex items-center\">\r\n                                                  <span className=\"text-blue-600 mr-2\">✓</span>\r\n                                                  <span className=\"text-gray-700\">Grok</span>\r\n                                                  <span className=\"ml-2 bg-blue-100 text-blue-600 text-xs px-2 py-0.5 rounded\">\r\n                                                    New\r\n                                                  </span>\r\n                                                </li>\r\n                                                <li className=\"flex items-center\">\r\n                                                  <span className=\"text-blue-600 mr-2\">✓</span>\r\n                                                  <span className=\"text-gray-700\">GPT-5</span>\r\n                                                  <span className=\"ml-2 bg-blue-100 text-blue-600 text-xs px-2 py-0.5 rounded\">\r\n                                                    New\r\n                                                  </span>\r\n                                                </li>\r\n                                                <li className=\"flex items-center\">\r\n                                                  <span className=\"text-blue-600 mr-2\">✓</span>\r\n                                                  <span className=\"text-gray-700\">\r\n                                                    Claude:{\" \"}\r\n                                                    <span className=\"text-gray-500\">\r\n                                                      50k tokens{isTablet && !allowTrialPlan ? \"/mo.\" : \"/month\"}\r\n                                                    </span>\r\n                                                  </span>\r\n                                                </li>\r\n                                              </ul>\r\n                                            </li>\r\n\r\n                                            {/* Image Generation Section */}\r\n                                            <li className=\"w-full\">\r\n                                              <h3 className=\"font-semibold text-gray-800 mb-3\">Image Generation</h3>\r\n                                              <ul className=\"space-y\">\r\n                                                <li className=\"flex items-center\">\r\n                                                  <span className=\"text-blue-600 mr-2\">✓</span>\r\n                                                  <span className=\"text-gray-700\">\r\n                                                    DALL-E 3:{\" \"}\r\n                                                    <span className=\"text-gray-500\">\r\n                                                      50 images{isTablet && !allowTrialPlan ? \"/mo.\" : \"/month\"}\r\n                                                    </span>\r\n                                                  </span>\r\n                                                </li>\r\n                                                <li className=\"flex items-center\">\r\n                                                  <span className=\"text-blue-600 mr-2\">✓</span>\r\n                                                  <span className=\"text-gray-700\">\r\n                                                    Flux:{\" \"}\r\n                                                    <span className=\"text-gray-500\">\r\n                                                      30 images{isTablet && !allowTrialPlan ? \"/mo.\" : \"/month\"}\r\n                                                    </span>\r\n                                                  </span>\r\n                                                </li>\r\n                                              </ul>\r\n                                            </li>\r\n\r\n                                            {/* Video Generation Section */}\r\n                                            <li className=\"w-full\">\r\n                                              <h3 className=\"font-semibold text-gray-800 mb-3\">Video Generation</h3>\r\n                                              <ul className=\"space-y\">\r\n                                                <li className=\"flex items-center\">\r\n                                                  <span className=\"text-blue-600 mr-2\">✓</span>\r\n                                                  <span className=\"text-gray-700\">\r\n                                                    KlingAI:{\" \"}\r\n                                                    <span className=\"text-gray-500\">\r\n                                                      3 videos{isTablet && !allowTrialPlan ? \"/mo.\" : \"/month\"}\r\n                                                    </span>\r\n                                                  </span>\r\n                                                </li>\r\n                                              </ul>\r\n                                            </li>\r\n\r\n                                            {/* Features Section */}\r\n                                            <li className=\"w-full\">\r\n                                              <h3 className=\"font-semibold text-gray-800 mb-3 feat5\">Features</h3>\r\n                                              <ul className=\"space-y\">\r\n                                                <li className=\"flex items-center\">\r\n                                                  <span className=\"text-blue-600 mr-2\">✓</span>\r\n                                                  <span className=\"text-gray-700\">\r\n                                                    Dialogue Limit:{\" \"}\r\n                                                    <span className=\"text-gray-500\">\r\n                                                      1,000,000 tokens{isTablet && !allowTrialPlan ? \"\" : \"/month\"}\r\n                                                    </span>\r\n                                                  </span>\r\n                                                </li>\r\n                                                {allowTrialPlan && (\r\n                                                  <li className=\"flex items-center\">\r\n                                                    <span className=\"text-blue-600 mr-2\">✓</span>\r\n                                                    <span className=\"text-gray-500\">{plan.trial_days}-Day Trial, then only {getPricePlan(plan.currency, plan.price)}/month</span>\r\n                                                  </li>\r\n                                                )}\r\n                                              </ul>\r\n                                            </li>\r\n                                          </>\r\n                                        )}\r\n\r\n                                        {plan.plan_type === \"ProMax\" && (\r\n                                          <>\r\n                                            {/* Language Models Section */}\r\n                                            <li className=\"w-full\">\r\n                                              <h3 className=\"font-semibold text-gray-800 mb-3\">Language Models</h3>\r\n                                              <ul className=\"space-y\">\r\n                                                <li className=\"flex items-center\">\r\n                                                  <span className=\"text-blue-600 mr-2\">✓</span>\r\n                                                  <span className=\"text-gray-700\">GPT-4o</span>\r\n                                                </li>\r\n                                                <li className=\"flex items-center\">\r\n                                                  <span className=\"text-blue-600 mr-2\">✓</span>\r\n                                                  <span className=\"text-gray-700\">DeepSeek</span>\r\n                                                </li>\r\n                                                <li className=\"flex items-center\">\r\n                                                  <span className=\"text-blue-600 mr-2\">✓</span>\r\n                                                  <span className=\"text-gray-700\">Grok</span>\r\n                                                  <span className=\"ml-2 bg-blue-100 text-blue-600 text-xs px-2 py-0.5 rounded\">\r\n                                                    New\r\n                                                  </span>\r\n                                                </li>\r\n                                                <li className=\"flex items-center\">\r\n                                                  <span className=\"text-blue-600 mr-2\">✓</span>\r\n                                                  <span className=\"text-gray-700\">GPT-5</span>\r\n                                                  <span className=\"ml-2 bg-blue-100 text-blue-600 text-xs px-2 py-0.5 rounded\">\r\n                                                    New\r\n                                                  </span>\r\n                                                </li>\r\n                                                <li className=\"flex items-center\">\r\n                                                  <span className=\"text-blue-600 mr-2\">✓</span>\r\n                                                  <span className=\"text-gray-700\">OpenAI o1</span>\r\n                                                </li>\r\n                                                <li className=\"flex items-center\">\r\n                                                <span className=\"text-blue-600 mr-2\">✓</span>\r\n                                                  <span className=\"text-gray-700\">\r\n                                                    Claude: <span className=\"text-blue-600 font-medium\">Unlimited</span>\r\n                                                  </span>\r\n                                                </li>\r\n                                              </ul>\r\n                                            </li>\r\n\r\n                                            {/* Image Generation Section */}\r\n                                            <li className=\"w-full\">\r\n                                              <h3 className=\"font-semibold text-gray-800 mb-3\">Image Generation</h3>\r\n                                              <ul className=\"space-y\">\r\n                                                <li className=\"flex items-center\">\r\n                                                  <span className=\"text-blue-600 mr-2\">✓</span>\r\n                                                  <span className=\"text-gray-700\">\r\n                                                    DALL-E 3:{\" \"}\r\n                                                    <span className=\"text-blue-600 font-medium\">Unlimited</span>\r\n                                                  </span>\r\n                                                </li>\r\n                                                <li className=\"flex items-center\">\r\n                                                  <span className=\"text-blue-600 mr-2\">✓</span>\r\n                                                  <span className=\"text-gray-700\">\r\n                                                    Flux:{\" \"}\r\n                                                    <span className=\"text-gray-500\">\r\n                                                      160 images{isTablet && !allowTrialPlan ? \"/mo.\" : \"/month\"}\r\n                                                    </span>\r\n                                                  </span>\r\n                                                </li>\r\n                                              </ul>\r\n                                            </li>\r\n\r\n                                            {/* Video Generation Section */}\r\n                                            <li className=\"w-full\">\r\n                                              <h3 className=\"font-semibold text-gray-800 mb-3\">Video Generation</h3>\r\n                                              <ul className=\"space-y\">\r\n                                                <li className=\"flex items-center\">\r\n                                                  <span className=\"text-blue-600 mr-2\">✓</span>\r\n                                                  <span className=\"text-gray-700\">\r\n                                                    KlingAI:{\" \"}\r\n                                                    <span className=\"text-gray-500\">\r\n                                                      15 videos{isTablet && !allowTrialPlan ? \"/mo.\" : \"/month\"}\r\n                                                    </span>\r\n                                                  </span>\r\n                                                </li>\r\n                                              </ul>\r\n                                            </li>\r\n\r\n                                            {/* Features Section */}\r\n                                            <li className=\"w-full\">\r\n                                              <h3 className=\"font-semibold text-gray-800 mb-3 feat6\">Features</h3>\r\n                                              <ul className=\"space-y\">\r\n                                                <li className=\"flex items-center\">\r\n                                                  <span className=\"text-blue-600 mr-2\">✓</span>\r\n                                                  <span className=\"text-gray-700\">\r\n                                                    Dialogue Limit:{\" \"}\r\n                                                    <span className=\"text-blue-600 font-medium\">Unlimited</span>\r\n                                                  </span>\r\n                                                </li>\r\n                                              </ul>\r\n                                            </li>\r\n                                          </>\r\n                                        )}\r\n                                      </ul>\r\n                                    </div>\r\n                                  </div>\r\n                                </div>\r\n                                {plan.plan_type === \"Pro\" && !allowTrialPlan ? (\r\n                                  <div className=\"absolute top-0 left-1/2 transform rounded-full min-w-[130px] max-w-[130px] max-w-768:min-w-[142px] mt-[8px] md:mt-[0px] -translate-x-1/2 md:-translate-y-1/2 bg-blue-600 text-white font-bold text-[16px] py-1 px-2 text-xs span-highlight\">\r\n                                    <HiMiniSparkles className=\"inline text-yellow-400\" /> Most Popular{\" \"}\r\n                                    <HiMiniSparkles className=\"inline text-yellow-400\" />\r\n                                  </div>\r\n                                ) : null}\r\n                              </div>\r\n                            </div>\r\n                          ))}\r\n                        </div>\r\n                      </div>\r\n\r\n                      {/* Pagination Dots */}\r\n                      <div className=\"flex justify-center mt-4 space-x-2\">\r\n                        {visiblePlans.map((_, index) => (\r\n                          <button\r\n                            key={index}\r\n                            className={`h-2 w-2 rounded-full ${index === currentPlan ? \"bg-blue-600\" : \"bg-gray-300\"}`}\r\n                            onClick={() => setCurrentPlan(index)}\r\n                            aria-label={`Go to plan ${index + 1}`}\r\n                          />\r\n                        ))}\r\n                      </div>\r\n                    </div>\r\n                  ) : (\r\n                    <div className={`pricing-toggle flex flex-col max-w-768:flex-row justify-center`}>\r\n                      {data?.map((plan, index) => (checkPlanInterval(plan) ? renderPlanCard(plan, index) : \"\"))}\r\n                      {enterprise === \"on\" &&\r\n                      ppg !== \"46\" &&\r\n                      planInterval === \"yearly\" &&\r\n                      data[0].currency === \"USD\" ? (\r\n                        <>{enterpriseTab()}</>\r\n                      ) : null}\r\n                    </div>\r\n                  )}\r\n                </>\r\n            </div>\r\n          </div>\r\n\r\n          {tpreviews === \"on\" ? (\r\n            <>\r\n              <Suspense fallback={null}>\r\n                <TpReviews />\r\n              </Suspense>\r\n            </>\r\n          ) : null}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default VPrice04\r\n"], "names": ["_ref", "planType", "t", "useTranslation", "pricingDescription", "useMemo", "_models$planType", "languageModels", "Basic", "model", "tokens_per_month", "Pro", "new_badge", "ProMax", "unlimited", "imageGeneration", "getImageModels", "_dalle_limit$planType", "_flux_limit$planType", "dalle_model", "current_dalle_limit", "current_flux_limit", "models", "push", "images_per_month", "videoGeneration", "getVideoModels", "_klingai_limit$planTy", "current_klingai_limit", "videos_per_month", "features", "getFeatures", "_dialouge_limit$planT", "translation", "dialougeLimit", "current_dialouge_limit", "text", "includes", "tokens", "images", "videos", "perMonth", "booleanOptions", "perMonthOptions", "Object", "fromEntries", "entries", "map", "_ref2", "section", "sectionValues", "translatedSection", "option", "hasBooleanOption", "some", "currentOption", "hasOwnProperty", "hasPerMonthOption", "currentBooleanOption", "find", "currentPerMonthOption", "_jsx", "_Fragment", "children", "_ref3", "index", "_jsxs", "className", "length", "value", "TpReviews", "React", "props", "data", "setPricing", "ppg", "Get<PERSON><PERSON><PERSON>", "process", "pp_ctaclr", "showToggle", "tpreviews", "enterprise", "ptoggle", "desc_align", "useState", "isMobile", "setIsMobile", "isTablet", "useDeviceSize", "allowTrialPlan", "plan_name", "toLowerCase", "startsWith", "appName", "pricingText", "setPricingText", "isChatPDFContext", "setIsChatPDFContext", "planInterval", "setPlanInterval", "currentPlan", "setCurrentPlan", "carouselRef", "useRef", "touchStart", "setTouchStart", "touchEnd", "setTouchEnd", "showSwipe", "setShowSwipe", "visiblePlans", "setVisiblePlans", "billedAnnualDisplay", "useEffect", "checkScreenSize", "window", "innerWidth", "addEventListener", "removeEventListener", "referrer", "document", "parentDomain", "URL", "hostname", "checkPlanInterval", "useCallback", "plan", "payment_interval", "filtered", "filter", "handleResize", "planIntervalState", "setPlanIntervalState", "enterpriseTab", "motion", "button", "style", "backgroundColor", "hexHash", "whileHover", "scale", "hoverDarken", "whileTap", "onClick", "htmlFor", "type", "id", "onChange", "intervalChange", "defaultChecked", "goToPrevPlan", "prev", "disabled", "xmlns", "width", "height", "viewBox", "fill", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "points", "goToNextPlan", "ref", "onTouchStart", "e", "targetTouches", "clientX", "onTouchMove", "onTouchEnd", "distance", "isRightSwipe", "transform", "label", "DailyPrice", "trial_price", "getPricePlan", "currency", "price", "Number", "parseFloat", "toFixed", "plan_id", "plan_type", "trial_days", "HiMiniSparkles", "_", "renderPlanCard", "PPEcho02", "Suspense", "fallback"], "sourceRoot": ""}