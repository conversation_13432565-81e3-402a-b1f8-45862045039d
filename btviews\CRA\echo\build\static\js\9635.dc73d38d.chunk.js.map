{"version": 3, "file": "static/js/9635.dc73d38d.chunk.js", "mappings": "0LAKA,MAoBA,EApBqBA,KAEjBC,EAAAA,EAAAA,MAAAC,EAAAA,SAAA,CAAAC,SAAA,EACEC,EAAAA,EAAAA,KAACC,EAAAA,QAAM,KACPJ,EAAAA,EAAAA,MAAA,OAAKK,UAAU,iEAAgEH,SAAA,EAC7EC,EAAAA,EAAAA,KAAA,MAAIE,UAAU,wEAAuEH,SAAC,SACtFC,EAAAA,EAAAA,KAAA,KAAGE,UAAU,iBAAgBH,SAAC,oBAC9BC,EAAAA,EAAAA,KAACG,EAAAA,EAAOC,OAAM,CACZF,UAAU,+DACVG,SAAU,CAAEC,MAAO,IACnBC,QAASA,KAAQC,OAAOC,SAASC,KAAO,SAAWX,SACpD,qBAIHC,EAAAA,EAAAA,KAACW,EAAAA,QAAM,M,0ECMb,QAxBA,WAGE,MAAMC,EAAWC,yBAWjB,OAVAC,EAAAA,EAAAA,WAAU,KACR,MAAMC,EAASC,SAASC,cAAc,UACtCF,EAAOG,IAAMN,EAAW,iDACxBG,EAAOI,OAAQ,EACfJ,EAAOK,OAAS,OAIhBJ,SAASK,KAAKC,YAAYP,IACzB,CAACH,KAEFZ,EAAAA,EAAAA,KAAAF,EAAAA,SAAA,CAAAC,UACEC,EAAAA,EAAAA,KAAA,UAAQE,UAAW,iHAMzB,C", "sources": ["404/index.jsx", "footer/index.jsx"], "sourcesContent": ["import React from 'react';\r\nimport Header from '../header';\r\nimport Footer from '../footer';\r\nimport { motion } from \"framer-motion\";\r\n\r\nconst NotFoundPage = () => {\r\n  return (\r\n    <>\r\n      <Header />\r\n      <div className=\"flex flex-col items-center justify-center h-screen bg-gray-100\">\r\n        <h1 className=\"text-red-950 text-[6rem] md:text-[8rem] font-black mt-4 text-gradient\">404</h1>\r\n        <p className=\"text-2xl pb-10\">Page not found</p>\r\n        <motion.button\r\n          className=\"bg-blue-500 text-white font-bold py-3 px-6 rounded-lg shadow\"\r\n          whileTap={{ scale: 0.9 }}\r\n          onClick={() => { window.location.href = \"/home\"; }}\r\n        >\r\n        Back to Home\r\n        </motion.button>\r\n      </div>\r\n      <Footer />\r\n    </>\r\n  );\r\n};\r\n\r\nexport default NotFoundPage;", "import React, { useEffect } from 'react';\r\n\r\nfunction Powered() {\r\n  // const [isHidden, setIsHidden] = useState(false);\r\n\r\n  const base_url = process.env.REACT_APP_BASE_URL;\r\n  useEffect(() => {\r\n    const script = document.createElement('script');\r\n    script.src = base_url + 'snippets/com.global.vuzo/js/com.global.vuzo.js';\r\n    script.async = true;\r\n    script.onload = () => {\r\n      // window.displayGooglePlay();\r\n      // window.displayQR();\r\n    };\r\n    document.body.appendChild(script);\r\n  }, [base_url]);\r\n  return (\r\n    <>\r\n      <footer className={`relative hidden md:block md:absolute bottom-[10px] right-[5px] w-full mt-3 py-12 md:py-2 z-[8888] md:hidden`}>\r\n      {/* <footer className=\"relative hidden md:block md:absolute bottom-[10px] right-[5px] w-full mt-3 py-12 md:py-2 z-[8888]\"> */}\r\n\r\n      </footer>\r\n    </>\r\n  );\r\n}\r\n\r\nexport default Powered;\r\n"], "names": ["NotFoundPage", "_jsxs", "_Fragment", "children", "_jsx", "Header", "className", "motion", "button", "whileTap", "scale", "onClick", "window", "location", "href", "Footer", "base_url", "process", "useEffect", "script", "document", "createElement", "src", "async", "onload", "body", "append<PERSON><PERSON><PERSON>"], "sourceRoot": ""}