{"version": 3, "file": "static/js/807.78cb36cb.chunk.js", "mappings": "sEASA,IAAIA,EAAM,SAASC,EAAQC,GAEzB,IAAIC,EAAYC,SAASC,cAAc,IAAIJ,EAAO,8BAGlDK,KAAKN,IAAM,CACTO,OAAQ,CACNC,GAAIL,EACJM,EAAGN,EAAUO,YACbC,EAAGR,EAAUS,cAEfC,UAAW,CACTC,OAAQ,CACNC,MAAO,IACPC,QAAS,CACPC,QAAQ,EACRC,WAAY,MAGhBC,MAAO,CACLJ,MAAO,QAETK,MAAO,CACLC,KAAM,SACNC,OAAQ,CACNC,MAAO,EACPJ,MAAO,WAETK,QAAS,CACPC,SAAU,GAEZC,MAAO,CACLC,IAAK,GACLJ,MAAO,IACPK,OAAQ,MAGZC,QAAS,CACPd,MAAO,EACPe,QAAQ,EACRC,KAAM,CACJd,QAAQ,EACRe,MAAO,EACPC,YAAa,EACbC,MAAM,IAGVC,KAAM,CACJpB,MAAO,GACPe,QAAQ,EACRC,KAAM,CACJd,QAAQ,EACRe,MAAO,GACPI,SAAU,EACVF,MAAM,IAGVG,YAAa,CACXpB,QAAQ,EACRqB,SAAU,IACVnB,MAAO,OACPU,QAAS,EACTN,MAAO,GAETgB,KAAM,CACJtB,QAAQ,EACRe,MAAO,EACPQ,UAAW,OACXV,QAAQ,EACRW,UAAU,EACVC,SAAU,MACVC,QAAQ,EACRC,QAAS,CACP3B,QAAQ,EACR4B,QAAS,IACTC,QAAS,MAGbC,MAAO,IAETC,cAAe,CACbC,UAAW,SACXC,OAAQ,CACNC,QAAS,CACPlC,QAAQ,EACRmC,KAAM,QAERC,QAAS,CACPpC,QAAQ,EACRmC,KAAM,QAERE,QAAQ,GAEVC,MAAO,CACLC,KAAK,CACHlB,SAAU,IACVD,YAAY,CACVR,QAAS,IAGb4B,OAAO,CACLnB,SAAU,IACVH,KAAM,GACNuB,SAAU,IAEZC,QAAQ,CACNrB,SAAU,IACVoB,SAAU,IAEZE,KAAK,CACHC,aAAc,GAEhBC,OAAO,CACLD,aAAc,IAGlBE,MAAM,CAAC,GAETC,eAAe,EACfC,GAAI,CACFC,SAAU,CAAC,EACXX,MAAO,CAAC,EACRY,QAAQ,CAAC,GAEXC,IAAK,CAAC,GAGR,IAAIpE,EAAMM,KAAKN,IAGZE,GACDmE,OAAOC,WAAWtE,EAAKE,GAGzBF,EAAIoE,IAAIG,IAAM,CACZC,WAAYxE,EAAIa,UAAUsB,KAAKpB,MAC/B0D,gBAAiBzE,EAAIa,UAAUsB,KAAKJ,KAAKC,MACzC0C,WAAY1E,EAAIa,UAAU0B,KAAKP,MAC/B2C,qBAAsB3E,EAAIa,UAAUwB,YAAYC,SAChDsC,kBAAmB5E,EAAIa,UAAUwB,YAAYd,MAC7CsD,mBAAoB7E,EAAIgD,cAAcO,MAAMC,KAAKlB,SACjDwC,qBAAsB9E,EAAIgD,cAAcO,MAAME,OAAOnB,SACrDyC,iBAAkB/E,EAAIgD,cAAcO,MAAME,OAAOtB,KACjD6C,sBAAuBhF,EAAIgD,cAAcO,MAAMI,QAAQrB,UAIzDtC,EAAIiE,GAAGgB,WAAa,WAEfjF,EAAIgE,eAAiBkB,OAAOC,iBAAmB,GAChDnF,EAAIO,OAAO6E,QAAUF,OAAOC,iBAC5BnF,EAAIoE,IAAIiB,QAAS,IAGjBrF,EAAIO,OAAO6E,QAAU,EACrBpF,EAAIoE,IAAIiB,QAAS,GAGnBrF,EAAIO,OAAOE,EAAIT,EAAIO,OAAOC,GAAGE,YAAcV,EAAIO,OAAO6E,QACtDpF,EAAIO,OAAOI,EAAIX,EAAIO,OAAOC,GAAGI,aAAeZ,EAAIO,OAAO6E,QAEvDpF,EAAIa,UAAUsB,KAAKpB,MAAQf,EAAIoE,IAAIG,IAAIC,WAAaxE,EAAIO,OAAO6E,QAC/DpF,EAAIa,UAAUsB,KAAKJ,KAAKC,MAAQhC,EAAIoE,IAAIG,IAAIE,gBAAkBzE,EAAIO,OAAO6E,QACzEpF,EAAIa,UAAU0B,KAAKP,MAAQhC,EAAIoE,IAAIG,IAAIG,WAAa1E,EAAIO,OAAO6E,QAC/DpF,EAAIa,UAAUwB,YAAYC,SAAWtC,EAAIoE,IAAIG,IAAII,qBAAuB3E,EAAIO,OAAO6E,QACnFpF,EAAIgD,cAAcO,MAAMC,KAAKlB,SAAWtC,EAAIoE,IAAIG,IAAIM,mBAAqB7E,EAAIO,OAAO6E,QACpFpF,EAAIgD,cAAcO,MAAME,OAAOnB,SAAWtC,EAAIoE,IAAIG,IAAIO,qBAAuB9E,EAAIO,OAAO6E,QACxFpF,EAAIa,UAAUwB,YAAYd,MAAQvB,EAAIoE,IAAIG,IAAIK,kBAAoB5E,EAAIO,OAAO6E,QAC7EpF,EAAIgD,cAAcO,MAAME,OAAOtB,KAAOnC,EAAIoE,IAAIG,IAAIQ,iBAAmB/E,EAAIO,OAAO6E,QAChFpF,EAAIgD,cAAcO,MAAMI,QAAQrB,SAAWtC,EAAIoE,IAAIG,IAAIS,sBAAwBhF,EAAIO,OAAO6E,OAE5F,EAMApF,EAAIiE,GAAGqB,WAAa,WAClBtF,EAAIO,OAAOgF,IAAMvF,EAAIO,OAAOC,GAAGgF,WAAW,KAC5C,EAEAxF,EAAIiE,GAAGwB,WAAa,WAElBzF,EAAIO,OAAOC,GAAGe,MAAQvB,EAAIO,OAAOE,EACjCT,EAAIO,OAAOC,GAAGoB,OAAS5B,EAAIO,OAAOI,EAE/BX,GAAOA,EAAIgD,cAAcE,OAAOI,QAEjC4B,OAAOQ,iBAAiB,SAAU,WAE9B1F,EAAIO,OAAOE,EAAIT,EAAIO,OAAOC,GAAGE,YAC7BV,EAAIO,OAAOI,EAAIX,EAAIO,OAAOC,GAAGI,aAG1BZ,EAAIoE,IAAIiB,SACTrF,EAAIO,OAAOE,GAAKT,EAAIO,OAAO6E,QAC3BpF,EAAIO,OAAOI,GAAKX,EAAIO,OAAO6E,SAG7BpF,EAAIO,OAAOC,GAAGe,MAAQvB,EAAIO,OAAOE,EACjCT,EAAIO,OAAOC,GAAGoB,OAAS5B,EAAIO,OAAOI,EAG9BX,EAAIa,UAAU0B,KAAKtB,SACrBjB,EAAIiE,GAAG0B,iBACP3F,EAAIiE,GAAG2B,kBACP5F,EAAIiE,GAAG4B,gBACP7F,EAAIiE,GAAGE,QAAQ2B,wBAInB9F,EAAIiE,GAAGE,QAAQ2B,sBAEjB,EAIJ,EAGA9F,EAAIiE,GAAG8B,YAAc,WACnB/F,EAAIO,OAAOgF,IAAIS,SAAS,EAAG,EAAGhG,EAAIO,OAAOE,EAAGT,EAAIO,OAAOI,EACzD,EAEAX,EAAIiE,GAAGgC,YAAc,WACnBjG,EAAIO,OAAOgF,IAAIW,UAAU,EAAG,EAAGlG,EAAIO,OAAOE,EAAGT,EAAIO,OAAOI,EAC1D,EAKAX,EAAIiE,GAAGkC,SAAW,SAAShF,EAAOU,EAASuE,GA6BzC,GA1BA9F,KAAK+F,QAAUrG,EAAIa,UAAUsB,KAAKL,OAASwE,KAAKxE,SAAW,GAAK9B,EAAIa,UAAUsB,KAAKpB,MAChFf,EAAIa,UAAUsB,KAAKJ,KAAKd,SACzBX,KAAKiG,aAAc,EACnBjG,KAAKkG,GAAKxG,EAAIa,UAAUsB,KAAKJ,KAAKC,MAAQ,IACtChC,EAAIa,UAAUsB,KAAKJ,KAAKG,OAC1B5B,KAAKkG,GAAKlG,KAAKkG,GAAKF,KAAKxE,WAK7BxB,KAAKmG,EAAIL,EAAWA,EAASK,EAAIH,KAAKxE,SAAW9B,EAAIO,OAAOE,EAC5DH,KAAKoG,EAAIN,EAAWA,EAASM,EAAIJ,KAAKxE,SAAW9B,EAAIO,OAAOI,EAGzDL,KAAKmG,EAAIzG,EAAIO,OAAOE,EAAgB,EAAZH,KAAK+F,OAAU/F,KAAKmG,EAAInG,KAAKmG,EAAInG,KAAK+F,OACzD/F,KAAKmG,EAAgB,EAAZnG,KAAK+F,SAAU/F,KAAKmG,EAAInG,KAAKmG,EAAInG,KAAK+F,QACpD/F,KAAKoG,EAAI1G,EAAIO,OAAOI,EAAgB,EAAZL,KAAK+F,OAAU/F,KAAKoG,EAAIpG,KAAKoG,EAAIpG,KAAK+F,OACzD/F,KAAKoG,EAAgB,EAAZpG,KAAK+F,SAAU/F,KAAKoG,EAAIpG,KAAKoG,EAAIpG,KAAK+F,QAGpDrG,EAAIa,UAAU0B,KAAKI,QACpB3C,EAAIiE,GAAGE,QAAQwC,aAAarG,KAAM8F,GAIpC9F,KAAKa,MAAQ,CAAC,EACY,iBAAhBA,EAAMJ,MAEd,GAAGI,EAAMJ,iBAAiB6F,MAAM,CAC9B,IAAIC,EAAiB1F,EAAMJ,MAAMuF,KAAKQ,MAAMR,KAAKxE,SAAW9B,EAAIa,UAAUM,MAAMJ,MAAMgG,SACtFzG,KAAKa,MAAM6F,IAAMC,EAASJ,EAC5B,MACsBK,MAAjB/F,EAAMJ,MAAMoG,GAAmCD,MAAjB/F,EAAMJ,MAAMqG,GAAmCF,MAAjB/F,EAAMJ,MAAMsG,IACzE/G,KAAKa,MAAM6F,IAAM,CACfG,EAAGhG,EAAMJ,MAAMoG,EACfC,EAAGjG,EAAMJ,MAAMqG,EACfC,EAAGlG,EAAMJ,MAAMsG,IAGCH,MAAjB/F,EAAMJ,MAAMJ,GAAmCuG,MAAjB/F,EAAMJ,MAAMuG,GAAmCJ,MAAjB/F,EAAMJ,MAAMwG,IACzEjH,KAAKa,MAAMqG,IAAM,CACf7G,EAAGQ,EAAMJ,MAAMJ,EACf2G,EAAGnG,EAAMJ,MAAMuG,EACfC,EAAGpG,EAAMJ,MAAMwG,QAMA,UAAfpG,EAAMJ,MACZT,KAAKa,MAAM6F,IAAM,CACfG,EAAIb,KAAKQ,MAAsB,IAAhBR,KAAKxE,UAA4B,EAChDsF,EAAId,KAAKQ,MAAsB,IAAhBR,KAAKxE,UAA4B,EAChDuF,EAAIf,KAAKQ,MAAsB,IAAhBR,KAAKxE,UAA4B,GAGrB,iBAAhBX,EAAMJ,QACnBT,KAAKa,MAAQA,EACbb,KAAKa,MAAM6F,IAAMC,EAAS3G,KAAKa,MAAMJ,QAIvCT,KAAKuB,SAAW7B,EAAIa,UAAUgB,QAAQC,OAASwE,KAAKxE,SAAW,GAAK9B,EAAIa,UAAUgB,QAAQd,MACvFf,EAAIa,UAAUgB,QAAQE,KAAKd,SAC5BX,KAAKmH,gBAAiB,EACtBnH,KAAKoH,GAAK1H,EAAIa,UAAUgB,QAAQE,KAAKC,MAAQ,IACzChC,EAAIa,UAAUgB,QAAQE,KAAKG,OAC7B5B,KAAKoH,GAAKpH,KAAKoH,GAAKpB,KAAKxE,WAK7B,IAAI6F,EAAU,CAAC,EACf,OAAO3H,EAAIa,UAAU0B,KAAKC,WACxB,IAAK,MACHmF,EAAU,CAAElB,EAAE,EAAGC,GAAG,GACtB,MACA,IAAK,YACHiB,EAAU,CAAElB,EAAE,GAAKC,GAAG,IACxB,MACA,IAAK,QACHiB,EAAU,CAAElB,EAAE,EAAGC,GAAG,GACtB,MACA,IAAK,eACHiB,EAAU,CAAElB,EAAE,GAAKC,EAAE,IACvB,MACA,IAAK,SACHiB,EAAU,CAAElB,EAAE,EAAGC,EAAE,GACrB,MACA,IAAK,cACHiB,EAAU,CAAElB,GAAG,GAAKC,EAAE,GACxB,MACA,IAAK,OACHiB,EAAU,CAAElB,GAAG,EAAGC,EAAE,GACtB,MACA,IAAK,WACHiB,EAAU,CAAElB,GAAG,GAAKC,GAAG,IACzB,MACA,QACEiB,EAAU,CAAElB,EAAE,EAAGC,EAAE,GAIpB1G,EAAIa,UAAU0B,KAAKE,UACpBnC,KAAKsH,GAAKD,EAAQlB,EAClBnG,KAAKuH,GAAKF,EAAQjB,EACf1G,EAAIa,UAAU0B,KAAKT,SACpBxB,KAAKsH,GAAKtH,KAAKsH,GAAMtB,KAAKxE,SAC1BxB,KAAKuH,GAAKvH,KAAKuH,GAAMvB,KAAKxE,YAG5BxB,KAAKsH,GAAKD,EAAQlB,EAAIH,KAAKxE,SAAS,GACpCxB,KAAKuH,GAAKF,EAAQjB,EAAIJ,KAAKxE,SAAS,IAOtCxB,KAAKwH,KAAOxH,KAAKsH,GACjBtH,KAAKyH,KAAOzH,KAAKuH,GAMjB,IAAIG,EAAahI,EAAIa,UAAUO,MAAMC,KACrC,GAAyB,iBAAf2G,GACR,GAAGA,aAAsBpB,MAAM,CAC7B,IAAIqB,EAAiBD,EAAW1B,KAAKQ,MAAMR,KAAKxE,SAAWkG,EAAWjB,SACtEzG,KAAKc,MAAQ6G,CACf,OAEA3H,KAAKc,MAAQ4G,EAGf,GAAiB,SAAd1H,KAAKc,MAAiB,CACvB,IAAI8G,EAAKlI,EAAIa,UAAUO,MACvBd,KAAK6H,IAAM,CACTxG,IAAKuG,EAAGxG,MAAMC,IACdyG,MAAOF,EAAGxG,MAAMH,MAAQ2G,EAAGxG,MAAME,QAE/BtB,KAAK6H,IAAIC,QAAO9H,KAAK6H,IAAIC,MAAQ,GACd,OAApBpI,EAAIoE,IAAIiE,UAA2CnB,MAAtBlH,EAAIoE,IAAIkE,aACtCtI,EAAIiE,GAAGE,QAAQoE,aAAajI,MACzBN,EAAIoE,IAAIoE,UACTlI,KAAK6H,IAAIM,QAAS,GAGxB,CAIF,EAGAzI,EAAIiE,GAAGkC,SAASuC,UAAUC,KAAO,WAE/B,IAAIC,EAAItI,KAER,GAAsB4G,MAAnB0B,EAAEC,cACH,IAAIxC,EAASuC,EAAEC,mBAEXxC,EAASuC,EAAEvC,OAGjB,GAAuBa,MAApB0B,EAAEE,eACH,IAAIjH,EAAU+G,EAAEE,oBAEZjH,EAAU+G,EAAE/G,QAGlB,GAAG+G,EAAEzH,MAAM6F,IACT,IAAI+B,EAAc,QAAQH,EAAEzH,MAAM6F,IAAIG,EAAE,IAAIyB,EAAEzH,MAAM6F,IAAII,EAAE,IAAIwB,EAAEzH,MAAM6F,IAAIK,EAAE,IAAIxF,EAAQ,SAEpFkH,EAAc,QAAQH,EAAEzH,MAAMqG,IAAI7G,EAAE,IAAIiI,EAAEzH,MAAMqG,IAAIF,EAAE,KAAKsB,EAAEzH,MAAMqG,IAAID,EAAE,KAAK1F,EAAQ,IAM5F,OAHA7B,EAAIO,OAAOgF,IAAIyD,UAAYD,EAC3B/I,EAAIO,OAAOgF,IAAI0D,YAERL,EAAExH,OAEP,IAAK,SACHpB,EAAIO,OAAOgF,IAAI2D,IAAIN,EAAEnC,EAAGmC,EAAElC,EAAGL,EAAQ,EAAa,EAAVC,KAAK6C,IAAQ,GACvD,MAEA,IAAK,OACHnJ,EAAIO,OAAOgF,IAAI6D,KAAKR,EAAEnC,EAAEJ,EAAQuC,EAAElC,EAAEL,EAAe,EAAPA,EAAiB,EAAPA,GACxD,MAEA,IAAK,WACHrG,EAAIiE,GAAGE,QAAQkF,UAAUrJ,EAAIO,OAAOgF,IAAKqD,EAAEnC,EAAEJ,EAAQuC,EAAElC,EAAEL,EAAS,KAAa,EAAPA,EAAU,EAAG,GACvF,MAEA,IAAK,UACHrG,EAAIiE,GAAGE,QAAQkF,UACbrJ,EAAIO,OAAOgF,IACXqD,EAAEnC,EAAIJ,GAAUrG,EAAIa,UAAUO,MAAMI,QAAQC,SAAS,KACrDmH,EAAElC,EAAIL,EAAS,IACR,KAAPA,GAAerG,EAAIa,UAAUO,MAAMI,QAAQC,SAAS,GACpDzB,EAAIa,UAAUO,MAAMI,QAAQC,SAC5B,GAEJ,MAEA,IAAK,OACHzB,EAAIiE,GAAGE,QAAQkF,UACbrJ,EAAIO,OAAOgF,IACXqD,EAAEnC,EAAW,EAAPJ,GAAYrG,EAAIa,UAAUO,MAAMI,QAAQC,SAAS,GACvDmH,EAAElC,EAAIL,EAAS,KACR,EAAPA,EAAS,MAAQrG,EAAIa,UAAUO,MAAMI,QAAQC,SAAS,GACtDzB,EAAIa,UAAUO,MAAMI,QAAQC,SAC5B,GAEJ,MAEA,IAAK,QAYH,GAAuB,OAApBzB,EAAIoE,IAAIiE,SACT,IAAIiB,EAAUV,EAAET,IAAI5D,SAEhB+E,EAAUtJ,EAAIoE,IAAIkF,QAGrBA,GAfDtJ,EAAIO,OAAOgF,IAAIgE,UACbD,EACAV,EAAEnC,EAAEJ,EACJuC,EAAElC,EAAEL,EACG,EAAPA,EACO,EAAPA,EAAWuC,EAAET,IAAIC,OAkBzBpI,EAAIO,OAAOgF,IAAIiE,YAEZxJ,EAAIa,UAAUO,MAAME,OAAOC,MAAQ,IACpCvB,EAAIO,OAAOgF,IAAIkE,YAAczJ,EAAIa,UAAUO,MAAME,OAAOH,MACxDnB,EAAIO,OAAOgF,IAAImE,UAAY1J,EAAIa,UAAUO,MAAME,OAAOC,MACtDvB,EAAIO,OAAOgF,IAAIjE,UAGjBtB,EAAIO,OAAOgF,IAAIoE,MAEjB,EAGA3J,EAAIiE,GAAG2B,gBAAkB,WACvB,IAAI,IAAIgE,EAAI,EAAGA,EAAI5J,EAAIa,UAAUC,OAAOC,MAAO6I,IAC7C5J,EAAIa,UAAUkC,MAAMa,KAAK,IAAI5D,EAAIiE,GAAGkC,SAASnG,EAAIa,UAAUM,MAAOnB,EAAIa,UAAUgB,QAAQd,OAE5F,EAEAf,EAAIiE,GAAG4F,gBAAkB,WAEvB,IAAI,IAAID,EAAI,EAAGA,EAAI5J,EAAIa,UAAUkC,MAAMgE,OAAQ6C,IAAI,CAGjD,IAAIhB,EAAI5I,EAAIa,UAAUkC,MAAM6G,GAW5B,GAAG5J,EAAIa,UAAU0B,KAAKtB,OAAO,CAC3B,IAAI6I,EAAK9J,EAAIa,UAAU0B,KAAKP,MAAM,EAClC4G,EAAEnC,GAAKmC,EAAEhB,GAAKkC,EACdlB,EAAElC,GAAKkC,EAAEf,GAAKiC,CAChB,CA2BA,GAxBG9J,EAAIa,UAAUgB,QAAQE,KAAKd,SACL,GAApB2H,EAAEnB,gBACAmB,EAAE/G,SAAW7B,EAAIa,UAAUgB,QAAQd,QAAO6H,EAAEnB,gBAAiB,GAChEmB,EAAE/G,SAAW+G,EAAElB,KAEZkB,EAAE/G,SAAW7B,EAAIa,UAAUgB,QAAQE,KAAKE,cAAa2G,EAAEnB,gBAAiB,GAC3EmB,EAAE/G,SAAW+G,EAAElB,IAEdkB,EAAE/G,QAAU,IAAG+G,EAAE/G,QAAU,IAI7B7B,EAAIa,UAAUsB,KAAKJ,KAAKd,SACL,GAAjB2H,EAAErC,aACAqC,EAAEvC,QAAUrG,EAAIa,UAAUsB,KAAKpB,QAAO6H,EAAErC,aAAc,GACzDqC,EAAEvC,QAAUuC,EAAEpC,KAEXoC,EAAEvC,QAAUrG,EAAIa,UAAUsB,KAAKJ,KAAKK,WAAUwG,EAAErC,aAAc,GACjEqC,EAAEvC,QAAUuC,EAAEpC,IAEboC,EAAEvC,OAAS,IAAGuC,EAAEvC,OAAS,IAII,UAA/BrG,EAAIa,UAAU0B,KAAKG,SACpB,IAAIqH,EAAU,CACZC,OAAQpB,EAAEvC,OACV4D,QAAUjK,EAAIO,OAAOE,EACrByJ,MAAOtB,EAAEvC,OACT8D,SAAUnK,EAAIO,OAAOI,QAGnBoJ,EAAU,CACZC,QAASpB,EAAEvC,OACX4D,QAASjK,EAAIO,OAAOE,EAAImI,EAAEvC,OAC1B6D,OAAQtB,EAAEvC,OACV8D,SAAUnK,EAAIO,OAAOI,EAAIiI,EAAEvC,QAsB/B,GAlBGuC,EAAEnC,EAAImC,EAAEvC,OAASrG,EAAIO,OAAOE,GAC7BmI,EAAEnC,EAAIsD,EAAQC,OACdpB,EAAElC,EAAIJ,KAAKxE,SAAW9B,EAAIO,OAAOI,GAE3BiI,EAAEnC,EAAImC,EAAEvC,OAAS,IACvBuC,EAAEnC,EAAIsD,EAAQE,QACdrB,EAAElC,EAAIJ,KAAKxE,SAAW9B,EAAIO,OAAOI,GAEhCiI,EAAElC,EAAIkC,EAAEvC,OAASrG,EAAIO,OAAOI,GAC7BiI,EAAElC,EAAIqD,EAAQG,MACdtB,EAAEnC,EAAIH,KAAKxE,SAAW9B,EAAIO,OAAOE,GAE3BmI,EAAElC,EAAIkC,EAAEvC,OAAS,IACvBuC,EAAElC,EAAIqD,EAAQI,SACdvB,EAAEnC,EAAIH,KAAKxE,SAAW9B,EAAIO,OAAOE,GAK5B,WADAT,EAAIa,UAAU0B,KAAKG,UAElBkG,EAAEnC,EAAImC,EAAEvC,OAASrG,EAAIO,OAAOE,GACvBmI,EAAEnC,EAAImC,EAAEvC,OAAS,KADSuC,EAAEhB,IAAMgB,EAAEhB,KAEzCgB,EAAElC,EAAIkC,EAAEvC,OAASrG,EAAIO,OAAOI,GACvBiI,EAAElC,EAAIkC,EAAEvC,OAAS,KADSuC,EAAEf,IAAMe,EAAEf,IAmBjD,GAbGuC,EAAU,OAAQpK,EAAIgD,cAAcE,OAAOC,QAAQC,OACpDpD,EAAIiE,GAAGV,MAAM8G,aAAazB,IAGzBwB,EAAU,SAAUpK,EAAIgD,cAAcE,OAAOC,QAAQC,OAASgH,EAAU,SAAUpK,EAAIgD,cAAcE,OAAOG,QAAQD,QACpHpD,EAAIiE,GAAGV,MAAM+G,eAAe1B,IAG3BwB,EAAU,UAAWpK,EAAIgD,cAAcE,OAAOC,QAAQC,OAASgH,EAAU,UAAWpK,EAAIgD,cAAcE,OAAOG,QAAQD,QACtHpD,EAAIiE,GAAGV,MAAMgH,gBAAgB3B,GAI5B5I,EAAIa,UAAUwB,YAAYpB,QAAUjB,EAAIa,UAAU0B,KAAKK,QAAQ3B,OAChE,IAAI,IAAIuJ,EAAIZ,EAAI,EAAGY,EAAIxK,EAAIa,UAAUkC,MAAMgE,OAAQyD,IAAI,CACrD,IAAIC,EAAKzK,EAAIa,UAAUkC,MAAMyH,GAG1BxK,EAAIa,UAAUwB,YAAYpB,QAC3BjB,EAAIiE,GAAGC,SAASwG,cAAc9B,EAAE6B,GAI/BzK,EAAIa,UAAU0B,KAAKK,QAAQ3B,QAC5BjB,EAAIiE,GAAGC,SAASyG,iBAAiB/B,EAAE6B,GAIlCzK,EAAIa,UAAU0B,KAAKI,QACpB3C,EAAIiE,GAAGC,SAAS0G,gBAAgBhC,EAAE6B,EAGtC,CAIJ,CAEF,EAEAzK,EAAIiE,GAAG4B,cAAgB,WAGrB7F,EAAIO,OAAOgF,IAAIW,UAAU,EAAG,EAAGlG,EAAIO,OAAOE,EAAGT,EAAIO,OAAOI,GAGxDX,EAAIiE,GAAG4F,kBAGP,IAAI,IAAID,EAAI,EAAGA,EAAI5J,EAAIa,UAAUkC,MAAMgE,OAAQ6C,IAAI,CACzC5J,EAAIa,UAAUkC,MAAM6G,GAC1BjB,MACJ,CAEF,EAEA3I,EAAIiE,GAAG0B,eAAiB,WACtB3F,EAAIa,UAAUkC,MAAQ,EACxB,EAEA/C,EAAIiE,GAAG4G,iBAAmB,WAGxBC,uBAAuB9K,EAAIiE,GAAG8G,gBAC9BD,uBAAuB9K,EAAIiE,GAAG+G,eAC9BhL,EAAIoE,IAAIkE,gBAAapB,EACrBlH,EAAIoE,IAAIkF,aAAUpC,EAClBlH,EAAIoE,IAAI6G,UAAY,EACpBjL,EAAIiE,GAAG0B,iBACP3F,EAAIiE,GAAGgC,cAGPjG,EAAIiE,GAAGE,QAAQ+G,OAEjB,EAKAlL,EAAIiE,GAAGC,SAASwG,cAAgB,SAASS,EAAIV,GAE3C,IAAIW,EAAKD,EAAG1E,EAAIgE,EAAGhE,EACf4E,EAAKF,EAAGzE,EAAI+D,EAAG/D,EACf4E,EAAOhF,KAAKiF,KAAKH,EAAGA,EAAKC,EAAGA,GAGhC,GAAGC,GAAQtL,EAAIa,UAAUwB,YAAYC,SAAS,CAE5C,IAAIkJ,EAAexL,EAAIa,UAAUwB,YAAYR,QAAWyJ,GAAQ,EAAEtL,EAAIa,UAAUwB,YAAYR,SAAY7B,EAAIa,UAAUwB,YAAYC,SAElI,GAAGkJ,EAAe,EAAE,CAGlB,IAAIC,EAAazL,EAAIa,UAAUwB,YAAYqJ,eAC3C1L,EAAIO,OAAOgF,IAAIkE,YAAc,QAAQgC,EAAWtE,EAAE,IAAIsE,EAAWrE,EAAE,IAAIqE,EAAWpE,EAAE,IAAImE,EAAa,IACrGxL,EAAIO,OAAOgF,IAAImE,UAAY1J,EAAIa,UAAUwB,YAAYd,MAIrDvB,EAAIO,OAAOgF,IAAI0D,YACfjJ,EAAIO,OAAOgF,IAAIoG,OAAOR,EAAG1E,EAAG0E,EAAGzE,GAC/B1G,EAAIO,OAAOgF,IAAIqG,OAAOnB,EAAGhE,EAAGgE,EAAG/D,GAC/B1G,EAAIO,OAAOgF,IAAIjE,SACftB,EAAIO,OAAOgF,IAAIiE,WAEjB,CAEF,CAEF,EAGAxJ,EAAIiE,GAAGC,SAASyG,iBAAoB,SAASQ,EAAIV,GAG/C,IAAIW,EAAKD,EAAG1E,EAAIgE,EAAGhE,EACf4E,EAAKF,EAAGzE,EAAI+D,EAAG/D,EAGnB,GAFWJ,KAAKiF,KAAKH,EAAGA,EAAKC,EAAGA,IAErBrL,EAAIa,UAAUwB,YAAYC,SAAS,CAE5C,IAAIuJ,EAAKT,GAAuC,IAAnCpL,EAAIa,UAAU0B,KAAKK,QAAQC,SACpCiJ,EAAKT,GAAuC,IAAnCrL,EAAIa,UAAU0B,KAAKK,QAAQE,SAExCqI,EAAGvD,IAAMiE,EACTV,EAAGtD,IAAMiE,EAETrB,EAAG7C,IAAMiE,EACTpB,EAAG5C,IAAMiE,CAEX,CAGF,EAGA9L,EAAIiE,GAAGC,SAAS0G,gBAAkB,SAASO,EAAIV,GAE7C,IAAIW,EAAKD,EAAG1E,EAAIgE,EAAGhE,EACf4E,EAAKF,EAAGzE,EAAI+D,EAAG/D,EACRJ,KAAKiF,KAAKH,EAAGA,EAAKC,EAAGA,IACnBF,EAAG9E,OAAOoE,EAAGpE,SAGxB8E,EAAGvD,IAAMuD,EAAGvD,GACZuD,EAAGtD,IAAMsD,EAAGtD,GAEZ4C,EAAG7C,IAAM6C,EAAG7C,GACZ6C,EAAG5C,IAAM4C,EAAG5C,GAGhB,EAKA7H,EAAIiE,GAAGV,MAAMwI,cAAgB,SAASC,EAAIC,GAExCjM,EAAIoE,IAAIoE,SAAU,EAElB,IAAI,IAAIoB,EAAI,EAAGA,EAAIoC,EAAIpC,IACrB5J,EAAIa,UAAUkC,MAAMa,KAClB,IAAI5D,EAAIiE,GAAGkC,SACTnG,EAAIa,UAAUM,MACdnB,EAAIa,UAAUgB,QAAQd,MACtB,CACE,EAAKkL,EAAMA,EAAIC,MAAQ5F,KAAKxE,SAAW9B,EAAIO,OAAOE,EAClD,EAAKwL,EAAMA,EAAIE,MAAQ7F,KAAKxE,SAAW9B,EAAIO,OAAOI,KAIrDiJ,GAAKoC,EAAG,IACLhM,EAAIa,UAAU0B,KAAKtB,QACrBjB,EAAIiE,GAAG4B,gBAET7F,EAAIoE,IAAIoE,SAAU,EAIxB,EAGAxI,EAAIiE,GAAGV,MAAM6I,gBAAkB,SAASJ,GAEtChM,EAAIa,UAAUkC,MAAMsJ,OAAO,EAAGL,GAC1BhM,EAAIa,UAAU0B,KAAKtB,QACrBjB,EAAIiE,GAAG4B,eAGX,EAGA7F,EAAIiE,GAAGV,MAAM+G,eAAiB,SAAS1B,GAGrC,GAAG5I,EAAIgD,cAAcE,OAAOC,QAAQlC,QAAUmJ,EAAU,SAAUpK,EAAIgD,cAAcE,OAAOC,QAAQC,MAAM,CAEvG,IAAIkJ,EAAW1D,EAAEnC,EAAIzG,EAAIgD,cAAce,MAAMmI,MACzCK,EAAW3D,EAAElC,EAAI1G,EAAIgD,cAAce,MAAMoI,MAEzC/D,EAAQ,GADRoE,EAAalG,KAAKiF,KAAKe,EAASA,EAAWC,EAASA,IAC3BvM,EAAIgD,cAAcO,MAAME,OAAOnB,SAE5D,SAASmK,IACP7D,EAAEE,eAAiBF,EAAE/G,QACrB+G,EAAEC,cAAgBD,EAAEvC,MACtB,CAGA,GAAGmG,GAAcxM,EAAIgD,cAAcO,MAAME,OAAOnB,UAE9C,GAAG8F,GAAS,GAAiC,aAA5BpI,EAAIgD,cAAc0J,OAAsB,CAGvD,GAAG1M,EAAIgD,cAAcO,MAAME,OAAOtB,MAAQnC,EAAIa,UAAUsB,KAAKpB,MAE3D,GAAGf,EAAIgD,cAAcO,MAAME,OAAOtB,KAAOnC,EAAIa,UAAUsB,KAAKpB,MAAM,EAC5DoB,EAAOyG,EAAEvC,OAAUrG,EAAIgD,cAAcO,MAAME,OAAOtB,KAAKiG,IAChD,IACTQ,EAAEC,cAAgB1G,EAEtB,KAAK,CACH,IAAIwK,EAAM/D,EAAEvC,OAASrG,EAAIgD,cAAcO,MAAME,OAAOtB,KAChDA,EAAOyG,EAAEvC,OAAUsG,EAAIvE,EAEzBQ,EAAEC,cADD1G,EAAO,EACUA,EAEA,CAEtB,CAQE,IAKIN,EARR,GAAG7B,EAAIgD,cAAcO,MAAME,OAAO5B,SAAW7B,EAAIa,UAAUgB,QAAQd,MAEjE,GAAGf,EAAIgD,cAAcO,MAAME,OAAO5B,QAAU7B,EAAIa,UAAUgB,QAAQd,OAC5Dc,EAAU7B,EAAIgD,cAAcO,MAAME,OAAO5B,QAAQuG,GACxCQ,EAAE/G,SAAWA,GAAW7B,EAAIgD,cAAcO,MAAME,OAAO5B,UAClE+G,EAAEE,eAAiBjH,QAGjBA,EAAU+G,EAAE/G,SAAW7B,EAAIa,UAAUgB,QAAQd,MAAMf,EAAIgD,cAAcO,MAAME,OAAO5B,SAASuG,GAClFQ,EAAE/G,SAAWA,GAAW7B,EAAIgD,cAAcO,MAAME,OAAO5B,UAClE+G,EAAEE,eAAiBjH,EAM3B,OAGA4K,IAK6B,cAA5BzM,EAAIgD,cAAc0J,QACnBD,GAGJ,MAGK,GAAGzM,EAAIgD,cAAcE,OAAOG,QAAQpC,QAAUmJ,EAAU,SAAUpK,EAAIgD,cAAcE,OAAOG,QAAQD,MAAM,CAG5G,GAAGpD,EAAIoE,IAAIwI,gBAAgB,CACrBN,EAAW1D,EAAEnC,EAAIzG,EAAIgD,cAAce,MAAM8I,YACzCN,EAAW3D,EAAElC,EAAI1G,EAAIgD,cAAce,MAAM+I,YAD7C,IAEIN,EAAalG,KAAKiF,KAAKe,EAASA,EAAWC,EAASA,GACpDQ,IAAc,IAAIC,MAAOC,UAAYjN,EAAIgD,cAAce,MAAMmJ,YAAY,IAE1EH,EAAa/M,EAAIgD,cAAcO,MAAME,OAAOC,WAC7C1D,EAAIoE,IAAI+I,qBAAsB,GAG7BJ,EAAqD,EAAxC/M,EAAIgD,cAAcO,MAAME,OAAOC,WAC7C1D,EAAIoE,IAAIwI,iBAAkB,EAC1B5M,EAAIoE,IAAI+I,qBAAsB,EAElC,CAGA,SAASC,EAAQC,EAAcC,EAAiBC,EAAcC,EAAOC,GAEnE,GAAGJ,GAAgBC,EAEjB,GAAItN,EAAIoE,IAAI+I,oBAcSjG,MAAhBqG,IAGGxM,EAAQsM,GADFA,GADMG,EAAST,GAAcS,EAAQH,GAAgBrN,EAAIgD,cAAcO,MAAME,OAAOC,WAGrF,QAAN+J,IAAc7E,EAAEC,cAAgB9H,GAC1B,WAAN0M,IAAiB7E,EAAEE,eAAiB/H,SAlBzC,GAAGyL,GAAcxM,EAAIgD,cAAcO,MAAME,OAAOnB,SAAS,CACvD,GAAmB4E,MAAhBqG,EAA2B,IAAIhJ,EAAMgJ,OAC/BhJ,EAAMiJ,EACf,GAAGjJ,GAAO8I,EAAa,CACrB,IAAItM,EAAQyM,EAAST,GAAcS,EAAQH,GAAgBrN,EAAIgD,cAAcO,MAAME,OAAOC,SACjF,QAAN+J,IAAc7E,EAAEC,cAAgB9H,GAC1B,WAAN0M,IAAiB7E,EAAEE,eAAiB/H,EACzC,CACF,KACW,QAAN0M,IAAc7E,EAAEC,mBAAgB3B,GAC1B,WAANuG,IAAiB7E,EAAEE,oBAAiB5B,EAc/C,CAEGlH,EAAIoE,IAAIwI,kBAETQ,EAAQpN,EAAIgD,cAAcO,MAAME,OAAOtB,KAAMnC,EAAIa,UAAUsB,KAAKpB,MAAO6H,EAAEC,cAAeD,EAAEvC,OAAQ,QAElG+G,EAAQpN,EAAIgD,cAAcO,MAAME,OAAO5B,QAAS7B,EAAIa,UAAUgB,QAAQd,MAAO6H,EAAEE,eAAgBF,EAAE/G,QAAS,WAG9G,CAEF,EAGA7B,EAAIiE,GAAGV,MAAMgH,gBAAkB,SAAS3B,GAEtC,GAAG5I,EAAIgD,cAAcE,OAAOC,QAAQlC,QAAUmJ,EAAU,UAAWpK,EAAIgD,cAAcE,OAAOC,QAAQC,OAAqC,aAA5BpD,EAAIgD,cAAc0J,OAAuB,CAEpJ,IAAIJ,EAAW1D,EAAEnC,EAAIzG,EAAIgD,cAAce,MAAMmI,MACzCK,EAAW3D,EAAElC,EAAI1G,EAAIgD,cAAce,MAAMoI,MACzCK,EAAalG,KAAKiF,KAAKe,EAASA,EAAWC,EAASA,GAEpDmB,EAAU,CAACjH,EAAG6F,EAASE,EAAY9F,EAAG6F,EAASC,GAC/CmB,EAAgB3N,EAAIgD,cAAcO,MAAMI,QAAQrB,SAEhDsL,GAihBK9M,EAjhBkB,EAAE6M,IAAiB,EAAErH,KAAKuH,IAAIrB,EAAWmB,EAAc,GAAG,GAAGA,EADzE,IAkhBEG,EAjhB+F,EAihB1FC,EAjhB6F,GAkhBhHzH,KAAKwH,IAAIxH,KAAKyH,IAAIjN,EAAQgN,GAAMC,IAhhB/B9B,EAAM,CACRxF,EAAGmC,EAAEnC,EAAIiH,EAAQjH,EAAImH,EACrBlH,EAAGkC,EAAElC,EAAIgH,EAAQhH,EAAIkH,GAGW,UAA/B5N,EAAIa,UAAU0B,KAAKG,UACjBuJ,EAAIxF,EAAImC,EAAEvC,OAAS,GAAK4F,EAAIxF,EAAImC,EAAEvC,OAASrG,EAAIO,OAAOE,IAAGmI,EAAEnC,EAAIwF,EAAIxF,GACnEwF,EAAIvF,EAAIkC,EAAEvC,OAAS,GAAK4F,EAAIvF,EAAIkC,EAAEvC,OAASrG,EAAIO,OAAOI,IAAGiI,EAAElC,EAAIuF,EAAIvF,KAEtEkC,EAAEnC,EAAIwF,EAAIxF,EACVmC,EAAElC,EAAIuF,EAAIvF,EAGd,MAGK,GAAG1G,EAAIgD,cAAcE,OAAOG,QAAQpC,QAAUmJ,EAAU,UAAWpK,EAAIgD,cAAcE,OAAOG,QAAQD,MASvG,GAPIpD,EAAIoE,IAAI4J,iBACVhO,EAAIoE,IAAI6J,gBACLjO,EAAIoE,IAAI6J,eAAiBjO,EAAIa,UAAUkC,MAAMgE,SAC9C/G,EAAIoE,IAAI4J,gBAAiB,IAI1BhO,EAAIoE,IAAI8J,iBAAiB,CAEtBP,EAAgBrH,KAAKuH,IAAI7N,EAAIgD,cAAcO,MAAMI,QAAQrB,SAAS,EAAG,GAAzE,IAEI8I,EAAKpL,EAAIgD,cAAce,MAAM8I,YAAcjE,EAAEnC,EAC7C4E,EAAKrL,EAAIgD,cAAce,MAAM+I,YAAclE,EAAElC,EAC7CyH,EAAI/C,EAAGA,EAAKC,EAAGA,EAEf+C,GAAST,EAAgBQ,EAAI,EAsB9BA,GAAKR,GApBR,WAEE,IAAIU,EAAI/H,KAAKgI,MAAMjD,EAAGD,GAItB,GAHAxC,EAAEhB,GAAKwG,EAAQ9H,KAAKiI,IAAIF,GACxBzF,EAAEf,GAAKuG,EAAQ9H,KAAKkI,IAAIH,GAEU,UAA/BrO,EAAIa,UAAU0B,KAAKG,SAAqB,CACzC,IAAIuJ,EAAM,CACRxF,EAAGmC,EAAEnC,EAAImC,EAAEhB,GACXlB,EAAGkC,EAAElC,EAAIkC,EAAEf,KAEToE,EAAIxF,EAAImC,EAAEvC,OAASrG,EAAIO,OAAOE,GACzBwL,EAAIxF,EAAImC,EAAEvC,OAAS,KADSuC,EAAEhB,IAAMgB,EAAEhB,KAE3CqE,EAAIvF,EAAIkC,EAAEvC,OAASrG,EAAIO,OAAOI,GACzBsL,EAAIvF,EAAIkC,EAAEvC,OAAS,KADSuC,EAAEf,IAAMe,EAAEf,GAEjD,CAEF,CAIEuF,EAaJ,MAEiC,GAA5BpN,EAAIoE,IAAI8J,mBAETtF,EAAEhB,GAAKgB,EAAEd,KACTc,EAAEf,GAAKe,EAAEb,MAqcnB,IAAejH,EAAQgN,EAAKC,CA7b1B,EAGA/N,EAAIiE,GAAGV,MAAM8G,aAAe,SAASzB,GAEnC,GAAG5I,EAAIgD,cAAcE,OAAOC,QAAQlC,QAAsC,aAA5BjB,EAAIgD,cAAc0J,OAAsB,CAEpF,IAAIJ,EAAW1D,EAAEnC,EAAIzG,EAAIgD,cAAce,MAAMmI,MACzCK,EAAW3D,EAAElC,EAAI1G,EAAIgD,cAAce,MAAMoI,MACzCK,EAAalG,KAAKiF,KAAKe,EAASA,EAAWC,EAASA,GAGxD,GAAGC,GAAcxM,EAAIgD,cAAcO,MAAMC,KAAKlB,SAAS,CAErD,IAAIkJ,EAAexL,EAAIgD,cAAcO,MAAMC,KAAKnB,YAAYR,QAAW2K,GAAc,EAAExM,EAAIgD,cAAcO,MAAMC,KAAKnB,YAAYR,SAAY7B,EAAIgD,cAAcO,MAAMC,KAAKlB,SAEzK,GAAGkJ,EAAe,EAAE,CAGlB,IAAIC,EAAazL,EAAIa,UAAUwB,YAAYqJ,eAC3C1L,EAAIO,OAAOgF,IAAIkE,YAAc,QAAQgC,EAAWtE,EAAE,IAAIsE,EAAWrE,EAAE,IAAIqE,EAAWpE,EAAE,IAAImE,EAAa,IACrGxL,EAAIO,OAAOgF,IAAImE,UAAY1J,EAAIa,UAAUwB,YAAYd,MAIrDvB,EAAIO,OAAOgF,IAAI0D,YACfjJ,EAAIO,OAAOgF,IAAIoG,OAAO/C,EAAEnC,EAAGmC,EAAElC,GAC7B1G,EAAIO,OAAOgF,IAAIqG,OAAO5L,EAAIgD,cAAce,MAAMmI,MAAOlM,EAAIgD,cAAce,MAAMoI,OAC7EnM,EAAIO,OAAOgF,IAAIjE,SACftB,EAAIO,OAAOgF,IAAIiE,WAEjB,CAEF,CAEF,CAEF,EAMAxJ,EAAIiE,GAAGE,QAAQsK,gBAAkB,WAGG,UAA/BzO,EAAIgD,cAAcC,UACnBjD,EAAIgD,cAAcxC,GAAK0E,OAEvBlF,EAAIgD,cAAcxC,GAAKR,EAAIO,OAAOC,IAKjCR,EAAIgD,cAAcE,OAAOC,QAAQlC,QAAUjB,EAAIgD,cAAcE,OAAOG,QAAQpC,UAG7EjB,EAAIgD,cAAcxC,GAAGkF,iBAAiB,YAAa,SAASgJ,GAE1D,GAAG1O,EAAIgD,cAAcxC,IAAM0E,OACzB,IAAIgH,EAAQwC,EAAEC,QACVxC,EAAQuC,EAAEE,aAGV1C,EAAQwC,EAAEG,SAAWH,EAAEC,QACvBxC,EAAQuC,EAAEI,SAAWJ,EAAEE,QAG7B5O,EAAIgD,cAAce,MAAMmI,MAAQA,EAChClM,EAAIgD,cAAce,MAAMoI,MAAQA,EAE7BnM,EAAIoE,IAAIiB,SACTrF,EAAIgD,cAAce,MAAMmI,OAASlM,EAAIO,OAAO6E,QAC5CpF,EAAIgD,cAAce,MAAMoI,OAASnM,EAAIO,OAAO6E,SAG9CpF,EAAIgD,cAAc0J,OAAS,WAE7B,GAGA1M,EAAIgD,cAAcxC,GAAGkF,iBAAiB,aAAc,SAASgJ,GAE3D1O,EAAIgD,cAAce,MAAMmI,MAAQ,KAChClM,EAAIgD,cAAce,MAAMoI,MAAQ,KAChCnM,EAAIgD,cAAc0J,OAAS,YAE7B,IAKC1M,EAAIgD,cAAcE,OAAOG,QAAQpC,QAElCjB,EAAIgD,cAAcxC,GAAGkF,iBAAiB,QAAS,WAM7C,GAJA1F,EAAIgD,cAAce,MAAM8I,YAAc7M,EAAIgD,cAAce,MAAMmI,MAC9DlM,EAAIgD,cAAce,MAAM+I,YAAc9M,EAAIgD,cAAce,MAAMoI,MAC9DnM,EAAIgD,cAAce,MAAMmJ,YAAa,IAAIF,MAAOC,UAE7CjN,EAAIgD,cAAcE,OAAOG,QAAQpC,OAElC,OAAOjB,EAAIgD,cAAcE,OAAOG,QAAQD,MAEtC,IAAK,OACApD,EAAIa,UAAU0B,KAAKtB,QAG4B,GAA7CjB,EAAIgD,cAAcO,MAAMK,KAAKC,aAFhC7D,EAAIiE,GAAGV,MAAMwI,cAAc/L,EAAIgD,cAAcO,MAAMK,KAAKC,aAAc7D,EAAIgD,cAAce,OAKhF/D,EAAIgD,cAAcO,MAAMK,KAAKC,aAAe,GAClD7D,EAAIiE,GAAGV,MAAMwI,cAAc/L,EAAIgD,cAAcO,MAAMK,KAAKC,cAG9D,MAEA,IAAK,SACH7D,EAAIiE,GAAGV,MAAM6I,gBAAgBpM,EAAIgD,cAAcO,MAAMO,OAAOD,cAC9D,MAEA,IAAK,SACH7D,EAAIoE,IAAIwI,iBAAkB,EAC5B,MAEA,IAAK,UACH5M,EAAIoE,IAAI8J,kBAAmB,EAC3BlO,EAAIoE,IAAI6J,cAAgB,EACxBjO,EAAIoE,IAAI4J,gBAAiB,EACzBe,WAAW,WACT/O,EAAIoE,IAAI8J,kBAAmB,CAC7B,EAA4C,IAAzClO,EAAIgD,cAAcO,MAAMI,QAAQD,UAO3C,EAKJ,EAEA1D,EAAIiE,GAAGE,QAAQ2B,qBAAuB,WAEpC,GAAG9F,EAAIa,UAAUC,OAAOE,QAAQC,OAAO,CAGrC,IAAI+N,EAAOhP,EAAIO,OAAOC,GAAGe,MAAQvB,EAAIO,OAAOC,GAAGoB,OAAS,IACrD5B,EAAIoE,IAAIiB,SACT2J,GAAgC,EAAnBhP,EAAIO,OAAO6E,SAI1B,IAAI6J,EAAeD,EAAOhP,EAAIa,UAAUC,OAAOC,MAAQf,EAAIa,UAAUC,OAAOE,QAAQE,WAGhFgO,EAAoBlP,EAAIa,UAAUkC,MAAMgE,OAASkI,EAClDC,EAAoB,EAAGlP,EAAIiE,GAAGV,MAAMwI,cAAczF,KAAK6I,IAAID,IACzDlP,EAAIiE,GAAGV,MAAM6I,gBAAgB8C,EAEpC,CAEF,EAGAlP,EAAIiE,GAAGE,QAAQwC,aAAe,SAASwE,EAAI/E,GACzC,IAAI,IAAIwD,EAAI,EAAGA,EAAI5J,EAAIa,UAAUkC,MAAMgE,OAAQ6C,IAAI,CACjD,IAAIa,EAAKzK,EAAIa,UAAUkC,MAAM6G,GAEzBwB,EAAKD,EAAG1E,EAAIgE,EAAGhE,EACf4E,EAAKF,EAAGzE,EAAI+D,EAAG/D,EACRJ,KAAKiF,KAAKH,EAAGA,EAAKC,EAAGA,IAErBF,EAAG9E,OAASoE,EAAGpE,SACxB8E,EAAG1E,EAAIL,EAAWA,EAASK,EAAIH,KAAKxE,SAAW9B,EAAIO,OAAOE,EAC1D0K,EAAGzE,EAAIN,EAAWA,EAASM,EAAIJ,KAAKxE,SAAW9B,EAAIO,OAAOI,EAC1DX,EAAIiE,GAAGE,QAAQwC,aAAawE,GAEhC,CACF,EAGAnL,EAAIiE,GAAGE,QAAQoE,aAAe,SAASK,GAGrC,IAEIwG,EAFSpP,EAAIoE,IAAIkE,WAEM+G,QADd,qBAC8B,SAAUC,EAAGnI,EAAGC,EAAGC,GACxD,GAAGuB,EAAEzH,MAAM6F,IACT,IAAI+B,EAAc,QAAQH,EAAEzH,MAAM6F,IAAIG,EAAE,IAAIyB,EAAEzH,MAAM6F,IAAII,EAAE,IAAIwB,EAAEzH,MAAM6F,IAAIK,EAAE,IAAIuB,EAAE/G,QAAQ,SAEtFkH,EAAc,QAAQH,EAAEzH,MAAMqG,IAAI7G,EAAE,IAAIiI,EAAEzH,MAAMqG,IAAIF,EAAE,KAAKsB,EAAEzH,MAAMqG,IAAID,EAAE,KAAKqB,EAAE/G,QAAQ,IAE9F,OAAOkH,CACT,GAGAwG,EAAM,IAAIC,KAAK,CAACJ,GAAgB,CAAC/N,KAAM,gCACvCoO,EAASvK,OAAOwK,KAAOxK,OAAOyK,WAAazK,OAC3C0K,EAAMH,EAAOI,gBAAgBN,GAG7BpH,EAAM,IAAI2H,MACd3H,EAAIzC,iBAAiB,OAAQ,WAC3BkD,EAAET,IAAI5D,IAAM4D,EACZS,EAAET,IAAIM,QAAS,EACfgH,EAAOM,gBAAgBH,GACvB5P,EAAIoE,IAAI6G,WACV,GACA9C,EAAIxG,IAAMiO,CAEZ,EAGA5P,EAAIiE,GAAGE,QAAQ6L,WAAa,WAC1BC,qBAAqBjQ,EAAIiE,GAAG+G,eAC5B7K,EAAU2D,SACVoM,OAAS,IACX,EAGAlQ,EAAIiE,GAAGE,QAAQkF,UAAY,SAAS8G,EAAGC,EAAQC,EAAQC,EAAYC,EAAoBC,GAGrF,IAAIC,EAAYF,EAAqBC,EACjCE,EAAeH,EAAqBC,EACpCG,EAAwB,KAAOD,EAAe,GAAMA,EACpDE,EAAgBtK,KAAK6C,GAAK7C,KAAK6C,GAAKwH,EAAuB,IAC/DR,EAAEU,OACFV,EAAElH,YACFkH,EAAEW,UAAUV,EAAQC,GACpBF,EAAExE,OAAO,EAAE,GACX,IAAK,IAAI/B,EAAI,EAAGA,EAAI6G,EAAW7G,IAC7BuG,EAAEvE,OAAO0E,EAAW,GACpBH,EAAEW,UAAUR,EAAW,GACvBH,EAAEY,OAAOH,GAGXT,EAAExG,OACFwG,EAAEa,SAEJ,EAEAhR,EAAIiE,GAAGE,QAAQ8M,UAAY,WACzB/L,OAAOgM,KAAKlR,EAAIO,OAAOC,GAAG2Q,UAAU,aAAc,SACpD,EAGAnR,EAAIiE,GAAGE,QAAQiN,QAAU,SAAS/P,GAIhC,GAFArB,EAAIoE,IAAIiN,eAAYnK,EAEgB,IAAjClH,EAAIa,UAAUO,MAAMM,MAAMC,IAE3B,GAAW,OAARN,EAAc,CAEf,IAAIiQ,EAAM,IAAIC,eACdD,EAAIJ,KAAK,MAAOlR,EAAIa,UAAUO,MAAMM,MAAMC,KAC1C2P,EAAIE,mBAAqB,SAAUC,GACZ,GAAlBH,EAAII,aACY,KAAdJ,EAAI5E,QACL1M,EAAIoE,IAAIkE,WAAamJ,EAAKE,cAAcC,SACxC5R,EAAIiE,GAAGE,QAAQ0N,oBAEfC,QAAQC,IAAI,+BACZ/R,EAAIoE,IAAIiN,WAAY,GAG1B,EACAC,EAAIU,MAEN,KAAK,CAEH,IAAI7J,EAAM,IAAI2H,MACd3H,EAAIzC,iBAAiB,OAAQ,WAC3B1F,EAAIoE,IAAIkF,QAAUnB,EAClBnI,EAAIiE,GAAGE,QAAQ0N,iBACjB,GACA1J,EAAIxG,IAAM3B,EAAIa,UAAUO,MAAMM,MAAMC,GAEtC,MAGAmQ,QAAQC,IAAI,4BACZ/R,EAAIoE,IAAIiN,WAAY,CAGxB,EAGArR,EAAIiE,GAAGE,QAAQwE,KAAO,WAEW,SAA5B3I,EAAIa,UAAUO,MAAMC,KAEE,OAApBrB,EAAIoE,IAAIiE,SAENrI,EAAIoE,IAAI6G,WAAajL,EAAIa,UAAUC,OAAOC,OAC3Cf,EAAIiE,GAAG4B,gBACH7F,EAAIa,UAAU0B,KAAKtB,OAClBjB,EAAIiE,GAAG+G,cAAgBiH,iBAAiBjS,EAAIiE,GAAGE,QAAQwE,MAD7BmC,uBAAuB9K,EAAIiE,GAAG+G,gBAIzDhL,EAAIoE,IAAIiN,YAAWrR,EAAIiE,GAAG+G,cAAgBiH,iBAAiBjS,EAAIiE,GAAGE,QAAQwE,OAK1DzB,MAAnBlH,EAAIoE,IAAIkF,SACTtJ,EAAIiE,GAAG4B,gBACH7F,EAAIa,UAAU0B,KAAKtB,OAClBjB,EAAIiE,GAAG+G,cAAgBiH,iBAAiBjS,EAAIiE,GAAGE,QAAQwE,MAD7BmC,uBAAuB9K,EAAIiE,GAAG+G,gBAGzDhL,EAAIoE,IAAIiN,YAAWrR,EAAIiE,GAAG+G,cAAgBiH,iBAAiBjS,EAAIiE,GAAGE,QAAQwE,QAMlF3I,EAAIiE,GAAG4B,gBACH7F,EAAIa,UAAU0B,KAAKtB,OAClBjB,EAAIiE,GAAG+G,cAAgBiH,iBAAiBjS,EAAIiE,GAAGE,QAAQwE,MAD7BmC,uBAAuB9K,EAAIiE,GAAG+G,eAIjE,EAGAhL,EAAIiE,GAAGE,QAAQ0N,gBAAkB,WAGA,SAA5B7R,EAAIa,UAAUO,MAAMC,KAEE,OAApBrB,EAAIoE,IAAIiE,UAA2CnB,MAAtBlH,EAAIoE,IAAIkE,WACtCtI,EAAIoE,IAAI2G,eAAiBkH,iBAAiBC,QAG1CpH,uBAAuB9K,EAAIoE,IAAI2G,gBAC3B/K,EAAIoE,IAAIiN,YACVrR,EAAIiE,GAAGE,QAAQsI,OACfzM,EAAIiE,GAAGE,QAAQwE,UAMnB3I,EAAIiE,GAAGE,QAAQsI,OACfzM,EAAIiE,GAAGE,QAAQwE,OAGnB,EAGA3I,EAAIiE,GAAGE,QAAQsI,KAAO,WAGpBzM,EAAIiE,GAAGgB,aACPjF,EAAIiE,GAAGqB,aACPtF,EAAIiE,GAAGwB,aACPzF,EAAIiE,GAAG8B,cACP/F,EAAIiE,GAAG2B,kBACP5F,EAAIiE,GAAGE,QAAQ2B,uBAGf9F,EAAIa,UAAUwB,YAAYqJ,eAAiBzE,EAASjH,EAAIa,UAAUwB,YAAYlB,MAEhF,EAGAnB,EAAIiE,GAAGE,QAAQ+G,MAAQ,WAElBd,EAAU,QAASpK,EAAIa,UAAUO,MAAMC,OACxCrB,EAAIoE,IAAIiE,SAAWrI,EAAIa,UAAUO,MAAMM,MAAMC,IAAIwQ,OAAOnS,EAAIa,UAAUO,MAAMM,MAAMC,IAAIoF,OAAS,GAC/F/G,EAAIiE,GAAGE,QAAQiN,QAAQpR,EAAIoE,IAAIiE,WAE/BrI,EAAIiE,GAAGE,QAAQ0N,iBAGnB,EAQA7R,EAAIiE,GAAGE,QAAQsK,kBAEfzO,EAAIiE,GAAGE,QAAQ+G,OAIjB,EAqCA,SAASjE,EAASmL,GAIhBA,EAAMA,EAAI/C,QADW,mCACa,SAASC,EAAGnI,EAAGC,EAAGC,GACjD,OAAOF,EAAIA,EAAIC,EAAIA,EAAIC,EAAIA,CAC9B,GACA,IAAIgL,EAAS,4CAA4CC,KAAKF,GAC9D,OAAOC,EAAS,CACZlL,EAAGoL,SAASF,EAAO,GAAI,IACvBjL,EAAGmL,SAASF,EAAO,GAAI,IACvBhL,EAAGkL,SAASF,EAAO,GAAI,KACvB,IACN,CAMA,SAASjI,EAAUrJ,EAAOgC,GACxB,OAAOA,EAAMyP,QAAQzR,IAAU,CACjC,CAtDAsD,OAAOC,WAAa,SAASmO,EAAaC,GACxC,IAAK,IAAIC,KAAYD,EACfA,EAAOC,IAAaD,EAAOC,GAAUC,aACxCF,EAAOC,GAAUC,cAAgBvO,QAChCoO,EAAYE,GAAYF,EAAYE,IAAa,CAAC,EAClDE,UAAUC,OAAOL,EAAYE,GAAWD,EAAOC,KAE/CF,EAAYE,GAAYD,EAAOC,GAGnC,OAAOF,CACT,EAEAvN,OAAO+M,iBACG/M,OAAO6N,uBACb7N,OAAO8N,6BACP9N,OAAO+N,0BACP/N,OAAOgO,wBACPhO,OAAOiO,yBACP,SAASC,GACPlO,OAAO6J,WAAWqE,EAAU,IAAO,GACrC,EAGJlO,OAAO4F,uBACE5F,OAAO+K,sBACZ/K,OAAOmO,mCACPnO,OAAOoO,gCACPpO,OAAOqO,8BACPrO,OAAOsO,+BACPC,aA6BJvO,OAAOgL,OAAS,GAEhBhL,OAAOwO,YAAc,SAASzT,EAAQC,GAKf,iBAAXD,IACRC,EAASD,EACTA,EAAS,gBAIPA,IACFA,EAAS,gBAIX,IAAI0T,EAAUvT,SAASwT,eAAe3T,GAClC4T,EAAmB,yBACnBC,EAAeH,EAAQI,uBAAuBF,GAGlD,GAAGC,EAAa/M,OACd,KAAM+M,EAAa/M,OAAS,GAC1B4M,EAAQK,YAAYF,EAAa,IAKrC,IAAI3T,EAAYC,SAAS6T,cAAc,UACvC9T,EAAU+T,UAAYL,EAGtB1T,EAAUgU,MAAM5S,MAAQ,OACxBpB,EAAUgU,MAAMvS,OAAS,OAMZ,MAHAxB,SAASwT,eAAe3T,GAAQmU,YAAYjU,IAIvD+P,OAAOtM,KAAK,IAAI5D,EAAIC,EAAQC,GAGhC,EAEAgF,OAAOwO,YAAYW,KAAO,SAASpU,EAAQqU,EAAkBlB,GAG3D,IAAI9B,EAAM,IAAIC,eACdD,EAAIJ,KAAK,MAAOoD,GAChBhD,EAAIE,mBAAqB,SAAUC,GACjC,GAAqB,GAAlBH,EAAII,WACL,GAAiB,KAAdJ,EAAI5E,OAAc,CACnB,IAAIxM,EAASqU,KAAKC,MAAM/C,EAAKE,cAAcC,UAC3C1M,OAAOwO,YAAYzT,EAAQC,GACxBkT,GAAUA,GACf,MACEtB,QAAQC,IAAI,sCAAsCT,EAAI5E,QACtDoF,QAAQC,IAAI,oCAGlB,EACAT,EAAIU,MAEN,C", "sources": ["../node_modules/particles.js/particles.js"], "sourcesContent": ["/* -----------------------------------------------\n/* Author : <PERSON>  - vincentgarreau.com\n/* MIT license: http://opensource.org/licenses/MIT\n/* Demo / Generator : vincentgarreau.com/particles.js\n/* GitHub : github.com/VincentGarreau/particles.js\n/* How to use? : Check the GitHub README\n/* v2.0.0\n/* ----------------------------------------------- */\n\nvar pJS = function(tag_id, params){\n\n  var canvas_el = document.querySelector('#'+tag_id+' > .particles-js-canvas-el');\n\n  /* particles.js variables with default values */\n  this.pJS = {\n    canvas: {\n      el: canvas_el,\n      w: canvas_el.offsetWidth,\n      h: canvas_el.offsetHeight\n    },\n    particles: {\n      number: {\n        value: 400,\n        density: {\n          enable: true,\n          value_area: 800\n        }\n      },\n      color: {\n        value: '#fff'\n      },\n      shape: {\n        type: 'circle',\n        stroke: {\n          width: 0,\n          color: '#ff0000'\n        },\n        polygon: {\n          nb_sides: 5\n        },\n        image: {\n          src: '',\n          width: 100,\n          height: 100\n        }\n      },\n      opacity: {\n        value: 1,\n        random: false,\n        anim: {\n          enable: false,\n          speed: 2,\n          opacity_min: 0,\n          sync: false\n        }\n      },\n      size: {\n        value: 20,\n        random: false,\n        anim: {\n          enable: false,\n          speed: 20,\n          size_min: 0,\n          sync: false\n        }\n      },\n      line_linked: {\n        enable: true,\n        distance: 100,\n        color: '#fff',\n        opacity: 1,\n        width: 1\n      },\n      move: {\n        enable: true,\n        speed: 2,\n        direction: 'none',\n        random: false,\n        straight: false,\n        out_mode: 'out',\n        bounce: false,\n        attract: {\n          enable: false,\n          rotateX: 3000,\n          rotateY: 3000\n        }\n      },\n      array: []\n    },\n    interactivity: {\n      detect_on: 'canvas',\n      events: {\n        onhover: {\n          enable: true,\n          mode: 'grab'\n        },\n        onclick: {\n          enable: true,\n          mode: 'push'\n        },\n        resize: true\n      },\n      modes: {\n        grab:{\n          distance: 100,\n          line_linked:{\n            opacity: 1\n          }\n        },\n        bubble:{\n          distance: 200,\n          size: 80,\n          duration: 0.4\n        },\n        repulse:{\n          distance: 200,\n          duration: 0.4\n        },\n        push:{\n          particles_nb: 4\n        },\n        remove:{\n          particles_nb: 2\n        }\n      },\n      mouse:{}\n    },\n    retina_detect: false,\n    fn: {\n      interact: {},\n      modes: {},\n      vendors:{}\n    },\n    tmp: {}\n  };\n\n  var pJS = this.pJS;\n\n  /* params settings */\n  if(params){\n    Object.deepExtend(pJS, params);\n  }\n\n  pJS.tmp.obj = {\n    size_value: pJS.particles.size.value,\n    size_anim_speed: pJS.particles.size.anim.speed,\n    move_speed: pJS.particles.move.speed,\n    line_linked_distance: pJS.particles.line_linked.distance,\n    line_linked_width: pJS.particles.line_linked.width,\n    mode_grab_distance: pJS.interactivity.modes.grab.distance,\n    mode_bubble_distance: pJS.interactivity.modes.bubble.distance,\n    mode_bubble_size: pJS.interactivity.modes.bubble.size,\n    mode_repulse_distance: pJS.interactivity.modes.repulse.distance\n  };\n\n\n  pJS.fn.retinaInit = function(){\n\n    if(pJS.retina_detect && window.devicePixelRatio > 1){\n      pJS.canvas.pxratio = window.devicePixelRatio; \n      pJS.tmp.retina = true;\n    } \n    else{\n      pJS.canvas.pxratio = 1;\n      pJS.tmp.retina = false;\n    }\n\n    pJS.canvas.w = pJS.canvas.el.offsetWidth * pJS.canvas.pxratio;\n    pJS.canvas.h = pJS.canvas.el.offsetHeight * pJS.canvas.pxratio;\n\n    pJS.particles.size.value = pJS.tmp.obj.size_value * pJS.canvas.pxratio;\n    pJS.particles.size.anim.speed = pJS.tmp.obj.size_anim_speed * pJS.canvas.pxratio;\n    pJS.particles.move.speed = pJS.tmp.obj.move_speed * pJS.canvas.pxratio;\n    pJS.particles.line_linked.distance = pJS.tmp.obj.line_linked_distance * pJS.canvas.pxratio;\n    pJS.interactivity.modes.grab.distance = pJS.tmp.obj.mode_grab_distance * pJS.canvas.pxratio;\n    pJS.interactivity.modes.bubble.distance = pJS.tmp.obj.mode_bubble_distance * pJS.canvas.pxratio;\n    pJS.particles.line_linked.width = pJS.tmp.obj.line_linked_width * pJS.canvas.pxratio;\n    pJS.interactivity.modes.bubble.size = pJS.tmp.obj.mode_bubble_size * pJS.canvas.pxratio;\n    pJS.interactivity.modes.repulse.distance = pJS.tmp.obj.mode_repulse_distance * pJS.canvas.pxratio;\n\n  };\n\n\n\n  /* ---------- pJS functions - canvas ------------ */\n\n  pJS.fn.canvasInit = function(){\n    pJS.canvas.ctx = pJS.canvas.el.getContext('2d');\n  };\n\n  pJS.fn.canvasSize = function(){\n\n    pJS.canvas.el.width = pJS.canvas.w;\n    pJS.canvas.el.height = pJS.canvas.h;\n\n    if(pJS && pJS.interactivity.events.resize){\n\n      window.addEventListener('resize', function(){\n\n          pJS.canvas.w = pJS.canvas.el.offsetWidth;\n          pJS.canvas.h = pJS.canvas.el.offsetHeight;\n\n          /* resize canvas */\n          if(pJS.tmp.retina){\n            pJS.canvas.w *= pJS.canvas.pxratio;\n            pJS.canvas.h *= pJS.canvas.pxratio;\n          }\n\n          pJS.canvas.el.width = pJS.canvas.w;\n          pJS.canvas.el.height = pJS.canvas.h;\n\n          /* repaint canvas on anim disabled */\n          if(!pJS.particles.move.enable){\n            pJS.fn.particlesEmpty();\n            pJS.fn.particlesCreate();\n            pJS.fn.particlesDraw();\n            pJS.fn.vendors.densityAutoParticles();\n          }\n\n        /* density particles enabled */\n        pJS.fn.vendors.densityAutoParticles();\n\n      });\n\n    }\n\n  };\n\n\n  pJS.fn.canvasPaint = function(){\n    pJS.canvas.ctx.fillRect(0, 0, pJS.canvas.w, pJS.canvas.h);\n  };\n\n  pJS.fn.canvasClear = function(){\n    pJS.canvas.ctx.clearRect(0, 0, pJS.canvas.w, pJS.canvas.h);\n  };\n\n\n  /* --------- pJS functions - particles ----------- */\n\n  pJS.fn.particle = function(color, opacity, position){\n\n    /* size */\n    this.radius = (pJS.particles.size.random ? Math.random() : 1) * pJS.particles.size.value;\n    if(pJS.particles.size.anim.enable){\n      this.size_status = false;\n      this.vs = pJS.particles.size.anim.speed / 100;\n      if(!pJS.particles.size.anim.sync){\n        this.vs = this.vs * Math.random();\n      }\n    }\n\n    /* position */\n    this.x = position ? position.x : Math.random() * pJS.canvas.w;\n    this.y = position ? position.y : Math.random() * pJS.canvas.h;\n\n    /* check position  - into the canvas */\n    if(this.x > pJS.canvas.w - this.radius*2) this.x = this.x - this.radius;\n    else if(this.x < this.radius*2) this.x = this.x + this.radius;\n    if(this.y > pJS.canvas.h - this.radius*2) this.y = this.y - this.radius;\n    else if(this.y < this.radius*2) this.y = this.y + this.radius;\n\n    /* check position - avoid overlap */\n    if(pJS.particles.move.bounce){\n      pJS.fn.vendors.checkOverlap(this, position);\n    }\n\n    /* color */\n    this.color = {};\n    if(typeof(color.value) == 'object'){\n\n      if(color.value instanceof Array){\n        var color_selected = color.value[Math.floor(Math.random() * pJS.particles.color.value.length)];\n        this.color.rgb = hexToRgb(color_selected);\n      }else{\n        if(color.value.r != undefined && color.value.g != undefined && color.value.b != undefined){\n          this.color.rgb = {\n            r: color.value.r,\n            g: color.value.g,\n            b: color.value.b\n          }\n        }\n        if(color.value.h != undefined && color.value.s != undefined && color.value.l != undefined){\n          this.color.hsl = {\n            h: color.value.h,\n            s: color.value.s,\n            l: color.value.l\n          }\n        }\n      }\n\n    }\n    else if(color.value == 'random'){\n      this.color.rgb = {\n        r: (Math.floor(Math.random() * (255 - 0 + 1)) + 0),\n        g: (Math.floor(Math.random() * (255 - 0 + 1)) + 0),\n        b: (Math.floor(Math.random() * (255 - 0 + 1)) + 0)\n      }\n    }\n    else if(typeof(color.value) == 'string'){\n      this.color = color;\n      this.color.rgb = hexToRgb(this.color.value);\n    }\n\n    /* opacity */\n    this.opacity = (pJS.particles.opacity.random ? Math.random() : 1) * pJS.particles.opacity.value;\n    if(pJS.particles.opacity.anim.enable){\n      this.opacity_status = false;\n      this.vo = pJS.particles.opacity.anim.speed / 100;\n      if(!pJS.particles.opacity.anim.sync){\n        this.vo = this.vo * Math.random();\n      }\n    }\n\n    /* animation - velocity for speed */\n    var velbase = {}\n    switch(pJS.particles.move.direction){\n      case 'top':\n        velbase = { x:0, y:-1 };\n      break;\n      case 'top-right':\n        velbase = { x:0.5, y:-0.5 };\n      break;\n      case 'right':\n        velbase = { x:1, y:-0 };\n      break;\n      case 'bottom-right':\n        velbase = { x:0.5, y:0.5 };\n      break;\n      case 'bottom':\n        velbase = { x:0, y:1 };\n      break;\n      case 'bottom-left':\n        velbase = { x:-0.5, y:1 };\n      break;\n      case 'left':\n        velbase = { x:-1, y:0 };\n      break;\n      case 'top-left':\n        velbase = { x:-0.5, y:-0.5 };\n      break;\n      default:\n        velbase = { x:0, y:0 };\n      break;\n    }\n\n    if(pJS.particles.move.straight){\n      this.vx = velbase.x;\n      this.vy = velbase.y;\n      if(pJS.particles.move.random){\n        this.vx = this.vx * (Math.random());\n        this.vy = this.vy * (Math.random());\n      }\n    }else{\n      this.vx = velbase.x + Math.random()-0.5;\n      this.vy = velbase.y + Math.random()-0.5;\n    }\n\n    // var theta = 2.0 * Math.PI * Math.random();\n    // this.vx = Math.cos(theta);\n    // this.vy = Math.sin(theta);\n\n    this.vx_i = this.vx;\n    this.vy_i = this.vy;\n\n    \n\n    /* if shape is image */\n\n    var shape_type = pJS.particles.shape.type;\n    if(typeof(shape_type) == 'object'){\n      if(shape_type instanceof Array){\n        var shape_selected = shape_type[Math.floor(Math.random() * shape_type.length)];\n        this.shape = shape_selected;\n      }\n    }else{\n      this.shape = shape_type;\n    }\n\n    if(this.shape == 'image'){\n      var sh = pJS.particles.shape;\n      this.img = {\n        src: sh.image.src,\n        ratio: sh.image.width / sh.image.height\n      }\n      if(!this.img.ratio) this.img.ratio = 1;\n      if(pJS.tmp.img_type == 'svg' && pJS.tmp.source_svg != undefined){\n        pJS.fn.vendors.createSvgImg(this);\n        if(pJS.tmp.pushing){\n          this.img.loaded = false;\n        }\n      }\n    }\n\n    \n\n  };\n\n\n  pJS.fn.particle.prototype.draw = function() {\n\n    var p = this;\n\n    if(p.radius_bubble != undefined){\n      var radius = p.radius_bubble; \n    }else{\n      var radius = p.radius;\n    }\n\n    if(p.opacity_bubble != undefined){\n      var opacity = p.opacity_bubble;\n    }else{\n      var opacity = p.opacity;\n    }\n\n    if(p.color.rgb){\n      var color_value = 'rgba('+p.color.rgb.r+','+p.color.rgb.g+','+p.color.rgb.b+','+opacity+')';\n    }else{\n      var color_value = 'hsla('+p.color.hsl.h+','+p.color.hsl.s+'%,'+p.color.hsl.l+'%,'+opacity+')';\n    }\n\n    pJS.canvas.ctx.fillStyle = color_value;\n    pJS.canvas.ctx.beginPath();\n\n    switch(p.shape){\n\n      case 'circle':\n        pJS.canvas.ctx.arc(p.x, p.y, radius, 0, Math.PI * 2, false);\n      break;\n\n      case 'edge':\n        pJS.canvas.ctx.rect(p.x-radius, p.y-radius, radius*2, radius*2);\n      break;\n\n      case 'triangle':\n        pJS.fn.vendors.drawShape(pJS.canvas.ctx, p.x-radius, p.y+radius / 1.66, radius*2, 3, 2);\n      break;\n\n      case 'polygon':\n        pJS.fn.vendors.drawShape(\n          pJS.canvas.ctx,\n          p.x - radius / (pJS.particles.shape.polygon.nb_sides/3.5), // startX\n          p.y - radius / (2.66/3.5), // startY\n          radius*2.66 / (pJS.particles.shape.polygon.nb_sides/3), // sideLength\n          pJS.particles.shape.polygon.nb_sides, // sideCountNumerator\n          1 // sideCountDenominator\n        );\n      break;\n\n      case 'star':\n        pJS.fn.vendors.drawShape(\n          pJS.canvas.ctx,\n          p.x - radius*2 / (pJS.particles.shape.polygon.nb_sides/4), // startX\n          p.y - radius / (2*2.66/3.5), // startY\n          radius*2*2.66 / (pJS.particles.shape.polygon.nb_sides/3), // sideLength\n          pJS.particles.shape.polygon.nb_sides, // sideCountNumerator\n          2 // sideCountDenominator\n        );\n      break;\n\n      case 'image':\n\n        function draw(){\n          pJS.canvas.ctx.drawImage(\n            img_obj,\n            p.x-radius,\n            p.y-radius,\n            radius*2,\n            radius*2 / p.img.ratio\n          );\n        }\n\n        if(pJS.tmp.img_type == 'svg'){\n          var img_obj = p.img.obj;\n        }else{\n          var img_obj = pJS.tmp.img_obj;\n        }\n\n        if(img_obj){\n          draw();\n        }\n\n      break;\n\n    }\n\n    pJS.canvas.ctx.closePath();\n\n    if(pJS.particles.shape.stroke.width > 0){\n      pJS.canvas.ctx.strokeStyle = pJS.particles.shape.stroke.color;\n      pJS.canvas.ctx.lineWidth = pJS.particles.shape.stroke.width;\n      pJS.canvas.ctx.stroke();\n    }\n    \n    pJS.canvas.ctx.fill();\n    \n  };\n\n\n  pJS.fn.particlesCreate = function(){\n    for(var i = 0; i < pJS.particles.number.value; i++) {\n      pJS.particles.array.push(new pJS.fn.particle(pJS.particles.color, pJS.particles.opacity.value));\n    }\n  };\n\n  pJS.fn.particlesUpdate = function(){\n\n    for(var i = 0; i < pJS.particles.array.length; i++){\n\n      /* the particle */\n      var p = pJS.particles.array[i];\n\n      // var d = ( dx = pJS.interactivity.mouse.click_pos_x - p.x ) * dx + ( dy = pJS.interactivity.mouse.click_pos_y - p.y ) * dy;\n      // var f = -BANG_SIZE / d;\n      // if ( d < BANG_SIZE ) {\n      //     var t = Math.atan2( dy, dx );\n      //     p.vx = f * Math.cos(t);\n      //     p.vy = f * Math.sin(t);\n      // }\n\n      /* move the particle */\n      if(pJS.particles.move.enable){\n        var ms = pJS.particles.move.speed/2;\n        p.x += p.vx * ms;\n        p.y += p.vy * ms;\n      }\n\n      /* change opacity status */\n      if(pJS.particles.opacity.anim.enable) {\n        if(p.opacity_status == true) {\n          if(p.opacity >= pJS.particles.opacity.value) p.opacity_status = false;\n          p.opacity += p.vo;\n        }else {\n          if(p.opacity <= pJS.particles.opacity.anim.opacity_min) p.opacity_status = true;\n          p.opacity -= p.vo;\n        }\n        if(p.opacity < 0) p.opacity = 0;\n      }\n\n      /* change size */\n      if(pJS.particles.size.anim.enable){\n        if(p.size_status == true){\n          if(p.radius >= pJS.particles.size.value) p.size_status = false;\n          p.radius += p.vs;\n        }else{\n          if(p.radius <= pJS.particles.size.anim.size_min) p.size_status = true;\n          p.radius -= p.vs;\n        }\n        if(p.radius < 0) p.radius = 0;\n      }\n\n      /* change particle position if it is out of canvas */\n      if(pJS.particles.move.out_mode == 'bounce'){\n        var new_pos = {\n          x_left: p.radius,\n          x_right:  pJS.canvas.w,\n          y_top: p.radius,\n          y_bottom: pJS.canvas.h\n        }\n      }else{\n        var new_pos = {\n          x_left: -p.radius,\n          x_right: pJS.canvas.w + p.radius,\n          y_top: -p.radius,\n          y_bottom: pJS.canvas.h + p.radius\n        }\n      }\n\n      if(p.x - p.radius > pJS.canvas.w){\n        p.x = new_pos.x_left;\n        p.y = Math.random() * pJS.canvas.h;\n      }\n      else if(p.x + p.radius < 0){\n        p.x = new_pos.x_right;\n        p.y = Math.random() * pJS.canvas.h;\n      }\n      if(p.y - p.radius > pJS.canvas.h){\n        p.y = new_pos.y_top;\n        p.x = Math.random() * pJS.canvas.w;\n      }\n      else if(p.y + p.radius < 0){\n        p.y = new_pos.y_bottom;\n        p.x = Math.random() * pJS.canvas.w;\n      }\n\n      /* out of canvas modes */\n      switch(pJS.particles.move.out_mode){\n        case 'bounce':\n          if (p.x + p.radius > pJS.canvas.w) p.vx = -p.vx;\n          else if (p.x - p.radius < 0) p.vx = -p.vx;\n          if (p.y + p.radius > pJS.canvas.h) p.vy = -p.vy;\n          else if (p.y - p.radius < 0) p.vy = -p.vy;\n        break;\n      }\n\n      /* events */\n      if(isInArray('grab', pJS.interactivity.events.onhover.mode)){\n        pJS.fn.modes.grabParticle(p);\n      }\n\n      if(isInArray('bubble', pJS.interactivity.events.onhover.mode) || isInArray('bubble', pJS.interactivity.events.onclick.mode)){\n        pJS.fn.modes.bubbleParticle(p);\n      }\n\n      if(isInArray('repulse', pJS.interactivity.events.onhover.mode) || isInArray('repulse', pJS.interactivity.events.onclick.mode)){\n        pJS.fn.modes.repulseParticle(p);\n      }\n\n      /* interaction auto between particles */\n      if(pJS.particles.line_linked.enable || pJS.particles.move.attract.enable){\n        for(var j = i + 1; j < pJS.particles.array.length; j++){\n          var p2 = pJS.particles.array[j];\n\n          /* link particles */\n          if(pJS.particles.line_linked.enable){\n            pJS.fn.interact.linkParticles(p,p2);\n          }\n\n          /* attract particles */\n          if(pJS.particles.move.attract.enable){\n            pJS.fn.interact.attractParticles(p,p2);\n          }\n\n          /* bounce particles */\n          if(pJS.particles.move.bounce){\n            pJS.fn.interact.bounceParticles(p,p2);\n          }\n\n        }\n      }\n\n\n    }\n\n  };\n\n  pJS.fn.particlesDraw = function(){\n\n    /* clear canvas */\n    pJS.canvas.ctx.clearRect(0, 0, pJS.canvas.w, pJS.canvas.h);\n\n    /* update each particles param */\n    pJS.fn.particlesUpdate();\n\n    /* draw each particle */\n    for(var i = 0; i < pJS.particles.array.length; i++){\n      var p = pJS.particles.array[i];\n      p.draw();\n    }\n\n  };\n\n  pJS.fn.particlesEmpty = function(){\n    pJS.particles.array = [];\n  };\n\n  pJS.fn.particlesRefresh = function(){\n\n    /* init all */\n    cancelRequestAnimFrame(pJS.fn.checkAnimFrame);\n    cancelRequestAnimFrame(pJS.fn.drawAnimFrame);\n    pJS.tmp.source_svg = undefined;\n    pJS.tmp.img_obj = undefined;\n    pJS.tmp.count_svg = 0;\n    pJS.fn.particlesEmpty();\n    pJS.fn.canvasClear();\n    \n    /* restart */\n    pJS.fn.vendors.start();\n\n  };\n\n\n  /* ---------- pJS functions - particles interaction ------------ */\n\n  pJS.fn.interact.linkParticles = function(p1, p2){\n\n    var dx = p1.x - p2.x,\n        dy = p1.y - p2.y,\n        dist = Math.sqrt(dx*dx + dy*dy);\n\n    /* draw a line between p1 and p2 if the distance between them is under the config distance */\n    if(dist <= pJS.particles.line_linked.distance){\n\n      var opacity_line = pJS.particles.line_linked.opacity - (dist / (1/pJS.particles.line_linked.opacity)) / pJS.particles.line_linked.distance;\n\n      if(opacity_line > 0){        \n        \n        /* style */\n        var color_line = pJS.particles.line_linked.color_rgb_line;\n        pJS.canvas.ctx.strokeStyle = 'rgba('+color_line.r+','+color_line.g+','+color_line.b+','+opacity_line+')';\n        pJS.canvas.ctx.lineWidth = pJS.particles.line_linked.width;\n        //pJS.canvas.ctx.lineCap = 'round'; /* performance issue */\n        \n        /* path */\n        pJS.canvas.ctx.beginPath();\n        pJS.canvas.ctx.moveTo(p1.x, p1.y);\n        pJS.canvas.ctx.lineTo(p2.x, p2.y);\n        pJS.canvas.ctx.stroke();\n        pJS.canvas.ctx.closePath();\n\n      }\n\n    }\n\n  };\n\n\n  pJS.fn.interact.attractParticles  = function(p1, p2){\n\n    /* condensed particles */\n    var dx = p1.x - p2.x,\n        dy = p1.y - p2.y,\n        dist = Math.sqrt(dx*dx + dy*dy);\n\n    if(dist <= pJS.particles.line_linked.distance){\n\n      var ax = dx/(pJS.particles.move.attract.rotateX*1000),\n          ay = dy/(pJS.particles.move.attract.rotateY*1000);\n\n      p1.vx -= ax;\n      p1.vy -= ay;\n\n      p2.vx += ax;\n      p2.vy += ay;\n\n    }\n    \n\n  }\n\n\n  pJS.fn.interact.bounceParticles = function(p1, p2){\n\n    var dx = p1.x - p2.x,\n        dy = p1.y - p2.y,\n        dist = Math.sqrt(dx*dx + dy*dy),\n        dist_p = p1.radius+p2.radius;\n\n    if(dist <= dist_p){\n      p1.vx = -p1.vx;\n      p1.vy = -p1.vy;\n\n      p2.vx = -p2.vx;\n      p2.vy = -p2.vy;\n    }\n\n  }\n\n\n  /* ---------- pJS functions - modes events ------------ */\n\n  pJS.fn.modes.pushParticles = function(nb, pos){\n\n    pJS.tmp.pushing = true;\n\n    for(var i = 0; i < nb; i++){\n      pJS.particles.array.push(\n        new pJS.fn.particle(\n          pJS.particles.color,\n          pJS.particles.opacity.value,\n          {\n            'x': pos ? pos.pos_x : Math.random() * pJS.canvas.w,\n            'y': pos ? pos.pos_y : Math.random() * pJS.canvas.h\n          }\n        )\n      )\n      if(i == nb-1){\n        if(!pJS.particles.move.enable){\n          pJS.fn.particlesDraw();\n        }\n        pJS.tmp.pushing = false;\n      }\n    }\n\n  };\n\n\n  pJS.fn.modes.removeParticles = function(nb){\n\n    pJS.particles.array.splice(0, nb);\n    if(!pJS.particles.move.enable){\n      pJS.fn.particlesDraw();\n    }\n\n  };\n\n\n  pJS.fn.modes.bubbleParticle = function(p){\n\n    /* on hover event */\n    if(pJS.interactivity.events.onhover.enable && isInArray('bubble', pJS.interactivity.events.onhover.mode)){\n\n      var dx_mouse = p.x - pJS.interactivity.mouse.pos_x,\n          dy_mouse = p.y - pJS.interactivity.mouse.pos_y,\n          dist_mouse = Math.sqrt(dx_mouse*dx_mouse + dy_mouse*dy_mouse),\n          ratio = 1 - dist_mouse / pJS.interactivity.modes.bubble.distance;\n\n      function init(){\n        p.opacity_bubble = p.opacity;\n        p.radius_bubble = p.radius;\n      }\n\n      /* mousemove - check ratio */\n      if(dist_mouse <= pJS.interactivity.modes.bubble.distance){\n\n        if(ratio >= 0 && pJS.interactivity.status == 'mousemove'){\n          \n          /* size */\n          if(pJS.interactivity.modes.bubble.size != pJS.particles.size.value){\n\n            if(pJS.interactivity.modes.bubble.size > pJS.particles.size.value){\n              var size = p.radius + (pJS.interactivity.modes.bubble.size*ratio);\n              if(size >= 0){\n                p.radius_bubble = size;\n              }\n            }else{\n              var dif = p.radius - pJS.interactivity.modes.bubble.size,\n                  size = p.radius - (dif*ratio);\n              if(size > 0){\n                p.radius_bubble = size;\n              }else{\n                p.radius_bubble = 0;\n              }\n            }\n\n          }\n\n          /* opacity */\n          if(pJS.interactivity.modes.bubble.opacity != pJS.particles.opacity.value){\n\n            if(pJS.interactivity.modes.bubble.opacity > pJS.particles.opacity.value){\n              var opacity = pJS.interactivity.modes.bubble.opacity*ratio;\n              if(opacity > p.opacity && opacity <= pJS.interactivity.modes.bubble.opacity){\n                p.opacity_bubble = opacity;\n              }\n            }else{\n              var opacity = p.opacity - (pJS.particles.opacity.value-pJS.interactivity.modes.bubble.opacity)*ratio;\n              if(opacity < p.opacity && opacity >= pJS.interactivity.modes.bubble.opacity){\n                p.opacity_bubble = opacity;\n              }\n            }\n\n          }\n\n        }\n\n      }else{\n        init();\n      }\n\n\n      /* mouseleave */\n      if(pJS.interactivity.status == 'mouseleave'){\n        init();\n      }\n    \n    }\n\n    /* on click event */\n    else if(pJS.interactivity.events.onclick.enable && isInArray('bubble', pJS.interactivity.events.onclick.mode)){\n\n\n      if(pJS.tmp.bubble_clicking){\n        var dx_mouse = p.x - pJS.interactivity.mouse.click_pos_x,\n            dy_mouse = p.y - pJS.interactivity.mouse.click_pos_y,\n            dist_mouse = Math.sqrt(dx_mouse*dx_mouse + dy_mouse*dy_mouse),\n            time_spent = (new Date().getTime() - pJS.interactivity.mouse.click_time)/1000;\n\n        if(time_spent > pJS.interactivity.modes.bubble.duration){\n          pJS.tmp.bubble_duration_end = true;\n        }\n\n        if(time_spent > pJS.interactivity.modes.bubble.duration*2){\n          pJS.tmp.bubble_clicking = false;\n          pJS.tmp.bubble_duration_end = false;\n        }\n      }\n\n\n      function process(bubble_param, particles_param, p_obj_bubble, p_obj, id){\n\n        if(bubble_param != particles_param){\n\n          if(!pJS.tmp.bubble_duration_end){\n            if(dist_mouse <= pJS.interactivity.modes.bubble.distance){\n              if(p_obj_bubble != undefined) var obj = p_obj_bubble;\n              else var obj = p_obj;\n              if(obj != bubble_param){\n                var value = p_obj - (time_spent * (p_obj - bubble_param) / pJS.interactivity.modes.bubble.duration);\n                if(id == 'size') p.radius_bubble = value;\n                if(id == 'opacity') p.opacity_bubble = value;\n              }\n            }else{\n              if(id == 'size') p.radius_bubble = undefined;\n              if(id == 'opacity') p.opacity_bubble = undefined;\n            }\n          }else{\n            if(p_obj_bubble != undefined){\n              var value_tmp = p_obj - (time_spent * (p_obj - bubble_param) / pJS.interactivity.modes.bubble.duration),\n                  dif = bubble_param - value_tmp;\n                  value = bubble_param + dif;\n              if(id == 'size') p.radius_bubble = value;\n              if(id == 'opacity') p.opacity_bubble = value;\n            }\n          }\n\n        }\n\n      }\n\n      if(pJS.tmp.bubble_clicking){\n        /* size */\n        process(pJS.interactivity.modes.bubble.size, pJS.particles.size.value, p.radius_bubble, p.radius, 'size');\n        /* opacity */\n        process(pJS.interactivity.modes.bubble.opacity, pJS.particles.opacity.value, p.opacity_bubble, p.opacity, 'opacity');\n      }\n\n    }\n\n  };\n\n\n  pJS.fn.modes.repulseParticle = function(p){\n\n    if(pJS.interactivity.events.onhover.enable && isInArray('repulse', pJS.interactivity.events.onhover.mode) && pJS.interactivity.status == 'mousemove') {\n\n      var dx_mouse = p.x - pJS.interactivity.mouse.pos_x,\n          dy_mouse = p.y - pJS.interactivity.mouse.pos_y,\n          dist_mouse = Math.sqrt(dx_mouse*dx_mouse + dy_mouse*dy_mouse);\n\n      var normVec = {x: dx_mouse/dist_mouse, y: dy_mouse/dist_mouse},\n          repulseRadius = pJS.interactivity.modes.repulse.distance,\n          velocity = 100,\n          repulseFactor = clamp((1/repulseRadius)*(-1*Math.pow(dist_mouse/repulseRadius,2)+1)*repulseRadius*velocity, 0, 50);\n      \n      var pos = {\n        x: p.x + normVec.x * repulseFactor,\n        y: p.y + normVec.y * repulseFactor\n      }\n\n      if(pJS.particles.move.out_mode == 'bounce'){\n        if(pos.x - p.radius > 0 && pos.x + p.radius < pJS.canvas.w) p.x = pos.x;\n        if(pos.y - p.radius > 0 && pos.y + p.radius < pJS.canvas.h) p.y = pos.y;\n      }else{\n        p.x = pos.x;\n        p.y = pos.y;\n      }\n    \n    }\n\n\n    else if(pJS.interactivity.events.onclick.enable && isInArray('repulse', pJS.interactivity.events.onclick.mode)) {\n\n      if(!pJS.tmp.repulse_finish){\n        pJS.tmp.repulse_count++;\n        if(pJS.tmp.repulse_count == pJS.particles.array.length){\n          pJS.tmp.repulse_finish = true;\n        }\n      }\n\n      if(pJS.tmp.repulse_clicking){\n\n        var repulseRadius = Math.pow(pJS.interactivity.modes.repulse.distance/6, 3);\n\n        var dx = pJS.interactivity.mouse.click_pos_x - p.x,\n            dy = pJS.interactivity.mouse.click_pos_y - p.y,\n            d = dx*dx + dy*dy;\n\n        var force = -repulseRadius / d * 1;\n\n        function process(){\n\n          var f = Math.atan2(dy,dx);\n          p.vx = force * Math.cos(f);\n          p.vy = force * Math.sin(f);\n\n          if(pJS.particles.move.out_mode == 'bounce'){\n            var pos = {\n              x: p.x + p.vx,\n              y: p.y + p.vy\n            }\n            if (pos.x + p.radius > pJS.canvas.w) p.vx = -p.vx;\n            else if (pos.x - p.radius < 0) p.vx = -p.vx;\n            if (pos.y + p.radius > pJS.canvas.h) p.vy = -p.vy;\n            else if (pos.y - p.radius < 0) p.vy = -p.vy;\n          }\n\n        }\n\n        // default\n        if(d <= repulseRadius){\n          process();\n        }\n\n        // bang - slow motion mode\n        // if(!pJS.tmp.repulse_finish){\n        //   if(d <= repulseRadius){\n        //     process();\n        //   }\n        // }else{\n        //   process();\n        // }\n        \n\n      }else{\n\n        if(pJS.tmp.repulse_clicking == false){\n\n          p.vx = p.vx_i;\n          p.vy = p.vy_i;\n        \n        }\n\n      }\n\n    }\n\n  }\n\n\n  pJS.fn.modes.grabParticle = function(p){\n\n    if(pJS.interactivity.events.onhover.enable && pJS.interactivity.status == 'mousemove'){\n\n      var dx_mouse = p.x - pJS.interactivity.mouse.pos_x,\n          dy_mouse = p.y - pJS.interactivity.mouse.pos_y,\n          dist_mouse = Math.sqrt(dx_mouse*dx_mouse + dy_mouse*dy_mouse);\n\n      /* draw a line between the cursor and the particle if the distance between them is under the config distance */\n      if(dist_mouse <= pJS.interactivity.modes.grab.distance){\n\n        var opacity_line = pJS.interactivity.modes.grab.line_linked.opacity - (dist_mouse / (1/pJS.interactivity.modes.grab.line_linked.opacity)) / pJS.interactivity.modes.grab.distance;\n\n        if(opacity_line > 0){\n\n          /* style */\n          var color_line = pJS.particles.line_linked.color_rgb_line;\n          pJS.canvas.ctx.strokeStyle = 'rgba('+color_line.r+','+color_line.g+','+color_line.b+','+opacity_line+')';\n          pJS.canvas.ctx.lineWidth = pJS.particles.line_linked.width;\n          //pJS.canvas.ctx.lineCap = 'round'; /* performance issue */\n          \n          /* path */\n          pJS.canvas.ctx.beginPath();\n          pJS.canvas.ctx.moveTo(p.x, p.y);\n          pJS.canvas.ctx.lineTo(pJS.interactivity.mouse.pos_x, pJS.interactivity.mouse.pos_y);\n          pJS.canvas.ctx.stroke();\n          pJS.canvas.ctx.closePath();\n\n        }\n\n      }\n\n    }\n\n  };\n\n\n\n  /* ---------- pJS functions - vendors ------------ */\n\n  pJS.fn.vendors.eventsListeners = function(){\n\n    /* events target element */\n    if(pJS.interactivity.detect_on == 'window'){\n      pJS.interactivity.el = window;\n    }else{\n      pJS.interactivity.el = pJS.canvas.el;\n    }\n\n\n    /* detect mouse pos - on hover / click event */\n    if(pJS.interactivity.events.onhover.enable || pJS.interactivity.events.onclick.enable){\n\n      /* el on mousemove */\n      pJS.interactivity.el.addEventListener('mousemove', function(e){\n\n        if(pJS.interactivity.el == window){\n          var pos_x = e.clientX,\n              pos_y = e.clientY;\n        }\n        else{\n          var pos_x = e.offsetX || e.clientX,\n              pos_y = e.offsetY || e.clientY;\n        }\n\n        pJS.interactivity.mouse.pos_x = pos_x;\n        pJS.interactivity.mouse.pos_y = pos_y;\n\n        if(pJS.tmp.retina){\n          pJS.interactivity.mouse.pos_x *= pJS.canvas.pxratio;\n          pJS.interactivity.mouse.pos_y *= pJS.canvas.pxratio;\n        }\n\n        pJS.interactivity.status = 'mousemove';\n\n      });\n\n      /* el on onmouseleave */\n      pJS.interactivity.el.addEventListener('mouseleave', function(e){\n\n        pJS.interactivity.mouse.pos_x = null;\n        pJS.interactivity.mouse.pos_y = null;\n        pJS.interactivity.status = 'mouseleave';\n\n      });\n\n    }\n\n    /* on click event */\n    if(pJS.interactivity.events.onclick.enable){\n\n      pJS.interactivity.el.addEventListener('click', function(){\n\n        pJS.interactivity.mouse.click_pos_x = pJS.interactivity.mouse.pos_x;\n        pJS.interactivity.mouse.click_pos_y = pJS.interactivity.mouse.pos_y;\n        pJS.interactivity.mouse.click_time = new Date().getTime();\n\n        if(pJS.interactivity.events.onclick.enable){\n\n          switch(pJS.interactivity.events.onclick.mode){\n\n            case 'push':\n              if(pJS.particles.move.enable){\n                pJS.fn.modes.pushParticles(pJS.interactivity.modes.push.particles_nb, pJS.interactivity.mouse);\n              }else{\n                if(pJS.interactivity.modes.push.particles_nb == 1){\n                  pJS.fn.modes.pushParticles(pJS.interactivity.modes.push.particles_nb, pJS.interactivity.mouse);\n                }\n                else if(pJS.interactivity.modes.push.particles_nb > 1){\n                  pJS.fn.modes.pushParticles(pJS.interactivity.modes.push.particles_nb);\n                }\n              }\n            break;\n\n            case 'remove':\n              pJS.fn.modes.removeParticles(pJS.interactivity.modes.remove.particles_nb);\n            break;\n\n            case 'bubble':\n              pJS.tmp.bubble_clicking = true;\n            break;\n\n            case 'repulse':\n              pJS.tmp.repulse_clicking = true;\n              pJS.tmp.repulse_count = 0;\n              pJS.tmp.repulse_finish = false;\n              setTimeout(function(){\n                pJS.tmp.repulse_clicking = false;\n              }, pJS.interactivity.modes.repulse.duration*1000)\n            break;\n\n          }\n\n        }\n\n      });\n        \n    }\n\n\n  };\n\n  pJS.fn.vendors.densityAutoParticles = function(){\n\n    if(pJS.particles.number.density.enable){\n\n      /* calc area */\n      var area = pJS.canvas.el.width * pJS.canvas.el.height / 1000;\n      if(pJS.tmp.retina){\n        area = area/(pJS.canvas.pxratio*2);\n      }\n\n      /* calc number of particles based on density area */\n      var nb_particles = area * pJS.particles.number.value / pJS.particles.number.density.value_area;\n\n      /* add or remove X particles */\n      var missing_particles = pJS.particles.array.length - nb_particles;\n      if(missing_particles < 0) pJS.fn.modes.pushParticles(Math.abs(missing_particles));\n      else pJS.fn.modes.removeParticles(missing_particles);\n\n    }\n\n  };\n\n\n  pJS.fn.vendors.checkOverlap = function(p1, position){\n    for(var i = 0; i < pJS.particles.array.length; i++){\n      var p2 = pJS.particles.array[i];\n\n      var dx = p1.x - p2.x,\n          dy = p1.y - p2.y,\n          dist = Math.sqrt(dx*dx + dy*dy);\n\n      if(dist <= p1.radius + p2.radius){\n        p1.x = position ? position.x : Math.random() * pJS.canvas.w;\n        p1.y = position ? position.y : Math.random() * pJS.canvas.h;\n        pJS.fn.vendors.checkOverlap(p1);\n      }\n    }\n  };\n\n\n  pJS.fn.vendors.createSvgImg = function(p){\n\n    /* set color to svg element */\n    var svgXml = pJS.tmp.source_svg,\n        rgbHex = /#([0-9A-F]{3,6})/gi,\n        coloredSvgXml = svgXml.replace(rgbHex, function (m, r, g, b) {\n          if(p.color.rgb){\n            var color_value = 'rgba('+p.color.rgb.r+','+p.color.rgb.g+','+p.color.rgb.b+','+p.opacity+')';\n          }else{\n            var color_value = 'hsla('+p.color.hsl.h+','+p.color.hsl.s+'%,'+p.color.hsl.l+'%,'+p.opacity+')';\n          }\n          return color_value;\n        });\n\n    /* prepare to create img with colored svg */\n    var svg = new Blob([coloredSvgXml], {type: 'image/svg+xml;charset=utf-8'}),\n        DOMURL = window.URL || window.webkitURL || window,\n        url = DOMURL.createObjectURL(svg);\n\n    /* create particle img obj */\n    var img = new Image();\n    img.addEventListener('load', function(){\n      p.img.obj = img;\n      p.img.loaded = true;\n      DOMURL.revokeObjectURL(url);\n      pJS.tmp.count_svg++;\n    });\n    img.src = url;\n\n  };\n\n\n  pJS.fn.vendors.destroypJS = function(){\n    cancelAnimationFrame(pJS.fn.drawAnimFrame);\n    canvas_el.remove();\n    pJSDom = null;\n  };\n\n\n  pJS.fn.vendors.drawShape = function(c, startX, startY, sideLength, sideCountNumerator, sideCountDenominator){\n\n    // By Programming Thomas - https://programmingthomas.wordpress.com/2013/04/03/n-sided-shapes/\n    var sideCount = sideCountNumerator * sideCountDenominator;\n    var decimalSides = sideCountNumerator / sideCountDenominator;\n    var interiorAngleDegrees = (180 * (decimalSides - 2)) / decimalSides;\n    var interiorAngle = Math.PI - Math.PI * interiorAngleDegrees / 180; // convert to radians\n    c.save();\n    c.beginPath();\n    c.translate(startX, startY);\n    c.moveTo(0,0);\n    for (var i = 0; i < sideCount; i++) {\n      c.lineTo(sideLength,0);\n      c.translate(sideLength,0);\n      c.rotate(interiorAngle);\n    }\n    //c.stroke();\n    c.fill();\n    c.restore();\n\n  };\n\n  pJS.fn.vendors.exportImg = function(){\n    window.open(pJS.canvas.el.toDataURL('image/png'), '_blank');\n  };\n\n\n  pJS.fn.vendors.loadImg = function(type){\n\n    pJS.tmp.img_error = undefined;\n\n    if(pJS.particles.shape.image.src != ''){\n\n      if(type == 'svg'){\n\n        var xhr = new XMLHttpRequest();\n        xhr.open('GET', pJS.particles.shape.image.src);\n        xhr.onreadystatechange = function (data) {\n          if(xhr.readyState == 4){\n            if(xhr.status == 200){\n              pJS.tmp.source_svg = data.currentTarget.response;\n              pJS.fn.vendors.checkBeforeDraw();\n            }else{\n              console.log('Error pJS - Image not found');\n              pJS.tmp.img_error = true;\n            }\n          }\n        }\n        xhr.send();\n\n      }else{\n\n        var img = new Image();\n        img.addEventListener('load', function(){\n          pJS.tmp.img_obj = img;\n          pJS.fn.vendors.checkBeforeDraw();\n        });\n        img.src = pJS.particles.shape.image.src;\n\n      }\n\n    }else{\n      console.log('Error pJS - No image.src');\n      pJS.tmp.img_error = true;\n    }\n\n  };\n\n\n  pJS.fn.vendors.draw = function(){\n\n    if(pJS.particles.shape.type == 'image'){\n\n      if(pJS.tmp.img_type == 'svg'){\n\n        if(pJS.tmp.count_svg >= pJS.particles.number.value){\n          pJS.fn.particlesDraw();\n          if(!pJS.particles.move.enable) cancelRequestAnimFrame(pJS.fn.drawAnimFrame);\n          else pJS.fn.drawAnimFrame = requestAnimFrame(pJS.fn.vendors.draw);\n        }else{\n          //console.log('still loading...');\n          if(!pJS.tmp.img_error) pJS.fn.drawAnimFrame = requestAnimFrame(pJS.fn.vendors.draw);\n        }\n\n      }else{\n\n        if(pJS.tmp.img_obj != undefined){\n          pJS.fn.particlesDraw();\n          if(!pJS.particles.move.enable) cancelRequestAnimFrame(pJS.fn.drawAnimFrame);\n          else pJS.fn.drawAnimFrame = requestAnimFrame(pJS.fn.vendors.draw);\n        }else{\n          if(!pJS.tmp.img_error) pJS.fn.drawAnimFrame = requestAnimFrame(pJS.fn.vendors.draw);\n        }\n\n      }\n\n    }else{\n      pJS.fn.particlesDraw();\n      if(!pJS.particles.move.enable) cancelRequestAnimFrame(pJS.fn.drawAnimFrame);\n      else pJS.fn.drawAnimFrame = requestAnimFrame(pJS.fn.vendors.draw);\n    }\n\n  };\n\n\n  pJS.fn.vendors.checkBeforeDraw = function(){\n\n    // if shape is image\n    if(pJS.particles.shape.type == 'image'){\n\n      if(pJS.tmp.img_type == 'svg' && pJS.tmp.source_svg == undefined){\n        pJS.tmp.checkAnimFrame = requestAnimFrame(check);\n      }else{\n        //console.log('images loaded! cancel check');\n        cancelRequestAnimFrame(pJS.tmp.checkAnimFrame);\n        if(!pJS.tmp.img_error){\n          pJS.fn.vendors.init();\n          pJS.fn.vendors.draw();\n        }\n        \n      }\n\n    }else{\n      pJS.fn.vendors.init();\n      pJS.fn.vendors.draw();\n    }\n\n  };\n\n\n  pJS.fn.vendors.init = function(){\n\n    /* init canvas + particles */\n    pJS.fn.retinaInit();\n    pJS.fn.canvasInit();\n    pJS.fn.canvasSize();\n    pJS.fn.canvasPaint();\n    pJS.fn.particlesCreate();\n    pJS.fn.vendors.densityAutoParticles();\n\n    /* particles.line_linked - convert hex colors to rgb */\n    pJS.particles.line_linked.color_rgb_line = hexToRgb(pJS.particles.line_linked.color);\n\n  };\n\n\n  pJS.fn.vendors.start = function(){\n\n    if(isInArray('image', pJS.particles.shape.type)){\n      pJS.tmp.img_type = pJS.particles.shape.image.src.substr(pJS.particles.shape.image.src.length - 3);\n      pJS.fn.vendors.loadImg(pJS.tmp.img_type);\n    }else{\n      pJS.fn.vendors.checkBeforeDraw();\n    }\n\n  };\n\n\n\n\n  /* ---------- pJS - start ------------ */\n\n\n  pJS.fn.vendors.eventsListeners();\n\n  pJS.fn.vendors.start();\n  \n\n\n};\n\n/* ---------- global functions - vendors ------------ */\n\nObject.deepExtend = function(destination, source) {\n  for (var property in source) {\n    if (source[property] && source[property].constructor &&\n     source[property].constructor === Object) {\n      destination[property] = destination[property] || {};\n      arguments.callee(destination[property], source[property]);\n    } else {\n      destination[property] = source[property];\n    }\n  }\n  return destination;\n};\n\nwindow.requestAnimFrame = (function(){\n  return  window.requestAnimationFrame ||\n    window.webkitRequestAnimationFrame ||\n    window.mozRequestAnimationFrame    ||\n    window.oRequestAnimationFrame      ||\n    window.msRequestAnimationFrame     ||\n    function(callback){\n      window.setTimeout(callback, 1000 / 60);\n    };\n})();\n\nwindow.cancelRequestAnimFrame = ( function() {\n  return window.cancelAnimationFrame         ||\n    window.webkitCancelRequestAnimationFrame ||\n    window.mozCancelRequestAnimationFrame    ||\n    window.oCancelRequestAnimationFrame      ||\n    window.msCancelRequestAnimationFrame     ||\n    clearTimeout\n} )();\n\nfunction hexToRgb(hex){\n  // By Tim Down - http://stackoverflow.com/a/5624139/3493650\n  // Expand shorthand form (e.g. \"03F\") to full form (e.g. \"0033FF\")\n  var shorthandRegex = /^#?([a-f\\d])([a-f\\d])([a-f\\d])$/i;\n  hex = hex.replace(shorthandRegex, function(m, r, g, b) {\n     return r + r + g + g + b + b;\n  });\n  var result = /^#?([a-f\\d]{2})([a-f\\d]{2})([a-f\\d]{2})$/i.exec(hex);\n  return result ? {\n      r: parseInt(result[1], 16),\n      g: parseInt(result[2], 16),\n      b: parseInt(result[3], 16)\n  } : null;\n};\n\nfunction clamp(number, min, max) {\n  return Math.min(Math.max(number, min), max);\n};\n\nfunction isInArray(value, array) {\n  return array.indexOf(value) > -1;\n}\n\n\n/* ---------- particles.js functions - start ------------ */\n\nwindow.pJSDom = [];\n\nwindow.particlesJS = function(tag_id, params){\n\n  //console.log(params);\n\n  /* no string id? so it's object params, and set the id with default id */\n  if(typeof(tag_id) != 'string'){\n    params = tag_id;\n    tag_id = 'particles-js';\n  }\n\n  /* no id? set the id to default id */\n  if(!tag_id){\n    tag_id = 'particles-js';\n  }\n\n  /* pJS elements */\n  var pJS_tag = document.getElementById(tag_id),\n      pJS_canvas_class = 'particles-js-canvas-el',\n      exist_canvas = pJS_tag.getElementsByClassName(pJS_canvas_class);\n\n  /* remove canvas if exists into the pJS target tag */\n  if(exist_canvas.length){\n    while(exist_canvas.length > 0){\n      pJS_tag.removeChild(exist_canvas[0]);\n    }\n  }\n\n  /* create canvas element */\n  var canvas_el = document.createElement('canvas');\n  canvas_el.className = pJS_canvas_class;\n\n  /* set size canvas */\n  canvas_el.style.width = \"100%\";\n  canvas_el.style.height = \"100%\";\n\n  /* append canvas */\n  var canvas = document.getElementById(tag_id).appendChild(canvas_el);\n\n  /* launch particle.js */\n  if(canvas != null){\n    pJSDom.push(new pJS(tag_id, params));\n  }\n\n};\n\nwindow.particlesJS.load = function(tag_id, path_config_json, callback){\n\n  /* load json config */\n  var xhr = new XMLHttpRequest();\n  xhr.open('GET', path_config_json);\n  xhr.onreadystatechange = function (data) {\n    if(xhr.readyState == 4){\n      if(xhr.status == 200){\n        var params = JSON.parse(data.currentTarget.response);\n        window.particlesJS(tag_id, params);\n        if(callback) callback();\n      }else{\n        console.log('Error pJS - XMLHttpRequest status: '+xhr.status);\n        console.log('Error pJS - File config not found');\n      }\n    }\n  };\n  xhr.send();\n\n};"], "names": ["pJS", "tag_id", "params", "canvas_el", "document", "querySelector", "this", "canvas", "el", "w", "offsetWidth", "h", "offsetHeight", "particles", "number", "value", "density", "enable", "value_area", "color", "shape", "type", "stroke", "width", "polygon", "nb_sides", "image", "src", "height", "opacity", "random", "anim", "speed", "opacity_min", "sync", "size", "size_min", "line_linked", "distance", "move", "direction", "straight", "out_mode", "bounce", "attract", "rotateX", "rotateY", "array", "interactivity", "detect_on", "events", "onhover", "mode", "onclick", "resize", "modes", "grab", "bubble", "duration", "repulse", "push", "particles_nb", "remove", "mouse", "retina_detect", "fn", "interact", "vendors", "tmp", "Object", "deepExtend", "obj", "size_value", "size_anim_speed", "move_speed", "line_linked_distance", "line_linked_width", "mode_grab_distance", "mode_bubble_distance", "mode_bubble_size", "mode_repulse_distance", "retinaInit", "window", "devicePixelRatio", "pxratio", "retina", "canvasInit", "ctx", "getContext", "canvasSize", "addEventListener", "particlesEmpty", "particlesCreate", "particlesDraw", "densityAutoParticles", "canvas<PERSON><PERSON><PERSON>", "fillRect", "canvasClear", "clearRect", "particle", "position", "radius", "Math", "size_status", "vs", "x", "y", "checkOverlap", "Array", "color_selected", "floor", "length", "rgb", "hexToRgb", "undefined", "r", "g", "b", "s", "l", "hsl", "opacity_status", "vo", "velbase", "vx", "vy", "vx_i", "vy_i", "shape_type", "shape_selected", "sh", "img", "ratio", "img_type", "source_svg", "createSvgImg", "pushing", "loaded", "prototype", "draw", "p", "radius_bubble", "opacity_bubble", "color_value", "fillStyle", "beginPath", "arc", "PI", "rect", "drawShape", "img_obj", "drawImage", "closePath", "strokeStyle", "lineWidth", "fill", "i", "particlesUpdate", "ms", "new_pos", "x_left", "x_right", "y_top", "y_bottom", "isInArray", "grabParticle", "bubbleParticle", "repulseParticle", "j", "p2", "linkParticles", "attractParticles", "bounceParticles", "particlesRefresh", "cancelRequestAnimFrame", "checkAnimFrame", "drawAnimFrame", "count_svg", "start", "p1", "dx", "dy", "dist", "sqrt", "opacity_line", "color_line", "color_rgb_line", "moveTo", "lineTo", "ax", "ay", "pushParticles", "nb", "pos", "pos_x", "pos_y", "removeParticles", "splice", "dx_mouse", "dy_mouse", "dist_mouse", "init", "status", "dif", "bubble_clicking", "click_pos_x", "click_pos_y", "time_spent", "Date", "getTime", "click_time", "bubble_duration_end", "process", "bubble_param", "particles_param", "p_obj_bubble", "p_obj", "id", "normVec", "repulseRadius", "repulseFactor", "pow", "min", "max", "repulse_finish", "repulse_count", "repulse_clicking", "d", "force", "f", "atan2", "cos", "sin", "eventsListeners", "e", "clientX", "clientY", "offsetX", "offsetY", "setTimeout", "area", "nb_particles", "missing_particles", "abs", "coloredSvgXml", "replace", "m", "svg", "Blob", "DOMURL", "URL", "webkitURL", "url", "createObjectURL", "Image", "revokeObjectURL", "destroypJS", "cancelAnimationFrame", "pJSDom", "c", "startX", "startY", "sideLength", "sideCountNumerator", "sideCountDenominator", "sideCount", "decimalSides", "interiorAngleDegrees", "interiorAngle", "save", "translate", "rotate", "restore", "exportImg", "open", "toDataURL", "loadImg", "img_error", "xhr", "XMLHttpRequest", "onreadystatechange", "data", "readyState", "currentTarget", "response", "checkBeforeDraw", "console", "log", "send", "requestAnimFrame", "check", "substr", "hex", "result", "exec", "parseInt", "indexOf", "destination", "source", "property", "constructor", "arguments", "callee", "requestAnimationFrame", "webkitRequestAnimationFrame", "mozRequestAnimationFrame", "oRequestAnimationFrame", "msRequestAnimationFrame", "callback", "webkitCancelRequestAnimationFrame", "mozCancelRequestAnimationFrame", "oCancelRequestAnimationFrame", "msCancelRequestAnimationFrame", "clearTimeout", "particlesJS", "pJS_tag", "getElementById", "pJS_canvas_class", "exist_canvas", "getElementsByClassName", "<PERSON><PERSON><PERSON><PERSON>", "createElement", "className", "style", "append<PERSON><PERSON><PERSON>", "load", "path_config_json", "JSON", "parse"], "sourceRoot": ""}