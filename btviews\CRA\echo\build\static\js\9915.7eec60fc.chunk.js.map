{"version": 3, "file": "static/js/9915.7eec60fc.chunk.js", "mappings": "gOACO,SAASA,EAAYC,GACxB,OAAIA,EAC0B,QAA3BA,EAASC,cAAgC,IACd,QAA3BD,EAASC,cAAgC,IACd,QAA3BD,EAASC,cAAgC,IACd,QAA3BD,EAASC,eACkB,QAA3BD,EAASC,cADgC,KAErC,GANc,EAOzB,CAEO,SAASC,EAAWF,GACvB,OAAIA,EAC0B,QAA3BA,EAASC,eACkB,QAA3BD,EAASC,cADgC,KAEd,QAA3BD,EAASC,cAAgC,KACd,QAA3BD,EAASC,cAAgC,KACd,QAA3BD,EAASC,cAAgC,KACd,QAA3BD,EAASC,cAAgC,KACd,QAA3BD,EAASC,cAAgC,KACrC,KARc,EASzB,CAEO,SAASE,EAAWC,GACvB,IAAIA,EAAS,MAAO,GACpB,MACMC,EAAO,IAAIC,KAAKF,EAAQG,QAAQ,KAAK,MAE3C,MAHc,CAAC,UAAU,WAAW,QAAQ,QAAQ,MAAM,OAAO,OAAO,SAAS,YAAY,UAAU,WAAW,YAGrGF,EAAKG,YAAc,IAAMH,EAAKI,UAAY,IAAMJ,EAAKK,aACtE,CAEO,SAASC,EAASC,GACrB,OAAGA,EAAKC,YAAoBD,EAAKC,YAC9BD,EAAKE,MAAcF,EAAKE,MACpB,GACX,CAEO,SAASC,EAAiBC,GAC7B,OAAOA,EAAEC,WAAWV,QAAQ,wBAAyB,IACzD,CAEO,SAASW,EAAaC,GAC3B,MAAMC,EAAcC,WAAWF,GAC/B,OACMJ,EADEK,EAAc,GAAM,EACLA,EAAYE,QAAQ,GACpBF,EAAYE,QAAQ,GAC7C,CAEO,SAASC,EAAavB,EAAUc,GACnC,OAAId,GAAac,EAEdU,MAAMV,GAAe,GAErBO,WAAWP,IAAU,EACU,QAA3Bd,EAASC,cACDiB,EAAaJ,GAAOW,eAAe,SAAW,OACpB,QAA3BzB,EAASC,cACRiB,EAAaJ,GAAOW,eAAe,SAAW,MACpB,QAA3BzB,EAASC,cACRiB,EAAaJ,GAAOW,eAAe,SAAW,MACpB,QAA3BzB,EAASC,cACRiB,EAAaJ,GAAOW,eAAe,SAAW,MACpB,QAA3BzB,EAASC,cACRiB,EAAaJ,GAAOW,eAAe,SAAW,KACrB,QAA3BzB,EAASC,cACPiB,EAAaJ,GAAOW,eAAe,SAAW,MACrB,QAA3BzB,EAASC,cACP,KAAOiB,EAAaJ,GAAOW,eAAe,SAChB,QAA3BzB,EAASC,cACRiB,EAAaJ,GAAOW,eAAe,SAAW,KACpB,QAA3BzB,EAASC,cACRiB,EAAaJ,GAAOW,eAAe,SAAW,MACpB,QAA3BzB,EAASC,cACRiB,EAAaJ,GAAOW,eAAe,SAAW,KACpB,QAA3BzB,EAASC,cACRiB,EAAaJ,GAAOW,eAAe,SAAW,IAElD1B,EAAYC,GAAYkB,EAAaJ,GAAOW,eAAe,SAG/D,IAAM1B,EAAYC,KAAoC,EAAvBkB,EAAaJ,IAAaW,eAAe,SA/BhD,EAgCnC,CAEO,SAASC,EAAQC,EAAKC,GAEzBD,EAAM,IAAIrB,KAAKqB,GAEf,IAAIE,IADJD,EAAM,IAAItB,KAAKsB,IACAE,UAAYH,EAAIG,WAAa,IAE5C,OADAD,GAAQ,GACDE,KAAKC,IAAID,KAAKE,MAAMJ,GAC/B,CAEO,SAASK,EAAe9B,GAC3B,IAAIA,EAAS,MAAO,GACpB,MACMC,EAAO,IAAIC,KAAKF,EAAQG,QAAQ,KAAK,MAE3C,MAHc,CAAC,UAAU,WAAW,QAAQ,QAAQ,MAAM,OAAO,OAAO,SAAS,YAAY,UAAU,WAAW,YAGrGF,EAAKG,YAAc,IAAMH,EAAKI,UAAY,IAAMJ,EAAKK,cAAgB,IAAML,EAAK8B,WAAa,IAAM9B,EAAK+B,YACzH,CAEO,SAASC,EAAUC,GAAU,IAAT,KAACC,GAAKD,EAC3BE,EAAa,GACbC,EAAW,GAgBf,MAf8B,WAA1BF,EAAKG,mBACPF,EAAajB,EAAagB,EAAKvC,SAAUqB,WAAWkB,EAAKzB,MAAQ,KAAKQ,QAAQ,IAC9EmB,GAAWE,EAAAA,EAAAA,MAAA,OAAKC,MAAM,eAAcC,SAAA,CAAC,UAAQtB,EAAagB,EAAKvC,SAAUuC,EAAKzB,OAAO,YAGzD,YAA1ByB,EAAKG,mBACPF,EAAajB,EAAagB,EAAKvC,SAAUqB,WAAWkB,EAAKzB,MAAQ,IAAIQ,QAAQ,IAC7EmB,GAAWE,EAAAA,EAAAA,MAAA,OAAKC,MAAM,eAAcC,SAAA,CAAC,UAAQtB,EAAagB,EAAKvC,SAAUuC,EAAKzB,OAAO,aAGnFyB,EAAK1B,cACP2B,EAAajB,EAAagB,EAAKvC,SAAUqB,WAAWkB,EAAK1B,YAAc0B,EAAKO,YAAYxB,QAAQ,IAChGmB,GAAWE,EAAAA,EAAAA,MAAA,OAAKC,MAAM,eAAcC,SAAA,CAAC,UAAQtB,EAAagB,EAAKvC,SAAUuC,EAAK1B,kBAI9E8B,EAAAA,EAAAA,MAAAI,EAAAA,SAAA,CAAAF,SAAA,EACEF,EAAAA,EAAAA,MAAA,KAAGK,UAAW,qCAA+D,MAA1BC,EAAAA,EAAAA,IAAU,YAAsB,OAAS,IAAKJ,SAAA,CAC9FL,EAAW,KAACU,EAAAA,EAAAA,KAAA,QAAMF,UAAU,UAASH,SAAC,gBAExCJ,IAGP,CAEO,SAASU,EAAcC,GAAU,IAAT,KAACb,GAAKa,EACnC,MAA2B,QAAvBH,EAAAA,EAAAA,IAAU,SACLZ,EAAW,CAACE,SAEjBA,EAAK1B,aACCqC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wCAAuCH,SAAEtB,EAAagB,EAAKvC,SAAUuC,EAAK1B,gBAG/F8B,EAAAA,EAAAA,MAAA,KAAGK,UAAU,wCAAuCH,SAAA,CACjDtB,EAAagB,EAAKvC,SAAUuC,EAAKzB,QAClC6B,EAAAA,EAAAA,MAAA,QAAMK,UAAU,UAASH,SAAA,CAAC,IACK,YAA1BN,EAAKG,iBAAiC,QAAU,YAI3D,CAqBO,SAASW,IACd,MAAMC,EAAkBC,OAAOC,SAASC,KAExC,OAAIH,EAAgBI,QAAQ,YAAc,EAC/B,WACAJ,EAAgBI,QAAQ,QAAU,EAClC,OAGJ,EACT,C,0ECpJA,QAxBA,WAGE,MAAMC,EAAWC,yBAWjB,OAVAC,EAAAA,EAAAA,WAAU,KACR,MAAMC,EAASC,SAASC,cAAc,UACtCF,EAAOG,IAAMN,EAAW,iDACxBG,EAAOI,OAAQ,EACfJ,EAAOK,OAAS,OAIhBJ,SAASK,KAAKC,YAAYP,IACzB,CAACH,KAEFT,EAAAA,EAAAA,KAAAH,EAAAA,SAAA,CAAAF,UACEK,EAAAA,EAAAA,KAAA,UAAQF,UAAW,iHAMzB,C,6QCjBO,SAASsB,EAAkBhC,GAAyG,IAAvG,uBAACiC,EAAsB,0BAAEC,EAAyB,mBAAEC,EAAkB,aAAEC,EAAY,cAAEC,GAAcrC,EACtI,MAAOsC,EAAcC,IAAmBC,EAAAA,EAAAA,UAAS,IAC1CC,EAAeC,IAAoBF,EAAAA,EAAAA,WAAS,IAEnDjB,EAAAA,EAAAA,WAAU,KACRoB,IAAAA,QAAiB,CACfC,cAAe,qBAEhB,KAEHrB,EAAAA,EAAAA,WAAU,KAENmB,EADEJ,GAAc,IAKjB,CAACA,IAGJ,MAQMO,EAA0BA,KAM9BX,GAA0B,IA8C5B,YAP6BY,IAAzBb,IAAiE,IAA3BA,GArDXc,MAC7B,IAAIC,EAAQvB,SAASwB,eAAe,sBACxB,OAARD,IACFA,EAAME,MAAMC,QAAU,QACtBjB,GAA0B,KAkD5Ba,QAE2BD,IAAzBb,IAAiE,IAA3BA,GACxCY,KAIAjC,EAAAA,EAAAA,KAAAH,EAAAA,SAAA,CAAAF,UAEJK,EAAAA,EAAAA,KAACwC,EAAAA,EAAU,CAACC,QAAM,EAACC,KAAMrB,EAAwBsB,GAAIC,EAAAA,SAASjD,UACtDF,EAAAA,EAAAA,MAACoD,EAAAA,EAAM,CAACF,GAAG,MAAM7C,UAAU,gBAAgBgD,QAASA,IAAKb,IAA0BtC,SAAA,EACjFK,EAAAA,EAAAA,KAACwC,EAAAA,EAAWO,MAAK,CACfJ,GAAIC,EAAAA,SACJI,MAAM,wBACNC,UAAU,YACVC,QAAQ,cACRC,MAAM,uBACNC,UAAU,cACVC,QAAQ,YAAW1D,UAEnBK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,4CAGjBE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,gCAA+BH,UAC5CK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,8DAA6DH,UAC1EK,EAAAA,EAAAA,KAACwC,EAAAA,EAAWO,MAAK,CACfJ,GAAIC,EAAAA,SACJI,MAAM,wBACNC,UAAU,qBACVC,QAAQ,wBACRC,MAAM,uBACNC,UAAU,wBACVC,QAAQ,qBAAoB1D,UAE5BF,EAAAA,EAAAA,MAACoD,EAAAA,EAAOS,MAAK,CAACxD,UAAU,qHAAoHH,SAAA,EAC1IK,EAAAA,EAAAA,KAAC6C,EAAAA,EAAOU,MAAK,CACXZ,GAAG,KACH7C,UAAU,8CAA6CH,SACxD,wBAGDF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,OAAMH,SAAA,EACnBK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wBAAuBH,SAAC,gHAGvCF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,cAAaH,SAAA,EAC1BK,EAAAA,EAAAA,KAAA,SAAOF,UAAU,8DAA8D0D,KAAK,SAASC,MAAM,IAAIC,QAASA,KA9E9HhC,EAAa,GACfC,EAAgBD,EAAa,OA8Eb1B,EAAAA,EAAAA,KAAA,QAAMF,UAAU,mCAAkCH,SAAE+B,KACpD1B,EAAAA,EAAAA,KAAA,SAAOF,UAAU,8DAA8D0D,KAAK,SAASC,MAAM,IAAIC,QAASA,KApFlI/B,EAAgBD,EAAa,IAoFuHiC,SAAU9B,WAIhJpC,EAAAA,EAAAA,MAAA,OAAKK,UAAU,kBAAiBH,SAAA,EAC9BK,EAAAA,EAAAA,KAAA,UACEwD,KAAK,SACL1D,UAAU,8IACV4D,QAlFAE,KAClB3B,IACApB,SAASgD,cAAc,qBAAqBC,UAAUC,IAAI,UAE1DC,EAAAA,EAAMC,KAAK,+CAAuD,CAChE,IAAMlE,EAAAA,EAAAA,IAAU,UAChB,gBAAmBwB,EACnB,SAAYC,EACZ,cAAkBE,EAClB,YAAgBD,GACf,CAAEyC,QAAS,CAAE,eAAgB,uCAAyCC,KAAK,SAASC,GACrF,IAAIC,EAASD,EAAI1G,KACd2G,EAAOC,SACRzD,SAASgD,cAAc,qBAAqBC,UAAUS,OAAO,UAC7DxC,IAAAA,QAAe,iDACfyC,WAAW,WACTnE,OAAOC,SAASmE,QAClB,EAAG,OAEH5D,SAASgD,cAAc,qBAAqBC,UAAUS,OAAO,UAC7DxC,IAAAA,MAAasC,EAAO3G,KAAKgH,KAE7B,IA4DuC/E,SACtB,aAGDK,EAAAA,EAAAA,KAAA,UACEwD,KAAK,SACL1D,UAAU,yOACV4D,QAASA,IAAKzB,IAA0BtC,SACzC,4BAYrB,C,eCvIIgF,EAAa,GACbC,EAAkB,EAgVtB,MAAMC,EAAUzF,IAAgD,IAA/C,QAAE0F,EAAO,UAAEC,EAAS,QAAErB,EAAO,SAAE/D,GAAUP,EACxD,MAAM4F,EAAWD,IAAcD,EAE/B,OACE9E,EAAAA,EAAAA,KAAA,MACEF,UAAW,gDACTkF,EAAW,mCAAqC,IAElDtB,QAASA,IAAMA,EAAQoB,GAASnF,SAE/BA,KAKDsF,EAAqBC,IACzB,MAAOC,EAAUC,IAAexD,EAAAA,EAAAA,UAAS,KAClCyD,IAASzD,EAAAA,EAAAA,UAASsD,EAAMI,KAAKD,QAC7BE,EAAaC,IAAkB5D,EAAAA,EAAAA,UAAS,KACxC6D,EAAaC,IAAkB9D,EAAAA,EAAAA,UAAS,KACxC+D,EAAcC,IAAmBhE,EAAAA,EAAAA,UAAS,KAC1CiE,IAAWjE,EAAAA,EAAAA,UAASsD,EAAMI,KAAKQ,WAC/BC,EAAeC,IAAoBpE,EAAAA,EAAAA,WAAS,IAQnDjB,EAAAA,EAAAA,WAAU,UACWuB,IAAfgD,EAAMI,OACRW,QAAQC,IAAI,uBAAuBhB,EAAMI,KAAKa,WAClB,OAAxBjB,EAAMI,KAAKa,WAA4C,KAAvBjB,EAAMI,KAAKa,UAC7CH,GAAiB,GAEjBA,GAAiB,KAGpB,CAACd,EAAMI,OA0DV,OACE7F,EAAAA,EAAAA,MAAA,OAAKK,UAAU,OAAMH,SAAA,EACnBK,EAAAA,EAAAA,KAAA,KAAGF,UAAU,eAAcH,SAAC,8CAC5BF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,oBAAmBH,SAAA,EAChCF,EAAAA,EAAAA,MAAA,SAAOK,UAAU,gCAA+BH,SAAA,CAAC,oBAAkBkG,MACnE7F,EAAAA,EAAAA,KAAA,UAAQN,MAAM,mEAAmEgE,QA9EpE0C,KAEjBC,UAAUC,UAAUC,UAAUV,GAC9B9D,IAAAA,QAAe,wBA2E0FpC,UAACK,EAAAA,EAAAA,KAACwG,EAAAA,IAAS,UAElH/G,EAAAA,EAAAA,MAAA,OAAKK,UAAU,sBAAqBH,SAAA,EAClCF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,mDAAkDH,SAAA,EAC/DK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uDAAsDH,SAAC,0BAGtEF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,gBAAeH,SAAA,EAC5BK,EAAAA,EAAAA,KAAA,SAAOF,UAAU,2OACjB2G,YAAY,gBACZjD,KAAK,QACLkD,KAAK,QACLC,UAAQ,EACRlD,MAAOyB,EAAMI,KAAKD,SAElBrF,EAAAA,EAAAA,KAAA,SAAOF,UAAU,iNACjB2G,YAAY,YACZjD,KAAK,QACLkD,KAAK,YACLE,aAAa,YACbC,QAAUC,IACR1B,EAAY0B,EAAMC,OAAOtD,QAE3BuD,SAAWC,GAAM7B,EAAY6B,EAAEF,OAAOtD,OACtCE,SAAUoC,WAIdtG,EAAAA,EAAAA,MAAA,OAAKK,UAAU,wDAAuDH,SAAA,EACpEK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uDAAsDH,SAAC,qBAGtEF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,gBAAeH,SAAA,EAC5BK,EAAAA,EAAAA,KAAA,SAAOF,UAAU,0NACjB2G,YAAY,eACZjD,KAAK,WACLkD,KAAK,eACLE,aAAa,eACbC,QAAUC,IACRpB,EAAeoB,EAAMC,OAAOtD,QAE9BE,SAAUoC,KAEV/F,EAAAA,EAAAA,KAAA,SAAOF,UAAU,0NACjB2G,YAAY,uBACZjD,KAAK,WACLkD,KAAK,gBACLG,QAAUC,IACRlB,EAAgBkB,EAAMC,OAAOtD,QAE/BE,SAAUoC,cAKhB/F,EAAAA,EAAAA,KAACkH,EAAa,CAACC,kBA5FOA,KACxB,IAAIC,EAzBmBC,MACvB,IAAID,EAAY,GAUhB,OATI7B,EAEOE,EAAY6B,SAClB7B,IAAgBE,EACjByB,EAAY,6BACH3B,EAAY6B,OAAS,IAC9BF,EAAY,kDALdA,EAAY,gCAQPA,GAcSC,GACbD,EACDrF,IAAAA,MAAaqF,IAGfA,EAhBoBG,MACpB,IAAIH,EAAY,GAMhB,OALIjC,EAASmC,SACN,eAAeE,KAAKrC,KACvBiC,EAAY,yBAGTA,GASKG,GACTH,EACDrF,IAAAA,MAAaqF,IAGfvG,SAASgD,cAAc,qBAAqBC,UAAUC,IAAI,UAC1DC,EAAAA,EAAMC,KAAK,2CAAmD,CAC5DwD,IAAI1H,EAAAA,EAAAA,IAAU,UACd2H,SAAUvC,EACVwC,SAAUpC,EACVqC,YAAanC,EACboC,aAAclC,GACb,CAAEzB,QAAS,CAAE,eAAgB,uCAAyCC,KAAK,SAASC,GACrF,IAAIC,EAASD,EAAI1G,KACjB,GAAG2G,EAAOC,QAKR,OAJAvC,IAAAA,QAAe,mDACfyC,WAAW,WACTnE,OAAOC,SAASmE,QAClB,EAAG,KAGL5D,SAASgD,cAAc,qBAAqBC,UAAUS,OAAO,UAC1DF,EAAO3G,MAAMqE,IAAAA,MAAasC,EAAO3G,KAAKgH,IAC3C,MA+DuDc,eAAgBA,KACrExF,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wCAAuCH,UACpDK,EAAAA,EAAAA,KAAC8H,EAAa,CAACzC,MAAOA,UAMxB6B,EAAiBhC,IACrB,MAAMM,EAAiBN,EAAMM,gBACtBuC,EAAaC,IAAkBpG,EAAAA,EAAAA,WAAS,GAKzCqG,EAAaA,KACjBD,GAAe,IAGXE,EAAuBA,KAC3BhD,EAAMiC,qBAER,OACE1H,EAAAA,EAAAA,MAAAI,EAAAA,SAAA,CAAAF,SAAA,EACEK,EAAAA,EAAAA,KAACmI,EAAAA,EAAOC,OAAM,CACZtI,UAAU,qGACVuI,SAAU,CAAEC,MAAO,IACnB5E,QAfyB6E,KAC7BP,GAAe,IAcqBrI,SACjC,kBAGDK,EAAAA,EAAAA,KAACwC,EAAAA,EAAU,CAACC,QAAM,EAACC,KAAMqF,EAAapF,GAAIC,EAAAA,SAASjD,UACjDF,EAAAA,EAAAA,MAACoD,EAAAA,EAAM,CAACF,GAAG,MAAM7C,UAAU,gBAAgBgD,QAASmF,EAAWtI,SAAA,EAC7DK,EAAAA,EAAAA,KAACwC,EAAAA,EAAWO,MAAK,CACfJ,GAAIC,EAAAA,SACJI,MAAM,wBACNC,UAAU,YACVC,QAAQ,cACRC,MAAM,uBACNC,UAAU,cACVC,QAAQ,YAAW1D,UAEnBK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,4CAGjBE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,gCAA+BH,UAC5CK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,8DAA6DH,UAC1EK,EAAAA,EAAAA,KAACwC,EAAAA,EAAWO,MAAK,CACfJ,GAAIC,EAAAA,SACJI,MAAM,wBACNC,UAAU,qBACVC,QAAQ,wBACRC,MAAM,uBACNC,UAAU,wBACVC,QAAQ,qBAAoB1D,UAE5BF,EAAAA,EAAAA,MAACoD,EAAAA,EAAOS,MAAK,CAACxD,UAAU,qHAAoHH,SAAA,EAC1IK,EAAAA,EAAAA,KAAC6C,EAAAA,EAAOU,MAAK,CACXZ,GAAG,KACH7C,UAAU,8CAA6CH,SACxD,oBAGDF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,OAAMH,SAAA,EACnBK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wBAAuBH,SAAC,4CAGvCK,EAAAA,EAAAA,KAAA,OAAAL,UACEK,EAAAA,EAAAA,KAAA,SAAOF,UAAU,0NACjB2G,YAAY,qBACZjD,KAAK,WACLkD,KAAK,mBACLG,QAAUC,IACRtB,EAAesB,EAAMC,OAAOtD,OACP,KAAlBqD,EAAM0B,SAAgBN,aAM/BzI,EAAAA,EAAAA,MAAA,OAAKK,UAAU,OAAMH,SAAA,EACnBK,EAAAA,EAAAA,KAAA,UACEwD,KAAK,SACL1D,UAAU,+IACV4D,QAASwE,EAAqBvI,SAC/B,aAGDK,EAAAA,EAAAA,KAAA,UACEwD,KAAK,SACL1D,UAAU,yOACV4D,QAASuE,EAAWtI,SACrB,+BAcrBqB,eAAeyH,IACb,MAGMpE,SAHiBL,EAAAA,EAAMC,KAAK,mDAA2D,CAC3F,IAAMlE,EAAAA,EAAAA,IAAU,WACf,CAAEmE,QAAS,CAAE,eAAgB,wCACRxG,KACxB,OAAG2G,EAAOC,QACDD,EAAO3G,KAEP,EAEX,CAEA,MAAMgL,EAAyBxI,IAAwJ,IAAvJ,qBAACyI,EAAoB,0BAAErH,EAAyB,sBAAEsH,EAAqB,gBAAEC,EAAe,iBAAEC,EAAgB,SAAEC,EAAQ,kBAAEC,EAAiB,QAAEC,GAAQ/I,EAC/K,MAAMuH,GAAK1H,EAAAA,EAAAA,IAAU,UAErB,QAAyBmC,IAAtB8G,EAAiC,OACpC,MAAME,EAAcpC,IAClB,IAAIqC,EAAOrC,EAAMC,OAAOqC,aAAa,OACjCD,IACJtI,SAASgD,cAAc,qBAAqBC,UAAUC,IAAI,UAC1D1D,OAAOC,SAASC,KAAO,oBAAsB4I,IAYzCE,EAAeA,KACnBhJ,OAAOC,SAASC,KAAO,cAmBnB+I,EAAeC,GACZA,EAAIC,SAAWD,EAAIC,SAASzM,cAAgB,GAG/CF,EAAe0M,GACZA,EAAIzM,SAAWyM,EAAIzM,SAASC,cAAgB,GAG/C0M,EAAeF,GACZA,EAAIG,UAAYH,EAAIG,UAAU3M,cAAgB,GAGjD4M,EAAsBJ,GACnBA,EAAI/J,iBAAmB+J,EAAI/J,iBAAiBzC,cAAgB,GAGrE,OACEiD,EAAAA,EAAAA,KAAAH,EAAAA,SAAA,CAAAF,UACAK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uEAAsEH,SAClFqJ,GAAqBA,EAAkB1B,QACxC7H,EAAAA,EAAAA,MAAA,SAAOK,UAAU,yEAAwEH,SAAA,EACvFK,EAAAA,EAAAA,KAAA,SAAAL,UACEF,EAAAA,EAAAA,MAAA,MAAIK,UAAU,cAAaH,SAAA,EACzBK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,qHAAoHH,SAAC,kBACnIK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,qHAAoHH,SAAC,YACnIK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,qHAAoHH,SAAC,kBACnIK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,qHAAoHH,SAAC,eAChIqJ,GAAqBA,EAAkB1B,OAAS,GAAK0B,EAAkBY,KAAKL,GAAsB,aAAfA,EAAIM,SAA2Bb,EAAkBY,KAAKL,GAAsB,WAAfA,EAAIM,WACrJ7J,EAAAA,EAAAA,KAAA,MAAIF,UAAU,qHAAoHH,SAAC,gBAErIK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,qHAAoHH,SAAC,YAEnIK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,qHAAoHH,SAAC,kBAGvIK,EAAAA,EAAAA,KAAA,SAAOF,UAAU,oCAAmCH,SACjDqJ,aAAiB,EAAjBA,EAAmBc,IAAI,CAACP,EAAKQ,KAC5BtK,SAAAA,EAAAA,MAAA,MAAgBK,UAAU,cAAaH,SAAA,EACvCK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,yGAAwGH,UA/C1G+J,EA+CyHH,EAAIG,UA9C7IA,GAA8C,eAA5BA,EAAU3M,cA8C8HwM,EAAIG,UAAUrM,QAAQ,SAAS,YAAckM,EAAIS,cACvMhK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,yGAAwGH,SAAE4J,EAAIU,eAC5HjK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,yGAAwGH,SAAE4J,EAAI3J,WAAa,MAAQ,QACjJI,EAAAA,EAAAA,KAAA,MAAIF,UAAU,yGAAwGH,UAAE1C,EAAAA,EAAAA,IAAWsM,EAAIW,cAErIlB,GAAqBA,EAAkB1B,OAAS,GAAK0B,EAAkBY,KAAKL,GAAsB,aAAfA,EAAIM,QACtE,WAAfN,EAAIM,QACF7J,EAAAA,EAAAA,KAAA,MAAIF,UAAU,qHAGdE,EAAAA,EAAAA,KAAA,MAAIF,UAAU,kHAAiHH,UAC5H1C,EAAAA,EAAAA,IAAWsM,EAAIY,YAIH,WAAfZ,EAAIM,OACN,IACEN,EAAIM,QACN7J,EAAAA,EAAAA,KAAA,MAAIF,UAAU,gHAA+GH,UAC1H1C,EAAAA,EAAAA,IAAWsM,EAAIY,cAQtBnK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,yGAAwGH,SAAE4J,EAAIM,UAC5HpK,EAAAA,EAAAA,MAAA,MAAIK,UAAU,yGAAwGH,SAAA,CACnG,WAAf4J,EAAIM,QACN7J,EAAAA,EAAAA,KAAA,OAAKF,UAAU,cAAaH,UAC1BF,EAAAA,EAAAA,MAAC2K,EAAAA,EAAI,CAACzH,GAAG,MAAM7C,UAAU,uDAAsDH,SAAA,EAC7EK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,SAAQH,UACrBK,EAAAA,EAAAA,KAACoK,EAAAA,EAAKC,OAAM,CAACvK,UAAU,kHAAiHH,SAAC,eAI3IK,EAAAA,EAAAA,KAACwC,EAAAA,EAAU,CACTG,GAAIC,EAAAA,SACJI,MAAM,mCACNC,UAAU,+BACVC,QAAQ,kCACRC,MAAM,iCACNC,UAAU,kCACVC,QAAQ,+BAA8B1D,UAGtCK,EAAAA,EAAAA,KAACoK,EAAAA,EAAKE,MAAK,CAACxK,WACRiK,EAAQ,EAAI,SAAW,UADJ,gLAE2JpK,UAChLF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,yBAAwBH,SAAA,CAChB,YAArB2J,EAAYC,IAA2C,WAArBD,EAAYC,IAC9CvJ,EAAAA,EAAAA,KAACoK,EAAAA,EAAKG,KAAI,CAAA5K,SACP6K,IAAA,IAAC,OAAEC,GAAQD,EAAA,OACVxK,EAAAA,EAAAA,KAAA,UACEF,WACE2K,EAAS,4BAA8B,iBAD9B,iFAGXC,IAAKnB,EAAImB,IACThH,QAASwF,EAAWvJ,SACrB,mBAKD,GACoB,YAArB2J,EAAYC,IAA2C,WAArBD,EAAYC,IAA0C,eAArBD,EAAYC,IAAyC,WAAfA,EAAIM,QAAmD,YAA5BF,EAAmBJ,GAatJ,IAZJvJ,EAAAA,EAAAA,KAACoK,EAAAA,EAAKG,KAAI,CAAA5K,SACTgL,IAAA,IAAC,OAAEF,GAAQE,EAAA,OACV3K,EAAAA,EAAAA,KAAA,UACEF,WACE2K,EAAS,4BAA8B,iBAD9B,iFAGX/G,QAAUrE,IAlHlCgB,OAAOC,SAASC,KAAO,WAkHmCZ,SACjC,eAMLK,EAAAA,EAAAA,KAACoK,EAAAA,EAAKG,KAAI,CAAA5K,SACPiL,IAAA,IAAC,OAAEH,GAAQG,EAAA,OACV5K,EAAAA,EAAAA,KAAC6K,EAAkB,CAACH,IAAKnB,EAAImB,IAAKjD,GAAIA,mBAO3C,GAEQ,WAAf8B,EAAIM,QACN7J,EAAAA,EAAAA,KAAA,OAAKF,UAAU,cAAaH,UAC1BF,EAAAA,EAAAA,MAAC2K,EAAAA,EAAI,CAACzH,GAAG,MAAM7C,UAAU,uDAAsDH,SAAA,EAC7EK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,SAAQH,UACrBK,EAAAA,EAAAA,KAACoK,EAAAA,EAAKC,OAAM,CAACvK,UAAU,kHAAiHH,SAAC,eAI3IK,EAAAA,EAAAA,KAACwC,EAAAA,EAAU,CACTG,GAAIC,EAAAA,SACJI,MAAM,mCACNC,UAAU,+BACVC,QAAQ,kCACRC,MAAM,iCACNC,UAAU,kCACVC,QAAQ,+BAA8B1D,UAEtCK,EAAAA,EAAAA,KAACoK,EAAAA,EAAKE,MAAK,CAACxK,WACRiK,EAAQ,EAAI,SAAW,UADJ,gLAE2JpK,UAChLF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,yBAAwBH,SAAA,CACd,YAArB2J,EAAYC,IAA2C,WAArBD,EAAYC,IAChDvJ,EAAAA,EAAAA,KAACoK,EAAAA,EAAKG,KAAI,CAAA5K,SACPmL,IAAA,IAAC,OAAEL,GAAQK,EAAA,OACV9K,EAAAA,EAAAA,KAAA,UACEF,WACE2K,EAAS,4BAA8B,iBAD9B,iFAGXC,IAAKnB,EAAImB,IACThH,QAASwF,EAAWvJ,SACrB,mBAKD,IACoB,QAArB9C,EAAY0M,IAAuC,WAArBD,EAAYC,IAA0C,WAArBD,EAAYC,IAA0C,WAArBD,EAAYC,IAA0C,eAArBD,EAAYC,MAC7I/K,EAAAA,EAAAA,KAAQQ,EAAAA,EAAAA,IAAe+J,IAAW/J,EAAAA,EAAAA,IAAeuK,EAAIW,cAAgB,GACxEzK,EAAAA,EAAAA,MAAAI,EAAAA,SAAA,CAAAF,SAAA,CACyB,eAArB8J,EAAYF,IAAqD,WAA5BI,EAAmBJ,IAA0C,WAArBE,EAAYF,IAAiC,OAAZN,GACnG,OAAZA,GAAiD,WAA5BU,EAAmBJ,IAA0C,WAArBE,EAAYF,IACpD,UAArBE,EAAYF,IAA2C,QAArBE,EAAYF,IAC/CvJ,EAAAA,EAAAA,KAACoK,EAAAA,EAAKG,KAAI,CAAA5K,SACPoL,IAAA,IAAC,OAAEN,GAAQM,EAAA,OACV/K,EAAAA,EAAAA,KAAA,UACEF,WACE2K,EAAS,4BAA8B,iBAD9B,iFAGX/G,QAAUrE,GA9MpBA,KAClB4G,QAAQC,IAAI7G,GACD,eAAPA,EACFsJ,GAAqB,GAErBtI,OAAOC,SAASC,KAAO,YAyMsByK,CAAWvB,EAAYF,IAAM5J,SACjD,eAKD,GAEsB,UAArB8J,EAAYF,IAAgD,YAA5BI,EAAmBJ,IAA4C,eAArBE,EAAYF,GAavF,IAZJvJ,EAAAA,EAAAA,KAACoK,EAAAA,EAAKG,KAAI,CAAA5K,SACPsL,IAAA,IAAC,OAAER,GAAQQ,EAAA,OACVjL,EAAAA,EAAAA,KAAA,UACEF,WACE2K,EAAS,4BAA8B,iBAD9B,iFAGX/G,QAAS2F,EAAa1J,SACvB,iBAOmB,YAArB2J,EAAYC,IAA2C,WAArBD,EAAYC,IAA0C,eAArBD,EAAYC,IAAyC,WAAfA,EAAIM,QAAmD,YAA5BF,EAAmBJ,IAA2C,QAArBA,EAAI2B,cAA4C,eAAlB3B,EAAIG,UAa9M,IAZJ1J,EAAAA,EAAAA,KAACoK,EAAAA,EAAKG,KAAI,CAAA5K,SACPwL,IAAA,IAAC,OAAEV,GAAQU,EAAA,OACVnL,EAAAA,EAAAA,KAAA,UACEF,WACE2K,EAAS,4BAA8B,iBAD9B,iFAGX/G,QAAUrE,GA/NtBA,KAChBuJ,EAAsBvJ,EAAK+L,0BAC3BvC,EAAgBxJ,EAAKmK,UACrBV,EAAiBzJ,EAAKqL,KACtBpJ,GAA0B,IA2NqB+J,CAAS9B,GAAK5J,SAClC,iBAODK,EAAAA,EAAAA,KAAAH,EAAAA,SAAA,KACNG,EAAAA,EAAAA,KAACoK,EAAAA,EAAKG,KAAI,CAAA5K,SACP2L,IAAA,IAAC,OAAEb,GAAQa,EAAA,OACVtL,EAAAA,EAAAA,KAAC6K,EAAkB,CAACH,IAAKnB,EAAImB,IAAKjD,GAAIA,mBAO3C,QA5LFsC,GA9CGL,cAkPhB1J,EAAAA,EAAAA,KAAA,OAAAL,UACAF,EAAAA,EAAAA,MAAA,QAAMK,UAAU,4BAA2BH,SAAA,EAACK,EAAAA,EAAAA,KAACuL,EAAAA,IAAY,CAACzL,UAAU,wBAAuB,gDAA4CE,EAAAA,EAAAA,KAAA,KAAGO,KAAK,WAAWT,UAAU,0BAAyBH,SAAC,4BAShM6L,EAAkBC,IAAmB,IAAlB,WAAC9G,GAAW8G,EAEnC,QAAkBvJ,IAAfyC,EACH,OACE3E,EAAAA,EAAAA,KAAA,OAAKF,UAAU,mEAAkEH,SAC9EgF,GAAcA,EAAW2C,QAC1B7H,EAAAA,EAAAA,MAAA,SAAOK,UAAU,mDAAkDH,SAAA,EACjEK,EAAAA,EAAAA,KAAA,SAAAL,UACEF,EAAAA,EAAAA,MAAA,MAAIK,UAAU,cAAaH,SAAA,EACzBK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,8GAA6GH,SAAC,aAC5HK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,8GAA6GH,SAAC,gBAC5HK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,8GAA6GH,SAAC,YAC5HK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,8GAA6GH,SAAC,UAC5HK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,8GAA6GH,SAAC,iBAGhIK,EAAAA,EAAAA,KAAA,SAAOF,UAAU,oCAAmCH,SACjDgF,aAAU,EAAVA,EAAYmF,IAAI,CAACP,EAAKQ,KACrBtK,EAAAA,EAAAA,MAAA,MAAgBK,UAAU,cAAaH,SAAA,EACvCK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,yGAAwGH,SAAE4J,EAAImC,aAC5H1L,EAAAA,EAAAA,KAAA,MAAIF,UAAU,yGAAwGH,SAAE4J,EAAIoC,SAC5H3L,EAAAA,EAAAA,KAAA,MAAIF,UAAU,yGAAwGH,SAAE4J,EAAIqC,QAASvN,EAAAA,EAAAA,IAAakL,EAAIzM,SAAUyM,EAAIqC,QAAU,MAC9K5L,EAAAA,EAAAA,KAAA,MAAIF,UAAU,yGAAwGH,UAAE1C,EAAAA,EAAAA,IAAWsM,EAAIsC,eACvI7L,EAAAA,EAAAA,KAAA,MAAIF,UAAU,yGAAwGH,SAAoB,wBAAlB4J,EAAIuC,UAAsC,WAAa,gBALtK/B,UAUbtK,EAAAA,EAAAA,MAAA,QAAMK,UAAU,4BAA2BH,SAAA,EAACK,EAAAA,EAAAA,KAACuL,EAAAA,IAAY,CAACzL,UAAU,wBAAuB,sDAAkDE,EAAAA,EAAAA,KAAA,KAAGO,KAAK,WAAWT,UAAU,0BAAyBH,SAAC,wBAKtMoM,EAAoBC,IAAgX,IAA/W,WAACC,EAAU,gBAAEC,EAAe,aAAEC,EAAY,YAAEC,EAAW,iBAAEC,EAAgB,YAAEC,EAAW,gBAAEC,EAAe,mBAAEC,EAAkB,KAAEnN,EAAI,gBAAEoN,EAAe,cAAEC,EAAa,iBAAEC,EAAgB,oBAAEC,EAAmB,gBAAEC,EAAe,WAAEC,EAAU,gBAAEC,EAAe,uBAAEC,EAAsB,SAAEC,EAAQ,cAAEC,EAAa,qBAAEC,EAAoB,mBAAEC,EAAkB,sBAAEC,GAAsBrB,EAClY,MAMMsB,EAAWA,IACW,WAAvBjO,EAAKtC,cAIJwQ,EAAaA,IACS,aAAvBlO,EAAKtC,cAIJyQ,EAAWA,IACH,KAATnO,GAAwB,OAATA,EAIdoO,EAAqBA,IACC,eAAvBpO,EAAKtC,eAAoE,eAAlC8P,EAAgB9P,cAItD2Q,EAAyBC,GACtBC,SAASD,GAAOpP,eAAe,SAGlCsP,EAAkBA,IACnB1B,GAAgBD,IAAoBoB,IAInCQ,EAAkBA,IACnBF,SAASnB,IAAoBmB,SAASlB,KAAmBY,IAIxDS,EAAmBA,IACpBH,SAASjB,IAAqBiB,SAAShB,KAA0BU,MAAeC,IAI/ES,EAAqBA,IACtBJ,SAASR,IAAuBQ,SAASP,KAA4BC,IAI1E,OACE7N,EAAAA,EAAAA,MAAA,OAAKK,UAAU,SAAQH,SAAA,EACrBF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,KAAGK,UAAW,kCAET+N,KAAuBL,KAAeC,IAEnC,GADA,gBAEH9N,SAAA,EACHF,EAAAA,EAAAA,MAAA,QAAMK,UAAU,SAAQH,SAAA,CAEnBkO,MAAuBL,MAAeC,MACvCzN,EAAAA,EAAAA,KAAA,OAAKe,IAAKkN,EAAYC,IAAI,gBAAgBpO,UAAU,+BACrD,uBACkBE,EAAAA,EAAAA,KAAAH,EAAAA,SAAA,QAErBJ,EAAAA,EAAAA,MAAA,QAAMK,UAAU,SAAQH,SAAA,CACpB+N,EAAsBxB,GACpBoB,KAAeG,IAAqF,IAA7DhO,EAAAA,EAAAA,MAAA,QAAAE,SAAA,CAAM,WAAS+N,EAAsBvB,aAGpF1M,EAAAA,EAAAA,MAAA,KAAGK,UAAW,yBAGiB,UAAvBT,EAAKtC,eACL6Q,SAASrB,IAAoBqB,SAASpB,IAClCqB,MAAuBL,MAAeC,IACxC,eACA,IACH9N,SAAA,EACLF,EAAAA,EAAAA,MAAA,QAAMK,UAAU,SAAQH,SAAA,CAEnBkO,MAAuBL,MAAeC,MACvCzN,EAAAA,EAAAA,KAAA,OAAKe,IAAKkN,EAAYC,IAAI,gBAAgBpO,UAAU,+BACrD,8BACyBE,EAAAA,EAAAA,KAAAH,EAAAA,SAAA,QAE5BJ,EAAAA,EAAAA,MAAA,QAAMK,UAAU,SAAQH,SAAA,CACpB+N,EAAsBnB,GACC,UAAvBlN,EAAKtC,eAA4B0C,EAAAA,EAAAA,MAAA,QAAAE,SAAA,CAAM,WAAS+N,EAAsBlB,MAA8B,SAKjF,eAAvBnN,EAAKtC,cACHuQ,KACE7N,EAAAA,EAAAA,MAAAI,EAAAA,SAAA,CAAAF,SAAA,EACEF,EAAAA,EAAAA,MAAA,KAAGK,UAAW,UAAUH,SAAA,EACtBK,EAAAA,EAAAA,KAAA,QAAMF,UAAU,SAAQH,SAAC,oCACzBK,EAAAA,EAAAA,KAAA,QAAMF,UAAU,SAAQH,SACrB+N,EAAsBjB,SAG3BhN,EAAAA,EAAAA,MAAA,KAAGK,UAAW,UAAUH,SAAA,EACtBK,EAAAA,EAAAA,KAAA,QAAMF,UAAU,SAAQH,SAAC,gCACzBK,EAAAA,EAAAA,KAAA,QAAMF,UAAU,SAAQH,SACrB+N,EAAsBjB,SAG3BhN,EAAAA,EAAAA,MAAA,KAAGK,UAAW,UAAUH,SAAA,EACtBK,EAAAA,EAAAA,KAAA,QAAMF,UAAU,SAAQH,SAAC,qCACzBK,EAAAA,EAAAA,KAAA,QAAMF,UAAU,SAAQH,SACrB+N,EAAsBN,YAK7B3N,EAAAA,EAAAA,MAAAI,EAAAA,SAAA,CAAAF,SAAA,EACEF,EAAAA,EAAAA,MAAA,KAAGK,UAAW,8BAERgO,MAAsBN,IACpB,eACA,uBAEN7N,SAAA,EACAF,EAAAA,EAAAA,MAAA,QAAMK,UAAU,SAAQH,SAAA,CAEnBmO,KAAsBD,MAAsBL,MAC7CxN,EAAAA,EAAAA,KAAA,OAAKe,IAAKkN,EAAYC,IAAI,gBAAgBpO,UAAU,+BACrD,kCAC6BE,EAAAA,EAAAA,KAAAH,EAAAA,SAAA,QAEhCJ,EAAAA,EAAAA,MAAA,QAAMK,UAAU,SAAQH,SAAA,EACxBF,EAAAA,EAAAA,MAAA,QAAAE,SAAA,CAAM,IAAG+N,EAAsBjB,MAAyB,YAAQzM,EAAAA,EAAAA,KAAA,QAAAL,SAAQ+N,EAAsBhB,YAGhGjN,EAAAA,EAAAA,MAAA,KAAGK,UAAW,+BAEPiO,MAAsBF,KAAuBL,IAE5C,GADA,mCAGN7N,SAAA,EACAF,EAAAA,EAAAA,MAAA,QAAMK,UAAU,SAAQH,SAAA,EAElBoO,KAAsBF,MAAsBL,KAAgBK,KAAqBN,OACnFvN,EAAAA,EAAAA,KAAA,OAAKe,IAAKkN,EAAYC,IAAI,gBAAgBpO,UAAU,+BACrD,8BACyBE,EAAAA,EAAAA,KAAAH,EAAAA,SAAA,QAE5BJ,EAAAA,EAAAA,MAAA,QAAMK,UAAU,SAAQH,SAAA,EACxBF,EAAAA,EAAAA,MAAA,QAAAE,SAAA,CAAM,IAAG+N,EAAsBf,MAC7BY,IAAqF,IAAtE9N,EAAAA,EAAAA,MAAA,QAAAE,SAAA,CAAM,WAAU+N,EAAsBd,YAvJ3C,QAAvBvN,EAAKtC,eA2JM0C,EAAAA,EAAAA,MAAA,KAAGK,UAAW,iCAEPkO,MAAwBH,KAAuBL,IAE9C,GADA,qCAGN7N,SAAA,EACAF,EAAAA,EAAAA,MAAA,QAAMK,UAAU,SAAQH,SAAA,EAElBqO,KAAwBH,MAAsBL,KAAgBK,KAAqBN,OACrFvN,EAAAA,EAAAA,KAAA,OAAKe,IAAKkN,EAAYC,IAAI,gBAAgBpO,UAAU,+BACrD,mCAC8BE,EAAAA,EAAAA,KAAAH,EAAAA,SAAA,QAEjCJ,EAAAA,EAAAA,MAAA,QAAMK,UAAU,SAAQH,SAAA,EACxBF,EAAAA,EAAAA,MAAA,QAAAE,SAAA,CAAM,IAAG+N,EAAsBN,MAC7BG,IAAuF,IAAxE9N,EAAAA,EAAAA,MAAA,QAAAE,SAAA,CAAM,WAAU+N,EAAsBL,aAGxDrN,EAAAA,EAAAA,KAAAH,EAAAA,SAAA,OAGiB,eAAvBR,EAAKtC,eAAoE,eAAlC8P,EAAgB9P,eACxD0C,EAAAA,EAAAA,MAAAI,EAAAA,SAAA,CAAAF,SAAA,EACEF,EAAAA,EAAAA,MAAA,KAAGK,UAAW,4BAERgN,GAAcC,EACZ,eACA,qBAENpN,SAAA,EACAF,EAAAA,EAAAA,MAAA,QAAMK,UAAU,SAAQH,SAAA,CAIjB,wBACeK,EAAAA,EAAAA,KAAAH,EAAAA,SAAA,QAEtBJ,EAAAA,EAAAA,MAAA,QAAMK,UAAU,SAAQH,SAAA,EACxBF,EAAAA,EAAAA,MAAA,QAAAE,SAAA,CAAM,IAAG+N,EAAsBZ,MAAoB,YAAQ9M,EAAAA,EAAAA,KAAA,QAAAL,SAAQ+N,EAAsBX,YAG3FtN,EAAAA,EAAAA,MAAA,KAAGK,UAAW,4BAERmN,GAAYC,EACV,eACA,qBAENvN,SAAA,EACAF,EAAAA,EAAAA,MAAA,QAAMK,UAAU,SAAQH,SAAA,CAIjB,sBACaK,EAAAA,EAAAA,KAAAH,EAAAA,SAAA,QAEpBJ,EAAAA,EAAAA,MAAA,QAAMK,UAAU,SAAQH,SAAA,EACxBF,EAAAA,EAAAA,MAAA,QAAAE,SAAA,CAAM,IAAG+N,EAAsBT,MAAkB,YAAQjN,EAAAA,EAAAA,KAAA,QAAAL,SAAQ+N,EAAsBR,eAI3FlN,EAAAA,EAAAA,KAAAH,EAAAA,SAAA,IAIFR,IAASiO,KACT7N,EAAAA,EAAAA,MAAA,KAAGK,UAAU,yBAAwBH,SAAA,EAACK,EAAAA,EAAAA,KAAA,QAAMF,UAAU,SAAQH,SAAC,+BAAiC,MAAEK,EAAAA,EAAAA,KAAA,QAAMF,UAAU,SAAQH,SAAEyM,OAC1H,MAIW,KAAfE,IAAsBgB,KAAcO,MAA0C,IAArBxB,GAC3D5M,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEK,EAAAA,EAAAA,KAAA,KAAGF,UAAU,uBAAsBH,SAAC,qCACpCK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,2BAA0BH,UACvCK,EAAAA,EAAAA,KAAA,KAAAL,UACEK,EAAAA,EAAAA,KAAA,KAAGO,KAAK,iBAAiBb,MAAM,yFAAwFC,SAAC,mBAI1H,GAEW,KAAf2M,GAAsBgB,MAAcO,MAA0C,IAArBxB,GAA8BoB,IASrF,IARFhO,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEK,EAAAA,EAAAA,KAAA,KAAGF,UAAU,uBAAsBH,SAAC,gEACpCK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,2BAA0BH,UACvCK,EAAAA,EAAAA,KAAA,KAAAL,UACEK,EAAAA,EAAAA,KAAA,KAAGO,KAAK,gCAAgCb,MAAM,yFAAwFC,SAAC,4BAM7IK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,OAAMH,SACnBsM,aAAU,EAAVA,EAAYnC,IAAI,CAACP,EAAKQ,KACrBtK,EAAAA,EAAAA,MAAA,MAAAE,SAAA,EAAIF,EAAAA,EAAAA,MAAA,QAAMK,UAAU,YAAWH,SAAA,CAAE4J,EAAI4E,IAAI,OAAQ,IAAEP,SAASrE,EAAI6E,aAAa7P,eAAe,mBAO9F8P,EAAiBA,KAEnB5O,EAAAA,EAAAA,MAAA,OAAKK,UAAU,SAAQH,SAAA,EACrBF,EAAAA,EAAAA,MAAA,KAAGK,UAAU,eAAcH,SAAA,CAAC,2MAAuMK,EAAAA,EAAAA,KAAA,KAAGO,KAAK,iCAAiCT,UAAU,0BAAyBH,SAAC,eAAc,aAC9TK,EAAAA,EAAAA,KAAA,KAAGF,UAAU,YAAWH,SAAC,kBACzBF,EAAAA,EAAAA,MAAA,MAAAE,SAAA,EACEK,EAAAA,EAAAA,KAAA,MAAAL,UAAIK,EAAAA,EAAAA,KAAA,KAAGO,KAAK,cAAcT,UAAU,+BAA8BH,SAAC,gBACnEK,EAAAA,EAAAA,KAAA,MAAAL,UAAIK,EAAAA,EAAAA,KAAA,KAAGO,KAAK,cAAcT,UAAU,+BAA8BH,SAAC,iBACnEK,EAAAA,EAAAA,KAAA,MAAAL,UAAIK,EAAAA,EAAAA,KAAA,KAAGO,KAAK,+BAA+BT,UAAU,+BAA8BH,SAAC,iBACpFK,EAAAA,EAAAA,KAAA,MAAAL,UAAIK,EAAAA,EAAAA,KAAA,KAAGO,KAAK,gCAAgCT,UAAU,+BAA8BH,SAAC,uBAMvF2O,EAAgBA,KAYlBtO,EAAAA,EAAAA,KAAA,OAAAL,UACEK,EAAAA,EAAAA,KAACmI,EAAAA,EAAOC,OAAM,CACZtI,UAAU,kEACVuI,SAAU,CAAEC,MAAO,IACnB5E,QAdN,YACE6K,EAAAA,EAAAA,IAAa,WACbA,EAAAA,EAAAA,IAAa,cAEbvK,EAAAA,EAAMwK,IAAI,oCAA2CrK,KAAK,WACxD9D,OAAOC,SAASC,KAAO,QACzB,EACF,EAOsBZ,SACjB,aAODmI,EAAiB5C,IACrB,MAAOG,IAASzD,EAAAA,EAAAA,UAASsD,EAAMG,QACxB0C,EAAaC,IAAkBpG,EAAAA,EAAAA,WAAS,GAKzCqG,EAAaA,KACjBD,GAAe,IAqBjB,OACEvI,EAAAA,EAAAA,MAAAI,EAAAA,SAAA,CAAAF,SAAA,EACEK,EAAAA,EAAAA,KAAA,QAAMF,UAAU,wDAAwD4D,QA3BpD+K,KACtBzG,GAAe,IA0BoFrI,SAAC,oBAClGK,EAAAA,EAAAA,KAACwC,EAAAA,EAAU,CAACC,QAAM,EAACC,KAAMqF,EAAapF,GAAIC,EAAAA,SAASjD,UACjDF,EAAAA,EAAAA,MAACoD,EAAAA,EAAM,CAACF,GAAG,MAAM7C,UAAU,gBAAgBgD,QAASmF,EAAWtI,SAAA,EAC7DK,EAAAA,EAAAA,KAACwC,EAAAA,EAAWO,MAAK,CACfJ,GAAIC,EAAAA,SACJI,MAAM,wBACNC,UAAU,YACVC,QAAQ,cACRC,MAAM,uBACNC,UAAU,cACVC,QAAQ,YAAW1D,UAEnBK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,4CAGjBE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,gCAA+BH,UAC5CK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,8DAA6DH,UAC1EK,EAAAA,EAAAA,KAACwC,EAAAA,EAAWO,MAAK,CACfJ,GAAIC,EAAAA,SACJI,MAAM,wBACNC,UAAU,qBACVC,QAAQ,wBACRC,MAAM,uBACNC,UAAU,wBACVC,QAAQ,qBAAoB1D,UAE5BF,EAAAA,EAAAA,MAACoD,EAAAA,EAAOS,MAAK,CAACxD,UAAU,qHAAoHH,SAAA,EAC1IK,EAAAA,EAAAA,KAAC6C,EAAAA,EAAOU,MAAK,CACXZ,GAAG,KACH7C,UAAU,8CAA6CH,SACxD,oBAGDF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,OAAMH,SAAA,EACnBK,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wBAAuBH,SAAC,2MAGrCK,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wBAAuBH,SAAC,sDAKvCF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,OAAMH,SAAA,EACnBK,EAAAA,EAAAA,KAAA,UACEwD,KAAK,SACL1D,UAAU,8IACV4D,QAlESgL,KAC3B7N,SAASgD,cAAc,qBAAqBC,UAAUC,IAAI,UAC1DC,EAAAA,EAAMC,KAAK,2CAAmD,CAC9D,EAAG,CAAEC,QAAS,CAAE,eAAgB,uCAAyCC,KAAK,SAASC,GAErF,GADaA,EAAI1G,KACP4G,QAOR,OANAjE,OAAOsO,SAASC,OAAOC,SAAS,CAAE,OAAUxJ,IAC5ChF,OAAOsO,SAASG,SAASzJ,GACzBhF,OAAOsO,SAASI,MAAM,iBAAkB,CAAC,GAEzChN,IAAAA,QAAe,gBACf1B,OAAOC,SAASmE,SAGlB5D,SAASgD,cAAc,qBAAqBC,UAAUS,OAAO,UAC7DxC,IAAAA,MAAa,OACf,IAkDgDpC,SAC/B,4BAGDK,EAAAA,EAAAA,KAAA,UACEwD,KAAK,SACL1D,UAAU,yOACV4D,QAASuE,EAAWtI,SACrB,gCAcfkL,EAAqBmE,IAAkB,IAAjB,IAAEtE,EAAG,GAAEjD,GAAIuH,EACrC,MAAOjH,EAAaC,IAAkBpG,EAAAA,EAAAA,WAAS,GACzC0D,GAAO2J,EAAAA,EAAAA,MAIPhH,EAAaA,KACjBD,GAAe,IAyBjB,OACEvI,EAAAA,EAAAA,MAAAI,EAAAA,SAAA,CAAAF,SAAA,EACEK,EAAAA,EAAAA,KAAA,UACAF,UAAW,wGACX4K,IAAKA,EACLhH,QAlCoBwL,KACtBlH,GAAe,IAiCYrI,SACxB,YAGDK,EAAAA,EAAAA,KAACwC,EAAAA,EAAU,CAACC,QAAM,EAACC,KAAMqF,EAAapF,GAAIC,EAAAA,SAASjD,UACjDF,EAAAA,EAAAA,MAACoD,EAAAA,EAAM,CAACF,GAAG,MAAM7C,UAAU,gBAAgBgD,QAASmF,EAAWtI,SAAA,EAC7DK,EAAAA,EAAAA,KAACwC,EAAAA,EAAWO,MAAK,CACfJ,GAAIC,EAAAA,SACJI,MAAM,wBACNC,UAAU,YACVC,QAAQ,cACRC,MAAM,uBACNC,UAAU,cACVC,QAAQ,YAAW1D,UAEnBK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,4CAGjBE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,gCAA+BH,UAC5CK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,8DAA6DH,UAC1EK,EAAAA,EAAAA,KAACwC,EAAAA,EAAWO,MAAK,CACfJ,GAAIC,EAAAA,SACJI,MAAM,wBACNC,UAAU,qBACVC,QAAQ,wBACRC,MAAM,uBACNC,UAAU,wBACVC,QAAQ,qBAAoB1D,UAE5BF,EAAAA,EAAAA,MAACoD,EAAAA,EAAOS,MAAK,CAACxD,UAAU,qHAAoHH,SAAA,EAC1IK,EAAAA,EAAAA,KAAC6C,EAAAA,EAAOU,MAAK,CACXZ,GAAG,KACH7C,UAAU,8CAA6CH,SACxD,yBAGDK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,OAAMH,UACnBK,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wBAAuBH,SAAC,0DAKvCF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,kBAAiBH,SAAA,EAC9BK,EAAAA,EAAAA,KAAA,UACEwD,KAAK,SACL1D,UAAU,8IACV4D,QA1ECoD,IACf4D,IACJzC,IACApH,SAASgD,cAAc,qBAAqBC,UAAUC,IAAI,UACnC,KAApBuB,EAAK6J,YAAyC,OAApB7J,EAAK6J,WAChC9O,OAAOC,SAASC,KAAO,UAEvByD,EAAAA,EAAMC,KAAK,gDAAwD,CACjEwD,KACA0B,KAAMuB,GACL,CAAExG,QAAS,CAAE,eAAgB,uCAAyCC,KAAK,SAASC,GACrF,IAAIC,EAASD,EAAI1G,KACjB,GAAG2G,EAAOC,QAGR,OAFAvC,IAAAA,QAAe,gBACf1B,OAAOC,SAASmE,SAGlB5D,SAASgD,cAAc,qBAAqBC,UAAUS,OAAO,UAC1DF,EAAO3G,MAAMqE,IAAAA,MAAasC,EAAO3G,KAAKgH,IAC3C,KAuDqC/E,SACtB,SAGDK,EAAAA,EAAAA,KAAA,UACEwD,KAAK,SACL1D,UAAU,yOACV4D,QAASuE,EAAWtI,SACrB,+BAcrB,SAASyP,EAAaC,EAASC,GAC7B,OAAOD,EAAQE,MAAM,EAAGD,EAC1B,CAEA,SAASE,EAAcH,EAASC,GAC9B,GAAID,EAAQ/H,OAAOgI,EACjB,OAAO,EAGT,IAAIG,EAAeJ,EAAQ/H,OACvBoI,EAAW7Q,KAAK8Q,MAAMF,EAAaH,GAMvC,OALgBG,EAAeH,EAEjB,IACZI,GAAoB,GAEfA,CACT,CAEA,MAAME,EAAoBC,IAA6H,IAA5H,oBAACC,EAAmB,kBAAEC,EAAiB,oBAAEC,EAAmB,0BAAEC,EAAyB,YAAE3D,EAAW,gBAAEO,GAAgBgD,EAE/I,MAAOK,EAAQC,IAAavO,EAAAA,EAAAA,UAAS,KAC9BwO,EAAwBC,IAA6BzO,EAAAA,EAAAA,UAAS,KAC9D0O,EAA0BC,IAA+B3O,EAAAA,EAAAA,UAAS,KAClE4O,EAAUC,IAAe7O,EAAAA,EAAAA,UAAS,IAClC8O,EAAWC,IAAgB/O,EAAAA,EAAAA,UAAS,IACpCgP,EAAaC,IAAkBjP,EAAAA,EAAAA,UAAS,IAE/CjB,EAAAA,EAAAA,WAAU,KACR,IAAI0O,EAAUU,EACdM,EAA0BhB,GAE1BA,EAAUD,EAAaC,EAAQ,GAC/BkB,EAA4BlB,GAE5B,IAAIyB,EAAYtB,EAAcO,EAAkB,GAChDY,EAAaG,IACZ,CAACf,IAEL,MAAMgB,EAAeA,KAClB,IAAIC,EAAQnQ,SAASwB,eAAe,cAAcoB,MAC9C4L,EAAUU,EAEdV,EAAUU,EAAkBkB,OAAO,SAASC,GAC1C,IAAIC,EAAYD,EAAKE,WAAa,IAAMF,EAAKG,UAC7C,OAASH,EAAKE,WAAW5Q,QAAQwQ,IAAQ,GAAKE,EAAKG,UAAU7Q,QAAQwQ,IAAQ,GAAKE,EAAK7L,MAAM7E,QAAQwQ,IAAQ,GAAKG,EAAU3Q,QAAQwQ,IAAU,CAChJ,GAEA,IAAIF,EAAYtB,EAAcH,EAAQmB,GACtCG,EAAaG,GACbT,EAA0BhB,GAE1BA,EAAUD,EAAaC,EAAQmB,GAC/BD,EAA4BlB,KAG9B1O,EAAAA,EAAAA,WAAU,KACRoQ,KAEC,CAACP,IAEJ,MA4CMc,EAAoBC,IACxBV,EAAeU,GAEf,IAAIlC,EAAUe,EACVoB,GAAeD,EAAK,GAAGf,EAE3BnB,EAAUA,EAAQE,MAAMiC,EAAanC,EAAQ/H,QAC7C+H,EAAUD,EAAaC,EAAQmB,GAE/BD,EAA4BlB,IAmB9B,OACErP,EAAAA,EAAAA,KAAAH,EAAAA,SAAA,CAAAF,UACAF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,SAAQH,SAAA,EACrBF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,oDAAmDH,SAAA,EAChEK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,0BAAyBH,UACtCK,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEACjB0D,KAAK,OACLiO,GAAG,aACH/K,KAAK,SACLD,YAAY,0BACZhD,MAAOyM,EACPlJ,SAAWC,IACT8J,IACAZ,EAAUlJ,EAAEF,OAAOtD,aAIvBzD,EAAAA,EAAAA,KAAA,OAAKF,UAAU,0BAAyBH,UACtCK,EAAAA,EAAAA,KAACmI,EAAAA,EAAOC,OAAM,CACdtI,UAAU,kFACV4R,WAAY,CAAEC,gBAAiB,WAC/BtJ,SAAU,CAAEC,MAAO,IACnB5E,QAASA,IAAKoM,IAAsBnQ,SACnC,mBAKFoQ,EAAkBzI,OAAS,GAC5B7H,EAAAA,EAAAA,MAAA,OAAKK,UAAU,2DAA0DH,SAAA,CAAC,SACnEF,EAAAA,EAAAA,MAAA,UAAQK,UAAU,oBAAoBkH,SAAWC,IA7C5DwJ,EA6CoFxJ,EA7ClEF,OAAOtD,YACzBoN,EAAe,IA4CwElR,SAAA,EAC/EK,EAAAA,EAAAA,KAAA,UAAQyD,MAAM,IAAG9D,SAAC,OAClBK,EAAAA,EAAAA,KAAA,UAAQyD,MAAM,KAAI9D,SAAC,QACnBK,EAAAA,EAAAA,KAAA,UAAQyD,MAAM,KAAI9D,SAAC,QACnBK,EAAAA,EAAAA,KAAA,UAAQyD,MAAM,KAAI9D,SAAC,UACZ,cAET,OAGJK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,kHAAiHH,SAC7H2Q,GAA4BA,EAAyBhJ,QACtD7H,EAAAA,EAAAA,MAAA,SAAOK,UAAU,yEAAwEH,SAAA,EACvFK,EAAAA,EAAAA,KAAA,SAAAL,UACEF,EAAAA,EAAAA,MAAA,MAAIK,UAAU,cAAaH,SAAA,EACzBK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,qHAAoHH,SAAC,cACnIK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,qHAAoHH,SAAC,WACnIK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,qHAAoHH,SAAC,YACnIK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,qHAAoHH,SAAC,eACnG,eAA9B2M,EAAYvP,eAAoE,eAAlC8P,EAAgB9P,eAC9DiD,EAAAA,EAAAA,KAAA,MAAIF,UAAU,qHAAoHH,SAAC,iBAEnIK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,qHAAoHH,SAAC,sBAGrIK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,qHAAoHH,SAAC,kBAGvIK,EAAAA,EAAAA,KAAA,SAAOF,UAAU,oCAAmCH,SACjD2Q,aAAwB,EAAxBA,EAA0BxG,IAAI,CAACP,EAAKQ,KACnCtK,EAAAA,EAAAA,MAAA,MAAgBK,UAAU,cAAaH,SAAA,EACvCF,EAAAA,EAAAA,MAAA,MAAIK,UAAU,yGAAwGH,SAAA,CAAE4J,EAAI6H,WAAW,IAAE7H,EAAI8H,cAC7IrR,EAAAA,EAAAA,KAAA,MAAIF,UAAU,yGAAwGH,SAAE4J,EAAIlE,SAC5HrF,EAAAA,EAAAA,KAAA,MAAIF,UAAU,yGAAwGH,SAAE4J,EAAIM,UAC5H7J,EAAAA,EAAAA,KAAA,MAAIF,UAAU,yGAAwGH,UAAE1C,EAAAA,EAAAA,IAAWsM,EAAIsC,eACvI7L,EAAAA,EAAAA,KAAA,MAAIF,UAAU,+FAA8FH,SAEzE,eAA9B2M,EAAYvP,eAAoE,eAAlC8P,EAAgB9P,eAC/D0C,EAAAA,EAAAA,MAAAI,EAAAA,SAAA,CAAAF,SAAA,EACEF,EAAAA,EAAAA,MAAA,KAAAE,SAAA,CAAG,WAAS4J,EAAI6E,gBAChB3O,EAAAA,EAAAA,MAAA,KAAAE,SAAA,CAAG,SAAO4J,EAAIqI,sBACdnS,EAAAA,EAAAA,MAAA,KAAAE,SAAA,CAAG,OAAK4J,EAAIsI,uBAGd7R,EAAAA,EAAAA,KAAA,KAAAL,SAAI4J,EAAI6E,iBAIZpO,EAAAA,EAAAA,KAAA,MAAIF,UAAU,yGAAwGH,SACnG,WAAf4J,EAAIM,QACN7J,EAAAA,EAAAA,KAAA,OAAKF,UAAU,cAAaH,UAC1BF,EAAAA,EAAAA,MAAC2K,EAAAA,EAAI,CAACzH,GAAG,MAAM7C,UAAU,uDAAsDH,SAAA,EAC7EK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,SAAQH,UACrBK,EAAAA,EAAAA,KAACoK,EAAAA,EAAKC,OAAM,CAACvK,UAAU,kHAAiHH,SAAC,eAI3IK,EAAAA,EAAAA,KAACwC,EAAAA,EAAU,CACTG,GAAIC,EAAAA,SACJI,MAAM,mCACNC,UAAU,+BACVC,QAAQ,kCACRC,MAAM,iCACNC,UAAU,kCACVC,QAAQ,+BAA8B1D,UAEtCK,EAAAA,EAAAA,KAACoK,EAAAA,EAAKE,MAAK,CAACxK,WACViK,EAAQ,EAAI,SAAW,UADF,yLAEkKpK,UACzLF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,yBAAwBH,SAAA,EACrCK,EAAAA,EAAAA,KAACoK,EAAAA,EAAKG,KAAI,CAAA5K,UACRK,EAAAA,EAAAA,KAAA,UACAF,UAAU,8FACV4D,QAASA,CAACoO,EAASV,EAAYC,EAAWhM,IAjH/C0M,EAACD,EAASV,EAAYC,EAAWhM,KAClD,IAAI2M,EAAS,CAAC,EACdA,EAAgB,QAAIF,EACpBE,EAAmB,WAAIZ,EACvBY,EAAkB,UAAIX,EACtBW,EAAc,MAAI3M,EAElB4K,GAA0B,EAAK+B,IA0G2CD,CAAWxI,EAAIuI,QAASvI,EAAI6H,WAAY7H,EAAI8H,UAAW9H,EAAIlE,OAAO1F,SACrH,iBAIHK,EAAAA,EAAAA,KAACoK,EAAAA,EAAKG,KAAI,CAAA5K,UACRK,EAAAA,EAAAA,KAACiS,EAAoB,CAACC,eAAgB3I,EAAIuI,QAASzM,MAAOkE,EAAIlE,WAEhErF,EAAAA,EAAAA,KAACoK,EAAAA,EAAKG,KAAI,CAAA5K,UACRK,EAAAA,EAAAA,KAACmS,EAAY,CAACD,eAAgB3I,EAAIuI,QAASzM,MAAOkE,EAAIlE,MAAO2K,oBAAqBA,kBAMnF,OA1DFjG,UAgEb/J,EAAAA,EAAAA,KAAA,OAAAL,UACAF,EAAAA,EAAAA,MAAA,QAAMK,UAAU,4BAA2BH,SAAA,EAACK,EAAAA,EAAAA,KAACuL,EAAAA,IAAY,CAACzL,UAAU,wBAAuB,4BAI/FL,EAAAA,EAAAA,MAAA,OAAKK,UAAU,mBAAkBH,SAAA,EAC/BK,EAAAA,EAAAA,KAAA,QAAMF,UAAU,wIAAuIH,SA1KlIyS,MACzB,IAAIC,EAAejC,EAAuB9I,OACtCgL,EAAS1B,EAAYJ,EACrB+B,EAAWD,GAAU9B,EAAS,GAMlC,OAJI8B,EAASD,IACXC,EAASD,GAGJ,WAAYE,EAAW,OAASD,EAAS,OAAUD,EAAe,YAkKlED,MAEHpS,EAAAA,EAAAA,KAAA,MAAIF,UAAU,6GAA4GH,SA7M1G6S,MACpB,IAAIC,EAAU,GAEd,GAAI/B,GAAW,GAAgC,IAA3BX,EAAkBzI,OACpC,OAAOmL,EAGL7B,EAAY,EACd6B,EAAQC,MAAK1S,EAAAA,EAAAA,KAAA,MAAIF,UAAU,qBAAuC4D,QAAUuD,GAAIqK,EAAiBV,EAAY,GAAGjR,SAAC,YAA5D,cAErD8S,EAAQC,MAAK1S,EAAAA,EAAAA,KAAA,MAAIF,UAAU,qBAAoBH,SAAmB,YAAb,cAGvD,IAAK,IAAIgT,EAAI,EAAGA,EAAIjC,EAAWiC,IAAK,CAClC,MACM7S,EAAY,oDADG8Q,IAAgB+B,EAAI,EAC2C,SAAW,IAC/FF,EAAQC,MACN1S,EAAAA,EAAAA,KAAA,MAAIF,UAAWA,EAAuB4D,QAAUuD,GAAMqK,EAAiBqB,EAAI,GAAGhT,SAC3EgT,EAAI,GADwBA,EAAI,GAIvC,CAQA,OANI/B,EAAYF,EACd+B,EAAQC,MAAK1S,EAAAA,EAAAA,KAAA,MAAIF,UAAU,qBAAuC4D,QAAUuD,GAAIqK,EAAiBV,EAAY,GAAGjR,SAAC,QAA5D,cAErD8S,EAAQC,MAAK1S,EAAAA,EAAAA,KAAA,MAAIF,UAAU,qBAAoBH,SAAmB,QAAb,cAGhD8S,GAiLFD,cASHI,EAAkBC,IAAsF,IAArF,eAACC,EAAc,sBAAEC,EAAqB,kBAAEC,EAAiB,oBAAEhD,GAAoB6C,EACtG,MAAOI,EAAUC,IAAetR,EAAAA,EAAAA,UAAS,KAClCuR,EAAaC,IAAkBxR,EAAAA,EAAAA,UAAS,KACxCyD,EAAOgO,IAAYzR,EAAAA,EAAAA,UAAS,KAC5B0R,EAAWC,IAAgB3R,EAAAA,EAAAA,UAAS,KACpC4R,EAAQC,IAAa7R,EAAAA,EAAAA,UAAS,IAS/B8R,EAAwBA,KAC5B,IAAItR,EAAQvB,SAASwB,eAAe,oBACpC,GAAY,OAARD,EAAa,CAClB,IAAIuR,EAAa9S,SAASwB,eAAe,kBACzCsR,EAAWC,MAAQ,GACnBD,EAAW7P,UAAUS,OAAO,mBAE5B,IAAIsP,EAAYhT,SAASwB,eAAe,qBACxCwR,EAAUD,MAAQ,GAClBC,EAAU/P,UAAUS,OAAO,mBAE3B0B,QAAQC,IAAI,eAGZ9D,EAAME,MAAMC,QAAU,OACnBwQ,GAAsB,EACxB,IAGFpS,EAAAA,EAAAA,WAAU,KACR,IAAIyQ,EAAa4B,EAAkB5B,WAAa4B,EAAkB5B,WAAa,GAC3EC,EAAY2B,EAAkB3B,UAAa2B,EAAkB3B,UAAY,GACzEhM,EAAQ2N,EAAkB3N,MAAS2N,EAAkB3N,MAAQ,GAC7DyM,EAAUkB,EAAkBlB,QAAWkB,EAAkBlB,QAAU,GAEvEoB,EAAY9B,EAAa,IAAMC,GAC/B+B,EAAehC,EAAa,IAAMC,GAClCgC,EAAShO,GACTkO,EAAalO,GACboO,EAAU3B,IACT,CAACkB,SAoCiB9Q,IAAjB4Q,IAAiD,IAAnBA,GAzELgB,MAC3B,IAAI1R,EAAQvB,SAASwB,eAAe,oBACxB,OAARD,IACLA,EAAME,MAAMC,QAAU,UAuErBuR,QAEmB5R,IAAjB4Q,IAAiD,IAAnBA,GAChCY,IA6EF,OACE1T,EAAAA,EAAAA,KAAAH,EAAAA,SAAA,CAAAF,UACAK,EAAAA,EAAAA,KAAA,OAAKyR,GAAG,mBAAmB3R,UAAU,iBAAgBH,UACnDK,EAAAA,EAAAA,KAAA,OAAKN,MAAM,kCAAiCC,UAC1CF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,0BAAyBH,SAAA,EACtCK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uDAAsDH,SAAC,kBAGtEK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wCAAuCH,UACpDK,EAAAA,EAAAA,KAAA,QAAMN,MAAM,QAAQgE,QAASA,IAAKgQ,IAAwB/T,SAAC,YAG/DF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,8CAA6CH,SAAA,EACxDF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,mDAAkDH,SAAA,EAC/DK,EAAAA,EAAAA,KAAA,KAAAL,SAAG,eACHK,EAAAA,EAAAA,KAAA,SAAOwD,KAAK,OAAOiO,GAAG,oBAAoB3R,UAAU,kDACpD2G,YAAY,WACZhD,MAAOwP,EACPjM,SAAWC,IACTA,EAAE8M,iBACFb,EAAYjM,EAAEF,OAAOtD,OArHfwD,KACpB,IAAIkK,EAAYlK,EAAEF,OAAOtD,MAAMuQ,OAC3BC,EAAqBpT,SAASwB,eAAe,2BAEjC,KAAZ8O,GACF8C,EAAmBC,UAAY,wBAClCD,EAAmB3R,MAAMC,QAAU,QAChC0E,EAAEF,OAAOjD,UAAUC,IAAI,qBAEvBkQ,EAAmBC,UAAY,GAClCD,EAAmB3R,MAAMC,QAAU,OAChC0E,EAAEF,OAAOjD,UAAUS,OAAO,qBA2Gd4P,CAAalN,IAGf2M,MAAM,MAEN5T,EAAAA,EAAAA,KAAA,OAAKN,MAAM,eAAe+R,GAAG,gCAE/BhS,EAAAA,EAAAA,MAAA,OAAKK,UAAU,mDAAkDH,SAAA,EAC/DK,EAAAA,EAAAA,KAAA,KAAAL,SAAG,aACHK,EAAAA,EAAAA,KAAA,SAAOwD,KAAK,OAAOiO,GAAG,iBAAiB3R,UAAU,kDACjD2G,YAAY,QACZhD,MAAO4B,EACP2B,SAAWC,IACTA,EAAE8M,iBACFV,EAASpM,EAAEF,OAAOtD,OAvJXwD,KACrB,IAAI5B,EAAQ4B,EAAEF,OAAOtD,MACjB2Q,EAAkBvT,SAASwB,eAAe,wBAE3B,KAAfgD,EAAM2O,QACRI,EAAgBF,UAAY,oBAC/BE,EAAgB9R,MAAMC,QAAU,QAC7B0E,EAAEF,OAAOjD,UAAUC,IAAI,oBAChB,eAAeyD,KAAKnC,IAK3B+O,EAAgBF,UAAY,GAC/BE,EAAgB9R,MAAMC,QAAU,OAC7B0E,EAAEF,OAAOjD,UAAUS,OAAO,qBAN1B6P,EAAgBF,UAAY,uBAC/BE,EAAgB9R,MAAMC,QAAU,QAC7B0E,EAAEF,OAAOjD,UAAUC,IAAI,qBA6IXwD,CAAcN,IAGhB2M,MAAM,MAEN5T,EAAAA,EAAAA,KAAA,OAAKN,MAAM,eAAe+R,GAAG,gCAGnCzR,EAAAA,EAAAA,KAAA,OAAKF,UAAU,GAAEH,UACfF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,aAAYH,SAAA,EACzBK,EAAAA,EAAAA,KAAA,SAAOwD,KAAK,SAASC,MAAM,gBAAgB3D,UAAU,2GAA2G4D,QAASA,IAzHhK1C,WACnB,IAAIqT,EAAgBxT,SAASwB,eAAe,qBACxC4R,EAAqBpT,SAASwB,eAAe,2BAC7CsR,EAAa9S,SAASwB,eAAe,kBACrC+R,EAAkBvT,SAASwB,eAAe,wBAI9C,GAA8B,KAA1BsR,EAAWlQ,MAAMuQ,OAItB,OAHGI,EAAgBF,UAAY,oBAC/BE,EAAgB9R,MAAMC,QAAU,aAChCoR,EAAW7P,UAAUC,IAAI,mBAEpB,IAAK,eAAeyD,KAAKnC,GAI9B,OAHG+O,EAAgBF,UAAY,uBAC/BE,EAAgB9R,MAAMC,QAAU,aAC7BoR,EAAW7P,UAAUC,IAAI,mBAI3B,GAAiC,KAA7BsQ,EAAc5Q,MAAMuQ,OAIzB,OAHGC,EAAmBC,UAAY,wBAClCD,EAAmB3R,MAAMC,QAAU,aAChC8R,EAAcvQ,UAAUC,IAAI,mBAM9B,GAFAlD,SAASgD,cAAc,qBAAqBC,UAAUC,IAAI,UAEtDoP,IAAckB,EAAc5Q,OAAS6P,IAAcK,EAAWlQ,MAGhE,OAFAiQ,SACA7S,SAASgD,cAAc,qBAAqBC,UAAUS,OAAO,UAK/DP,EAAAA,EAAMC,KADI,mDACM,CACdiO,eAAgBsB,EAChBc,aAAcX,EAAWlQ,MACzB8Q,gBAAkBF,EAAc5Q,MAChC+Q,oBAAqBrB,EACrBsB,iBAAkBnB,GACjB,CAAEpP,QAAS,CAAE,eAAgB,uCAAyCC,KAAK,SAASC,GACrF,IAAIC,EAASD,EAAI1G,KAIjB,GAFAmD,SAASgD,cAAc,qBAAqBC,UAAUS,OAAO,UAE1DF,EAAOC,QACR,GAAIoQ,MAAMC,QAAQtQ,EAAO3G,OAAS2G,EAAO3G,KAAK4J,OAAS,EACrD,IAAI,IAAIqL,EAAI,EAAGA,EAAItO,EAAO3G,KAAK4J,OAAQqL,IAAK,CAC1C,IAAIgB,EAAa9S,SAASwB,eAAe,kBACzCsR,EAAWC,MAAQvP,EAAO3G,KAAKiV,GAAGiC,MAClCjB,EAAW7P,UAAUC,IAAI,mBACzBhC,IAAAA,MAAasC,EAAO3G,KAAKiV,GAAGiC,MAC9B,MAEAlB,IACA1D,IACAjO,IAAAA,QAAe,uBAGjB2R,IACA1D,IACAjO,IAAAA,MAAa,uBAGjB,GAAG8S,MAAM,SAAUD,GACjBlB,IACA7S,SAASgD,cAAc,qBAAqBC,UAAUS,OAAO,UAC7DxC,IAAAA,MAAa,mDACf,IAmDwL+S,MAC9K9U,EAAAA,EAAAA,KAAA,SAAOwD,KAAK,SAASC,MAAM,QAAQ3D,UAAU,mGAAmG4D,QAASA,IAAKgQ,qBAUtKqB,EAAiBC,IAAqI,IAApI,cAACC,EAAa,iBAAEC,EAAgB,oBAAElF,EAAmB,cAAEmF,EAAa,kBAAEpF,EAAiB,MAAEqF,EAAK,YAAEC,EAAW,gBAAEC,GAAiBN,EAEpJ,MAAO3F,EAASkG,IAAc3T,EAAAA,EAAAA,UAAS,CACrC,CAAE8E,KAAM,GAAIrB,MAAO,GAAIuP,MAAO,OAEzBY,EAAcC,IAAmB7T,EAAAA,EAAAA,WAAS,IAC1C8T,EAAaC,IAAgB/T,EAAAA,EAAAA,WAAS,GAEvCgU,EAAYA,KAChBL,EAAWM,GAAW,IAAIA,EAAS,CAAEnP,KAAM,GAAIrB,MAAO,GAAIuP,MAAO,MAEjE,IAAIkB,EAA6BzG,EAAQ/H,OAAOyI,EAAkBzI,OAElEgO,GAAgB,GAChBK,GAAa,GACTP,GAASU,EAA6B,GAAKX,GAC7CM,GAAgB,IAIdM,EAAc/U,gBACZuU,EAAWM,GACRA,EAAQ5E,OAAO,CAAC+E,EAAGrD,IAAMA,IAAM,IAExCiD,IACAH,GAAgB,GAChBE,GAAa,IAGTM,EAAgBlM,IACpBwL,EAAWM,GACFA,EAAQ5E,OAAO,CAAC+E,EAAGrD,IAAMA,IAAM5I,IAGxC,IAAImM,EAAkB7G,EAAQ/H,OAASyI,EAAkBzI,OAAS,EAE9D8N,GAASc,EAAkBf,IAC7BG,GAAgB,GAChBG,GAAgB,IAGdL,GAA4B,IAAnB/F,EAAQ/H,QACnBqO,GAAa,IAkFXQ,EAAuBA,KAC3B,IAAI/T,EAAQvB,SAASwB,eAAe,mBACxB,OAARD,IACFA,EAAME,MAAMC,QAAU,OACtB2S,GAAiB,KA+DfkB,EAAiBpV,UACrB,IAAI2R,EAAI,EACJ0D,GAAW,EACXxC,EAAY,GACZF,EAAa,GACbxC,EAAY,GACZmF,EAAc,GACdC,EAAc,GACdC,EAAe,GACfC,GAAY,EAEhB,KAAO9D,EAAItD,EAAQ/H,QAAQ,CACzBuM,EAAYhT,SAASwB,eAAe,QAAQsQ,GAC5CgB,EAAa9S,SAASwB,eAAe,SAASsQ,GAC9C2D,EAAczV,SAASwB,eAAe,UAAUsQ,GAChD4D,EAAc1V,SAASwB,eAAe,UAAUsQ,GAEhD,IAAItN,EAAQgK,EAAQsD,GAAGtN,MAAM2O,OAC7B7C,EAAY9B,EAAQsD,GAAGjM,KAAKsN,OAC5BwC,EAAenH,EAAQsD,GAAGiC,MAAMZ,OAEhCsC,EAAYhU,MAAMC,QAAU,OAC5BgU,EAAYjU,MAAMC,QAAU,OAC5BsR,EAAUD,MAAQ,GAClBC,EAAU/P,UAAUS,OAAO,mBAC3BoP,EAAWC,MAAQ,GACnBD,EAAW7P,UAAUS,OAAO,mBAEhB,KAARc,GACFsO,EAAWC,MAAQ,oBACnBD,EAAW7P,UAAUC,IAAI,mBACzBwS,EAAYjU,MAAMC,QAAU,QAC5BgU,EAAYrC,UAAY,oBACxBmC,GAAW,GACD,eAAe7O,KAAKnC,KAC9BsO,EAAWC,MAAQ,uBACnBD,EAAW7P,UAAUC,IAAI,mBACzBwS,EAAYjU,MAAMC,QAAU,QAC5BgU,EAAYrC,UAAY,uBACxBmC,GAAW,GAGM,KAAfG,IACF7C,EAAWC,MAAQ4C,EACnB7C,EAAW7P,UAAUC,IAAI,mBACzBsS,GAAW,GAGG,KAAZlF,IACF0C,EAAUD,MAAQ,wBAClBC,EAAU/P,UAAUC,IAAI,mBACxBuS,EAAYpC,UAAY,wBACxBoC,EAAYhU,MAAMC,QAAU,QAC5B8T,GAAW,GAGRA,IACHI,EAAYpH,EAAQ4B,OAAO,SAASC,GAClC,OAASA,EAAK7L,MAAM2O,SAAS3O,CAC/B,GAEIoR,EAAUnP,OAAO,IACnBqM,EAAWC,MAAQ,kBACnBD,EAAW7P,UAAUC,IAAI,mBACzBwS,EAAYrC,UAAY,kBACxBqC,EAAYjU,MAAMC,QAAU,QAC5B8T,GAAW,IAIf1D,GACF,CAEA,OAAO0D,GAUT,YAPoBnU,IAAhB+S,IAA+C,IAAlBA,GAtJLyB,MAC1B,IAAItU,EAAQvB,SAASwB,eAAe,mBACxB,OAARD,IACFA,EAAME,MAAMC,QAAU,UAoJxBmU,QAEkBxU,IAAhB+S,IAA+C,IAAlBA,GAC/BkB,KAIAnW,EAAAA,EAAAA,KAAAH,EAAAA,SAAA,CAAAF,UACFK,EAAAA,EAAAA,KAAA,OAAKyR,GAAG,kBAAkB3R,UAAU,iBAAgBH,UACnDK,EAAAA,EAAAA,KAAA,OAAKN,MAAM,kCAAiCC,UACvCF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,0BAAyBH,SAAA,EACtCK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uDAAsDH,SAAC,iBAGtEK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wCAAuCH,UACpDK,EAAAA,EAAAA,KAAA,QAAMN,MAAM,QAAQgE,QAASA,IAAKyS,IAAuBxW,SAAC,YAG9DF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,mBAAkBH,SAAA,EACjCK,EAAAA,EAAAA,KAAA,KAAAL,SAAG,UAAS,wEAEZF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,GAAEH,SAAA,EACfF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,wCAAuCH,SAAA,EACpDK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,6CAA4CH,SAAC,eAG5DK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uDAAsDH,SAAC,aAIvE0P,aAAO,EAAPA,EAASvF,IAAI,CAAC6M,EAAK5M,KACpBtK,EAAAA,EAAAA,MAAA,OAAKK,UAAU,6BAA4BH,SAAA,EACzCF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,wCAAuCH,SAAA,EACpDK,EAAAA,EAAAA,KAAA,SAAOwD,KAAK,OAAOiO,GAAI,QAAQ1H,EAAOA,MAAOA,EAAOjK,UAAU,6DAA6D2G,YAAY,kBAAkBhD,MAAOkT,EAAIjQ,KACpKM,SAAWC,IACTsO,EAAWqB,IACT,MAAMC,EAASD,EAAErH,QAGjB,OAFAsH,EAAO9M,GAAOrD,KAAOO,EAAEF,OAAOtD,MAtI3B0Q,EAAClN,EAAG8C,KACvB,IAAIoH,EAAYlK,EAAEF,OAAOtD,MAAMuQ,OAC3BL,EAAa9S,SAASwB,eAAe,SAAS0H,GAAOtG,MACrD6S,EAAczV,SAASwB,eAAe,UAAU0H,GAEpC,KAAZoH,GAA+B,KAAbwC,GACpB1M,EAAEF,OAAO6M,MAAQ,wBACjB3M,EAAEF,OAAOjD,UAAUC,IAAI,mBACvBuS,EAAYhU,MAAMC,QAAU,QAC5B+T,EAAYpC,UAAY,0BAExBjN,EAAEF,OAAO6M,MAAQ,GACjB3M,EAAEF,OAAOjD,UAAUS,OAAO,mBAC1B+R,EAAYhU,MAAMC,QAAU,OAC5B+T,EAAYpC,UAAY,KAyHVC,CAAalN,EAAG8C,GACT8M,KAGXjD,MAAM,MAEN5T,EAAAA,EAAAA,KAAA,OAAKN,MAAM,eAAe+R,GAAI,UAAU1H,QAE1CtK,EAAAA,EAAAA,MAAA,OAAKK,UAAU,wCAAuCH,SAAA,EACpDK,EAAAA,EAAAA,KAAA,SAAOwD,KAAK,OAAOiO,GAAI,SAAS1H,EAAOA,MAAOA,EAAOjK,UAAU,6DAA6D2G,YAAY,cAAchD,MAAOkT,EAAItR,MACjK2B,SAAWC,IACTsO,EAAWqB,IACT,MAAMC,EAASD,EAAErH,QAGjB,OAFAsH,EAAO9M,GAAO1E,MAAQ4B,EAAEF,OAAOtD,MA3L3B8D,EAACN,EAAG8C,KACxB,IAAI1E,EAAQ4B,EAAEF,OAAOtD,MACjBqT,GAAU,EACVjD,EAAYhT,SAASwB,eAAe,QAAQ0H,GAC5CoH,EAAY0C,EAAUpQ,MAAMuQ,OAC5BsC,EAAczV,SAASwB,eAAe,UAAU0H,GAChDwM,EAAc1V,SAASwB,eAAe,UAAU0H,GAEjC,KAAf1E,EAAM2O,QACR/M,EAAEF,OAAO6M,MAAQ,oBACjB3M,EAAEF,OAAOjD,UAAUC,IAAI,mBACvBwS,EAAYjU,MAAMC,QAAU,QAC5BgU,EAAYrC,UAAY,oBACxB4C,GAAU,GACD,eAAetP,KAAKnC,KAC7B4B,EAAEF,OAAO6M,MAAQ,uBACjB3M,EAAEF,OAAOjD,UAAUC,IAAI,mBACvBwS,EAAYjU,MAAMC,QAAU,QAC5BgU,EAAYrC,UAAY,uBACxB4C,GAAU,GAGRA,IACF7P,EAAEF,OAAO6M,MAAQ,GACjB3M,EAAEF,OAAOjD,UAAUS,OAAO,mBAC1B+R,EAAYhU,MAAMC,QAAU,OAC5B+T,EAAYpC,UAAY,GACxBqC,EAAYjU,MAAMC,QAAU,OAC5BgU,EAAYrC,UAAY,IAGtB4C,GAAuB,KAAZ3F,IACb0C,EAAUD,MAAQ,wBAClBC,EAAU/P,UAAUC,IAAI,mBACxBuS,EAAYhU,MAAMC,QAAU,QAC5B+T,EAAYpC,UAAY,0BAyJV3M,CAAcN,EAAG8C,GACV8M,KAGXjD,MAAM,MAEN5T,EAAAA,EAAAA,KAAA,OAAKN,MAAM,eAAe+R,GAAI,UAAU1H,QAE1C/J,EAAAA,EAAAA,KAAA,OAAKF,UAAU,yBAAwBH,UACrCK,EAAAA,EAAAA,KAAA,UAAQ0D,QAASA,IAAMuS,EAAalM,GAAOpK,UACzCK,EAAAA,EAAAA,KAAA,QAAAL,UAAMK,EAAAA,EAAAA,KAAC+W,EAAAA,IAAO,CAACjX,UAAU,4CAMjCL,EAAAA,EAAAA,MAAA,OAAKK,WAAcsV,IAAUI,EAAe,cAAkBH,EAA8B,kBAAhB,eAA5D,oDAAkJ1V,SAAA,CACjK6V,GAAgBH,IACbrV,EAAAA,EAAAA,KAAA,SACEwD,KAAK,SACLC,MAAM,WACN3D,UAAU,wFACV4D,QAASA,IAAMkS,MAGpBF,IACC1V,EAAAA,EAAAA,KAAA,SAAOwD,KAAK,SAASC,MAAM,cAAc3D,UAAU,oGAAoG4D,QAASA,IA9OjJsT,MACvB,GAAI3H,EAAQ/H,OAAO,EAAE,CACnB,IAAI2P,EAAY5H,EAAQ/H,OAAS,EACjC2O,EAAagB,EACf,GA0O6KD,QAGtKtB,IACC1V,EAAAA,EAAAA,KAAA,OAAKF,UAAU,aAAYH,UACzBK,EAAAA,EAAAA,KAAA,SAAOwD,KAAK,SAASC,MAAM,aAAa3D,UAAU,sGAAsG4D,QAASA,IAjT1J1C,WAEgBqO,EAAQ/H,OAAOyI,EAAkBzI,OACnC6N,EAC7BpT,IAAAA,MAAa,8CAA8CoT,EAAc,mBAItDiB,MAOjB/G,EAAQ/H,QAAQ,EAClB6O,KAIFtV,SAASgD,cAAc,qBAAqBC,UAAUC,IAAI,UAE1DC,EAAAA,EAAMC,KATI,kDASM,CACdoL,QAAS6H,KAAKC,UAAU9H,IACvB,CAAEnL,QAAS,CAAE,eAAgB,uCAAyCC,KAAK,SAASC,GACrF,IAAIC,EAASD,EAAI1G,KAIjB,GAFAmD,SAASgD,cAAc,qBAAqBC,UAAUS,OAAO,UAE1DF,EAAOC,QACR,GAAIoQ,MAAMC,QAAQtQ,EAAO3G,OAAS2G,EAAO3G,KAAK4J,OAAS,EACrD,IAAI,IAAIqL,EAAI,EAAGA,EAAItO,EAAO3G,KAAK4J,OAAQqL,IAAK,CAC1C,IAAI5I,EAAQ1F,EAAO3G,KAAKiV,GAAG5I,MACvB4J,EAAa9S,SAASwB,eAAe,SAAS0H,GAClD4J,EAAWC,MAAQvP,EAAO3G,KAAKiV,GAAGiC,MAClCjB,EAAW7P,UAAUC,IAAI,mBAEzB,IAAIwS,EAAc1V,SAASwB,eAAe,UAAU0H,GACpDwM,EAAYjU,MAAMC,QAAU,QAC5BgU,EAAYrC,UAAY7P,EAAO3G,KAAKiV,GAAGiC,KACzC,MAEA7S,IAAAA,QAAe,iBACfoU,IACAnG,IACA+F,SAGFhU,IAAAA,MAAa,yBACboU,IACAnG,IACA+F,GAGJ,GAAGlB,MAAM,SAAUD,GACjBuB,IACAtV,SAASgD,cAAc,qBAAqBC,UAAUS,OAAO,UAC7DxC,IAAAA,MAAa,mDACf,MAuPgLqV,iBAU9KjF,EAAekF,IAAqD,IAApD,eAAEnF,EAAc,MAAE7M,EAAK,oBAAE2K,GAAqBqH,EAClE,MAAOtP,EAAaC,IAAkBpG,EAAAA,EAAAA,WAAS,GAIzCqG,EAAaA,KACjBD,GAAe,IAwBjB,OACEvI,EAAAA,EAAAA,MAAAI,EAAAA,SAAA,CAAAF,SAAA,EACEK,EAAAA,EAAAA,KAAA,UACAF,UAAW,wGACX4D,QAhC0B4T,KAC5BtP,GAAe,IA+BkBrI,SAC9B,YAGDK,EAAAA,EAAAA,KAACwC,EAAAA,EAAU,CAACC,QAAM,EAACC,KAAMqF,EAAapF,GAAIC,EAAAA,SAASjD,UACjDF,EAAAA,EAAAA,MAACoD,EAAAA,EAAM,CAACF,GAAG,MAAM7C,UAAU,gBAAgBgD,QAASmF,EAAWtI,SAAA,EAC7DK,EAAAA,EAAAA,KAACwC,EAAAA,EAAWO,MAAK,CACfJ,GAAIC,EAAAA,SACJI,MAAM,wBACNC,UAAU,YACVC,QAAQ,cACRC,MAAM,uBACNC,UAAU,cACVC,QAAQ,YAAW1D,UAEnBK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,4CAGjBE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,gCAA+BH,UAC5CK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,8DAA6DH,UAC1EK,EAAAA,EAAAA,KAACwC,EAAAA,EAAWO,MAAK,CACfJ,GAAIC,EAAAA,SACJI,MAAM,wBACNC,UAAU,qBACVC,QAAQ,wBACRC,MAAM,uBACNC,UAAU,wBACVC,QAAQ,qBAAoB1D,UAE5BF,EAAAA,EAAAA,MAACoD,EAAAA,EAAOS,MAAK,CAACxD,UAAU,qHAAoHH,SAAA,EAC1IK,EAAAA,EAAAA,KAAC6C,EAAAA,EAAOU,MAAK,CACXZ,GAAG,KACH7C,UAAU,sDAAqDH,SAChE,mBAGDK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,OAAMH,UACnBF,EAAAA,EAAAA,MAAA,KAAGK,UAAU,4EAA2EH,SAAA,EACtFK,EAAAA,EAAAA,KAACuX,EAAAA,IAAa,CAACzX,UAAU,qCAAoC,oCAAgCE,EAAAA,EAAAA,KAAA,UAAAL,SAAS0F,IAAe,kBAIzH5F,EAAAA,EAAAA,MAAA,OAAKK,UAAU,kBAAiBH,SAAA,EAC9BK,EAAAA,EAAAA,KAAA,UACEwD,KAAK,SACL1D,UAAU,8IACV4D,QAvEEoD,IACpBjG,SAASgD,cAAc,qBAAqBC,UAAUC,IAAI,UAC1DkE,IAEAjE,EAAAA,EAAMC,KAAK,qDAA6D,CACtEiO,eAAgBA,EAChBoC,aAAcjP,GACb,CAAEnB,QAAS,CAAE,eAAgB,uCAAyCC,KAAK,SAASC,GACrF,IAAIC,EAASD,EAAI1G,KACjB,GAAG2G,EAAOC,QAIR,OAHAzD,SAASgD,cAAc,qBAAqBC,UAAUS,OAAO,UAC7DxC,IAAAA,QAAe,wBACfiO,IAGFnP,SAASgD,cAAc,qBAAqBC,UAAUS,OAAO,UAC1DF,EAAO3G,MAAMqE,IAAAA,MAAasC,EAAO3G,KAAKgH,IAC3C,IAsDwC/E,SACvB,mBAGDK,EAAAA,EAAAA,KAAA,UACEwD,KAAK,SACL1D,UAAU,yOACV4D,QAASuE,EAAWtI,SACrB,+BAcfsS,EAAuBuF,IAAgC,IAA/B,eAAEtF,EAAc,MAAE7M,GAAOmS,EACrD,MAAOzP,EAAaC,IAAkBpG,EAAAA,EAAAA,WAAS,GAIzCqG,EAAaA,KACjBD,GAAe,IAuBjB,OACEvI,EAAAA,EAAAA,MAAAI,EAAAA,SAAA,CAAAF,SAAA,EACEK,EAAAA,EAAAA,KAAA,UACAF,UAAW,wGACX4D,QA/B0B4T,KAC5BtP,GAAe,IA8BkBrI,SAC9B,qBAGDK,EAAAA,EAAAA,KAACwC,EAAAA,EAAU,CAACC,QAAM,EAACC,KAAMqF,EAAapF,GAAIC,EAAAA,SAASjD,UACjDF,EAAAA,EAAAA,MAACoD,EAAAA,EAAM,CAACF,GAAG,MAAM7C,UAAU,gBAAgBgD,QAASmF,EAAWtI,SAAA,EAC7DK,EAAAA,EAAAA,KAACwC,EAAAA,EAAWO,MAAK,CACfJ,GAAIC,EAAAA,SACJI,MAAM,wBACNC,UAAU,YACVC,QAAQ,cACRC,MAAM,uBACNC,UAAU,cACVC,QAAQ,YAAW1D,UAEnBK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,4CAGjBE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,gCAA+BH,UAC5CK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,8DAA6DH,UAC1EK,EAAAA,EAAAA,KAACwC,EAAAA,EAAWO,MAAK,CACfJ,GAAIC,EAAAA,SACJI,MAAM,wBACNC,UAAU,qBACVC,QAAQ,wBACRC,MAAM,uBACNC,UAAU,wBACVC,QAAQ,qBAAoB1D,UAE5BF,EAAAA,EAAAA,MAACoD,EAAAA,EAAOS,MAAK,CAACxD,UAAU,qHAAoHH,SAAA,EAC1IK,EAAAA,EAAAA,KAAC6C,EAAAA,EAAOU,MAAK,CACXZ,GAAG,KACH7C,UAAU,8CAA6CH,SACxD,qBAGDK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,OAAMH,UACnBF,EAAAA,EAAAA,MAAA,KAAGK,UAAU,oCAAmCH,SAAA,CAAC,4DACSK,EAAAA,EAAAA,KAAA,UAAAL,SAAS0F,IAAe,UAIpF5F,EAAAA,EAAAA,MAAA,OAAKK,UAAU,kBAAiBH,SAAA,EAC9BK,EAAAA,EAAAA,KAAA,UACEwD,KAAK,SACL1D,UAAU,8IACV4D,QAtEIoD,IACtBjG,SAASgD,cAAc,qBAAqBC,UAAUC,IAAI,UAC1DkE,IAEAjE,EAAAA,EAAMC,KAAK,0DAAkE,CAC3EiO,eAAgBA,EAChBoC,aAAcjP,GACb,CAAEnB,QAAS,CAAE,eAAgB,uCAAyCC,KAAK,SAASC,GACrF,IAAIC,EAASD,EAAI1G,KACjB,GAAG2G,EAAOC,QAGR,OAFAvC,IAAAA,QAAe,2BACflB,SAASgD,cAAc,qBAAqBC,UAAUS,OAAO,UAG/D1D,SAASgD,cAAc,qBAAqBC,UAAUS,OAAO,UAC1DF,EAAO3G,MAAMqE,IAAAA,MAAasC,EAAO3G,KAAKgH,IAC3C,IAsD0C/E,SACzB,qBAGDK,EAAAA,EAAAA,KAAA,UACEwD,KAAK,SACL1D,UAAU,yOACV4D,QAASuE,EAAWtI,SACrB,+BAcrB,EAv7EA,WACE,MAAOoQ,EAAmB0H,IAAwB7V,EAAAA,EAAAA,UAAS,KACpDqK,EAAYyL,IAAiB9V,EAAAA,EAAAA,UAAS,KACtCuK,EAAcwL,IAAmB/V,EAAAA,EAAAA,UAAS,IAC1CsK,EAAiB0L,IAAsBhW,EAAAA,EAAAA,UAAS,IAChD2K,EAAiBsL,IAAsBjW,EAAAA,EAAAA,UAAS,IAChD4K,EAAoBsL,IAAyBlW,EAAAA,EAAAA,UAAS,IACtD6K,EAAiBsL,IAAsBnW,EAAAA,EAAAA,UAAS,IAChD8K,EAAesL,IAAoBpW,EAAAA,EAAAA,UAAS,IAC5C+K,EAAkBsL,IAAuBrW,EAAAA,EAAAA,UAAS,IAClDgL,EAAqBsL,IAA0BtW,EAAAA,EAAAA,UAAS,IACxDwL,EAAoB+K,IAAyBvW,EAAAA,EAAAA,UAAS,IACtDyL,EAAuB+K,IAA4BxW,EAAAA,EAAAA,UAAS,IAG5DkL,EAAYuL,IAAiBzW,EAAAA,EAAAA,UAAS,IACtCmL,EAAiBuL,IAAsB1W,EAAAA,EAAAA,UAAS,IAChDoL,EAAwBuL,KAA6B3W,EAAAA,EAAAA,UAAS,IAC9DqL,GAAUuL,KAAe5W,EAAAA,EAAAA,UAAS,IAClCsL,GAAeuL,KAAoB7W,EAAAA,EAAAA,UAAS,IAC5CuL,GAAsBuL,KAA2B9W,EAAAA,EAAAA,UAAS,IAE1DwK,GAAauM,KAAkB/W,EAAAA,EAAAA,UAAS,KACxCmD,GAAW6T,KAAgBhX,EAAAA,EAAAA,UAAS,iBACpCyK,GAAkBwM,KAAuBjX,EAAAA,EAAAA,WAAS,IAClDkX,GAAoB/F,KAAyBnR,EAAAA,EAAAA,WAAS,IACtDoR,GAAmB+F,KAAwBnX,EAAAA,EAAAA,UAAS,KAEpDqT,GAAeC,KAAoBtT,EAAAA,EAAAA,WAAS,IAC5CoX,GAAmBrQ,KAAwB/G,EAAAA,EAAAA,WAAS,IACpDqX,GAAsBC,KAA2BtX,EAAAA,EAAAA,WAAS,IAC1DuX,GAAiBC,KAAsBxX,EAAAA,EAAAA,UAAS,KAChDyX,GAA4BC,KAAiC1X,EAAAA,EAAAA,UAAS,KACtE2X,GAAiBC,KAAsB5X,EAAAA,EAAAA,UAAS,KAChD0K,GAAamN,KAAkB7X,EAAAA,EAAAA,UAAS,KACxCiL,GAAiB6M,KAAsB9X,EAAAA,EAAAA,UAAS,KAChDqH,GAAS0Q,KAAc/X,EAAAA,EAAAA,UAAS,KAEhCP,GAAwBC,KAA6BM,EAAAA,EAAAA,WAAS,IAC9DL,GAAoBqH,KAAyBhH,EAAAA,EAAAA,WAAS,IACtDJ,GAAcqH,KAAmBjH,EAAAA,EAAAA,WAAS,IAC1CH,GAAeqH,KAAoBlH,EAAAA,EAAAA,WAAS,IAC5CwT,GAAOwE,KAAYhY,EAAAA,EAAAA,WAAS,IAC5ByT,GAAaC,KAAmB1T,EAAAA,EAAAA,WAAS,IAEzCiY,GAAcC,KAAmBlY,EAAAA,EAAAA,UAAS,KAC1CmY,GAAYC,KAAiBpY,EAAAA,EAAAA,UAAS,KACtCqY,GAAaC,KAAkBtY,EAAAA,EAAAA,UAAS,IAEzC0D,IAAO2J,EAAAA,EAAAA,MA+Gb,IA7GAtO,EAAAA,EAAAA,WAAU,KACRoB,IAAAA,QAAiB,CACfC,cAAe,qBAEhB,KAEHrB,EAAAA,EAAAA,WAAU,KAER,IAAIwZ,GAAepa,EAAAA,EAAAA,IAAU,qBAEVmC,IAAfiY,GAA2C,KAAfA,GAC9B3V,WAAW,YACT4V,EAAAA,EAAAA,IAAU,eAAe,IACzBrY,IAAAA,MAAaoY,EACf,EAAG,MAEJ,KAGHxZ,EAAAA,EAAAA,WAAU,KAMR,QAJauB,IAAToD,IAAkC,eAAZA,GAAKjG,OAC7BuF,EAAkBU,GAAK+U,YACvBxB,IAAoB,SAET3W,IAAToD,GAAmB,CACrB,IAAIgV,EAAiD,OAA5BhV,GAAKgV,mBAA8B,GAAKhV,GAAKgV,mBACtEd,GAAmBc,GACnB3C,EAAgBrS,GAAKiV,YACrB5B,GAAerT,GAAKkV,SACpBf,GAAenU,GAAKjG,MACpBqa,GAAmBpU,GAAK0E,WACxB2P,GAAWrU,GAAKmV,UAEoB,QAAhCpa,OAAOqa,UAAUC,YAAyB/B,GAAa,UAC7D,KAA2C,QAAhCvY,OAAOqa,UAAUC,YAC1B/B,GAAa,YAEd,CAACtT,MAEJ3E,EAAAA,EAAAA,WAAU,KACUK,WAChB,IAAI4Z,QAAmBnS,IACvBgP,EAAqBmD,IAEvBC,GAEA9Y,IAAAA,QAAiB,CACfC,cAAe,qBAGhB,KAEHrB,EAAAA,EAAAA,WAAU,KACYK,WAClB,IAAI8Z,QAsGR9Z,iBACE,MAAM+Z,QAAiB/W,EAAAA,EAAMC,KAAK,4CAAoD,CACpF,IAAMlE,EAAAA,EAAAA,IAAU,WACf,CAAEmE,QAAS,CAAE,eAAgB,uCAE1BG,EAAS0W,EAASrd,KAAOqd,EAASrd,KAAO,GAC/C,OAAG2G,EAAOC,QACDD,EAEA,EAEX,CAjHuB2W,GACnB/U,QAAQC,IAAI,SAAS4U,QACH5Y,IAAd4Y,EAAOpd,OACTga,EAAcR,KAAK+D,MAAMH,EAAOpd,OAChCka,EAAmBkD,EAAOnN,OAC1BkK,EAAmBiD,EAAOI,mBAC1BpD,EAAsBgD,EAAOK,wBAC7BpD,EAAmB+C,EAAOM,mBAC1BpD,EAAiB8C,EAAOO,iBACxBpD,EAAoB6C,EAAOQ,oBAC3BpD,EAAuB4C,EAAOS,wBAC9BlD,EAAcyC,EAAOU,aACrBlD,EAAmBwC,EAAOW,mBAC1BlD,GAA0BuC,EAAOY,4BACjClD,GAAYsC,EAAOa,WACnBlD,GAAiBqC,EAAOc,iBACxBlD,GAAwBoC,EAAOe,0BAC/B1D,EAAsB2C,EAAOgB,sBAC7B1D,EAAyB0C,EAAOiB,4BAGpCC,IACC,KAEHrb,EAAAA,EAAAA,WAAU,KAOiB,IAAtBgE,EAAW2C,QANKtG,WACjB,MAAM+Z,QAyFV/Z,iBACE,MAAM+Z,QAAiB/W,EAAAA,EAAMC,KAAK,wCAAgD,CAChF,IAAMlE,EAAAA,EAAAA,IAAU,WACf,CAAEmE,QAAS,CAAE,eAAgB,uCAE1BG,EAAS0W,EAASrd,KACxB,OAAG2G,EAAOC,QACDD,EAAO3G,KAEP,EAEX,CApG2Bue,GAEvB/B,GAAea,IAGWmB,IAE3B,KAEHvb,EAAAA,EAAAA,WAAU,KAER,QAAmBuB,IAAhB+X,GAA0B,CAC3B,MAAMkC,EAAc,CAAC,gBAAiB,YAAa,WAE3BC,MACtB,MAEMC,EAAYpC,GAAYqC,KAAMpL,GAClCiL,EAAYI,SAAwBrL,EAAKvF,MAHL5O,cAAcM,QAAQ,OAAQ,MAKpEuc,GAAS4C,QAAQH,KAGnBD,EACF,GAEA,CAACnC,UAES/X,IAAToD,GAAoB,OACvB,IAAY,IAATA,GAED,YADAjF,OAAOC,SAASC,KAAO,UAIzB,MAAMkc,GAAmBC,IACvB9D,GAAa8D,IA+BT1M,GAAsBhP,UAC1B,IAAI4Z,QAAmBnS,IACvBgP,EAAqBmD,IAmCvB,OADA+B,EAAAA,EAAAA,OAEEld,EAAAA,EAAAA,MAAAI,EAAAA,SAAA,CAAAF,SAAA,EACEF,EAAAA,EAAAA,MAACmd,EAAAA,EAAM,CAAAjd,SAAA,EACLK,EAAAA,EAAAA,KAAA,SAAAL,SAAO,6BACPK,EAAAA,EAAAA,KAAA,QAAM0G,KAAK,cAAc+L,QAAQ,6JAEnCzS,EAAAA,EAAAA,KAAC6c,EAAAA,QAAM,CAACvX,KAAMA,GAAMwU,gBAAiBA,GAAiBE,cAAeA,GAAerR,qBAAsBA,MAC1G3I,EAAAA,EAAAA,KAAA,OAAKF,UAAU,qDAAoDH,UACjEK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uCAAsCH,UACnDF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,qCAAoCH,SAAA,EACjDK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,+CAA8CH,SAAC,yBAG7DK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,GAAEH,UACfF,EAAAA,EAAAA,MAAA,MAAIK,UAAU,oCAAmCH,SAAA,CAC5B,KAAlB4Z,IACDvZ,EAAAA,EAAAA,KAAC6E,EAAO,CACRC,QAAQ,eACRC,UAAWA,GACXrB,QAAS+Y,GAAgB9c,SACxB,iBAGG,GAEH0M,IAAsC,KAAlBkN,IACrBvZ,EAAAA,EAAAA,KAAC6E,EAAO,CACRC,QAAQ,UACRC,UAAWA,GACXrB,QAAS+Y,GAAgB9c,SACxB,YAGG,GAEe,KAAlB4Z,IACDvZ,EAAAA,EAAAA,KAAC6E,EAAO,CACNC,QAAQ,QACRC,UAAWA,GACXrB,QAAS+Y,GAAgB9c,SAC1B,YAGG,IAEJK,EAAAA,EAAAA,KAAC6E,EAAO,CACNC,QAAQ,cACRC,UAAWA,GACXrB,QAAS+Y,GAAgB9c,SAC1B,iBAGDK,EAAAA,EAAAA,KAAC6E,EAAO,CACNC,QAAQ,UACRC,UAAWA,GACXrB,QAAS+Y,GAAgB9c,SAC1B,aAGDK,EAAAA,EAAAA,KAAC6E,EAAO,CACNC,QAAQ,OACRC,UAAWA,GACXrB,QAAS+Y,GAAgB9c,SAC1B,UAGDK,EAAAA,EAAAA,KAAC6E,EAAO,CACNC,QAAQ,SACRC,UAAWA,GACXrB,QAAS+Y,GAAgB9c,SAC1B,iBAMLF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,2FAA0FH,SAAA,CACxF,iBAAdoF,KAAgC/E,EAAAA,EAAAA,KAAC0I,EAAsB,CAACC,qBAAsBA,GAAsBrH,0BAA2BA,GAA2BsH,sBAAuBA,GAAuBC,gBAAiBA,GAAiBC,iBAAkBA,GAAkBC,SAAUgR,GAAY/Q,kBAAmB6Q,GAAc5Q,QAASA,KAChU,YAAdlE,KAA2B/E,EAAAA,EAAAA,KAAC4P,EAAiB,CAACE,oBA9I/BA,KAC1B,MAAMgN,EAAiBlY,EAAkBmL,EAAkBzI,OAEvDyI,EAAkBzI,QAAU1C,EAC1BmL,EAAkBzI,SAAWyV,OAAOnY,IAAoBwQ,GAC1DrT,IAAAA,MAAa,0BAEbuT,IAAgB,GAChB3M,IAAqB,IAEdoH,EAAkBzI,OAAS,GAAK8N,IACzCF,IAAiB,GACbnF,EAAkBzI,OAAS1C,EAC7B0Q,IAAgB,GAGdA,GADqB,IAAnBwH,IAON5H,IAAiB,IAwHgFnF,kBAAmBA,EAAmBC,oBAAqBA,GAAqBC,0BA9GnJA,CAAC6I,EAAoB9G,KACrDe,IAAsB,GACtBgG,GAAqB/G,IA4GoN1F,YAAaA,GAAaO,gBAAiBA,KAC3P,UAAd9H,KAAyB/E,EAAAA,EAAAA,KAACwL,EAAe,CAAC7G,WAAYsV,KACxC,gBAAdlV,KAA+B/E,EAAAA,EAAAA,KAAC+L,EAAiB,CAACE,WAAYA,EAAYC,gBAAiBA,EAAiBC,aAAcA,EAAcC,YAAaA,GAAaC,iBAAkBA,GAAkBC,YAAaA,GAAaC,gBAAiBA,EAAiBC,mBAAoBA,EAAoBnN,KAAMiG,GAAKjG,KAAMoN,gBAAiBA,EAAiBC,cAAeA,EAAeC,iBAAkBA,EAAkBC,oBAAqBA,EAAqBC,gBAAiBA,GAAiBC,WAAYA,EAAYC,gBAAiBA,EAAiBC,uBAAwBA,EAAwBC,SAAUA,GAAUC,cAAeA,GAAeC,qBAAsBA,GAAsBC,mBAAoBA,EAAoBC,sBAAuBA,IACpuB,YAAdtI,KAA2B/E,EAAAA,EAAAA,KAACiF,EAAiB,CAACK,KAAMA,KACtC,SAAdP,KAAwB/E,EAAAA,EAAAA,KAACqO,EAAc,IACzB,WAAdtJ,KAA0B/E,EAAAA,EAAAA,KAACsO,EAAa,eAMjDtO,EAAAA,EAAAA,KAAC4S,EAAe,CAACE,eAAgBgG,GAAoB/F,sBAAuBA,GAAuBC,kBAAmBA,GAAmBhD,oBAAqBA,MAC9JhQ,EAAAA,EAAAA,KAAC+U,EAAc,CAACE,cAAeA,GAAeC,iBAAkBA,GAAkBlF,oBAAqBA,GAAqBmF,cAAevQ,EAAiBmL,kBAAmBA,EAAmBqF,MAAOA,GAAOC,YAAaA,GAAaC,gBAAiBA,MAC3PtV,EAAAA,EAAAA,KAACgd,EAAAA,EAAkB,CAAChE,kBAAmBA,GAAmBrQ,qBAAsBA,GAAsByQ,mBAAoBA,GAAoBE,8BAA+BA,GAAgCJ,wBAAyBA,MACtOlZ,EAAAA,EAAAA,KAACid,EAAAA,EAAsB,CAAC9D,gBAAiBA,GAAiBE,2BAA4BA,GAA4BH,wBAAyBA,GAAyBD,qBAAsBA,MAC1LjZ,EAAAA,EAAAA,KAACoB,EAAkB,CAACC,uBAAwBA,GAAwBC,0BAA2BA,GAA2BC,mBAAoBA,GAAoBC,aAAcA,GAAcC,cAAeA,MAC7MzB,EAAAA,EAAAA,KAACkd,EAAAA,QAAM,CAAC5X,KAAMA,OAGpB,C,2JC3VI6X,EAAmB,IACnBrL,EAAU,GAEP,SAASkL,EAAkB5d,GAA0H,IAAxH,kBAAC4Z,EAAiB,qBAAErQ,EAAoB,mBAAEyQ,EAAkB,8BAAEE,EAA6B,wBAAEJ,GAAwB9Z,EACvJ,MAAOge,EAAcC,IAAmBzb,EAAAA,EAAAA,UAAS,IAC1C0b,EAAaC,IAAkB3b,EAAAA,EAAAA,UAAS,IACxC4b,EAAiBC,IAAsB7b,EAAAA,EAAAA,UAAS,IAEjD0D,GAAO2J,EAAAA,EAAAA,OAEbtO,EAAAA,EAAAA,WAAU,UACKuB,IAAToD,GAAkC,eAAZA,EAAKjG,OAC7B8d,EAAmB7X,EAAK6X,iBAEU,YAA9B7X,EAAK/F,SAASxC,cAChB0gB,EAAmB,SAEnBA,EAAmB,UAItB,CAACnY,IAGJ,MAOMoY,EAA2BA,KAC/B,IAAItb,EAAQvB,SAASwB,eAAe,uBACxB,OAARD,IACFA,EAAME,MAAMC,QAAU,OACtBoG,GAAqB,MAczBhI,EAAAA,EAAAA,WAAU,KACRoB,IAAAA,QAAiB,CACfC,cAAe,qBAEhB,KAEHrB,EAAAA,EAAAA,WAAU,KAER4c,EADaH,EAAeD,IAE3B,CAACC,KAEJzc,EAAAA,EAAAA,WAAU,KACR0c,EAAgB,GAGhBE,EADaH,EAAeD,IAG3B,CAACnE,IAoBJ,YAPwB9W,IAApB8W,IAAuD,IAAtBA,GAvDL2E,MAC9B,IAAIvb,EAAQvB,SAASwB,eAAe,uBACxB,OAARD,IACFA,EAAME,MAAMC,QAAU,UAqDxBob,QAEsBzb,IAApB8W,IAAuD,IAAtBA,GACnC0E,KAIA1d,EAAAA,EAAAA,KAAAH,EAAAA,SAAA,CAAAF,UACFK,EAAAA,EAAAA,KAAA,OAAKyR,GAAG,sBAAsB3R,UAAU,iBAAgBH,UACvDK,EAAAA,EAAAA,KAAA,OAAKN,MAAM,yDAAwDC,UAC9DF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,0BAAyBH,SAAA,EACtCK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uDAAsDH,SAAC,sBAGtEK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wCAAuCH,UACpDK,EAAAA,EAAAA,KAAA,QAAMN,MAAM,QAAQgE,QAASA,IAAKga,IAA2B/d,SAAC,YAGlEF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,gDAA+CH,SAAA,EAC5DK,EAAAA,EAAAA,KAAA,OAAAL,SAAK,gEACLK,EAAAA,EAAAA,KAAA,OAAAL,SAAK,kDACLF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,mBAAkBH,SAAA,EAC/BK,EAAAA,EAAAA,KAAA,QAAMF,UAAU,uDAAsDH,SAAC,SACvEF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,kCAAiCH,SAAA,EAC9CK,EAAAA,EAAAA,KAAA,SAAOF,UAAU,8DAA8D0D,KAAK,SAASC,MAAM,IAAIC,QAASA,KA7DxH0Z,EAAa,GACfC,EAAgBD,EAAa,OA6DnBpd,EAAAA,EAAAA,KAAA,QAAMF,UAAU,sCAAqCH,SAAEyd,KACvDpd,EAAAA,EAAAA,KAAA,SAAOF,UAAU,8DAA8D0D,KAAK,SAASC,MAAM,IAAIC,QAASA,KAnE5H2Z,EAAgBD,EAAa,UAqEnBpd,EAAAA,EAAAA,KAAA,QAAMF,UAAU,uDAAsDH,SAAC,iBAEzEF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,oBAAmBH,SAAA,CAAC,kBACjB2d,MAElB7d,EAAAA,EAAAA,MAAA,OAAKK,UAAU,oBAAmBH,SAAA,CAAC,OAC5B6d,MAEPxd,EAAAA,EAAAA,KAAA,OAAAL,UACEK,EAAAA,EAAAA,KAACmI,EAAAA,EAAOC,OAAM,CACdtI,UAAU,oFACV4R,WAAY,CAAEC,gBAAiB,WAC/BtJ,SAAU,CAAEC,MAAO,IACnB5E,QAASA,KArDnBrD,OAAOC,SAASC,KAAO,gBAAgB6c,GAqDKzd,SACjC,6BAUf,CAEO,SAASsd,EAAsB/c,GAAiG,IAA/F,gBAACiZ,EAAe,2BAAEE,EAA0B,wBAAEH,EAAuB,qBAAED,GAAqB/Y,EAElI,MAAMoF,GAAO2J,EAAAA,EAAAA,OAEbtO,EAAAA,EAAAA,WAAU,UACKuB,IAAToD,GAAkC,eAAZA,EAAKjG,OAC7ByS,EAAUxM,EAAKwM,UAEhB,CAACxM,IAEJ,MAOMsY,EAAaA,KACjB,IAAIxb,EAAQvB,SAASwB,eAAe,iBACxB,OAARD,IACFA,EAAME,MAAMC,QAAU,OACtB2W,GAAwB,KA8C5B,YAP2BhX,IAAvB+W,IAA6D,IAAzBA,GAlDtB4E,MAChB,IAAIzb,EAAQvB,SAASwB,eAAe,iBACxB,OAARD,IACFA,EAAME,MAAMC,QAAU,UAgDxBsb,QAEyB3b,IAAvB+W,IAA6D,IAAzBA,GACtC2E,KAIA5d,EAAAA,EAAAA,KAAAH,EAAAA,SAAA,CAAAF,UACAK,EAAAA,EAAAA,KAAA,OAAKyR,GAAG,gBAAgB3R,UAAU,iBAAgBH,UAChDF,EAAAA,EAAAA,MAAA,OAAKC,MAAM,4EAA2EC,SAAA,EACpFK,EAAAA,EAAAA,KAAA,QAAMN,MAAM,QAAQgE,QAASA,IAAKka,IAAaje,SAAC,OAChDK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,sCAAqCH,UAACK,EAAAA,EAAAA,KAAA,OAAKe,IAAK+c,EAAAA,EAAW5P,IAAI,cAAcpO,UAAU,yBACtGL,EAAAA,EAAAA,MAAA,MAAIK,UAAU,qEAAoEH,SAAA,CAAC,mBAAeK,EAAAA,EAAAA,KAAA,SAAK,2BACvGA,EAAAA,EAAAA,KAAA,OAAKF,UAAU,yCAAwCH,SAAC,kEACxDK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,yCAAwCH,SAAC,sFACxDF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,OAAMH,SAAA,EACnBF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,wBAAuBH,SAAA,CAAC,mBAAiBwZ,MACxD1Z,EAAAA,EAAAA,MAAA,OAAKK,UAAU,wBAAuBH,SAAA,CAAC,wBAAsB0Z,SAE/D5Z,EAAAA,EAAAA,MAAA,OAAKK,UAAU,+DAA8DH,SAAA,EAC3EK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWH,SAAC,sBAC3BK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,4EAA4E4D,QAASA,KAAKqa,OAnD3G1O,EAAU8J,EACV6E,EAAe3E,EAGnBuE,IACA/c,SAASgD,cAAc,qBAAqBC,UAAUC,IAAI,eAE1DC,EAAAA,EAAMC,KALI,2DAKM,CACdoL,UACA2O,gBACC,CAAE9Z,QAAS,CAAE,eAAgB,uCAAyCC,KAAK,SAASC,GACrF,IAAIC,EAASD,EAAI1G,KAEjBmD,SAASgD,cAAc,qBAAqBC,UAAUS,OAAO,UAE1DF,EAAOC,QACRvC,IAAAA,QAAe,iBAAiBsC,EAAO3G,MAEvCqE,IAAAA,MAAa,gBAEjB,GAAG8S,MAAM,SAAUD,GACjB/T,SAASgD,cAAc,qBAAqBC,UAAUS,OAAO,UACzDqQ,EAAMmG,UAAoC,MAAxBnG,EAAMmG,SAASlR,SACnChJ,SAASgD,cAAc,qBAAqBC,UAAUS,OAAO,UAC7DxC,IAAAA,MAAa,wDAEjB,GA3BmBgc,IACf1O,EACA2O,GAkD0Hre,SAAC,oBACzHF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EAAKK,EAAAA,EAAAA,KAAA,QAAMF,UAAU,YAAWH,SAAC,iBAAmB,uCACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EAAKK,EAAAA,EAAAA,KAAA,QAAMF,UAAU,YAAWH,SAAC,WAAa,gBAC9CF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EAAKK,EAAAA,EAAAA,KAAA,QAAMF,UAAU,YAAWH,SAAC,eAAiB,uBAClDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EAAKK,EAAAA,EAAAA,KAAA,QAAMF,UAAU,YAAWH,SAAC,oBAAsB,iBACvDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EAAKK,EAAAA,EAAAA,KAAA,QAAMF,UAAU,YAAWH,SAAC,yCAA2C,iBAC5EF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EAAKK,EAAAA,EAAAA,KAAA,QAAMF,UAAU,YAAWH,SAAC,oBAAsB,sBACvDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EAAKK,EAAAA,EAAAA,KAAA,QAAMF,UAAU,YAAWH,SAAC,qBAAuB,IAAEmS,MAC1DrS,EAAAA,EAAAA,MAAA,OAAKK,UAAU,kDAAiDH,SAAA,EAACK,EAAAA,EAAAA,KAACuL,EAAAA,IAAY,CAACzL,UAAU,wBAAuB,0HAGlHE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,8CAA6CH,SAAC,8IAG7DF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,yCAAwCH,SAAA,CAAC,iBACzCK,EAAAA,EAAAA,KAAA,KAAAL,SAAG,qBAAoB,kDAEtCK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,cAAaH,UAC1BK,EAAAA,EAAAA,KAACmI,EAAAA,EAAOC,OAAM,CACZtI,UAAU,sEACV4R,WAAY,CAAEC,gBAAiB,WAC/BtJ,SAAU,CAAEC,MAAO,IACnB5E,QA/EuBua,KAC/B5d,OAAOC,SAASC,KAAO,sBA8EmBZ,SACnC,sCAQX,C", "sources": ["core/utils/main.jsx", "footer/index.jsx", "modal/manage.jsx", "manage-account/index.jsx", "modal/enterprise.jsx"], "sourcesContent": ["import { GetCookie } from '../../core/utils/cookies';\r\nexport function getCurrency(currency) {\r\n    if(!currency) return \"\";\r\n    if(currency.toLowerCase() === 'usd') return \"$\";\r\n    if(currency.toLowerCase() === 'eur') return \"€\";\r\n    if(currency.toLowerCase() === 'gbp') return \"£\";\r\n    if(currency.toLowerCase() === 'brl') return \"R$\";\r\n    if(currency.toLowerCase() === 'sar') return \"R$\";\r\n    return \"\";\r\n}\r\n\r\nexport function getCountry(currency) {\r\n    if(!currency) return \"\";\r\n    if(currency.toLowerCase() === 'usd') return \"US\";\r\n    if(currency.toLowerCase() === 'eur') return \"US\";\r\n    if(currency.toLowerCase() === 'gbp') return \"GB\";\r\n    if(currency.toLowerCase() === 'brl') return \"US\";\r\n    if(currency.toLowerCase() === 'sar') return \"AE\";\r\n    if(currency.toLowerCase() === 'chf') return \"CH\";\r\n    if(currency.toLowerCase() === 'sek') return \"SE\";\r\n    return \"US\";\r\n}\r\n\r\nexport function formatDate(dateStr) {\r\n    if(!dateStr) return \"\";\r\n    const month = [\"January\",\"February\",\"March\",\"April\",\"May\",\"June\",\"July\",\"August\",\"September\",\"October\",\"November\",\"December\"];\r\n    const date = new Date(dateStr.replace(/ /g,\"T\"));\r\n\r\n    return month[date.getMonth()] + \" \" + date.getDate() + \" \" + date.getFullYear();\r\n}\r\n\r\nexport function getPrice(data) {\r\n    if(data.trial_price) return data.trial_price;\r\n    if(data.price) return data.price;\r\n    return \"0\";\r\n}\r\n\r\nexport function numberWithCommas(x) {\r\n    return x.toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, \",\");\r\n}\r\n\r\nexport function formatNumber(number) {\r\n  const floatNumber = parseFloat(number);\r\n  return (floatNumber % 1 === 0)\r\n      ? numberWithCommas(floatNumber.toFixed(0))\r\n      : numberWithCommas(floatNumber.toFixed(2)); \r\n}\r\n\r\nexport function getPricePlan(currency, price) {\r\n    if(!currency || !price) return \"\";\r\n    // Check if price is a number\r\n    if(isNaN(price)) return \"\";\r\n    // Check if price is positive number\r\n    if(parseFloat(price) >= 0) {\r\n        if(currency.toLowerCase() === 'sar') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"ر.س.\";\r\n        } else if(currency.toLowerCase() === 'aed') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"AED\";\r\n        } else if(currency.toLowerCase() === 'chf') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"CHF\";\r\n        } else if(currency.toLowerCase() === 'sek') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"SEK\";\r\n        } else if(currency.toLowerCase() === 'pln') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"zł\";\r\n        }else if(currency.toLowerCase() === 'ron') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"LEI\";\r\n        }else if(currency.toLowerCase() === 'czk') {\r\n            return \"Kč\" + formatNumber(price).toLocaleString(\"en-US\");\r\n        } else if(currency.toLowerCase() === 'huf') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"Ft\";\r\n        } else if(currency.toLowerCase() === 'dkk') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"kr.\";\r\n        } else if(currency.toLowerCase() === 'bgn') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"лв\";\r\n        } else if(currency.toLowerCase() === 'try') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"₺\";\r\n        }\r\n        return getCurrency(currency) + formatNumber(price).toLocaleString(\"en-US\");\r\n    }\r\n    // Negative number\r\n    return \"-\" + getCurrency(currency) + (formatNumber(price) * -1).toLocaleString(\"en-US\");\r\n}\r\n\r\nexport function diffMin(dt1, dt2)\r\n{\r\n    dt1 = new Date(dt1);\r\n    dt2 = new Date(dt2);\r\n    var diff =(dt2.getTime() - dt1.getTime()) / 1000;\r\n    diff /= 60;\r\n    return Math.abs(Math.round(diff));\r\n}\r\n\r\nexport function formatDateTime(dateStr) {\r\n    if(!dateStr) return \"\";\r\n    const month = [\"January\",\"February\",\"March\",\"April\",\"May\",\"June\",\"July\",\"August\",\"September\",\"October\",\"November\",\"December\"];\r\n    const date = new Date(dateStr.replace(/ /g,\"T\"));\r\n\r\n    return month[date.getMonth()] + \" \" + date.getDate() + \" \" + date.getFullYear() + \" \" + date.getHours() + \":\" + date.getMinutes();\r\n}\r\n\r\nexport function DailyPrice({plan}) {\r\n  let dailyPrice = '';\r\n  let interval = '';\r\n  if (plan.payment_interval === 'Yearly') {\r\n    dailyPrice = getPricePlan(plan.currency, parseFloat(plan.price / 365).toFixed(2));\r\n    interval = <div class=\"text-xs mb-4\">billed {getPricePlan(plan.currency, plan.price)}/year</div>;\r\n  }\r\n\r\n  if (plan.payment_interval === 'Monthly') {\r\n    dailyPrice = getPricePlan(plan.currency, parseFloat(plan.price / 30).toFixed(2));\r\n    interval = <div class=\"text-xs mb-4\">billed {getPricePlan(plan.currency, plan.price)}/Month</div>;\r\n  }\r\n\r\n  if (plan.trial_price) {\r\n    dailyPrice = getPricePlan(plan.currency, parseFloat(plan.trial_price / plan.trial_days).toFixed(2));\r\n    interval = <div class=\"text-xs mb-4\">billed {getPricePlan(plan.currency, plan.trial_price)}</div>;\r\n  }\r\n\r\n  return (\r\n    <>\r\n      <p className={`text-4xl font-bold text-gray-800 ${(GetCookie(\"p_toggle\") === '') ? 'mb-4' : ''}`}>\r\n        {dailyPrice} <span className=\"text-sm\"> per Day</span>\r\n      </p>\r\n      {interval}\r\n    </>\r\n  );\r\n}\r\n\r\nexport function PriceFormatted({plan}) {\r\n  if (GetCookie(\"daily\") === 'on') {\r\n    return DailyPrice({plan});\r\n  } \r\n  if (plan.trial_price) {\r\n    return (<p className=\"text-4xl font-bold text-gray-800 mb-4\">{getPricePlan(plan.currency, plan.trial_price)}</p>)\r\n  } \r\n  return (\r\n    <p className=\"text-4xl font-bold text-gray-800 mb-4\">\r\n      {getPricePlan(plan.currency, plan.price)}\r\n      <span className=\"text-sm\"> \r\n        /{ plan.payment_interval === \"Monthly\" ? \"month\" : \"year\" }\r\n      </span>\r\n    </p>\r\n  )\r\n}\r\n\r\nexport function displayTextFormatted(plan) {\r\n  let payment_interval = plan.payment_interval === 'Yearly' ?  365 : 30;\r\n  let trialPricePer = `\r\n    then only ${getPricePlan(plan.currency, plan.price)} per month\r\n  `;\r\n\r\n  if (GetCookie(\"daily\") === 'on') {\r\n    trialPricePer = `\r\n      then only ${getPricePlan(plan.currency, parseFloat(plan.price / payment_interval).toFixed(2))} per day<br>\r\n      (billed ${getPricePlan(plan.currency, plan.price)} ${plan.payment_interval})\r\n    `;\r\n  }\r\n\r\n  return plan.display_txt2\r\n        .replace(\"{trialDays}\", plan.trial_days)\r\n        .replace(\"{trialPricePer}\", trialPricePer);\r\n  \r\n}\r\n\r\nexport function getPrefixLocation() {\r\n  const currentLocation = window.location.href;\r\n\r\n  if (currentLocation.indexOf(\"staging\") > -1) {\r\n      return \"staging.\";\r\n  } else if (currentLocation.indexOf(\"dev\") > -1) {\r\n      return \"dev.\";\r\n  }\r\n\r\n  return \"\"\r\n}", "import React, { useEffect } from 'react';\r\n\r\nfunction Powered() {\r\n  // const [isHidden, setIsHidden] = useState(false);\r\n\r\n  const base_url = process.env.REACT_APP_BASE_URL;\r\n  useEffect(() => {\r\n    const script = document.createElement('script');\r\n    script.src = base_url + 'snippets/com.global.vuzo/js/com.global.vuzo.js';\r\n    script.async = true;\r\n    script.onload = () => {\r\n      // window.displayGooglePlay();\r\n      // window.displayQR();\r\n    };\r\n    document.body.appendChild(script);\r\n  }, [base_url]);\r\n  return (\r\n    <>\r\n      <footer className={`relative hidden md:block md:absolute bottom-[10px] right-[5px] w-full mt-3 py-12 md:py-2 z-[8888] md:hidden`}>\r\n      {/* <footer className=\"relative hidden md:block md:absolute bottom-[10px] right-[5px] w-full mt-3 py-12 md:py-2 z-[8888]\"> */}\r\n\r\n      </footer>\r\n    </>\r\n  );\r\n}\r\n\r\nexport default Powered;\r\n", "import { useEffect, useState, Fragment } from 'react';\r\nimport { GetCookie } from '../core/utils/cookies';\r\nimport axios from 'axios';\r\nimport toastr from 'toastr';\r\nimport 'toastr/build/toastr.min.css';\r\nimport { Dialog, Transition } from '@headlessui/react';\r\n\r\nexport function PausedAccountModal ({showPausedAccountModal, setShowPausedAccountModal, userSubscriptionID, userMerchant, userAccountID}) {\r\n  const [billingCycle, setbillingCycle] = useState(1);\r\n  const [buttonDisable, setButtonDisable] = useState(false);\r\n\r\n  useEffect(() => {\r\n    toastr.options = {\r\n      positionClass: 'toast-top-center'\r\n    };\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    if (billingCycle>=5){\r\n      setButtonDisable(true);\r\n    }else{\r\n      setButtonDisable(false);\r\n    }\r\n  }, [billingCycle]);\r\n\r\n\r\n  const modalPausedAccountOpen = () => {\r\n    let modal = document.getElementById(\"modalPausedAccount\");\t\t\r\n    if (modal!==null){\r\n      modal.style.display = \"block\";\r\n      setShowPausedAccountModal(true);\r\n    }\r\n  }\r\n\r\n  const modalPausedAccountClose = () => {\r\n    // let modal = document.getElementById(\"modalPausedAccount\");\t\t\r\n    // if (modal!==null){\r\n    //   modal.style.display = \"none\";\r\n    //   setShowPausedAccountModal(false);\r\n    // }\r\n    setShowPausedAccountModal(false);\r\n  }\r\n\r\n  const handlePlus = () => {\r\n    setbillingCycle(billingCycle+1);\r\n  }\r\n\r\n  const handleMinus = () => {\r\n    if (billingCycle>1){\r\n      setbillingCycle(billingCycle-1);\r\n    }\r\n  }\r\n\r\n\r\n  const handlePause = () => {\r\n    modalPausedAccountClose();\r\n    document.querySelector(\".loader-container\").classList.add('active');\r\n\r\n    axios.post(`${process.env.REACT_APP_API_URL}/pause-subscription`, {\r\n      'tk': GetCookie('access'),\r\n      'subscription_id': userSubscriptionID,\r\n      'merchant': userMerchant,\r\n      'billing_cycle' : billingCycle,\r\n      'account_pid' : userAccountID,\r\n    }, { headers: { 'content-type': 'application/x-www-form-urlencoded' } }).then(function(res){\r\n      let output = res.data;\r\n      if(output.success) {\r\n        document.querySelector(\".loader-container\").classList.remove('active');\r\n        toastr.success('Success!<br> Your subscription is now paused.');\r\n        setTimeout(function(){ \r\n          window.location.reload();          \r\n        }, 1000);        \r\n      } else {\r\n        document.querySelector(\".loader-container\").classList.remove('active');\r\n        toastr.error(output.data.msg);\r\n      }  \r\n    });\r\n  }\r\n  \r\n  if (showPausedAccountModal!==undefined && showPausedAccountModal === true){\r\n    modalPausedAccountOpen();\r\n  }\r\n  if (showPausedAccountModal!==undefined && showPausedAccountModal === false){\r\n    modalPausedAccountClose();\r\n  }\r\n\r\n  return (\r\n    <>\r\n\r\n<Transition appear show={showPausedAccountModal} as={Fragment}>\r\n        <Dialog as=\"div\" className=\"relative z-10\" onClose={()=> modalPausedAccountClose()}>\r\n          <Transition.Child\r\n            as={Fragment}\r\n            enter=\"ease-out duration-300\"\r\n            enterFrom=\"opacity-0\"\r\n            enterTo=\"opacity-100\"\r\n            leave=\"ease-in duration-200\"\r\n            leaveFrom=\"opacity-100\"\r\n            leaveTo=\"opacity-0\"\r\n          >\r\n            <div className=\"fixed inset-0 bg-black bg-opacity-25\" />\r\n          </Transition.Child>\r\n\r\n          <div className=\"fixed inset-0 overflow-y-auto\">\r\n            <div className=\"flex min-h-full items-center justify-center p-4 text-center\">\r\n              <Transition.Child\r\n                as={Fragment}\r\n                enter=\"ease-out duration-300\"\r\n                enterFrom=\"opacity-0 scale-95\"\r\n                enterTo=\"opacity-100 scale-100\"\r\n                leave=\"ease-in duration-200\"\r\n                leaveFrom=\"opacity-100 scale-100\"\r\n                leaveTo=\"opacity-0 scale-95\"\r\n              >\r\n                <Dialog.Panel className=\"w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all\">\r\n                  <Dialog.Title\r\n                    as=\"h3\"\r\n                    className=\"text-lg font-medium leading-6 text-gray-900\"\r\n                  >\r\n                    Pause Subscription\r\n                  </Dialog.Title>\r\n                  <div className=\"mt-2\">\r\n                    <div className=\"text-sm text-gray-500\">\r\n                    You're about to pause your subscription. Please select how many billing cycles you'd like it to be paused.\r\n                    </div>\r\n                    <div className='text-center'>\r\n                      <input className=\"bg-gray-400 rounded px-3 py-2 m-2 text-white cursor-pointer\" type=\"button\" value=\"-\" onClick={()=>handleMinus()}/>\r\n                      <span className=\"text-black p-2 mx-auto font-bold\">{billingCycle}</span>\r\n                      <input className=\"bg-gray-400 rounded px-3 py-2 m-2 text-white cursor-pointer\" type=\"button\" value=\"+\" onClick={()=>handlePlus()} disabled={buttonDisable}></input>\r\n                    </div>\r\n                  </div>\r\n\r\n                  <div className=\"mt-4 text-right\">\r\n                    <button\r\n                      type=\"button\"\r\n                      className=\"inline-flex justify-center rounded-md border border-transparent bg-red-500 px-4 py-2 mr-2 text-sm font-medium text-white hover:bg-[#2563eb]\"\r\n                      onClick={handlePause}\r\n                    >\r\n                      Confirm\r\n                    </button>\r\n                    <button\r\n                      type=\"button\"\r\n                      className=\"inline-flex justify-center rounded-md border border-transparent bg-gray-100 px-4 py-2 text-sm font-medium text-black hover:bg-gray-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-gray-500 focus-visible:ring-offset-2\"\r\n                      onClick={()=> modalPausedAccountClose()}\r\n                    >\r\n                      Close\r\n                    </button>\r\n                  </div>\r\n                </Dialog.Panel>\r\n              </Transition.Child>\r\n            </div>\r\n          </div> \r\n        </Dialog>\r\n      </Transition>\r\n    </>\r\n  );\r\n}\r\n\r\n", "import React, { Fragment, useEffect, useState } from 'react';\r\nimport './style.css';\r\nimport { motion } from \"framer-motion\";\r\nimport Header from '../header';\r\nimport Footer from '../footer';\r\nimport { Auth } from '../core/utils/auth';\r\nimport { Get<PERSON><PERSON>ie, RemoveCookie, SetCookie } from '../core/utils/cookies';\r\nimport { AiFillWarning } from \"react-icons/ai\";\r\nimport { FaInfoCircle, FaTrash, FaRegCopy } from 'react-icons/fa';\r\nimport axios from 'axios';\r\nimport { getPricePlan, formatDate, formatDateTime, diffMin } from '../core/utils/main';\r\nimport { Dialog, Menu, Transition } from '@headlessui/react';\r\nimport { Helmet } from 'react-helmet';\r\nimport { install } from \"resize-observer\";\r\nimport toastr from 'toastr';\r\nimport 'toastr/build/toastr.min.css';\r\nimport { AddMoreMemberModal } from '../modal/enterprise';\r\nimport { MemberCompletePurchase } from '../modal/enterprise';\r\nimport { PausedAccountModal } from '../modal/manage';\r\nimport imgWarning from '../assets/images/warning-icon.png';\r\n\r\nvar user_order = [];\r\nvar ent_max_members = 0;\r\n\r\nfunction Manage() {\r\n  const [enterpriseMembers, setEnterpriseMembers] = useState([]);\r\n  const [tokenUsage, setTokenUsage] = useState([]);\r\n  const [userMaxToken, setUserMaxToken] = useState(0);\r\n  const [tokenUsageTotal, setTokenUsageTotal] = useState(0);\r\n  const [gpt4oTotalToken, setGpt4oTotalToken] = useState(0);\r\n  const [gpt4oMaxTokenUsage, setGpt4oMaxTokenUsage] = useState(0);\r\n  const [dalleTotalImage, setDalleTotalImage] = useState(0);\r\n  const [dalleMaxImage, setDalleMaxImage] = useState(0);\r\n  const [claudeTotalToken, setClaudeTotalToken] = useState(0);\r\n  const [claudeMaxTokenUsage, setClaudeMaxTokenUsage] = useState(0);\r\n  const [deepseekTotalToken, setDeepseekTotalToken] = useState(0);\r\n  const [deepseekMaxTokenUsage, setDeepseekMaxTokenUsage] = useState(0);\r\n\r\n  // Enterprise Plan Owner (Team Max, Office Max, Enterprise Max)\r\n  const [fluxPrompt, setFluxPrompt] = useState(0);\r\n  const [fluxTotalPrompt, setFluxTotalPrompt] = useState(0);\r\n  const [membersTotalFluxPrompt, setMembersTotalFluxPrompt] = useState(0);\r\n  const [o1Prompt, seto1Prompt] = useState(0);\r\n  const [o1TotalPrompt, seto1TotalPrompt] = useState(0);\r\n  const [membersTotalo1Prompt, setMembersTotalo1Prompt] = useState(0);\r\n\r\n  const [currentDate, setCurrentDate] = useState(\"\");\r\n  const [activeTab, setActiveTab] = useState('subscription');\r\n  const [isShowEnterprise, setisShowEnterprise] = useState(false);\r\n  const [showEditEnterprise, setShowEditEnterprise] = useState(false);\r\n  const [editMemberDetails, setEditMemberDetails] = useState([]);\r\n\r\n  const [showAddMember, setshowAddMember] = useState(false);\r\n  const [showAddMoreMember, setshowAddMoreMember] = useState(false);\r\n  const [showCompletePurchase, setShowCompletePurchase] = useState(false);\r\n  const [moreToAddMember, setMoreToAddMember] = useState(\"\");\r\n  const [moreToAddMemberTotalAmount, setMoreToAddMemberTotalAmount] = useState(\"\");\r\n  const [entParentUserID, setEntParentUserID] = useState(\"\");\r\n  const [currentPlan, setCurrentPlan] = useState(\"\");\r\n  const [currentPlanName, setCurrentPlanName] = useState(\"\");\r\n  const [userPpg, setUserPpg] = useState(\"\");\r\n\r\n  const [showPausedAccountModal, setShowPausedAccountModal] = useState(false);\r\n  const [userSubscriptionID, setUserSubscriptionID] = useState(false);\r\n  const [userMerchant, setUserMerchant] = useState(false);\r\n  const [userAccountID, setUserAccountID] = useState(false);\r\n  const [isMax, setIsMax] = useState(false);\r\n  const [isVisbleBtn, setIsVisibleBtn] = useState(true);\r\n\r\n  const [subscription, setSubscription] = useState([]);\r\n  const [getDateNow, setGetDateNow] = useState(\"\");\r\n  const [getOrderNow, setGetOrderNow] = useState([]);\r\n\r\n  const auth = Auth();\r\n\r\n  useEffect(() => {\r\n    toastr.options = {\r\n      positionClass: 'toast-top-center'\r\n    };\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    //if error occured in 3d secure\r\n    let threed_error = GetCookie('threed_error');\r\n\r\n    if (threed_error!==undefined && threed_error!==''){\r\n      setTimeout(function(){ \r\n        SetCookie('threed_error','');\r\n        toastr.error(threed_error);\r\n      }, 3000);      \r\n    }\r\n  }, []);\r\n\r\n\r\n  useEffect(() => {\r\n    \r\n    if (auth !== undefined && auth.plan==='enterprise'){\r\n      ent_max_members = auth.max_members;\r\n      setisShowEnterprise(true);\r\n    }\r\n    if (auth !== undefined){\r\n      let ent_parent_user_id = auth.ent_parent_user_id === null ? '' : auth.ent_parent_user_id;\r\n      setEntParentUserID(ent_parent_user_id)\r\n      setUserMaxToken(auth.max_tokens);\r\n      setCurrentDate(auth.max_end);\r\n      setCurrentPlan(auth.plan);\r\n      setCurrentPlanName(auth.plan_name);\r\n      setUserPpg(auth.user_ppg);\r\n\r\n      if (window.view_data.active_tab !== 'mem' ) { setActiveTab('account'); }\r\n    } else if (window.view_data.active_tab === 'mem' ) {\r\n      setActiveTab('members');\r\n    }\r\n  }, [auth]);\r\n\r\n  useEffect(() => {\r\n    const fetchData = async () => {\r\n      let entMembers = await getEnterpriseMembers();\r\n      setEnterpriseMembers(entMembers);\r\n    }\r\n    fetchData();\r\n\r\n    toastr.options = {\r\n      positionClass: 'toast-top-center'\r\n    };\r\n\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    const fetchTokens = async () => {\r\n      let tokens = await getTokenUsage();\r\n      console.log(\"tokens\",tokens)\r\n      if (tokens.data!==undefined){\r\n        setTokenUsage(JSON.parse(tokens.data))\r\n        setTokenUsageTotal(tokens.total)\r\n        setGpt4oTotalToken(tokens.gpt4o_total_token)\r\n        setGpt4oMaxTokenUsage(tokens.gpt_4o_max_token_usage)\r\n        setDalleTotalImage(tokens.dalle_total_image)\r\n        setDalleMaxImage(tokens.dalle_max_image)\r\n        setClaudeTotalToken(tokens.claude_total_token);\r\n        setClaudeMaxTokenUsage(tokens.claude_max_token_limit);\r\n        setFluxPrompt(tokens.flux_prompt)\r\n        setFluxTotalPrompt(tokens.flux_total_prompt)\r\n        setMembersTotalFluxPrompt(tokens.members_total_flux_prompts)\r\n        seto1Prompt(tokens.o1_prompt)\r\n        seto1TotalPrompt(tokens.o1_total_prompt)\r\n        setMembersTotalo1Prompt(tokens.members_total_o1_prompts)\r\n        setDeepseekTotalToken(tokens.deepseek_total_token)\r\n        setDeepseekMaxTokenUsage(tokens.deepseek_max_token_usage)\r\n      }\r\n    }\r\n    fetchTokens();\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    const fetchOrder = async () => {\r\n      const response = await getOrders()\r\n\r\n      setGetOrderNow(response);\r\n      }\r\n      \r\n    if(user_order.length === 0) fetchOrder()\r\n\r\n  }, [])\r\n\r\n  useEffect(() => {\r\n    \r\n    if(getOrderNow !== undefined){\r\n      const targetPlans = ['enterprisemax', 'officemax', 'teammax'];\r\n\r\n      const checkEntMaxPlan = () => {\r\n        const formatPlanName = (name) => name.toLowerCase().replace(/\\s+/g, '');\r\n    \r\n        const user_plan = getOrderNow.find((item) =>\r\n          targetPlans.includes(formatPlanName(item.label))\r\n        );\r\n        setIsMax(Boolean(user_plan));\r\n      }\r\n\r\n      checkEntMaxPlan()\r\n    }\r\n\r\n  },[getOrderNow])\r\n\r\n  if(auth === undefined) return;\r\n  if(auth === false) {\r\n    window.location.href = '/login';\r\n    return;\r\n  }\r\n\r\n  const handleTabChange = (tab) => {\r\n    setActiveTab(tab);\r\n  };\r\n\r\n\r\n  const handleShowAddMember = () => {\r\n    const remainingSlots = ent_max_members - enterpriseMembers.length;\r\n    \r\n    if (enterpriseMembers.length >= ent_max_members) {\r\n      if (enterpriseMembers.length === Number(ent_max_members) && isMax) {\r\n        toastr.error(\"Member limit reached.\");\r\n      } else {\r\n        setIsVisibleBtn(false);\r\n        setshowAddMoreMember(true);\r\n      }\r\n    } else if (enterpriseMembers.length > 0 && isMax) {\r\n      setshowAddMember(true);\r\n      if (enterpriseMembers.length > ent_max_members) {\r\n        setIsVisibleBtn(false);\r\n      } else {\r\n        if (remainingSlots === 1) {\r\n          setIsVisibleBtn(false);\r\n        }else{\r\n          setIsVisibleBtn(true);\r\n        }\r\n      }\r\n    } else {\r\n      setshowAddMember(true);\r\n    }\r\n  };\r\n  \r\n\r\n  const handleReloadMembers = async () => {\r\n    let entMembers = await getEnterpriseMembers();\r\n    setEnterpriseMembers(entMembers);\r\n  }\r\n\r\n  const handleShowEditMemberModal = (showEditEnterprise, params) => {\r\n    setShowEditEnterprise(true);\r\n    setEditMemberDetails(params)\r\n  }\r\n\r\n  async function getTokenUsage() {\r\n    const response = await axios.post(`${process.env.REACT_APP_API_URL}/get-token-usage`, {\r\n      'tk': GetCookie('access')\r\n    }, { headers: { 'content-type': 'application/x-www-form-urlencoded' } })\r\n\r\n    const output = response.data ? response.data : [];\r\n    if(output.success) {\r\n      return output;\r\n    } else {\r\n      return [];\r\n    }\r\n  }\r\n\r\n  async function getOrders() {\r\n    const response = await axios.post(`${process.env.REACT_APP_API_URL}/get-payment`, {\r\n      'tk': GetCookie('access')\r\n    }, { headers: { 'content-type': 'application/x-www-form-urlencoded' } })\r\n\r\n    const output = response.data;\r\n    if(output.success) {\r\n      return output.data;\r\n    } else {\r\n      return [];\r\n    }\r\n  }\r\n\r\n  install();\r\n  return (\r\n    <>\r\n      <Helmet>\r\n        <title>AI Pro | Manage Account</title>\r\n        <meta name=\"description\" content=\"Take control of your account with ease through our My Account page. Review billing details, update contact information, and tailor your preferences.\" />\r\n      </Helmet>\r\n      <Header auth={auth} setSubscription={setSubscription} setGetDateNow={setGetDateNow} setshowAddMoreMember={setshowAddMoreMember} />\r\n      <div className=\"Manage bg-gray-100 min-h-[500px] flex items-center\">\r\n        <div className=\"container mx-auto py-10 px-4 sm:px-0\">\r\n          <div className=\"max-w-6xl mx-auto pt-8 pb-8 sm:p-8\">\r\n            <h1 className=\"text-xl font-bold text-blue-600 my-6 lg:my-8\">\r\n              Manage Your Account\r\n            </h1>\r\n            <div className=\"\">\r\n              <ul className=\"flex flex-wrap text-xs md:text-sm\">\r\n                {entParentUserID==='' ? (\r\n                <TabItem\r\n                tabName=\"subscription\"\r\n                activeTab={activeTab}\r\n                onClick={handleTabChange}\r\n                >\r\n                  Subscription\r\n                </TabItem>\r\n                ) : \"\"}\r\n\r\n                {isShowEnterprise && entParentUserID==='' ? (\r\n                <TabItem\r\n                tabName=\"members\"\r\n                activeTab={activeTab}\r\n                onClick={handleTabChange}\r\n                >\r\n                  Members\r\n                </TabItem>\r\n                ) : \"\"}\r\n\r\n                {entParentUserID==='' ? (\r\n                <TabItem\r\n                  tabName=\"order\"\r\n                  activeTab={activeTab}\r\n                  onClick={handleTabChange}\r\n                >\r\n                  Invoice\r\n                </TabItem>\r\n                ) : \"\"}\r\n\r\n                <TabItem\r\n                  tabName=\"token-usage\"\r\n                  activeTab={activeTab}\r\n                  onClick={handleTabChange}\r\n                >\r\n                  Token Usage\r\n                </TabItem>\r\n                <TabItem\r\n                  tabName=\"account\"\r\n                  activeTab={activeTab}\r\n                  onClick={handleTabChange}\r\n                >\r\n                  Account\r\n                </TabItem>\r\n                <TabItem\r\n                  tabName=\"help\"\r\n                  activeTab={activeTab}\r\n                  onClick={handleTabChange}\r\n                >\r\n                  Help\r\n                </TabItem>\r\n                <TabItem\r\n                  tabName=\"logout\"\r\n                  activeTab={activeTab}\r\n                  onClick={handleTabChange}\r\n                >\r\n                  Logout\r\n                </TabItem>\r\n              </ul>\r\n            </div>\r\n\r\n            <div className=\"bg-white drop-shadow-sm p-6 rounded-tr-md rounded-br-md rounded-bl-md min-h-[400px] flex\">\r\n              {activeTab === 'subscription' && <SubscriptionTabContent setshowAddMoreMember={setshowAddMoreMember} setShowPausedAccountModal={setShowPausedAccountModal} setUserSubscriptionID={setUserSubscriptionID} setUserMerchant={setUserMerchant} setUserAccountID={setUserAccountID} date_now={getDateNow} user_subscription={subscription} userPpg={userPpg}/>}\r\n              {activeTab === 'members' && <MembersTabContent handleShowAddMember={handleShowAddMember} enterpriseMembers={enterpriseMembers} handleReloadMembers={handleReloadMembers} handleShowEditMemberModal={handleShowEditMemberModal} currentPlan={currentPlan} currentPlanName={currentPlanName}/>}\r\n              {activeTab === 'order' && <OrderTabContent user_order={getOrderNow} />}\r\n              {activeTab === 'token-usage' && <TokenUsageContent tokenUsage={tokenUsage} tokenUsageTotal={tokenUsageTotal} userMaxToken={userMaxToken} currentDate={currentDate} isShowEnterprise={isShowEnterprise} currentPlan={currentPlan} gpt4oTotalToken={gpt4oTotalToken} gpt4oMaxTokenUsage={gpt4oMaxTokenUsage} plan={auth.plan} dalleTotalImage={dalleTotalImage} dalleMaxImage={dalleMaxImage} claudeTotalToken={claudeTotalToken} claudeMaxTokenUsage={claudeMaxTokenUsage} currentPlanName={currentPlanName} fluxPrompt={fluxPrompt} fluxTotalPrompt={fluxTotalPrompt} membersTotalFluxPrompt={membersTotalFluxPrompt} o1Prompt={o1Prompt} o1TotalPrompt={o1TotalPrompt} membersTotalo1Prompt={membersTotalo1Prompt} deepseekTotalToken={deepseekTotalToken} deepseekMaxTokenUsage={deepseekMaxTokenUsage} />}\r\n              {activeTab === 'account' && <AccountTabContent auth={auth} />}\r\n              {activeTab === 'help' && <HelpTabContent />}\r\n              {activeTab === 'logout' && <LogOutContent />}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <EditMemberModal showEditMember={showEditEnterprise} setShowEditEnterprise={setShowEditEnterprise} editMemberDetails={editMemberDetails} handleReloadMembers={handleReloadMembers} />\r\n      <AddMemberModal showAddMember={showAddMember} setshowAddMember={setshowAddMember} handleReloadMembers={handleReloadMembers} entMaxMembers={ent_max_members} enterpriseMembers={enterpriseMembers} isMax={isMax} isVisbleBtn={isVisbleBtn} setIsVisibleBtn={setIsVisibleBtn}/>\r\n      <AddMoreMemberModal showAddMoreMember={showAddMoreMember} setshowAddMoreMember={setshowAddMoreMember} setMoreToAddMember={setMoreToAddMember} setMoreToAddMemberTotalAmount={setMoreToAddMemberTotalAmount}  setShowCompletePurchase={setShowCompletePurchase} />\r\n      <MemberCompletePurchase moreToAddMember={moreToAddMember} moreToAddMemberTotalAmount={moreToAddMemberTotalAmount} setShowCompletePurchase={setShowCompletePurchase} showCompletePurchase={showCompletePurchase}/>\r\n      <PausedAccountModal showPausedAccountModal={showPausedAccountModal} setShowPausedAccountModal={setShowPausedAccountModal} userSubscriptionID={userSubscriptionID} userMerchant={userMerchant} userAccountID={userAccountID} />\r\n      <Footer auth={auth}/>\r\n    </>\r\n  );\r\n}\r\n\r\nconst TabItem = ({ tabName, activeTab, onClick, children }) => {\r\n  const isActive = activeTab === tabName;\r\n\r\n  return (\r\n    <li\r\n      className={` pb-2 cursor-pointer py-3 px-6 rounded-t-lg ${\r\n        isActive ? 'border-b-2 border-white bg-white' : ''\r\n      }`}\r\n      onClick={() => onClick(tabName)}\r\n    >\r\n      {children}\r\n    </li>\r\n  );\r\n};\r\n\r\nconst AccountTabContent = (props) => {\r\n  const [newEmail, setNewEmail] = useState(\"\");\r\n  const [email] = useState(props.auth.email);\r\n  const [oldPassword, setOldPassword] = useState(\"\");\r\n  const [newPassword, setNewPassword] = useState(\"\");\r\n  const [confPassword, setConfPassword] = useState(\"\");\r\n  const [userPid] = useState(props.auth.user_pid);\r\n  const [isSocialLogin, setisSocialLogin] = useState(false);\r\n\r\n  const handleCopy = () => {\r\n    // Copy the userPid to clipboard\r\n    navigator.clipboard.writeText(userPid);\r\n    toastr.success(\"Successfully copied\");\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (props.auth !== undefined){\r\n      console.log(\"props.auth.is_social\",props.auth.is_social)\r\n      if (props.auth.is_social !==null && props.auth.is_social!==''){\r\n        setisSocialLogin(true);\r\n      }else{\r\n        setisSocialLogin(false);\r\n      }\r\n    }\r\n  }, [props.auth]);\r\n\r\n  const validatePassword = () => {\r\n    let error_msg = \"\";\r\n    if(!oldPassword) {\r\n      error_msg = \"Current Password is required.\";\r\n    } else if (newPassword.length){\r\n      if(newPassword !== confPassword) {\r\n        error_msg = \"New Password do not match.\";\r\n      } else if (newPassword.length < 6) {\r\n        error_msg = \"New Password should be at least 6 characters.\";\r\n      }\r\n    }\r\n    return error_msg;\r\n  };\r\n\r\n  const validateEmail = () => {\r\n    let error_msg = \"\";\r\n    if (newEmail.length) {\r\n      if (!/\\S+@\\S+\\.\\S+/.test(newEmail)) {\r\n        error_msg = 'Invalid email format';\r\n      }\r\n    }\r\n    return error_msg;\r\n  };\r\n\r\n  const updateUserDetails = () => {\r\n    let error_msg = validatePassword();\r\n    if(error_msg) {\r\n      toastr.error(error_msg);\r\n      return;\r\n    }\r\n    error_msg = validateEmail();\r\n    if(error_msg) {\r\n      toastr.error(error_msg);\r\n      return;\r\n    }\r\n    document.querySelector(\".loader-container\").classList.add('active');\r\n    axios.post(`${process.env.REACT_APP_API_URL}/update-account`, {\r\n      tk: GetCookie('access'),\r\n      newemail: newEmail,\r\n      password: oldPassword,\r\n      newpassword: newPassword,\r\n      confpassword: confPassword\r\n    }, { headers: { 'content-type': 'application/x-www-form-urlencoded' } }).then(function(res) {\r\n      let output = res.data;\r\n      if(output.success) {\r\n        toastr.success(\"Success!<br> Please re-login your account.\");\r\n        setTimeout(function(){\r\n          window.location.reload();\r\n        }, 1000);\r\n        return;\r\n      }\r\n      document.querySelector(\".loader-container\").classList.remove('active');\r\n      if(output.data) toastr.error(output.data.msg);\r\n    });\r\n  };\r\n\r\n  return (\r\n    <div className=\"w-96\">\r\n      <p className=\"text-sm py-4\">You may edit your account details below:</p>\r\n      <div className=\"flex items-center\">\r\n        <label className='text-sm mr-2 font-bold font-s'>Your Account ID: {userPid}</label>\r\n        <button class=\"bg-white hover:bg-gray-100 text-gray-800 font-semibold py-2 ml-1\" onClick={handleCopy}><FaRegCopy /></button>\r\n      </div>\r\n      <div className=\"relative block mt-3\">\r\n        <div className='border-solid border-2 border-gray-100 rounded-sm'>\r\n          <div className='p-3 bg-gray-50 border-b-2 border-gray-100 sm:text-sm'>\r\n            Change Email Address\r\n          </div>\r\n          <div className='p-2 pt-1 pb-1'>\r\n            <input className=\"placeholder:italic placeholder-text-slate-400 block bg-gray-100 w-full border border-slate-300 rounded-md my-3 py-2 pl-3 pr-3 shadow-sm focus:outline-none focus:border-sky-500 focus:ring-sky-500 focus:ring-1 sm:text-sm text-gray-400\"\r\n            placeholder=\"<EMAIL>\"\r\n            type=\"email\"\r\n            name=\"email\"\r\n            readOnly\r\n            value={props.auth.email}\r\n            />\r\n            <input className=\"placeholder:italic placeholder-text-slate-400 block w-full border border-slate-300 rounded-md my-3 py-2 pl-3 pr-3 shadow-sm focus:outline-none focus:border-sky-500 focus:ring-sky-500 focus:ring-1 sm:text-sm\"\r\n            placeholder=\"New Email\"\r\n            type=\"email\"\r\n            name=\"new_email\"\r\n            autocomplete=\"new-email\"\r\n            onKeyUp={(event) => {\r\n              setNewEmail(event.target.value);\r\n            }}\r\n            onChange={(e) => setNewEmail(e.target.value)}\r\n            disabled={isSocialLogin}\r\n            />\r\n          </div>\r\n        </div>\r\n        <div className='border-solid border-2 border-gray-100 rounded-sm mt-2'>\r\n          <div className='p-3 bg-gray-50 border-b-2 border-gray-100 sm:text-sm'>\r\n            Change Password\r\n          </div>\r\n          <div className='p-2 pt-1 pb-1'>\r\n            <input className=\"placeholder:italic placeholder:text-slate-400 block bg-white w-full border border-slate-300 rounded-md my-3 py-2 pl-3 pr-3 shadow-sm focus:outline-none focus:border-sky-500 focus:ring-sky-500 focus:ring-1 sm:text-sm\"\r\n            placeholder=\"New Password\"\r\n            type=\"password\"\r\n            name=\"new_password\"\r\n            autocomplete=\"new-password\"\r\n            onKeyUp={(event) => {\r\n              setNewPassword(event.target.value);\r\n            }}\r\n            disabled={isSocialLogin}\r\n            />\r\n            <input className=\"placeholder:italic placeholder:text-slate-400 block bg-white w-full border border-slate-300 rounded-md my-3 py-2 pl-3 pr-3 shadow-sm focus:outline-none focus:border-sky-500 focus:ring-sky-500 focus:ring-1 sm:text-sm\"\r\n            placeholder=\"Confirm New Password\"\r\n            type=\"password\"\r\n            name=\"conf_password\"\r\n            onKeyUp={(event) => {\r\n              setConfPassword(event.target.value);\r\n            }}\r\n            disabled={isSocialLogin}\r\n            />\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <UpdateAccount updateUserDetails={updateUserDetails} setOldPassword={setOldPassword}></UpdateAccount>\r\n      <div className=\"text-sm text-slate-400 py-4 mt-2 ml-2\">\r\n        <DeleteAccount email={email}></DeleteAccount>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nconst UpdateAccount = (props) => {\r\n  const setOldPassword = props.setOldPassword;\r\n  const [isShowModal, setIsShowModal] = useState(false);\r\n\r\n  const showUpdateAccountModal = () => {\r\n    setIsShowModal(true);\r\n  };\r\n  const closeModal = () => {\r\n    setIsShowModal(false);\r\n  };\r\n\r\n  const proceedUpdateAccount = () => {\r\n    props.updateUserDetails();\r\n  }\r\n  return (\r\n    <>\r\n      <motion.button\r\n        className=\"bg-blue-500 mb-1 text-white font-bold py-3 px-6 my-3 rounded-lg change-password hover:bg-[#2563eb]\"\r\n        whileTap={{ scale: 0.9 }}\r\n        onClick={showUpdateAccountModal}\r\n      >\r\n        Save Changes\r\n      </motion.button>\r\n      <Transition appear show={isShowModal} as={Fragment}>\r\n        <Dialog as=\"div\" className=\"relative z-10\" onClose={closeModal}>\r\n          <Transition.Child\r\n            as={Fragment}\r\n            enter=\"ease-out duration-300\"\r\n            enterFrom=\"opacity-0\"\r\n            enterTo=\"opacity-100\"\r\n            leave=\"ease-in duration-200\"\r\n            leaveFrom=\"opacity-100\"\r\n            leaveTo=\"opacity-0\"\r\n          >\r\n            <div className=\"fixed inset-0 bg-black bg-opacity-25\" />\r\n          </Transition.Child>\r\n\r\n          <div className=\"fixed inset-0 overflow-y-auto\">\r\n            <div className=\"flex min-h-full items-center justify-center p-4 text-center\">\r\n              <Transition.Child\r\n                as={Fragment}\r\n                enter=\"ease-out duration-300\"\r\n                enterFrom=\"opacity-0 scale-95\"\r\n                enterTo=\"opacity-100 scale-100\"\r\n                leave=\"ease-in duration-200\"\r\n                leaveFrom=\"opacity-100 scale-100\"\r\n                leaveTo=\"opacity-0 scale-95\"\r\n              >\r\n                <Dialog.Panel className=\"w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all\">\r\n                  <Dialog.Title\r\n                    as=\"h3\"\r\n                    className=\"text-lg font-medium leading-6 text-gray-900\"\r\n                  >\r\n                    Update Account\r\n                  </Dialog.Title>\r\n                  <div className=\"mt-2\">\r\n                    <div className=\"text-sm text-gray-500\">\r\n                      Please enter your password to proceed.\r\n                    </div>\r\n                    <div>\r\n                      <input className=\"placeholder:italic placeholder:text-slate-400 block bg-white w-full border border-slate-300 rounded-md my-3 py-2 pl-3 pr-3 shadow-sm focus:outline-none focus:border-sky-500 focus:ring-sky-500 focus:ring-1 sm:text-sm\"\r\n                      placeholder=\"Current Password *\"\r\n                      type=\"password\"\r\n                      name=\"current_password\"\r\n                      onKeyUp={(event) => {\r\n                        setOldPassword(event.target.value);\r\n                        if(event.keyCode === 13) proceedUpdateAccount();\r\n                      }}\r\n                      />\r\n                    </div>\r\n                  </div>\r\n\r\n                  <div className=\"mt-4\">\r\n                    <button\r\n                      type=\"button\"\r\n                      className=\"inline-flex justify-center rounded-md border border-transparent bg-blue-500 px-4 py-2 mr-2 text-sm font-medium text-white hover:bg-[#2563eb]\"\r\n                      onClick={proceedUpdateAccount}\r\n                    >\r\n                      Proceed\r\n                    </button>\r\n                    <button\r\n                      type=\"button\"\r\n                      className=\"inline-flex justify-center rounded-md border border-transparent bg-gray-100 px-4 py-2 text-sm font-medium text-black hover:bg-gray-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-gray-500 focus-visible:ring-offset-2\"\r\n                      onClick={closeModal}\r\n                    >\r\n                      Close\r\n                    </button>\r\n                  </div>\r\n                </Dialog.Panel>\r\n              </Transition.Child>\r\n            </div>\r\n          </div>\r\n        </Dialog>\r\n      </Transition>\r\n    </>\r\n  )\r\n}\r\n\r\nasync function getEnterpriseMembers() {\r\n  const response = await axios.post(`${process.env.REACT_APP_API_URL}/get-enterprise-members`, {\r\n    'tk': GetCookie('access')\r\n  }, { headers: { 'content-type': 'application/x-www-form-urlencoded' } })\r\n  const output = response.data;\r\n  if(output.success) {\r\n    return output.data;\r\n  } else {\r\n    return [];\r\n  }\r\n}\r\n\r\nconst SubscriptionTabContent = ({setshowAddMoreMember, setShowPausedAccountModal, setUserSubscriptionID, setUserMerchant, setUserAccountID, date_now, user_subscription, userPpg}) => {\r\n  const tk = GetCookie('access');\r\n\r\n  if(user_subscription === undefined) return;\r\n  const changeCard = (event) => {\r\n    let p_id = event.target.getAttribute(\"pid\");\r\n    if(!p_id) return;\r\n    document.querySelector(\".loader-container\").classList.add('active');\r\n    window.location.href = '/change-card?pid=' + p_id;\r\n  }\r\n\r\n  const upgradeSub = (plan) => {\r\n    console.log(plan);\r\n    if (plan==='enterprise'){\r\n      setshowAddMoreMember(true);\r\n    }else{\r\n      window.location.href = '/upgrade';\r\n    }\r\n  }\r\n\r\n  const downgradeSub = () => {\r\n    window.location.href = '/downgrade';\r\n  };\r\n\r\n  const pauseSub = (plan) => {\r\n    setUserSubscriptionID(plan.merchant_subscription_id);\r\n    setUserMerchant(plan.merchant);\r\n    setUserAccountID(plan.pid);\r\n    setShowPausedAccountModal(true);\r\n  }\r\n\r\n  const checkPlanEnt = (plan_type) => {\r\n    if (plan_type) return plan_type.toLowerCase() !== 'enterprise';\r\n    return false; \r\n  };\r\n\r\n  const resumeSub = (plan) => {\r\n    window.location.href = '/resume';\r\n  }\r\n\r\n  const getMerchant = (sub) => {\r\n    return sub.merchant ? sub.merchant.toLowerCase() : \"\";\r\n  };\r\n\r\n  const getCurrency = (sub) => {\r\n    return sub.currency ? sub.currency.toLowerCase() : \"\";\r\n  }\r\n\r\n  const getPlanType = (sub) => {\r\n    return sub.plan_type ? sub.plan_type.toLowerCase() : \"\";\r\n  }\r\n\r\n  const getPaymentInterval = (sub) => {\r\n    return sub.payment_interval ? sub.payment_interval.toLowerCase() : \"\";\r\n  };\r\n\r\n  return (\r\n    <>\r\n    <div className=\"overflow-x-auto overflow-y-visible container-full-width cm-scrollbar\">\r\n      {user_subscription && user_subscription.length ?\r\n      <table className=\"subs-table min-w-full divide-y bg-gray-50 divide-gray-200 min-h-[50px]\">\r\n        <thead>\r\n          <tr className=\"sub_tbl h-3\">\r\n            <td className=\"px-6 py-2 bg-gray-50 text-center text-[12px] font-medium bg-gray-200 text-gray-500 uppercase tracking-wider border\">Plan Details</td>\r\n            <td className=\"px-6 py-2 bg-gray-50 text-center text-[12px] font-medium bg-gray-200 text-gray-500 uppercase tracking-wider border\">Amount</td>\r\n            <td className=\"px-6 py-2 bg-gray-50 text-center text-[12px] font-medium bg-gray-200 text-gray-500 uppercase tracking-wider border\">Trial Period</td>\r\n            <td className=\"px-6 py-2 bg-gray-50 text-center text-[12px] font-medium bg-gray-200 text-gray-500 uppercase tracking-wider border\">Starts On</td>\r\n            {((user_subscription && user_subscription.length > 0 && user_subscription.some(sub => sub.status === 'inactive')) || user_subscription.some(sub => sub.status === 'paused')) && (\r\n              <td className=\"px-6 py-2 bg-gray-50 text-center text-[12px] font-medium bg-gray-200 text-gray-500 uppercase tracking-wider border\">Expires On</td>\r\n            )}\r\n            <td className=\"px-6 py-2 bg-gray-50 text-center text-[12px] font-medium bg-gray-200 text-gray-500 uppercase tracking-wider border\">Status</td>\r\n\r\n            <td className=\"px-6 py-2 bg-gray-50 text-center text-[12px] font-medium bg-gray-200 text-gray-500 uppercase tracking-wider border\">Actions</td>\r\n          </tr>\r\n        </thead>\r\n        <tbody className=\"bg-white divide-y divide-gray-200\">\r\n          {user_subscription?.map((sub, index) => (\r\n            <tr key={index} className=\"sub_tbl h-3\">\r\n            <td className=\"px-6 py-2 bg-gray-50 text-center text-[12px] text-gray-500 uppercase tracking-wider border font-medium\">{checkPlanEnt(sub.plan_type) ? sub.plan_type.replace(\"ProMax\",\" pro max\") : sub.plan_name}</td>\r\n            <td className=\"px-6 py-2 bg-gray-50 text-center text-[12px] text-gray-500 uppercase tracking-wider border font-medium\">{sub.price_label}</td>\r\n            <td className=\"px-6 py-2 bg-gray-50 text-center text-[12px] text-gray-500 uppercase tracking-wider border font-medium\">{sub.trial_days ? \"Yes\" : \"No\"}</td>\r\n            <td className=\"px-6 py-2 bg-gray-50 text-center text-[12px] text-gray-500 uppercase tracking-wider border font-medium\">{formatDate(sub.start_date)}</td>\r\n\r\n            { user_subscription && user_subscription.length > 0 && user_subscription.some(sub => sub.status === 'inactive') ? (\r\n                sub.status === 'active' ? (\r\n                  <td className=\"inactive px-6 py-2 bg-gray-50 text-center text-[12px] text-gray-500 uppercase tracking-wider border font-medium\">\r\n                  </td>\r\n                ) : (\r\n                  <td className=\"inactive px-6 py-2 bg-gray-50 text-center text-[12px] text-gray-500 uppercase tracking-wider border font-medium\">\r\n                    {formatDate(sub.end_date)}\r\n                  </td>\r\n                )\r\n\r\n              ) : sub.status === 'active' ? (\r\n                ''\r\n              ) : sub.status === 'paused' ? (\r\n                <td className=\"active px-6 py-2 bg-gray-50 text-center text-[12px] text-gray-500 uppercase tracking-wider border font-medium\">\r\n                  {formatDate(sub.end_date)}\r\n                </td>\r\n              ) : (\r\n                <td className=\"active px-6 py-2 bg-gray-50 text-center text-[12px] text-gray-500 uppercase tracking-wider border font-medium\">\r\n                  {formatDate(sub.end_date)}\r\n                </td>\r\n            )}\r\n\r\n            <td className=\"px-6 py-2 bg-gray-50 text-center text-[12px] text-gray-500 uppercase tracking-wider border font-medium\">{sub.status}</td>\r\n            <td className=\"px-6 py-2 bg-gray-50 text-center text-[12px] text-gray-500 uppercase tracking-wider border font-medium\">\r\n              { sub.status === 'paused'  ?\r\n              <div className=\"text-[12px]\">\r\n                <Menu as=\"div\" className=\"relative inline-block text-center w-full text-[12px]\">\r\n                  <div className='w-full'>\r\n                    <Menu.Button className=\"inline-flex w-full justify-center rounded-md bg-sky-500/100 px-4 py-2 font-medium text-white hover:bg-[#49b1df]\">\r\n                      Options\r\n                    </Menu.Button>\r\n                  </div>\r\n                  <Transition\r\n                    as={Fragment}\r\n                    enter=\"transition ease-out duration-100\"\r\n                    enterFrom=\"transform opacity-0 scale-95\"\r\n                    enterTo=\"transform opacity-100 scale-100\"\r\n                    leave=\"transition ease-in duration-75\"\r\n                    leaveFrom=\"transform opacity-100 scale-100\"\r\n                    leaveTo=\"transform opacity-0 scale-95\"\r\n                  >\r\n\r\n                    <Menu.Items className={`${\r\n                        index < 3 ? \"dp-top\" : \"dp-bot\"\r\n                      } absolute w-50 origin-top-right divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none text-center min-w-full text-center`}>\r\n                      <div className=\"text-center min-w-full\">\r\n                      { getMerchant(sub) === 'recurly' || getMerchant(sub) === 'stripe' ? (\r\n                        <Menu.Item>\r\n                          {({ active }) => (\r\n                            <button\r\n                              className={`${\r\n                                active ? 'bg-sky-500/100 text-white' : 'text-gray-900'\r\n                              } group min-w-full items-center px-4 py-2 text-left border-b border-b-slate-100`}\r\n                              pid={sub.pid}\r\n                              onClick={changeCard}\r\n                            >\r\n                              Change Card\r\n                            </button>\r\n                          )}\r\n                        </Menu.Item>\r\n                        ) : \"\" }\r\n                        { (getMerchant(sub) === 'recurly' || getMerchant(sub) === 'stripe' || getMerchant(sub) === 'fastspring') && sub.status === 'paused' && getPaymentInterval(sub) === 'monthly' ? (\r\n                        <Menu.Item>\r\n                        {({ active }) => (\r\n                          <button\r\n                            className={`${\r\n                              active ? 'bg-sky-500/100 text-white' : 'text-gray-900'\r\n                            } group min-w-full items-center px-4 py-2 text-left border-b border-b-slate-100`}\r\n                            onClick={(plan) => resumeSub(sub)}\r\n                            >\r\n                              Resume\r\n                          </button>\r\n                        )}\r\n                        </Menu.Item>\r\n                        ) : \"\" }\r\n                        <Menu.Item>\r\n                          {({ active }) => (\r\n                            <CancelSubscription pid={sub.pid} tk={tk}/>\r\n                          )}\r\n                        </Menu.Item>\r\n                      </div>\r\n                    </Menu.Items>\r\n                  </Transition>\r\n                </Menu>\r\n              </div> : \"\"}\r\n\r\n              { sub.status === 'active'  ?\r\n              <div className=\"text-[12px]\">\r\n                <Menu as=\"div\" className=\"relative inline-block text-center w-full text-[12px]\">\r\n                  <div className='w-full'>\r\n                    <Menu.Button className=\"inline-flex w-full justify-center rounded-md bg-sky-500/100 px-4 py-2 font-medium text-white hover:bg-[#49b1df]\">\r\n                      Options\r\n                    </Menu.Button>\r\n                  </div>\r\n                  <Transition\r\n                    as={Fragment}\r\n                    enter=\"transition ease-out duration-100\"\r\n                    enterFrom=\"transform opacity-0 scale-95\"\r\n                    enterTo=\"transform opacity-100 scale-100\"\r\n                    leave=\"transition ease-in duration-75\"\r\n                    leaveFrom=\"transform opacity-100 scale-100\"\r\n                    leaveTo=\"transform opacity-0 scale-95\"\r\n                  >\r\n                    <Menu.Items className={`${\r\n                        index < 3 ? \"dp-top\" : \"dp-bot\"\r\n                      } absolute w-50 origin-top-right divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none text-center min-w-full text-center`}>\r\n                      <div className=\"text-center min-w-full\">\r\n                        { getMerchant(sub) === 'recurly' || getMerchant(sub) === 'stripe' ? (\r\n                        <Menu.Item>\r\n                          {({ active }) => (\r\n                            <button\r\n                              className={`${\r\n                                active ? 'bg-sky-500/100 text-white' : 'text-gray-900'\r\n                              } group min-w-full items-center px-4 py-2 text-left border-b border-b-slate-100`}\r\n                              pid={sub.pid}\r\n                              onClick={changeCard}\r\n                            >\r\n                              Change Card\r\n                            </button>\r\n                          )}\r\n                        </Menu.Item>\r\n                        ) : \"\" }\r\n                        { (getCurrency(sub) === 'usd' || getMerchant(sub) === 'paddle' || getMerchant(sub) === 'stripe' || getMerchant(sub) === 'paypal' || getMerchant(sub) === 'fastspring')\r\n                        && diffMin(formatDateTime(date_now), formatDateTime(sub.start_date)) >= 0 ?\r\n                        <>\r\n                         { (getPlanType(sub) !== \"enterprise\" && getPaymentInterval(sub) !== 'yearly' && getPlanType(sub) !== \"promax\" && userPpg !== '97') ||\r\n                          (userPpg === '97' && (getPaymentInterval(sub) !== 'yearly' && getPlanType(sub) === \"promax\")) ||\r\n                          (getPlanType(sub) === \"basic\") || (getPlanType(sub) === \"pro\") ? (\r\n                          <Menu.Item>\r\n                            {({ active }) => (\r\n                              <button\r\n                                className={`${\r\n                                  active ? 'bg-sky-500/100 text-white' : 'text-gray-900'\r\n                                } group min-w-full items-center px-4 py-2 text-left border-b border-b-slate-100`}\r\n                                onClick={(plan) => upgradeSub(getPlanType(sub))}\r\n                              >\r\n                                Upgrade\r\n                              </button>\r\n                            )}\r\n                          </Menu.Item>\r\n                          ) : \"\" }\r\n\r\n                          { (!(getPlanType(sub) === \"basic\" && getPaymentInterval(sub) === 'monthly') && getPlanType(sub) !== \"enterprise\") ? (\r\n                          <Menu.Item>\r\n                            {({ active }) => (\r\n                              <button\r\n                                className={`${\r\n                                  active ? 'bg-sky-500/100 text-white' : 'text-gray-900'\r\n                                } group min-w-full items-center px-4 py-2 text-left border-b border-b-slate-100`}\r\n                                onClick={downgradeSub}\r\n                              >\r\n                                Downgrade\r\n                              </button>\r\n                            )}\r\n                          </Menu.Item>\r\n                          ) : \"\" }\r\n\r\n                          { (getMerchant(sub) === 'recurly' || getMerchant(sub) === 'stripe' || getMerchant(sub) === 'fastspring') && sub.status === 'active' && getPaymentInterval(sub) === 'monthly' && sub.is_trial_end === 'yes' && sub.plan_type !== 'Enterprise' ? (\r\n                          <Menu.Item>\r\n                            {({ active }) => (\r\n                              <button\r\n                                className={`${\r\n                                  active ? 'bg-sky-500/100 text-white' : 'text-gray-900'\r\n                                } group min-w-full items-center px-4 py-2 text-left border-b border-b-slate-100`}\r\n                                onClick={(plan) => pauseSub(sub)}\r\n                              >\r\n                                Pause\r\n                              </button>\r\n                            )}\r\n                          </Menu.Item>\r\n                          ) : \"\" }\r\n\r\n                        </> : <></> }\r\n                        <Menu.Item>\r\n                          {({ active }) => (\r\n                            <CancelSubscription pid={sub.pid} tk={tk}/>\r\n                          )}\r\n                        </Menu.Item>\r\n                      </div>\r\n                    </Menu.Items>\r\n                  </Transition>\r\n                </Menu>\r\n              </div> : \"\" }\r\n\r\n\r\n            </td>\r\n          </tr>\r\n          ))}\r\n        </tbody>\r\n      </table>\r\n      : <div>\r\n        <span className=\"text-[12px] text-gray-600\"><FaInfoCircle className=\"inline text-lg mr-1\"/> No active subscription. Look for available <a href=\"/pricing\" className=\"text-blue-400 font-bold\">SUBSCRIPTIONS</a></span>\r\n        </div>\r\n      }\r\n    </div>\r\n    </>\r\n  );\r\n};\r\n\r\n\r\nconst OrderTabContent = ({user_order}) => {\r\n\r\n  if(user_order === undefined) return;\r\n  return (\r\n    <div className=\"overflow-x-auto custom-scrollbar container-full-width bg-gray-50\">\r\n      {user_order && user_order.length ?\r\n      <table className=\"min-w-full divide-y divide-gray-200 min-h-[50px]\">\r\n        <thead>\r\n          <tr className=\"sub_tbl h-3\">\r\n            <th className=\"px-6 py-3 bg-gray-50 text-center text-[12px] font-medium bg-gray-200 text-gray-500 uppercase tracking-wider\">Invoice</th>\r\n            <th className=\"px-6 py-3 bg-gray-50 text-center text-[12px] font-medium bg-gray-200 text-gray-500 uppercase tracking-wider\">Membership</th>\r\n            <th className=\"px-6 py-3 bg-gray-50 text-center text-[12px] font-medium bg-gray-200 text-gray-500 uppercase tracking-wider\">Amount</th>\r\n            <th className=\"px-6 py-3 bg-gray-50 text-center text-[12px] font-medium bg-gray-200 text-gray-500 uppercase tracking-wider\">Date</th>\r\n            <th className=\"px-6 py-3 bg-gray-50 text-center text-[12px] font-medium bg-gray-200 text-gray-500 uppercase tracking-wider\">Status</th>\r\n          </tr>\r\n        </thead>\r\n        <tbody className=\"bg-white divide-y divide-gray-200\">\r\n          {user_order?.map((sub, index) => (\r\n            <tr key={index} className=\"sub_tbl h-3\">\r\n            <td className=\"px-6 py-3 bg-gray-50 text-center text-[12px] text-gray-500 uppercase tracking-wider border font-medium\">{sub.charge_id}</td>\r\n            <td className=\"px-6 py-3 bg-gray-50 text-center text-[12px] text-gray-500 uppercase tracking-wider border font-medium\">{sub.label}</td>\r\n            <td className=\"px-6 py-3 bg-gray-50 text-center text-[12px] text-gray-500 uppercase tracking-wider border font-medium\">{sub.amount ? getPricePlan(sub.currency, sub.amount) : \"\"}</td>\r\n            <td className=\"px-6 py-3 bg-gray-50 text-center text-[12px] text-gray-500 uppercase tracking-wider border font-medium\">{formatDate(sub.created_at)}</td>\r\n            <td className=\"px-6 py-3 bg-gray-50 text-center text-[12px] text-gray-500 uppercase tracking-wider border font-medium\">{sub.refund_at !== \"0000-00-00 00:00:00\" ? \"Refunded\" : \"Completed\"}</td>\r\n            </tr>\r\n          ))}\r\n        </tbody>\r\n      </table>\r\n      : <span className=\"text-[12px] text-gray-600\"><FaInfoCircle className=\"inline text-lg mr-1\"/> No orders have been made yet. Look for available <a href=\"/pricing\" className=\"text-blue-400 font-bold\">SUBSCRIPTIONS</a></span> }\r\n    </div>\r\n  );\r\n};\r\n\r\nconst TokenUsageContent = ({tokenUsage, tokenUsageTotal, userMaxToken, currentDate, isShowEnterprise, currentPlan, gpt4oTotalToken, gpt4oMaxTokenUsage, plan, dalleTotalImage, dalleMaxImage, claudeTotalToken, claudeMaxTokenUsage, currentPlanName, fluxPrompt, fluxTotalPrompt, membersTotalFluxPrompt, o1Prompt, o1TotalPrompt, membersTotalo1Prompt, deepseekTotalToken, deepseekMaxTokenUsage}) => {\r\n  const isPro = () => {\r\n    if(plan.toLowerCase() === 'pro') return true;\r\n    return false;\r\n  };\r\n  \r\n  \r\n  const isProMax = () => {\r\n    if(plan.toLowerCase() === 'promax') return true;\r\n    return false;\r\n  };\r\n\r\n  const isAdvanced = () => {\r\n    if(plan.toLowerCase() === 'advanced') return true;\r\n    return false;\r\n  };\r\n\r\n  const isUnPaid = () => {\r\n    if(plan === '' || plan === null) return true;\r\n    return false;\r\n  };\r\n\r\n  const isEnterpriseCluter = () => {\r\n    if(plan.toLowerCase() === 'enterprise' && currentPlanName.toLowerCase() !== 'enterprise') return true;\r\n    return false;\r\n  };\r\n\r\n  const convertToLocaleString = (total) => {\r\n    return parseInt(total).toLocaleString(\"en-US\");\r\n  }\r\n\r\n  const isTokenMaxUsage = () => {\r\n    if(userMaxToken <= tokenUsageTotal && !isProMax()) return true;\r\n    return false;\r\n  }\r\n\r\n  const isDalleMaxUsage = () => {\r\n    if(parseInt(dalleTotalImage) >= parseInt(dalleMaxImage) && !isProMax()) return true;\r\n    return false;\r\n  }\r\n\r\n  const isClaudeMaxUsage = () => {\r\n    if(parseInt(claudeTotalToken) >= parseInt(claudeMaxTokenUsage) && (!isProMax() && !isAdvanced())) return true;\r\n    return false;\r\n  }\r\n\r\n  const isDeepseekMaxUsage = () => {\r\n    if(parseInt(deepseekTotalToken) >= parseInt(deepseekMaxTokenUsage) && (!isProMax())) return true;\r\n    return false;\r\n  }\r\n\r\n  return (\r\n    <div className=\"w-full\">\r\n      <div>\r\n        <p className={`text-md font-bold\r\n          ${\r\n            (isTokenMaxUsage() && (!isUnPaid() && !isEnterpriseCluter()))\r\n              ? \"text-red-700\"\r\n              : \"\"\r\n          }`}>\r\n          <span className='total1'>\r\n            {\r\n              (isTokenMaxUsage() && (!isUnPaid() && !isEnterpriseCluter())) &&\r\n              <img src={imgWarning} alt=\"usage-warning\" className=\"block pr-2 w-8 inline-flex\"/>\r\n            }\r\n            TOTAL TOKEN USAGE: <></>\r\n          </span>\r\n          <span className='total2'>\r\n            { convertToLocaleString(tokenUsageTotal) }\r\n            { (!isProMax() && !isEnterpriseCluter()) ? <span> out of {convertToLocaleString(userMaxToken)}</span> : \"\" }\r\n          </span>\r\n        </p>\r\n        <p className={`text-sm\r\n            ${\r\n              (\r\n                plan.toLowerCase() === \"basic\" &&\r\n                parseInt(gpt4oTotalToken) >= parseInt(gpt4oMaxTokenUsage)\r\n              ) || (isTokenMaxUsage() && (!isUnPaid() && !isEnterpriseCluter()))\r\n                ? \"text-red-700\"\r\n                : \"\"\r\n            }`}>\r\n          <span className='total1'>\r\n            {\r\n              (isTokenMaxUsage() && (!isUnPaid() && !isEnterpriseCluter())) &&\r\n              <img src={imgWarning} alt=\"usage-warning\" className=\"block pr-2 w-8 inline-flex\"/>\r\n            }\r\n            TOTAL GPT-4o TOKEN USAGE: <></>\r\n          </span>\r\n          <span className='total2'>\r\n            { convertToLocaleString(gpt4oTotalToken) }\r\n            { plan.toLowerCase() === \"basic\" ? <span> out of {convertToLocaleString(gpt4oMaxTokenUsage)}</span> : \"\" }\r\n          </span>\r\n        </p>\r\n\r\n        {\r\n          plan.toLowerCase() !== 'enterprise' ?\r\n            isProMax() ?\r\n              <>\r\n                <p className={`text-sm`}>\r\n                  <span className='total1'>TOTAL DALL·E IMAGE GENERATED: </span>\r\n                  <span className='total2'>\r\n                    {convertToLocaleString(dalleTotalImage)}\r\n                  </span>\r\n                </p>\r\n                <p className={`text-sm`}>\r\n                  <span className='total1'>TOTAL CLAUDE TOKEN USAGE: </span>\r\n                  <span className='total2'>\r\n                    {convertToLocaleString(dalleTotalImage)}\r\n                  </span>\r\n                </p>\r\n                <p className={`text-sm`}>\r\n                  <span className='total1'>TOTAL DEEPSEEK R1 TOKEN USAGE: </span>\r\n                  <span className='total2'>\r\n                    {convertToLocaleString(deepseekTotalToken)}\r\n                  </span>\r\n                </p>\r\n              </>\r\n            :\r\n              <>\r\n                <p className={`text-sm\r\n                  ${\r\n                    ( isDalleMaxUsage() && !isUnPaid() )\r\n                      ? \"text-red-700\"\r\n                      : \"\"\r\n                  }\r\n                `}>\r\n                  <span className='total1'>\r\n                    {\r\n                      (isDalleMaxUsage()  && isTokenMaxUsage() && !isUnPaid()) &&\r\n                      <img src={imgWarning} alt=\"usage-warning\" className=\"block pr-2 w-8 inline-flex\"/>\r\n                    }\r\n                    TOTAL DALL·E IMAGE GENERATED: <></>\r\n                  </span>\r\n                  <span className='total2'>\r\n                  <span> { convertToLocaleString(dalleTotalImage) }</span> out of <span>{ convertToLocaleString(dalleMaxImage) }</span>\r\n                  </span>\r\n                </p>\r\n                <p className={`text-sm\r\n                  ${\r\n                    ( (isClaudeMaxUsage() || isTokenMaxUsage()) && !isUnPaid() )\r\n                      ? \"text-red-700\"\r\n                      : \"\"\r\n                  }\r\n                `}>\r\n                  <span className='total1'>\r\n                    {\r\n                      ((isClaudeMaxUsage() && isTokenMaxUsage() && !isUnPaid()) || (isTokenMaxUsage() && isAdvanced())) &&\r\n                      <img src={imgWarning} alt=\"usage-warning\" className=\"block pr-2 w-8 inline-flex\"/>\r\n                    }\r\n                    TOTAL CLAUDE TOKEN USAGE: <></>\r\n                  </span>\r\n                  <span className='total2'>\r\n                  <span> { convertToLocaleString(claudeTotalToken) }</span> \r\n                  {!isAdvanced() ? <span> out of { convertToLocaleString(claudeMaxTokenUsage) }</span> : \"\"}\r\n                  </span>\r\n                </p>\r\n                { isPro() ?\r\n                  <p className={`text-sm\r\n                    ${\r\n                      ( (isDeepseekMaxUsage() || isTokenMaxUsage()) && !isUnPaid() )\r\n                        ? \"text-red-700\"\r\n                        : \"\"\r\n                    }\r\n                  `}>\r\n                    <span className='total1'>\r\n                      {\r\n                        ((isDeepseekMaxUsage() && isTokenMaxUsage() && !isUnPaid()) || (isTokenMaxUsage() && isAdvanced())) &&\r\n                        <img src={imgWarning} alt=\"usage-warning\" className=\"block pr-2 w-8 inline-flex\"/>\r\n                      }\r\n                      TOTAL DEEPSEEK R1 TOKEN USAGE: <></>\r\n                    </span>\r\n                    <span className='total2'>\r\n                    <span> { convertToLocaleString(deepseekTotalToken) }</span> \r\n                    {!isAdvanced() ? <span> out of { convertToLocaleString(deepseekMaxTokenUsage) }</span> : \"\"}\r\n                    </span>\r\n                  </p>\r\n                  :<></>\r\n                }\r\n              </>\r\n          : (plan.toLowerCase() === 'enterprise' && currentPlanName.toLowerCase() !== 'enterprise') ?\r\n            <>\r\n              <p className={`text-sm\r\n                ${\r\n                  ( fluxPrompt >= fluxTotalPrompt )\r\n                    ? \"text-red-700\"\r\n                    : \"\"\r\n                }\r\n              `}>\r\n                <span className='total1'>\r\n                  {/* {\r\n                    membersTotalFluxPrompt >= fluxTotalPrompt &&\r\n                    <img src={imgWarning} alt=\"usage-warning\" className=\"block pr-2 w-8 inline-flex\"/>\r\n                  } */}\r\n                  TOTAL FLUX PROMPTS: <></>\r\n                </span>\r\n                <span className='total2'>\r\n                <span> { convertToLocaleString(fluxPrompt) }</span> out of <span>{ convertToLocaleString(fluxTotalPrompt) }</span>\r\n                </span>\r\n              </p>\r\n              <p className={`text-sm\r\n                ${\r\n                  ( o1Prompt >= o1TotalPrompt )\r\n                    ? \"text-red-700\"\r\n                    : \"\"\r\n                }\r\n              `}>\r\n                <span className='total1'>\r\n                  {/* {\r\n                    membersTotalFluxPrompt >= fluxTotalPrompt &&\r\n                    <img src={imgWarning} alt=\"usage-warning\" className=\"block pr-2 w-8 inline-flex\"/>\r\n                  } */}\r\n                  TOTAL o1 PROMPTS: <></>\r\n                </span>\r\n                <span className='total2'>\r\n                <span> { convertToLocaleString(o1Prompt) }</span> out of <span>{ convertToLocaleString(o1TotalPrompt) }</span>\r\n                </span>\r\n              </p>\r\n            </>\r\n          : <></>\r\n        }\r\n\r\n        {\r\n          plan && !isProMax() ?\r\n          <p className=\"text-sm font-bold pb-2\"><span className='total1'>Token count will reset on:</span>  <span className='total2'>{currentDate}</span></p>\r\n          : \"\"\r\n        }\r\n      </div>\r\n\r\n      {(currentPlan !=='' && !isProMax() && isTokenMaxUsage() && isShowEnterprise === false ) ? (\r\n      <div>\r\n        <p className=\"text-md text-red-700\">UPGRADE is required to continue</p>\r\n        <div className='flex inline-flex sm:mr-2'>\r\n          <p>\r\n            <a href=\"/upgrade/?mx=1\" class=\"block w-full font-bold text-white py-2 px-4 rounded-md bg-[#ffa500] hover:bg-[#FFC034]\">Upgrade</a>\r\n          </p>\r\n        </div>\r\n        </div>\r\n      ) : \"\"}\r\n\r\n    {(currentPlan !=='' && !isProMax() && isTokenMaxUsage() && isShowEnterprise === true && !isEnterpriseCluter()) ? (\r\n      <div>\r\n        <p className=\"text-md text-red-700\">For continous access, kindly reach out to our support team</p>\r\n        <div className='flex inline-flex sm:mr-2'>\r\n          <p>\r\n            <a href=\"https://ai-pro.org/contact-us\" class=\"block w-full font-bold text-white py-2 px-4 rounded-md bg-[#ffa500] hover:bg-[#FFC034]\">Contact Support</a>\r\n          </p>\r\n        </div>\r\n      </div>\r\n    ) : \"\"}\r\n\r\n      <ul className='pt-5'>\r\n      {tokenUsage?.map((sub, index) => (\r\n        <li><span className='font-bold'>{sub.app}:</span> {parseInt(sub.total_token).toLocaleString(\"en-US\")}</li>\r\n      ))}\r\n      </ul>\r\n    </div>\r\n  );\r\n};\r\n\r\nconst HelpTabContent = () => {\r\n  return (\r\n    <div className=\"w-full\">\r\n      <p className=\"text-sm py-4\">If you have any comments about our website, AI tools. and articles, or if you have questions about your account access, please don’t hesitate to get in touch with us. Leave your messages through the <a href=\"https://ai-pro.org/contact-us/\" className=\"text-blue-400 font-bold\">Contact us</a> page.</p>\r\n      <p className=\"font-bold\">Quick Links:</p>\r\n      <ul>\r\n        <li><a href=\"/my-account\" className=\"text-blue-400 font-bold px-2\"> My Apps</a></li>\r\n        <li><a href=\"/my-account\" className=\"text-blue-400 font-bold px-2\"> AI Tools</a></li>\r\n        <li><a href=\"https://ai-pro.org/articles/\" className=\"text-blue-400 font-bold px-2\"> Articles</a></li>\r\n        <li><a href=\"https://ai-pro.org/tutorials/\" className=\"text-blue-400 font-bold px-2\"> Tutorials</a></li>\r\n      </ul>\r\n    </div>\r\n  );\r\n};\r\n\r\nconst LogOutContent = () => {\r\n\r\n  function logout() {\r\n    RemoveCookie(\"access\");\r\n    RemoveCookie(\"ci_session\");\r\n\r\n    axios.get(`${process.env.REACT_APP_API_URL}/logout`).then(function(){\r\n      window.location.href = '/login';\r\n    });\r\n  };\r\n\r\n  return (\r\n    <div>\r\n      <motion.button\r\n        className=\"bg-blue-500 mb-1 text-white font-bold py-3 px-6 my-3 rounded-lg\"\r\n        whileTap={{ scale: 0.9 }}\r\n        onClick={logout}\r\n      >\r\n        Logout\r\n      </motion.button>\r\n    </div>\r\n  );\r\n};\r\n\r\nconst DeleteAccount = (props) => {\r\n  const [email] = useState(props.email);\r\n  const [isShowModal, setIsShowModal] = useState(false);\r\n\r\n  const showDeleteModal = () => {\r\n    setIsShowModal(true);\r\n  };\r\n  const closeModal = () => {\r\n    setIsShowModal(false);\r\n  };\r\n\r\n  const proceedDeleteAccount = () => {\r\n    document.querySelector(\".loader-container\").classList.add('active');\r\n    axios.post(`${process.env.REACT_APP_API_URL}/delete-account`, {\r\n    }, { headers: { 'content-type': 'application/x-www-form-urlencoded' } }).then(function(res) {\r\n      let output = res.data;\r\n      if(output.success) {\r\n        window.mixpanel.people.set_once({ \"$email\": email });\r\n        window.mixpanel.identify(email);\r\n        window.mixpanel.track(\"delete_account\", {});\r\n        //\r\n        toastr.success(\"Success\");\r\n        window.location.reload();\r\n        return;\r\n      }\r\n      document.querySelector(\".loader-container\").classList.remove('active');\r\n      toastr.error(\"Fail\");\r\n    });\r\n  }\r\n  return (\r\n    <>\r\n      <span className=\"cursor-pointer hover:underline hover:decoration-solid\" onClick={showDeleteModal}>Delete Account</span>\r\n      <Transition appear show={isShowModal} as={Fragment}>\r\n        <Dialog as=\"div\" className=\"relative z-10\" onClose={closeModal}>\r\n          <Transition.Child\r\n            as={Fragment}\r\n            enter=\"ease-out duration-300\"\r\n            enterFrom=\"opacity-0\"\r\n            enterTo=\"opacity-100\"\r\n            leave=\"ease-in duration-200\"\r\n            leaveFrom=\"opacity-100\"\r\n            leaveTo=\"opacity-0\"\r\n          >\r\n            <div className=\"fixed inset-0 bg-black bg-opacity-25\" />\r\n          </Transition.Child>\r\n\r\n          <div className=\"fixed inset-0 overflow-y-auto\">\r\n            <div className=\"flex min-h-full items-center justify-center p-4 text-center\">\r\n              <Transition.Child\r\n                as={Fragment}\r\n                enter=\"ease-out duration-300\"\r\n                enterFrom=\"opacity-0 scale-95\"\r\n                enterTo=\"opacity-100 scale-100\"\r\n                leave=\"ease-in duration-200\"\r\n                leaveFrom=\"opacity-100 scale-100\"\r\n                leaveTo=\"opacity-0 scale-95\"\r\n              >\r\n                <Dialog.Panel className=\"w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all\">\r\n                  <Dialog.Title\r\n                    as=\"h3\"\r\n                    className=\"text-lg font-medium leading-6 text-gray-900\"\r\n                  >\r\n                    Delete Account\r\n                  </Dialog.Title>\r\n                  <div className=\"mt-2\">\r\n                    <p className=\"text-sm text-gray-500\">\r\n                    Deleting your account will permanently remove all of your data and information associated with it. This action is irreversible, and you won't be able to recover your account or any of its contents.\r\n                    </p>\r\n                    <p className=\"text-sm text-gray-500\">\r\n                    Are you sure you want to delete your account?\r\n                    </p>\r\n                  </div>\r\n\r\n                  <div className=\"mt-4\">\r\n                    <button\r\n                      type=\"button\"\r\n                      className=\"inline-flex justify-center rounded-md border border-transparent bg-red-500 px-4 py-2 mr-2 text-sm font-medium text-white hover:bg-[#d30101]\"\r\n                      onClick={proceedDeleteAccount}\r\n                    >\r\n                      Yes! Delete My Account\r\n                    </button>\r\n                    <button\r\n                      type=\"button\"\r\n                      className=\"inline-flex justify-center rounded-md border border-transparent bg-gray-100 px-4 py-2 text-sm font-medium text-black hover:bg-gray-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-gray-500 focus-visible:ring-offset-2\"\r\n                      onClick={closeModal}\r\n                    >\r\n                      Cancel\r\n                    </button>\r\n                  </div>\r\n                </Dialog.Panel>\r\n              </Transition.Child>\r\n            </div>\r\n          </div>\r\n        </Dialog>\r\n      </Transition>\r\n    </>\r\n  )\r\n}\r\n\r\nconst CancelSubscription = ({ pid, tk }) => {\r\n  const [isShowModal, setIsShowModal] = useState(false);\r\n  const auth = Auth();\r\n  const showCancelModal = () => {\r\n    setIsShowModal(true);\r\n  };\r\n  const closeModal = () => {\r\n    setIsShowModal(false);\r\n  };\r\n  const unSubscribe = (event) => {\r\n    if(!pid) return;\r\n    closeModal();\r\n    document.querySelector(\".loader-container\").classList.add('active');\r\n    if(auth.surveydata === '' || auth.surveydata === null){\r\n      window.location.href = '/survey';\r\n    }else{\r\n      axios.post(`${process.env.REACT_APP_API_URL}/cancel-subscription`, {\r\n        tk,\r\n        p_id: pid\r\n      }, { headers: { 'content-type': 'application/x-www-form-urlencoded' } }).then(function(res){\r\n        let output = res.data;\r\n        if(output.success) {\r\n          toastr.success(\"Success\");\r\n          window.location.reload();\r\n          return;\r\n        }\r\n        document.querySelector(\".loader-container\").classList.remove('active');\r\n        if(output.data) toastr.error(output.data.msg);\r\n      });\r\n    }\r\n\r\n  };\r\n  return (\r\n    <>\r\n      <button\r\n      className={`hover:bg-sky-500/100 hover:text-white text-gray-900 group min-w-full items-center px-4 py-2 text-left`}\r\n      pid={pid}\r\n      onClick={showCancelModal}\r\n      >\r\n        Cancel\r\n      </button>\r\n      <Transition appear show={isShowModal} as={Fragment}>\r\n        <Dialog as=\"div\" className=\"relative z-10\" onClose={closeModal}>\r\n          <Transition.Child\r\n            as={Fragment}\r\n            enter=\"ease-out duration-300\"\r\n            enterFrom=\"opacity-0\"\r\n            enterTo=\"opacity-100\"\r\n            leave=\"ease-in duration-200\"\r\n            leaveFrom=\"opacity-100\"\r\n            leaveTo=\"opacity-0\"\r\n          >\r\n            <div className=\"fixed inset-0 bg-black bg-opacity-25\" />\r\n          </Transition.Child>\r\n\r\n          <div className=\"fixed inset-0 overflow-y-auto\">\r\n            <div className=\"flex min-h-full items-center justify-center p-4 text-center\">\r\n              <Transition.Child\r\n                as={Fragment}\r\n                enter=\"ease-out duration-300\"\r\n                enterFrom=\"opacity-0 scale-95\"\r\n                enterTo=\"opacity-100 scale-100\"\r\n                leave=\"ease-in duration-200\"\r\n                leaveFrom=\"opacity-100 scale-100\"\r\n                leaveTo=\"opacity-0 scale-95\"\r\n              >\r\n                <Dialog.Panel className=\"w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all\">\r\n                  <Dialog.Title\r\n                    as=\"h3\"\r\n                    className=\"text-lg font-medium leading-6 text-gray-900\"\r\n                  >\r\n                    Cancel Subscription\r\n                  </Dialog.Title>\r\n                  <div className=\"mt-2\">\r\n                    <p className=\"text-sm text-gray-500\">\r\n                      Are you sure you want to cancel your subscription?\r\n                    </p>\r\n                  </div>\r\n\r\n                  <div className=\"mt-4 text-right\">\r\n                    <button\r\n                      type=\"button\"\r\n                      className=\"inline-flex justify-center rounded-md border border-transparent bg-red-500 px-4 py-2 mr-2 text-sm font-medium text-white hover:bg-[#d30101]\"\r\n                      onClick={unSubscribe}\r\n                    >\r\n                      Yes\r\n                    </button>\r\n                    <button\r\n                      type=\"button\"\r\n                      className=\"inline-flex justify-center rounded-md border border-transparent bg-gray-100 px-4 py-2 text-sm font-medium text-black hover:bg-gray-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-gray-500 focus-visible:ring-offset-2\"\r\n                      onClick={closeModal}\r\n                    >\r\n                      Close\r\n                    </button>\r\n                  </div>\r\n                </Dialog.Panel>\r\n              </Transition.Child>\r\n            </div>\r\n          </div>\r\n        </Dialog>\r\n      </Transition>\r\n    </>\r\n  )\r\n}\r\n\r\nfunction limitDisplay(members, displayLimit) {\r\n  return members.slice(0, displayLimit)\r\n}\r\n\r\nfunction getTotalPages(members, displayLimit) {\r\n  if (members.length<displayLimit){\r\n    return 1;\r\n  }\r\n\r\n  let totalMembers = members.length;\r\n  let quotient = Math.floor(totalMembers/displayLimit); // => 4 => the times 3 fits into 13\r\n  let remainder = totalMembers % displayLimit;\r\n\r\n  if (remainder>0){\r\n    quotient = quotient+1;\r\n  }\r\n  return quotient;\r\n}\r\n\r\nconst MembersTabContent = ({handleShowAddMember, enterpriseMembers, handleReloadMembers, handleShowEditMemberModal, currentPlan, currentPlanName}) => {\r\n\r\n  const [search, setSearch] = useState(\"\");\r\n  const [cloneEnterpriseMembers, setcloneEnterpriseMembers] = useState([]);\r\n  const [displayEnterpriseMembers, setDisplayEnterpriseMembers] = useState([]);\r\n  const [showOnly, setShowOnly] = useState(5);\r\n  const [pageCount, setPageCount] = useState(0);\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n\r\n  useEffect(() => {\r\n    let members = enterpriseMembers;\r\n    setcloneEnterpriseMembers(members);\r\n\r\n    members = limitDisplay(members,5);\r\n    setDisplayEnterpriseMembers(members);\r\n\r\n    let PageCount = getTotalPages(enterpriseMembers,5);\r\n    setPageCount(PageCount);\r\n  }, [enterpriseMembers]);\r\n\r\n\tconst handleSearch = () => {\r\n    let input = document.getElementById(\"ent_search\").value;\r\n    let members = enterpriseMembers;\r\n\r\n    members = enterpriseMembers.filter(function(item){\r\n      let full_name = item.first_name + ' ' + item.last_name;\r\n      return ((item.first_name.indexOf(input)>-1 || item.last_name.indexOf(input)>-1 || item.email.indexOf(input)>-1 || full_name.indexOf(input) > -1))\r\n    })\r\n\r\n    let PageCount = getTotalPages(members,showOnly);\r\n    setPageCount(PageCount);\r\n    setcloneEnterpriseMembers(members);\r\n\r\n    members = limitDisplay(members,showOnly);\r\n    setDisplayEnterpriseMembers(members);\r\n  };\r\n\r\n  useEffect(() => {\r\n    handleSearch();\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [showOnly]);\r\n\r\n  const generatePager = () => {\r\n    let content = [];\r\n\r\n    if (pageCount<=1 || enterpriseMembers.length===0){\r\n      return content;\r\n    }\r\n\r\n    if (currentPage>1){\r\n      content.push(<li className=\"p-2 cursor-pointer\" key={'prev_page'} onClick={(e)=>handlePagerClick(currentPage-1)}>Previous</li>);\r\n    }else{\r\n      content.push(<li className=\"p-2 cursor-default\" key={'prev_page'}>Previous</li>);\r\n    }\r\n\r\n    for (let i = 0; i < pageCount; i++) {\r\n      const isActivePage = currentPage === i + 1;\r\n      const className = `p-2 rounded cursor-pointer w-[30px] text-center ${isActivePage ? 'border' : ''}`;\r\n      content.push(\r\n        <li className={className} key={i + 1} onClick={(e) => handlePagerClick(i + 1)}>\r\n          {i + 1}\r\n        </li>\r\n      );\r\n    }\r\n\r\n    if (currentPage<pageCount){\r\n      content.push(<li className=\"p-2 cursor-pointer\" key={'prev_page'} onClick={(e)=>handlePagerClick(currentPage+1)}>Next</li>);\r\n    }else{\r\n      content.push(<li className=\"p-2 cursor-default\" key={'prev_page'}>Next</li>);\r\n    }\r\n\r\n    return content;\r\n  }\r\n\r\n  const generatePagerStats = () => {\r\n    let totalEntries = cloneEnterpriseMembers.length;\r\n    let ending = currentPage*showOnly;\r\n    let starting = ending - (showOnly-1);\r\n\r\n    if (ending > totalEntries){\r\n      ending = totalEntries;\r\n    }\r\n\r\n    return \"Showing \"+ starting + \" to \" + ending + \" of \" +  totalEntries + \" entries\";\r\n  }\r\n\r\n  const handlePagerClick = (page) => {\r\n    setCurrentPage(page);\r\n\r\n    let members = cloneEnterpriseMembers;\r\n    let arrayToLoad = (page-1)*showOnly;\r\n\r\n    members = members.slice(arrayToLoad, members.length);\r\n    members = limitDisplay(members,showOnly);\r\n\r\n    setDisplayEnterpriseMembers(members);\r\n  }\r\n\r\n  const handleShowPerPage = (event) => {\r\n    setShowOnly(event.target.value);\r\n    setCurrentPage(1);\r\n  }\r\n\r\n\r\n  const handleEdit = (user_id, first_name, last_name, email) => {\r\n    let params = {};\r\n    params['user_id'] = user_id;\r\n    params['first_name'] = first_name;\r\n    params['last_name'] = last_name;\r\n    params['email'] = email;\r\n\r\n    handleShowEditMemberModal(true,params);\r\n  }\r\n\r\n  return (\r\n    <>\r\n    <div className='w-full'>\r\n      <div className=\"block md:grid md:grid-cols-3 md:gap-4 text-[12px]\">\r\n        <div className=\"text-left md:col-span-1\">\r\n          <input className=\"w-full px-3 py-2 mb-2 border border-gray-300 rounded fs-exclude\"\r\n          type=\"text\"\r\n          id=\"ent_search\"\r\n          name=\"search\"\r\n          placeholder=\"Search by Name or Email\"\r\n          value={search}\r\n          onChange={(e) => {\r\n            handleSearch()\r\n            setSearch(e.target.value);\r\n          }}\r\n          />\r\n        </div>\r\n        <div className=\"text-left md:col-span-1\">\r\n          <motion.button\r\n          className=\"bg-sky-500 w-full md:w-48 text-white font-bold py-2 px-6 rounded-md proceed-pmt\"\r\n          whileHover={{ backgroundColor: \"#49b1df\" }}\r\n          whileTap={{ scale: 0.9 }}\r\n          onClick={()=> handleShowAddMember()}\r\n          >\r\n          + Add Member\r\n          </motion.button>\r\n\r\n        </div>\r\n        {enterpriseMembers.length > 0 ?\r\n        <div className=\"text-center md:text-right md:col-span-1 my-2 text-[11px]\">\r\n          Show <select className=\"border rounded-md\" onChange={(e) => handleShowPerPage(e)}>\r\n            <option value=\"5\">5</option>\r\n            <option value=\"10\">10</option>\r\n            <option value=\"15\">15</option>\r\n            <option value=\"20\">20</option>\r\n          </select> entries\r\n        </div>\r\n        : ''}\r\n      </div>\r\n\r\n      <div className=\"overflow-x-scroll lg:overflow-x-visible overflow-y-visible container-full-width cm-scrollbar pb-1 min-h-[400px]\">\r\n        {displayEnterpriseMembers && displayEnterpriseMembers.length ?\r\n        <table className=\"subs-table min-w-full divide-y bg-gray-50 divide-gray-200 min-h-[50px]\">\r\n          <thead>\r\n            <tr className=\"sub_tbl h-3\">\r\n              <td className=\"px-6 py-2 bg-gray-50 text-center text-[12px] font-medium bg-gray-200 text-gray-500 uppercase tracking-wider border\">Fullname</td>\r\n              <td className=\"px-6 py-2 bg-gray-50 text-center text-[12px] font-medium bg-gray-200 text-gray-500 uppercase tracking-wider border\">Email</td>\r\n              <td className=\"px-6 py-2 bg-gray-50 text-center text-[12px] font-medium bg-gray-200 text-gray-500 uppercase tracking-wider border\">Status</td>\r\n              <td className=\"px-6 py-2 bg-gray-50 text-center text-[12px] font-medium bg-gray-200 text-gray-500 uppercase tracking-wider border\">Date Added</td>\r\n              {(currentPlan.toLowerCase() === 'enterprise' && currentPlanName.toLowerCase() !== 'enterprise') ?\r\n                <td className=\"px-6 py-2 bg-gray-50 text-center text-[12px] font-medium bg-gray-200 text-gray-500 uppercase tracking-wider border\">Total Usage</td>\r\n              :\r\n                <td className=\"px-6 py-2 bg-gray-50 text-center text-[12px] font-medium bg-gray-200 text-gray-500 uppercase tracking-wider border\">Total Token Used</td>\r\n              }\r\n              \r\n              <td className=\"px-6 py-2 bg-gray-50 text-center text-[12px] font-medium bg-gray-200 text-gray-500 uppercase tracking-wider border\">Actions</td>\r\n            </tr>\r\n          </thead>\r\n          <tbody className=\"bg-white divide-y divide-gray-200\">\r\n            {displayEnterpriseMembers?.map((sub, index) => (\r\n              <tr key={index} className=\"sub_tbl h-3\">\r\n              <td className=\"px-6 py-2 bg-gray-50 text-center text-[12px] text-gray-500 uppercase tracking-wider border font-medium\">{sub.first_name} {sub.last_name}</td>\r\n              <td className=\"px-6 py-2 bg-gray-50 text-center text-[12px] text-gray-500 uppercase tracking-wider border font-medium\">{sub.email}</td>\r\n              <td className=\"px-6 py-2 bg-gray-50 text-center text-[12px] text-gray-500 uppercase tracking-wider border font-medium\">{sub.status}</td>\r\n              <td className=\"px-6 py-2 bg-gray-50 text-center text-[12px] text-gray-500 uppercase tracking-wider border font-medium\">{formatDate(sub.created_at)}</td>\r\n              <td className=\"px-6 py-2 bg-gray-50 text-center text-[12px] text-gray-500 tracking-wider border font-medium\">\r\n                {\r\n                  (currentPlan.toLowerCase() === 'enterprise' && currentPlanName.toLowerCase() !== 'enterprise') ?\r\n                  <>\r\n                    <p>Tokens: {sub.total_token}</p>\r\n                    <p>Flux: {sub.total_flux_prompt}</p>\r\n                    <p>o1: {sub.total_o1_prompt}</p>\r\n                  </>\r\n                :\r\n                  <p>{sub.total_token}</p>\r\n                }\r\n                \r\n              </td>\r\n              <td className=\"px-6 py-2 bg-gray-50 text-center text-[12px] text-gray-500 uppercase tracking-wider border font-medium\">\r\n                { sub.status === 'active' ?\r\n                <div className=\"text-[12px]\">\r\n                  <Menu as=\"div\" className=\"relative inline-block text-center w-full text-[12px]\">\r\n                    <div className='w-full'>\r\n                      <Menu.Button className=\"inline-flex w-full justify-center rounded-md bg-sky-500/100 px-4 py-2 font-medium text-white hover:bg-[#49b1df]\">\r\n                        Options\r\n                      </Menu.Button>\r\n                    </div>\r\n                    <Transition\r\n                      as={Fragment}\r\n                      enter=\"transition ease-out duration-100\"\r\n                      enterFrom=\"transform opacity-0 scale-95\"\r\n                      enterTo=\"transform opacity-100 scale-100\"\r\n                      leave=\"transition ease-in duration-75\"\r\n                      leaveFrom=\"transform opacity-100 scale-100\"\r\n                      leaveTo=\"transform opacity-0 scale-95\"\r\n                    >\r\n                      <Menu.Items className={`${\r\n                        index < 3 ? \"dp-top\" : \"dp-bot\"\r\n                      } z-[9999] absolute w-50 origin-top-right divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none text-center min-w-full text-center`}>\r\n                      <div className=\"text-center min-w-full\">\r\n                        <Menu.Item>\r\n                          <button\r\n                          className=\"group min-w-full items-center px-4 py-2 text-left border-b border-b-slate-100 text-gray-900\"\r\n                          onClick={(user_id, first_name, last_name, email) => handleEdit(sub.user_id, sub.first_name, sub.last_name, sub.email)}\r\n                          >\r\n                            Edit Info\r\n                          </button>\r\n                      </Menu.Item>\r\n                        <Menu.Item>\r\n                          <ResendPasswordMember member_user_id={sub.user_id} email={sub.email} />\r\n                        </Menu.Item>\r\n                        <Menu.Item>\r\n                          <DeleteMember member_user_id={sub.user_id} email={sub.email} handleReloadMembers={handleReloadMembers} />\r\n                        </Menu.Item>\r\n                      </div>\r\n                      </Menu.Items>\r\n                    </Transition>\r\n                  </Menu>\r\n                </div> : \"\" }\r\n              </td>\r\n            </tr>\r\n            ))}\r\n          </tbody>\r\n        </table>\r\n        : <div>\r\n          <span className=\"text-[12px] text-gray-600\"><FaInfoCircle className=\"inline text-lg mr-1\"/>No members found.</span>\r\n          </div>\r\n        }\r\n      </div>\r\n      <div className=\"py-2 text-[11px]\">\r\n        <span className=\"block md:flex float-left w-full md:w-auto text-center md:text-right py-2 justify-center md:justify-normal items-center md:items-start\">\r\n          {generatePagerStats()}\r\n        </span>\r\n        <ul className=\"w-full md:w-auto flex float-none md:float-right justify-center md:justify-normal items-center md:items-end\">\r\n        {generatePager()}\r\n        </ul>\r\n      </div>\r\n\r\n    </div>\r\n    </>\r\n  );\r\n}\r\n\r\nconst EditMemberModal = ({showEditMember, setShowEditEnterprise, editMemberDetails, handleReloadMembers}) => {\r\n  const [fullName, setfullName] = useState(\"\");\r\n  const [oldFullName, setoldFullName] = useState(\"\");\r\n  const [email, setEamil] = useState(\"\");\r\n  const [origEmail, setorigEmail] = useState(\"\");\r\n  const [userId, setUserId] = useState(\"\");\r\n\r\n  const modalEditMembersOpen = () => {\r\n    let modal = document.getElementById(\"modalEditMembers\");\r\n    if (modal!==null){\r\n\t\t\tmodal.style.display = \"block\";\r\n    }\r\n  }\r\n\r\n  const modalEditMembersClose = () => {\r\n    let modal = document.getElementById(\"modalEditMembers\");\r\n    if (modal!==null){\r\n\t\t\tlet fieldEmail = document.getElementById(\"ent-edit-email\");\r\n\t\t\tfieldEmail.title = \"\";\r\n\t\t\tfieldEmail.classList.remove('ent-field-error');\r\n\r\n\t\t\tlet fieldName = document.getElementById(\"ent-edit-fullname\");\r\n\t\t\tfieldName.title = \"\";\r\n\t\t\tfieldName.classList.remove('ent-field-error');\r\n\r\n\t\t\tconsole.log(\"modal close\");\r\n\r\n\r\n\t\t\tmodal.style.display = \"none\";\r\n      setShowEditEnterprise(false);\r\n    }\r\n  }\r\n\r\n  useEffect(() => {\r\n    let first_name = editMemberDetails.first_name ? editMemberDetails.first_name : '';\r\n    let last_name = editMemberDetails.last_name ?  editMemberDetails.last_name : '';\r\n    let email = editMemberDetails.email ?  editMemberDetails.email : '';\r\n    let user_id = editMemberDetails.user_id ?  editMemberDetails.user_id : '';\r\n\r\n    setfullName(first_name + \" \" + last_name);\r\n    setoldFullName(first_name + \" \" + last_name);\r\n    setEamil(email);\r\n    setorigEmail(email);\r\n    setUserId(user_id);\r\n  }, [editMemberDetails]);\r\n\r\n  const validateEmail = (e) => {\r\n    let email = e.target.value;\r\n    let fieldEmailError = document.getElementById(\"ent-edit-email-error\");\r\n\r\n    if (email.trim()===\"\"){\r\n      fieldEmailError.innerHTML = \"Email is required\";\r\n\t\t\tfieldEmailError.style.display = \"block\";\r\n      e.target.classList.add('ent-field-error');\r\n\t\t}else if (!/\\S+@\\S+\\.\\S+/.test(email)) {\r\n      fieldEmailError.innerHTML = \"Invalid email format\";\r\n\t\t\tfieldEmailError.style.display = \"block\";\r\n      e.target.classList.add('ent-field-error');\r\n    }else{\r\n      fieldEmailError.innerHTML = \"\";\r\n\t\t\tfieldEmailError.style.display = \"none\";\r\n      e.target.classList.remove('ent-field-error');\r\n    }\r\n  };\r\n\r\n  const validateName = (e) => {\r\n    let full_name = e.target.value.trim();\r\n    let fieldFullnameError = document.getElementById(\"ent-edit-fullname-error\");\r\n\r\n    if (full_name===\"\"){\r\n      fieldFullnameError.innerHTML = \"Full Name is required\";\r\n\t\t\tfieldFullnameError.style.display = \"block\";\r\n      e.target.classList.add('ent-field-error');\r\n    }else{\r\n      fieldFullnameError.innerHTML = \"\";\r\n\t\t\tfieldFullnameError.style.display = \"none\";\r\n      e.target.classList.remove('ent-field-error');\r\n    }\r\n\t}\r\n\r\n  if (showEditMember!==undefined && showEditMember === true){\r\n    modalEditMembersOpen();\r\n  }\r\n  if (showEditMember!==undefined && showEditMember === false){\r\n    modalEditMembersClose();\r\n  }\r\n\r\n  const updateMember = async () => {\r\n    let fieldFullname = document.getElementById(\"ent-edit-fullname\");\r\n    let fieldFullnameError = document.getElementById(\"ent-edit-fullname-error\");\r\n    let fieldEmail = document.getElementById(\"ent-edit-email\");\r\n    let fieldEmailError = document.getElementById(\"ent-edit-email-error\");\r\n\r\n\t\t// fieldError2.style.display = \"block\";\r\n\r\n    if (fieldEmail.value.trim()===\"\"){\r\n      fieldEmailError.innerHTML = \"Email is required\";\r\n\t\t\tfieldEmailError.style.display = \"block\";\r\n\t\t\tfieldEmail.classList.add('ent-field-error');\r\n\t\t\treturn;\r\n\t\t}else if (!/\\S+@\\S+\\.\\S+/.test(email)) {\r\n      fieldEmailError.innerHTML = \"Invalid email format\";\r\n\t\t\tfieldEmailError.style.display = \"block\";\r\n      fieldEmail.classList.add('ent-field-error');\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n    if (fieldFullname.value.trim()===\"\"){\r\n      fieldFullnameError.innerHTML = \"Full Name is required\";\r\n\t\t\tfieldFullnameError.style.display = \"block\";\r\n      fieldFullname.classList.add('ent-field-error');\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n    document.querySelector(\".loader-container\").classList.add('active');\r\n\r\n    if (oldFullName===fieldFullname.value && origEmail === fieldEmail.value){\r\n      modalEditMembersClose();\r\n      document.querySelector(\".loader-container\").classList.remove('active');\r\n      return;\r\n    }\r\n\r\n    let url = `${process.env.REACT_APP_API_URL}/edit-enterprise-member`;\r\n    axios.post(url, {\r\n      member_user_id: userId,\r\n      member_email: fieldEmail.value,\r\n      member_fullname : fieldFullname.value,\r\n      member_old_fullname: oldFullName,\r\n      member_old_email: origEmail,\r\n    }, { headers: { 'content-type': 'application/x-www-form-urlencoded' } }).then(function(res) {\r\n      let output = res.data;\r\n\r\n      document.querySelector(\".loader-container\").classList.remove('active');\r\n\r\n      if(output.success) {\r\n        if (Array.isArray(output.data) && output.data.length > 0){\r\n          for(var i = 0; i < output.data.length; i++) {\r\n            let fieldEmail = document.getElementById(\"ent-edit-email\");\r\n            fieldEmail.title = output.data[i].error;\r\n            fieldEmail.classList.add('ent-field-error');\r\n            toastr.error(output.data[i].error);\r\n          }\r\n        }else{\r\n          modalEditMembersClose();\r\n          handleReloadMembers();\r\n          toastr.success(\"Member Updated\");\r\n        }\r\n      }else{\r\n        modalEditMembersClose();\r\n        handleReloadMembers();\r\n        toastr.error(\"Update Member Failed\");\r\n      }\r\n\r\n    }).catch(function (error) {\r\n      modalEditMembersClose();\r\n      document.querySelector(\".loader-container\").classList.remove('active');\r\n      toastr.error(\"Something went wrong. Please try again in a bit!\");\r\n    });\r\n\r\n  }\r\n\r\n  return (\r\n    <>\r\n    <div id=\"modalEditMembers\" className=\"modal z-[9999]\">\r\n      <div class=\"modal-content w-full md:w-[50%]\">\r\n        <div>\r\n          <div className=\"mb-4 flex border-b pb-2\">\r\n            <div className=\"text-blue-500 w-full md:w-2/3 mr-2 md:mr-5 font-bold\">\r\n            Edit Members\r\n            </div>\r\n            <div className=\"float-right mt-[-5px] w-full md:w-1/3\">\r\n              <span class=\"close\" onClick={()=> modalEditMembersClose()}>&times;</span>\r\n            </div>\r\n          </div>\r\n          <div className=\"border-b border-[#dddddd] mx-auto my-4 pb-4\">\r\n              <div className=\"w-full text-[12px] md:text-[12px] font-bold pb-2\">\r\n                <p>Fulname *</p>\r\n                <input type=\"text\" id=\"ent-edit-fullname\" className=\"w-full px-3 py-2 border border-gray-300 rounded\"\r\n                placeholder=\"Fullname\"\r\n                value={fullName}\r\n                onChange={(e) => {\r\n                  e.preventDefault();\r\n                  setfullName(e.target.value);\r\n                  validateName(e);\r\n                  }\r\n                }\r\n                title=\"\"\r\n                ></input>\r\n                <div class=\"member-error\" id=\"ent-edit-fullname-error\"></div>\r\n              </div>\r\n              <div className=\"w-full text-[12px] md:text-[12px] font-bold pb-2\">\r\n                <p>Email *</p>\r\n                <input type=\"text\" id=\"ent-edit-email\" className=\"w-full px-3 py-2 border border-gray-300 rounded\"\r\n                placeholder=\"Email\"\r\n                value={email}\r\n                onChange={(e) => {\r\n                  e.preventDefault();\r\n                  setEamil(e.target.value);\r\n                  validateEmail(e);\r\n                  }\r\n                }\r\n                title=\"\"\r\n                ></input>\r\n                <div class=\"member-error\" id=\"ent-edit-email-error\"></div>\r\n              </div>\r\n          </div>\r\n          <div className=''>\r\n            <div className=\"text-right\">\r\n              <input type=\"button\" value=\"Update Member\" className=\"border rounded font-bold bg-blue-500 text-white py-2 px-4 cursor-pointer text-[12px] md:text-[14px] mr-2\" onClick={()=> updateMember()}/>\r\n              <input type=\"button\" value=\"Close\" className=\"border rounded font-bold bg-white text-black py-2 px-4 cursor-pointer text-[12px] md:text-[14px]\" onClick={()=> modalEditMembersClose()}/>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    </>\r\n  )\r\n}\r\n\r\nconst AddMemberModal = ({showAddMember, setshowAddMember, handleReloadMembers, entMaxMembers, enterpriseMembers, isMax, isVisbleBtn, setIsVisibleBtn }) => {\r\n\r\n  const [members, setMembers] = useState([\r\n    { name: '', email: '', error: '' }\r\n  ]);\r\n  const [addMemberBtn, setAddMemberBtn] = useState(true);\r\n  const [isRemoveBtn, setRemoveBtn] = useState(true)\r\n\r\n  const addMember = () => {\r\n    setMembers(current => [...current, { name: '', email: '', error: '' }]);\r\n\r\n    let total_members_after_insert = members.length+enterpriseMembers.length;\r\n    \r\n    setIsVisibleBtn(true);\r\n    setRemoveBtn(true);\r\n    if (isMax && total_members_after_insert + 1 >= entMaxMembers) {\r\n      setAddMemberBtn(false);\r\n    }\r\n  };\r\n\r\n  const resetMember = async () => {\r\n    await setMembers(current => {\r\n      return current.filter((_, i) => i <= -1)\r\n    });\r\n    addMember();\r\n    setAddMemberBtn(true);\r\n    setRemoveBtn(true);\r\n  };\r\n\r\n  const removeMember = (index) => {\r\n    setMembers(current => {\r\n      return current.filter((_, i) => i !== index)\r\n    });\r\n\r\n    let newTotalMembers = members.length + enterpriseMembers.length - 1;\r\n\r\n    if (isMax && newTotalMembers < entMaxMembers) {\r\n      setIsVisibleBtn(true);\r\n      setAddMemberBtn(true);\r\n    }\r\n\r\n    if (isMax && members.length === 1) { \r\n      setRemoveBtn(false); \r\n    }\r\n   \r\n  };\r\n\r\n  const saveMember = async () => {\r\n\r\n    let total_members_after_insert = members.length+enterpriseMembers.length;\r\n    if (total_members_after_insert>entMaxMembers){\r\n      toastr.error(\"Members exceeded. Max. members should only \"+entMaxMembers+\" members.\");\r\n      return;\r\n    }\r\n\r\n    let hasError = await validateFields();\r\n    if (hasError){\r\n      return;\r\n    }\r\n\r\n    var url = `${process.env.REACT_APP_API_URL}/add-enterprise-member`;\r\n\r\n    if (members.length<=0){\r\n      modalAddMembersClose();\r\n      return;\r\n    }\r\n\r\n    document.querySelector(\".loader-container\").classList.add('active');\r\n\r\n    axios.post(url, {\r\n      members: JSON.stringify(members)\r\n    }, { headers: { 'content-type': 'application/x-www-form-urlencoded' } }).then(function(res) {\r\n      let output = res.data;\r\n\r\n      document.querySelector(\".loader-container\").classList.remove('active');\r\n\r\n      if(output.success) {\r\n        if (Array.isArray(output.data) && output.data.length > 0){\r\n          for(var i = 0; i < output.data.length; i++) {\r\n            let index = output.data[i].index;\r\n            let fieldEmail = document.getElementById(\"email-\"+index);\r\n            fieldEmail.title = output.data[i].error;\r\n            fieldEmail.classList.add('ent-field-error');\r\n\r\n            let fieldError2 = document.getElementById(\"error2-\"+index);\r\n            fieldError2.style.display = \"block\";\r\n            fieldError2.innerHTML = output.data[i].error;\r\n          }\r\n        }else{\r\n          toastr.success(\"Members Added\");\r\n          modalAddMembersClose();\r\n          handleReloadMembers();\r\n          resetMember();\r\n        }\r\n      }else{\r\n        toastr.error(\"Adding Members Failed\");\r\n        modalAddMembersClose();\r\n        handleReloadMembers();\r\n        resetMember();\r\n      }\r\n\r\n    }).catch(function (error) {\r\n      modalAddMembersClose();\r\n      document.querySelector(\".loader-container\").classList.remove('active');\r\n      toastr.error(\"Something went wrong. Please try again in a bit!\");\r\n    });\r\n\r\n  }\r\n\r\n  const removeLastMember = () => {\r\n    if (members.length>0){\r\n      let lastIndex = members.length - 1;\r\n      removeMember(lastIndex);\r\n    }\r\n\r\n  }\r\n\r\n  const modalAddMembersOpen = () => {\r\n    let modal = document.getElementById(\"modalAddMembers\");\r\n    if (modal!==null){\r\n      modal.style.display = \"block\";\r\n    }\r\n  }\r\n\r\n  const modalAddMembersClose = () => {\r\n    let modal = document.getElementById(\"modalAddMembers\");\r\n    if (modal!==null){\r\n      modal.style.display = \"none\";\r\n      setshowAddMember(false);\r\n    }\r\n  }\r\n\r\n\r\n  const validateEmail = (e, index) => {\r\n    let email = e.target.value;\r\n    let isValid = true;\r\n    let fieldName = document.getElementById(\"name-\"+index);\r\n    let full_name = fieldName.value.trim();\r\n    let fieldError1 = document.getElementById(\"error1-\"+index);\r\n    let fieldError2 = document.getElementById(\"error2-\"+index);\r\n\r\n    if (email.trim()===\"\"){\r\n      e.target.title = \"Email is required\";\r\n      e.target.classList.add('ent-field-error');\r\n      fieldError2.style.display = \"block\";\r\n      fieldError2.innerHTML = \"Email is required\";\r\n      isValid = false;\r\n    }else if (!/\\S+@\\S+\\.\\S+/.test(email)) {\r\n      e.target.title = \"Invalid email format\";\r\n      e.target.classList.add('ent-field-error');\r\n      fieldError2.style.display = \"block\";\r\n      fieldError2.innerHTML = \"Invalid email format\";\r\n      isValid = false;\r\n    }\r\n\r\n    if (isValid){\r\n      e.target.title = \"\";\r\n      e.target.classList.remove('ent-field-error');\r\n      fieldError1.style.display = \"none\";\r\n      fieldError1.innerHTML = \"\";\r\n      fieldError2.style.display = \"none\";\r\n      fieldError2.innerHTML = \"\";\r\n    }\r\n\r\n    if (isValid && full_name==='') {\r\n      fieldName.title = \"Full Name is required\";\r\n      fieldName.classList.add('ent-field-error');\r\n      fieldError1.style.display = \"block\";\r\n      fieldError1.innerHTML = \"Full Name is required\";\r\n    }\r\n  };\r\n\r\n  const validateName = (e, index) => {\r\n    let full_name = e.target.value.trim();\r\n    let fieldEmail = document.getElementById(\"email-\"+index).value;\r\n    let fieldError1 = document.getElementById(\"error1-\"+index);\r\n\r\n    if (full_name===\"\" && fieldEmail!==''){\r\n      e.target.title = \"Full Name is required\";\r\n      e.target.classList.add('ent-field-error');\r\n      fieldError1.style.display = \"block\";\r\n      fieldError1.innerHTML = \"Full Name is required\";\r\n    }else{\r\n      e.target.title = \"\";\r\n      e.target.classList.remove('ent-field-error');\r\n      fieldError1.style.display = \"none\";\r\n      fieldError1.innerHTML = \"\";\r\n\r\n    }\r\n  }\r\n\r\n  const validateFields = async () => {\r\n    let i = 0;\r\n    let hasError = false;\r\n    let fieldName = \"\";\r\n    let fieldEmail = \"\";\r\n    let full_name = \"\";\r\n    let fieldError1 = \"\";\r\n    let fieldError2 = \"\";\r\n    let server_error = \"\";\r\n    let doesExist = false;\r\n\r\n    while (i < members.length) {\r\n      fieldName = document.getElementById(\"name-\"+i);\r\n      fieldEmail = document.getElementById(\"email-\"+i);\r\n      fieldError1 = document.getElementById(\"error1-\"+i);\r\n      fieldError2 = document.getElementById(\"error2-\"+i);\r\n\r\n      let email = members[i].email.trim();\r\n      full_name = members[i].name.trim();\r\n      server_error = members[i].error.trim();\r\n\r\n      fieldError1.style.display = \"none\";\r\n      fieldError2.style.display = \"none\";\r\n      fieldName.title = \"\";\r\n      fieldName.classList.remove('ent-field-error');\r\n      fieldEmail.title = \"\";\r\n      fieldEmail.classList.remove('ent-field-error');\r\n\r\n      if (email===\"\") {\r\n        fieldEmail.title = \"Email is required\";\r\n        fieldEmail.classList.add('ent-field-error');\r\n        fieldError2.style.display = \"block\";\r\n        fieldError2.innerHTML = \"Email is required\"\r\n        hasError = true;\r\n      } else if (!/\\S+@\\S+\\.\\S+/.test(email)) {\r\n        fieldEmail.title = \"Invalid email format\";\r\n        fieldEmail.classList.add('ent-field-error');\r\n        fieldError2.style.display = \"block\";\r\n        fieldError2.innerHTML = \"Invalid email format\"\r\n        hasError = true;\r\n      }\r\n\r\n      if (server_error!==\"\"){\r\n        fieldEmail.title = server_error;\r\n        fieldEmail.classList.add('ent-field-error');\r\n        hasError = true;\r\n      }\r\n\r\n      if (full_name===\"\"){\r\n        fieldName.title = \"Full Name is required\";\r\n        fieldName.classList.add('ent-field-error');\r\n        fieldError1.innerHTML = \"Full Name is required\"\r\n        fieldError1.style.display = \"block\";\r\n        hasError = true;\r\n      }\r\n\r\n      if (!hasError){\r\n        doesExist = members.filter(function(item){\r\n          return ((item.email.trim()===email))\r\n        })\r\n\r\n        if (doesExist.length>1){\r\n          fieldEmail.title = \"Duplicate email\";\r\n          fieldEmail.classList.add('ent-field-error');\r\n          fieldError2.innerHTML = \"Duplicate email\"\r\n          fieldError2.style.display = \"block\";\r\n          hasError = true;\r\n        }\r\n      }\r\n\r\n      i++;\r\n    }\r\n\r\n    return hasError;\r\n  }\r\n\r\n  if (showAddMember!==undefined && showAddMember === true){\r\n    modalAddMembersOpen();\r\n  }\r\n  if (showAddMember!==undefined && showAddMember === false){\r\n    modalAddMembersClose();\r\n  }\r\n\r\n  return (\r\n    <>\r\n\t\t<div id=\"modalAddMembers\" className=\"modal z-[9999]\">\r\n\t\t\t<div class=\"modal-content w-full md:w-[60%]\">\r\n        <div>\r\n          <div className=\"mb-4 flex border-b pb-2\">\r\n            <div className=\"text-blue-500 w-full md:w-2/3 mr-2 md:mr-5 font-bold\">\r\n            Add Members\r\n            </div>\r\n            <div className=\"float-right mt-[-5px] w-full md:w-1/3\">\r\n              <span class=\"close\" onClick={()=> modalAddMembersClose()}>&times;</span>\r\n            </div>\r\n          </div>\r\n          <div className=\"text-[11px] pb-2\">\r\n          <b>Note:</b> The password for each member will be sent to their email address\r\n          </div>\r\n          <div className=\"\">\r\n            <div className=\"border rounded-tr rounded-tl flex p-2\">\r\n              <div className=\"w-1/2 text-[12px] md:text-[12px] font-bold\">\r\n              Full Name\r\n              </div>\r\n              <div className=\"w-1/2 ml-[-4px] text-[12px] md:text-[12px] font-bold\">\r\n              Email\r\n              </div>\r\n            </div>\r\n            {members?.map((mem, index) => (\r\n            <div className=\"flex py-2 pl-2 bg-gray-100\">\r\n              <div className=\"w-1/2 mr-2 text-[11px] md:text-[14px]\">\r\n                <input type=\"text\" id={\"name-\"+index} index={index} className=\"w-full px-3 py-2 border border-gray-300 rounded fs-exclude\" placeholder=\"Enter Full Name\" value={mem.name}\r\n                onChange={(e) => {\r\n                  setMembers(s => {\r\n                    const newArr = s.slice();\r\n                    newArr[index].name = e.target.value;\r\n                    validateName(e, index);\r\n                    return newArr;\r\n                  });\r\n                }}\r\n                title=\"\"\r\n                ></input>\r\n                <div class=\"member-error\" id={\"error1-\"+index}></div>\r\n              </div>\r\n              <div className=\"w-1/2 mr-2 text-[11px] md:text-[14px]\">\r\n                <input type=\"text\" id={\"email-\"+index} index={index} className=\"w-full px-3 py-2 border border-gray-300 rounded fs-exclude\" placeholder=\"Enter Email\" value={mem.email}\r\n                onChange={(e) => {\r\n                  setMembers(s => {\r\n                    const newArr = s.slice();\r\n                    newArr[index].email = e.target.value;\r\n                    validateEmail(e, index)\r\n                    return newArr;\r\n                  });\r\n                }}\r\n                title=\"\"\r\n                ></input>\r\n                <div class=\"member-error\" id={\"error2-\"+index}></div>\r\n              </div>\r\n              <div className=\"w-1/20 text-right pt-2\">\r\n                <button onClick={() => removeMember(index)}>\r\n                  <span><FaTrash className=\"text-lg mr-2 text-red-500\" /></span>\r\n                </button>\r\n              </div>\r\n            </div>\r\n            ))}\r\n          </div>\r\n          <div className={`${isMax && !addMemberBtn ? 'justify-end' : (!isVisbleBtn ? 'justify-end' : 'justify-between')} flex border-b border-[#dddddd] mx-auto my-4 pb-4`}>\r\n          {addMemberBtn && isVisbleBtn && (\r\n              <input \r\n                type=\"button\" \r\n                value=\"Add More\" \r\n                className=\"border rounded font-bold bg-white py-2 px-4 cursor-pointer text-[12px] md:text-[14px]\" \r\n                onClick={() => addMember()} \r\n              />\r\n            )}\r\n          {isRemoveBtn && (\r\n            <input type=\"button\" value=\"Remove Last\" className=\"border rounded font-bold bg-white float-right py-2 px-4 cursor-pointer text-[12px] md:text-[14px]\" onClick={()=> removeLastMember()}/>\r\n          )}\r\n          </div>\r\n          {isRemoveBtn && (\r\n            <div className=\"text-right\">\r\n              <input type=\"button\" value=\"Add Member\" className=\"border rounded font-bold bg-blue-500 text-white py-2 px-4 cursor-pointer text-[12px] md:text-[14px]\" onClick={()=> saveMember()}/>\r\n            </div>\r\n          )} \r\n        </div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n    </>\r\n  );\r\n}\r\n\r\nconst DeleteMember = ({ member_user_id, email, handleReloadMembers }) => {\r\n  const [isShowModal, setIsShowModal] = useState(false);\r\n  const showDeleteMemberModal = () => {\r\n    setIsShowModal(true);\r\n  };\r\n  const closeModal = () => {\r\n    setIsShowModal(false);\r\n  };\r\n\r\n  const deleteMember = (event) => {\r\n    document.querySelector(\".loader-container\").classList.add('active');\r\n    closeModal();\r\n\r\n    axios.post(`${process.env.REACT_APP_API_URL}/delete-enterprise-member`, {\r\n      member_user_id: member_user_id,\r\n      member_email: email\r\n    }, { headers: { 'content-type': 'application/x-www-form-urlencoded' } }).then(function(res){\r\n      let output = res.data;\r\n      if(output.success) {\r\n        document.querySelector(\".loader-container\").classList.remove('active');\r\n        toastr.success(\"Delete success.\");\r\n        handleReloadMembers();\r\n        return;\r\n      }\r\n      document.querySelector(\".loader-container\").classList.remove('active');\r\n      if(output.data) toastr.error(output.data.msg);\r\n    });\r\n\r\n\r\n  };\r\n  return (\r\n    <>\r\n      <button\r\n      className={`hover:bg-sky-500/100 hover:text-white text-gray-900 group min-w-full items-center px-4 py-2 text-left`}\r\n      onClick={showDeleteMemberModal}\r\n      >\r\n        Delete\r\n      </button>\r\n      <Transition appear show={isShowModal} as={Fragment}>\r\n        <Dialog as=\"div\" className=\"relative z-10\" onClose={closeModal}>\r\n          <Transition.Child\r\n            as={Fragment}\r\n            enter=\"ease-out duration-300\"\r\n            enterFrom=\"opacity-0\"\r\n            enterTo=\"opacity-100\"\r\n            leave=\"ease-in duration-200\"\r\n            leaveFrom=\"opacity-100\"\r\n            leaveTo=\"opacity-0\"\r\n          >\r\n            <div className=\"fixed inset-0 bg-black bg-opacity-25\" />\r\n          </Transition.Child>\r\n\r\n          <div className=\"fixed inset-0 overflow-y-auto\">\r\n            <div className=\"flex min-h-full items-center justify-center p-4 text-center\">\r\n              <Transition.Child\r\n                as={Fragment}\r\n                enter=\"ease-out duration-300\"\r\n                enterFrom=\"opacity-0 scale-95\"\r\n                enterTo=\"opacity-100 scale-100\"\r\n                leave=\"ease-in duration-200\"\r\n                leaveFrom=\"opacity-100 scale-100\"\r\n                leaveTo=\"opacity-0 scale-95\"\r\n              >\r\n                <Dialog.Panel className=\"w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all\">\r\n                  <Dialog.Title\r\n                    as=\"h3\"\r\n                    className=\"text-red-800 w-full md:w-2/3 mr-2 md:mr-5 font-bold\"\r\n                  >\r\n                    Delete Member\r\n                  </Dialog.Title>\r\n                  <div className=\"mt-2\">\r\n                    <p className=\"text-sm text-gray-500 break-words border-t border-b border-[#dddddd] py-2\">\r\n                      <AiFillWarning className=\"inline text-sm mr-1 text-red-800\"/>Are you sure you want to delete <strong>{email}</strong> account?\r\n                    </p>\r\n                  </div>\r\n\r\n                  <div className=\"mt-4 text-right\">\r\n                    <button\r\n                      type=\"button\"\r\n                      className=\"inline-flex justify-center rounded-md border border-transparent bg-red-500 px-4 py-2 mr-2 text-sm font-medium text-white hover:bg-[#d30101]\"\r\n                      onClick={deleteMember}\r\n                    >\r\n                      Delete Member\r\n                    </button>\r\n                    <button\r\n                      type=\"button\"\r\n                      className=\"inline-flex justify-center rounded-md border border-transparent bg-gray-100 px-4 py-2 text-sm font-medium text-black hover:bg-gray-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-gray-500 focus-visible:ring-offset-2\"\r\n                      onClick={closeModal}\r\n                    >\r\n                      Close\r\n                    </button>\r\n                  </div>\r\n                </Dialog.Panel>\r\n              </Transition.Child>\r\n            </div>\r\n          </div>\r\n        </Dialog>\r\n      </Transition>\r\n    </>\r\n  )\r\n}\r\n\r\nconst ResendPasswordMember = ({ member_user_id, email }) => {\r\n  const [isShowModal, setIsShowModal] = useState(false);\r\n  const showDeleteMemberModal = () => {\r\n    setIsShowModal(true);\r\n  };\r\n  const closeModal = () => {\r\n    setIsShowModal(false);\r\n  };\r\n\r\n  const resendPassword = (event) => {\r\n    document.querySelector(\".loader-container\").classList.add('active');\r\n    closeModal();\r\n\r\n    axios.post(`${process.env.REACT_APP_API_URL}/resend-pass-enterprise-member`, {\r\n      member_user_id: member_user_id,\r\n      member_email: email\r\n    }, { headers: { 'content-type': 'application/x-www-form-urlencoded' } }).then(function(res){\r\n      let output = res.data;\r\n      if(output.success) {\r\n        toastr.success(\"New Password sent.\");\r\n        document.querySelector(\".loader-container\").classList.remove('active');\r\n        return;\r\n      }\r\n      document.querySelector(\".loader-container\").classList.remove('active');\r\n      if(output.data) toastr.error(output.data.msg);\r\n    });\r\n\r\n\r\n  };\r\n  return (\r\n    <>\r\n      <button\r\n      className={`hover:bg-sky-500/100 hover:text-white text-gray-900 group min-w-full items-center px-4 py-2 text-left`}\r\n      onClick={showDeleteMemberModal}\r\n      >\r\n        Resend Password\r\n      </button>\r\n      <Transition appear show={isShowModal} as={Fragment}>\r\n        <Dialog as=\"div\" className=\"relative z-10\" onClose={closeModal}>\r\n          <Transition.Child\r\n            as={Fragment}\r\n            enter=\"ease-out duration-300\"\r\n            enterFrom=\"opacity-0\"\r\n            enterTo=\"opacity-100\"\r\n            leave=\"ease-in duration-200\"\r\n            leaveFrom=\"opacity-100\"\r\n            leaveTo=\"opacity-0\"\r\n          >\r\n            <div className=\"fixed inset-0 bg-black bg-opacity-25\" />\r\n          </Transition.Child>\r\n\r\n          <div className=\"fixed inset-0 overflow-y-auto\">\r\n            <div className=\"flex min-h-full items-center justify-center p-4 text-center\">\r\n              <Transition.Child\r\n                as={Fragment}\r\n                enter=\"ease-out duration-300\"\r\n                enterFrom=\"opacity-0 scale-95\"\r\n                enterTo=\"opacity-100 scale-100\"\r\n                leave=\"ease-in duration-200\"\r\n                leaveFrom=\"opacity-100 scale-100\"\r\n                leaveTo=\"opacity-0 scale-95\"\r\n              >\r\n                <Dialog.Panel className=\"w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all\">\r\n                  <Dialog.Title\r\n                    as=\"h3\"\r\n                    className=\"text-lg font-medium leading-6 text-gray-900\"\r\n                  >\r\n                    Resend Password\r\n                  </Dialog.Title>\r\n                  <div className=\"mt-2\">\r\n                    <p className=\"text-sm text-gray-500 break-words\">\r\n                      Do you want to proceed with sending the new password to <strong>{email}</strong>?\r\n                    </p>\r\n                  </div>\r\n\r\n                  <div className=\"mt-4 text-right\">\r\n                    <button\r\n                      type=\"button\"\r\n                      className=\"inline-flex justify-center rounded-md border border-transparent bg-red-500 px-4 py-2 mr-2 text-sm font-medium text-white hover:bg-[#d30101]\"\r\n                      onClick={resendPassword}\r\n                    >\r\n                      Resend Password\r\n                    </button>\r\n                    <button\r\n                      type=\"button\"\r\n                      className=\"inline-flex justify-center rounded-md border border-transparent bg-gray-100 px-4 py-2 text-sm font-medium text-black hover:bg-gray-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-gray-500 focus-visible:ring-offset-2\"\r\n                      onClick={closeModal}\r\n                    >\r\n                      Close\r\n                    </button>\r\n                  </div>\r\n                </Dialog.Panel>\r\n              </Transition.Child>\r\n            </div>\r\n          </div>\r\n        </Dialog>\r\n      </Transition>\r\n    </>\r\n  )\r\n}\r\n\r\nexport default Manage;", "import { useEffect, useState } from 'react';\r\nimport { motion } from \"framer-motion\";\r\nimport axios from 'axios';\r\nimport aiproLogo from '../assets/images/AIPRO.svg';\r\nimport { FaInfoCircle } from 'react-icons/fa';\r\nimport { Auth } from '../core/utils/auth';\r\nimport toastr from 'toastr';\r\nimport 'toastr/build/toastr.min.css';\r\n\r\nvar price_per_member = '0';\r\nvar user_id = '';\r\n\r\nexport function AddMoreMemberModal ({showAddMoreMember, setshowAddMoreMember, setMoreToAddMember, setMoreToAddMemberTotalAmount, setShowCompletePurchase}) {\r\n  const [membersToAdd, setMembersToAdd] = useState(1);\r\n  const [totalAmount, setTotalAmount] = useState(0);\r\n  const [paymentInterval, setpaymentInterval] = useState(\"\");\r\n\r\n  const auth = Auth();\r\n\r\n  useEffect(() => {\r\n    if (auth !== undefined && auth.plan==='enterprise'){\r\n      price_per_member = auth.price_per_member;\r\n\r\n      if (auth.interval.toLowerCase()===\"monthly\"){\r\n        setpaymentInterval(\"MONTH\");        \r\n      }else{\r\n        setpaymentInterval(\"YEAR\");       \r\n      }\r\n\r\n    }\r\n  }, [auth]);\r\n\r\n\r\n  const modalAddMoreMembersOpen = () => {\r\n    let modal = document.getElementById(\"modalAddMoreMembers\");\t\t\r\n    if (modal!==null){\r\n      modal.style.display = \"block\";\r\n    }\r\n  }\r\n\r\n  const modalAddMoreMembersClose = () => {\r\n    let modal = document.getElementById(\"modalAddMoreMembers\");\t\t\r\n    if (modal!==null){\r\n      modal.style.display = \"none\";\t\t \r\n      setshowAddMoreMember(false);\r\n    }\r\n  }\r\n\r\n  const handlePlus = () => {\r\n    setMembersToAdd(membersToAdd+1);\r\n  }\r\n\r\n  const handleMinus = () => {\r\n    if (membersToAdd>1){\r\n      setMembersToAdd(membersToAdd-1);\r\n    }\r\n  }\r\n  \r\n  useEffect(() => {\r\n    toastr.options = {\r\n      positionClass: 'toast-top-center'\r\n    };\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    let amount = membersToAdd * price_per_member;\r\n    setTotalAmount(amount);\r\n  }, [membersToAdd]);\r\n\r\n  useEffect(() => {\r\n    setMembersToAdd(1);\r\n\r\n    let amount = membersToAdd * price_per_member;\r\n    setTotalAmount(amount);\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps    \r\n  }, [showAddMoreMember]);\r\n\r\n  const handleUupgradeNow = () => {\r\n    window.location.href = '/upgrade-ent/'+membersToAdd;\r\n    return;\r\n\r\n    // setMoreToAddMember(membersToAdd);\r\n    // setMoreToAddMemberTotalAmount(totalAmount);\r\n\r\n    // setshowAddMoreMember(false);\r\n    // setShowCompletePurchase(true)\r\n  }\r\n\r\n  if (showAddMoreMember!==undefined && showAddMoreMember === true){\r\n    modalAddMoreMembersOpen();\r\n  }\r\n  if (showAddMoreMember!==undefined && showAddMoreMember === false){\r\n    modalAddMoreMembersClose();\r\n  }\r\n\r\n  return (\r\n    <>\r\n\t\t<div id=\"modalAddMoreMembers\" className=\"modal z-[9999]\">\r\n\t\t\t<div class=\"modal-content w-full md:w-[60%] max-w-[90%] p-3 md:p-4\">\r\n        <div>\r\n          <div className=\"mb-4 flex border-b pb-2\">\r\n            <div className=\"text-blue-500 w-full md:w-2/3 mr-2 md:mr-5 font-bold\">\r\n            Add More Members\r\n            </div>\r\n            <div className=\"float-right mt-[-5px] w-full md:w-1/3\">\r\n              <span class=\"close\" onClick={()=> modalAddMoreMembersClose()}>&times;</span>\r\n            </div>\r\n          </div>\r\n          <div className=\"p-2 md:p-4 border rounded text-sm text-center\">\r\n            <div>Your enterprise account has hit its maximum user capacity.</div>\r\n            <div>Add more members to your Enterprise Account.</div>\r\n            <div className=\"py-4 text-center\">\r\n              <span className=\"font-bold text-[12px] sm:text-sm inline px-1 sm:px-2\">Add</span>\r\n              <div className=\"border rounded px-2 py-4 inline\">\r\n                <input className=\"bg-blue-500 rounded px-3 py-2 m-2 text-white cursor-pointer\" type=\"button\" value=\"-\" onClick={()=>handleMinus()}/>\r\n                <span className=\"text-blue-500 p-2 mx-auto font-bold\">{membersToAdd}</span>\r\n                <input className=\"bg-blue-500 rounded px-3 py-2 m-2 text-white cursor-pointer\" type=\"button\" value=\"+\" onClick={()=>handlePlus()} />\r\n              </div>\r\n              <span className=\"font-bold text-[12px] sm:text-sm inline px-1 sm:px-2\">Member/s</span>\r\n            </div>\r\n            <div className=\"font-bold text-sm\">\r\n              Total Amount: ${totalAmount}\r\n            </div>\r\n            <div className=\"font-bold text-sm\">\r\n              PER {paymentInterval}\r\n            </div>\r\n            <div>\r\n              <motion.button\r\n              className=\"bg-sky-600 w-full md:w-70 text-white font-bold my-4 py-2 px-6 rounded proceed-pmt\"\r\n              whileHover={{ backgroundColor: \"#49b1df\" }}\r\n              whileTap={{ scale: 0.9 }}\r\n              onClick={()=> handleUupgradeNow()}\r\n              >\r\n              UPGRADE NOW\r\n              </motion.button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n    </>\r\n  );\r\n}\r\n\r\nexport function MemberCompletePurchase ({moreToAddMember, moreToAddMemberTotalAmount, setShowCompletePurchase, showCompletePurchase}) {\r\n\r\n  const auth = Auth();\r\n\r\n  useEffect(() => {\r\n    if (auth !== undefined && auth.plan==='enterprise'){\r\n      user_id = auth.user_id;\r\n    }\r\n  }, [auth]);\r\n\r\n  const modalOpen = () => {\r\n    let modal = document.getElementById(\"modalComplete\");\t\t\r\n    if (modal!==null){\r\n      modal.style.display = \"block\";\t\t\r\n    }\r\n  }\r\n\r\n  const modalClose = () => {\r\n    let modal = document.getElementById(\"modalComplete\");\t\t\r\n    if (modal!==null){\r\n      modal.style.display = \"none\";\t\t \r\n      setShowCompletePurchase(false);\r\n    }\r\n  }\r\n\r\n  const submitPaymentInformation = () => {\r\n    window.location.href = '/payment-reference';\r\n    return;\r\n  }\r\n\r\n  const sendViaEmail = () => {\r\n    var members = moreToAddMember;\r\n    var total_amount = moreToAddMemberTotalAmount;\r\n    var url = `${process.env.REACT_APP_API_URL}/t/send-enterprise-payment-info`;\r\n\r\n    modalClose();    \r\n    document.querySelector(\".loader-container\").classList.add('active');\r\n\r\n    axios.post(url, {\r\n      members,\r\n      total_amount\r\n    }, { headers: { 'content-type': 'application/x-www-form-urlencoded' } }).then(function(res) {\r\n      let output = res.data;\r\n\r\n      document.querySelector(\".loader-container\").classList.remove('active');\r\n\r\n      if(output.success) {\r\n        toastr.success(\"Email sent to \"+output.data);\r\n      }else{\r\n        toastr.error(\"Email Failed.\");\r\n      }\r\n    }).catch(function (error) {\r\n      document.querySelector(\".loader-container\").classList.remove('active');\r\n      if (error.response && error.response.status===429) {\r\n        document.querySelector(\".loader-container\").classList.remove('active');\r\n        toastr.error(\"Sorry, too many requests. Please try again in a bit!\");\r\n      }\r\n    });\r\n  }\r\n\r\n  if (showCompletePurchase!==undefined && showCompletePurchase === true){\r\n    modalOpen();\r\n  }\r\n  if (showCompletePurchase!==undefined && showCompletePurchase === false){\r\n    modalClose();\r\n  }\r\n\r\n  return (\r\n    <>\r\n    <div id=\"modalComplete\" className=\"modal z-[9999]\">\r\n      <div class=\"w-full md:w-[600px] border-[#888] md:mt-[15px] mx-[auto] bg-[#fefefe] p-6\">\r\n        <span class=\"close\" onClick={()=> modalClose()}>&times;</span>\r\n        <div className=\"border-b pb-[10px] border-[#d5d5d5]\"><img src={aiproLogo} alt=\"AI-Pro Logo\" className=\"aiprologo mx-auto\"/></div>\r\n        <h1 className=\"font-bold text-center p-2 text-gray-700 text-[20px] md:text-[24px]\">Payment Details<br/>for Enterprise Order</h1>\r\n        <div className=\"text-center text-[12px] md:text-[14px]\">Adding more than 10 Enterprise users requires prior payment.</div>\r\n        <div className=\"text-center text-[12px] md:text-[14px]\">Please use the provided payment details below to settle your Enterprise account.</div>\r\n        <div className=\"py-2\">\r\n          <div className=\"font-bold text-[11px]\">No. of Members: {moreToAddMember}</div>\r\n          <div className=\"font-bold text-[11px]\">Enterprise - Total: ${moreToAddMemberTotalAmount}</div>\r\n        </div>\r\n        <div className=\"border rounded p-2 text-[12px] md:text-[14px] leading-7 my-2\">\r\n          <div className=\"font-bold\">Bank Information</div>\r\n          <div className=\"float-right text-blue-400 font-bold cursor-pointer mt-[-28px] text-[12px]\" onClick={()=> sendViaEmail()}>Send via Email</div>\r\n          <div><span className=\"font-bold\">Beneficiary:</span> TELECOM BUSINESS SOLUTIONS INC.</div>\r\n          <div><span className=\"font-bold\">SWIFT:</span> BOFAUS3N</div>\r\n          <div><span className=\"font-bold\">Bank Name:</span> Bank of America</div>\r\n          <div><span className=\"font-bold\">Routing (Wire):</span> *********</div>\r\n          <div><span className=\"font-bold\">Routing Number (Paper & Electronic):</span> *********</div>\r\n          <div><span className=\"font-bold\">Account Number:</span> 3810-6766-2647</div>\r\n          <div><span className=\"font-bold\">Customer Number:</span> {user_id}</div>\r\n          <div className=\"bg-[#dddddd] px-4 py-2 rounded text-center mt-4\"><FaInfoCircle className=\"inline text-lg mr-2\"/>Customer Number must be included in the bank transfer description field for your funds to transfer successfully.</div>\r\n        </div>\r\n\r\n        <div className=\"text-center text-[12px] md:text-[14px] mt-4\">\r\n          Once the payment is received, our dedicated account manager will contact you to assist in the seamless setup of your Enterprise account.\r\n        </div>\r\n        <div className=\"text-center text-[12px] md:text-[14px]\">\r\n          Please allow <b>2-3 banking days</b> for the payment to reflect in the account.\r\n        </div>\r\n        <div className=\"text-center\">\r\n          <motion.button\r\n            className=\"bg-blue-500 text-white font-bold py-3 px-4 rounded my-4 proceed-pmt\"\r\n            whileHover={{ backgroundColor: \"#5997fd\" }}\r\n            whileTap={{ scale: 0.9 }}\r\n            onClick={submitPaymentInformation}\r\n          >\r\n            Send Payment Confirmation\r\n          </motion.button>\r\n        </div>\r\n      </div>\r\n    </div>    \r\n    </>\r\n  )\r\n}\r\n"], "names": ["getCurrency", "currency", "toLowerCase", "getCountry", "formatDate", "dateStr", "date", "Date", "replace", "getMonth", "getDate", "getFullYear", "getPrice", "data", "trial_price", "price", "numberWithCommas", "x", "toString", "formatNumber", "number", "floatNumber", "parseFloat", "toFixed", "getPricePlan", "isNaN", "toLocaleString", "diffMin", "dt1", "dt2", "diff", "getTime", "Math", "abs", "round", "formatDateTime", "getHours", "getMinutes", "DailyPrice", "_ref", "plan", "dailyPrice", "interval", "payment_interval", "_jsxs", "class", "children", "trial_days", "_Fragment", "className", "Get<PERSON><PERSON><PERSON>", "_jsx", "PriceFormatted", "_ref2", "getPrefixLocation", "currentLocation", "window", "location", "href", "indexOf", "base_url", "process", "useEffect", "script", "document", "createElement", "src", "async", "onload", "body", "append<PERSON><PERSON><PERSON>", "PausedAccountModal", "showPausedAccountModal", "setShowPausedAccountModal", "userSubscriptionID", "userMerchant", "userAccountID", "billingCycle", "setbillingCycle", "useState", "buttonDisable", "setButtonDisable", "toastr", "positionClass", "modalPausedAccountClose", "undefined", "modalPausedAccountOpen", "modal", "getElementById", "style", "display", "Transition", "appear", "show", "as", "Fragment", "Dialog", "onClose", "Child", "enter", "enterFrom", "enterTo", "leave", "leaveFrom", "leaveTo", "Panel", "Title", "type", "value", "onClick", "disabled", "handlePause", "querySelector", "classList", "add", "axios", "post", "headers", "then", "res", "output", "success", "remove", "setTimeout", "reload", "msg", "user_order", "ent_max_members", "TabItem", "tabName", "activeTab", "isActive", "Account<PERSON><PERSON><PERSON><PERSON><PERSON>", "props", "newEmail", "setNewEmail", "email", "auth", "oldPassword", "setOldPassword", "newPassword", "setNewPassword", "confPassword", "setConfPassword", "userPid", "user_pid", "isSocialLogin", "setisSocialLogin", "console", "log", "is_social", "handleCopy", "navigator", "clipboard", "writeText", "FaRegCopy", "placeholder", "name", "readOnly", "autocomplete", "onKeyUp", "event", "target", "onChange", "e", "UpdateAccount", "updateUserDetails", "error_msg", "validatePassword", "length", "validateEmail", "test", "tk", "newemail", "password", "newpassword", "confpassword", "DeleteAccount", "isShowModal", "setIsShowModal", "closeModal", "proceedUpdateAccount", "motion", "button", "whileTap", "scale", "showUpdateAccountModal", "keyCode", "getEnterpriseMembers", "SubscriptionTabContent", "setshowAddMoreMember", "setUserSubscriptionID", "setUserMerchant", "setUserAccountID", "date_now", "user_subscription", "userPpg", "changeCard", "p_id", "getAttribute", "downgradeSub", "getMerchant", "sub", "merchant", "getPlanType", "plan_type", "getPaymentInterval", "some", "status", "map", "index", "plan_name", "price_label", "start_date", "end_date", "<PERSON><PERSON>", "<PERSON><PERSON>", "Items", "<PERSON><PERSON>", "_ref3", "active", "pid", "_ref4", "_ref5", "CancelSubscription", "_ref6", "_ref7", "upgradeSub", "_ref8", "is_trial_end", "_ref9", "merchant_subscription_id", "pauseSub", "_ref0", "FaInfoCircle", "OrderTabContent", "_ref1", "charge_id", "label", "amount", "created_at", "refund_at", "TokenUsageContent", "_ref10", "tokenUsage", "tokenUsageTotal", "userMaxToken", "currentDate", "isShowEnterprise", "currentPlan", "gpt4oTotalToken", "gpt4oMaxTokenUsage", "dalleTotalImage", "dalleMaxImage", "claude<PERSON><PERSON><PERSON><PERSON>", "claudeMaxTokenUsage", "currentPlanName", "fluxPrompt", "fluxTotalPrompt", "membersTotalFluxPrompt", "o1Prompt", "o1TotalPrompt", "membersTotalo1Prompt", "deepseekTotalToken", "deepseekMaxTokenUsage", "isProMax", "isAdvanced", "isUnPaid", "isEnterpriseCluter", "convertToLocaleString", "total", "parseInt", "isTokenMaxUsage", "isDalleMaxUsage", "isClaudeMaxUsage", "isDeepseekMaxUsage", "imgWarning", "alt", "app", "total_token", "HelpTabContent", "LogOutContent", "RemoveCookie", "get", "showDeleteModal", "proceedDeleteAccount", "mixpanel", "people", "set_once", "identify", "track", "_ref11", "<PERSON><PERSON>", "showCancelModal", "surveydata", "limitDisplay", "members", "displayLimit", "slice", "getTotalPages", "totalMembers", "quotient", "floor", "MembersTabContent", "_ref12", "handleShowAddMember", "enterpriseMembers", "handleReloadMembers", "handleShowEditMemberModal", "search", "setSearch", "cloneEnterpriseMembers", "setcloneEnterpriseMembers", "displayEnterpriseMembers", "setDisplayEnterpriseMembers", "showOnly", "setShowOnly", "pageCount", "setPageCount", "currentPage", "setCurrentPage", "PageCount", "handleSearch", "input", "filter", "item", "full_name", "first_name", "last_name", "handlePagerClick", "page", "arrayToLoad", "id", "whileHover", "backgroundColor", "total_flux_prompt", "total_o1_prompt", "user_id", "handleEdit", "params", "ResendPasswordMember", "member_user_id", "DeleteMember", "generatePagerStats", "totalEntries", "ending", "starting", "generatePager", "content", "push", "i", "EditMemberModal", "_ref13", "showEditMember", "setShowEditEnterprise", "editMemberDetails", "fullName", "setfullName", "oldFull<PERSON>ame", "setoldFullName", "setEamil", "origEmail", "setorigEmail", "userId", "setUserId", "modalEditMembersClose", "fieldEmail", "title", "fieldName", "modalEditMembersOpen", "preventDefault", "trim", "fieldFullnameError", "innerHTML", "validateName", "fieldE<PERSON><PERSON><PERSON><PERSON>", "fieldFullname", "member_email", "member_fullname", "member_old_fullname", "member_old_email", "Array", "isArray", "error", "catch", "updateMember", "AddMemberModal", "_ref14", "showAddMember", "setshowAddMember", "entMaxMembers", "isMax", "isVisbleBtn", "setIsVisibleBtn", "setMembers", "addMemberBtn", "setAddMemberBtn", "isRemoveBtn", "setRemoveBtn", "addMember", "current", "total_members_after_insert", "resetMember", "_", "removeMember", "newTotalMembers", "modalAddMembersClose", "validateFields", "<PERSON><PERSON><PERSON><PERSON>", "fieldError1", "fieldError2", "server_error", "doesExist", "modalAddMembersOpen", "mem", "s", "newArr", "<PERSON><PERSON><PERSON><PERSON>", "FaTrash", "removeLastMember", "lastIndex", "JSON", "stringify", "saveMember", "_ref15", "showDeleteMemberModal", "AiFillWarning", "_ref16", "setEnterpriseMembers", "setTokenUsage", "setUserMaxToken", "setTokenUsageTotal", "setGpt4oTotalToken", "setGpt4oMaxTokenUsage", "setDalleTotalImage", "setDalleMaxImage", "setClaudeTotalToken", "setClaudeMaxTokenUsage", "setDeepseekTotalToken", "setDeepseekMaxTokenUsage", "setFluxPrompt", "setFluxTotalPrompt", "setMembersTotalFluxPrompt", "seto1Prompt", "seto1TotalPrompt", "setMembersTotalo1Prompt", "setCurrentDate", "setActiveTab", "setisShowEnterprise", "showEditEnterprise", "setEditMemberDetails", "showAddMoreMember", "showCompletePurchase", "setShowCompletePurchase", "moreToAddMember", "setMoreToAddMember", "moreToAddMemberTotalAmount", "setMoreToAddMemberTotalAmount", "entParentUserID", "setEntParentUserID", "setCurrentPlan", "setCurrentPlanName", "setUserPpg", "setIsMax", "subscription", "setSubscription", "getDateNow", "setGetDateNow", "getOrderNow", "setGetOrderNow", "threed_error", "<PERSON><PERSON><PERSON><PERSON>", "max_members", "ent_parent_user_id", "max_tokens", "max_end", "user_ppg", "view_data", "active_tab", "entMembers", "fetchData", "tokens", "response", "getTokenUsage", "parse", "gpt4o_total_token", "gpt_4o_max_token_usage", "dalle_total_image", "dalle_max_image", "claude_total_token", "claude_max_token_limit", "flux_prompt", "flux_total_prompt", "members_total_flux_prompts", "o1_prompt", "o1_total_prompt", "members_total_o1_prompts", "deepseek_total_token", "deepseek_max_token_usage", "fetchTokens", "getOrders", "fetchOrder", "targetPlans", "checkEntMaxPlan", "user_plan", "find", "includes", "Boolean", "handleTabChange", "tab", "install", "<PERSON><PERSON><PERSON>", "Header", "remainingSlots", "Number", "AddMoreMemberModal", "MemberCompletePurchase", "Footer", "price_per_member", "membersToAdd", "setMembersToAdd", "totalAmount", "setTotalAmount", "paymentInterval", "setpaymentInterval", "modalAddMoreMembersClose", "modalAddMoreMembersOpen", "modalClose", "modalOpen", "aiproLogo", "sendViaEmail", "total_amount", "submitPaymentInformation"], "sourceRoot": ""}