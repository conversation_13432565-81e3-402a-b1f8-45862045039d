{"version": 3, "file": "static/js/8339.7db6282f.chunk.js", "mappings": "sNACO,SAASA,EAAYC,GACxB,OAAIA,EAC0B,QAA3BA,EAASC,cAAgC,IACd,QAA3BD,EAASC,cAAgC,IACd,QAA3BD,EAASC,cAAgC,IACd,QAA3BD,EAASC,eACkB,QAA3BD,EAASC,cADgC,KAErC,GANc,EAOzB,CAEO,SAASC,EAAWF,GACvB,OAAIA,EAC0B,QAA3BA,EAASC,eACkB,QAA3BD,EAASC,cADgC,KAEd,QAA3BD,EAASC,cAAgC,KACd,QAA3BD,EAASC,cAAgC,KACd,QAA3BD,EAASC,cAAgC,KACd,QAA3BD,EAASC,cAAgC,KACd,QAA3BD,EAASC,cAAgC,KACrC,KARc,EASzB,CAEO,SAASE,EAAWC,GACvB,IAAIA,EAAS,MAAO,GACpB,MACMC,EAAO,IAAIC,KAAKF,EAAQG,QAAQ,KAAK,MAE3C,MAHc,CAAC,UAAU,WAAW,QAAQ,QAAQ,MAAM,OAAO,OAAO,SAAS,YAAY,UAAU,WAAW,YAGrGF,EAAKG,YAAc,IAAMH,EAAKI,UAAY,IAAMJ,EAAKK,aACtE,CAEO,SAASC,EAASC,GACrB,OAAGA,EAAKC,YAAoBD,EAAKC,YAC9BD,EAAKE,MAAcF,EAAKE,MACpB,GACX,CAEO,SAASC,EAAiBC,GAC7B,OAAOA,EAAEC,WAAWV,QAAQ,wBAAyB,IACzD,CAEO,SAASW,EAAaC,GAC3B,MAAMC,EAAcC,WAAWF,GAC/B,OACMJ,EADEK,EAAc,GAAM,EACLA,EAAYE,QAAQ,GACpBF,EAAYE,QAAQ,GAC7C,CAEO,SAASC,EAAavB,EAAUc,GACnC,OAAId,GAAac,EAEdU,MAAMV,GAAe,GAErBO,WAAWP,IAAU,EACU,QAA3Bd,EAASC,cACDiB,EAAaJ,GAAOW,eAAe,SAAW,OACpB,QAA3BzB,EAASC,cACRiB,EAAaJ,GAAOW,eAAe,SAAW,MACpB,QAA3BzB,EAASC,cACRiB,EAAaJ,GAAOW,eAAe,SAAW,MACpB,QAA3BzB,EAASC,cACRiB,EAAaJ,GAAOW,eAAe,SAAW,MACpB,QAA3BzB,EAASC,cACRiB,EAAaJ,GAAOW,eAAe,SAAW,KACrB,QAA3BzB,EAASC,cACPiB,EAAaJ,GAAOW,eAAe,SAAW,MACrB,QAA3BzB,EAASC,cACP,KAAOiB,EAAaJ,GAAOW,eAAe,SAChB,QAA3BzB,EAASC,cACRiB,EAAaJ,GAAOW,eAAe,SAAW,KACpB,QAA3BzB,EAASC,cACRiB,EAAaJ,GAAOW,eAAe,SAAW,MACpB,QAA3BzB,EAASC,cACRiB,EAAaJ,GAAOW,eAAe,SAAW,KACpB,QAA3BzB,EAASC,cACRiB,EAAaJ,GAAOW,eAAe,SAAW,IAElD1B,EAAYC,GAAYkB,EAAaJ,GAAOW,eAAe,SAG/D,IAAM1B,EAAYC,KAAoC,EAAvBkB,EAAaJ,IAAaW,eAAe,SA/BhD,EAgCnC,CAEO,SAASC,EAAQC,EAAKC,GAEzBD,EAAM,IAAIrB,KAAKqB,GAEf,IAAIE,IADJD,EAAM,IAAItB,KAAKsB,IACAE,UAAYH,EAAIG,WAAa,IAE5C,OADAD,GAAQ,GACDE,KAAKC,IAAID,KAAKE,MAAMJ,GAC/B,CAEO,SAASK,EAAe9B,GAC3B,IAAIA,EAAS,MAAO,GACpB,MACMC,EAAO,IAAIC,KAAKF,EAAQG,QAAQ,KAAK,MAE3C,MAHc,CAAC,UAAU,WAAW,QAAQ,QAAQ,MAAM,OAAO,OAAO,SAAS,YAAY,UAAU,WAAW,YAGrGF,EAAKG,YAAc,IAAMH,EAAKI,UAAY,IAAMJ,EAAKK,cAAgB,IAAML,EAAK8B,WAAa,IAAM9B,EAAK+B,YACzH,CAEO,SAASC,EAAUC,GAAU,IAAT,KAACC,GAAKD,EAC3BE,EAAa,GACbC,EAAW,GAgBf,MAf8B,WAA1BF,EAAKG,mBACPF,EAAajB,EAAagB,EAAKvC,SAAUqB,WAAWkB,EAAKzB,MAAQ,KAAKQ,QAAQ,IAC9EmB,GAAWE,EAAAA,EAAAA,MAAA,OAAKC,MAAM,eAAcC,SAAA,CAAC,UAAQtB,EAAagB,EAAKvC,SAAUuC,EAAKzB,OAAO,YAGzD,YAA1ByB,EAAKG,mBACPF,EAAajB,EAAagB,EAAKvC,SAAUqB,WAAWkB,EAAKzB,MAAQ,IAAIQ,QAAQ,IAC7EmB,GAAWE,EAAAA,EAAAA,MAAA,OAAKC,MAAM,eAAcC,SAAA,CAAC,UAAQtB,EAAagB,EAAKvC,SAAUuC,EAAKzB,OAAO,aAGnFyB,EAAK1B,cACP2B,EAAajB,EAAagB,EAAKvC,SAAUqB,WAAWkB,EAAK1B,YAAc0B,EAAKO,YAAYxB,QAAQ,IAChGmB,GAAWE,EAAAA,EAAAA,MAAA,OAAKC,MAAM,eAAcC,SAAA,CAAC,UAAQtB,EAAagB,EAAKvC,SAAUuC,EAAK1B,kBAI9E8B,EAAAA,EAAAA,MAAAI,EAAAA,SAAA,CAAAF,SAAA,EACEF,EAAAA,EAAAA,MAAA,KAAGK,UAAW,qCAA+D,MAA1BC,EAAAA,EAAAA,IAAU,YAAsB,OAAS,IAAKJ,SAAA,CAC9FL,EAAW,KAACU,EAAAA,EAAAA,KAAA,QAAMF,UAAU,UAASH,SAAC,gBAExCJ,IAGP,CAEO,SAASU,EAAcC,GAAU,IAAT,KAACb,GAAKa,EACnC,MAA2B,QAAvBH,EAAAA,EAAAA,IAAU,SACLZ,EAAW,CAACE,SAEjBA,EAAK1B,aACCqC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wCAAuCH,SAAEtB,EAAagB,EAAKvC,SAAUuC,EAAK1B,gBAG/F8B,EAAAA,EAAAA,MAAA,KAAGK,UAAU,wCAAuCH,SAAA,CACjDtB,EAAagB,EAAKvC,SAAUuC,EAAKzB,QAClC6B,EAAAA,EAAAA,MAAA,QAAMK,UAAU,UAASH,SAAA,CAAC,IACK,YAA1BN,EAAKG,iBAAiC,QAAU,YAI3D,CAqBO,SAASW,IACd,MAAMC,EAAkBC,OAAOC,SAASC,KAExC,OAAIH,EAAgBI,QAAQ,YAAc,EAC/B,WACAJ,EAAgBI,QAAQ,QAAU,EAClC,OAGJ,EACT,C,0DC5KO,SAASC,EAAiBC,GAA+B,IACxDC,EAAM,GAMV,OALKD,EAEMA,EAASE,OAAS,IACzBD,EAAME,EAAAA,EAAKC,EAAE,8CAFbH,EAAME,EAAAA,EAAKC,EAAE,4CAIVH,CACX,CAEO,SAASI,EAAwBL,EAAUM,GAC9C,IAAIL,EAAM,GAMV,OAJGD,IAAaM,IACZL,EAAME,EAAAA,EAAKC,EAAE,iDAGVH,CACX,C,wECiBA,QAlCA,SAAwBvB,GAAgD,IAA/C,UAAEU,EAAS,WAAEmB,EAAU,UAAEC,EAAS,OAAEC,GAAQ/B,EACnE,MAAMgC,GAAcC,EAAAA,EAAAA,aAAY,KAC9B,MAAMC,EAAUL,EAAWM,QAE3BL,GAAWC,GAKTG,EAAQE,KAHLL,EAGY,WAFA,OAKjBG,EAAQG,SACP,CAACP,EAAWD,EAAYE,IAE3B,OACE1B,EAAAA,EAAAA,MAAAI,EAAAA,SAAA,CAAAF,SAAA,EACEK,EAAAA,EAAAA,KAAA,UACEF,UAAW,mPAAmPA,IAC9P4B,QAASN,EACTI,KAAK,SACLG,OAAUR,EAAS,OAAS,QAArB,YAAuCxB,UAE9CK,EAAAA,EAAAA,KAAC4B,EAAAA,IAAK,CAACC,MAAO,CAAEC,MAAO,aAEzB9B,EAAAA,EAAAA,KAAA,QACEF,UAAW,uIAAuIA,qHAChJqB,EAAS,GAAK,gCAKxB,C,iMC1BA,MAsdA,EAtdgB/B,IAsBT,IAtBU,eACf2C,EAAc,gBACdC,EAAe,WACfC,EAAU,cACVC,EAAa,cACbC,EAAa,iBACbC,EAAgB,QAChBC,EAAO,WACPC,EAAU,UACVC,EAAS,SACTC,EAAQ,YACRC,EAAW,cACXC,EAAa,MACbC,EAAK,SACLjC,EAAQ,WACRkC,EAAU,OACVzB,EAAM,UACND,EAAS,WACTD,EAAU,cACV4B,EAAa,aACbC,EAAY,OACZC,GACD3D,EACC,MAAM,EAAE0B,IAAMkC,EAAAA,EAAAA,MACRC,GAAcC,EAAAA,EAAAA,QAAO,MACrBC,GAAWpD,EAAAA,EAAAA,IAAU,MAErBqD,EAAeA,CAACC,EAAOC,KAC3B,MAAMC,EAAKF,EAAMG,OACXC,EAAQF,EAAGE,MACXC,EAAyB,UAAfL,EAAM7B,KAChBmC,EAAyB,WAAfN,EAAM7B,MAAoC,UAAf6B,EAAM7B,KAQjD,GANMiC,GAASC,GAAWC,EACxBJ,EAAGK,UAAUC,IAAI,cAEjBN,EAAGK,UAAUE,OAAO,cAGD,OAAjBhB,GAA2C,OAAjBA,GAAyBa,EACrD,OAAQL,GACN,IAAK,QACHnB,EAAc,IACdK,EAASiB,GACT,MACF,IAAK,WACHrB,EAAiB,IACjBK,EAAYgB,GACZ,MACF,IAAK,aACHf,EAAca,EAAGQ,SACjB,MACF,QACwB,KAAlBV,EAAMW,SACRC,MAOV,SAASA,IAAgB,IAADC,EACC,QAAvBA,EAAIjB,EAAY1B,eAAO,IAAA2C,GAAnBA,EAAqBT,SACvBU,EAAAA,EAAAA,IAAU,aAAc,MAAO,CAAEC,KAAM,OACvCD,EAAAA,EAAAA,IAAU,aAAc,MAAO,CAAEE,OAAQ,cAAeD,KAAM,OAGhEpC,GAAgB,GAChB,IAAIsC,EAAgBC,IAChBC,EAAmBC,IAClBH,GAAkBE,IAEvBE,SAASC,cAAc,qBAAqBf,UAAUC,IAAI,UAC1De,EAAAA,EACGC,KACC,uCACA,CACElC,QACAjC,WACAoE,SAAUpE,EACVkC,cAEF,CAAEmC,QAAS,CAAE,eAAgB,uCAE9BC,KAAK,SAAUC,GACd,IAAIC,EAASD,EAAIvH,KAEjB,GAAIwH,EAAOC,QAAS,CAClB,IACgC,QAA1BpF,EAAAA,EAAAA,IAAU,aACZM,OAAO+E,SAASC,MAAM,WAAY,CAChCzC,eAGJvC,OAAOiF,GAAG,QAAS,wBACnBjF,OAAOkF,IAAIC,SAAS,CAAE7C,MAAO,GAAGA,MAChCtC,OAAOkF,IAAIF,MAAM,oBAAqB,CACpCI,SAAU,CACR,CACEC,WAAY,GAAGR,EAAOxH,KAAKiI,cAC3BC,aAAc,GAAGjD,MAGrBkD,YAAa,uBAEjB,CAAE,MAAOC,GACPC,QAAQD,MAAM,qBAAsBA,EAAME,QAC5C,CACAC,IAAAA,QAAe,YACf9B,EAAAA,EAAAA,IAAU,SAAUe,EAAOxH,KAAKiI,cAChCxB,EAAAA,EAAAA,IAAU,aAAce,EAAOxH,KAAKiF,MAAO,CAAE0B,OAAQ,cAAeD,KAAM,MAE1E,MAAM8B,GAAOnG,EAAAA,EAAAA,IAAU,QACjBoG,GAAUpG,EAAAA,EAAAA,IAAU,WAC1B,GAAY,YAATmG,EACe,OAAZC,EACe,UAAbhD,EACF9C,OAAO+F,IAAI9F,SAASC,KAAO,WAAawC,EAAS,kBAEjDhB,EAAe,QAGA,UAAboB,EACF9C,OAAO+F,IAAI9F,SAASC,KAAO,WAAawC,EAAS,kBAEjDhB,EAAe,WAAagB,EAAS,2BAIzC,GAAgB,OAAZV,EACe,UAAbc,EACF9C,OAAO+F,IAAI9F,SAASC,KAAO,cAE3BwB,EAAe,mBAEZ,CACL,MAAMsE,GAAUtG,EAAAA,EAAAA,IAAU,WACpBuG,GAAYvG,EAAAA,EAAAA,IAAU,aAExBwG,OAAwCF,GAAyB,MAAdC,EACpC,UAAbnD,EACF9C,OAAO+F,IAAI9F,SAASC,KAAO,WAAawC,EAAS,kBAEjDhB,EAAe,mBAGA,UAAboB,GACF9C,OAAO+F,IAAI9F,SAASC,KAAO,WAAawC,EAAS,kBACjDgD,QAAQS,IAAI,UAEZzE,EAAe,OAGrB,CAGF,MACF,CACA2C,SAASC,cAAc,qBAAqBf,UAAUE,OAAO,UACzDoB,EAAOxH,MAAMuI,IAAAA,MAAaf,EAAOxH,KAAKiD,IAC5C,GACC8F,MAAM,SAAUX,GACXA,EAAMY,UAAsC,MAA1BZ,EAAMY,SAASC,SACnCjC,SACGC,cAAc,qBACdf,UAAUE,OAAO,UACpBmC,IAAAA,MACE,wDAGN,GACJ,CAEA,MAAM1B,EAAgBA,KACpB,IAAIqC,GAAW,EASf,OARKjE,EAEO,eAAekE,KAAKlE,IAG9BR,EAAc,IACdyE,GAAW,GAHXzE,EAAcrB,EAAE,wCAFhBqB,EAAcrB,EAAE,oCAOX8F,GAGHnC,EAAmBA,KACvB,IAAI9D,GAAMF,EAAAA,EAAAA,GAAiBC,GAC3B,OAAIC,IACFyB,EAAiBzB,IACV,IAMLmG,EAA+B,KAAjBnE,EAAMoE,QAAqC,KAApBrG,EAASqG,OAEpD,MAAiB,UAAb5D,GAEA1D,EAAAA,EAAAA,MAAAI,EAAAA,SAAA,CAAAF,SAAA,EACEF,EAAAA,EAAAA,MAAA,SAAOK,UAAU,wBAAuBH,SAAA,EACtCK,EAAAA,EAAAA,KAAA,SAAOwB,KAAK,OAAOwF,IAAK/D,EAAagE,KAAK,aAAanH,UAAU,iCAAiCoH,aAAa,SAC/GlH,EAAAA,EAAAA,KAAA,SACEF,UAAW,wLACTmC,EACI,gCACA,mBAENkF,YAAY,sBACZ,aAAW,sBACX3F,KAAK,QACLyF,KAAK,QACLG,SAAWC,GAAMjE,EAAaiE,EAAG,SACjCC,QAAUjE,IACRlB,EAAc,IACdK,EAASa,EAAMG,OAAOC,OACA,KAAlBJ,EAAMW,SAAgBC,OAG7BhC,IACCjC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,6CAA4CH,SACzDsC,QAKPxC,EAAAA,EAAAA,MAAA,SAAOK,UAAU,eAAcH,SAAA,EAC7BF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,wBAAuBH,SAAA,EACpCK,EAAAA,EAAAA,KAAA,SACEF,UAAW,yLACToC,EACI,gCACA,mBAENiF,YAAY,iBACZ,aAAW,iBACX3F,KAAK,WACLyF,KAAK,WACLG,SAAWC,GAAMjE,EAAaiE,EAAG,YACjCC,QAAUjE,IACRjB,EAAiB,IACjBK,EAAYY,EAAMG,OAAOC,OACH,KAAlBJ,EAAMW,SAAgBC,KAE5B+C,IAAK/F,KAEPjB,EAAAA,EAAAA,KAACuH,EAAAA,EAAe,CACdzH,UAAW,kBAA8B,UAAbqD,EAAuB,WAAa,IAChElC,WAAYA,EACZC,UAAWA,EACXC,OAAQA,OAGXe,IACClC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,6CAA4CH,SACzDuC,OAMoB,QAA1BnC,EAAAA,EAAAA,IAAU,cACTN,EAAAA,EAAAA,MAAA,OAAKC,MAAM,0BAAyBC,SAAA,EAClCK,EAAAA,EAAAA,KAAA,SACEwH,GAAG,mBACHhG,KAAK,WACLyF,KAAK,aACLlD,QAASnB,EACTwE,SAAWC,GAAMjE,EAAaiE,EAAG,cACjCvH,UAAU,+DAEZE,EAAAA,EAAAA,KAAA,SAAOyH,IAAI,mBAAmB/H,MAAM,wBAAuBC,SACxDmB,EAAE,yCAKTd,EAAAA,EAAAA,KAAC0H,EAAAA,EAAOC,OAAM,CACZjG,QAASuC,EACT2D,UAAWd,EACXhH,UAAW,oDACTgH,EACI,6CACA,iDAENe,WAAYf,EAAc,CAAEgB,MAAO,MAAS,CAAC,EAC7CC,SAAUjB,EAAc,CAAEgB,MAAO,KAAS,CAAC,EAC3CE,UAAYX,IACI,UAAVA,EAAEY,KAAmBnB,GACvB7C,KAGJ,aAAW,WAAUtE,SACtB,cAIDF,EAAAA,EAAAA,MAAA,OAAKC,MAAM,kDAAiDC,SAAA,EAC1DK,EAAAA,EAAAA,KAAA,OAAKN,MAAM,mCACXM,EAAAA,EAAAA,KAAA,QAAMN,MAAM,wBAAuBC,SAAC,QACpCK,EAAAA,EAAAA,KAAA,OAAKN,MAAM,yCAOjBD,EAAAA,EAAAA,MAAAI,EAAAA,SAAA,CAAAF,SAAA,CACoB,OAAjBmD,GACCrD,EAAAA,EAAAA,MAAAI,EAAAA,SAAA,CAAAF,SAAA,EACEF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,2CAA0CH,SAAA,EACvDK,EAAAA,EAAAA,KAAA,SAAOwB,KAAK,OAAOwF,IAAK/D,EAAagE,KAAK,aAAanH,UAAU,iCAAiCoH,aAAa,SAC/GlH,EAAAA,EAAAA,KAAA,SACEF,UAAU,wNACVqH,YAAa,GAAGrG,EAAE,oCAClBU,KAAK,QACLyF,KAAK,QACLiB,OAASb,GAAMjE,EAAaiE,EAAG,SAC/BD,SAAWC,GAAMjE,EAAaiE,EAAG,SACjCc,QAAUd,GAAMjE,EAAaiE,EAAG,SAChCC,QAASlE,KAEXpD,EAAAA,EAAAA,KAAA,SACEF,UAAU,iMACV2H,IAAI,QAAO9H,SAEVmB,EAAE,sCAGPd,EAAAA,EAAAA,KAAA,QACEF,UAAU,6GACVkH,IAAMzD,GAAQV,EAActB,QAAQ,GAAKgC,EAAI5D,SAE5CsC,KAEHxC,EAAAA,EAAAA,MAAA,OAAKK,UAAU,2CAA0CH,SAAA,EACvDK,EAAAA,EAAAA,KAAA,SACEF,UAAU,mOACVqH,YAAa,GAAGrG,EAAE,uCAClBU,KAAK,WACLyF,KAAK,WACLiB,OAASb,GAAMjE,EAAaiE,EAAG,YAC/BD,SAAWC,GAAMjE,EAAaiE,EAAG,YACjCc,QAAUd,GAAMjE,EAAaiE,EAAG,YAChCC,QAASlE,EACT4D,IAAK/F,KAEPjB,EAAAA,EAAAA,KAAA,SACEF,UAAU,iMACV2H,IAAI,WAAU9H,SAEbmB,EAAE,sCAELd,EAAAA,EAAAA,KAACuH,EAAAA,EAAe,CACdzH,UAAU,mCACVmB,WAAYA,EACZC,UAAWA,EACXC,OAAQA,QAGZnB,EAAAA,EAAAA,KAAA,QACEF,UAAU,6GACVkH,IAAMzD,GAAQV,EAActB,QAAQ,GAAKgC,EAAI5D,SAE5CuC,QAILzC,EAAAA,EAAAA,MAAAI,EAAAA,SAAA,CAAAF,SAAA,EACEF,EAAAA,EAAAA,MAAA,SAAOK,UAAU,wBAAuBH,SAAA,CACrCsC,IACCjC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,6CAA4CH,SACzDsC,KAGLjC,EAAAA,EAAAA,KAAA,SAAOwB,KAAK,OAAOwF,IAAK/D,EAAagE,KAAK,aAAanH,UAAU,iCAAiCoH,aAAa,SAC/GlH,EAAAA,EAAAA,KAAA,SACEF,UAAU,0NACVqH,YAAa,GAAGrG,EAAE,2CAClB,aAAW,gBACXU,KAAK,QACLyF,KAAK,QACLG,SAAWC,GAAMjE,EAAaiE,EAAG,SACjCC,QAAUjE,IACRlB,EAAc,IACdK,EAASa,EAAMG,OAAOC,OACA,KAAlBJ,EAAMW,SAAgBC,WAIhCxE,EAAAA,EAAAA,MAAA,SAAOK,UAAU,eAAcH,SAAA,CAC5BuC,IACClC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,6CAA4CH,SACzDuC,KAGLzC,EAAAA,EAAAA,MAAA,OAAKK,UAAU,wBAAuBH,SAAA,EACpCK,EAAAA,EAAAA,KAAA,SACEF,UAAU,2NACVqH,YAAa,GAAGrG,EAAE,uCAClB,aAAW,WACXU,KAAK,WACLyF,KAAK,WACLG,SAAWC,GAAMjE,EAAaiE,EAAG,YACjCC,QAAUjE,IACRjB,EAAiB,IACjBK,EAAYY,EAAMG,OAAOC,OACH,KAAlBJ,EAAMW,SAAgBC,KAE5B+C,IAAK/F,KAEPjB,EAAAA,EAAAA,KAACuH,EAAAA,EAAe,CACdzH,UAAU,gBACVmB,WAAYA,EACZC,UAAWA,EACXC,OAAQA,aAOS,QAA1BpB,EAAAA,EAAAA,IAAU,cACTN,EAAAA,EAAAA,MAAA,OAAKC,MAAM,0BAAyBC,SAAA,EAClCK,EAAAA,EAAAA,KAAA,SACEwH,GAAG,mBACHhG,KAAK,WACLyF,KAAK,aACLlD,QAASnB,EACTwE,SAAWC,GAAMjE,EAAaiE,EAAG,cACjCvH,UAAU,+HAEZE,EAAAA,EAAAA,KAAA,SAAOyH,IAAI,mBAAmB/H,MAAM,kCAAiCC,SAAEmB,EAAE,yCAI7Ed,EAAAA,EAAAA,KAAC0H,EAAAA,EAAOC,OAAM,CACZjG,QAASuC,EACTnE,UAAW,4EACO,OAAfwC,GAAqC,OAAdC,EAAsB,oBAC9B,OAAfD,GAAqC,OAAdC,EAAsB,uBAC9C,eAEFsF,WAAY,CAAEC,MAAO,KACrBC,SAAU,CAAED,MAAO,IACnB,aAAW,WAAUnI,SAEpBmB,EAAE,sCAGLd,EAAAA,EAAAA,KAAC0H,EAAAA,EAAOC,OAAM,CACZjG,QAASA,IAAMK,EAAe,SAC9BjC,UAAU,oDACV+H,WAAY,CAAEO,gBAAiB,QAC/BL,SAAU,CAAED,MAAO,IACnB,aAAW,QAAOnI,SAEjBmB,EAAE,mCAGLrB,EAAAA,EAAAA,MAAA,OAAKC,MAAM,kDAAiDC,SAAA,EAC1DK,EAAAA,EAAAA,KAAA,OAAKN,MAAM,mCACXM,EAAAA,EAAAA,KAAA,QAAMN,MAAM,wBAAuBC,SAAEmB,EAAE,gCACvCd,EAAAA,EAAAA,KAAA,OAAKN,MAAM,yC,yDCzcnB,MAAM2I,GAAkBC,EAAAA,EAAAA,MAAK,IAAM,iCAC7BC,GAAaD,EAAAA,EAAAA,MAAK,IAAM,iCACxBE,EAAS,IAAIC,gBAAgBpI,OAAOC,SAASoI,QAsbnD,QApbA,WACE,MAAM,EAAE5H,IAAMkC,EAAAA,EAAAA,OACP2F,EAAc3G,IAAmB4G,EAAAA,EAAAA,WAAS,GAC3CvG,GAAUtC,EAAAA,EAAAA,IAAU,YAAc,GAClCuC,GAAavC,EAAAA,EAAAA,IAAU,eAAiB,MACxCwC,GAAYxC,EAAAA,EAAAA,IAAU,cAAgB,MACtC8I,GAAc9I,EAAAA,EAAAA,IAAU,SAAW,GACnC+I,GAAOC,EAAAA,EAAAA,MACPjG,GAAe/C,EAAAA,EAAAA,IAAU,iBAAmB,MAC5C8C,GAAgBK,EAAAA,EAAAA,QAAO,IACvB8F,GAAU9F,EAAAA,EAAAA,QAAO,OAChB+F,EAAgBC,IAAqBN,EAAAA,EAAAA,WAAS,IAC9CO,EAAeC,IAAoBR,EAAAA,EAAAA,WAAS,GACnD,IAAIxI,EAAkBC,OAAOC,SAASC,KAClCwC,EAAS,GACb,MAAOd,EAAYE,IAAiByG,EAAAA,EAAAA,UAAS,KACtC1G,EAAeE,IAAoBwG,EAAAA,EAAAA,UAAS,KAC5CzH,EAAQD,IAAa0H,EAAAA,EAAAA,WAAS,GAC/B3H,GAAaiC,EAAAA,EAAAA,QAAO,OACnBP,EAAOH,IAAYoG,EAAAA,EAAAA,UAAS,KAC5BlI,EAAU+B,IAAemG,EAAAA,EAAAA,UAAS,KAClChG,EAAYF,IAAiBkG,EAAAA,EAAAA,UAAS,IACvCzF,GAAWpD,EAAAA,EAAAA,IAAU,OACpBsJ,EAAiBC,IAAsBV,EAAAA,EAAAA,WAAS,GACjDW,GAAUxJ,EAAAA,EAAAA,IAAU,YAAc,IAAI0I,gBAAgBpI,OAAOC,SAASoI,QAAQc,IAAI,YAAc,KAGtGC,EAAAA,EAAAA,WAAU,KACR,MAAMC,EAAkBA,KACpB,MAAMC,EAAQtJ,OAAOuJ,WACfC,EAASxJ,OAAOyJ,YACtBR,EAAmBK,GAAS,KAAOE,GAAU,MAMjD,OAHAH,IAEArJ,OAAO0J,iBAAiB,SAAUL,GAC3B,IAAMrJ,OAAO2J,oBAAoB,SAAUN,IACjD,KAEHD,EAAAA,EAAAA,WAAU,MACRQ,EAAAA,EAAAA,OACC,IAGC7J,EAAgBI,QAAQ,YAAc,EACxCuC,EAAS,WACA3C,EAAgBI,QAAQ,QAAU,IAC3CuC,EAAS,QAGX,MAAMhB,EAAkBqC,IACtB,MAAM8F,EAAgB,YAAa/J,EAAAA,EAAAA,MAAsB,yBAC7C,UAATiE,EACG/D,OAAO+F,MAAQ/F,OAAO8J,OACxBhG,EAAAA,EAAAA,IAAU,WAAY,SAAU,CAAEC,KAAM,OACxCD,EAAAA,EAAAA,IAAU,WAAY,SAAU,CAAEC,KAAM,IAAKC,OAAQ,gBACrDhE,OAAO+F,IAAI9F,SAASC,KAAO2J,GAE3BE,WAAW,KACT/J,OAAOC,SAASC,KAAO6D,GACtB,KAGL/D,OAAOC,SAASC,KAAO6D,GAIrBiG,GAAiBhJ,EAAAA,EAAAA,aAAY,KACZ,OAAjByB,GACFD,EAActB,QAAQ+I,QAAQ,CAACtD,EAAKuD,KAClC,GAAIvD,EAAK,CAAC,IAADwD,EACP,MAAMC,EAASzD,EAAI0D,YACbC,EAAa3D,EAAI4D,aACjBf,EAAWY,EACE,IAAfE,EACEA,GACiB,QAAjBH,EAACxD,EAAI6D,oBAAY,IAAAL,EAAAA,EAAI,GAAK,GAC5B,EAEJJ,WAAW,KACTpD,EAAInF,MAAMgI,OAAS,GAAGA,OACrB,IACL,KAGH,CAAC/G,IAsJJ,OApJAsH,WAAW,KACT,MAAMU,EAAO9B,EAAQzH,QACfwJ,EACJC,UAAUC,UAAUC,SAAS,WAC7BF,UAAUC,UAAUC,SAAS,OAE/B,GAAIJ,GAAQC,EAAY,CACtB,MAAMI,EAASL,EAAKM,iBAAiB,SAEjCD,GAAUA,EAAOvK,OAAS,GAC5BuK,EAAOb,QAASe,IACVA,EAAMC,QAAQ,iCAChBD,EAAMzH,UAAUC,IAAI,eAI5B,GACC,MAEH4F,EAAAA,EAAAA,WAAU,KACR,GAAqB,OAAjB3G,EAGF,OAFAuH,IACAhK,OAAO0J,iBAAiB,SAAUM,GAC3B,KACLhK,OAAO2J,oBAAoB,SAAUK,KAGxC,CAACvH,EAAcuH,KAElBZ,EAAAA,EAAAA,WAAU,KACa,OAAjB3G,GACFuH,KAED,CAACpI,EAAYC,EAAeY,EAAcuH,KAE7CZ,EAAAA,EAAAA,WAAU,KACRxD,IAAAA,QAAiB,CACfsF,cAAe,oBAEjB,MAAMC,EAAcC,YAAY,KAChC,IAAIC,GAA0B3L,EAAAA,EAAAA,IAAU,MAAQyI,EAAOgB,IAAI,KACvDmC,EAAe,GAEa,OAA5BD,GAAgE,KAA5BA,IACpCC,EAAeC,EAAOC,KACpBzM,IAAA,IAAC,WAAE0M,GAAY1M,EAAA,OAAK0M,IAAeJ,SAGhBK,IAAjBJ,IACF1F,IAAAA,MAAa0F,EAAahL,MAC1BqL,EAAAA,EAAAA,IAAa,IAAK,CAAE5H,KAAM,OAC1B4H,EAAAA,EAAAA,IAAa,IAAK,CAAE5H,KAAM,IAAKC,OAAQ,mBAG1C,KAEH,MAAO,IAAM4H,cAAcT,IAC1B,KAEH/B,EAAAA,EAAAA,WAAU,KACR,MAAMyC,GAAiBnM,EAAAA,EAAAA,IAAU,cAC3BoM,GAAgBpM,EAAAA,EAAAA,IAAU,aAShC,GAPuB,OAAnBmM,GAA0C,OAAf5J,GAC7B4G,GAAkB,GAEE,OAAlBiD,GAAwC,OAAd5J,GAC5B6G,GAAiB,QAGN2C,IAATjD,GACAH,IACY,IAATG,EAAgB,CACnB,GAAoB,WAAhBA,EAAKnC,OAMP,YALiB,UAAbxD,EACF9C,OAAO+F,IAAI9F,SAASC,KAAO,cAE3BwB,EAAe,gBAIjB,GAAiB,UAAboB,EACF,IACE,MAAMiJ,EAAY/L,OAAO+F,IAAI9F,SAASC,KACtCwF,QAAQS,IAAI,qBAAsB4F,GAE9BA,EAAUlB,SAAS,mBACrB7K,OAAO+F,IAAI9F,SAASC,KAAO,WAAawC,EAAS,kBAEjDhB,EAAe,WAEnB,CAAE,MAAO+D,GAEPC,QAAQS,IAAI,8CAA+CV,GAC3D/D,EAAe,WACjB,MAEAA,EAAe,WAGrB,GAED,CAAC+G,EAAMH,EAActG,EAASwG,EAAa9F,EAAQT,EAAYC,EAAWY,KAE7EsG,EAAAA,EAAAA,WAAU,KACR,MAAM+B,EAAcC,YAAY,KACJ,mBAAtB1L,EAAAA,EAAAA,IAAU,UAA+BA,EAAAA,EAAAA,IAAU,gBACpC,UAAboD,EACF9C,OAAOC,SAASC,KAAO,WAEvBwB,EAAe,YAEjBkK,cAAcT,KAEf,KAEH,MAAO,IAAMS,cAAcT,IAC1B,CAACrI,EAAU2F,KAEdW,EAAAA,EAAAA,WAAU,KACR,GAAoB,YAAhBZ,EAA2B,CAC7B,MAAMwD,EAAa,WAAatJ,EAAS,8BACnCuJ,EAAe5H,SAAS6H,cAAc,QAC5CD,EAAa/L,KAAO8L,EACpBC,EAAaE,IAAM,WACnBF,EAAaG,GAAK,WAClBH,EAAaI,aAAa,qBAAsB,iBAEhDhI,SAASiI,KAAKC,YAAYN,EAC5B,GACC,CAACzD,EAAa9F,KAEjB0G,EAAAA,EAAAA,WAAU,KACRxD,IAAAA,QAAiB,CACfsF,cAAe,oBAEa,QAA1BxL,EAAAA,EAAAA,IAAU,aACZ2C,GAAc,IAEf,KAEH+G,EAAAA,EAAAA,WAAU,KACR1D,QAAQS,IAAI,mBAAoB9B,SAASmI,QACzC,MAAMC,GAAM/M,EAAAA,EAAAA,IAAU,WAChBgN,GAAKhN,EAAAA,EAAAA,IAAU,YACrBgG,QAAQS,IAAI,WAAYsG,EAAK,cAAeC,IAC3C,KAGDtN,EAAAA,EAAAA,MAAAI,EAAAA,SAAA,CAAAF,SAAA,EACEF,EAAAA,EAAAA,MAACuN,EAAAA,EAAM,CAAArN,SAAA,EACLK,EAAAA,EAAAA,KAAA,SAAAL,SAAO,mCACPK,EAAAA,EAAAA,KAAA,QACEiH,KAAK,cACLgG,QAAQ,gJAGZjN,EAAAA,EAAAA,KAACkN,EAAAA,SAAQ,CAACC,SAAU,KAAKxN,SACV,MAAZ4J,GAEC9J,EAAAA,EAAAA,MAAA,OAAKK,WAAeuJ,EAAwC,GAAtB,qBAAtB,uDAAqG1J,SAAA,EACnHK,EAAAA,EAAAA,KAAA,OACEoN,IAAKC,EACLC,IAAI,GACJxN,UAAU,wFACV+B,MAAO,CAAE0L,UAAW,6BAEtBvN,EAAAA,EAAAA,KAACqI,EAAe,CAACtG,eAAgBA,OAEpB,UAAboB,GAEF1D,EAAAA,EAAAA,MAAA,OAAKK,UAAU,sEAAqEH,SAAA,EAClFK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,kBAAiBH,UAC9BF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,+BAA8BH,SAAA,EAC3CF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,mBAAkBH,SAAA,EAC/BK,EAAAA,EAAAA,KAAA,OAAKoN,IAAKI,EAASF,IAAI,cAAcxN,UAAU,uBAC/CE,EAAAA,EAAAA,KAAA,MAAIF,UAAU,wCAAuCH,SAAC,uBAIxDF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,YAAWH,SAAA,EACxBK,EAAAA,EAAAA,KAACyN,EAAO,CACNzL,gBAAiBA,EACjBD,eAAgBA,EAChBO,WAAYA,EACZC,UAAWA,EACXN,WAAYA,EACZC,cAAeA,EACfC,cAAeA,EACfC,iBAAkBA,EAClBC,QAASA,EACTG,SAAUA,EACVC,YAAaA,EACbC,cAAeA,EACfC,MAAOA,EACPjC,SAAUA,EACVkC,WAAYA,EACZzB,OAAQA,EACRD,UAAWA,EACXD,WAAYA,EACZ4B,cAAeA,EACfC,aAAcA,EACdC,OAAQA,EACR2K,SAAS,KAEX1N,EAAAA,EAAAA,KAACuI,EAAU,CACTjG,WAAyB,UAAba,EAAuB,KAAOb,EAC1CC,UAAwB,UAAbY,EAAuB,KAAOZ,EACzCR,eAAgBA,EAChBkH,eAAgBA,EAChBE,cAAeA,EACfuE,SAAS,aAKjB1N,EAAAA,EAAAA,KAAA,OAAKF,UAAU,+FAA8FH,UAC3GF,EAAAA,EAAAA,MAAA,QAAMK,UAAU,yBAAwBH,SAAA,EACtCK,EAAAA,EAAAA,KAAA,UACEwB,KAAK,SACLmM,SAAU,EACV7N,UAAU,0IACV4B,QAAU2F,IACRA,EAAEuG,iBACF,IACoBvN,OAAO+F,IAAIyH,KAAK,WAAW9K,8BAAoC,YAG/E1C,OAAO+F,IAAI9F,SAASC,KAAO,WAAWwC,8BAE1C,CAAE,MAAO+C,GAEPzF,OAAO+F,IAAI9F,SAASC,KAAO,WAAWwC,6BACxC,GAEFiF,UAAYX,IACV,GAAc,UAAVA,EAAEY,IAAiB,CACrBZ,EAAEuG,iBACF,IACoBvN,OAAO+F,IAAIyH,KAAK,WAAW9K,8BAAoC,YAG/E1C,OAAO+F,IAAI9F,SAASC,KAAO,WAAWwC,8BAE1C,CAAE,MAAO+C,GAEPzF,OAAO+F,IAAI9F,SAASC,KAAO,WAAWwC,6BACxC,CACF,GACApD,SACH,iBAGA,OACDK,EAAAA,EAAAA,KAAA,UACEwB,KAAK,SACLmM,SAAU,EACV7N,UAAU,0IACV4B,QAAU2F,IACRA,EAAEuG,iBACF,IACoBvN,OAAO+F,IAAIyH,KAAK,WAAW9K,6BAAmC,YAG9E1C,OAAO+F,IAAI9F,SAASC,KAAO,WAAWwC,6BAE1C,CAAE,MAAO+C,GAEPzF,OAAO+F,IAAI9F,SAASC,KAAO,WAAWwC,4BACxC,GAEFiF,UAAYX,IACV,GAAc,UAAVA,EAAEY,IAAiB,CACrBZ,EAAEuG,iBACF,IACoBvN,OAAO+F,IAAIyH,KAAK,WAAW9K,6BAAmC,YAG9E1C,OAAO+F,IAAI9F,SAASC,KAAO,WAAWwC,6BAE1C,CAAE,MAAO+C,GAEPzF,OAAO+F,IAAI9F,SAASC,KAAO,WAAWwC,4BACxC,CACF,GACApD,SACH,4BAQPK,EAAAA,EAAAA,KAAA,OAAKF,UAAW,6CAA6CH,UAC3DK,EAAAA,EAAAA,KAAA,OAAKF,UAAW,yFAAyFH,UACvGF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,2BAA0BH,SAAA,EACvCK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,mEAAkEH,SAC7EmB,EAAE,4CAELd,EAAAA,EAAAA,KAAA,OACEF,UAAU,gDACVkH,IAAKgC,EAAQrJ,UAEbF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,iBAAgBH,SAAA,EAC7BK,EAAAA,EAAAA,KAACyN,EAAO,CACNzL,gBAAiBA,EACjBD,eAAgBA,EAChBO,WAAYA,EACZC,UAAWA,EACXN,WAAYA,EACZC,cAAeA,EACfC,cAAeA,EACfC,iBAAkBA,EAClBC,QAASA,EACTG,SAAUA,EACVC,YAAaA,EACbC,cAAeA,EACfC,MAAOA,EACPjC,SAAUA,EACVkC,WAAYA,EACZzB,OAAQA,EACRD,UAAWA,EACXD,WAAYA,EACZ4B,cAAeA,EACfC,aAAcA,EACdC,OAAQA,EACR2K,SAAS,KAEX1N,EAAAA,EAAAA,KAACuI,EAAU,CACTjG,WAAYA,EACZC,UAAWA,EACXR,eAAgBA,EAChBkH,eAAgBA,EAChBE,cAAeA,EACfuE,SAAS,oBAW/B,C,oCCtcO,I,WCCII,EAAiB,CAC1BhM,WAAOiK,EACPgC,UAAMhC,EACNjM,eAAWiM,EACXlK,WAAOkK,EACPiC,UAAMjC,GAEGkC,EAAcC,EAAAA,eAAuBA,EAAAA,cAAoBJ,GCRhEK,EAAoC,WAQtC,OAPAA,EAAWC,OAAOC,QAAU,SAAUvN,GACpC,IAAK,IAAIwN,EAAG/D,EAAI,EAAGgE,EAAIC,UAAU5N,OAAQ2J,EAAIgE,EAAGhE,IAE9C,IAAK,IAAIkE,KADTH,EAAIE,UAAUjE,GACO6D,OAAOM,UAAUC,eAAeC,KAAKN,EAAGG,KAAI3N,EAAE2N,GAAKH,EAAEG,IAE5E,OAAO3N,CACT,EACOqN,EAASU,MAAMC,KAAMN,UAC9B,EACIO,EAAgC,SAAUT,EAAGjH,GAC/C,IAAIvG,EAAI,CAAC,EACT,IAAK,IAAI2N,KAAKH,EAAOF,OAAOM,UAAUC,eAAeC,KAAKN,EAAGG,IAAMpH,EAAE7G,QAAQiO,GAAK,IAAG3N,EAAE2N,GAAKH,EAAEG,IAC9F,GAAS,MAALH,GAAqD,mBAAjCF,OAAOY,sBAA2C,KAAIzE,EAAI,EAAb,IAAgBkE,EAAIL,OAAOY,sBAAsBV,GAAI/D,EAAIkE,EAAE7N,OAAQ2J,IAClIlD,EAAE7G,QAAQiO,EAAElE,IAAM,GAAK6D,OAAOM,UAAUO,qBAAqBL,KAAKN,EAAGG,EAAElE,MAAKzJ,EAAE2N,EAAElE,IAAM+D,EAAEG,EAAElE,IADuB,CAGvH,OAAOzJ,CACT,EAGA,SAASoO,EAAaC,GACpB,OAAOA,GAAQA,EAAKC,IAAI,SAAUC,EAAM9E,GACtC,OAAO2D,EAAAA,cAAoBmB,EAAKC,IAAKnB,EAAS,CAC5ClG,IAAKsC,GACJ8E,EAAKrB,MAAOkB,EAAaG,EAAKE,OACnC,EACF,CACO,SAASC,EAAQ9R,GAEtB,OAAO,SAAU+R,GACf,OAAOvB,EAAAA,cAAoBwB,EAAUvB,EAAS,CAC5CH,KAAMG,EAAS,CAAC,EAAGzQ,EAAKsQ,OACvByB,GAAQP,EAAaxR,EAAK6R,OAC/B,CACF,CACO,SAASG,EAASD,GACvB,IAAIE,EAAO,SAAUC,GACnB,IAKI9P,EALAkO,EAAOyB,EAAMzB,KACfD,EAAO0B,EAAM1B,KACbpM,EAAQ8N,EAAM9N,MACdkO,EAAWd,EAAOU,EAAO,CAAC,OAAQ,OAAQ,UACxCK,EAAe/B,GAAQ6B,EAAK7B,MAAQ,MAIxC,OAFI6B,EAAK9P,YAAWA,EAAY8P,EAAK9P,WACjC2P,EAAM3P,YAAWA,GAAaA,EAAYA,EAAY,IAAM,IAAM2P,EAAM3P,WACrEoO,EAAAA,cAAoB,MAAOC,EAAS,CACzC4B,OAAQ,eACRC,KAAM,eACNC,YAAa,KACZL,EAAK5B,KAAMA,EAAM6B,EAAU,CAC5B/P,UAAWA,EACX+B,MAAOsM,EAASA,EAAS,CACvBrM,MAAO2N,EAAM3N,OAAS8N,EAAK9N,OAC1B8N,EAAK/N,OAAQ4N,EAAM5N,OACtBgI,OAAQiG,EACRnG,MAAOmG,EACPI,MAAO,+BACLvO,GAASuM,EAAAA,cAAoB,QAAS,KAAMvM,GAAQ8N,EAAM9P,SAChE,EACA,YAAuBoM,IAAhBkC,EAA4BC,EAAAA,cAAoBD,EAAYkC,SAAU,KAAM,SAAUP,GAC3F,OAAOD,EAAKC,EACd,GAAKD,EAAK7B,EACZ,C", "sources": ["core/utils/main.jsx", "core/utils/validation.jsx", "login/passwordtoggler.jsx", "register/Form.jsx", "register/index.jsx", "../node_modules/react-icons/lib/esm/iconsManifest.js", "../node_modules/react-icons/lib/esm/iconContext.js", "../node_modules/react-icons/lib/esm/iconBase.js"], "sourcesContent": ["import { GetCookie } from '../../core/utils/cookies';\r\nexport function getCurrency(currency) {\r\n    if(!currency) return \"\";\r\n    if(currency.toLowerCase() === 'usd') return \"$\";\r\n    if(currency.toLowerCase() === 'eur') return \"€\";\r\n    if(currency.toLowerCase() === 'gbp') return \"£\";\r\n    if(currency.toLowerCase() === 'brl') return \"R$\";\r\n    if(currency.toLowerCase() === 'sar') return \"R$\";\r\n    return \"\";\r\n}\r\n\r\nexport function getCountry(currency) {\r\n    if(!currency) return \"\";\r\n    if(currency.toLowerCase() === 'usd') return \"US\";\r\n    if(currency.toLowerCase() === 'eur') return \"US\";\r\n    if(currency.toLowerCase() === 'gbp') return \"GB\";\r\n    if(currency.toLowerCase() === 'brl') return \"US\";\r\n    if(currency.toLowerCase() === 'sar') return \"AE\";\r\n    if(currency.toLowerCase() === 'chf') return \"CH\";\r\n    if(currency.toLowerCase() === 'sek') return \"SE\";\r\n    return \"US\";\r\n}\r\n\r\nexport function formatDate(dateStr) {\r\n    if(!dateStr) return \"\";\r\n    const month = [\"January\",\"February\",\"March\",\"April\",\"May\",\"June\",\"July\",\"August\",\"September\",\"October\",\"November\",\"December\"];\r\n    const date = new Date(dateStr.replace(/ /g,\"T\"));\r\n\r\n    return month[date.getMonth()] + \" \" + date.getDate() + \" \" + date.getFullYear();\r\n}\r\n\r\nexport function getPrice(data) {\r\n    if(data.trial_price) return data.trial_price;\r\n    if(data.price) return data.price;\r\n    return \"0\";\r\n}\r\n\r\nexport function numberWithCommas(x) {\r\n    return x.toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, \",\");\r\n}\r\n\r\nexport function formatNumber(number) {\r\n  const floatNumber = parseFloat(number);\r\n  return (floatNumber % 1 === 0)\r\n      ? numberWithCommas(floatNumber.toFixed(0))\r\n      : numberWithCommas(floatNumber.toFixed(2)); \r\n}\r\n\r\nexport function getPricePlan(currency, price) {\r\n    if(!currency || !price) return \"\";\r\n    // Check if price is a number\r\n    if(isNaN(price)) return \"\";\r\n    // Check if price is positive number\r\n    if(parseFloat(price) >= 0) {\r\n        if(currency.toLowerCase() === 'sar') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"ر.س.\";\r\n        } else if(currency.toLowerCase() === 'aed') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"AED\";\r\n        } else if(currency.toLowerCase() === 'chf') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"CHF\";\r\n        } else if(currency.toLowerCase() === 'sek') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"SEK\";\r\n        } else if(currency.toLowerCase() === 'pln') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"zł\";\r\n        }else if(currency.toLowerCase() === 'ron') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"LEI\";\r\n        }else if(currency.toLowerCase() === 'czk') {\r\n            return \"Kč\" + formatNumber(price).toLocaleString(\"en-US\");\r\n        } else if(currency.toLowerCase() === 'huf') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"Ft\";\r\n        } else if(currency.toLowerCase() === 'dkk') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"kr.\";\r\n        } else if(currency.toLowerCase() === 'bgn') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"лв\";\r\n        } else if(currency.toLowerCase() === 'try') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"₺\";\r\n        }\r\n        return getCurrency(currency) + formatNumber(price).toLocaleString(\"en-US\");\r\n    }\r\n    // Negative number\r\n    return \"-\" + getCurrency(currency) + (formatNumber(price) * -1).toLocaleString(\"en-US\");\r\n}\r\n\r\nexport function diffMin(dt1, dt2)\r\n{\r\n    dt1 = new Date(dt1);\r\n    dt2 = new Date(dt2);\r\n    var diff =(dt2.getTime() - dt1.getTime()) / 1000;\r\n    diff /= 60;\r\n    return Math.abs(Math.round(diff));\r\n}\r\n\r\nexport function formatDateTime(dateStr) {\r\n    if(!dateStr) return \"\";\r\n    const month = [\"January\",\"February\",\"March\",\"April\",\"May\",\"June\",\"July\",\"August\",\"September\",\"October\",\"November\",\"December\"];\r\n    const date = new Date(dateStr.replace(/ /g,\"T\"));\r\n\r\n    return month[date.getMonth()] + \" \" + date.getDate() + \" \" + date.getFullYear() + \" \" + date.getHours() + \":\" + date.getMinutes();\r\n}\r\n\r\nexport function DailyPrice({plan}) {\r\n  let dailyPrice = '';\r\n  let interval = '';\r\n  if (plan.payment_interval === 'Yearly') {\r\n    dailyPrice = getPricePlan(plan.currency, parseFloat(plan.price / 365).toFixed(2));\r\n    interval = <div class=\"text-xs mb-4\">billed {getPricePlan(plan.currency, plan.price)}/year</div>;\r\n  }\r\n\r\n  if (plan.payment_interval === 'Monthly') {\r\n    dailyPrice = getPricePlan(plan.currency, parseFloat(plan.price / 30).toFixed(2));\r\n    interval = <div class=\"text-xs mb-4\">billed {getPricePlan(plan.currency, plan.price)}/Month</div>;\r\n  }\r\n\r\n  if (plan.trial_price) {\r\n    dailyPrice = getPricePlan(plan.currency, parseFloat(plan.trial_price / plan.trial_days).toFixed(2));\r\n    interval = <div class=\"text-xs mb-4\">billed {getPricePlan(plan.currency, plan.trial_price)}</div>;\r\n  }\r\n\r\n  return (\r\n    <>\r\n      <p className={`text-4xl font-bold text-gray-800 ${(GetCookie(\"p_toggle\") === '') ? 'mb-4' : ''}`}>\r\n        {dailyPrice} <span className=\"text-sm\"> per Day</span>\r\n      </p>\r\n      {interval}\r\n    </>\r\n  );\r\n}\r\n\r\nexport function PriceFormatted({plan}) {\r\n  if (GetCookie(\"daily\") === 'on') {\r\n    return DailyPrice({plan});\r\n  } \r\n  if (plan.trial_price) {\r\n    return (<p className=\"text-4xl font-bold text-gray-800 mb-4\">{getPricePlan(plan.currency, plan.trial_price)}</p>)\r\n  } \r\n  return (\r\n    <p className=\"text-4xl font-bold text-gray-800 mb-4\">\r\n      {getPricePlan(plan.currency, plan.price)}\r\n      <span className=\"text-sm\"> \r\n        /{ plan.payment_interval === \"Monthly\" ? \"month\" : \"year\" }\r\n      </span>\r\n    </p>\r\n  )\r\n}\r\n\r\nexport function displayTextFormatted(plan) {\r\n  let payment_interval = plan.payment_interval === 'Yearly' ?  365 : 30;\r\n  let trialPricePer = `\r\n    then only ${getPricePlan(plan.currency, plan.price)} per month\r\n  `;\r\n\r\n  if (GetCookie(\"daily\") === 'on') {\r\n    trialPricePer = `\r\n      then only ${getPricePlan(plan.currency, parseFloat(plan.price / payment_interval).toFixed(2))} per day<br>\r\n      (billed ${getPricePlan(plan.currency, plan.price)} ${plan.payment_interval})\r\n    `;\r\n  }\r\n\r\n  return plan.display_txt2\r\n        .replace(\"{trialDays}\", plan.trial_days)\r\n        .replace(\"{trialPricePer}\", trialPricePer);\r\n  \r\n}\r\n\r\nexport function getPrefixLocation() {\r\n  const currentLocation = window.location.href;\r\n\r\n  if (currentLocation.indexOf(\"staging\") > -1) {\r\n      return \"staging.\";\r\n  } else if (currentLocation.indexOf(\"dev\") > -1) {\r\n      return \"dev.\";\r\n  }\r\n\r\n  return \"\"\r\n}", "import i18n from '../../i18n';\r\n\r\nexport function ValidatePassword(password, label = 'Password') {\r\n    let msg = '';\r\n    if (!password) {\r\n        msg = i18n.t('echo.register.validation.passwordReqText');\r\n    } else if (password.length < 6) {\r\n        msg = i18n.t('echo.register.validation.passwordCharText');\r\n    }\r\n    return msg;\r\n}\r\n\r\nexport function ValidateConfirmPassword(password, confpassword) {\r\n    let msg = '';\r\n\r\n    if(password !== confpassword) {\r\n        msg = i18n.t('echo.register.validation.passwordsDoNotMatch');\r\n    }\r\n\r\n    return msg;\r\n}", "import React, { useCallback } from \"react\";\r\nimport { FiEye } from \"react-icons/fi\";\r\n\r\nfunction PasswordToggler({ className, pwInputRef, setShowPW, showPW }) {\r\n  const handleClick = useCallback(() => {\r\n    const pwInput = pwInputRef.current;\r\n\r\n    setShowPW(!showPW);\r\n\r\n    if (!showPW) {\r\n      pwInput.type = \"text\";\r\n    } else {\r\n      pwInput.type = \"password\";\r\n    }\r\n\r\n    pwInput.focus();\r\n  }, [setShowPW, pwInputRef, showPW]);\r\n\r\n  return (\r\n    <>\r\n      <button\r\n        className={`absolute cursor-pointer w-4 h-4 [&_svg]:w-auto [&_svg]:h-auto [&_svg]:max-w-full [&_svg]:max-h-full z-10 fill-[#597291] fill-gray-150 hover:ring-8 hover:ring-slate-300 rounded-full hover:bg-slate-300 transition-all duration-200 ease-linear ${className}`}\r\n        onClick={handleClick}\r\n        type=\"button\"\r\n        title={`${showPW ? \"Hide\" : \"Show\"} Password`}\r\n      >\r\n        <FiEye style={{ color: 'gray' }} />\r\n      </button>\r\n      <span\r\n        className={`absolute z-10 w-[18px] h-[18px] pointer-events-none -rotate-45 mr-[-1px] mt-[-1px] flex items-center justify-center overflow-hidden ${className} after:block after:w-0.5 after:bg-[#597291] after:transition-all after:duration-200 after:ease-in after:h-[18px] ${\r\n          showPW ? \"\" : \"after:-translate-y-full\"\r\n        }`}\r\n      />\r\n    </>\r\n  );\r\n}\r\n\r\nexport default PasswordToggler;\r\n", "import React, { useRef } from \"react\";\r\nimport { motion } from \"framer-motion\";\r\nimport axios from \"axios\";\r\nimport PasswordToggler from '../login/passwordtoggler';\r\nimport { SetCookie, GetCookie } from \"../core/utils/cookies\";\r\nimport { ValidatePassword } from \"../core/utils/validation\";\r\nimport toastr from 'toastr';\r\nimport { useTranslation } from 'react-i18next';\r\n\r\nconst RegForm = ({\r\n  handleRedirect,\r\n  setWillRedirect,\r\n  emailError,\r\n  passwordError,\r\n  setEmailError,\r\n  setPasswordError,\r\n  members,\r\n  reg_google,\r\n  reg_apple,\r\n  setEmail,\r\n  setPassword,\r\n  setEmailOptIn,\r\n  email,\r\n  password,\r\n  emailOptIn,\r\n  showPW,\r\n  setShowPW,\r\n  pwInputRef,\r\n  spanErrorRefs,\r\n  smooth_login,\r\n  prefix\r\n}) => {\r\n  const { t } = useTranslation();\r\n  const honeypotRef = useRef(null);\r\n  const lpCookie = GetCookie(\"lp\");\r\n\r\n  const handleChange = (event, inputType) => {\r\n    const el = event.target;\r\n    const value = el.value;\r\n    const isFocus = event.type === \"focus\";\r\n    const isInput = event.type === \"change\" || event.type === \"keyup\";\r\n\r\n    if (!!value || isFocus || isInput) {\r\n      el.classList.add(\"autofilled\");\r\n    } else {\r\n      el.classList.remove(\"autofilled\");\r\n    }\r\n\r\n    if (smooth_login !== \"on\" || (smooth_login === \"on\" && isInput)) {\r\n      switch (inputType) {\r\n        case \"email\":\r\n          setEmailError(\"\");\r\n          setEmail(value);\r\n          break;\r\n        case \"password\":\r\n          setPasswordError(\"\");\r\n          setPassword(value);\r\n          break;\r\n        case \"emailOptIn\":\r\n          setEmailOptIn(el.checked);\r\n          break;\r\n        default:\r\n          if (event.keyCode === 13) {\r\n            registerUser();\r\n          }\r\n          break;\r\n      }\r\n    }\r\n  };\r\n\r\n  function registerUser() {\r\n    if (honeypotRef.current?.value) {\r\n      SetCookie('ishoneypot', \"yes\", { path: '/' });\r\n      SetCookie('ishoneypot', \"yes\", { domain: '.ai-pro.org', path: '/' });\r\n    }\r\n\r\n    setWillRedirect(false);\r\n    var isEmailPassed = validateEmail();\r\n    var isPasswordPassed = validatePassword();\r\n    if (!isEmailPassed || !isPasswordPassed) return;\r\n\r\n    document.querySelector(\".loader-container\").classList.add(\"active\");\r\n    axios\r\n      .post(\r\n        `${process.env.REACT_APP_API_URL}/t/register`,\r\n        {\r\n          email,\r\n          password,\r\n          pass_con: password,\r\n          emailOptIn\r\n        },\r\n        { headers: { \"content-type\": \"application/x-www-form-urlencoded\" } }\r\n      )\r\n      .then(function (res) {\r\n        let output = res.data;\r\n\r\n        if (output.success) {\r\n          try {\r\n            if (GetCookie(\"emailopt\") === 'on') {\r\n              window.mixpanel.track(\"register\", {\r\n                emailOptIn\r\n              });\r\n            }\r\n            window.qp(\"track\", \"CompleteRegistration\");\r\n            window.ttq.identify({ email: `${email}` });\r\n            window.ttq.track(\"Registration Page\", {\r\n              contents: [\r\n                {\r\n                  content_id: `${output.data.login_token}`,\r\n                  content_name: `${email}`,\r\n                },\r\n              ],\r\n              description: \"Registration (echo)\",\r\n            });\r\n          } catch (error) {\r\n            console.error(\"An error occurred:\", error.message);\r\n          }\r\n          toastr.success(\"Success\");\r\n          SetCookie(\"access\", output.data.login_token);\r\n          SetCookie(\"user_email\", output.data.email, { domain: '.ai-pro.org', path: '/' });\r\n\r\n          const flow = GetCookie(\"flow\");\r\n          const chatpdf = GetCookie(\"chatpdf\");\r\n          if(flow === \"chatpdf\") {\r\n            if (chatpdf === \"01\") {\r\n              if (lpCookie === \"aihub\") {\r\n                window.top.location.href = \"https://\" + prefix + \"chat.ai-pro.org\";\r\n              } else {\r\n                handleRedirect(\"/pay\");\r\n              }\r\n            } else {\r\n              if (lpCookie === \"aihub\") {\r\n                window.top.location.href = \"https://\" + prefix + \"chat.ai-pro.org\";\r\n              } else {\r\n                handleRedirect(\"https://\" + prefix + \"chatpdf.ai-pro.org\");\r\n              }\r\n            }\r\n          } else {\r\n            if (members === \"on\") {\r\n              if (lpCookie === \"aihub\") {\r\n                window.top.location.href = \"/my-account\";\r\n              } else {\r\n                handleRedirect(\"/my-account\");\r\n              }\r\n            } else {\r\n              const pricing = GetCookie(\"pricing\")\r\n              const iSplanEnt = GetCookie(\"iSplanEnt\")\r\n\r\n              if (process.env.REACT_APP_ENTERPRISE_ID === pricing || iSplanEnt === '1') {\r\n                if (lpCookie === \"aihub\") {\r\n                  window.top.location.href = \"https://\" + prefix + \"chat.ai-pro.org\";\r\n                } else {\r\n                  handleRedirect('/pay/mcWiDilmgQ');\r\n                }\r\n              } else {\r\n                if (lpCookie === \"aihub\") {\r\n                  window.top.location.href = \"https://\" + prefix + \"chat.ai-pro.org\";\r\n                  console.log(\"aihub\");\r\n                } else {\r\n                  handleRedirect('/pay');\r\n                }\r\n              }\r\n            }\r\n          }\r\n\r\n          return;\r\n        }\r\n        document.querySelector(\".loader-container\").classList.remove(\"active\");\r\n        if (output.data) toastr.error(output.data.msg);\r\n      })\r\n      .catch(function (error) {\r\n        if (error.response && error.response.status === 429) {\r\n          document\r\n            .querySelector(\".loader-container\")\r\n            .classList.remove(\"active\");\r\n          toastr.error(\r\n            \"Sorry, too many requests. Please try again in a bit!\"\r\n          );\r\n        }\r\n      });\r\n  }\r\n\r\n  const validateEmail = () => {\r\n    let isPassed = false;\r\n    if (!email) {\r\n      setEmailError(t('echo.register.form.emailReqText'));\r\n    } else if (!/\\S+@\\S+\\.\\S+/.test(email)) {\r\n      setEmailError(t('echo.register.form.emailInvalidText'));\r\n    } else {\r\n      setEmailError(\"\");\r\n      isPassed = true;\r\n    }\r\n    return isPassed;\r\n  };\r\n\r\n  const validatePassword = () => {\r\n    let msg = ValidatePassword(password);\r\n    if (msg) {\r\n      setPasswordError(msg);\r\n      return false;\r\n    }\r\n    return true;\r\n  };\r\n\r\n  // Check if both email and password have values\r\n  const isFormValid = email.trim() !== \"\" && password.trim() !== \"\";\r\n\r\n  if (lpCookie === \"aihub\") {\r\n    return (\r\n      <>\r\n        <label className=\"relative block w-full\">\r\n          <input type=\"text\" ref={honeypotRef} name=\"user_phone\" className=\"hidden absolute left-[-9999px]\" autocomplete=\"off\" />\r\n          <input\r\n            className={`placeholder:text-gray-400 min-h-[50px] block w-full border rounded-md py-2 pl-3 pr-3 shadow-sm focus:outline-none focus:border-blue-500 focus:ring-blue-500 focus:ring-1 sm:text-sm ${\r\n              emailError \r\n                ? 'border-[#E28C8C] bg-[#FCF3F3]' \r\n                : 'border-gray-200'\r\n            }`}\r\n            placeholder=\"Enter email address\"\r\n            aria-label=\"Enter email address\"\r\n            type=\"email\"\r\n            name=\"email\"\r\n            onChange={(e) => handleChange(e, \"email\")}\r\n            onKeyUp={(event) => {\r\n              setEmailError(\"\");\r\n              setEmail(event.target.value);\r\n              if (event.keyCode === 13) registerUser();\r\n            }}\r\n          />\r\n          {emailError && (\r\n            <span className=\"text-red-400 text-xs text-left w-full mb-2\">\r\n              {emailError}\r\n            </span>\r\n          )}\r\n\r\n        </label>\r\n        <label className=\"block w-full\">\r\n          <div className=\"relative block w-full\">\r\n            <input\r\n              className={`placeholder:text-gray-400 min-h-[50px] block w-full border rounded-md py-2 pl-3 shadow-sm focus:outline-none focus:border-blue-500 focus:ring-blue-500 focus:ring-1 sm:text-sm pr-10 ${\r\n                passwordError \r\n                  ? 'border-[#E28C8C] bg-[#FCF3F3]' \r\n                  : 'border-gray-200'\r\n              }`}\r\n              placeholder=\"Enter password\"\r\n              aria-label=\"Enter Password\"\r\n              type=\"password\"\r\n              name=\"password\"\r\n              onChange={(e) => handleChange(e, \"password\")}\r\n              onKeyUp={(event) => {\r\n                setPasswordError(\"\");\r\n                setPassword(event.target.value);\r\n                if (event.keyCode === 13) registerUser();\r\n              }}\r\n              ref={pwInputRef}\r\n            />\r\n            <PasswordToggler\r\n              className={`right-3 top-3 ${lpCookie === \"aihub\" ? \"mt-[5px]\" : \"\"}`}\r\n              pwInputRef={pwInputRef}\r\n              setShowPW={setShowPW}\r\n              showPW={showPW}\r\n            />\r\n          </div>\r\n          {passwordError && (\r\n            <span className=\"text-red-400 text-xs text-left w-full mb-2\">\r\n              {passwordError}\r\n            </span>\r\n          )}\r\n\r\n        </label>\r\n\r\n        {GetCookie(\"emailopt\") === 'on' && (\r\n          <div class=\"flex gap-2 items-center\">\r\n            <input\r\n              id=\"checked-checkbox\"\r\n              type=\"checkbox\"\r\n              name=\"emailOptIn\"\r\n              checked={emailOptIn}\r\n              onChange={(e) => handleChange(e, \"emailOptIn\")}\r\n              className=\"w-4 h-4 text-blue-500 bg-gray-100 border-gray-200 rounded\"\r\n            />\r\n            <label for=\"checked-checkbox\" class=\"text-xs text-gray-500\">\r\n              {t('echo.register.form.emailOptText')}\r\n            </label>\r\n          </div>\r\n        )}\r\n\r\n        <motion.button\r\n          onClick={registerUser}\r\n          disabled={!isFormValid}\r\n          className={`mb-1 font-bold py-3 px-6 my-3 rounded-lg w-full ${\r\n            isFormValid \r\n              ? \"text-white bg-[#3073D5] hover:bg-[#2563eb]\" \r\n              : \"text-gray-400 bg-[#F1F1F1] cursor-not-allowed\"\r\n          }`}\r\n          whileHover={isFormValid ? { scale: 1.02 } : {}}\r\n          whileTap={isFormValid ? { scale: 0.98 } : {}}\r\n          onKeyDown={(e) => {\r\n            if (e.key === 'Enter' && isFormValid) {\r\n              registerUser();\r\n            }\r\n          }}\r\n          aria-label=\"register\"\r\n        >\r\n          Continue\r\n        </motion.button>\r\n\r\n        <div class=\"flex items-center justify-center space-x-4 mb-4\">\r\n          <div class=\"w-24 border-t border-gray-200\"></div>\r\n          <span class=\"text-gray-400 text-sm\">or</span>\r\n          <div class=\"w-24 border-t border-gray-200\"></div>\r\n        </div>\r\n      </>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <>\r\n      {smooth_login === \"on\" ? (\r\n        <>\r\n          <div className=\"bg-white w-full relative mb-3 sm:text-sm\">\r\n            <input type=\"text\" ref={honeypotRef} name=\"user_phone\" className=\"hidden absolute left-[-9999px]\" autocomplete=\"off\" />\r\n            <input\r\n              className=\"peer block border-2 border-slate-300 rounded-md shadow-sm focus:outline-none focus:border-sky-500 focus:ring-sky-500 focus:ring-1  w-full pt-6 pb-2 px-6 placeholder:text-transparent placeholder:[user-select:_none]\"\r\n              placeholder={`${t('echo.register.form.emailText')} *`}\r\n              type=\"email\"\r\n              name=\"email\"\r\n              onBlur={(e) => handleChange(e, \"email\")}\r\n              onChange={(e) => handleChange(e, \"email\")}\r\n              onFocus={(e) => handleChange(e, \"email\")}\r\n              onKeyUp={handleChange}\r\n            />\r\n            <label\r\n              className=\"transition-all text-[#597291] duration-100 ease-in absolute top-1/2 -translate-y-1/2 left-6 pointer-events-none [.autofilled~&]:top-[17px] [.autofilled~&]:text-xs [.autofilled~&]:left-[25px]\"\r\n              for=\"email\"\r\n            >\r\n              {t('echo.register.form.emailText')}\r\n            </label>\r\n          </div>\r\n          <span\r\n            className=\"block text-red-500 text-xs text-center w-full h-0 transition-[height] duration-200 ease-in overflow-hidden\"\r\n            ref={(el) => (spanErrorRefs.current[0] = el)}\r\n          >\r\n            {emailError}\r\n          </span>\r\n          <div className=\"bg-white w-full relative mb-3 sm:text-sm\">\r\n            <input\r\n              className=\"peer block border-2 border-slate-300 rounded-md shadow-sm focus:outline-none focus:border-sky-500 focus:ring-sky-500 focus:ring-1  w-full pt-6 pb-2 px-6 pl-6 pr-16 placeholder:text-transparent placeholder:[user-select:_none]\"\r\n              placeholder={`${t('echo.register.form.passwordText')} *`}\r\n              type=\"password\"\r\n              name=\"password\"\r\n              onBlur={(e) => handleChange(e, \"password\")}\r\n              onChange={(e) => handleChange(e, \"password\")}\r\n              onFocus={(e) => handleChange(e, \"password\")}\r\n              onKeyUp={handleChange}\r\n              ref={pwInputRef}\r\n            />\r\n            <label\r\n              className=\"transition-all text-[#597291] duration-100 ease-in absolute top-1/2 -translate-y-1/2 left-6 pointer-events-none [.autofilled~&]:top-[17px] [.autofilled~&]:text-xs [.autofilled~&]:left-[25px]\"\r\n              for=\"password\"\r\n            >\r\n              {t('echo.register.form.passwordText')}\r\n            </label>\r\n            <PasswordToggler\r\n              className=\"right-6 top-1/2 -translate-y-1/2\"\r\n              pwInputRef={pwInputRef}\r\n              setShowPW={setShowPW}\r\n              showPW={showPW}\r\n            />\r\n          </div>\r\n          <span\r\n            className=\"block text-red-500 text-xs text-center w-full h-0 transition-[height] duration-200 ease-in overflow-hidden\"\r\n            ref={(el) => (spanErrorRefs.current[1] = el)}\r\n          >\r\n            {passwordError}\r\n          </span>\r\n        </>\r\n      ) : (\r\n        <>\r\n          <label className=\"relative block w-full\">\r\n            {emailError && (\r\n              <span className=\"text-red-500 text-xs text-left w-full mb-2\">\r\n                {emailError}\r\n              </span>\r\n            )}\r\n            <input type=\"text\" ref={honeypotRef} name=\"user_phone\" className=\"hidden absolute left-[-9999px]\" autocomplete=\"off\" />\r\n            <input\r\n              className=\"placeholder:italic placeholder:text-[#597291] block bg-white w-full border border-slate-300 rounded-md mb-3 py-2 pl-3 pr-3 shadow-sm focus:outline-none focus:border-sky-500 focus:ring-sky-500 focus:ring-1 sm:text-sm\"\r\n              placeholder={`${t('echo.register.form.emailAddressText')} *`}\r\n              aria-label=\"Email Address\"\r\n              type=\"email\"\r\n              name=\"email\"\r\n              onChange={(e) => handleChange(e, \"email\")}\r\n              onKeyUp={(event) => {\r\n                setEmailError(\"\");\r\n                setEmail(event.target.value);\r\n                if (event.keyCode === 13) registerUser();\r\n              }}\r\n            />\r\n          </label>\r\n          <label className=\"block w-full\">\r\n            {passwordError && (\r\n              <span className=\"text-red-500 text-xs text-left w-full mb-2\">\r\n                {passwordError}\r\n              </span>\r\n            )}\r\n            <div className=\"relative block w-full\">\r\n              <input\r\n                className=\"placeholder:italic placeholder:text-[#597291] block bg-white w-full border border-slate-300 rounded-md mb-3 py-2 pl-3 shadow-sm focus:outline-none focus:border-sky-500 focus:ring-sky-500 focus:ring-1 sm:text-sm pr-10\"\r\n                placeholder={`${t('echo.register.form.passwordText')} *`}\r\n                aria-label=\"Password\"\r\n                type=\"password\"\r\n                name=\"password\"\r\n                onChange={(e) => handleChange(e, \"password\")}\r\n                onKeyUp={(event) => {\r\n                  setPasswordError(\"\");\r\n                  setPassword(event.target.value);\r\n                  if (event.keyCode === 13) registerUser();\r\n                }}\r\n                ref={pwInputRef}\r\n              />\r\n              <PasswordToggler\r\n                className=\"right-3 top-3\"\r\n                pwInputRef={pwInputRef}\r\n                setShowPW={setShowPW}\r\n                showPW={showPW}\r\n              />\r\n            </div>\r\n          </label>\r\n        </>\r\n      )}\r\n\r\n      {GetCookie(\"emailopt\") === 'on' && (\r\n        <div class=\"flex gap-2 items-center\">\r\n          <input\r\n            id=\"checked-checkbox\"\r\n            type=\"checkbox\"\r\n            name=\"emailOptIn\"\r\n            checked={emailOptIn}\r\n            onChange={(e) => handleChange(e, \"emailOptIn\")}\r\n            className=\"w-4 h-4 text-gray-600 bg-gray-100 border-gray-300 rounded dark:ring-offset-gray-800 dark:bg-gray-700 dark:border-gray-600\"\r\n          />\r\n          <label for=\"checked-checkbox\" class=\"text-xs text-blue-600 text-left\">{t('echo.register.form.emailOptText')}</label>\r\n        </div>\r\n      )}\r\n\r\n      <motion.button\r\n        onClick={registerUser}\r\n        className={`mb-1 text-white font-bold py-3 px-6 my-3 rounded-lg w-full register-btn ${\r\n          (reg_google === \"02\" || reg_apple === \"02\") ? \"bg-black mb-[5px]\" :\r\n          (reg_google === \"on\" || reg_apple === \"on\") ? \"bg-blue-600 mb-[5px]\" :\r\n          \"bg-blue-600\"\r\n        }`}\r\n        whileHover={{ scale: 1.1 }}\r\n        whileTap={{ scale: 0.9 }}\r\n        aria-label=\"register\"\r\n      >\r\n        {t('echo.register.form.continueText')}\r\n      </motion.button>\r\n\r\n      <motion.button\r\n        onClick={() => handleRedirect(\"login\")}\r\n        className=\"bg-gray-50 mb-1 text-black py-3 rounded-lg w-full\"\r\n        whileHover={{ backgroundColor: \"#eee\" }}\r\n        whileTap={{ scale: 0.9 }}\r\n        aria-label=\"login\"\r\n      >\r\n        {t('echo.register.form.loginText')}\r\n      </motion.button>\r\n\r\n      <div class=\"flex items-center justify-center space-x-4 mb-4\">\r\n        <div class=\"w-24 border-t border-gray-400\"></div>\r\n        <span class=\"text-gray-500 text-sm\">{t('echo.register.form.orText')}</span>\r\n        <div class=\"w-24 border-t border-gray-400\"></div>\r\n      </div>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default RegForm;\r\n", "import React, { useState, useEffect, useRef, useCallback, Suspense, lazy} from \"react\";\r\nimport \"./style.css\";\r\nimport { Auth } from \"../core/utils/auth\";\r\nimport { GetCookie, RemoveCookie, SetCookie } from \"../core/utils/cookies\";\r\nimport { Helmet } from \"react-helmet\";\r\nimport 'toastr/build/toastr.min.css';\r\nimport toastr from 'toastr';\r\nimport RegForm from \"./Form\";\r\nimport errors from '../locales/errors.json';\r\nimport { getPrefixLocation } from \"../core/utils/main\";\r\nimport { getLocales } from '../core/utils/app';\r\nimport { useTranslation } from 'react-i18next';\r\nimport regLogo from '../assets/images/reglogo.png';\r\nimport LeftRect from '../assets/images/left-rect.png'\r\n\r\n// import RegPopupContent from './components/RegPopupContent';\r\nconst RegPopupContent = lazy(() => import('./components/RegPopupContent'));\r\nconst RegButtons = lazy(() => import('./Buttons'));\r\nconst params = new URLSearchParams(window.location.search);\r\n\r\nfunction Register() {\r\n  const { t } = useTranslation();\r\n  const [willRedirect, setWillRedirect] = useState(true);\r\n  const members = GetCookie(\"members\") || \"\";\r\n  const reg_google = GetCookie(\"reg_google\") || \"off\";\r\n  const reg_apple = GetCookie(\"reg_apple\") || \"off\";\r\n  const fromChatapp = GetCookie(\"flow\") || \"\";\r\n  const auth = Auth();\r\n  const smooth_login = GetCookie(\"smooth_login\") || \"off\";\r\n  const spanErrorRefs = useRef([]);\r\n  const formRef = useRef(null);\r\n  const [regGoogleShown, setRegGoogleShown] = useState(false);\r\n  const [regAppleShown, setRegAppleShown] = useState(false);\r\n  let currentLocation = window.location.href;\r\n  let prefix = \"\";\r\n  const [emailError, setEmailError] = useState(\"\");\r\n  const [passwordError, setPasswordError] = useState(\"\");\r\n  const [showPW, setShowPW] = useState(false);\r\n  const pwInputRef = useRef(null);\r\n  const [email, setEmail] = useState(\"\");\r\n  const [password, setPassword] = useState(\"\");\r\n  const [emailOptIn, setEmailOptIn] = useState(\"\");\r\n  const lpCookie = GetCookie(\"lp\");\r\n  const [isCompactScreen, setIsCompactScreen] = useState(false);\r\n  const reg_pop = GetCookie(\"reg_pop\") || new URLSearchParams(window.location.search).get('reg_pop') || \"0\";\r\n  // alert('reg_pop val', reg_pop);\r\n\r\n  useEffect(() => {\r\n    const checkScreenSize = () => {\r\n        const width = window.innerWidth;\r\n        const height = window.innerHeight;\r\n        setIsCompactScreen(width <= 415 && height <= 753);\r\n    };\r\n\r\n    checkScreenSize();\r\n\r\n    window.addEventListener('resize', checkScreenSize);\r\n    return () => window.removeEventListener('resize', checkScreenSize);\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    getLocales();\r\n  }, []);\r\n\r\n  // Global prefix logic\r\n  if (currentLocation.indexOf(\"staging\") > -1) {\r\n    prefix = \"staging.\";\r\n  } else if (currentLocation.indexOf(\"dev\") > -1) {\r\n    prefix = \"dev.\";\r\n  }\r\n\r\n  const handleRedirect = (path) => {\r\n    const redirectToPay = \"https://\" + getPrefixLocation() + \"start.ai-pro.org/login\";\r\n    if(path === 'login'){\r\n      if (window.top !== window.self) {\r\n        SetCookie(\"kt8typtb\", \"arcana\", { path: '/'});\r\n        SetCookie(\"kt8typtb\", \"arcana\", { path: '/', domain: '.ai-pro.org' });\r\n        window.top.location.href = redirectToPay;\r\n      } else {\r\n        setTimeout(() => {\r\n          window.location.href = path;\r\n        }, 300);\r\n      }\r\n    }else{\r\n      window.location.href = path;\r\n    }\r\n  };\r\n\r\n  const setSpanHeights = useCallback(() => {\r\n    if (smooth_login === \"on\") {\r\n      spanErrorRefs.current.forEach((ref, i) => {\r\n        if (ref) {\r\n          const errMsg = ref.textContent;\r\n          const currHeight = ref.offsetHeight;\r\n          const height = !!errMsg\r\n            ? currHeight !== 0\r\n              ? currHeight\r\n              : (ref.scrollHeight ?? 0) + 12\r\n            : 0;\r\n\r\n          setTimeout(() => {\r\n            ref.style.height = `${height}px`;\r\n          }, 100);\r\n        }\r\n      });\r\n    }\r\n  }, [smooth_login]);\r\n\r\n  setTimeout(() => {\r\n    const form = formRef.current;\r\n    const isChromium =\r\n      navigator.userAgent.includes(\"Chrome\") ||\r\n      navigator.userAgent.includes(\"Edg\");\r\n\r\n    if (form && isChromium) {\r\n      const inputs = form.querySelectorAll(\"input\");\r\n\r\n      if (inputs && inputs.length > 0) {\r\n        inputs.forEach((input) => {\r\n          if (input.matches(\":-internal-autofill-selected\")) {\r\n            input.classList.add(\"autofilled\");\r\n          }\r\n        });\r\n      }\r\n    }\r\n  }, 200);\r\n\r\n  useEffect(() => {\r\n    if (smooth_login === \"on\") {\r\n      setSpanHeights();\r\n      window.addEventListener(\"resize\", setSpanHeights);\r\n      return () => {\r\n        window.removeEventListener(\"resize\", setSpanHeights);\r\n      };\r\n    }\r\n  }, [smooth_login, setSpanHeights]);\r\n\r\n  useEffect(() => {\r\n    if (smooth_login === \"on\") {\r\n      setSpanHeights();\r\n    }\r\n  }, [emailError, passwordError, smooth_login, setSpanHeights]);\r\n\r\n  useEffect(() => {\r\n    toastr.options = {\r\n      positionClass: \"toast-top-center\",\r\n    };\r\n    const checkCookie = setInterval(() => {\r\n    let google_apple_error_code = GetCookie(\"e\") || params.get(\"e\");;\r\n    let google_error = \"\";\r\n\r\n    if (google_apple_error_code !== null && google_apple_error_code !== \"\") {\r\n        google_error = errors.find(\r\n          ({ error_code }) => error_code === google_apple_error_code,\r\n        );\r\n\r\n        if (google_error !== undefined) {\r\n          toastr.error(google_error.msg);\r\n          RemoveCookie('e', { path: '/'});\r\n          RemoveCookie('e', { path: '/', domain: '.ai-pro.org' });\r\n        }\r\n      }\r\n    }, 500);\r\n\r\n    return () => clearInterval(checkCookie); \r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    const regGoogleParam = GetCookie('reg_google');\r\n    const regAppleParam = GetCookie('reg_apple');\r\n\r\n    if (regGoogleParam === \"02\" || reg_google === \"02\") {\r\n      setRegGoogleShown(true);\r\n    }\r\n    if (regAppleParam === \"02\" || reg_apple === \"02\") {\r\n      setRegAppleShown(true);\r\n    }\r\n\r\n    if (auth === undefined) return;\r\n    if (willRedirect) {\r\n      if (!auth === false) {\r\n        if (auth.status === \"active\") {\r\n          if (lpCookie === \"aihub\") {\r\n            window.top.location.href = \"/my-account\";\r\n          } else {\r\n            handleRedirect(\"/my-account\");\r\n          }\r\n          return;\r\n        } else {\r\n          if (lpCookie === \"aihub\") {\r\n            try {\r\n              const parentUrl = window.top.location.href;\r\n              console.log(\"Parent window URL:\", parentUrl);\r\n              \r\n              if (parentUrl.includes(\"/create-account\")) {\r\n                window.top.location.href = \"https://\" + prefix + \"chat.ai-pro.org\";\r\n              } else {\r\n                handleRedirect(\"/pricing\");\r\n              }\r\n            } catch (error) {\r\n              // Cross-origin error, use default redirect\r\n              console.log(\"Cross-origin error, using default redirect:\", error);\r\n              handleRedirect(\"/pricing\");\r\n            }\r\n          } else {\r\n            handleRedirect(\"/pricing\");\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }, [auth, willRedirect, members, fromChatapp, prefix, reg_google, reg_apple, lpCookie]);\r\n\r\n  useEffect(() => {\r\n    const checkCookie = setInterval(() => {\r\n      if (GetCookie(\"flow\") === \"register-auth\" && GetCookie(\"user_email\")) {\r\n        if (lpCookie === \"aihub\") {\r\n          window.location.href = \"/pricing\";\r\n        } else {\r\n          handleRedirect(\"/pricing\");\r\n        }\r\n        clearInterval(checkCookie); \r\n      }\r\n    }, 500);\r\n\r\n    return () => clearInterval(checkCookie); \r\n  }, [lpCookie, auth])\r\n\r\n  useEffect(() => {\r\n    if (fromChatapp === \"chatapp\") {\r\n      const chatProUrl = \"https://\" + prefix + \"chatpro.ai-pro.org/chat/new\";\r\n      const prefetchLink = document.createElement(\"link\");\r\n      prefetchLink.href = chatProUrl;\r\n      prefetchLink.rel = \"prefetch\";\r\n      prefetchLink.as = \"document\";\r\n      prefetchLink.setAttribute(\"data-cache-control\", \"max-age=86400\"); // 1 week cache\r\n\r\n      document.head.appendChild(prefetchLink);\r\n    }\r\n  }, [fromChatapp, prefix]);\r\n\r\n  useEffect(() => {\r\n    toastr.options = {\r\n      positionClass: 'toast-top-center'\r\n    };\r\n    if (GetCookie(\"emailopt\") === 'on') {\r\n      setEmailOptIn(true);\r\n    }\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    console.log(\"document.cookie:\", document.cookie);\r\n    const reg = GetCookie(\"reg_pop\");\r\n    const lp = GetCookie(\"lpCookie\");\r\n    console.log(\"reg_pop:\", reg, \"| lpCookie:\", lp);\r\n  }, []);\r\n\r\n  return (\r\n    <>\r\n      <Helmet>\r\n        <title>AI Pro | Account Registration</title>\r\n        <meta\r\n          name=\"description\"\r\n          content=\"Join now and register for a free account at AI Pro. Unlock access to cutting-edge AI tools and resources. Sign up today to get started!\"\r\n        />\r\n      </Helmet>\r\n      <Suspense fallback={null}>\r\n        {reg_pop === \"1\" ? (\r\n          // <div className=\"fixed inset-0 z-50 flex items-center justify-center h-screen bg-white\">\r\n          <div className={`${!isCompactScreen ? \"flex items-center\" : \"\"} fixed inset-0 z-50 justify-center h-screen bg-white`}>\r\n            <img\r\n              src={LeftRect}\r\n              alt=\"\"\r\n              className='absolute top-0 left-0 h-inherit xl:w-[34%] hidden xl:block lg:block md:block sm:block'\r\n              style={{ minHeight: \"-webkit-fill-available\" }}\r\n            />\r\n            <RegPopupContent handleRedirect={handleRedirect} />\r\n          </div>\r\n        ) : lpCookie === \"aihub\" ? (\r\n          // AI Hub specific design\r\n          <div className=\"min-h-screen bg-white flex flex-col items-center justify-center p-4\">\r\n            <div className=\"max-w-md w-full\">\r\n              <div className=\"bg-[#FCFCFC] rounded-2xl p-8\">\r\n                <div className=\"text-center mb-8\">\r\n                  <img src={regLogo} alt=\"AI Pro Logo\" className=\"mx-auto mb-2 h-12\" />\r\n                  <h1 className=\"text-[20px] font-bold text-black mb-2\">\r\n                    Create Account\r\n                  </h1>\r\n                </div>\r\n                <div className=\"space-y-4\">\r\n                  <RegForm\r\n                    setWillRedirect={setWillRedirect}\r\n                    handleRedirect={handleRedirect}\r\n                    reg_google={reg_google}\r\n                    reg_apple={reg_apple}\r\n                    emailError={emailError}\r\n                    passwordError={passwordError}\r\n                    setEmailError={setEmailError}\r\n                    setPasswordError={setPasswordError}\r\n                    members={members}\r\n                    setEmail={setEmail}\r\n                    setPassword={setPassword}\r\n                    setEmailOptIn={setEmailOptIn}\r\n                    email={email}\r\n                    password={password}\r\n                    emailOptIn={emailOptIn}\r\n                    showPW={showPW}\r\n                    setShowPW={setShowPW}\r\n                    pwInputRef={pwInputRef}\r\n                    spanErrorRefs={spanErrorRefs}\r\n                    smooth_login={smooth_login}\r\n                    prefix={prefix}\r\n                    isAiHub={true}\r\n                  />\r\n                  <RegButtons\r\n                    reg_google={lpCookie === \"aihub\" ? \"on\" : reg_google}\r\n                    reg_apple={lpCookie === \"aihub\" ? \"on\" : reg_apple}\r\n                    handleRedirect={handleRedirect}\r\n                    regGoogleShown={regGoogleShown}\r\n                    regAppleShown={regAppleShown}\r\n                    isAiHub={true}\r\n                  />\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div className=\"text-center mt-8 absolute bottom-[15px] left-0 right-0 max-h-600:relative max-h-600:bottom-0\">\r\n              <span className=\"text-xs text-[#3073D5]\">\r\n                <button \r\n                  type=\"button\"\r\n                  tabIndex={0}\r\n                  className=\"cursor-pointer hover:underline hover:text-blue-700 transition-colors duration-200 bg-transparent border-none p-0 text-xs text-[#3073D5]\"\r\n                  onClick={(e) => {\r\n                    e.preventDefault();\r\n                    try {\r\n                      const newWindow = window.top.open(`https://${prefix}ai-pro.org/member-tos-page`, '_blank');\r\n                      if (!newWindow) {\r\n                        // If popup is blocked, redirect the entire iframe\r\n                        window.top.location.href = `https://${prefix}ai-pro.org/member-tos-page`;\r\n                      }\r\n                    } catch (error) {\r\n                      // If cross-origin blocks window.top.open, redirect the entire iframe\r\n                      window.top.location.href = `https://${prefix}ai-pro.org/member-tos-page`;\r\n                    }\r\n                  }}\r\n                  onKeyDown={(e) => {\r\n                    if (e.key === 'Enter') {\r\n                      e.preventDefault();\r\n                      try {\r\n                        const newWindow = window.top.open(`https://${prefix}ai-pro.org/member-tos-page`, '_blank');\r\n                        if (!newWindow) {\r\n                          // If popup is blocked, redirect the entire iframe\r\n                          window.top.location.href = `https://${prefix}ai-pro.org/member-tos-page`;\r\n                        }\r\n                      } catch (error) {\r\n                        // If cross-origin blocks window.top.open, redirect the entire iframe\r\n                        window.top.location.href = `https://${prefix}ai-pro.org/member-tos-page`;\r\n                      }\r\n                    }\r\n                  }}\r\n                >\r\n                  Terms of Use\r\n                </button>\r\n                {\" / \"}\r\n                <button \r\n                  type=\"button\"\r\n                  tabIndex={0}\r\n                  className=\"cursor-pointer hover:underline hover:text-blue-700 transition-colors duration-200 bg-transparent border-none p-0 text-xs text-[#3073D5]\"\r\n                  onClick={(e) => {\r\n                    e.preventDefault();\r\n                    try {\r\n                      const newWindow = window.top.open(`https://${prefix}ai-pro.org/privacy-policy`, '_blank');\r\n                      if (!newWindow) {\r\n                        // If popup is blocked, redirect the entire iframe\r\n                        window.top.location.href = `https://${prefix}ai-pro.org/privacy-policy`;\r\n                      }\r\n                    } catch (error) {\r\n                      // If cross-origin blocks window.top.open, redirect the entire iframe\r\n                      window.top.location.href = `https://${prefix}ai-pro.org/privacy-policy`;\r\n                    }\r\n                  }}\r\n                  onKeyDown={(e) => {\r\n                    if (e.key === 'Enter') {\r\n                      e.preventDefault();\r\n                      try {\r\n                        const newWindow = window.top.open(`https://${prefix}ai-pro.org/privacy-policy`, '_blank');\r\n                        if (!newWindow) {\r\n                          // If popup is blocked, redirect the entire iframe\r\n                          window.top.location.href = `https://${prefix}ai-pro.org/privacy-policy`;\r\n                        }\r\n                      } catch (error) {\r\n                        // If cross-origin blocks window.top.open, redirect the entire iframe\r\n                        window.top.location.href = `https://${prefix}ai-pro.org/privacy-policy`;\r\n                      }\r\n                    }\r\n                  }}\r\n                >\r\n                  Privacy Policy\r\n                </button>\r\n              </span>\r\n            </div>\r\n          </div>\r\n        ) : (\r\n          // Original design\r\n          <div className={`flex vertical-center items-center h-screen`}>\r\n            <div className={`container md:pt-[70px] px-0 md:px-4 sm:px-0 min-w-[330px] w-64 sm:w-80 md:w-96 mx-auto`}>\r\n              <div className=\"reg_col text-center mb-8\">\r\n                <h1 className=\"text-2xl h-[32px] lg:text-2xl font-bold text-center mb-2 lg:mb-8\">\r\n                  {t('echo.register.index.createAccountText')}\r\n                </h1>\r\n                <div\r\n                  className=\"bg-white rounded-lg shadow-lg overflow-hidden\"\r\n                  ref={formRef}\r\n                >\r\n                  <div className=\"p-6 text-black\">\r\n                    <RegForm\r\n                      setWillRedirect={setWillRedirect}\r\n                      handleRedirect={handleRedirect}\r\n                      reg_google={reg_google}\r\n                      reg_apple={reg_apple}\r\n                      emailError={emailError}\r\n                      passwordError={passwordError}\r\n                      setEmailError={setEmailError}\r\n                      setPasswordError={setPasswordError}\r\n                      members={members}\r\n                      setEmail={setEmail}\r\n                      setPassword={setPassword}\r\n                      setEmailOptIn={setEmailOptIn}\r\n                      email={email}\r\n                      password={password}\r\n                      emailOptIn={emailOptIn}\r\n                      showPW={showPW}\r\n                      setShowPW={setShowPW}\r\n                      pwInputRef={pwInputRef}\r\n                      spanErrorRefs={spanErrorRefs}\r\n                      smooth_login={smooth_login}\r\n                      prefix={prefix}\r\n                      isAiHub={false}\r\n                    />\r\n                    <RegButtons\r\n                      reg_google={reg_google}\r\n                      reg_apple={reg_apple}\r\n                      handleRedirect={handleRedirect}\r\n                      regGoogleShown={regGoogleShown}\r\n                      regAppleShown={regAppleShown}\r\n                      isAiHub={false}\r\n                    />\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        )}\r\n      </Suspense>\r\n    </>\r\n  );\r\n}\r\n\r\nexport default Register;", "export var IconsManifest = [\n  {\n    \"id\": \"ci\",\n    \"name\": \"Circum Icons\",\n    \"projectUrl\": \"https://circumicons.com/\",\n    \"license\": \"MPL-2.0 license\",\n    \"licenseUrl\": \"https://github.com/Klarr-Agency/Circum-Icons/blob/main/LICENSE\"\n  },\n  {\n    \"id\": \"fa\",\n    \"name\": \"Font Awesome 5\",\n    \"projectUrl\": \"https://fontawesome.com/\",\n    \"license\": \"CC BY 4.0 License\",\n    \"licenseUrl\": \"https://creativecommons.org/licenses/by/4.0/\"\n  },\n  {\n    \"id\": \"fa6\",\n    \"name\": \"Font Awesome 6\",\n    \"projectUrl\": \"https://fontawesome.com/\",\n    \"license\": \"CC BY 4.0 License\",\n    \"licenseUrl\": \"https://creativecommons.org/licenses/by/4.0/\"\n  },\n  {\n    \"id\": \"io\",\n    \"name\": \"Ionicons 4\",\n    \"projectUrl\": \"https://ionicons.com/\",\n    \"license\": \"MIT\",\n    \"licenseUrl\": \"https://github.com/ionic-team/ionicons/blob/master/LICENSE\"\n  },\n  {\n    \"id\": \"io5\",\n    \"name\": \"Ionicons 5\",\n    \"projectUrl\": \"https://ionicons.com/\",\n    \"license\": \"MIT\",\n    \"licenseUrl\": \"https://github.com/ionic-team/ionicons/blob/master/LICENSE\"\n  },\n  {\n    \"id\": \"md\",\n    \"name\": \"Material Design icons\",\n    \"projectUrl\": \"http://google.github.io/material-design-icons/\",\n    \"license\": \"Apache License Version 2.0\",\n    \"licenseUrl\": \"https://github.com/google/material-design-icons/blob/master/LICENSE\"\n  },\n  {\n    \"id\": \"ti\",\n    \"name\": \"Typicons\",\n    \"projectUrl\": \"http://s-ings.com/typicons/\",\n    \"license\": \"CC BY-SA 3.0\",\n    \"licenseUrl\": \"https://creativecommons.org/licenses/by-sa/3.0/\"\n  },\n  {\n    \"id\": \"go\",\n    \"name\": \"Github Octicons icons\",\n    \"projectUrl\": \"https://octicons.github.com/\",\n    \"license\": \"MIT\",\n    \"licenseUrl\": \"https://github.com/primer/octicons/blob/master/LICENSE\"\n  },\n  {\n    \"id\": \"fi\",\n    \"name\": \"Feather\",\n    \"projectUrl\": \"https://feathericons.com/\",\n    \"license\": \"MIT\",\n    \"licenseUrl\": \"https://github.com/feathericons/feather/blob/master/LICENSE\"\n  },\n  {\n    \"id\": \"lu\",\n    \"name\": \"Lucide\",\n    \"projectUrl\": \"https://lucide.dev/\",\n    \"license\": \"ISC\",\n    \"licenseUrl\": \"https://github.com/lucide-icons/lucide/blob/main/LICENSE\"\n  },\n  {\n    \"id\": \"gi\",\n    \"name\": \"Game Icons\",\n    \"projectUrl\": \"https://game-icons.net/\",\n    \"license\": \"CC BY 3.0\",\n    \"licenseUrl\": \"https://creativecommons.org/licenses/by/3.0/\"\n  },\n  {\n    \"id\": \"wi\",\n    \"name\": \"Weather Icons\",\n    \"projectUrl\": \"https://erikflowers.github.io/weather-icons/\",\n    \"license\": \"SIL OFL 1.1\",\n    \"licenseUrl\": \"http://scripts.sil.org/OFL\"\n  },\n  {\n    \"id\": \"di\",\n    \"name\": \"Devicons\",\n    \"projectUrl\": \"https://vorillaz.github.io/devicons/\",\n    \"license\": \"MIT\",\n    \"licenseUrl\": \"https://opensource.org/licenses/MIT\"\n  },\n  {\n    \"id\": \"ai\",\n    \"name\": \"Ant Design Icons\",\n    \"projectUrl\": \"https://github.com/ant-design/ant-design-icons\",\n    \"license\": \"MIT\",\n    \"licenseUrl\": \"https://opensource.org/licenses/MIT\"\n  },\n  {\n    \"id\": \"bs\",\n    \"name\": \"Bootstrap Icons\",\n    \"projectUrl\": \"https://github.com/twbs/icons\",\n    \"license\": \"MIT\",\n    \"licenseUrl\": \"https://opensource.org/licenses/MIT\"\n  },\n  {\n    \"id\": \"ri\",\n    \"name\": \"Remix Icon\",\n    \"projectUrl\": \"https://github.com/Remix-Design/RemixIcon\",\n    \"license\": \"Apache License Version 2.0\",\n    \"licenseUrl\": \"http://www.apache.org/licenses/\"\n  },\n  {\n    \"id\": \"fc\",\n    \"name\": \"Flat Color Icons\",\n    \"projectUrl\": \"https://github.com/icons8/flat-color-icons\",\n    \"license\": \"MIT\",\n    \"licenseUrl\": \"https://opensource.org/licenses/MIT\"\n  },\n  {\n    \"id\": \"gr\",\n    \"name\": \"Grommet-Icons\",\n    \"projectUrl\": \"https://github.com/grommet/grommet-icons\",\n    \"license\": \"Apache License Version 2.0\",\n    \"licenseUrl\": \"http://www.apache.org/licenses/\"\n  },\n  {\n    \"id\": \"hi\",\n    \"name\": \"Heroicons\",\n    \"projectUrl\": \"https://github.com/tailwindlabs/heroicons\",\n    \"license\": \"MIT\",\n    \"licenseUrl\": \"https://opensource.org/licenses/MIT\"\n  },\n  {\n    \"id\": \"hi2\",\n    \"name\": \"Heroicons 2\",\n    \"projectUrl\": \"https://github.com/tailwindlabs/heroicons\",\n    \"license\": \"MIT\",\n    \"licenseUrl\": \"https://opensource.org/licenses/MIT\"\n  },\n  {\n    \"id\": \"si\",\n    \"name\": \"Simple Icons\",\n    \"projectUrl\": \"https://simpleicons.org/\",\n    \"license\": \"CC0 1.0 Universal\",\n    \"licenseUrl\": \"https://creativecommons.org/publicdomain/zero/1.0/\"\n  },\n  {\n    \"id\": \"sl\",\n    \"name\": \"Simple Line Icons\",\n    \"projectUrl\": \"https://thesabbir.github.io/simple-line-icons/\",\n    \"license\": \"MIT\",\n    \"licenseUrl\": \"https://opensource.org/licenses/MIT\"\n  },\n  {\n    \"id\": \"im\",\n    \"name\": \"IcoMoon Free\",\n    \"projectUrl\": \"https://github.com/Keyamoon/IcoMoon-Free\",\n    \"license\": \"CC BY 4.0 License\",\n    \"licenseUrl\": \"https://github.com/Keyamoon/IcoMoon-Free/blob/master/License.txt\"\n  },\n  {\n    \"id\": \"bi\",\n    \"name\": \"BoxIcons\",\n    \"projectUrl\": \"https://github.com/atisawd/boxicons\",\n    \"license\": \"CC BY 4.0 License\",\n    \"licenseUrl\": \"https://github.com/atisawd/boxicons/blob/master/LICENSE\"\n  },\n  {\n    \"id\": \"cg\",\n    \"name\": \"css.gg\",\n    \"projectUrl\": \"https://github.com/astrit/css.gg\",\n    \"license\": \"MIT\",\n    \"licenseUrl\": \"https://opensource.org/licenses/MIT\"\n  },\n  {\n    \"id\": \"vsc\",\n    \"name\": \"VS Code Icons\",\n    \"projectUrl\": \"https://github.com/microsoft/vscode-codicons\",\n    \"license\": \"CC BY 4.0\",\n    \"licenseUrl\": \"https://creativecommons.org/licenses/by/4.0/\"\n  },\n  {\n    \"id\": \"tb\",\n    \"name\": \"Tabler Icons\",\n    \"projectUrl\": \"https://github.com/tabler/tabler-icons\",\n    \"license\": \"MIT\",\n    \"licenseUrl\": \"https://opensource.org/licenses/MIT\"\n  },\n  {\n    \"id\": \"tfi\",\n    \"name\": \"Themify Icons\",\n    \"projectUrl\": \"https://github.com/lykmapipo/themify-icons\",\n    \"license\": \"MIT\",\n    \"licenseUrl\": \"https://github.com/thecreation/standard-icons/blob/master/modules/themify-icons/LICENSE\"\n  },\n  {\n    \"id\": \"rx\",\n    \"name\": \"Radix Icons\",\n    \"projectUrl\": \"https://icons.radix-ui.com\",\n    \"license\": \"MIT\",\n    \"licenseUrl\": \"https://github.com/radix-ui/icons/blob/master/LICENSE\"\n  },\n  {\n    \"id\": \"pi\",\n    \"name\": \"Phosphor Icons\",\n    \"projectUrl\": \"https://github.com/phosphor-icons/core\",\n    \"license\": \"MIT\",\n    \"licenseUrl\": \"https://github.com/phosphor-icons/core/blob/main/LICENSE\"\n  },\n  {\n    \"id\": \"lia\",\n    \"name\": \"Icons8 Line Awesome\",\n    \"projectUrl\": \"https://icons8.com/line-awesome\",\n    \"license\": \"MIT\",\n    \"licenseUrl\": \"https://github.com/icons8/line-awesome/blob/master/LICENSE.md\"\n  }\n]", "import React from \"react\";\nexport var DefaultContext = {\n  color: undefined,\n  size: undefined,\n  className: undefined,\n  style: undefined,\n  attr: undefined\n};\nexport var IconContext = React.createContext && React.createContext(DefaultContext);", "var __assign = this && this.__assign || function () {\n  __assign = Object.assign || function (t) {\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\n      s = arguments[i];\n      for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n    }\n    return t;\n  };\n  return __assign.apply(this, arguments);\n};\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport React from \"react\";\nimport { IconContext, DefaultContext } from \"./iconContext\";\nfunction Tree2Element(tree) {\n  return tree && tree.map(function (node, i) {\n    return React.createElement(node.tag, __assign({\n      key: i\n    }, node.attr), Tree2Element(node.child));\n  });\n}\nexport function GenIcon(data) {\n  // eslint-disable-next-line react/display-name\n  return function (props) {\n    return React.createElement(IconBase, __assign({\n      attr: __assign({}, data.attr)\n    }, props), Tree2Element(data.child));\n  };\n}\nexport function IconBase(props) {\n  var elem = function (conf) {\n    var attr = props.attr,\n      size = props.size,\n      title = props.title,\n      svgProps = __rest(props, [\"attr\", \"size\", \"title\"]);\n    var computedSize = size || conf.size || \"1em\";\n    var className;\n    if (conf.className) className = conf.className;\n    if (props.className) className = (className ? className + \" \" : \"\") + props.className;\n    return React.createElement(\"svg\", __assign({\n      stroke: \"currentColor\",\n      fill: \"currentColor\",\n      strokeWidth: \"0\"\n    }, conf.attr, attr, svgProps, {\n      className: className,\n      style: __assign(__assign({\n        color: props.color || conf.color\n      }, conf.style), props.style),\n      height: computedSize,\n      width: computedSize,\n      xmlns: \"http://www.w3.org/2000/svg\"\n    }), title && React.createElement(\"title\", null, title), props.children);\n  };\n  return IconContext !== undefined ? React.createElement(IconContext.Consumer, null, function (conf) {\n    return elem(conf);\n  }) : elem(DefaultContext);\n}"], "names": ["getCurrency", "currency", "toLowerCase", "getCountry", "formatDate", "dateStr", "date", "Date", "replace", "getMonth", "getDate", "getFullYear", "getPrice", "data", "trial_price", "price", "numberWithCommas", "x", "toString", "formatNumber", "number", "floatNumber", "parseFloat", "toFixed", "getPricePlan", "isNaN", "toLocaleString", "diffMin", "dt1", "dt2", "diff", "getTime", "Math", "abs", "round", "formatDateTime", "getHours", "getMinutes", "DailyPrice", "_ref", "plan", "dailyPrice", "interval", "payment_interval", "_jsxs", "class", "children", "trial_days", "_Fragment", "className", "Get<PERSON><PERSON><PERSON>", "_jsx", "PriceFormatted", "_ref2", "getPrefixLocation", "currentLocation", "window", "location", "href", "indexOf", "ValidatePassword", "password", "msg", "length", "i18n", "t", "ValidateConfirmPassword", "confpassword", "pwInputRef", "setShowPW", "showPW", "handleClick", "useCallback", "pwInput", "current", "type", "focus", "onClick", "title", "FiEye", "style", "color", "handleRedirect", "setWillRedirect", "emailError", "passwordError", "setEmailError", "setPasswordError", "members", "reg_google", "reg_apple", "setEmail", "setPassword", "setEmailOptIn", "email", "emailOptIn", "spanErrorRefs", "smooth_login", "prefix", "useTranslation", "honeypotRef", "useRef", "lpC<PERSON>ie", "handleChange", "event", "inputType", "el", "target", "value", "isFocus", "isInput", "classList", "add", "remove", "checked", "keyCode", "registerUser", "_honeypotRef$current", "<PERSON><PERSON><PERSON><PERSON>", "path", "domain", "isEmailPassed", "validateEmail", "isPasswordPassed", "validatePassword", "document", "querySelector", "axios", "post", "pass_con", "headers", "then", "res", "output", "success", "mixpanel", "track", "qp", "ttq", "identify", "contents", "content_id", "login_token", "content_name", "description", "error", "console", "message", "toastr", "flow", "chatpdf", "top", "pricing", "iSplanEnt", "process", "log", "catch", "response", "status", "isPassed", "test", "isFormValid", "trim", "ref", "name", "autocomplete", "placeholder", "onChange", "e", "onKeyUp", "Pass<PERSON><PERSON>oggler", "id", "for", "motion", "button", "disabled", "whileHover", "scale", "whileTap", "onKeyDown", "key", "onBlur", "onFocus", "backgroundColor", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lazy", "RegButtons", "params", "URLSearchParams", "search", "willRedirect", "useState", "fromChatapp", "auth", "<PERSON><PERSON>", "formRef", "regGoogleShown", "setRegGoogleShown", "regAppleShown", "setRegAppleShown", "isCompactScreen", "setIsCompactScreen", "reg_pop", "get", "useEffect", "checkScreenSize", "width", "innerWidth", "height", "innerHeight", "addEventListener", "removeEventListener", "getLocales", "redirectToPay", "self", "setTimeout", "setSpanHeights", "for<PERSON>ach", "i", "_ref$scrollHeight", "errMsg", "textContent", "currHeight", "offsetHeight", "scrollHeight", "form", "isChromium", "navigator", "userAgent", "includes", "inputs", "querySelectorAll", "input", "matches", "positionClass", "<PERSON><PERSON><PERSON><PERSON>", "setInterval", "google_apple_error_code", "google_error", "errors", "find", "error_code", "undefined", "RemoveCookie", "clearInterval", "regGoogleParam", "regAppleParam", "parentUrl", "chatProUrl", "prefetchLink", "createElement", "rel", "as", "setAttribute", "head", "append<PERSON><PERSON><PERSON>", "cookie", "reg", "lp", "<PERSON><PERSON><PERSON>", "content", "Suspense", "fallback", "src", "LeftRect", "alt", "minHeight", "reg<PERSON><PERSON>", "RegForm", "isAiHub", "tabIndex", "preventDefault", "open", "DefaultContext", "size", "attr", "IconContext", "React", "__assign", "Object", "assign", "s", "n", "arguments", "p", "prototype", "hasOwnProperty", "call", "apply", "this", "__rest", "getOwnPropertySymbols", "propertyIsEnumerable", "Tree2Element", "tree", "map", "node", "tag", "child", "GenIcon", "props", "IconBase", "elem", "conf", "svgProps", "computedSize", "stroke", "fill", "strokeWidth", "xmlns", "Consumer"], "sourceRoot": ""}