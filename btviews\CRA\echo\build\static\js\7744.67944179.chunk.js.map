{"version": 3, "file": "static/js/7744.67944179.chunk.js", "mappings": "mHA0FA,MACA,EAAe,IAA0B,yD,+IC1FlC,SAASA,EAAYC,GACxB,OAAIA,EAC0B,QAA3BA,EAASC,cAAgC,IACd,QAA3BD,EAASC,cAAgC,IACd,QAA3BD,EAASC,cAAgC,IACd,QAA3BD,EAASC,eACkB,QAA3BD,EAASC,cADgC,KAErC,GANc,EAOzB,CAEO,SAASC,EAAWF,GACvB,OAAIA,EAC0B,QAA3BA,EAASC,eACkB,QAA3BD,EAASC,cADgC,KAEd,QAA3BD,EAASC,cAAgC,KACd,QAA3BD,EAASC,cAAgC,KACd,QAA3BD,EAASC,cAAgC,KACd,QAA3BD,EAASC,cAAgC,KACd,QAA3BD,EAASC,cAAgC,KACrC,KARc,EASzB,CAEO,SAASE,EAAWC,GACvB,IAAIA,EAAS,MAAO,GACpB,MACMC,EAAO,IAAIC,KAAKF,EAAQG,QAAQ,KAAK,MAE3C,MAHc,CAAC,UAAU,WAAW,QAAQ,QAAQ,MAAM,OAAO,OAAO,SAAS,YAAY,UAAU,WAAW,YAGrGF,EAAKG,YAAc,IAAMH,EAAKI,UAAY,IAAMJ,EAAKK,aACtE,CAEO,SAASC,EAASC,GACrB,OAAGA,EAAKC,YAAoBD,EAAKC,YAC9BD,EAAKE,MAAcF,EAAKE,MACpB,GACX,CAEO,SAASC,EAAiBC,GAC7B,OAAOA,EAAEC,WAAWV,QAAQ,wBAAyB,IACzD,CAEO,SAASW,EAAaC,GAC3B,MAAMC,EAAcC,WAAWF,GAC/B,OACMJ,EADEK,EAAc,GAAM,EACLA,EAAYE,QAAQ,GACpBF,EAAYE,QAAQ,GAC7C,CAEO,SAASC,EAAavB,EAAUc,GACnC,OAAId,GAAac,EAEdU,MAAMV,GAAe,GAErBO,WAAWP,IAAU,EACU,QAA3Bd,EAASC,cACDiB,EAAaJ,GAAOW,eAAe,SAAW,OACpB,QAA3BzB,EAASC,cACRiB,EAAaJ,GAAOW,eAAe,SAAW,MACpB,QAA3BzB,EAASC,cACRiB,EAAaJ,GAAOW,eAAe,SAAW,MACpB,QAA3BzB,EAASC,cACRiB,EAAaJ,GAAOW,eAAe,SAAW,MACpB,QAA3BzB,EAASC,cACRiB,EAAaJ,GAAOW,eAAe,SAAW,KACrB,QAA3BzB,EAASC,cACPiB,EAAaJ,GAAOW,eAAe,SAAW,MACrB,QAA3BzB,EAASC,cACP,KAAOiB,EAAaJ,GAAOW,eAAe,SAChB,QAA3BzB,EAASC,cACRiB,EAAaJ,GAAOW,eAAe,SAAW,KACpB,QAA3BzB,EAASC,cACRiB,EAAaJ,GAAOW,eAAe,SAAW,MACpB,QAA3BzB,EAASC,cACRiB,EAAaJ,GAAOW,eAAe,SAAW,KACpB,QAA3BzB,EAASC,cACRiB,EAAaJ,GAAOW,eAAe,SAAW,IAElD1B,EAAYC,GAAYkB,EAAaJ,GAAOW,eAAe,SAG/D,IAAM1B,EAAYC,KAAoC,EAAvBkB,EAAaJ,IAAaW,eAAe,SA/BhD,EAgCnC,CAEO,SAASC,EAAQC,EAAKC,GAEzBD,EAAM,IAAIrB,KAAKqB,GAEf,IAAIE,IADJD,EAAM,IAAItB,KAAKsB,IACAE,UAAYH,EAAIG,WAAa,IAE5C,OADAD,GAAQ,GACDE,KAAKC,IAAID,KAAKE,MAAMJ,GAC/B,CAEO,SAASK,EAAe9B,GAC3B,IAAIA,EAAS,MAAO,GACpB,MACMC,EAAO,IAAIC,KAAKF,EAAQG,QAAQ,KAAK,MAE3C,MAHc,CAAC,UAAU,WAAW,QAAQ,QAAQ,MAAM,OAAO,OAAO,SAAS,YAAY,UAAU,WAAW,YAGrGF,EAAKG,YAAc,IAAMH,EAAKI,UAAY,IAAMJ,EAAKK,cAAgB,IAAML,EAAK8B,WAAa,IAAM9B,EAAK+B,YACzH,CAEO,SAASC,EAAUC,GAAU,IAAT,KAACC,GAAKD,EAC3BE,EAAa,GACbC,EAAW,GAgBf,MAf8B,WAA1BF,EAAKG,mBACPF,EAAajB,EAAagB,EAAKvC,SAAUqB,WAAWkB,EAAKzB,MAAQ,KAAKQ,QAAQ,IAC9EmB,GAAWE,EAAAA,EAAAA,MAAA,OAAKC,MAAM,eAAcC,SAAA,CAAC,UAAQtB,EAAagB,EAAKvC,SAAUuC,EAAKzB,OAAO,YAGzD,YAA1ByB,EAAKG,mBACPF,EAAajB,EAAagB,EAAKvC,SAAUqB,WAAWkB,EAAKzB,MAAQ,IAAIQ,QAAQ,IAC7EmB,GAAWE,EAAAA,EAAAA,MAAA,OAAKC,MAAM,eAAcC,SAAA,CAAC,UAAQtB,EAAagB,EAAKvC,SAAUuC,EAAKzB,OAAO,aAGnFyB,EAAK1B,cACP2B,EAAajB,EAAagB,EAAKvC,SAAUqB,WAAWkB,EAAK1B,YAAc0B,EAAKO,YAAYxB,QAAQ,IAChGmB,GAAWE,EAAAA,EAAAA,MAAA,OAAKC,MAAM,eAAcC,SAAA,CAAC,UAAQtB,EAAagB,EAAKvC,SAAUuC,EAAK1B,kBAI9E8B,EAAAA,EAAAA,MAAAI,EAAAA,SAAA,CAAAF,SAAA,EACEF,EAAAA,EAAAA,MAAA,KAAGK,UAAW,qCAA+D,MAA1BC,EAAAA,EAAAA,IAAU,YAAsB,OAAS,IAAKJ,SAAA,CAC9FL,EAAW,KAACU,EAAAA,EAAAA,KAAA,QAAMF,UAAU,UAASH,SAAC,gBAExCJ,IAGP,CAEO,SAASU,EAAcC,GAAU,IAAT,KAACb,GAAKa,EACnC,MAA2B,QAAvBH,EAAAA,EAAAA,IAAU,SACLZ,EAAW,CAACE,SAEjBA,EAAK1B,aACCqC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wCAAuCH,SAAEtB,EAAagB,EAAKvC,SAAUuC,EAAK1B,gBAG/F8B,EAAAA,EAAAA,MAAA,KAAGK,UAAU,wCAAuCH,SAAA,CACjDtB,EAAagB,EAAKvC,SAAUuC,EAAKzB,QAClC6B,EAAAA,EAAAA,MAAA,QAAMK,UAAU,UAASH,SAAA,CAAC,IACK,YAA1BN,EAAKG,iBAAiC,QAAU,YAI3D,CAqBO,SAASW,IACd,MAAMC,EAAkBC,OAAOC,SAASC,KAExC,OAAIH,EAAgBI,QAAQ,YAAc,EAC/B,WACAJ,EAAgBI,QAAQ,QAAU,EAClC,OAGJ,EACT,C,gSC5KA,MAWA,EAXeC,KACbhB,EAAAA,EAAAA,MAAAI,EAAAA,SAAA,CAAAF,SAAA,EACEF,EAAAA,EAAAA,MAAA,OAAK,cAAY,OAAOK,UAAU,wEAAwEY,QAAQ,cAAcC,KAAK,OAAOC,MAAM,6BAA6BC,KAAK,MAAKlB,SAAA,EACvLK,EAAAA,EAAAA,KAAA,QAAMc,EAAE,+WAA+WH,KAAK,kBAC5XX,EAAAA,EAAAA,KAAA,QAAMc,EAAE,glBAAglBH,KAAK,oBAE/lBX,EAAAA,EAAAA,KAAA,QAAMF,UAAU,UAASH,SAAC,kB,eCH9B,MAsDA,EAtDsBP,IAAA,IAAC,eACrB2B,EAAc,QACdC,EAAO,UACPC,EAAS,WACTC,EAAU,YACVC,EAAW,OACXC,EAAM,QACNC,EAAO,MACPC,EAAK,IACLC,EAAG,IACHC,GACDpC,EAAA,OACCK,EAAAA,EAAAA,MAAA,UACEK,UAAW,sDAAsDqB,+IAC/DD,IAAeD,EACX,kDACA,kBAEND,QAASA,EACTS,SAAUR,EAAUtB,SAAA,CAEnBoB,IAAmBK,GAAUH,IAC5BjB,EAAAA,EAAAA,KAAAH,EAAAA,SAAA,CAAAF,UACEF,EAAAA,EAAAA,MAAA,OAAKoB,KAAK,SAASf,UAAU,WAAUH,SAAA,EACrCK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,oEACfE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,yCAAwCH,UACrDK,EAAAA,EAAAA,KAACS,EAAM,WAKdM,IAAmBK,IAAWH,IAC7BjB,EAAAA,EAAAA,KAAC0B,EAAAA,IAAa,CAAC5B,UAAU,+DAE3BE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,mEAAkEH,UAC/EF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,6CAA4CH,SAAA,EACzDK,EAAAA,EAAAA,KAAA,OACEuB,IAAKA,EACLC,IAAKA,EACL1B,UAAU,wCACV6B,QAAQ,UAEV3B,EAAAA,EAAAA,KAAA,SACEqB,QAASA,EACTO,GAAG,cACH9B,UAAU,kEACV+B,wBAAyB,CAAEC,OAAQR,cCX7C,MACA,EAAe,IAA0B,uDCPzC,MACA,EAAe,IAA0B,uDCIzC,MACA,EAAe,IAA0B,uDCAzC,MACA,EAAe,IAA0B,uDCDnCS,GAAUhC,EAAAA,EAAAA,IAAU,YAAaA,EAAAA,EAAAA,IAAU,WAAa,GACxDiC,GAAKjC,EAAAA,EAAAA,IAAU,WAAYA,EAAAA,EAAAA,IAAU,UAAY,GACjDkC,GAAMlC,EAAAA,EAAAA,IAAU,QAASA,EAAAA,EAAAA,IAAU,OAAS,GAC5CmC,GAAUnC,EAAAA,EAAAA,IAAU,YAAaA,EAAAA,EAAAA,IAAU,WAAa,GAC9D,IAAIV,EAAO,KAEX,MAqBM8C,GAXoBC,EAWSC,EAXAC,EAWYC,EArBvBC,MACtB,MAAMC,EAAOC,SAASC,cAAc,UAEpC,SAAOF,EAAKG,aAAcH,EAAKG,WAAW,QAC2B,IAA5DH,EAAKI,UAAU,cAAcrC,QAAQ,oBAOzCgC,GAGEJ,EAFEE,GAFcQ,IAACV,EAASE,EAcnCS,eAAeC,IACb,GAAI3D,EAAM,OAAOA,EACjB,MACM4D,SADiBC,EAAAA,EAAMC,KAAK,qCAA6C,CAAEC,QAASrB,GAAW,CAAEsB,QAAS,CAAE,eAAgB,wCAC1G3F,KACxB,OAAIuF,EAAOK,SACTjE,EAAO4D,EAAOvF,KACPuF,EAAOvF,MAEP,EAEX,CA6sBA,QAnsBA,SAAgB0B,GAAyC,IAAxC,eAAE2B,EAAc,kBAAEwC,GAAmBnE,EACpD,MAAMoE,GAAOC,EAAAA,EAAAA,IAAK,kBACZC,GAASC,EAAAA,EAAAA,cACT,KAAEjG,IAASkG,EAAAA,EAAAA,UAAS,QAASZ,IAC5Ba,EAAMC,IAAWC,EAAAA,EAAAA,UAAS,KAC1BC,EAAYC,IAAiBF,EAAAA,EAAAA,UAAS,KACtCG,EAAUC,IAAeJ,EAAAA,EAAAA,UAAS,KAClCK,EAAKC,IAAUN,EAAAA,EAAAA,UAAS,KACxBO,EAAWC,IAAgBR,EAAAA,EAAAA,UAAS,KACpCS,EAAiBC,IAAsBV,EAAAA,EAAAA,UAAS,KAChDW,EAAeC,IAAoBZ,EAAAA,EAAAA,UAAS,KAC5Ca,EAAUC,IAAed,EAAAA,EAAAA,UAAS,KAClCe,EAAcC,IAAmBhB,EAAAA,EAAAA,WAAS,IAC1CiB,EAAeC,IAAkBlB,EAAAA,EAAAA,WAAS,IAC1CmB,EAAaC,IAAkBpB,EAAAA,EAAAA,WAAS,IACxCqB,GAAYC,KAAiBtB,EAAAA,EAAAA,WAAS,IACtCuB,GAAmBC,KAAwBxB,EAAAA,EAAAA,WAAS,IACpDyB,GAAkBC,KAAuB1B,EAAAA,EAAAA,WAAS,IAClD2B,GAAgBC,KAAqB5B,EAAAA,EAAAA,UAAS,OAE9C9C,GAAW2E,KAAgB7B,EAAAA,EAAAA,WAAS,IACpC8B,GAAqBC,KAA0B/B,EAAAA,EAAAA,WAAS,IACxDgC,GAAiBC,KAAsBjC,EAAAA,EAAAA,WAAS,IAChDkC,GAAoBC,KAAyBnC,EAAAA,EAAAA,WAAS,IACtDoC,GAAmBC,KAAwBrC,EAAAA,EAAAA,WAAS,GAwI3D,IAtIAsC,EAAAA,EAAAA,WAAU,KACRC,IAAAA,QAAiB,CACfC,cAAe,qBAEhB,KAEHF,EAAAA,EAAAA,WAAU,KAER,IAAIG,GAAezG,EAAAA,EAAAA,IAAU,qBAEV0G,IAAfD,GAA2C,KAAfA,GAC9BE,WAAW,YACTC,EAAAA,EAAAA,IAAU,eAAe,IACzBL,IAAAA,MAAaE,EACf,EAAG,MAEJ,KAGHH,EAAAA,EAAAA,WAAU,KACR,IAAa,YAARpE,GAA6B,aAARA,IAAyBvE,GAAUgG,EAAQ,CACnE,IAAI9F,EAAQ,IAGVA,EADuB,KAArBF,EAAKC,aAAsBD,EAAKC,YAAc,EACrB,IAAnBD,EAAKC,YAEQ,IAAbD,EAAKE,MAGf,IAAIgJ,GAAU5J,EAAAA,EAAAA,IAAWU,EAAKZ,UAC1B+J,EAAKnD,EAAOgC,eAAe,CAC7BkB,QAASA,EACT9J,SAAUY,EAAKZ,SAASC,cACxB+J,MAAO,CACLxF,MAAO5D,EAAKqJ,UACZC,OAAQpJ,GAEVqJ,kBAAkB,EAClBC,mBAAmB,IAGnBL,EAAGM,iBAAiBC,KAAKC,IAClBA,GAOL1B,GAAkBkB,GACdQ,EAAOC,WACTnC,GAAe,GACfI,IAAqB,IAIZ8B,EAAOE,WAChBlC,IAAc,GACdI,IAAoB,KAfpB+B,QAAQC,IAAI,mDAoBpB,GACC,CAAC/J,EAAMgG,KAEV2C,EAAAA,EAAAA,WAAU,KACJX,IACAA,GAAegC,GAAG,gBAAiB3E,UAC/B,MAAM4E,EAAkBC,EAAEC,cAAcjG,GAClCkG,EAAcF,EAAEC,cAAcE,WAC9BC,EAAYJ,EAAEC,cAAcG,UAElC,IAAIC,EAAoB,GACpBC,EAAWxF,SAASyF,kBAAkB,YACtCD,EAAS,KACTD,EAAoBC,EAAS,GAAGE,OAGpC1F,SAAS2F,cAAc,qBAAqBC,UAAUC,IAAI,UAC1D,IAUI,IAAItF,SATmBC,EAAAA,EAAMC,KAAK,sEAA8E,CAC5GnB,KACAwG,mBAAoBb,EACpBvE,QAASrB,EACT0G,MAAOX,EACPY,SAAUV,EACVW,oBAAqBV,GACtB,CAAE5E,QAAS,CAAE,eAAgB,wCAEV3F,KACtB,GAAKuF,EAAOK,QAMR,OAJAsE,EAAEgB,SAAS,WACXtC,IAAAA,QAAe,YACfuC,EAAAA,EAAAA,IAAa,gBACbxI,OAAOC,SAASC,KAAO,mBAAqBlB,EAAKiC,MAAMjE,QAAQ,IAAK,IAAIA,QAAQ,IAAK,KAGjF4F,EAAOvF,MACX4I,IAAAA,MAAarD,EAAOvF,KAAKoL,KACzBpG,SAAS2F,cAAc,qBAAqBC,UAAUS,OAAO,UAC7DnB,EAAEgB,SAAS,OAEnB,CAAE,MAAOI,GACDA,EAAMC,UAAsC,MAA1BD,EAAMC,SAASC,QACjC5C,IAAAA,MAAa,uDAGrB,KAGT,CAACZ,UAsBWe,IAATjD,IAA+B,IAATA,EAAgB,OAE1C,GAAIsB,GACkB,WAAhBtB,EAAK0F,QAAwC,OAAjB1F,EAAK2F,QAEnC,YADA9I,OAAOC,SAASC,KAAO,eAM3B,IAAIpD,GAAO,IAAIC,KACfD,GAAKiM,QAAQjM,GAAKyB,UAAY,QAC9B,IAAIyK,GAAQ,IAAIjM,KACZkM,GAAc,IAAIlM,KAGtB,GAFAkM,GAAYC,QAAQF,GAAM9L,UAAY,IAElCG,GAAQA,EAAKZ,UAAYY,EAAKE,MAAO,CACvC,IAAIoJ,GAAStJ,EAAKE,MACO,KAArBF,EAAKC,cACPqJ,GAAStJ,EAAKC,cAEhBgJ,EAAAA,EAAAA,IAAU,WAAYjJ,EAAKZ,SAAU,CAAE0M,QAASF,GAAaG,KAAM,OACnE9C,EAAAA,EAAAA,IAAU,WAAYjJ,EAAKZ,SAAU,CAAE0M,QAASF,GAAaI,OAAQ,cAAeD,KAAM,OAC1F9C,EAAAA,EAAAA,IAAU,SAAUK,GAAQ,CAAEwC,QAASF,GAAaG,KAAM,OAC1D9C,EAAAA,EAAAA,IAAU,SAAUK,GAAQ,CAAEwC,QAASF,GAAaI,OAAQ,cAAeD,KAAM,KACnF,CAGA,MAyIME,GAAeA,KACnB1E,GAAe,GAEf/B,EAAAA,EAAMC,KAAK,uDAA+D,CACxEnB,KACAoB,QAASrB,GACR,CAAEsB,QAAS,CAAE,eAAgB,uCAAyC+D,KAAK,SAAUwC,GA/IlEA,KACtB,IAAI3G,EAAS2G,EAAIlM,KAEjB,GAAIuF,EAAOK,QAGT,OAFAjD,OAAOC,SAASjD,QAAQ4F,EAAOvF,KAAKmM,WACpCjE,IAAa,GAIfA,IAAa,GACT3C,EAAOvF,MAAM4I,IAAAA,MAAarD,EAAOvF,KAAKoL,MAsIxCgB,CAAeF,EACjB,IAGIG,GAA4B3I,IAChCmC,EAAkBnC,GAClB0E,IAAuB,GACvBE,IAAmB,GACnBE,IAAsB,GACtBE,IAAqB,GACrBR,IAAa,GACbc,WAAW,KACTd,IAAa,GACbE,IAAuB,GACvBE,IAAmB,GACnBE,IAAsB,GACtBE,IAAqB,IACpB,MAKL,OACEpG,EAAAA,EAAAA,KAAAH,EAAAA,SAAA,CAAAF,SAEGjC,GACCsC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,eAAcH,UAC3BK,EAAAA,EAAAA,KAAA,OAAKF,UAAW,uCAA0D,OAAnBiB,EAA0B,WAAa,gBAAgBpB,UAC5GK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,6BAA4BH,UACzCK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,aAAYH,UACzBK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,yBAAwBH,UACrCF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,GAAEH,SAAA,EACfF,EAAAA,EAAAA,MAAA,OAAKK,UAAW,uEAAyF,OAAnBiB,EAA0B,kBAAoB,IAAKpB,SAAA,EACvIK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,kEAAiEH,SAAC,2BAChFK,EAAAA,EAAAA,KAAA,QAAMF,UAAU,gCAA+BH,SAAC,mDAIlDK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,yDAMdL,EAAAA,EAAAA,MAAA,OAAKK,UAAW,6HAA+I,OAAnBiB,EAA0B,iBAAmB,IAAKpB,SAAA,EAE5LK,EAAAA,EAAAA,KAACgK,EAAa,CACZhJ,QAASA,IAAM+I,GAAyB,cACxC9I,UAAW4E,GACX3E,WAA+B,eAAnBH,EACZI,YAAa,kBACbC,OAAO,aACPC,QAAQ,SACRC,MAAM,6BACNC,IAAK0I,EACLzI,IAAI,cACJT,eAAgBA,KAIlBf,EAAAA,EAAAA,KAACgK,EAAa,CACZhJ,QAASA,IAAM+I,GAAyB,UACxC9I,UAAW8E,GACX7E,WAA+B,WAAnBH,EACZK,OAAO,SACPC,QAAQ,cACRC,MAAM,SACNC,IAAK2I,EACL1I,IAAI,SACJT,eAAgBA,KAIlBf,EAAAA,EAAAA,KAACgK,EAAa,CACZhJ,QAASA,IAAM+I,GAAyB,cACxC7I,WAA+B,eAAnBH,EACZE,UAAWgF,GACX9E,YAAa,kBACbC,OAAO,aACPC,QAAQ,aACRC,MAAM,aACNC,IAAK4I,EACL3I,IAAI,aACJT,eAAgBA,KAIlBf,EAAAA,EAAAA,KAACgK,EAAa,CACZhJ,QAASA,IAAM+I,GAAyB,aACxC9I,UAAWkF,GACXjF,WAA+B,cAAnBH,EACZK,OAAO,YACPC,QAAQ,YACRC,MAAM,YACNC,IAAK6I,EACL5I,IAAI,YACJT,eAAgBA,QAIpBf,EAAAA,EAAAA,KAAA,OAAK4B,GAAG,eAAe9B,UAAW,2BAA8C,eAAnBiB,EAAkC,UAAY,iDAAiDpB,SACzJsB,IACCjB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,0CAAyCH,UACtDF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,iEAAgEH,SAAA,EAC7EK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,mDACfE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,yCACfE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,kCACfE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,yCACfE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,kCAEfL,EAAAA,EAAAA,MAAA,OAAKK,UAAU,4BAA2BH,SAAA,EACxCK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,yCACfE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,sDAEjBE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,4BAA2BH,UACxCK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,2CAEjBE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uCACfE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,yCACfE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,6CAInBL,EAAAA,EAAAA,MAAAI,EAAAA,SAAA,CAAAF,SAAA,EACEF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,OAAMH,SAAA,EACnBK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,+CAA8CH,UAC3DF,EAAAA,EAAAA,MAAA,SAA0BK,UAAU,oFAAmFH,SAAA,EACrHK,EAAAA,EAAAA,KAAA,OAAKuB,IAAK0I,EAAQzI,IAAK,aAAc1B,UAAU,yBAAyB6B,QAAQ,UAChF3B,EAAAA,EAAAA,KAAA,QAAMF,UAAU,2BAA0BH,SAAC,2BAFjC,iBAKdK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,+DACdL,EAAAA,EAAAA,MAAA,SAAOK,UAAU,yCAAyCuB,QAAQ,OAAM1B,SAAA,CAAC,oBAAgBK,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcH,SAAC,MACrH2E,IAAatE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,gEAA+DH,SAAE2E,QACjGtE,EAAAA,EAAAA,KAAA,SAAOF,UAAU,6DACfuK,KAAK,OACLzI,GAAG,OACHiC,KAAK,OACLyG,YAAY,WACZlC,MAAOvE,EACP0G,SA9QFC,IACxB,IAAIC,EAAQD,EAAME,OAAOtC,MACzBqC,EAAQA,EAAMpN,QAAQ,cAAe,IACrCoN,EAAQA,EAAME,MAAM,EAAG,IACvB7G,EAAQ2G,IA2QkBG,QAAUJ,IACR1G,EAAQ0G,EAAME,OAAOtC,QAEvB3G,SAAUR,SAEdxB,EAAAA,EAAAA,MAAA,OAAKK,UAAU,OAAMH,SAAA,EACnBF,EAAAA,EAAAA,MAAA,SAAOK,UAAU,yCAAyCuB,QAAQ,cAAa1B,SAAA,CAAC,gBAAYK,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcH,SAAC,MACxH6E,IAAmBxE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,gEAA+DH,SAAE6E,QAEvG/E,EAAAA,EAAAA,MAAA,OAAKK,UAAU,WAAUH,SAAA,EACvBK,EAAAA,EAAAA,KAAA,SAAOF,UAAU,6DACfuK,KAAK,OACLzI,GAAG,cACHiC,KAAK,cACLyG,YAAY,sBACZlC,MAAOpE,EACPuG,SAxREC,IAC9B,IAAIC,EAAQD,EAAME,OAAOtC,MACzBqC,EAAQA,EAAMpN,QAAQ,MAAO,IAC7BoN,EAAQA,EAAMpN,QAAQ,KAAM,IAC5BoN,EAAQA,EAAMpN,QAAQ,WAAY,OAClCoN,EAAQA,EAAMpN,QAAQ,KAAM,IAC5BoN,EAAQA,EAAME,MAAM,EAAG,IACvB1G,EAAcwG,IAkRcG,QAAUJ,IACRvG,EAAcuG,EAAME,OAAOtC,QAE7B3G,SAAUR,MACZjB,EAAAA,EAAAA,KAAC6K,EAAAA,IAAa,CAAC/K,UAAU,0CAA0CgL,KAAM,YAG7ErL,EAAAA,EAAAA,MAAA,OAAKK,UAAU,eAAcH,SAAA,EAC3BF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,8BAA6BH,SAAA,EAC1CF,EAAAA,EAAAA,MAAA,SAAOK,UAAU,yCAAyCuB,QAAQ,kBAAiB1B,SAAA,CAAC,oBAAgBK,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcH,SAAC,MAChI+E,IAAiB1E,EAAAA,EAAAA,KAAA,QAAMF,UAAU,gEAA+DH,SAAE+E,QACrG1E,EAAAA,EAAAA,KAAA,SAAOF,UAAU,0EACfuK,KAAK,OACLzI,GAAG,kBACHiC,KAAK,kBACLyG,YAAY,QACZlC,MAAOlE,EACPqG,SAhSAC,IAC5B,IAAIC,EAAQD,EAAME,OAAOtC,MACzBqC,EAAQA,EAAMpN,QAAQ,MAAO,IAC7BoN,EAAQA,EAAME,MAAM,EAAG,GACnBF,EAAMM,QAAU,IAClBN,EAAQA,EAAME,MAAM,EAAG,GAAK,IAAMF,EAAME,MAAM,IAEhDxG,EAAYsG,IA0RgBG,QAAUJ,IACRrG,EAAYqG,EAAME,OAAOtC,QAE3B3G,SAAUR,SAEdxB,EAAAA,EAAAA,MAAA,OAAKK,UAAU,aAAYH,SAAA,EACzBF,EAAAA,EAAAA,MAAA,SAAOK,UAAU,+BAA+BuB,QAAQ,MAAK1B,SAAA,CAAC,KAACK,EAAAA,EAAAA,KAAA,UAAAL,SAAQ,mBAAuB,SAAKK,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcH,SAAC,MAC/HiF,IAAY5E,EAAAA,EAAAA,KAAA,QAAMF,UAAU,gEAA+DH,SAAEiF,QAChG5E,EAAAA,EAAAA,KAAA,SAAOF,UAAU,6DACfuK,KAAK,OACLzI,GAAG,MACHiC,KAAK,MACLyG,YAAY,MACZlC,MAAOhE,EACPmG,SArSLC,IACvB,IAAIC,EAAQD,EAAME,OAAOtC,MACzBqC,EAAQA,EAAMpN,QAAQ,MAAO,IAC7BoN,EAAQA,EAAME,MAAM,EAAG,GACvBtG,EAAOoG,IAkSqBG,QAAUJ,IACRnG,EAAOmG,EAAME,OAAOtC,QAEtB3G,SAAUR,YAGhBxB,EAAAA,EAAAA,MAAA,QAAMK,UAAU,4BAA2BH,SAAA,EAACK,EAAAA,EAAAA,KAACgL,EAAAA,IAAY,CAAClL,UAAU,qCAAqC,qBAAmBoC,GAAoB,oBAAoB,4CAA6ClC,EAAAA,EAAAA,KAAA,KAAGO,KAAK,sCAAsC0K,IAAI,sBAAsBP,OAAO,SAAS5K,UAAU,uCAAsCH,SAAC,yBAAwB,QAClXK,EAAAA,EAAAA,KAACkL,EAAAA,EAAOC,OAAM,CACZrL,UAAU,4FACVsL,WAAY,CAAEC,gBAAiB,WAC/BC,SAAU,CAAEC,MAAO,IACnBvK,QA1SJwK,KACpBzG,GAAgB,GAChBR,EAAa,IACbE,EAAmB,IACnBE,EAAiB,IACjBE,EAAY,IAGZ,IAAI4G,GAAU,EAEVxD,EAAoB,GACpBC,EAAWxF,SAASyF,kBAAkB,YACtCD,EAAS,KACXD,EAAoBC,EAAS,GAAGE,OAG7BvE,EAAK6H,SAAS,OACjBnH,EAAa,iDACbkH,GAAU,GAEPzH,IACHS,EAAmB,YACnBgH,GAAU,GAEPvH,GAAa,2BAA2ByH,KAAKzH,KAChDS,EAAiB,SACjB8G,GAAU,GAEPrH,GAAQ,YAAYuH,KAAKvH,KAC5BS,EAAY,YACZ4G,GAAU,GAEZ,IAAIG,EAAa/H,EAAKgI,MAAM,KACxBC,EAAaF,EAAW,GACxBG,EAAYH,EAAWA,EAAWb,OAAS,GAC3CiB,EAAU9H,EAAS2H,MAAM,KAAK,GAC9BI,EAAO/H,EAAS2H,MAAM,KAAK,GAS/B,GARmB,KAAfC,GAAmC,KAAdC,GACvBxH,EAAa,YACbkH,GAAU,GACc,KAAfK,GAAmC,KAAdC,IAC9BxH,EAAa,iDACbkH,GAAU,IAGPA,EACH,OAIF/I,SAAS2F,cAAc,qBAAqBC,UAAUC,IAAI,UAE1D,MAAM2D,EAAc,YAARjK,EACR,yDACA,kDAEJiB,EAAAA,EAAMC,KAAK+I,EAAK,CACdlK,KACA8J,aACAC,YACAI,GAAInI,EACJgI,QAASA,EACTC,KAAM,KAAOA,EACb7H,IAAKA,EACLhB,QAASrB,EACT4G,oBAAqBV,EACrBmE,QAAS,QACR,CAAE/I,QAAS,CAAE,eAAgB,uCAAyC+D,KAAK,SAAUwC,GACtF,IAAI3G,EAAS2G,EAAIlM,KACjB,GAAIuF,EAAOK,QACT,OAAIL,EAAOoJ,UAA8B,KAAlBpJ,EAAOoJ,cAC5BhM,OAAOC,SAASC,KAAO0C,EAAOoJ,WAGhC/F,IAAAA,QAAe,YACfuC,EAAAA,EAAAA,IAAa,gBACbxI,OAAOC,SAASC,KAAO,mBAAqBlB,EAAKiC,MAAMjE,QAAQ,IAAK,IAAIA,QAAQ,IAAK,MAGvFqF,SAAS2F,cAAc,qBAAqBC,UAAUS,OAAO,UACzD9F,EAAOvF,MAAM4I,IAAAA,MAAarD,EAAOvF,KAAKoL,IAC5C,GAAGwD,MAAM,SAAUtD,GACbA,EAAMC,UAAsC,MAA1BD,EAAMC,SAASC,SACnCxG,SAAS2F,cAAc,qBAAqBC,UAAUS,OAAO,UAC7DzC,IAAAA,MAAa,wDAEjB,IAqNwB7E,SAAUR,GAAUtB,SAEnBuC,GAAoB,4BAM7BlC,EAAAA,EAAAA,KAAA,OAAK4B,GAAG,iBAAiB9B,UAAW,yDAA2E,WAAnBiB,EAA8B,UAAY,SAAUpB,SAC7IsB,IACCjB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,0CAAyCH,UACtDF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,iEAAgEH,SAAA,EAC7EK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,mDACfE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,kCACfE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,yCACfE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,mCACfE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,sDAInBL,EAAAA,EAAAA,MAAAI,EAAAA,SAAA,CAAAF,SAAA,EACEF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,kBAAiBH,SAAA,EAC5BK,EAAAA,EAAAA,KAAA,SAEEF,UAAW,gDAAgDH,UAE3DK,EAAAA,EAAAA,KAAA,OACEuB,IAAKY,EACLX,IAAK,SACL1B,UAAW,oBACX6B,QAAQ,UAPL,WAUT3B,EAAAA,EAAAA,KAAA,MAAIF,UAAU,kEAGhBL,EAAAA,EAAAA,MAAA,QAAMK,UAAU,sCAAqCH,SAAA,EACnDK,EAAAA,EAAAA,KAACgL,EAAAA,IAAY,CAAClL,UAAU,qCAAqC,8BACnCE,EAAAA,EAAAA,KAAA,UAAQF,UAAU,SAAQH,SAAC,WAAe,6CAAyCK,EAAAA,EAAAA,KAAA,KAAGO,KAAK,sCAAsC0K,IAAI,sBAAsBP,OAAO,SAAS5K,UAAU,uCAAsCH,SAAC,yBAAwB,QAEhRF,EAAAA,EAAAA,MAAA,UAAQK,UAAU,sHAAsH2B,SAAUR,GAAWD,QAASA,IAAM2I,KAAehK,SAAA,CAAC,aACjLK,EAAAA,EAAAA,KAAA,UAAQF,UAAU,SAAQH,SAAC,eAGtCK,EAAAA,EAAAA,KAAA,QAAMF,UAAU,2BAA0BH,SAAC,+DAKjDK,EAAAA,EAAAA,KAAA,OAAK4B,GAAG,oBAAoB9B,UAAW,4DAA8E,eAAnBiB,EAAkC,UAAY,SAAUpB,SACvJsB,IACCjB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,0CAAyCH,UACtDF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,iEAAgEH,SAAA,EAC7EK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,mDACfE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,kCACfE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,yCACfE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,mCACfE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,sDAInBL,EAAAA,EAAAA,MAAAI,EAAAA,SAAA,CAAAF,SAAA,EACEF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,kBAAiBH,SAAA,EAC9BK,EAAAA,EAAAA,KAAA,SAEEF,UAAW,gDAAgDH,UAE3DK,EAAAA,EAAAA,KAAA,OACEuB,IAAK4I,EACL3I,IAAK,aACL1B,UAAW,0BACX6B,QAAQ,UAPL,eAUP3B,EAAAA,EAAAA,KAAA,MAAIF,UAAU,kEAEhBE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,oBAAmBH,UAChCF,EAAAA,EAAAA,MAAA,QAAMK,UAAW,mCAAmCwF,GAAoC,eAAhB,eAAiC3F,SAAA,EACzGK,EAAAA,EAAAA,KAACgL,EAAAA,IAAY,CAAClL,UAAU,qCAAqC,6BAE3DE,EAAAA,EAAAA,KAACuM,EAAAA,IAAQ,CAACzM,UAAU,sDAAsDgL,KAAM,KAAM,+CAEtF9K,EAAAA,EAAAA,KAAA,KAAGO,KAAK,sCAAsC0K,IAAI,sBAAsBP,OAAO,SAAS5K,UAAU,uCAAsCH,SAAC,yBAAwB,UAIrKF,EAAAA,EAAAA,MAAA,UAAQK,UAAW,4FAA4FoF,EAAgD,GAAlC,iCAAwCtD,GAAG,2BAA2BH,SAAUR,GAAWD,QAhehNwL,KAC1B9G,IAAkBR,EACpBQ,GAAe+G,OAGflH,IAAqB,IA2dwP5F,SAAA,CAAC,YAExPK,EAAAA,EAAAA,KAACuM,EAAAA,IAAQ,CAACzM,UAAU,0BAA0BgL,KAAM,KAAM,SAM1DxF,IAGAtF,EAAAA,EAAAA,KAAA,QAAMF,UAAU,2BAA0BH,SAAC,8DAF7CK,EAAAA,EAAAA,KAAA,QAAMF,UAAU,wCAAuCH,SAAC,0JAQ9DK,EAAAA,EAAAA,KAAA,OAAK4B,GAAG,mBAAmB9B,UAAW,2DAA6E,cAAnBiB,EAAiC,UAAY,SAAUpB,SACpJsB,IACCjB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,0CAAyCH,UACtDF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,iEAAgEH,SAAA,EAC7EK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,mDACfE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,kCACfE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,yCACfE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,mCACfE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,sDAInBL,EAAAA,EAAAA,MAAAI,EAAAA,SAAA,CAAAF,SAAA,EACEF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,kBAAiBH,SAAA,EAC9BK,EAAAA,EAAAA,KAAA,SAEEF,UAAW,gDAAgDH,UAE3DK,EAAAA,EAAAA,KAAA,OACEuB,IAAK6I,EACL5I,IAAK,YACL1B,UAAW,iCACX6B,QAAQ,UAPL,cAUP3B,EAAAA,EAAAA,KAAA,MAAIF,UAAU,kEAEhBE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,oBAAmBH,UAChCF,EAAAA,EAAAA,MAAA,QAAMK,UAAW,mCAAkC0F,GAAmB,cAAgB,QAAS7F,SAAA,EAC7FK,EAAAA,EAAAA,KAACgL,EAAAA,IAAY,CAAClL,UAAU,qCAAqC,6BAE7DE,EAAAA,EAAAA,KAAC0M,EAAAA,IAAO,CAAC5M,UAAU,qBAAqBgL,KAAM,KAAM,+CAEpD9K,EAAAA,EAAAA,KAAA,KAAGO,KAAK,sCAAsC0K,IAAI,sBAAsBP,OAAO,SAAS5K,UAAU,uCAAsCH,SAAC,yBAAwB,UAIrKF,EAAAA,EAAAA,MAAA,UAAQK,UAAW,4FAA4FsF,GAA+C,GAAlC,iCAAwCxD,GAAG,0BAA0BH,SAAUR,GAAWD,QA7gB/M2L,KACzBjH,IAAkBN,GACpBM,GAAe+G,OAGfhH,IAAoB,IAwgBsP9F,SAAA,CAAC,YAErPK,EAAAA,EAAAA,KAAC0M,EAAAA,IAAO,CAAC5M,UAAU,0BAA0BgL,KAAM,KAAM,SAEzDtF,IAGAxF,EAAAA,EAAAA,KAAA,QAAMF,UAAU,2BAA0BH,SAAC,6DAF7CK,EAAAA,EAAAA,KAAA,QAAMF,UAAU,wCAAuCH,SAAC,uJAU7DqF,IACCvF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,sGAAqGH,SAAA,EAClHK,EAAAA,EAAAA,KAACS,EAAM,KACPT,EAAAA,EAAAA,KAAA,QAAAL,SAAM,mCASpB,IAGV,EC7wBMoC,GAAUhC,EAAAA,EAAAA,IAAU,YAAaA,EAAAA,EAAAA,IAAU,WAAa,GAE9D,IAAIV,EAAO,KAEX0D,eAAeC,IACb,GAAI3D,EAAM,OAAOA,EACjB,MAKM4D,SALiBC,EAAAA,EAAMC,KAC3B,qCACA,CAAEC,QAASrB,GACX,CAAEsB,QAAS,CAAE,eAAgB,wCAEP3F,KACxB,OAAIuF,EAAOK,SACTjE,EAAO4D,EAAOvF,KACPuF,EAAOvF,MAEP,EAEX,CAEA,MAAMkP,EAAiBA,KACrBnN,EAAAA,EAAAA,MAAAI,EAAAA,SAAA,CAAAF,SAAA,EACEF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,uIAAsIH,SAAA,EAACK,EAAAA,EAAAA,KAAC6M,EAAAA,IAAM,CAAC/M,UAAU,+CAA+C,uBACxNL,EAAAA,EAAAA,MAAA,OAAKK,UAAU,2CAA0CH,SAAA,EACxDK,EAAAA,EAAAA,KAAA,OAAKuB,IAAKuL,EAAMtL,IAAI,kBAAkB1B,UAAU,2GAChDL,EAAAA,EAAAA,MAAA,OAAKK,UAAU,iHAAgHH,SAAA,EAC/HK,EAAAA,EAAAA,KAAA,OAAKuB,IAAKwL,EAAQvL,IAAI,YAAY1B,UAAU,wBAC5CE,EAAAA,EAAAA,KAAA,UACEgB,QAASA,KACPX,OAAO2M,KACL,4FACA,eACA,6HAGJC,MAAM,0CACNnN,UAAU,GAAEH,UACZK,EAAAA,EAAAA,KAAA,OAAK2B,QAAQ,OACXJ,IAAI,wGACJC,IAAI,+CACJ1B,UAAU,4BAuJlB,QA/IA,WACE,MAAM,KAAEpC,IAASkG,EAAAA,EAAAA,UAAS,QAASZ,IAC5BkK,EAAaC,IAAkBpJ,EAAAA,EAAAA,UAAS1D,OAAO+M,aAC/CrM,EAAgBwC,IAAqBQ,EAAAA,EAAAA,UAAS,MAC/CP,GAAOC,EAAAA,EAAAA,IAAK,kBACZqB,EAAetB,GAAwB,WAAhBA,EAAK0F,QAAwC,OAAjB1F,EAAK2F,QAiC9D,IA9BA9C,EAAAA,EAAAA,WAAU,KACR,MAAMgH,EAAeA,KACnBF,EAAe9M,OAAO+M,aAKxB,OAFA/M,OAAOiN,iBAAiB,SAAUD,GAE3B,KACLhN,OAAOkN,oBAAoB,SAAUF,KAEtC,KAYHhH,EAAAA,EAAAA,WAAU,KACJ6G,GAAe,MAA2B,OAAnBnM,GACzBwC,EAAkB,eAEnB,CAAC2J,EAAanM,KAIZyC,EAAM,OAAO,KAElB,GAAIsB,EAEF,OADAzE,OAAOC,SAASC,KAAO,cAChB,KAGT,MAAMpD,EAAO,IAAIC,KACjBD,EAAKiM,QAAQjM,EAAKyB,UAAY,QAC9B,MAAMyK,EAAQ,IAAIjM,KACZkM,EAAc,IAAIlM,KAGxB,GAFAkM,EAAYC,QAAQF,EAAM9L,UAAY,IAElCG,GAAQA,EAAKZ,UAAYY,EAAKE,MAAO,CACvC,IAAIoJ,EAAStJ,EAAKE,MACd4P,EAAW9P,EAAK+P,UACK,KAArB/P,EAAKC,cACPqJ,EAAStJ,EAAKC,cAEhBgJ,EAAAA,EAAAA,IAAU,WAAYjJ,EAAKZ,SAAU,CAAE0M,QAASF,EAAaG,KAAM,OACnE9C,EAAAA,EAAAA,IAAU,WAAYjJ,EAAKZ,SAAU,CAAE0M,QAASF,EAAaI,OAAQ,cAAeD,KAAM,OAC1F9C,EAAAA,EAAAA,IAAU,SAAUK,EAAQ,CAAEwC,QAASF,EAAaG,KAAM,OAC1D9C,EAAAA,EAAAA,IAAU,SAAUK,EAAQ,CAAEwC,QAASF,EAAaI,OAAQ,cAAeD,KAAM,OACjF9C,EAAAA,EAAAA,IAAU,WAAY6G,EAAU,CAAEhE,QAASF,EAAaG,KAAM,OAC9D9C,EAAAA,EAAAA,IAAU,WAAY6G,EAAU,CAAEhE,QAASF,EAAaI,OAAQ,cAAeD,KAAM,KACvF,CAEA,OACEhK,EAAAA,EAAAA,MAAAI,EAAAA,SAAA,CAAAF,SAAA,EACEF,EAAAA,EAAAA,MAACiO,EAAAA,EAAM,CAAA/N,SAAA,EACLK,EAAAA,EAAAA,KAAA,SAAAL,SAAO,6BACPK,EAAAA,EAAAA,KAAA,QAAM6D,KAAK,cAAc8J,QAAQ,+FAGlCjQ,GACCsC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,mCAAkCH,UAC/CK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,iBAAgBH,UAC7BK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,qCAAoCH,UACjDK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,oDAAmDH,UAChEK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,oCAAmCH,UAChDK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,qEAAoEH,UACjFF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,qBAAoBH,SAAA,EACjCK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,QAAOH,UACtBK,EAAAA,EAAAA,KAAA,OAAKuB,IAAKqM,EAAAA,EAAWpM,IAAI,cAAc1B,UAAU,6BAA6B+N,MAAM,MAAMC,OAAO,UAEjGrO,EAAAA,EAAAA,MAAA,OAAKK,UAAU,yEAAwEH,SAAA,EACrFF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,kKAAiKH,SAAA,EAC9KF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,+JAA8JH,SAAA,EAC3KK,EAAAA,EAAAA,KAAA,OAAKF,UAAW,0BAA4C,OAAnBiB,EAA0B,kBAAoB,SAAUpB,UAC/FF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,mCAAkCH,SAAA,EAC/CF,EAAAA,EAAAA,MAAA,OAAKoO,MAAM,KAAKC,OAAO,KAAKpN,QAAQ,YAAYC,KAAK,OAAOC,MAAM,6BAA4BjB,SAAA,EAC5FK,EAAAA,EAAAA,KAAA,QAAMc,EAAE,8oCAA8oCH,KAAK,aAC3pCX,EAAAA,EAAAA,KAAA,QAAMc,EAAE,i3CAAi3CH,KAAK,gBAEh4CX,EAAAA,EAAAA,KAAA,KAAGF,UAAU,gFAA+EH,SAAC,wBAGjGK,EAAAA,EAAAA,KAAA,OAAKF,UAAW,0BAA4C,OAAnBiB,EAA0B,kBAAoB,UAAWpB,UAChGK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,mCAAkCH,UAC/CF,EAAAA,EAAAA,MAAA,UAAQK,UAAU,iFAAiFkB,QA7EtG+M,KAEvBxK,EADE2J,GAAe,KACC,aAGA,OAwEuIvN,SAAA,EAC/HK,EAAAA,EAAAA,KAAA,OAAK6N,MAAM,KAAKC,OAAO,KAAKpN,QAAQ,YAAYC,KAAK,OAAOC,MAAM,6BAA4BjB,UAC5FK,EAAAA,EAAAA,KAAA,QAAMc,EAAE,ibAAibH,KAAK,cAC1b,4BAKZX,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uJAEfL,EAAAA,EAAAA,MAAA,KAAGK,UAAU,mFAAkFH,SAAA,CAC5FjC,EAAKsQ,kBAAkB,YAE1BhO,EAAAA,EAAAA,KAAA,QAAMF,UAAU,gFAA+EH,SAC3FjC,EAAKuQ,aAAevQ,EAAKuQ,aAAe,+DAE5CjO,EAAAA,EAAAA,KAAA,OAAKF,UAAU,eAAcH,UAC3BF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,oIAAmIH,SAAA,EAChJK,EAAAA,EAAAA,KAAA,QAAMF,UAAU,QAAOH,SAAC,YAGxBK,EAAAA,EAAAA,KAAA,QAAMF,UAAU,QAAOH,UACtBtB,EAAAA,EAAAA,IAAaX,EAAKZ,SAAUY,EAAKE,eAItCoC,EAAAA,EAAAA,KAAA,OAAKF,UAAW,6CAA+D,OAAnBiB,EAA0B,OAAS,QAASpB,SACrGuN,EAAc,OAAQlN,EAAAA,EAAAA,KAACkO,EAAW,CAACnN,eAAgBA,EAAgBwC,kBAAmBA,OAEzFvD,EAAAA,EAAAA,KAAC4M,EAAc,QAEjB5M,EAAAA,EAAAA,KAAA,OAAKF,UAAU,oIAAmIH,SAC/IuN,GAAe,OAAQlN,EAAAA,EAAAA,KAACkO,EAAW,CAACnN,eAAgBA,EAAgBwC,kBAAmBA,wBASjG,KAGjB,E,eCnMA,IAAI4K,EAAiB,GAEnBA,EADS,WAHEpO,EAAAA,EAAAA,IAAU,SAAUA,EAAAA,EAAAA,IAAU,QAAU,IAIlCqO,8GAEAA,8GAGnB,MAAMC,GAAgBC,EAAAA,EAAAA,GAAWH,GAYjC,QAVA,WACE,OACEnO,EAAAA,EAAAA,KAAAH,EAAAA,SAAA,CAAAF,UACFK,EAAAA,EAAAA,KAACuO,EAAAA,SAAQ,CAAC7K,OAAQ2K,EAAc1O,UAC9BK,EAAAA,EAAAA,KAACwO,EAAS,OAId,C", "sources": ["assets/images/AIPRO.svg", "core/utils/main.jsx", "pay/components/loader.jsx", "pay/components/index_05_payment_button.jsx", "pay/components/mpay-assets/cc.svg", "pay/components/mpay-assets/pp.svg", "pay/components/mpay-assets/gp.svg", "pay/components/mpay-assets/ap.svg", "pay/components/index_05_payment_form.jsx", "pay/components/index_05_payment_receipt.jsx", "pay/index_05.jsx"], "sourcesContent": ["var _g, _defs;\nfunction _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\nimport * as React from \"react\";\nfunction SvgAipro(_ref, svgRef) {\n  let {\n    title,\n    titleId,\n    ...props\n  } = _ref;\n  return /*#__PURE__*/React.createElement(\"svg\", _extends({\n    width: 451,\n    height: 157,\n    viewBox: \"0 0 451 157\",\n    fill: \"none\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, _g || (_g = /*#__PURE__*/React.createElement(\"g\", {\n    clipPath: \"url(#clip0_2054_286)\"\n  }, /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M92.717 79.8955C91.7079 79.898 90.8221 79.5666 90.0844 78.8815C89.438 78.2806 88.7889 77.6721 88.2042 77.0144C87.6096 76.3467 86.6769 76.3763 86.107 77.0193C85.6035 77.5881 85.0483 78.1124 84.5007 78.6392C83.2546 79.8361 81.7939 80.17 80.1927 79.5345C78.5839 78.8964 77.7647 77.6277 77.6759 75.9088C77.5969 74.3657 78.5568 72.8595 79.9853 72.1993C81.4707 71.5118 83.1683 71.7739 84.3896 72.909C85.0064 73.4827 85.6159 74.0689 86.1736 74.6996C86.7732 75.3747 87.6441 75.4266 88.3054 74.697C88.8433 74.1034 89.4207 73.5446 90.0029 72.9931C91.1724 71.8876 92.9095 71.5982 94.3554 72.2437C95.8358 72.9065 96.7735 74.4077 96.7265 76.0424C96.6649 78.1989 94.8908 79.9029 92.717 79.898V79.8955Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M67.7897 52.4641C66.3315 52.4716 65.4408 52.1476 64.703 51.4625C64.0418 50.8467 63.4125 50.1963 62.7685 49.5607C61.9938 48.7965 61.3917 48.799 60.6096 49.5805C60.0273 50.1592 59.4575 50.7528 58.8652 51.324C57.6735 52.479 55.9735 52.7807 54.4807 52.1204C52.9607 51.4453 52.0034 49.9465 52.1022 48.2797C52.2008 46.6375 53.0151 45.4208 54.5324 44.7876C56.0894 44.1372 57.555 44.4093 58.8159 45.5469C59.4253 46.0959 60.0125 46.6771 60.5579 47.2904C61.2882 48.114 62.1296 48.1486 62.9068 47.2706C63.4373 46.6722 64.0196 46.1182 64.6019 45.5667C65.7887 44.4414 67.5455 44.1595 69.0036 44.8371C70.4841 45.5246 71.3971 47.0258 71.3305 48.6605C71.2441 50.7874 69.4824 52.4667 67.7873 52.4667L67.7897 52.4641Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M68.2449 83.3704C68.3484 82.7224 68.504 81.7086 68.6792 80.697C68.8543 79.688 68.5286 79.1711 67.5268 78.9733C66.725 78.8125 65.9107 78.7061 65.1113 78.533C63.2213 78.125 61.8889 76.426 61.9703 74.5142C62.0665 72.2217 63.8307 70.9234 65.3531 70.686C68.0179 70.2704 70.3421 72.6297 69.8979 75.2563C69.7722 76.0055 69.6439 76.7548 69.5205 77.5042C69.4884 77.7069 69.4564 77.9123 69.4515 78.1176C69.4317 78.7557 69.7771 79.2354 70.4063 79.3689C71.1268 79.5223 71.857 79.6335 72.5849 79.7448C74.3713 80.0168 75.6544 81.1248 76.0813 82.7794C76.6538 85.0002 75.1831 87.2457 72.9379 87.6885C70.616 88.146 68.1757 86.2887 68.2399 83.3728L68.2449 83.3704Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M89.2851 68.3141C87.6986 68.3438 85.8727 67.2137 85.4114 65.188C84.9944 63.3655 85.8802 61.518 87.558 60.7093C88.3525 60.326 89.1692 59.9946 89.9711 59.6286C90.8741 59.2181 91.0986 58.6493 90.7163 57.7392C90.4128 57.0194 90.0845 56.3122 89.7835 55.59C88.7645 53.1467 90.0994 50.5919 92.6827 50.033C95.1897 49.4889 97.6644 51.7048 97.4003 54.267C97.24 55.8274 96.4528 56.9577 95.0316 57.623C94.289 57.9716 93.5316 58.2858 92.7839 58.6196C91.8512 59.0375 91.6218 59.6088 92.0214 60.5487C92.3275 61.2658 92.6532 61.9781 92.9517 62.6977C93.9337 65.057 92.6654 67.587 90.1931 68.193C89.8971 68.2647 89.5862 68.277 89.2827 68.3141H89.2851Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M80.3604 40.3706C80.2444 41.0903 80.0816 42.1265 79.9089 43.1603C79.7558 44.0629 80.1382 44.6194 81.0462 44.7851C81.9321 44.9458 82.8252 45.0843 83.7085 45.2574C85.5937 45.6309 86.9063 47.2532 86.9014 49.1699C86.8964 51.1014 85.5715 52.6668 83.7085 53.0946C81.4238 53.6189 78.458 51.7147 79.0032 48.3587C79.1315 47.5673 79.2722 46.7784 79.4079 45.987C79.5584 45.1189 79.181 44.5699 78.2952 44.4018C77.4093 44.2361 76.5186 44.0951 75.6329 43.9269C73.7748 43.5757 72.4475 41.993 72.4105 40.0986C72.3734 38.1646 73.6565 36.5447 75.5119 36.102C77.9028 35.5332 80.422 37.3955 80.3604 40.3681V40.3706Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M58.2714 57.0669C60.1491 57.0916 61.7134 58.3331 62.1574 60.1755C62.5794 61.9363 61.7208 63.8159 60.0749 64.6395C59.2682 65.0426 58.4417 65.4135 57.6002 65.7351C56.781 66.0491 56.3443 66.7812 56.8279 67.7803C57.1857 68.5198 57.5114 69.2764 57.8199 70.0382C58.755 72.3357 57.4621 74.8657 55.0663 75.4494C53.191 75.9069 51.2838 74.967 50.4917 73.1939C49.7145 71.4578 50.2722 69.3951 51.829 68.3071C52.13 68.0969 52.4657 67.9336 52.8011 67.7803C53.5093 67.4563 54.2323 67.1645 54.9404 66.838C55.7571 66.4621 55.989 65.8513 55.6362 65.0179C55.2908 64.2041 54.9034 63.4079 54.5777 62.5867C53.6673 60.2991 54.9231 57.8162 57.2991 57.1979C57.6126 57.1163 57.9482 57.1088 58.2714 57.0669Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M69.1035 62.9101C69.111 59.992 71.492 57.6202 74.4036 57.6276C77.3026 57.6351 79.6812 60.034 79.6812 62.9473C79.6812 65.8804 77.2731 68.2719 74.3344 68.257C71.4378 68.2421 69.0937 65.8458 69.1035 62.9101Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M83.8945 39.572C83.8994 37.7988 85.3526 36.3594 87.1168 36.3792C88.8465 36.399 90.2702 37.8408 90.2801 39.5844C90.2899 41.3427 88.8341 42.8068 87.0749 42.8068C85.3107 42.8068 83.8895 41.36 83.8945 39.5745V39.572Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M58.7456 39.5941C58.7456 37.8233 60.1077 36.3865 61.7929 36.3791C63.4929 36.3716 64.9043 37.8505 64.887 39.6238C64.8697 41.3747 63.4756 42.8116 61.7953 42.8042C60.1053 42.7967 58.7482 41.3673 58.7456 39.5941Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M45.4366 62.2305C45.4292 60.5191 46.8256 59.1342 48.58 59.1143C50.3466 59.0945 51.8271 60.5068 51.8222 62.2106C51.8173 63.8973 50.3788 65.2871 48.6317 65.2947C46.8751 65.302 45.4439 63.9294 45.4366 62.2329V62.2305Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M98.659 59.1122C100.401 59.1071 101.852 60.4946 101.869 62.1787C101.886 63.8827 100.421 65.2973 98.6441 65.2899C96.8899 65.2825 95.4785 63.9025 95.481 62.1961C95.4834 60.4946 96.9022 59.1171 98.659 59.1122Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M87.3443 80.8603C89.0837 80.8678 90.5297 82.2699 90.5297 83.9517C90.5297 85.6606 89.059 87.0579 87.2826 87.0381C85.5307 87.0208 84.1341 85.631 84.1441 83.9196C84.1539 82.2229 85.5899 80.8529 87.3468 80.8603H87.3443Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M58.7471 83.9715C58.7422 82.2502 60.1214 80.8529 61.819 80.8603C63.482 80.8678 64.8712 82.2576 64.8859 83.9318C64.9007 85.6234 63.4893 87.0455 61.7968 87.0406C60.1165 87.0357 58.752 85.6631 58.7446 83.9739L58.7471 83.9715Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M17.2184 120.924L17.2141 116.161H67.6866C68.6013 114.482 69.2906 111.828 70.1946 110.17C70.186 110.154 70.169 110.074 70.1245 110.023C68.151 107.75 65.9356 105.851 63.2682 104.656C61.2906 103.772 59.2301 103.403 57.1039 103.581C55.3319 103.728 53.63 104.228 51.9983 105C46.1564 107.764 40.1531 109.997 33.978 111.664C31.2385 112.406 28.482 113.063 25.6873 113.491C23.994 113.75 22.2942 113.972 20.5903 114.141C19.5038 114.248 18.3472 114.336 17.214 114.373C17.1949 114.221 17.2184 113.963 17.2184 113.818C17.2141 111.968 17.2099 110.119 17.2226 108.269C17.2226 108.049 17.2969 107.813 17.3903 107.612C22.5595 96.3247 27.733 85.0422 32.9064 73.7573C33.3351 72.8219 33.7574 71.8795 34.2115 70.8764C34.6974 71.0845 35.1961 71.2974 35.7415 71.5311C31.3425 83.7116 26.9606 95.8407 22.5404 108.073C22.986 108.024 23.3574 107.996 23.7266 107.937C32.7792 106.473 41.4667 103.606 49.872 99.6569C51.8306 98.7357 53.8592 98.0996 55.9749 97.833C59.3828 97.4028 62.655 97.9967 65.7957 99.5026C68.5989 100.85 71.0242 102.802 73.1993 105.154C73.3373 105.304 73.4434 105.489 73.5897 105.692C74.0269 105.239 74.3941 104.853 74.7675 104.474C77.1378 102.054 79.7882 100.129 82.8461 98.9275C85.1823 98.0083 87.595 97.5876 90.0715 97.7209C92.5966 97.8565 95.0221 98.5111 97.3436 99.6102C103.139 102.355 109.098 104.554 115.23 106.169C117.904 106.876 120.599 107.481 123.328 107.876C123.78 107.942 124.23 108.017 124.786 108.103C120.367 95.8711 115.981 83.7303 111.589 71.5686C111.71 71.4961 111.78 71.4399 111.858 71.4072C112.263 71.2272 112.673 71.0541 113.114 70.8647C113.246 71.1757 113.359 71.4563 113.484 71.73C117.185 79.802 120.887 87.874 124.588 95.9436C126.356 99.7973 128.119 103.653 129.891 107.504C130.029 107.806 130.101 108.106 130.099 108.449C130.086 110.163 130.093 111.875 130.093 113.589V114.26C129.885 114.26 129.73 114.265 129.577 114.26C129.153 114.241 128.73 114.204 128.306 114.199C126.326 114.176 124.363 113.935 122.405 113.626C116.578 112.707 110.895 111.129 105.299 109.141C101.925 107.942 98.6148 106.558 95.3553 105.019C93.5409 104.163 91.6545 103.628 89.6725 103.55C86.933 103.443 84.4035 104.236 82.0418 105.73C80.2338 106.873 78.655 108.33 77.2184 109.986C77.1717 110.039 77.1336 110.107 77.1081 110.144C78.0078 111.795 78.7033 114.468 79.6264 116.161H130.093L130.093 120.906H76.3271C75.9431 119.963 75.5569 118.967 75.1325 117.99C74.7059 117.012 74.237 116.058 73.6577 115.104C72.5351 116.961 71.7564 118.934 70.9966 120.924L17.2184 120.92V120.924Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M197.092 84.997L190.692 67.0263C190.51 66.4944 190.275 65.5826 189.989 64.2908C189.703 62.9991 189.404 61.4161 189.092 59.5417C188.753 61.34 188.427 62.961 188.117 64.4047C187.804 65.8231 187.57 66.7729 187.414 67.2542L181.248 84.997H197.092ZM160.72 106.159L182.457 50.5374H196.428L218.477 106.159H204.584L199.94 94.3432H177.814L173.833 106.159H160.72Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M224.722 106.159V50.5374H236.937V106.159H224.722Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M249.347 92.4056V82.8313H271.201V92.4056H249.347Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M295.63 75.6507H297.386C300.716 75.6507 303.135 74.9795 304.645 73.6371C306.153 72.2946 306.908 70.1417 306.908 67.1782C306.908 64.4428 306.153 62.4545 304.645 61.2133C303.135 59.947 300.716 59.3137 297.386 59.3137H295.63V75.6507ZM283.338 106.159V50.5374H297.386C304.879 50.5374 310.407 51.9304 313.971 54.7165C317.562 57.5028 319.358 61.8086 319.358 67.6342C319.358 73.0291 317.55 77.2591 313.933 80.3238C310.343 83.3886 305.36 84.9209 298.986 84.9209H295.63V106.159H283.338Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M369.154 106.159H355.066L340.041 80.1719V106.159H327.825V50.5374H345.309C352.256 50.5374 357.459 51.8544 360.919 54.4886C364.38 57.0975 366.11 61.0361 366.11 66.3044C366.11 70.1291 364.926 73.3965 362.559 76.1066C360.19 78.8168 357.134 80.3998 353.387 80.8557L369.154 106.159ZM340.041 74.245H341.875C346.819 74.245 350.083 73.7257 351.67 72.6873C353.257 71.6235 354.05 69.7871 354.05 67.1782C354.05 64.4428 353.192 62.5052 351.475 61.3654C349.785 60.2002 346.585 59.6176 341.875 59.6176H340.041V74.245Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M434.793 78.4622C434.793 82.4388 434.026 86.2 432.49 89.746C430.982 93.2921 428.797 96.4075 425.936 99.0923C422.968 101.853 419.626 103.968 415.905 105.437C412.184 106.906 408.335 107.641 404.354 107.641C400.868 107.641 397.447 107.071 394.091 105.931C390.761 104.766 387.703 103.107 384.92 100.954C381.329 98.1678 378.571 94.837 376.647 90.9618C374.747 87.0866 373.797 82.92 373.797 78.4622C373.797 74.4603 374.552 70.7116 376.06 67.2163C377.57 63.6955 379.781 60.5674 382.695 57.832C385.556 55.1218 388.874 53.0195 392.647 51.5251C396.444 50.0308 400.347 49.2836 404.354 49.2836C408.335 49.2836 412.198 50.0308 415.945 51.5251C419.716 53.0195 423.046 55.1218 425.936 57.832C428.823 60.5674 431.02 63.6955 432.53 67.2163C434.038 70.7369 434.793 74.4856 434.793 78.4622ZM404.354 97.0406C409.531 97.0406 413.798 95.2804 417.155 91.7596C420.537 88.2137 422.227 83.7811 422.227 78.4622C422.227 73.1938 420.523 68.7612 417.115 65.1646C413.706 61.5679 409.453 59.7696 404.354 59.7696C399.176 59.7696 394.884 61.5679 391.476 65.1646C388.068 68.7359 386.363 73.1684 386.363 78.4622C386.363 83.8318 388.042 88.2769 391.398 91.7977C394.754 95.293 399.073 97.0406 404.354 97.0406Z\",\n    fill: \"black\"\n  }))), _defs || (_defs = /*#__PURE__*/React.createElement(\"defs\", null, /*#__PURE__*/React.createElement(\"clipPath\", {\n    id: \"clip0_2054_286\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    width: 418,\n    height: 86,\n    fill: \"white\",\n    transform: \"translate(17 36)\"\n  })))));\n}\nconst ForwardRef = /*#__PURE__*/React.forwardRef(SvgAipro);\nexport default __webpack_public_path__ + \"static/media/AIPRO.0580715cba3cbc2179c46b033d087db9.svg\";\nexport { ForwardRef as ReactComponent };", "import { GetCookie } from '../../core/utils/cookies';\r\nexport function getCurrency(currency) {\r\n    if(!currency) return \"\";\r\n    if(currency.toLowerCase() === 'usd') return \"$\";\r\n    if(currency.toLowerCase() === 'eur') return \"€\";\r\n    if(currency.toLowerCase() === 'gbp') return \"£\";\r\n    if(currency.toLowerCase() === 'brl') return \"R$\";\r\n    if(currency.toLowerCase() === 'sar') return \"R$\";\r\n    return \"\";\r\n}\r\n\r\nexport function getCountry(currency) {\r\n    if(!currency) return \"\";\r\n    if(currency.toLowerCase() === 'usd') return \"US\";\r\n    if(currency.toLowerCase() === 'eur') return \"US\";\r\n    if(currency.toLowerCase() === 'gbp') return \"GB\";\r\n    if(currency.toLowerCase() === 'brl') return \"US\";\r\n    if(currency.toLowerCase() === 'sar') return \"AE\";\r\n    if(currency.toLowerCase() === 'chf') return \"CH\";\r\n    if(currency.toLowerCase() === 'sek') return \"SE\";\r\n    return \"US\";\r\n}\r\n\r\nexport function formatDate(dateStr) {\r\n    if(!dateStr) return \"\";\r\n    const month = [\"January\",\"February\",\"March\",\"April\",\"May\",\"June\",\"July\",\"August\",\"September\",\"October\",\"November\",\"December\"];\r\n    const date = new Date(dateStr.replace(/ /g,\"T\"));\r\n\r\n    return month[date.getMonth()] + \" \" + date.getDate() + \" \" + date.getFullYear();\r\n}\r\n\r\nexport function getPrice(data) {\r\n    if(data.trial_price) return data.trial_price;\r\n    if(data.price) return data.price;\r\n    return \"0\";\r\n}\r\n\r\nexport function numberWithCommas(x) {\r\n    return x.toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, \",\");\r\n}\r\n\r\nexport function formatNumber(number) {\r\n  const floatNumber = parseFloat(number);\r\n  return (floatNumber % 1 === 0)\r\n      ? numberWithCommas(floatNumber.toFixed(0))\r\n      : numberWithCommas(floatNumber.toFixed(2)); \r\n}\r\n\r\nexport function getPricePlan(currency, price) {\r\n    if(!currency || !price) return \"\";\r\n    // Check if price is a number\r\n    if(isNaN(price)) return \"\";\r\n    // Check if price is positive number\r\n    if(parseFloat(price) >= 0) {\r\n        if(currency.toLowerCase() === 'sar') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"ر.س.\";\r\n        } else if(currency.toLowerCase() === 'aed') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"AED\";\r\n        } else if(currency.toLowerCase() === 'chf') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"CHF\";\r\n        } else if(currency.toLowerCase() === 'sek') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"SEK\";\r\n        } else if(currency.toLowerCase() === 'pln') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"zł\";\r\n        }else if(currency.toLowerCase() === 'ron') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"LEI\";\r\n        }else if(currency.toLowerCase() === 'czk') {\r\n            return \"Kč\" + formatNumber(price).toLocaleString(\"en-US\");\r\n        } else if(currency.toLowerCase() === 'huf') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"Ft\";\r\n        } else if(currency.toLowerCase() === 'dkk') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"kr.\";\r\n        } else if(currency.toLowerCase() === 'bgn') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"лв\";\r\n        } else if(currency.toLowerCase() === 'try') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"₺\";\r\n        }\r\n        return getCurrency(currency) + formatNumber(price).toLocaleString(\"en-US\");\r\n    }\r\n    // Negative number\r\n    return \"-\" + getCurrency(currency) + (formatNumber(price) * -1).toLocaleString(\"en-US\");\r\n}\r\n\r\nexport function diffMin(dt1, dt2)\r\n{\r\n    dt1 = new Date(dt1);\r\n    dt2 = new Date(dt2);\r\n    var diff =(dt2.getTime() - dt1.getTime()) / 1000;\r\n    diff /= 60;\r\n    return Math.abs(Math.round(diff));\r\n}\r\n\r\nexport function formatDateTime(dateStr) {\r\n    if(!dateStr) return \"\";\r\n    const month = [\"January\",\"February\",\"March\",\"April\",\"May\",\"June\",\"July\",\"August\",\"September\",\"October\",\"November\",\"December\"];\r\n    const date = new Date(dateStr.replace(/ /g,\"T\"));\r\n\r\n    return month[date.getMonth()] + \" \" + date.getDate() + \" \" + date.getFullYear() + \" \" + date.getHours() + \":\" + date.getMinutes();\r\n}\r\n\r\nexport function DailyPrice({plan}) {\r\n  let dailyPrice = '';\r\n  let interval = '';\r\n  if (plan.payment_interval === 'Yearly') {\r\n    dailyPrice = getPricePlan(plan.currency, parseFloat(plan.price / 365).toFixed(2));\r\n    interval = <div class=\"text-xs mb-4\">billed {getPricePlan(plan.currency, plan.price)}/year</div>;\r\n  }\r\n\r\n  if (plan.payment_interval === 'Monthly') {\r\n    dailyPrice = getPricePlan(plan.currency, parseFloat(plan.price / 30).toFixed(2));\r\n    interval = <div class=\"text-xs mb-4\">billed {getPricePlan(plan.currency, plan.price)}/Month</div>;\r\n  }\r\n\r\n  if (plan.trial_price) {\r\n    dailyPrice = getPricePlan(plan.currency, parseFloat(plan.trial_price / plan.trial_days).toFixed(2));\r\n    interval = <div class=\"text-xs mb-4\">billed {getPricePlan(plan.currency, plan.trial_price)}</div>;\r\n  }\r\n\r\n  return (\r\n    <>\r\n      <p className={`text-4xl font-bold text-gray-800 ${(GetCookie(\"p_toggle\") === '') ? 'mb-4' : ''}`}>\r\n        {dailyPrice} <span className=\"text-sm\"> per Day</span>\r\n      </p>\r\n      {interval}\r\n    </>\r\n  );\r\n}\r\n\r\nexport function PriceFormatted({plan}) {\r\n  if (GetCookie(\"daily\") === 'on') {\r\n    return DailyPrice({plan});\r\n  } \r\n  if (plan.trial_price) {\r\n    return (<p className=\"text-4xl font-bold text-gray-800 mb-4\">{getPricePlan(plan.currency, plan.trial_price)}</p>)\r\n  } \r\n  return (\r\n    <p className=\"text-4xl font-bold text-gray-800 mb-4\">\r\n      {getPricePlan(plan.currency, plan.price)}\r\n      <span className=\"text-sm\"> \r\n        /{ plan.payment_interval === \"Monthly\" ? \"month\" : \"year\" }\r\n      </span>\r\n    </p>\r\n  )\r\n}\r\n\r\nexport function displayTextFormatted(plan) {\r\n  let payment_interval = plan.payment_interval === 'Yearly' ?  365 : 30;\r\n  let trialPricePer = `\r\n    then only ${getPricePlan(plan.currency, plan.price)} per month\r\n  `;\r\n\r\n  if (GetCookie(\"daily\") === 'on') {\r\n    trialPricePer = `\r\n      then only ${getPricePlan(plan.currency, parseFloat(plan.price / payment_interval).toFixed(2))} per day<br>\r\n      (billed ${getPricePlan(plan.currency, plan.price)} ${plan.payment_interval})\r\n    `;\r\n  }\r\n\r\n  return plan.display_txt2\r\n        .replace(\"{trialDays}\", plan.trial_days)\r\n        .replace(\"{trialPricePer}\", trialPricePer);\r\n  \r\n}\r\n\r\nexport function getPrefixLocation() {\r\n  const currentLocation = window.location.href;\r\n\r\n  if (currentLocation.indexOf(\"staging\") > -1) {\r\n      return \"staging.\";\r\n  } else if (currentLocation.indexOf(\"dev\") > -1) {\r\n      return \"dev.\";\r\n  }\r\n\r\n  return \"\"\r\n}", "import React from \"react\";\r\n\r\nconst Loader = () => (\r\n  <>\r\n    <svg aria-hidden=\"true\" className=\"w-10 h-10 text-gray-200 animate-spin dark:text-gray-600 fill-blue-600\" viewBox=\"0 0 100 101\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\" role=\"img\">\r\n      <path d=\"M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z\" fill=\"currentColor\" />\r\n      <path d=\"M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z\" fill=\"currentFill\" />\r\n    </svg>\r\n    <span className=\"sr-only\">Loading...</span>\r\n  </>\r\n\r\n);\r\n\r\nexport default Loader;\r\n", "import React from \"react\";\r\nimport Loader from \"./loader\";\r\nimport { FaCircleCheck } from \"react-icons/fa6\";\r\n\r\n\r\nconst PaymentButton = ({\r\n  selectedMethod,\r\n  onClick,\r\n  isLoading,\r\n  isSelected,\r\n  buttonClass,\r\n  method,\r\n  htmlFor,\r\n  label,\r\n  src,\r\n  alt,\r\n}) => (\r\n  <button\r\n    className={`relative bg-white rounded-2xl hover:lg:bg-gray-200 ${buttonClass} overflow-hidden border-2 flex flex-col items-center justify-center aspect-square w-[102px] h-[102px] xl:w-[144px] xl:h-[144px] p-2 xl:p-0 ${\r\n      isSelected && !isLoading\r\n        ? \"text-blue-500 border-blue-500 hover:lg:bg-white\"\r\n        : \"text-gray-500\"\r\n    }`}\r\n    onClick={onClick}\r\n    disabled={isLoading}\r\n  >\r\n    {selectedMethod === method && isLoading && (\r\n      <>\r\n        <div role=\"status\" className=\"relative\">\r\n          <div className=\"absolute right-[-100px] top-[-20px] w-44 h-44 z-10 bg-black/50\"></div>\r\n          <div className=\"absolute top-[37px] right-[-20px] z-10\">\r\n            <Loader />\r\n          </div>\r\n        </div>\r\n      </>\r\n    )}\r\n    {selectedMethod === method && !isLoading && (\r\n      <FaCircleCheck className=\"absolute top-[7px] right-[7px] w-4 h-4 z-10 text-blue-500\" />\r\n    )}\r\n    <div className=\"payment-container w-full h-full flex justify-center items-center\">\r\n      <div className=\"relative w-full flex flex-col items-center\">\r\n        <img\r\n          src={src}\r\n          alt={alt}\r\n          className=\"block mx-auto w-auto h-8 mb-2 xl:mb-4\"\r\n          loading=\"lazy\"\r\n        />\r\n        <label\r\n          htmlFor={htmlFor}\r\n          id=\"other-label\"\r\n          className=\"text-center font-medium text-xs cursor-pointer xl:[&_br]:hidden\"\r\n          dangerouslySetInnerHTML={{ __html: label }}\r\n        >\r\n        </label>\r\n      </div>\r\n    </div>\r\n  </button>\r\n);\r\n\r\nexport default PaymentButton;", "var _path, _path2, _path3, _path4, _path5, _path6;\nfunction _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\nimport * as React from \"react\";\nfunction SvgCc(_ref, svgRef) {\n  let {\n    title,\n    titleId,\n    ...props\n  } = _ref;\n  return /*#__PURE__*/React.createElement(\"svg\", _extends({\n    width: 56,\n    height: 36,\n    viewBox: \"0 0 56 36\",\n    fill: \"none\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, _path || (_path = /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M0 4.46715C0 2.00001 2.00576 0 4.48 0H51.52C53.9942 0 56 2.00001 56 4.46715V31.5328C56 34 53.9942 36 51.52 36H4.48C2.00576 36 0 34 0 31.5328V4.46715Z\",\n    fill: \"#065482\"\n  })), _path2 || (_path2 = /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M0 26.3431V4.33577C0 1.94119 1.94677 0 4.34824 0H48.8188C34.9572 24.0701 10.4973 27.5912 0 26.3431Z\",\n    fill: \"#0B799E\",\n    fillOpacity: 0.2\n  })), _path3 || (_path3 = /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M3.55765 6.56934C3.55765 5.91628 4.08858 5.38686 4.74353 5.38686H12.9129C13.5679 5.38686 14.0988 5.91628 14.0988 6.56934V11.0365C14.0988 11.6896 13.5679 12.219 12.9129 12.219H4.74353C4.08858 12.219 3.55765 11.6896 3.55765 11.0365V6.56934Z\",\n    fill: \"white\"\n  })), _path4 || (_path4 = /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M37.6847 24.0438C37.6847 23.3907 38.2156 22.8613 38.8706 22.8613H50.8612C51.5161 22.8613 52.0471 23.3907 52.0471 24.0438V30.219C52.0471 30.872 51.5161 31.4015 50.8612 31.4015H38.8706C38.2156 31.4015 37.6847 30.872 37.6847 30.219V24.0438Z\",\n    fill: \"#FFC83A\"\n  })), _path5 || (_path5 = /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M3.55765 16.4234H52.0471V19.4453H3.55765V16.4234Z\",\n    fill: \"white\"\n  })), _path6 || (_path6 = /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M3.55765 25.0949H24.2447V29.6934H3.55765V25.0949Z\",\n    fill: \"white\"\n  })));\n}\nconst ForwardRef = /*#__PURE__*/React.forwardRef(SvgCc);\nexport default __webpack_public_path__ + \"static/media/cc.7129f8edc66abaab9740f14d05712fb1.svg\";\nexport { ForwardRef as ReactComponent };", "var _path, _path2, _path3, _path4;\nfunction _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\nimport * as React from \"react\";\nfunction SvgPp(_ref, svgRef) {\n  let {\n    title,\n    titleId,\n    ...props\n  } = _ref;\n  return /*#__PURE__*/React.createElement(\"svg\", _extends({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    width: 76,\n    height: 28,\n    preserveAspectRatio: \"xMidYMid\",\n    viewBox: \"0 0 256 302\",\n    id: \"paypal\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, _path || (_path = /*#__PURE__*/React.createElement(\"path\", {\n    fill: \"#27346A\",\n    d: \"M217.168 23.507C203.234 7.625 178.046.816 145.823.816h-93.52A13.393 13.393 0 0 0 39.076 12.11L.136 259.077c-.774 4.87 2.997 9.28 7.933 9.28h57.736l14.5-91.971-.45 2.88c1.033-6.501 6.593-11.296 13.177-11.296h27.436c53.898 0 96.101-21.892 108.429-85.221.366-1.873.683-3.696.957-5.477-1.556-.824-1.556-.824 0 0 3.671-23.407-.025-39.34-12.686-53.765\"\n  })), _path2 || (_path2 = /*#__PURE__*/React.createElement(\"path\", {\n    fill: \"#27346A\",\n    d: \"M102.397 68.84a11.737 11.737 0 0 1 5.053-1.14h73.318c8.682 0 16.78.565 24.18 1.756a101.6 101.6 0 0 1 6.177 1.182 89.928 89.928 0 0 1 8.59 2.347c3.638 1.215 7.026 2.63 10.14 4.287 3.67-23.416-.026-39.34-12.687-53.765C203.226 7.625 178.046.816 145.823.816H52.295C45.71.816 40.108 5.61 39.076 12.11L.136 259.068c-.774 4.878 2.997 9.282 7.925 9.282h57.744L95.888 77.58a11.717 11.717 0 0 1 6.509-8.74z\"\n  })), _path3 || (_path3 = /*#__PURE__*/React.createElement(\"path\", {\n    fill: \"#2790C3\",\n    d: \"M228.897 82.749c-12.328 63.32-54.53 85.221-108.429 85.221H93.024c-6.584 0-12.145 4.795-13.168 11.296L61.817 293.621c-.674 4.262 2.622 8.124 6.934 8.124h48.67a11.71 11.71 0 0 0 11.563-9.88l.474-2.48 9.173-58.136.591-3.213a11.71 11.71 0 0 1 11.562-9.88h7.284c47.147 0 84.064-19.154 94.852-74.55 4.503-23.15 2.173-42.478-9.739-56.054-3.613-4.112-8.1-7.508-13.327-10.28-.283 1.79-.59 3.604-.957 5.477z\"\n  })), _path4 || (_path4 = /*#__PURE__*/React.createElement(\"path\", {\n    fill: \"#1F264F\",\n    d: \"M216.952 72.128a89.928 89.928 0 0 0-5.818-1.49 109.904 109.904 0 0 0-6.177-1.174c-7.408-1.199-15.5-1.765-24.19-1.765h-73.309a11.57 11.57 0 0 0-5.053 1.149 11.683 11.683 0 0 0-6.51 8.74l-15.582 98.798-.45 2.88c1.025-6.501 6.585-11.296 13.17-11.296h27.444c53.898 0 96.1-21.892 108.428-85.221.367-1.873.675-3.688.958-5.477-3.122-1.648-6.501-3.072-10.14-4.279a83.26 83.26 0 0 0-2.77-.865\"\n  })));\n}\nconst ForwardRef = /*#__PURE__*/React.forwardRef(SvgPp);\nexport default __webpack_public_path__ + \"static/media/pp.dd9670ffda12dbaf6d8af7f2ce06b045.svg\";\nexport { ForwardRef as ReactComponent };", "var _rect, _defs;\nfunction _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\nimport * as React from \"react\";\nfunction SvgGp(_ref, svgRef) {\n  let {\n    title,\n    titleId,\n    ...props\n  } = _ref;\n  return /*#__PURE__*/React.createElement(\"svg\", _extends({\n    width: 76,\n    height: 36,\n    viewBox: \"0 0 76 36\",\n    fill: \"none\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, _rect || (_rect = /*#__PURE__*/React.createElement(\"rect\", {\n    width: 76,\n    height: 36,\n    fill: \"url(#pattern0_4111_35)\"\n  })), _defs || (_defs = /*#__PURE__*/React.createElement(\"defs\", null, /*#__PURE__*/React.createElement(\"pattern\", {\n    id: \"pattern0_4111_35\",\n    patternContentUnits: \"objectBoundingBox\",\n    width: 1,\n    height: 1\n  }, /*#__PURE__*/React.createElement(\"use\", {\n    xlinkHref: \"#image0_4111_35\",\n    transform: \"matrix(0.000389543 0 0 0.000822368 0.00796399 0)\"\n  })), /*#__PURE__*/React.createElement(\"image\", {\n    id: \"image0_4111_35\",\n    width: 2560,\n    height: 1216,\n    xlinkHref: \"data:image/png;base64,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*************************************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\"\n  }))));\n}\nconst ForwardRef = /*#__PURE__*/React.forwardRef(SvgGp);\nexport default __webpack_public_path__ + \"static/media/gp.00e3844352df6caf3ee867d58fb15f14.svg\";\nexport { ForwardRef as ReactComponent };", "var _rect, _defs;\nfunction _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\nimport * as React from \"react\";\nfunction SvgAp(_ref, svgRef) {\n  let {\n    title,\n    titleId,\n    ...props\n  } = _ref;\n  return /*#__PURE__*/React.createElement(\"svg\", _extends({\n    width: 78,\n    height: 35,\n    viewBox: \"0 0 78 35\",\n    fill: \"none\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    xmlnsXlink: \"http://www.w3.org/1999/xlink\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, _rect || (_rect = /*#__PURE__*/React.createElement(\"rect\", {\n    x: 0.5,\n    width: 77,\n    height: 35,\n    fill: \"url(#pattern0_4111_38)\"\n  })), _defs || (_defs = /*#__PURE__*/React.createElement(\"defs\", null, /*#__PURE__*/React.createElement(\"pattern\", {\n    id: \"pattern0_4111_38\",\n    patternContentUnits: \"objectBoundingBox\",\n    width: 1,\n    height: 1\n  }, /*#__PURE__*/React.createElement(\"use\", {\n    xlinkHref: \"#image0_4111_38\",\n    transform: \"matrix(0.000293341 0 0 0.000646412 -0.0979759 -0.790562)\"\n  })), /*#__PURE__*/React.createElement(\"image\", {\n    id: \"image0_4111_38\",\n    width: 4096,\n    height: 4096,\n    xlinkHref: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAEAAAABAACAYAAADyoyQXAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAOxAAADsQBlSsOGwAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAACAASURBVHic7MEBAQAAAICQ/q/uCAoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAIDZgwMBAAAAACD/10ZQVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVJuz5TQAAIABJREFUVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVYQ8OBAAAAACA/F8bQVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVYU9OBAAAAAAAPJ/bQRVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVW1fwSzAAAgAElEQVRVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVUV9uBAAAAAAADI/7URVFVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVdi7+yDbC/q+4+9dlnt5CIhRpEIQUNFoopHaprZXU6t2bMeHpDq2aRtom2m0zXRCZpKGmbTjaJsH6iRptENrHLWDkUnGUZsqaZOAhiiSmpgQKjSCmCYaEtGghTg+RIH+scuIJFy43N3zPXvO6zXzm8NeYPd97/3jzsJ+PwsAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHCENqYDAAAAgLV1cnVGddrO66OqU+7zvK26bCoQAAAAAAAAAAAAAAAWaWs6AAAAAFhZx1XfWD2uOuc+z1nV8Q/ifXwsAwAAAAAAAAAAAAAAAKwJAwAAAADA0Tq2+qbqadWTqyftPOdUm0f5vh91lP8+AAAAAAAAAAAAAADsGwYAAAAAgCNxsDqv+ta2D/6f1vbx/4E9+nin7tH7BQAAAAAAAAAAAACApWMAAAAAADic06tvq/5a9Yy2j/8PLvDjP2qBHwsAAAAAAAAAAAAAAEYZAAAAAADu7evaPvR/3s7zl6uNwZ5TBz82AAAAAAAAAAAAAAAs1OQX8AMAAADzjq+eWT23ek7bB//HjBZ9rS+23QgAAAAAAAAAAAAAACvPAAAAAACsn6dVL2j76P9vVAdncx7QwerPpiMAAAAAAAAAAAAAAAAAAADgaG1WT69eVX2kunufPY/c9V8RAAAAAAAAAAAAAAAAAAAAWJBjqmdWr63+qPkj/qN5ztnlXxsAAAAAAAAAAAAAAFhKW9MBAAAAwK45oXpu9bLqxdXDZnN2zcnTAQAAAAAAAAAAAAAAsAgGAAAAAGB/26qeX13Q9tH/cbM5e2JVhgwAAAAAAAAAAAAAAOCwDAAAAADA/vSU6p9U/7j6S8Mte+3E6QAAAAAAAAAAAAAAAFgEAwAAAACwfzy8ell1QXVouGWRDkwHAAAAAAAAAAAAAADAIhgAAAAAgOV2oHp+dX717a3nMfw6/pwBAAAAAAAAAAAAAFhDBgAAAABgOT2m+pfVP68eOdwy7eB0AAAAAAAAAAAAAAAALIIBAAAAAFguT68urP5hPm+/x4HpAAAAAAAAAAAAAAAAWASHBAAAADDvuOrvVz9QPXW4ZRkdnA4AAAAAAAAAAAAAAIBFMAAAAAAAcx5bfW/13dXDh1uW2THTAQAAAAAAAAAAAAAAsAgGAAAAAGDxnlN9f/WCanO4BQAAAAAAAAAAAAAAWBIGAAAAAGBxnlf9++oZ0yH7zN3TAQAAAAAAAAAAAAAAsAgGAAAAAGBvbVYvqF5Z/ZXhlv3KAAAAAAAAAAAAAAAAAGvBAAAAAADsjc3qpdWrqycNt+x3BgAAAAAAAAAAAAAAAFgLBgAAAABgdx2ovrP6t9W5wy0AAAAAAAAAAAAAAMA+YgAAAAAAdscJ1SuqH6xOH25ZNV+eDgAAAAAAAAAAAAAAgEUwAAAAAABHZ7P6rurHc/i/V74wHQAAAAAAAAAAAAAAAItgAAAAAAAeuudVP1U9ZTpkxX1xOgAAAAAAAAAAAAAAABZhczoAAAAA9qG/Xr2vuiLH/4vwhekAAAAAAAAAAAAAAABYBAMAAAAA8OA9uXpbdU31rOGWdWIAAAAAAAAAAAAAAACAtbA1HQAAAAD7wFnVv6u+K2N6E744HQAAAAAAAAAAAAAAAItgAAAAAADu38nVK6t/VR0cbllnn5sOAAAAAAAAAAAAAACARTAAAAAAAH+xF1WXVGdOh9BnpwMAAAAAAAAAAAAAAAAAAABYvCdWv1Ld7Vma56TD/o4BAAAAAAAAAAAAAAAAAACwUk6sXlV9qfmDd89Xnzurjfv/bQMAAAAAAAAAAAAAAAAAAGCVvKj6ePPH7p4//3zmML9vAAAAAAAAAAAAAACwUjanAwAAAGDQN1dXVe+qzpxN4X7cMR0AAAAAAAAAAAAAAACLYgAAAACAdXRi9VPVtdXfHG7h8D49HQAAAAAAAAAAAAAAAIuyNR0AAAAAC3aoenP1hOkQHpRbpwMAAAAAAAAAAAAAAGBRNqcDAAAAYEFOqC6u3pfj//3EAAAAAAAAAAAAAAAAAGtjazoAAAAAFuCZ1Zurc6dDOGIGAAAAAAAAAAAAAAAAWBub0wEAAACwh06oLq5+Lcf/+9WnpgMAAAAAAAAAAAAAAGBRtqYDAAAAYI88p3pjdc50CEfFAAAAAAAAAAAAAAAAAGtjczoAAAAAdtkJ1cXVFTn+XwWfmA4AAAAAAAAAAAAAAIBF2ZoOAAAAgF30jOrnqrOHO9g9BgAAAAAAAAAAAAAAAFgbm9MBAAAAsAs2qgurX8vx/yq5s7plOgIAAAAAAAAAAAAAABZlazoAAAAAjtKp1aXV350OYdf9cfXl6QgAAAAAAAAAAAAAAFgUAwAAAADsZ3+remt1+nQIe+IT0wEAAAAAAAAAAAAAALBIm9MBAAAA8BBsVT9SXZnj/1VmAAAAAAAAAAAAAAAAgLWyNR0AAAAAR+jM6rLqWdMh7LmPTgcAAAAAAAAAAAAAAMAibU4HAAAAwBF4cXVtjv/XhQEAAAAAAAAAAAAAAADWigEAAAAA9oOt6ierX6geMdzC4tw8HQAAAAAAAAAAAAAAAIu0NR0AAAAAD+CR1c9Vz5sOYeFumg4AAAAAAAAAAAAAAIBF2pgOAAAAgMM4r3pndfZwB4t3e3XKdAQAAAAAAAAAAAAAACzS5nQAAAAA3I9/Wl2T4/91deN0AAAAAAAAAAAAAAAALJoBAAAAAJbNMdXF1X+tjhtuYc710wEAAAAAAAAAAAAAALBoW9MBAAAAcC9fV11WvXg6hHE3TAcAAAAAAAAAAAAAAMCiGQAAAABgWXxD9a7qvOkQlsL10wEAAAAAAAAAAAAAALBom9MBAAAAUB2qfjvH/3zVDdMBAAAAAAAAAAAAAACwaAYAAAAAmPb3qiuqU6dDWBqfrW6ZjgAAAAAAAAAAAAAAgEUzAAAAAMCkC6u3V8dPh7BU/vd0AAAAAAAAAAAAAAAATDAAAAAAwISN6lXVT+dzU/68D00HAAAAAAAAAAAAAADAhK3pAAAAANbOgeot1T+YDmFpXTsdAAAAAAAAAAAAAAAAEwwAAAAAsEgnVO+o/s50CEvtQ9MBAAAAAAAAAAAAAAAwYWM6AAAAgLVxSnV5dWg6hKX2ueph1V3TIQAAAAAAAAAAAAAAsGhb0wEAAACshdOqX66+ZTqEpXdtjv8BAAAAAAAAAAAAAFhTBgAAAADYa4+prqzOnQ5hX/jgdAAAAAAAAAAAAAAAAEwxAAAAAMBeekz13upx0yHsG9dMBwAAAAAAAAAAAAAAwJSN6QAAAABW1lnVr1bnTIewrzy6+uR0BAAAAAAAAAAAAAAATNicDgAAAGAlnV1dleN/jsz/zfE/AAAAAAAAAAAAAABrzAAAAAAAu+0J1QfaHgGAI/Hr0wEAAAAAAAAAAAAAADDJAAAAAAC76ezqyur04Q72p2umAwAAAAAAAAAAAAAAYJIBAAAAAHbLGW0f/585HcK+9d7pAAAAAAAAAAAAAAAAmLQxHQAAAMBKOLW6qnrycAf716xKG0IAACAASURBVB+1PSIBAAAAAAAAAAAAAABra3M6AAAAgH3v4dUVOf7n6Fw5HQAAAAAAAAAAAAAAANMMAAAAAHA0jq/+e/Ut0yHse++ZDgAAAAAAAAAAAAAAgGkGAAAAAHiojqneWj1rOoSV8N7pAAAAAAAAAAAAAAAAmLY1HQAAAMC+9V+ql0xHsBJ+t/rD6QgAYNyh6urpCAD21B3Vnfd6+/bqrnv9+J9WX7nX6+07f33HzuufVp+9z4/dtvN8aSE/AwAAAAAAAACAPWYAAAAAgIfi1dX3TEewMq6cDgAAAGAhTr7P2w/fxfd9zxjAp6s/6avDALe1PTp3671eP1XdvYsfGwAAAAAAAABg1xgAAAAA4Ej9s+qV0xGslPdMBwAAALDvnbTznP0g/tkvtz0CcEv1yerj1R/c57l1TyoBAAAAAAAAAB6AAQAAAACOxLOr109HsFK+Ul01HQEAAMBaObY6Y+e5P1+sfr/tMYDfr26uPlrdVP1e9aU9LQQAAAAAAAAA1pYBAAAAAB6sc6t3VAemQ1gpH6pun44AAACA+ziu+sad577urD7e144C/G51Q3XLogIBAAAAAAAAgNVkAAAAAIAH4+ury3deYTddOR0AAAAAR+iY6pyd52/f5+/9v+r6tscA7nmurz61yEAAAAAAAAAAYP8yAAAAAMADObZ6e/WE6RBW0uXTAQAAALCLTqmeufPc263Vtfd5PlbdvdA6AAAAAAAAAGDpbUwHAAAAsPReX71iOoKVdEt1Zo4dAIBth6qrpyMAYIHuqH6n7TGA364+WN2Uz5MBAAAAAAAAYK1tTQcAAACw1L4vx//snf+WowYAAADW18nVt+0897ij+nDbozgfqH69+pPFpwEAAAAAAAAAUwwAAAAAcH+eX/3kdAQr7RemAwAAAGDJnFwd2nlqezjvxuqD1fuq91cfnUkDAAAAAAAAABZhYzoAAACApfTE6n9Vp0yHsLI+U51WfWU6BABYGofa/m7HAMDh3Vr9Ztt/bl5ZXVvdNVoEAAAAAAAAAOyarekAAAAAls7XV5fn+J+9dXmO/wEAAOChOK164c5TdVv1/rbHAK6sbhzqAgAAAAAAAAB2gQEAAAAA7u3Y6m3V46dDWHnvnA4AAACAFfGI6jt2nqpP9tVBgF+sbhnqAgAAAAAAAAAego3pAAAAAJbK66tXTEew8j5fnbrzCgBwj0PV1dMRALBi7q6uq36l+h/VB6qvjBYBAAAAAAAAAIe1OR0AAADA0nh5jv9ZjF/O8T8AAAAswkb1tOqHqquqT1c/X13Q9jgfAAAAAAAAALBkNqYDAAAAWArnVddUx02HsBYuqH52OgIAWDqHqqunIwBgjdxV/UZ1+c5z3WwOAAAAAAAAAFAGAAAAAKiHVb9VPW46hLXw5eq06rPTIQDA0jEAAACzfq96587zwbYHAgAAAAAAAACABducDgAAAGDURvWmHP+zOL+U438AAABYRo+tfrC6prq1ekv1ourYySgAAAAAAAAAWDcGAAAAANbb91cvnY5grVw6HQAAAAA8oEdW51fvqv64emP1nOqYySgAAAAAAAAAWAcb0wEAAACM+dbq/dWB6RDWxmeq06svTYcAAEvpUHX1dAQAcFi3Ve+ofrb6QHX3bA4AAAAAAAAArJ7N6QAAAABGPKJ6e47/Wayfz/E/AAAA7GePqF7e9qjkzdWPVt88WgQAAAAAAAAAK8YAAAAAwPrZqN5UnTkdwtq5dDoAAAAA2DWPrX64+nB1Q3VR9ajRIgAAAAAAAABYAQYAAAAA1s9F1bdPR7B2PlL9xnQEAAAAsCeeXF1cfaJ6e/XCamu0CAAAAAAAAAD2KQMAAAAA6+W86tXTEaylS6cDAAAAgD13oHpp9e62xwB+onrSaBEAAAAAAAAA7DMb0wEAAAAszMHqN6unTIewdu6qzm77C/8BAO7Poerq6QgAYE/8VvWG6q3V54dbAAAAAAAAAGCpbU4HAAAAsDA/nuN/Zrwnx/8AAACwzp5e/Ux1y87rN83mAAAAAAAAAMDyMgAAAACwHp5dXTgdwdq6dDoAAAAAWAqnVC+vPlxdWb2s2hotAgAAAAAAAIAlszEdAAAAwJ57WHVdddZ0CGvpjurR1eenQwCApXeouno6AgBYuE9Ul1RvrG4bbgEAAAAAAACAcZvTAQAAAOy51+X4nzmX5vgfAAAAuH9nVhdXt1RvqZ4ymwMAAAAAAAAAswwAAAAArLaXVBdMR7C27q7+83QEAAAAsC8crM6vrquurF6Ur2kAAAAAAAAAYA35n+UAAACr69QcXzPriuoj0xEAAADAvrJRPbd6V3VjdWF13GgRAAAAAAAAACyQAQAAAIDV9YbqtOkI1tol0wEAAADAvvb46qerm6t/XZ08mwMAAAAAAAAAe88AAAAAwGp6SfUd0xGstT+ofnE6AgAAAFgJZ1Svqf6weu3O2wAAAAAAAACwkgwAAAAArJ6Tq9dNR7D2LqnunI4AAAAAVspJ1fdVH2v7vz2cOZsDAAAAAAAAALvPAAAAAMDq+bF8FzRmfaF683QEAAAAsLIOVt9b3Vy9pXr8bA4AAAAAAAAA7B4DAAAAAKvlr1b/YjqCtXdZddt0BAAAALDyDlTnV/+n7SGAc2dzAAAAAAAAAODoGQAAAABYHVvVG6tjpkNYe5dMBwAAAABr5di2hwBuqN5UnT1aAwAAAAAAAABHwQAAAADA6viB6qnTEay991e/Mx0BAAAArKVjq++ubqp+pnr0bA4AAAAAAAAAHDkDAAAAAKvhsdUrpyOg+k/TAQAAAMDaO7Z6efXR6kerU2ZzAAAAAAAAAODBMwAAAACwGl5XnTAdwdq7uXrndAQAAADAjhOrH64+Xl1cnTSbAwAAAAAAAAAPzAAAAADA/ved1QumI6D6serO6QgAAACA+zipuqi6sfqe6pjZHAAAAAAAAAC4fwYAAAAA9rfjq/8wHQHVJ6rLpiMAAAAADuPR1RuqD1cvHG4BAAAAAAAAgL+QAQAAAID97aLqMdMRUL2m+rPpCAAAAIAH4UnVu6srqqcOtwAAAAAAAADA19iYDgAAAOAhO6O6sTpxOoS1d2t1TvWF6RAAYF87VF09HQEArJ27qsuqH6o+OdwCAAAAAAAAAG1OBwAAAPCQvSbH/yyH/5jjfwAAAGB/2qzOr26uXlUdGK0BAAAAAAAAYO1tTAcAAADwkDyjuiaf1zHv9uqsnVcAgKNxqLp6OgIAWHs3VRdWvzQdAgAAAAAAAMB62pwOAAAA4IhtVD+R43+Ww2tz/A8AAACsjidU/7N6d3X2bAoAAAAAAAAA68gAAAAAwP7zj9r+7qgw7XPV66YjAAAAAPbAC6vrq39THRxuAQAAAAAAAGCNGAAAAADYX06oLp6OgB1vqG6bjgAAAADYIydWP1JdVz17NgUAAAAAAACAdWEAAAAAYH+5qPqG6QioPle9ZjoCAAAAYAGeWP1q9bbq1OEW/j979x5ue13Qefx9OMhVQEAFlEEBL1zUGEULEQU00/GCgWDihRovaOOoOZbM1ExmTSM15JOlTWQWynQDyay8oY4X0MRrU1pewkyBRFQElDtn/libODFczjl77/Vda6/X63l+z29tjjy+9fHZm7Vd388PAAAAAAAAYI0zAAAAADA/9qpeOToClryu+sboCAAAAIApOr76XPXs0SEAAAAAAAAArF0GAAAAAObHf6l2GB0B1WXV/xwdAQAAADDAPaq3Vh+sDhibAgAAAAAAAMBaZAAAAABgPuxTvWB0BCz55eqK0REAAAAAAz2m+nT1inz2AgAAAAAAAIAV5P+EBgAAmA8/V207OgKqr1ZvHB0BAAAAMAO2r06rzq8OGNwCAAAAAAAAwBphAAAAAGD23bc6aXQELHl1de3oCAAAAIAZ8kPVZ6pXVesHtwAAAAAAAAAw5wwAAAAAzL7XVNuMjoDqC9WZoyMAAAAAZtB21Wur86oDB7cAAAAAAAAAMMcMAAAAAMy2A6oTR0fAklOqG0ZHAAAAAMywH6o+Xf1UtW5wCwAAAAAAAABzyAAAAADAbPuFav3oCKguqP5sdAQAAADAHNiu+rXq3GrvwS0AAAAAAAAAzBkDAAAAALPrQdXTR0fAklOqDaMjAAAAAObIY6u/rZ41OgQAAAAAAACA+WEAAAAAYHb9Ut63MRveUf2f0REAAAAAc2iX6szqjGrnwS0AAAAAAAAAzAEHSQAAAGbTQ6unjo6A6prqp0ZHAAAAAMy551afrQ4fHQIAAAAAAADAbDMAAAAAMJt+plo3OgKqX60uHB0BAAAAsAbsW32wenU+rwEAAAAAAADA7XCYBAAAYPbsW32x2np0CAvva9WB1fdGhwAAC+Hw6rzREQAAU/KB6tnVJaNDAAAAAAAAAJgtFuUBAABmzyty+J/Z8Ioc/gcAAABYDUdXn61+ZHQIAAAAAAAAALPFAAAAAMBs2a36idER0OQpdGePjgAAAABYw+5Zvav69eoug1sAAAAAAAAAmBEGAAAAAGbLS6odR0ew8K6vXjo6AgAAAGABrGvye5j3VnsMbgEAAAAAAABgBhgAAAAAmB3bVT85OgKq36w+NzoCAAAAYIEcWX2qOmxwBwAAAAAAAACDGQAAAACYHf8+T/livEur14yOAAAAAFhA964+XL1qdAgAAAAAAAAA4xgAAAAAmA3rq58aHQHVKdXloyMAAAAAFtTW1Wur36+2H5sCAAAAAAAAwAgGAAAAAGbDsdX9Rkew8D7U5MPlAAAAAIx1UvXRar/RIQAAAAAAAABMlwEAAACA2fDK0QEsvKurF1QbRocAAAAAUNUh1QXVUaNDAAAAAAAAAJgeAwAAAADjPWLpgpFeXX1pdAQAAAAA/8ru1Xurl44OAQAAAAAAAGA6DAAAAACMd/LoABbeZ6vXjY4AAAAA4DZtXf169dvVXQa3AAAAAAAAALDKDAAAAACMtUv1jNERLLQbqudV148OAQAAAOAOvbB6f3WP0SEAAAAAAAAArB4DAAAAAGM9p9pxdAQL7VeqT4+OAAAAAGCTHFF9tDpwdAgAAAAAAAAAq8MAAAAAwFgvHB3AQvtC9YujIwAAAADYLPdrMgJw1OgQAAAAAAAAAFaeAQAAAIBxDq8ePDqChXVT9fzqmtEhAAAAAGy2u1Xvrp4zOgQAAAAAAACAlWUAAAAAYJyTRwew0N5QnTc6AgAAAIAttk11RvXqwR0AAAAAAAAArKB1owMAAAAW1G7VRdV2o0NYSP9YPaS6cnAHAMDGDs9AEQDAlvrd6kXVDaNDAAAAAAAAAFierUYHAAAALKiTcvifMW6snpPD/wAAAABryfOqv6h2Gh0CAAAAAAAAwPIYAAAAABjj+aMDWFi/nCfrAgAAAKxFP1L9n+qeo0MAAAAAAAAA2HIGAAAAAKbv0dVBoyNYSB+vXjM6AgAAAIBV87DqI9V9RocAAAAAAAAAsGUMAAAAAEzfs0cHsJC+Vz23umF0CAAAAACr6gHVx6qHjA4BAAAAAAAAYPMZAAAAAJiuu1THjo5gIb20+uLoCAAAAACmYq/qg9XhgzsAAAAAAAAA2EwGAAAAAKbridXuoyNYOH9avXl0BAAAAABTtWv13ia/kwQAAAAAAABgThgAAAAAmK5njg5g4VxUvWB0BAAAAABD7FC9vTpudAgAAAAAAAAAm8YAAAAAwPTsWD1ldAQL5abqpOpbo0MAAAAAGGab6o+r54wOAQAAAAAAAODOGQAAAACYnmOajADAtPxa9f7REQAAAAAMt776vSZjkQAAAAAAAADMMAMAAAAA03Pi6AAWyl9VPzs6AgAAAICZcfMIwEtGhwAAAAAAAABw+wwAAAAATMdu1Q+PjmBhXFo9vbpudAgAAAAAM2Vd9frq5aNDAAAAAAAAALhtBgAAAACm4/hqm9ERLIQbq2dWF40OAQAAAGAmrateV71idAgAAAAAAAAA/z8DAAAAANNx4ugAFsbPVh8YHQEAAADAzDut+unREQAAAAAAAAD8awYAAAAAVt+9qkeNjmAhvKP6ldERAAAAAMyNU6sXj44AAAAAAAAA4BYGAAAAAFbfMXn/xer7cnVStWF0CAAAAABzY131hupFo0MAAAAAAAAAmHAABQAAYPUdMzqANe+a6hnV5aNDAAAAAJg766o3Vi8cHQIAAAAAAACAAQAAAIDVtnN15OgI1rwXV58eHQEAAADA3Lp5BODE0SEAAAAAAAAAi84AAAAAwOp6QrXt6AjWtN+ufn90BAAAAABzb311RvXU0SEAAAAAAAAAi8wAAAAAwOp62ugA1rR3VS8ZHQEAAADAmrF1dVb1+NEhAAAAAAAAAIvKAAAAAMDqWZ8PyrJ6PlWdUN0wOgQAAACANWWb6uzq0NEhAAAAAAAAAIvIAAAAAMDqeXi1++gI1qQLqydVV40OAQAAAGBN2ql6V3XQ6BAAAAAAAACARWMAAAAAYPU8YXQAa9Jl1b+rvjE6BAAAAIA17e7Ve6v7Du4AAAAAAAAAWCgGAAAAAFbP40cHsOZ8v3py9YXRIQAAAAAshHtX72kyBgAAAAAAAADAFBgAAAAAWB27Vo8YHcGacmP1rOrjo0MAAAAAWCgPqN5Z7Tg6BAAAAAAAAGARGAAAAABYHT9crR8dwZry4urtoyMAAAAAWEgPr96a33kCAAAAAAAArDoDAAAAAKvj6NEBrCm/UP3O6AgAAAAAFtqPVm8YHQEAAAAAAACw1hkAAAAAWB1Hjg5gzXh99erREQAAAABQnVz99OgIAAAAAAAAgLXMAAAAAMDK26t64OgI1oTfrV4+OgIAAAAANnJq9ZzREQAAAAAAAABrlQEAAACAlXfU6ADWhDdVL6g2jA4BAAAAgI2sq36neuToEAAAAAAAAIC1yAAAAADAyjMAwHKdUZ2cw/8AAAAAzKZtqz+r9h8dAgAAAAAAALDWGAAAAABYeUeMDmCunVk9r7ppdAgAAAAA3IG7V+dUO40OAQAAAAAAAFhLDAAAAACsrN2rB4yOYG6dXf1EdePoEAAAAADYBA+p/rhaPzoEAAAAAAAAYK0wAAAAALCyDqvWjY5gLp1TnVjdMDoEAAAAADbDE6v/MToCAAAAAAAAYK0wAAAAALCyfmh0AHPpz6ofq64fHQIAAAAAW+Cnq58YHQEAAAAAAACwFhgAAAAAWFmHjQ5g7pxVnZDD/wAAAADMt9+qfnB0BAAAAAAAAMC8MwAAAACwctZXjxgdwVw5vXpmdd3oEAAAAABYpm2rs6s9RocAAAAAAAAAzDMDAAAAACvnwOquoyOYG6dWL6puHB0CAAAAACtk7+qcapvRIQAAAAAAAADzygAAAADAynno6ADmwobqVdUpS68BAAAAYC15ZPUroyMAAAAAAAAA5pUBAAAAgJXzb0cHMPNurF6YD0ADAAAAsLa9rPrx0REAAAAAAAAA88gAAAAAwMoxAMAdubZ6evWm0SEAAAAAMAVvqA4ZHQEAAAAAAAAwbwwAAAAArIx1+TArt+971VOqt48OAQAAAIAp2aE6u9pldAgAAAAAAADAPDEAAAAAsDL2ywdZuW3frI6szh3cAQAAAADTtn/1ptERAAAAAAAAAPPEAAAAAMDKOHh0ADPpS9UR1SdHhwAAAADAIE+v/sPoCAAAAAAAAIB5YQAAAABgZRw0OoCZc271iOoLo0MAAAAAYLDTqoeNjgAAAAAAAACYBwYAAAAAVsaBowOYKadXT6ouHx0CAAAAADNg2+qPq11GhwAAAAAAAADMOgMAAAAAK+Og0QHMhBurl1UnV9cPbgEAAACAWbJ/9ZZq3egQAAAAAAAAgFlmAAAAAGD51lUPHB3BcFdWx1SvHx0CAAAAADPqqU3GMwEAAAAAAAC4HQYAAAAAlm/vaqfREQz15eoR1V+ODgEAAACAGXdadcDoCAAAAAAAAIBZZQAAAABg+fYfHcBQ51eHV38/OgQAAAAA5sAO1R9U24wOAQAAAAAAAJhFBgAAAACWb9/RAQzzhuro6tLRIQAAAAAwR/5t9UujIwAAAAAAAABm0dajAwAAANaA/UcHMHVXVi+o/nh0CAAAsFmurL44OgJW0Lrqbrf6a7ve6uu7Lf3rAGbNf6reXX1gdAgAAAAAAADALDEAAAAAsHz7jQ5gqv66OiGHhgAAYB59ujpydAQMcJfqrk3GAe660bXL0rXxX9u1usdG117VztNPBhbAVtUZ1Q9U3x7cAgAAAAAAADAzDAAAAAAsnwGAxfGm6qXV1aNDAAAAYDNcX31n6doS2zUZA9izuufStedG9/tW+1Z7LDcUWDh7V79VPWN0CAAAAAAAAMCsMAAAAACwfPuMDmDVfa96cfXW0SEAAAAwwDXV15auO7J9kyGAfbtlFGDj+26rFQjMtROqs6uzRocAAAAAAAAAzAIDAAAAAMuzdZOn3bF2fb46fukOAAAA3L6rm7x/vr330LtUB1QPqg7a6L73VOqAWfbG6kPVpaNDAAAAAAAAAEYzAAAAALA8e1TrR0ewat5avbj63ugQAAAAWAO+W3186drYLv3rQYCDl17vNdU6YKS7V79VHTc6BAAAAAAAAGA0AwAAAADLc6/RAayKK6uXVb83OgQAAAAWwHerjy1dG7tX9Yil6werQ6udp5sGTNGx1YnVH4wOAQAAAAAAABjJAAAAAMDy7D06gBX38eo51ZdGhwAAAMCCu7h6+9J1s/2qR1UP2+jabvppwCp5Q/Wh6qLRIQAAAAAAAACjGAAAAABYnj1HB7Birq9eU/2P6sbBLQAAAMBtu3DpesvS19s1GQE4ujqqOiyDADDP7la9sTpmdAgAAAAAAADAKFuNDgAAAJhz9xgdwIr4u+qR1S/l8D8AAADMk2uq86tfbDICsFv12Oq/Vx+tbhiXBmyhp1YnjI4AAAAAAAAAGGXr0QEAAABzbrfRASzLjdVp1c83OTAAAAAAzLerqw8sXVV3rR7dZBzg6OqQat2YNGAz/Hp1bvWd0SEAAAAAAAAA07bV6AAAAIA5ZwBgfn2+emT1qhz+BwAAgLXqquqd1Surh1Z7VidVf1FdO7ALuGN7VqeOjgAAAAAAAAAYwQAAAADA8uw6OoDNdkP12uph1QWDWwAAAIDpurR6S/WU6p7VM6o/yFPGYRY9v3rM6AgAAAAAAACAaTMAAAAAsDy7jQ5gs1xQPbz6z9U1g1sAAACAsa6o/qR6VrVH9bjq9dVXR0YB/2Jd9dvVdqNDAAAAAAAAAKbJAAAAAMDyGACYD1dU/7E6rPrs4BYAAABg9lxfvb96WbVvdUSTg8ffGRkF9MDqZ0dHAAAAAAAAAEyTAQAAAIDl2XF0AHfqbdVB1W9WNw1uAQAAAGbfhuq86kXVXtWPVudU146MggX2M9WBoyMAAAAAAAAApsUAAAAAwPLsMDqA2/Xl6knV06uLBrcAAAAA8+na6u3VcdUe1UnV+zIyCNO0TfUboyMAAAAAAAAApsUAAAAAwPJsNzqA/89V1SnVwdU7B7cAAAAAa8d3q7dUP1ztV/1CdcnQIlgcj61OGB0BAAAAAAAAMA0GAAAAALbcumr70RH8iw3VH1YHVKdW143NAQAAANawr1avru5TPaP60NAaWAynVXcdHQEAAAAAAACw2gwAAAAAbLlt875qVlxQPaY6sbpocAsAAACwOK6v/qQ6sltGCb8zMgjWsL2rnxsdAQAAAAAAALDaHFQBAADYctuODqB/qE6ofqj6yOAWAAAAYLF9oTqlum/1kurzQ2tgbfqp6sDREQAAAAAAAACryQAAAADAlvOeapxvVa+oDqrOqjaMzQEAAAD4F1dUb6gOrn6k+uDQGlhbtql+Y3QEAAAAAAAAwGpyWAUAAGDLrRsdsICuqk6t7le9rrpubA4AAADAHXpvdVT1sIwYwkp5bPW00REAAAAAAAAAq8UAAAAAwJYzADA911WnNzn4f0p1+dgcAAAAgM3y6eqE6geqt1Y3jM2Bufc/q21HRwAAAAAAAACsBgMAAAAAW84AwOq7uvrNat/q5OobY3MAAAAAluVvqudWB1dvbjJ6CGy+/aufHB0BAAAAAAAAsBoMAAAAADCLrmryFK/9qv9YXTw2BwAAAGBFfbF6XnX/6k3VjWNzYC7912r30REAAAAAAAAAK80AAAAAwJa7aXTAGnR59UvVfaufrv55aA0AAADA6vqn6gXVQ6p3DG6BebNr9fOjIwAAAAAAAABWmgEAAACALXfN6IA15JLqF6p9mzy561tjcwAAAACm6vPVMdVh1UcGt8A8eXF18OgIAAAAAAAAgJVkAAAAAGDLGQBYvk9Wz6z2qV5dXT60BgAAAGCsv6oeUx1ffWlwC8yDravXjo4AAAAAAAAAWEkGAAAAALbcTdW1oyPm0PXVWU0+zP7w6o+qG4YWAQAAAMyODdXZTZ5q/pLq0rE5MPOeXB0xOgIAAAAAAABgpRgAAAAAWJ5rRgfMkUuqU6v9qxOqD4/NAQAAAJhp11dvqB5Qvb66cWwOzLRfrdaNjgAAAAAAAABYCQYAAAAAlufq0QEz7qbqPdXTq32qU6qvDS0CAAAAmC/frV5WPbL6zOAWmFU/WD15dAQAAAAAAADASjAAAAAAsDxXjg6YUV+rTq3uVz2helt1w9AiAAAAgPl2QXVodXJ1xeAWmEW/XK0fHQEAAAAAAACwXAYAAAAAlufbowNmyBXVGdVR1X2qU6qvDC0CAAAAWFtuqk6vDqrOHtwCs+ZB1YmjIwAAAAAAAACWywAAAADA8nxndMBgVzf5sPlx1R7Vj1cfrDaMSwIAAABY8y6qjq/+XXXh4BaYJa+pthkdAQAAAAAAALAcBgAAAACW59ujAwb4fvWn1XOaHPo/vjqnumZkFAAAAMACelf1A9Xpo0NgRty3Onl0BAAAAAAAAMByGAAAAABYnu+MDpiSb1dvrU5ocuj/2OrM6sqRUQAAIUkypwAAIABJREFUAAB0VZMDz0+oLh7cArPgP1fbj44AAAAAAAAA2FIGAAAAAJbn26MDVslN1Seq/14dUd2zem51VpMPlQMAAAAwW95THVL92egQGGyv6gWjIwAAAAAAAAC21NajAwAAAObcJaMDVtBXqw9U763eV102NgcAAACAzfTN6mnV86rXVTuNzYFhXlWdXl0zOgQAAAAAAABgcxkAAAAAWJ6vjw5Yhgur86vzqnOrr4zNAQAAAGCF/G71/uqM6tGDW2CEe1UvrF4/OgQAAAAAAABgcxkAAAAAWJ6LRgdsokuqTy5dn1i6f3NoEQAAAACr6R+ro6v/Vv1ctdXQGpi+V1WnV9eMDgEAAAAAAADYHAYAAAAAlmfWBgCurj5ffW6j67PNXicAAAAAq+/G6uerC6ozq7uNzYGpulf1wur1o0MAAAAAAAAANse60QEAAABzbqsmh+63meK/57ebPMHtK9WFG92/vPT6pim2AADASjm8Om90BGveh6ojR0cADLJ/dU71kNEhMEUXV/tV144OAQAAAAAAANhUW48OAAAAmHM3Vf+h2qvJE9R22ei+U7e879q5Wn+rv/fq6pql19dUVyxd360ur65sctj/ourSje7XBAAAAACb5x+qw6rTq2cNboFpuVd1UpP/3QMAAAAAAADMhXWjAwAAAAAAAKrDq/NGR7Dmfag6cnQEwAx4YfUb1TajQ2AKLqweUN04OgQAAAAAAABgU2w1OgAAAAAAAAAAmKrTq6Ori0eHwBTsVx07OgIAAAAAAABgUxkAAAAAAAAAAIDFc3718Oqzo0NgCv5LtW50BAAAAAAAAMCmMAAAAAAAAAAAAIvp4uqI6t2jQ2CVHVI9bnQEAAAAAAAAwKYwAAAAAAAAAAAAi+uq6pjqzNEhsMpeNToAAAAAAAAAYFMYAAAAAAAAAACAxXZd9dzq1NEhsIoeWx06OgIAAAAAAADgzhgAAAAAAAAAAAA2VKdUz69uGNwCq+XlowMAAAAAAAAA7owBAAAAAAAAAADgZr9bHVd9f3QIrIITqr1HRwAAAAAAAADcEQMAAAAAAAAAAMDG3lE9obpqdAissLtULx4dAQAAAAAAAHBHDAAAAAAAAAAAALf2kepHqitGh8AKe1G1w+gIAAAAAAAAgNtjAAAAAAAAAAAAuC0frZ6YEQDWlt2qZ42OAAAAAAAAALg9W48OAAAAAObCrtU9qrtXuy/d715t3+SJaTtV21Y7L3297dLfc2duqK6srq2+X32vuq767tKfXXar6xtL9++tzH8sAAAA4E58tHpC9e4m7/thLXhp9aZqw+gQAAAAAAAAgFszAAAAAACLbcdqn+rfVHsv3W9+vXe3HPaftd8hXN1kDOCi6pLq4o1e33z/epNxAQAAAGB5PlY9vnpPtcvgFlgJD6oeV507OgQAAAAAAADg1mbtw/sAAADAytu5un/1gKX7A5fu96t2Hdi1HNtX91267si3qq9UFy7dv7LR1/9UXbdqhQAAALC2fLxbRgDuNrgFVsJPZgAAAAAAAAAAmEEGAAAAAGDt2KnJ08seUj146fUB1R4jowbbfek69Db+7MbqourL1d9Vn1u6/2112bQCAQAAYI5c0GQE4H1NBgdhnj252rv6+ugQAAAAAAAAgI0ZAAAAYNp2avJ0qF2Xrptf71JtV21T7Vit75YPkG7K06mvqK6uvner15dV31y63/waYC3Yp3p4dUiTw/4Prvat1o2MmjPrm/z3uE919K3+7LImQwC3Hga4dJqBAAAAMIM+UR1bvbPJ73NhXm1dvaD6+dEhAAAAAAAAABtzKAAAgJWwQ3Xvas87uN+9yUH+0SNUNzYZAbi0+mr1j0v3r1b/tPS1w53ArNm9yWH/ja89hxYtrm9Un7zV9c9DiwBg7Ti8Om90BGveh6ojR0cArBHPrM6sthodAstwcXWf6obRIQAAAAAAAAA3MwAAAMCm2qvav9rvNu57DOxaDZdXX6z+fun6wtL9S9X1A7uAxbCuOqh61NJ1WJPvt8yurzd5+uHGowDfHloEAPPJAADTYAAAYGW9ojptdAQs03HVOaMjAAAAAAAAAG5mAAAAgI1tVd23elCTw6c33x9Y7TAua2ZcV32u+uxG119X3x0ZBcy9bauHNznsf3j1yGq3oUWshH9oMgTwseojTX5e3Di0CABmnwEApsEAAMDKO63JEADMq3Orx4+OAAAAAAAAALiZAQAAgMW1U/XQ6tDqwU0O+x+Yg/5b4h+qjy9df1V9prp+aBEwy9Y3+d57dPXYJgf+tx9axDRcWZ3f5FDjh6tPVNcMLQKA2WMAgGkwAACw8raqzqyeOToEttCGJkPIXxodAgAAAAAAAFAGAAAAFsX21SFNDpw+fOn+wCYfzGTlXVN9uskgwIebPPn5W0OLgNEe1C0H/h9T7TI2hxlwdZMDju9fuj5T3Ti0CADGMwDANBgAAFgd21TvbPK7D5hHv1r9zOgIAAAAAAAAgDIAAACwVu1VHVE9aun+oGrroUWL7abqc9UHmxw0+HD1zZFBwKrbpfrh6glL173H5jAHvt3k58T7qndXXxlaAwBjGABgGgwAAKyenauPVgePDoEtcEm1T3XD6BAAAAAAAAAAAwAAAGvD/k0O+j+6yaH/+4/N4U5sqP66yQHPd1fn50OFMO/WVYc0Oez/xOqwDK+wPH/X5MmJ76o+Ul03NgcApsIAANNgAABgdd2/uqC62+gQ2AJPqf5idAQAAAAAAACAAQAAgPl0jyZPln780v1eY3NYpu92yxOf3119fWwOsIm2rY6untrkw8H3HpvDGnZlk58Tf1G9o7psbA4ArBoDAEyDAQCA1fekJu9ftxodApvpnOq40REAAAAAAAAABgAAAObD+iZPln5ck0Omh+XDk2vZhU0Oef55k6c+Xzs2B9jIbtVjm3wvPqbaeWwOC+im6mNNfkacU31pbA4ArCgDAEyDAQCA6fiv1WtGR8Bmuq7au/rm6BAAAAAAAABgsRkAAACYXXs2OWD6lOqo6q5jcxjkqur9TQYB3p6nPsMIe1THVsdXj24yygKzYEP1qepPqz+pvjw2BwCWzQAA02AAAGA61jV5v3rM6BDYTC+vfn10BAAAAAAAALDYDAAAAMyW/Zoc+H9ykw+jbz20hllzY/VX1VlL18Vjc2BN2716UpND/0/I92Pmw+eb/Hx4S3Xh4BYA2BIGAJgGAwAA07NTk99nHjQ6BDbD31QPGR0BAAAAAAAALDYDAAAAY62rfrB6WpMnIR0wNoc5clN1fvW26pzqa2NzYE3Yozquenr16Gr92BzYYjdVH6n+qDq7umxsDgBsMgMATIMBAIDpOqD6eLXz6BDYDA+tPjM6AgAAAAAAAFhcBgAAAMY4pHpm9WPVPoNbmH8bmnyI9m1L11fG5sBc2bM6tjq+OiKH/ll7rqv+svq96l3VDWNzAOAOGQBgGgwAAEzfjzYZMYV5cVr1ytERAAAAAAAAwOIyAAAAMD3375ZD/wcObmFt+1R1VvUH1dcGt8As2qM6Lof+WTz/XJ1Z/X71ubEpAHCbDAAwDQYAAMb4X9XJoyNgE329uk910+gQAAAAAAAAYDEZAAAAWF17VM9qcvD/0MEtLJ6bqg83Oez5turysTkw1PbV06pnV4+vth6bA8NdUL25yVjMlYNbAOBmBgCYBgMAAGPsWH26esDoENhER1UfHB0BAAAAAAAALCYDAAAAK2+r6ujqhU0Om95lbA5UdW11bnXW0nX12ByYiq2qR1bPqX6s2nlsDsykK6s/rN5Y/fXgFgAwAMA0GAAAGOdh1cfyO3Pmw+nVyaMjAAAAAAAAgMVkAAAAYOXcv3pW9ePVfcamwB26vPrz6i3V+6sNY3NgxR1YPaPJwf/9BrfAPPlUkw+3vzVDMQCMYQCAaTAAADDWz1W/ODoCNsF3qr2ajOsCAAAAAAAATJUBAACA5dmuOqF6fvWo/PMV8+cfq//dZAzgi2NTYFn2qJ7Z5ND/Qwe3wLy7tHpT9ZvVJYNbAFgsBgCYBgMAAGOtrz7Y5PfpMOuOqd4xOgIAAAAAAABYPA6oAQBsmX2qFzc5+H/3wS2wEjZU51dvrs6qrhqbA5tku+pHq2dXj6+2HpsDa8511R9Wv1b938EtACwGAwBMgwEAgPH2rT5b7Tw6BO7EHzUZHQUAAAAAAACYKgMAAACb56jqJU2e+rJ+cAusliubjAC8uckoAMyaQ6ufqE6s7ja4BRbF+6rTqvc0GY0BgNVgAIBpMAAAMBueW50xOgLuxPereyzdAQAAAAAAAKZmq9EBAABzYMfqRdXfVh+ojs3hf9a2nap/3+Tgzd9Xr6r2GloEdffq5U2eQv6J6idz+B+m6XHVu6q/aTLAcZexOQAAAMy5t1R/PjoC7sQO1RNHRwAAAAAAAACLxwAAAMDtu0f16uqr1W9VBw+tgTEeWL22+np1bnV8tc3QIhbJVk0OHb+l+qfqddWDhxYBB1dvrr5cvazafmwOAAAAc+wl1VWjI+BOHDs6AAAAAAAAAFg860YHAADMoP2r/1T9eA61wW35RnVG9abqS4NbWJse0OR78EnVvcamAHfi4upXq9Or7w9uAWD+HV6dNzqCNe9D1ZGjIwD4F69s8r4SZtUV1T2ra0eHAAAAAAAAAIvDAAAAwC0OrX6mydNc1g9ugXmwocnBid+pzqmuGZvDnNuxOr56XpODX96vwny5tPq16o3VlYNbAJhfBgCYBgMAALNl6+qT1Q+MDoE78OTqL0dHAAAAAAAAAItjq9EBAAAz4Kjq/dUnmhw+dfgfNs26Jocm/nd1UfW66uCRQcylQ6v/1eQp4r9XPSqH/2Ee3bN6bfWVJk9v3H5sDgAAAHPihupF1U2jQ+AOHDs6AAAAAACA/8fenUdbftd1vn4VVZnICGGMzDSIBpRJAQMyyRySKKLtAN5uNdq6GnBoofVe7duDQNvSwm3ROAMJM4KoIIMyRURGEZSGoAxCANFGkCFMyf1jFxIgQw3n7M85+/c8a+1VnFpZ8GKlUjm19+/7/gLAshgAAACW7M7VS6s/qe4x3AK73dWrR1RvbXVj19nVcaNF7GQntvo18oZW4ys/VJ0wWgRslZOrX6guqB5eHTmbAwAAwC7wmurXpiPgCpxZ7ZuOAAAAAAAAAJbDAAAAsESnVb9fvaq653ALbKLbVedU79//421nc9hB/NqA5fiq6peqt7ca/PAeFAAAAFfkkdWF0xFwOU6u7jodAQAAAAAAACyHh68BgCW5S/Un1fnV6cMtsAQn9KW3vJ9dHT9axIRrVz/V6hDw61v9Ojh2tAhYpxu1Gvx4fXXf2RQAAAB2sI9VPzEdAVfgzOkAAAAAAAAAYDkMAAAAS3Db6sXVK6u7D7fAUt2+1QHQC6vfqO4wm8M221vdv3pO9XfVY6ubjxYB025TvbB6UfW1wy0AAADsTE+vXjUdAZfDsDQAAAAAAACwNgYAAIBNdv1WB45fW91ruAVYOa76/uo11V9Xj6yuMVrEVrpeq7+n76z+sPq26ojRImCnuXf15lbfo11ruAUAAICd5yerS6Yj4DLcuDp1OgIAAAAAAABYBgMAAMAmOrl6XHVBdXarm6iBnedrqsdU762eXN2l2jNaxKE4uvqu6qXVe1r9Pb3RZBCw4+1r9T3a26qHZSgEAACAL3pt9ZzpCLgcp08HAAAAAAAAAMtgAAAA2CTHVI9qdfP0j1VHzeYAB+iY6iHVK6u3V/+xOmW0iAPx9dUTqgurp1b3zJ8xgYNz9erx1Zur+w23AAAAsHP8dPXZ6Qi4DAYAAAAAAAAAgLVwOAMA2AR7Wt0+/Y7q0dVJsznAYbhZ9fPVe6s/qL6tOnK0iEs7qfp31eurv6j+fXW10SJgE3xN9YLqudX1h1sAAACYd0F1znQEXIY7VSdPRwAAAAAAAACbzwAAALDb3bp6Ravbp6833AJsnb3VA6rnVO+rHledOlq0XHur+1TnVRdWT6xuN1oEbKqzqr+ufqzaN9wCAADArP9SfWw6Ar7M3up+0xEAAAAAAADA5jMAAADsVie3OoT6+uouwy3A9rpmq8Ogb61eU/1Qq5vo2V6nVo+t3lv9UfXd1TGjRcASHNdq9OW11TcOtwAAADDn76tfmI6Ay/CA6QAAAAAAAABg8+2ZDgAAOEhXqb63+h+tDgUDy/Tp6iXVs6pnV5+czdkYV6seXD20Om24BeCS6txWIzD/ONwCwHqcVp0/HcHGe0V1t+kIAA7IMdXbq+tPh8Cl/GN1reri6RAAAAAAAABgc11lOgAA4CDcqXpj9aQc/oelO6o6vdXvB++rfq365vwZ51AcWZ1VPbf6YHVODv8DO8Oe6iHVW6pvHW4BAABg/T5VPXo6Ar7MydXtpiMAAAAAAACAzeZwDACwG5xYPbHVLYBfP9wC7DxXq36w1S2O72r1UPCpo0U7397qntWvVu9vdfj/rFZjAAA7zXWr363Ora4+3AIAAMB6/Xb1gekI+DL3mg4AAAAAAAAANpsBAABgp3tw9bbq3+V7F+DK3aB6VPXW6i+qn6m+drRo57j0of8PVC+tfqi6xmQUwEH4nla/v58+HQIAAMDaXFT9z+kI+DIGAAAAAAAAAIBttWc6AADgcnxV9YTq26ZDgI3wrur3q2dVr64uns1Zm6tU39RqTOU7quvM5gBsmWdVP1z9n+kQALbUadX50xFsvFdUd5uOAOCgHFu9pzp5OgT2+0x19eoT0yEAAAAAAADAZnKLLgCw0+yrfrx6ew7/A1vnxtXDqldV762e2OqWpiMmo7bJkdW3VL9SfaDV/+eH5fA/sFkeXL25ut90CAAAANvuE9UvT0fApRxZffN0BAAAAAAAALC59kwHAABcyi2q36nuMNwBLMdHqhdWf1S9pPrgbM4hu051//2ve1fHz+YArM0l1TmtBqQ+NdwCwOE7rTp/OoKN94rqbtMRABy0k6t3V8cNd8AX/M9W70cAAAAAAAAAbDkDAADATnCV6hHVf62OGW4BluuS6i2thgD+pPrT6qOjRZfv2OrO1T33v26TP98By/bW6l9XfzUdAsBhMQDAOhgAANi9fjEHrtk53lrdajoCAAAAAAAA2EwOiAAA025a/U6rg6wAO8nnWw0CvLLVGMDrqncNtVyzusP+1zdXd6yOHGoB2Kk+Vf1Ydc50CACHzAAA62AAAGD3+qrqb6qjpkOg1aDstasPT4cAAAAAAAAAm8cAAAAwZU/1I9VjW91kDbAbfKR6Q/XGVuMA79j/+qct+u/fU924OrX62urrWh36v+kW/fcDLMFzqh9s9Xs2ALuLAQDWwQAAwO72a63+zAc7wbe3eh8CAAAAAAAAYEsZAAAAJly/+u3qntMhAFvk76sLqg9WF+7/+kOthgEurj56qb/2+OrI6sTqOtUp1fWqG1Q3ySgKwFZ4b/U9OUQKsNsYAGAdDAAA7G6nVm+djoD9nlA9fDoCAAAAAAAA2Dz7pgMAgMV5UPXr1dWmQwC20LX2vwDYGW5Qvaz62eox1SWzOQAAAGyRv6peWX3zdAhUd50OAAAAAAAAADbTVaYDAIDFOKZ6fPXsHP4HAGD77at+vnp+ddJwCwAAAFvnV6YDYL9bVVefjgAAAAAAAAA2jwEAAGAdTq3+vHrYdAgAAItzevXaVt+TAgAAsPv9bvWh6Qho9czNadMRAAAAAAAAwOYxAAAAbKc91dnV61rdggIAABNuVr2mevB0CAAAAIftM9VvTkfAfnedDgAAAAAAAAA2jwEAAGC7XKP6g+qc6pjhFgAAOK56RvXoau9wCwAAAIfn16rPT0dABgAAAAAAAACAbWAAAADYDt9YvaG6/3QIAABcyp7qUdULq5OHWwAAADh072n1ZzuYduvqqtMRAAAAAAAAwGYxAAAAbLWHVi+vbjDcAQAAl+de1Wurr5kOAQAA4JD9ynQAVPuq209HAAAAAAAAAJvFAAAAsFWuWj2lelJ1zHALAABcmZtU51d3G+4AAADg0PxR9a7pCKjuOB0AAAAAAAAAbBYDAADAVrhZ9WfV906HAADAQbh69ZLqh6dDAAAAOGgXV+dOR0AGAAAAAAAAAIAtZgAAADhc31q9vvq66RAAADgE+6pfqf5rtWe4BQAAgIPztOkAyAAAAAAAAAAAsMUMAAAAh2pP9cjq2dUJwy0AAHC4fqZ6ZnXMdAgAAAAH7G3Vm6YjWLzrVjecjgAAAAAAAAA2hwEAAOBQHNvqcNRj8v0EAACb49urP66uOR0CAADAAXvadABUd5oOAAAAAAAAADaHA3sAwMG6QXV+q8NRAACwae5UvarV970AAADsfE+vLp6OYPHuOB0AAAAAAAAAbA4DAADAwbhT9efVradDAABgG3119ZrqVtMhAAAAXKm/azVcDJNuPx0AAAAAAAAAbA4DAADAgfru6k+q60yHAADAGly3enlu8AMAANgNnjYdwOLduto7HQEAAAAAAABsBgMAAMCV2VM9tjqvOnq4BQAA1unq1Yuru0+HAAAAcIWeXX12OoJFO7a6+XQEAAAAAAAAsBkMAAAAV+TI6tzqp6ZDAABgyPHVC6sHTYcAAABwuf6h1YAbTLrtdAAAAAAAAACwGQwAAACX56TqRdV3T4cAAMCwo6qnV983HQIAAMDles50AIt3m+kAAAAAAAAAYDMYAAAALssNqvOruw13AADATrGv+u3qh6dDAAAAuEwvrC6ejmDRbjsdAAAAAAAAAGwGAwAAwJe7VavD/6dOhwAAwA6zp3pi9SPTIQAAAHyFD1ZvmI5g0W7b6r0DAAAAAAAAgMNiAAAAuLR7tTr8f/3pEAAA2KH2VP+r+sHpEAAAAL7CH04HsGgnVjeZjgAAAAAAAAB2PwMAAMAXfHv1B9UJ0yEAALDD7al+tfqB6RAAAAC+xB9MB7B4t54OAAAAAAAAAHY/AwAAQNX3Vk+rjpwOAQCAXeIq1a9VPzwdAgAAwL94Y/X+6QgW7dTpAAAAAAAAAGD3MwAAAPxo9aRq33QIAADsMnuqJ2YEAAAAYKe4pPqj6QgWzQAAAAAAAAAAcNgMAADAsv2n6n/lewIAADhUe6pfrv6v4Q4AAABW/nA6gEW75XQAAAAAAAAAsPs57AcAy7Sn+h/Vz02HAADABrhK9ZvVg6dDAAAA6CXVp6cjWKybV0dNRwAAAAAAAAC7mwEAAFieLxxO+onpEAAA2CBXqZ5S3WM6BAAAYOE+Xr1yOoLF2tdqBAAAAAAAAADgkO2bDgAA1uoq1W9XD50OAQCADXRU9dzq7tUbh1sAAACW7OXVvaYjWKxbVm+ZjgCgPdVJl/r6iOq4S319THX0pb4+vrqo+uylfu6T1acv9fVF1acu9fWn9/81AAAAAACwpQwAAMByOPwPAADb74TqhdWdqwuGWwAAAJbqldMBLNqp0wEAu9hJ1XVaHcY/cf/r+Eu9Ttj/11zWz52w/+uj1l79RR/f//pE9U8H8PU/7//PX/j6o9WH9v88AAAAAAALZgAAAJbhKtVv5fA/AACsw7WqF7UaAbhwuAUAAGCJXtfqht6jr+wvhG1gAADgS51YXbe65v4fr93qPdTr7v/xWtUp+3+cPLy/FY7b/zpcF1Ufrv6+1SDAP+z/+oP7f/zwpb7+h+pTW/C/CQAAAADADmIAAAA23xcO/3/fdAgAACzIjas/qu5afWS4BQAAYGk+Xf15qz+TwbrdfDoAYI32Vddr9X7opV83qm7Q6tC/QZ6Dd3R1/f2vA/HxVmMA76veU717/4/vqd67//WZLa8EAAAAAGDbGAAAgM3m8D8AAMy5VfXC6p7VJ4ZbAAAAluZVGQBgxk2rvdXnp0MAtsjVqptczusGeQZxJziu+lf7X5fnI9Xf7n99oLrwUl9fUH1smxsBAAAAADgI3nwHgM3l8D8AAMy7Q/Xk6sHVxcMtAAAAS/LK6QAW66hWB2LfNR0CcBD2VDeqvrY69VKvW1THzmWxha5W3W7/67JcWL2t+t/VX1dv3//1hWupAwAAAADgSxgAAIDN9Ys5/A8AADvBt1X/tfrp6RAAAIAF+bPqs9UR0yEs0s0yAADsXKf0xYP+X/jx61vdIs9ynbL/dc8v+/mPVu+s/rbVMMBf7f/Pb60+vc5AAAAAAIAlMQAAAJvp0dUjpiMAAIB/8R9b3Zz05OkQAACAhfh49abqG6dDWKSbVy+ejgAW76hWB/u/odWt77eqbpGD/hycE1v9+rndl/38Z6oLqre1+p7rDdUbqw+vtQ4AAAAAYEMZAACAzfOI6lHTEQAAwFf4jeq91cuHOwAAAJbiVRkAYMbNpgOAxdnb6nD/7b7sdfRkFBvtyOrU/a9vv9TPf6DVGMAXXq+rPrj2OgAAAACAXc4AAABsln9XPW46AgAAuExHVM+s7lC9a7gFAABgCV5V/cR0BIt08+kAYOPdpPqGS71uWx03WgQr161O3//6gvf1xUGAN+7/0SgAAAAAAMAVMAAAAJvje6r/Ve2ZDgEAAC7XNavfr76p+thwCwAAwKZ7/XQAi2UAANhK+1od8L/r/tcdq5NHi+DgXG//68xL/dz7qz+vXtlqtOnN1efXnwYM+87qQdMRLNpTWn12C8Du8KvV1acjWIyXVb8yHQHAsjkgCACb4YzqORn3AQCA3eKPWt2A5IFGgC86rTp/OoKN94rqbtMRAKzVh6prTUewOJ+rjtn/I8DB2lfdvi8e+L9zdfxoEWy/j1WvbjUG8MrqddWnR4uAdfiG6rXTESzaC6v7T0cAcEBuUb1tOoJF+bbqudMRACybQ4IAsPt9U/X0/HsdAAB2k/tWj6sePh0CAACw4d5U3Wc6gsXZ1+qm43cPdwC7wxGtDvzfrdWB/9Oq4yaDYMAJrd43v+/+ry9qNQLwylajAK+u/nkmDdhGr6veXn31dAiLdY9WQ0v+HQOw850xHcCifKR6wXQEADgoCACHJlwgAAAgAElEQVS721dXz291gwgAALC7PKzVrSbnTYcAAABsMAMATLlhBgCAy7anuk2rg85fOPB/7GgR7DxHV3fZ/6r6fPUXrcYA/rj6k+qTM2nAFju3+i/TESzWUa2+J3vWdAgAV+rM6QAW5TnVp6cjAOAq0wEAwCG7ZvX71cnTIQAAwCH79erW0xEAAAAb7E3TASzWDacDgB3lqtW3VI+v3lu9ofpv1b1z+B8OxN7qdtUjWj0r89Hq/OqR+39+z1wacJieUl0yHcGiOVAKsPNdu7rjdASLcu50AABU7ZsOAAAOyVWr51c3mw4BAAAOyzHVM6tvaPXAIgAAAFvrjdMBLNaNpgOAcTdpdej/gdW9Wt0wC2yNfdVp+19VH6xe0moc4MV5vx12k/dUf1rdeTqExXpAdUT12ekQAC7XGbkAl/V5f/Wq6QgAKN8AAcButLd6apYMAQBgU9yselJuKAIAANgOf5MDYMy44XQAsHb7Wh1efEz1V63+HXROdXoO/8N2u071kFaDu/9QnV89srpd3nuH3cANq0w6qbrLdAQAV+jM6QAW5dzq4ukIACgDAACwG/1y3sgAAIBNc2b1iOkIAACADXRJ9ebpCBbpRtMBwFqcVD20el71T61uiHtk9bWTUbBw+6rTWo1xvL7V7eLnVPfLGAfsVM+sPj0dwaJ5HhNg5zq2uud0BIvy1OkAAPgCAwAAsLs8svqh6QgAAGBbPLbVDWEAAABsrTdNB7BIN5oOALbNia1uGn9+9cHqSa0OjR07GQVcrutXZ1cvqP6+1WGOB1fHTUYBX+Ij1R9OR7BoBgAAdq77VEdPR7AYb6n+cjoCAL7AAAAA7B5nVD8/HQEAAGybI6pnVNeeDgEAANgwBgCYcL08lwOb5ITqe6rfqz5UPbl6YG4Th93mhOq7Wt02/uFWQx7f12rYA5h17nQAi3bD6tbTEQBcJiMtrNN50wEAcGk+aASA3eGWrT7k8O9uAADYbKdUT6v2TocAAABskL+aDmCRjqyuOR0BHJbjq++untvq0P+5rYb7HfqHzXB0qyGP32n1z/jzWo0DHDvYBEv2gur/TEewaA6YAuw8+6oHTEewGBe3emYLAHYMhwgBYOe7RqvF8eOnQwAAgLW4e/UfpyMAAAA2yAXTASzWKdMBwEE7uvrO6ndbHQg+rzpr/88Dm+uoVgc/n1r9ffWM6lsz+AHr9OnqWdMRLJoBAICd587VydMRLMarqvdORwDApRkAAICd7YjqmdWNp0MAAIC1+rnqjtMRAAAAG+Kj1YenI1gkAwCwe5xaPab6u+rprQ7+HjNaBEy5avUdrYZAPlg9ufqWas9kFCzEudMBLNpt8pwmwE5jnIV1Om86AAC+nAEAANjZ/r9Wt38CAADLsq/VQ07HT4cAAABsiAumA1ik604HAFfopOrs6g3VW6tHVtcYLQJ2mpOqh1Qvqd7daijkZpNBsOH+tPrb6QgW7fTpAAC+hAEA1uUzrUbgAGBHMQAAADvXv69+aDoCAAAYc9PqcdMRAAAAG+Id0wEs0inTAcBX2Fvdr3pmq5u9z6luO1oE7BY3aDUU8vbq5dX3VsdMBsEGuqR66nQEi+agKcDOcavqxtMRLMYLqn+cjgCAL2cAAAB2pm+qfnE6AgAAGPcD1YOnIwAAADbAO6cDWKTrTgcA/+L6rQ7uvrPVQ90Pro4aLQJ2qz3VXaunVB9oNSRym9Ei2CxPng5g0b65utp0BABVnTUdwKKcOx0AAJfFAAAA7DzXrZ5THTEdAgAA7AhPzI2BAAAAh+sd0wEskj/Pw6wjqu+oXlG9p3pMdaPJIGDjnFidXb2xem2rUd+rjhbB7ndBq3+eYMIR1f2nIwCo6ozpABbjo9UfTkcAwGUxAAAAO8u+6unVdaZDAACAHeMa1e+0ulUIAACAQ3PBdACLZAAAZpxUPbx6Z/WMVje5em8N2G7fUP169YHqnOrU2RzY1c6bDmDRzpwOAKCvqm43HcFiPLu6aDoCAC6LAQAA2Fke0+rhAwAAgEu7V6uHlgEAADg076wumY5gcYx+w3p9ffUbrQ7f/lJ1g9kcYKFOqM6u3lK9qNVB0r2jRbD7PL363HQEi3Xf6qjpCICFOzNDfqzPU6cDAODyGAAAgJ3j26ofn44AAAB2rAfkA04AAIBD9fFWB0JhnU6eDoAF2Nvqs/aXVX9RfX919GgRwMqe6t7V81qNUf1Yq3EA4Mr9fasBDZhwfHX36QiAhTtzOoDFeH/18ukIALg8BgAAYGe4efXbOcwDAAB8pU9UD6/uk9sqAQAADsc7pwNYnGOqY6cjYENdrfoPrX5vf051t9EagCt2o+px1d9Vv1jdcLQGdodzpwNYNAdPAeacmD/jsz5Pqy6ejgCAy2MAAADmXbV6dla+AQCAr/Ty6uuqJ+QDJwAAgMP1vukAFuka0wGwYW5a/UqrQ7T/vdWhWoDd4oTqx1uNlzyj+sbZHNjRnl99bDqCxTojlzkBTLlfdeR0BItx3nQAAFwRAwAAMO8J1a2mIwAAgB3lk9WjqntWfzvcAgAAsCkunA5gkU6eDoANcdPqnOp/Vz9cHTubA3BY9lXfUf15dX71wBw0hS/3yep3pyNYrFOqb5iOAFios6YDWIy3VX8xHQEAV8QAAADMenD1/dMRAADAjvLS6murx1YXD7cAAABsEgMATLjGdADscrepnl29ozq71aFZgE1yWqubzl9bfVue64VLO3c6gEU7czoAYIGOrO47HcFiPGU6AACujDcKAWDODatfm44AAAB2jE9VD6/uXb1nuAUAAGATfWA6gEU6eToAdqk7Vr9fvaF6UJ5zAzbf7avnVG+tHprBE6h6WfW+6QgWywAAwPrdtTpxOoJFuKR62nQEAFwZH4wAwIx91XnVSdMhAADAjvCWVg81P6HVh0wAAABsvQunA1ika0wHwC5z51YH//+sOr3aM5sDsHZfUz2puqA6O0MALNvFOZjFnFOrm09HACzMWdMBLMb51bunIwDgyhgAAIAZP1udNh0BAACM+3z16FY3+/zlcAsAAMCmMwDAhJOnA2CXuF+rh69f1ergP8DS3ag6p/rr6iHV3tEamPOU6QAW7YHTAQALsie/77I+T50OAIADYQAAANbvrtVPT0cAAADj/rYv/vngM8MtAAAAS2AAgAlXmw6AHe5u1WuqF2REH+Cy3Kx6cqshgO/Oc78sz1uqN09HsFhnTgcALMhtq+tPR7AIn6meNR0BAAfCG4EAsF5Xb7VKbJUbAACW7enVbao/nQ4BAABYkE9W/zQdweKcMB0AO9TXtTr0/7LqDsMtALvBzavzqjdU9xtugXU7dzqAxfqm6prTEQALYXSFdfmj6h+nIwDgQBgAAID1emLWCQEAYMkuqh5RfVf1seEWAACAJbpwOoDFMQAAX+r61TnVG3OAFeBQ3LrVgMqrq28eboF1eWr1+ekIFmlv9YDpCICFOGs6gMUwLgXArmEAAADW59ur75yOAAAAxryjulP1+OkQAACABfvAdACLc+J0AOwQ16geV11Qnd3qMBUAh+5O1Suq51a3GG6B7XZh9bLpCBbLjdQA2+/G1a2mI1iEj1V/MB0BAAfKAAAArMe1qidORwAAAGOeVt2++ovpEAAAgIUzAMC6nTAdAMOuWv109c7qx6qjZnMANs5Z1V9WT6hOHm6B7eSmVqbcuzpmOgJgwxlbYV1+t/rUdAQAHCgDAACwHk+srjkdAQAArN1F1SOq767+ebgFAACA+qfpABbHAABLta86u7qg+m/VibM5ABvtiOrft/o995EZW2EzPbv6xHQEi3TV6l7TEQAb7qzpABbDqBQAu4oBAADYft9TPWg6AgAAWLt3VHesHj8dAgAAwL8wAMC6GQBgie7Z6jbqc6pThlsAluRq1WOqt+QWVTbPJ6rnT0ewWH5PBdg+V69Om45gES6sXj4dAQAHwwAAAGyv61ZPmI4AAADW7oXVHao3T4cAAADwJT46HcDiGABgSU6pnly9tPqa4RaAJbtZ9bzqj6uvHW6BreTGVqacXu2djgDYUKdX+6YjWISnV5+fjgCAg2EAAAC216+3WiYEAACW4ZLq0a0+oHSrJAAAwM7zsekAFufYPMTM5juyemT1juohwy0AfNE9qjdW/63V9ySw2724+tB0BIt0repO0xEAG+qM6QAW47zpAAA4WAYAAGD7PLR6wHQEAACwNp+svqv66eri4RYAAAAum7E2Jlx1OgC20b2qv6wek8OlADvRUa0+t3hb9e3DLXC4Ptfq5laYcOZ0AMAGOrq6z3QEi/C2VuNoALCrGAAAgO1xcvUL0xEAAMDavK+6a/WM6RAAAACu0EenA1iko6cDYBucUj251U28Xz3cAsCVu371rOoF1Y1mU+CwuLmVKWdNBwBsoG+pjpuOYBF8DwnArmQAAAC2x+Oqa01HAAAAa3F+dfvq9dMhAAAAXCkDAEwwAMAmOaJ6ePW/q4cMtwBw8O5X/VX1yGrfcAscite1+jUM6/avqq+ZjgDYMGdOB7AIl1RPm44AgENhAAAAtt4986ADAAAsxROre1Qfmg4BAADggBgAYMIx0wGwRe5RvbX6per44RYADt1Vq8dUf17ddrgFDoUDXEw5azoAYINcpTp9OoJF+LPqb6cjAOBQGAAAgK11TPWr1Z7pEAAAYFt9pjq7+tHqs8MtAAAAHDgDAEw4ejoADtPxrT4Hf2l18+EWALbObVuNAPz3fL/C7nJeq5tcYd3OmA4A2CB3qK4zHcEinDcdAACHygAAAGyt/7v6V9MRAADAtvrH6luqX58OAQAA4KAZAGDCMdMBcBjuVb2l+qEM4QNson3Vf6jeVN1xuAUO1Lur86cjWKRvrK47HQGwIc6cDmARPls9czoCAA6VAQAA2Dq3bPWBGAAAsLn+pjqtetV0CAAAAIfkE60e+oN1cqMuu9EJ1a9VL6puONwCwPa7RasD1b+Q713YHc6dDmCRrlI9cDoCYEMYAGAdXlT9w3QEABwqAwAAsDX2tHr44YjpEAAAYNu8prpT9fbpEAAAAA7LRdMBLI5DdOw2963eWv1gq8/CAViGvdVPVm+q7jjcAlfmWdWnpyNYJAdWAQ7fV7caoILtdt50AAAcDgMAALA1vq/VQSAAAGAz/W51j+rD0yEAAAActs9OB7A4x0wHwAE6qfqt6oXV9YdbAJhzi+pV1X+q9s2mwOX6SPUH0xEs0j2q46YjAHY5Yyqswz9Xz5+OAIDDYQAAAA7fCdWjpyMAAIBt8/jqwdWnpkMAAADYEgYAWDcH59gNHlC9tfo30yEA7Aj7qp9rNQRw0+EWuDznTgewSEdX952OANjlzpgOYBGeW31yOgIADocBAAA4fP9PdZ3pCAAAYMtdUv109Yjq4uEWAAAAto4BANZt73QAXIHjqt9qdYPuVw23ALDz3LF6U/Vvp0PgMryg+j/TESySm6sBDt21qztNR7AI500HAMDhMgAAAIfnq6uHTUcAAABb7vPVD1aPng4BAABgyxkAYN0MALBT3aZ6ffVvpkMA2NGOr36zenZ10nALXNpnqmdOR7BI96/2TUcA7FKn5ywb2++D1R9PRwDA4fJNEwAcnv9ZHTkdAQAAbKlPV/+61cNsAAAAbJ7PTQewOJ7PYSd6aPWnrUbvAeBAPKh6c25sZWc5dzqARbp69c3TEQC71FnTASzC01td/gIAu5oPGAHg0J1R3W86AgAA2FIfb7U2/uzpEAAAALbNZ6cDWJy90wFwKSe2uin3SdUxwy0A7D43qF5Z/ac8g8zO8Orqb6cjWKQzpwMAdqGrVveYjmARzpsOAICt4M03ADg0R1a/MB0BAABsqY9U96xeOh0CAADAtjIAwLoZAGCnuEP1purB0yEA7Gr7qp+rntfqFmyYdEkOeDHjzGrPdATALnPfViMAsJ0uqF4/HQEAW8EAAAAcmh+vbj4dAQAAbJkPVHepXjsdAgAAwLYzAMC6GQBg2t7qZ6s/rW483ALA5nhg9YbqNtMhLN650wEs0g2rr5uOANhlzpgOYBF8bwjAxjAAAAAH71rVo6YjAACALfPe6q7VX02HAAAAsBYGAFg3z+cw6drVC6r/N2MUAGy9G1Wvrr5/uINle0dGvplx5nQAwC6yt3rAdASL8LTpAADYKj5gBICD95+rE6cjAACALfE3rQ7/XzAdAgAAwNoYAGDd9k0HsFj3q95a3Xs6BICNdnT1G9UvV0cOt7BcT5kOYJEMAAAcuDtX15iOYOO9Js+AAbBBDAAAwMG5ZfUD0xEAAMCWeFurw//vHu4AAABgvT4/HcDiXDwdwOLsqR5V/UEergdgfX6kenl1ynAHy/SMjL2xfrepbjAdAbBLGE1hHc6bDgCArWQAAAAOzv+o9k5HAAAAh+0d1T2r90+HAAAAsHZHTAewOEYnWKejqydVj86zYQCs352q11ffOB3C4ny4evF0BIuzpzpjOgJglzAAwHb7XPWs6QgA2Eo+5AGAA3f/6j7TEQAAwGG7oLp79YHpEAAAAEYYAGDdDACwLtevXlU9ZDoEgEW7bvWK6nunQ1icc6cDWCQHWgGu3K2qm0xHsPFeXH1oOgIAtpIBAAA4MHurx05HAAAAh+2drQ7/XzgdAgAAwBgDAKybAQDW4S6tbly+/XQIAFRHV0+uHpNnlVmf51UfnY5gce5aXW06AmCHM5bCOpw3HQAAW82bagBwYH6wuuV0BAAAcFj+ptXh//dPhwAAADBq33QAi2MAgO32sOpPqmtNhwDApeypHlk9s7rqcAvLcFH1u9MRLM4R1f2mIwB2uDOmA9h4H69+bzoCALaaAQAAuHLHVj83HQEAAByW91b3qt43HQIAAMC4I6cDWBwDAGyXo6rfrB6fcRMAdq4HVS/LUA3rce50AIvkZmuAy/dV1e2nI9h4z6s+MR0BAFvNAAAAXLmHVdeZjgAAAA7Z+6q7Vu+aDgEAAGBHcEiWdTMAwHY4pXpF9W+nQwDgAHxj9erqZtMhbLyXV383HcHi3DdjgwCX54xqz3QEG++86QAA2A4GAADgip1U/YfpCAAA4JD9Q3Xv6t3DHQAAAOwcR0wHsDgGANhqX1+9rrrDdAgAHISbthoBuNN0CBvt4uqp0xEszgnV3acjAHaoM6cD2Hgfql46HQEA28EAAABcsZ+srjYdAQAAHJKPVfer3jYdAgAAwI5iAIB1MwDAVrpH9crqlOkQADgE16j+uPrW6RA22rnTASySA64AX8lACuvwzOpz0xEAsB0MAADA5btm9bDpCAAA4JBc1Oohi9dPhwAAALDjHDkdwOJ8ejqAjfGg6g9bPUAPALvVMdWzqh+cDmFjvbV683QEi3NmtWc6AmCHuX/ei2X7GX8CYGMZAACAy/cz1fHTEQAAwEH7fPW91cuHOwAAANiZjpgOYHEumg5gIzy81Y1mR0+HAMAW2FudU/3UdAgby0Ew1u2U6vbTEQA7zJnTAWy8d1avm44AgO1iAAAALtsNqh+ejgAAAA7aJa1ujHnOdAgAAAA7lgEA1u1T0wHsanuqx1S/lGe9ANgse6rHVo/Prdlsvae2Gg2HdXLQFeCLjqjuOx3Bxju31bNiALCRfCgEAJftZ6ujpiMAAICD9pPVb09HAAAAsGPtye3ZrN9F0wHsWkdW51WPnA4BgG30sOpX80wzW+vC6k+mI1gcAwAAX3S36qTpCDbeU6cDAGA7ebMMAL7SDaqHTEcAAAAH7ZzqcdMRAAAA7Ggn5FkJ1u9T0wHsSsdVz6++azoEANbg7FajN/umQ9go504HsDi3rG42HQGwQxhFYbu9trpgOgIAtpMPtQHgK/1Mq5sUAACA3eMPqx+djgAAAGDHO3E6gEW6aDqAXec61Sur+0yHAMAa/etWIwBHTIewMZ5TfXw6gsV54HQAwA6wpzpjOoKNd950AABsNwMAAPClrld933QEAABwUP68+o7q89MhAAAA7HgnTAewSAYAOBg3rl5d3WY6BAAGfEf11IwAsDU+Uf3edASL48ZrgNV7GtefjmCjfa56xnQEAGw3AwAA8KV+qjpqOgIAADhg72r1EMUnp0MAAADYFU6aDmCRvG/Bgbp59cpWIwAAsFTfXj23Ono6hI3gZljW7bTqGtMRAMPOmg5g4720+tB0BABsNwMAAPBF16l+YDoCAAA4YP9Y3Tcf6AAAAHDgTpwOYJEumg5gV7hl9YrqetMhALADPKB6WnXEdAi73kvyWSLrtbc6fToCYNgZ0wFsPCNPACyCAQAA+KKfqI6ZjgAAAA7IZ6pvrd4xHQIAAMCuYgCAdftM9enpCHa821YvazVaDwCsnFU9pdVhWjhUn2s1JgHr5OArsGQ3qr5+OoKN9onqedMRALAOBgAAYOUa1Q9PRwAAAAfsR6tXTUcAAACw6xgAYN0+Nh3Ajnf76o9bfWYNAHyp76x+K887c3jOnQ5gce6dy6iA5TpzOoCN93vVx6cjAGAdvCEGACuPqI6bjgAAAA7IE6rfmI4AAABgVzIAwLoZAOCK3LZ6cXXSdAgA7GAPrX6l2jMdwq71hupt0xEsyrHVt0xHAAwxAMB2O286AADWxQAAAKwO/v/IdAQAAHBAXlr9xHQEAAAAu9YJ0wEszkenA9ixvr7V4f+rTYcAwC5wdvWL0xHsaudOB7A4DsACS3T16i7TEWy0D1cvmY4AgHUxAAAA9W/zUAUAAOwG76q+q/rcdAgAAAC7ls+EWDcDAFyWW1Qvqk6eDgGAXeTHqkdNR7BrnVddPB3Bojyw2jsdAbBmp1f7piPYaM+oPjsdAQDrYgAAgKXbWz18OgIAALhSH60eUP3DdAgAAAC7mgEA1u1j0wHsOF9TvaK69nQIAOxCP199/3QEu9J7qvOnI1iUa1V3nI4AWLMzpgPYeE+dDgCAdTIAAMDSfWt1k+kIAADgCl1cfXf1tukQAAAAdr3rTgewOAYAuLQbVi9udRgIADh4e6pzqjOnQ9iVzp0OYHH8XgUsydHVfaYj2Gh/U71mOgIA1skAAABL9xPTAQAAwJX6L9ULpiMAAADYCKdMB7A4H50OYMe4TvWS6nrTIQCwy+2tnl7dZTqEXedZ1UXTESyKAQBgSe5RHTcdwUZ7anXJdAQArJMBAACW7LTqjtMRAADAFXpx9Z+nIwAAANgYBgBYt49NB7AjXK16UXWz6RAA2BBHV8+rvvr/Z+++o2+/6zrfP08KBEggNEE60qQoCAICAhZApSSxizp20WufOzoqc+cqjmWYmeuMZRwZCyjnhA4ioIiAYAEFwYo0CyhFivRASDv3j30mhnBOOCf5/X7v/ft+H4+1fuukrKz1/CPZ2Wfv7+f1mQ5hX3lf9dzpCFbl9tUdpyMA9ojRE3bboekAANhrBgAAWLN/Nx0AAABcobdUX1NdMh0CAADAIpxZXXM6gtX5l+kAxl2rel716dMhALAw12vz/9gbToewrxycDmB1HIgF1uCk6hHTESzan1avn44AgL1mAACAtbptddZ0BAAAcEwXVl9RvXs6BAAAgMW4yXQAq+SzjXU7pXpydZ/pEABYqNtUz66uMR3CvvHbGelib3lOFViDe1WfPB3Boh2aDgCACQYAAFir76pOno4AAACO6Qeql09HAAAAsCg3nQ5gld41HcCo/1E9fDoCABbuPtWv55lojs8F1VOnI1iVe+dQLLB850wHsGiX5P0bACvlwy4A1uha1ddPRwAAAMf09OpnpiMAAABYnBtPB7BKbhddr/9Yfed0BACsxJdV/2k6gn3j4HQAq3JSRsGA5TtrOoBFe2H1tukIAJhgAACANfrq6szpCAAA4KjeWH3zdAQAAACLdJPpAFbp3dMBjPjG6jHTEQCwMj9cfcV0BPvCy6u/m45gVc6eDgDYRbev7jgdwaIdmg4AgCkGAABYI7csAADAdrqw+prqA9MhAAAALJIBACYYAFifB1WPqw5MhwDAyhyoHl/dbTqErXc4B8nYW59fnT4dAbBLjJywmz5cPWs6AgCmGAAAYG3uX911OgIAADiqH6leOR0BAADAYt10OoDVuSBDh2tzh+qp1anTIQCwUtesnl190nQIW++JbYYAYC+cVn3BdATALjlnOoBF+83qg9MRADDFAAAAa/Md0wEAAMBRvbR67HQEAAAAi3az6QBW519yqGhNblA9r7rudAgArNwtqqdlkIcr9rfVn0xHsCpuyAaW6JOqe09HsGjnTgcAwCQDAACsyY2rL5mOAAAAPs57q39TXTIdAgAAwKLdbjqA1XnXdAB75mrVM6rbTIcAAFU9oPov0xFsvYPTAazKw6pTpiMAdtjDq5OnI1isd1fPn44AgEkGAABYk29t8+AFAACwXb69+qfpCAAAABbt+tX1piNYnbdNB7BnHtfmoCEAsD2+t/qy6Qi22lOqC6cjWI3rVfefjgDYYWdPB7BoT8t7NQBWzgAAAGtxUvUt0xEAAMDHeUL11OkIAAAAFu920wGs0lumA9gT31N9w3QEAPBxDlSPr+44HcLWcqsse81BWWBJrlk9aDqCRTs4HQAA0wwAALAWD65uMR0BAAB8jL9r84A0AAAA7DYDAEx4+3QAu+4B1X+bjgAAjun0Nre8X3M6hK3lYBl76azpAIAd9JC8x2L3/EP18ukIAJhmAACAtfim6QAAAOBjXFJ9Y/XB6RAAAABWwQAAE942HcCuuln11OrU6RAA4Ap9WvU/pyPYWs+p3j8dwWrcurrrdATADjl7OoBFO7c6PB0BANMMAACwBtfPhwwAALBt/mf1B9MRAAAArIYBACa8dTqAXXNa9YzqRtMhAMBx+Ybqa6Yj2EofafO+DvaKZ1mBJTi5evh0BIv2pOkAANgGBgAAWIOvra4+HQEAAFzqTdWjpyMAAABYFQMATHjbdAC75meqe01HAAAn5BeqT5mOYCsdnA5gVc6aDgDYAferbjAdwWK9unrNdAQAbAMDAACswc2gnuoAACAASURBVDdOBwAAAJc6XH1b9aHpEAAAAFblttMBrNJbpwPYFV9ZPWo6AgA4YdeunlydOh3C1nlp9U/TEazG3aubT0cAXEXnTAewaIemAwBgWxgAAGDpPrO663QEAABwqV+pXjAdAQAAwKrcqLrOdASrc1H1rukIdtynVr88HQEAXGn3rB4zHcHWuSQHzdg7B6qzpiMAriKvY+yWi9uMdgEAGQAAYPm+aToAAAC41Fuq75+OAAAAYHVuNx3AKr21zQOrLMc1q6dVp0+HAABXyQ9WD5iOYOscnA5gVc6eDgC4Cu5S3WY6gsV6SfW26QgA2BYGAABYstOqR05HAAAAl/r26v3TEQAAAKzOp00HsEpvmg5gx/18m4fcAYD97aTqCdUZwx1sl9dUfzYdwWo8sLrOdATAlWTEhN1klAkALsMAAABL9tDqzOkIAACgqkPV86YjAAAAWKXPmA5gld40HcCO+srqG6cjAIAdc+vqv01HsHUcOGOvXK3N860A+9FZ0wEs1keqZ05HAMA2MQAAwJJ9zXQAAABQ1QeqH5iOAAAAYLXuPh3AKr1pOoAdc7PqF6YjAIAd96gcwOVjPam6eDqC1XCDNrAf3aS653QEi/WcNs+YAQBHGAAAYKmuXX3RdAQAAFDV/1O9fToCAACAVTq1uvN0BKv0pukAdsRJ1a9X15sOAQB2xS/l//P8q7dXL5qOYDUeWl19OgLgBJ1dHZiOYLEOTQcAwLYxAADAUn15dY3pCAAAoD/PDWkAAADMuWN12nQEq/Sm6QB2xA9VnzsdAQDsmptUPz0dwVY5OB3AapxRfc50BMAJOms6gMV6T/X86QgA2DYGAABYqq+eDgAAADpcfWd18XQIAAAAq/UZ0wGs1pumA7jKPrP60ekIAGDXfV314OkItsazqvOmI1iNs6cDAE7AtTOSyO55WnXBdAQAbBsDAAAs0U2qB05HAAAAPb562XQEAAAAq3b36QBW6aLqLdMRXCVXb/PZ1qnTIQDArjtQ/e/qWtMhbIUPVb8xHcFqnN3mNQhgP/iiNp+XwG44OB0AANvIAAAAS/TI6uTpCAAAWLn3Vj80HQEAAMDq3W06gFV6S5sRAPavH6vuMh0BAOyZW1U/OtzA9njidACrcZPqHtMRAMfprOkAFuvN1R9NRwDANjIAAMASffV0AAAA0H+o3jUdAQAAwKqdlAEAZrxhOoCr5D7Vv5uOAAD23PflIC4bL6z+eTqC1Th7OgDgOJxaPXQ6gsU6tzo8HQEA28gAAABLc5vq7tMRAACwcq+q/vd0BAAAAKt3m+ra0xGskgGA/eua1ROqk4c7AIC9d0r1uDxbTV1cPWk6gtUwAADsBw+szpyOYLEOTQcAwLbyIRUAS/Ml0wEAAEDf3+bBGAAAAJh0z+kAVssAwP71Y9XtpyMAgDH3qB41HcFWODgdwGp8WvUp0xEAn4CxEnbLn1evmY4AgG1lAACApfmy6QAAAFi551QvmY4AAACA6gHTAazW66cDuFLuWn3PdAQAMO4nqhtMRzDu1dVfT0ewGg7WAtvu4dMBLNah6QAA2GanTAcAwA66eW5yAQCASRdXPzwdAQAAAEfcfzqA1XrjdAAn7OTqV6pTp0OAq+R91XlHfj545Oeiy/z986uPHOWfu0Z12uX+2hltnq8848jfP726Ti5dgjW4XvWT1aOmQxh3bpt/F2C3nV399+kIgGP4jOpW0xEs0iXVk6cjAGCbGQAAYEm+uDowHQEAACv2q9VrpiMAAACgumF1x+kIVun86s3TEZyw763uMR0BfIwLq7dW/1i9pXr3kZ93Ve+4zJ+/p82B/w/tUdfVq2tWZx759fpHfm7Q5v3H//nj//PXb17duM3QCLB/fHP1S9Urp0MYdaj68Yy/sPvu1+Z9w79MhwAcxdnTASzWS9v8fh8AOAYDAAAsyZdOBwAAwIqdV/3odAQAAAAc8dkZjmbG37a5vYr945bVj01HwEq9o3pd9YY2r5//WP1T9abq7W3n6+lHj/y89wT+mVPajADcsrrZkZ+bV7eoblPdrrrGzmYCV9FJ1c9V96kOD7cw5x+r368+Z7iD5Tulenj1a9MhAEdhAIDdcmg6AAC2nQEAAJbiRm1WUAEAgBk/Xb1tOgIAAACOeMB0AKv1hukATtjPVteajoCFe1v159VfVK+tXt/m9fJ9k1F76KI2txoe62bDA20GAW7fZgzgdtUdjvx8SkaNYMq9q6+qnjQdwqiDGQBgb5ydAQBg+9yqutt0BIt0fvWM6QgA2HYGAABYinOqk6cjAABgpd5R/dfpCAAAALiM+08HsFqvnQ7ghDy0Oms6Ahbm76tXVn/W5tD/n1XvHC3afofb3DL9j9ULL/f3rl19eptDN3etPqO6c3XaXgbCiv1U9aw2B5RYp6dXP5/XXXbfQ6prVB+ZDgG4DJ+ZsFue13pGAQHgSjMAAMBSnDMdAAAAK/aY6oPTEQAAAHDEtXMzFXP+ejqA43b16memI2Cfu6B6VfXy6o+O/Pr20aLl+UD1h0d+/o9Tqk+t7lV9dnWf6g7VgT2vg+W7ZfVv2wwBsE7vr55Tffl0CIt3rerzq+dOhwBcxtnTASzWoekAANgPfOALwBJcq3p3VnYBAGDC37V50PCi6RAA9r379bEPs8NueGn1OdMRAOy6L6x+ezqC1fq0jADsF/+h+vHpCNhnLqpe0eaW+hdWr8yt2Nvi+tV9L/NzrzxHAzvlA9Xtq3dMhzDmrOrZ0xGswi9X3zodAXDEdat35uJZdt57q0+uPjodAgDbzhsxAJbgwfnSEgAApvxUDv8DAACwXe4/HcBqXVC9YTqC43KL6tHTEbBPvK763TYH/l/S5iAs2+df2txQ/Zwjf36N6gHVQ9o8V/NpQ12wBNeufqT6jukQxjy/zQVFN5gOYfEeUZ1UXTIdAlA9LGfO2B1Pz+F/ADguJ00HAMAOePh0AAAArNSbql+fjgAAAIDL+fzpAFbrDW1GANh+j62uOR0BW+ri6ver769uV92x+p7qN3P4fz/5SPU71b+rPr26SfUN1aE2YwHAifmW6jbTEYy5oHrqdASrcKPqs6YjAI44ezqAxTo0HQAA+4UBAAD2uwPVQ6cjAABgpX6yunA6AgAAAC7jk6p7TkewWn89HcBxuXf1ldMRsGU+Uj2z+vo2B88eWP1/1d9ORrGj3l79WvW11Y3bDCb9z+qtk1Gwj5xa/eh0BKMOTgewGg7cAtvg6tUXTEewSP9U/cF0BADsFwYAANjv7lF98nQEAACs0D+2eVgQAAAAtskX5VkI5hgA2H4H2hxqPjAdAlvg4jY3xH99mwPhX1r9em6HX4OLqhdX31XdvM1Nw/8lgw/wiXx1defpCMb8cV4n2RsGAIBt8PnVGdMRLNK51SXTEQCwX/jSG4D97uHTAQAAsFI/VV0wHQEAAACX87DpAFbNAMD2+5LqftMRMOyPq++pblp9YZtD/x8YLWLS4epPqh+sblfdp/pf1Xsno2BLnVT9p+kIxhyuDk5HsAp3qD51OgJYvbOmA1isQ9MBALCfGAAAYL8zAAAAAHvvLdXjpyMAAADgck6tHjIdwar95XQAV+hq1WOnI2DIe6qfqe7S5oD3z1XvGC1iW/1x9R3VJ1dfUT23umi0CLbLOdU9pyMYc6jNEADstrOnA4BVOykDAOyOvzryAwAcJwMAAOxnn1zdfToCAABW6D9XH52OAAAAgMv57Oo60xGs1nuqN01HcIUeVd1mOgL20OHqJdXXVjetvq96zWQQ+8pHq6dVj6huVv1Q9ebRItgOB6r/OB3BmL9tM5QCu80AADDpnm2e0Yeddmg6AAD2GwMAAOxnD27zpQoAALB33lb9ynQEAAAAHMVDpwNYtVfnNtBtdq3qP0xHwB75SPW46s7V57Z5wP780SL2u3dUj20zovIl1e/N5sC4h1d3m45gzMHpAFbh3tWNpyOA1TJCwm64pDp3OgIA9hsDAADsZw+aDgAAgBX673lYFAAAgO308OkAVu1V0wFcoe/OARqW75/b3Ep9i+rbq9fO5rBAF1fPqj6v+rQ2QxPnjRbBjAMZFlqzp1YXTEeweCflMw5gjgEAdsMfVP80HQEA+40BAAD2qwMZAAAAgL32oeqXpyMAAADgKG5Tfep0BKv26ukAjunM6t9PR8Auem31jdWtqh+v3j1aw1r8dZuhiVtVP1l9YLQG9t6XVHeejmDEu6vnT0ewCg7gAhNuV91pOoJFOjQdAAD7kQEAAParu1SfPB0BAAAr84TqfdMRAAAAcBQPmw5g9V41HcAxfX913ekI2AV/XX1Vm+cnnlB9dLSGtXp3m5vQb1X9aPWeyRjYQydVj56OYMzB6QBW4UHV6dMRwOoYH2E3fLR6+nQEAOxHBgAA2K8eNB0AAAArc0n1s9MRAAAAcAxfPh3Aqr2v+vvpCI7q+tX3TEfADvuL6suqu1ZPafPZLUx7b/WYNkMAP9xmGACW7iurW09HMOI51funI1i806qHTEcAq2MAgN3wW21+zwgAnCADAADsVw+eDgAAgJV5XvXG6QgAAAA4iltU95uOYNVeXR2ejuCovrc6YzoCdsgb2hz8/4zqGTn4z3b6YPWfq9se+fUjszmwq05u816D9Tm/etp0BKvgIC6wl25Y3Wc6gkU6NB0AAPuVAQAA9qOrVQ+YjgAAgJX5H9MBAAAAcAyPrA5MR7Bqr5oO4KiuU333dATsgHdW31ndpc3Bf4Mj7Afvr364+tTqiRmsYLm+ubrudAQjDk4HsAoPq06ZjgBW4xFtBo5gJ72vzaUzAMCVYAAAgP3ovtW1piMAAGBF/rJ68XQEAAAAHMNXTQewen88HcBRfVd15nQEXAUfrn68zU3qv1BdOJsDV8o/Vl9X3bP6veEW2A2nV4+ajmDEH7R5jYPddP3qs6cjgNU4ezqARXpGdf50BADsVwYAANiPPm86AAAAVuZ/TAcAAADAMdyputt0BKv38ukAPs7p1fdNR8BV8NTqDtV/rD443AI74dVtnvd5ZPXPwy2w0767utp0BHvukurQdASr4EAusBeuWT1oOoJF8n4JAK4CAwAA7EcPmA4AAIAVeUd17nQEAAAAHMMjpwNYvTdVb5+O4ON8a3WD6Qi4El5fPbj6yuotwy2wG55c3bF6XJvDs7AEN23zus36HJwOYBUMAAB74cFtRgBgJ72leul0BADsZwYAANhvrl7dezoCAABW5Jeqj05HAAAAwFEcyAAA814+HcDHOaX63ukIOEEfrh5dfXr1wuEW2G3vq769un/118MtsFO+czqAEX9TvXo6gsW7dZv3iAC7ydgIu+FJGX4DgKvEAAAA+829qtOmIwAAYCUuqX51OgIAAACO4V7VbaYjWD0DANvny6tbTkfACXhxdefqp6oLhltgL72sunv1o9VFsylwld27usd0BCMOTgewCg7mArvp5Orh0xEs0qHpAADY7wwAALDfPHA6AAAAVuTF1T9MRwAAAMAxPHI6AKo/mg7g43z/dAAcpw9XP1Q9uHrTbAqMubB6THW/6o3DLXBVfed0ACPOzYgJu88AALCb7lfdcDqCxfmb6i+mIwBgvzMAAMB+89nTAQAAsCK/Oh0AAAAAx3C1DAAw78PVX01H8DE+t81t0rDtXlzdpXpsdclwC2yDV7R5/fa9BPvZV1XXm45gz72jetF0BIt39+pm0xHAYp01HcAiHZwOAIAlMAAAwH5ySnXf6QgAAFiJ91TPmo4AAACAY/jS6pOmI1i9V7a5uZjt8X9PB8AncH71PdWDqn8YboFt86Hqm9u8z/uX4Ra4Mq5RfeN0BCMccGO3HcgBXWD3nD0dwOIcrs6djgCAJTAAAMB+cvfqjOkIAABYiUNtHkYFAACAbfQd0wFQ/f50AB/jltUXTUfAFXhddZ/q59o8DA8c3TOru7UZ2oH95lFtDuqyLs9qM2ICu8kBXWA33KW67XQEi/MH1ZunIwBgCQwAALCf3H86AAAAVuRXpgMAAADgGO5SffZ0BFQvnQ7gY3xHdfJ0BBzDr1afWf35dAjsE2+pHlg9cToETtDtq/tNR7DnzmszAgC76XOq60xHAItz1nQAi3TudAAALIUBAAD2k/tMBwAAwEq8qvqL6QgAAAA4hm+fDoDqgurl0xFc6rTqm6cj4Cg+WD2yzb+f5w23wH7zkerrqu+tLhxugRPxTdMBjDg4HcDiXa36oukIYHHOng5gcS6onjYdAQBLYQAAgP3ks6YDAABgJX5lOgAAAACO4fTq30xHQPWK6sPTEVzqK6vrT0fA5byhzXMOT54OgX3uZ6sHVe+aDoHj9OVtft/Curyoevt0BIvnoC6wk25S3XM6gsX57eo90xEAsBQGAADYL25W3XQ6AgAAVuAj1bnTEQAAAHAMX1NdezoCqpdMB/AxvmM6AC7nt6p7V38zHQIL8fvV/aq/nw6B43B69RXTEey5i6snTUeweF9UXW06AliMR1QHpiNYnEPTAQCwJAYAANgv7j0dAAAAK/Gc6v3TEQAAAHAM/9d0ABzx0ukALvXp1b2mI+CIw9VPtTlI8b7hFliaN1b3rV41HQLH4ZumAxhxcDqAxbtO9TnTEcBinD0dwOJ8oHrudAQALIkBAAD2CwMAAACwN542HQAAAADHcN/qrtMRUF1QvWw6gks5YMe2+Gj11dWjq0uGW2Cp3lF9bvWC6RD4BO5b3Xo6gj33Z9VrpiNYPAd2gZ1wRvV50xEszjOqj0xHAMCSGAAAYL8wAAAAALvvvOq3piMAAADgGL5vOgCOeGX14ekIqrp69bXTEVC9t3pI9eTpEFiBD1YPz03bbLcD1SOnIxjhtYnddlab1xiAq+IL23ymAjvp0HQAACyNAQAA9oNTqntMRwAAwAo8Nw+vAwAAsJ1uW33JdAQc8bvTAVzqnOr60xGs3lurz6l+f7gD1uTC6uuqx02HwBX4mukARjyxumQ6gkW7WXX36Qhg3ztnOoDFeVv1kukIAFgaAwAA7AefVl1rOgIAAFbg6dMBAAAAcAw/XJ08HQFHGADYHt80HcDq/Vl1r+ovp0NghQ5X31H9+nQIHMOdqk+fjmDPvbV66XQEi3f2dACwr51afdF0BIvz5Ori6QgAWBoDAADsB/eaDgAAgBU4r/qt6QgAAAA4iptXXzsdAUe8r3rFdARV3aT6/OkIVu2Pqs9pc8sdMOOSNmMwB6dD4BgeOR3ACK9J7DYDAMBV8YDqutMRLM6h6QAAWCIDAADsB/eYDgAAgBX4rerD0xEAAABwFN9fXW06Ao54cXXRdARVfWV18nQEq/WS6gurDwx3AJtbJr+hOne4A47mK6cDGPGM6iPTESzap1e3no4A9i0jIuy011avno4AgCUyAADAfvAZ0wEAALACT5sOAAAAgKO4YfUt0xFwGS+YDuBSbtRlyguqh1Ufmg4BLnVx9XXVc6dD4HJunWff1uj91XOmI1g8B3iBK+NAddZ0BItzaDoAAJbKAAAA2+7U6i7TEQAAsHDnVc+bjgAAAICj+N7qmtMRcBkGALbD7ap7TkewSs9tc9jqw9MhwMe5uPqq6pXTIXA5XzIdwIiD0wEsngEA4Mq4W3XL6QgW5XB17nQEACyVAQAAtt2dqtOmIwAAYOGelwdWAQAA2D7Xrr5zOgIu4w3VP0xHUNXXTAewSi+qvqw6fzoEOKbzqodWb5wOgcv44ukARjy/evd0BIt2/+oG0xHAvmM8hJ32snxeCgC7xgAAANvubtMBAACwAs+eDgAAAICj+I7qzOkIuIwXTAdwqa+YDmB1XtbmoMRHp0OAT+jd1SOqf5kOgSPuXN1hOoI9d2H1lOkIFu3kNqM3ACfirOkAFufQdAAALJkBAAC23WdMBwAAwMJdUv3udAQAAABcznWrfz8dAZfzvOkAqrpTdcfpCFblz6uHtblZHNgfXt9mtOOC6RA44ounAxhxcDqAxXOTN3AibpmL+dhZF1ZPm44AgCUzAADAtjMAAAAAu+tV1bumIwAAAOByHt1mBAC2xYeq35uOoHKAjr31+uoLqvdNhwAn7I+q75uOgCPctrtOf1y9cTqCRXtIddp0BLBvnFUdmI5gUZ5fvXs6AgCWzAAAANvsQJYGAQBgt/3OdAAAAABczi2r75qOgMt5QfXR6Qiq+pLpAFbjHdUXVu+cDgGutP+VG7jZDveqbjAdwQivQeym06sHTUcA+8bZ0wEszqHpAABYOgMAAGyz21TXno4AAICFMwAAAADAtvnx3GDH9nnOdABV3aq6+3QEq3B+dU71puEO4Kr7tuqvpiNYvZPb3NTN+hyqDk9HsGhnTQcA+8J1qwdMR7AoH8znpQCw6wwAALDN7jwdAAAAC/e+6o+nIwAAAOAyPqP66ukIuJxLqt+ajqCqL54OYBUOV9+Qz05hKT5cfWn1/ukQVu+h0wGM+Lvq5dMRLNojciYE+MQeWp06HcGiPKvN77UAgF3kN3sAbDMDAAAAsLteVF00HQEAAACX8dg8y8D2+ePqndMRVPXw6QBW4f+tnjIdAeyoN1bfPB3B6n1BdfJ0BCMOTgewaDeu7j0dAWy9s6cDWBzvbwBgD/jSHIBtZgAAAAB21/OnAwAAAOAyHlI9eDoCjuI50wFUdUb12dMRLN6Tqp+YjgB2xTOqJ05HsGo3qO45HcGIp1YXTEewaA72Alfk6tUXTkewKG+vXjwdAQBrYAAAgG1mAAAAAHbX70wHAAAAwBEnVY+djoBjMACwHR5UXW06gkX7m+pR1eHpEGDXfE/1lukIVs3g2Tr9S/Xb0xEsmgEA4Ip8XptRRdgpT6kuno4AgDUwAADAtjql+tTpCAAAWLDXVP80HQEAAABHfFt1t+kIOIrXt/kchXkPnQ5g0T5UffmRX4Hlel/1jRn6YM7nTQcw5uB0AIv2qdUdpiOArXXOdACL430NAOwRAwAAbKvbVFefjgAAgAV7wXQAAAAAHHGj6ienI+AYnjYdQFUHqi+cjmDRvqX6m+kIYE+8sPrF6QhW6z7VNaYjGPHcNiMksFvOng4AttKB6uHTESzK66tXTUcAwFoYAABgW915OgAAABbuD6cDAAAA4Iifrs6cjoBjeMZ0ANXm++ObTUewWD9XPWU6AthTP1C9eTqCVbp6db/pCEacn3ExdpcBAOBo7lndZDqCRTk0HQAAa2IAAIBtZQAAAAB218umAwAAAKD6nOqR0xFwDG+s/nw6gqoeNB3AYv1N9YPTEcCeO6/6t9MRrNbnTgcwxoE5dtNnVTeejgC2zjnTASzOk6cDAGBNDAAAsK3uOB0AAAAL9g/VP09HAAAAsHqnVf+7OjAdAsfwjOkALuWgHLvhguprq49MhwAjnlU9fzqCVfr86QDG/H715ukIFuuk6uHTEcDWOWs6gEV5eZvBVABgjxgAAGBb3X46AAAAFuwPpwMAAACgenR1u+kIuAJPnw6gqpOrB0xHsEg/Wv3ZdAQw6rur86cjWJ17VKdPRzDicHVoOoJFO3s6ANgqt63uPB3BongfAwB7zAAAANvqttMBAACwYC+fDgAAAGD1blf9wHQEXIE3Va+ejqDaHJI7czqCxXl59V+mI4Bxf1v99HQEq3NKdc/pCMYcnA5g0R5cnTEdAWyNc6YDWJSLqqdNRwDA2hgAAGAbfVJ1nekIAABYMAMAAAAATDpQPa46bToErsBT29zQybzPmw5gcc6vvr66eDoE2Ao/Uf3TdASrc9/pAMa8tnrVdASLdfXqQdMRwNY4azqARfmd6p3TEQCwNgYAANhGt5sOAACABftg9VfTEQAAAKzad1afOx0Bn8Ch6QAu9YDpABbnJ6o3TkcAW+PD1Y9NR7A695sOYNTB6QAW7ezpAGAr3CCDQ+wsn5UCwAADAABsIwMAAACwe/4kN1sBAAAw547VY6cj4BP4m+ovpyOo6kB17+kIFuX11X+djgC2zuOr101HsCr3yTPca3ZuddF0BIv1iOqU6Qhg3FnVydMRLMZ51XOmIwBgjXx4BMA2MgAAAAC75+XTAQAAAKzW1drcdHjN6RD4BJ44HcCl7lhdbzqCxThcfWv10ekQYOtcXP3YdASrcmab9zms0zur352OYLGuV91vOgIYd9Z0AIvyrOpD0xEAsEYGAADYRgYAAABg97xsOgAAAIDVekx19+kI+AQOV0+ajuBS95kOYFF+pfqD6Qhgaz2l+svpCFbl3tMBjDo4HcCinT0dAIy6ZvXg6QgW5dB0AACslQEAALaRAQAAANgdh6s/mY4AAABglR5Y/fvpCDgOf1C9eTqCS913OoDFeH/16OkIYKtdUv3H6QhW5R7TAYx6dm7SZfcYAIB1e1CbEQDYCe+oXjgdAQBrZQAAgG10m+kAAABYqH+o3jsdAQAAwOqcWf16nlFgf3Cj1Xa5z3QAi/FT1bumI4Ct95zqL6YjWI27Twcw6rzqmdMRLNanVJ82HQGMMQLCTnpKddF0BACslS/XAdg216vOmI4AAICF+vPpAAAAAFbp56tbTEfAcbigevp0BJc6vbrDdASL8ObqZ6YjgH3hcPXT0xGsxl2rU6YjGHVwOoBFcwAY1unk6hHTESyKsVQAGGQAAIBtc6vpAAAAWDADAAAAAOy1r62+ZjoCjtNzqvdMR3Cpu+XZJnbGo6vzpyOAfePJ1VunI1iFa1R3mo5g1Iurt01HsFgGAGCd7lPdcDqCxXhj9YrpCABYM1+SAbBtbjkdAAAAC2YAAAAAgL10t+px0xFwAn51OoCPcffpABbhldWTpiOAfeWC6uenI1iNe0wHMOrivE9h99yjutl0BLDnjH+wkw5NBwDA2hkAAGDbGAAAAIDdYwAAAACAvXK96pnVNadD4Di9tfqd6Qg+hgNx7IQfqQ5PRwD7zuOqD01HsAp3mw5g3MHpABbrQPWI6QhgzxkAYCedOx0AAGtnAACAbWMAAAAAdsf7qn+ajgAAAGAVTm5zd9A8WQAAIABJREFUi+Gtp0PgBPxamxs42R53nw5g33tF9dvTEcC+9N7q8dMRrMKdpwMY9+fVX09HsFgOAsO63Km63XQEi/En1RunIwBg7QwAALBtDAAAAMDueO10AAAAAKvx49VDpiPgBBzOIb9tc1r1qdMR7Hv/aToA2NceNx3AKtxlOoCt8MTpABbrc6trT0cAe8boBzvp0HQAAGAAAIDtc6vpAAAAWCgDAAAAAOyFc6ofnI6AE/T71d9OR/Ax7lCdMh3BvvZn1fOmI4B97TXVK6YjWLwbVTecjmDcoeri6QgW6WrVF01HAHvmnOkAFuOi6qnTEQCAAQAAts8tpwMAAGChXjcdAAAAwOLdqfr16sB0CJygx08H8HHuNB3Avvdj1eHpCGDf+9XpAFbhLtMBjHtr9dLpCBbLjeCwDp9cfeZ0BIvxwuod0xEAgAEAALbLtarrTUcAAMBCvXY6AAAAgEU7s3pWdcZ0CJyg91dPn47g4xgA4Kp4Y/Wb0xHAIjy5+sh0BItnAICqg9MBLNZDq6tNRwC77qycD2PnHJoOAAA2vMEDYJvcZDoAAAAWzAAAAAAAu+W06tnV7adD4Er49eq86Qg+jgEAroqfry6ZjgAW4f3VM6cjWDzve6h6RgZH2B3XqR44HQHsurOmA1iM86rfmI4AADYMAACwTQwAAADA7riwevN0BAAAAIt0UvXE6gHTIXAlHK5+YTqCo3IQjivrA9UTpiOARXn8dACLd7vpALbCB6rfnI5gsc6eDgB21RnV509HsBjPrj40HQEAbBgAAGCbGAAAAIDd8ebqoukIAAAAFulnqi+bjoAr6feq101H8HFOqW4zHcG+9YQ2B+gAdspLqndOR7Bot50OYGscnA5gsc6qDkxHALvmC6qrT0ewGIemAwCAf2UAAIBtYgAAAAB2x99NBwAAALBIP1R913QEXAW/MB3AUd2iOnU6gn3pkurnpyOAxbk4t3Kzu25enTYdwVb4nepd0xEs0s2ru09HALvm7OkAFuOd1QumIwCAf2UAAIBtYgAAAAB2hwEAAAAAdtpXVz85HQFXwdtymG9b3WY6gH3r96o3TkcAi/TM6QAW7aTqU6Yj2AoXVk+ZjmCxzpoOAHbFqdXDpiNYjKdUF01HAAD/ygAAANvEAAAAAOyOv58OAAAAYFE+r3p8dWA6BK6CX2xzwIbtYwCAK+vXpgOAxXph9d7pCBbtttMBbI2D0wEs1jnTAcCueEB13ekIFuPQdAAA8LEMAACwTT55OgAAABbKAAAAAAA75V7Vs6urTYfAVXBh9cvTERyTAQCujA/mhm5g91xY/fZ0BIt2u+kAtsafVG+YjmCRPr269XQEsOPOmg5gMf62esV0BADwsQwAALBNbjIdAAAAC/WW6QAAAAAW4V7VC6rTp0PgKnpq9fbpCI7JAABXxtOq86YjgEUzMsJuuuV0AFvl4HQAi+WgMCyP/67ZKedWh6cjAICPZQAAgG1y4+kAAABYKAMAAAAAXFX3bnP4/zrTIbAD/vt0AFfoFtMB7EtPmA4AFu+F1UXTESyW9z9c1qEcwGN3nD0dAOyou1W3mo5gMc6dDgAAPp4BAAC2xWnVGdMRAACwQBdV75yOAAAAYF/7rOp3cvifZfj96lXTEVyhm00HsO+8ufrD6Qhg8d5f/el0BItlAIDL+vvqZdMRLNL9q+tNRwA7xqgHO+WV1eunIwCAj2cAAIBtccPpAAAAWKi3VRdPRwAAALBv3SeH/1mWn54O4Aqdmu+OOXHPzC25wN548XQAi2UAgMs7NB3AIp1SPWw6Atgx50wHsBjedwDAljIAAMC2uMF0AAAALNRbpwMAAADYt+5bPb+69nQI7JB/qJ47HcEVummeZ+LEPWs6AFiNF00HsFjXr06fjmCrPKX66HQEi+TGcFiGW1R3nY5gES5u874DANhCvjADYFsYAAAAgN3xtukAAAAA9qX75fA/y/PTbR5qZXvdZDqAfedd1cumI4DVeFn1kekIFuvm0wFslfdUvz0dwSJ9QXXadARwlZ1VHZiOYBFeVP3zdAQAcHQGAADYFgYAAABgd7xzOgAAAIB956FtDv+fMR0CO+i91ROmI/iEbjodwL7zmxn2APbO+dUfTUewWIaQuLyD0wEs0unV509HAFfZ2dMBLMah6QAA4NgMAACwLQwAAADA7njXdAAAAAD7yjdXz27zQDgsyS9WH5qO4BO60XQA+85vTAcAq/PS6QAWy/sgLu+5bYbMYKc5OAz725nVA6cjWIQPV8+ajgAAjs0AAADb4pOmAwAAYKHeOR0AAADAvvGD1S9Xp0yHwA47v/rZ6QiOi+F4TsQF1UumI4DVeeV0AItlAIDL+2j19OkIFumsnCOB/exh1anTESzCs6sPTkcAAMfmN24AbAsPcgAAwO5413QAAAAAW++U6nHVf54OgV3yS9U/T0dwXHxvzIl4efWh6QhgdV5ZHZ6OYJFcoMPRHJwOYJFuVN1rOgK40s6eDmAxDk0HAABXzAAAANvietMBAACwUO+eDgAAAGCrnV49p3rUdAjskgur/zYdwXG7/nQA+8qLpgOAVXpP9ffTESzSjaYD2Ep/UL1pOoJFcoAY9qerV18wHcEivLt6wXQEAHDFDAAAsC2uMx0AAAAL9a7pAAAAALbWjauXVl84HQK76FD1j9MRHLcbTAewrxgAAKa8YjqARTIAwNEczu287A4DALA/fW517ekIFuGpbYZTAYAtZgAAgG1x5nQAAAAs1PunAwAAANhK96j+pLr7dAjsokuqx05HcEIMAHC8PpADuMCcV04HsEg3nA5gax2cDmCR7ljdfjoCOGHGO9gpBoYAYB8wAADAtrjOdAAAACzUB6YDAAAA2DrfVP1hdYvpENhlz6peNx3BCXGLHcfrZdVF0xHAahkAYDdcbzqArfW66k+nI1gkB4lhfzlQPWI6gkX4++rl0xEAwCdmAACAbWEAAAAAdt7h6oPTEQAAAGyNq1e/WP1KddpwC+y2w9VPTkdwws6YDmDfeMV0ALBqfz0dwCKdOR3AVjs4HcAiGQCA/eWe1U2nI1iEc9t8dgoAbDkDAABsi+tOBwAAwAJ9qLp4OgIAAICtcNPqJdW3DXfAXvmN6tXTEZwwAwAcrz+ZDgBW7X3VO6YjWJwz29zsC0fzpOqi6QgW5z7VjacjgONmtIOd8uTpAADg+BgAAGAbXC23zAAAwG74wHQAAAAAW+EB1Z9WnzUdAnvkcPWY6QhO2Cn53pjj96fTAcDqvXY6gMU5OWNIHNs7q9+djmBxTqoeNh0BHLdzpgNYhFdVr5mOAACOjwEAALbBdaYDAABgoT44HQAAAMCoA9X/Xb0oN7qxLs+s/mI6ghN2+nQA+8Y/tDkEBzDpddMBLNKZ0wFstSdOB7BIbhSH/eE21Z2mI1iEc6cDAIDjd8p0AABkAAAAAHbLh6cDAAAAGHPj6leqh06HwB67pHrMdARXyqltbiGDT+T3pgMAMgDA7rhu9Y/TEWytZ7cZgD9jOoRFeVB1req86RDgChnrYCdcXD15OgIAOH4GAADYBteeDgAAgIX66HQAAAAAI764elx1w+kQGPD06q+mI7hS3lV95nQEABwnAwDshtOnA9hqH66eWX39dAiLco3qIdWzpkOAK2QAgJ3we9XbpiMAgON30nQAAFTXnA4AAICFOn86AAAAgD11RvWrbQ4EOPzPGl1SPWY6AgBYhddPB7BInqPjEzk4HcAinTUdAFyhG1T3m45gEQ5NBwAAJ8YAAADbwBcXAACwOz46HQAAAMCe+ezqL6pvnA6BQU+q/mY6AgBYhbe2GR+CnXSN6QC23u+1ef2BnfTw6uTpCOCY/DfKTvhIm+FgAGAfMQAAwDa41nQAAAAs1PnTAQAAAOy6U6ofrV5S3Xq0BGZdUP3IdAQAsBoXVv88HcHiuEiHT+TiNsNnsJPcLg7b7ZzpABbhOdUHpiMAgBNjAACAbWC5GAAAdsdHpwMAAADYVfesXtnm0LNboFi7/1X93XQEALAqb5kOYHE8R8fxODgdwCKdPR0AHNU1qgdPR7AIh6YDAIATZwAAgG1wrekAAABYqAunAwAAANgV16l+pnp5dbfhFtgGH6x+cjoCAFgdAwDstGtOB7Av/EX1V9MRLI4BANhOD8n7A66691TPn44AAE6cAQAAtoHlYgAAAAAAgOPzFdVr6/9n797Ddb3r+s6/d04kkJAgSEI4HxQhAkEQQShnEDDZO62irVOgvTpqdVSsjmO1djxUR+tYK05Hi9N2qtk7SEQsglCEqlgoiHJSEOQUDgY5gxwDOez549kwARLI3nut9X3u53m9ruu+1l4r64/3H9nJWs9z/z5331edONwC6+Lnq/dORwAAW+fy6QA2jvvouKEung5g49y5+urpCOAL7J8OYCNcWn16OgIAOHoGAABYBzeZDgAAgA11eDoAAACAHXOn6rnV06tbDbfAOvmb6t9ORwAAW+mvpwPYOCdNB7AYh6qrpyPYOAemA4DPcUL1jdMRbIRD0wEAwLExAADAOrBcDAAAAAAAcN1Oqf5F9drqscMtsI5+qvr4dAQAsJUMALDTTpwOYDHeVf3RdAQbxwAArJcHVGdPR7B4b6teMh0BABwbAwAArAMDAAAAsDsOTwcAAABwXC6oXl39dN5Pgevy+uo/TEcAAFvrg9MBbJyTpgNYlIPTAWyc+1a3no4APssoBzvhktw/BgCLZQAAgHVwynQAAABsKG/gAAAALNPXVn9QPbu623ALrLMfrK6ajgAAttaHpwPYOCdOB7Aoz6w+MR3BRtlX7Z+OAD7LAAA74WnTAQDAsTMAAMA6OHk6AAAANpTXfgAAAJblztXTqz+pHjbcAuvueUcuAIApH5oOYOOcNB3Aonyk+t3pCDaOAQBYD3ervnI6gsV7dfXa6QgA4Ni5CRyAdWAAAAAAdoebhAAAAJbh5tXPVa+rvqXVE9eA63dV9UPTEQDA1vvwdAAbx33dHK2D0wFsnEdUZ01HAF00HcBG8HMCACycF4oAWAcGAAAAYHecMh0AAADAF3Xj6kert1Y/XN1oNgcW41daDWYAAEwyAMBOO3E6gMV5fvXe6Qg2ysnVo6cjgPZPB7B411S/OR0BABwfAwAArAOHkgAAYHcY2wIAAFhPZ1RPrt5c/Ux109kcWJQPVj81HQEAUH2q+uR0BLDVrqqePh3BxjkwHQBb7uzqftMRLN4fVpdPRwAAx8cAAADr4KTpAAAA2FDGtgAAANbLLaqfrt5R/VJ1q9kcWKQfrz4wHQEAcMSHpwOArXfxdAAb53F52ABMOpCzXhy/S6YDAIDj54dCANaBQ0kAALA7jG0BAACsh7Orn6jeUv2L6qzRGliu11ZPnY4AALiWT0wHAFvvT6s3TEewUc6qHjIdAVvsoukAFu+K6pnTEQDA8TMAAMA6sBQKAAC7w9gWAADArDtUT6kua/XU8puO1sCyHa6+p7pyOgQA4Fr8bAKsA0/5ZacdmA6ALXV69bDpCBbvOdWHpyMAgONnAACAdeCppAAAsDsMAAAAAMx4aHVp9abq+6rTRmtgM/zn6kXTEQAAn+fT0wEA1cFWo2mwU/ZX+6YjYAt9Q3XqdASLd2g6AADYGQYAAFgH/n8EAAC7wwETAACAvXNG9R3Va6o/rB6fEWTYKR+sfng6AgDgOlw5HQBQXVa9ZDqCjXK76t7TEbCFLpoOYPE+VD1vOgIA2BkOXAIAAABsrtOnAwAAALbAV1Y/V729emp1z9kc2Eg/Ur1vOgIA4DoYAADWxcHpADbOgekA2DInVY+djmDxfqv61HQEALAzDAAAAAAAbK4zpgMAAAA21AnVI6tnV29o9WTym40Wweb6s+o/TEcAAFyPT08HABxxaQ78sbMMAMDeenB18+kIFu/QdAAAsHMMAAAAAABsLgMAAAAAO+trql+sLq9eUF1Q7Rstgs12dfVPq2umQwAArocBAGBdfKh67nQEG+Ve1R2mI2CL7J8OYPHeUb14OgIA2DkGAAAAAAA212nVidMRAAAAC3e76keq11WvqP5Zdc5oEWyPp7T6ewcAsK6umg4AuJaLpwPYOA4kw945MB3A4j0tQ6oAsFEMAACwDjwZBwAAds/p0wEAAAALdGb1xOrZ1Vur/6O6+2gRbJ+3VT8+HQEA8CUcng4AuJbfqz4wHcFGcSAZ9sb51R2mI1i8Q9MBAMDOMgAAAAAAsNnOmA4AAABYiJtW31JdWr27+vXqgurEySjYUoerb68+Nh0CAACwIJ+unjEdwUZ5cPVl0xGwBfZPB7B4f179xXQEALCzDAAAAAAAbLabTgcAAACssdtW3109v3pf9fTq8dWpk1FAv169cDoCAABggQ5OB7BRTqoeNx0BW+DAdACLd2g6AADYeSdNBwAAAACwq24xHQAAALBmzm/1RKUD1b2rfbM5wOd5T/WD0xEAAAAL9ZLqsuqO0yFsjAMZloDddNtWr1PDsbqmetp0BACw8wwAALAOrp4OAACADXbz6QAAAIBhN60eUj26urC6/WwO8CU8ufrgdAQAAMBCHW71FOAfmw5hYzymulH1qekQ2FAHMlLL8fnj6p3TEQDAzjMAAMA6uGo6AAAANpgBAAAAYNucVj2weviR677ViaNFwA31X6qnT0cAAAAs3MEMALBzTq8eUT13OgQ21P7pABbv0HQAALA7DAAAsA4MAAAAwO65xXQAAADALjuxOr965JHrQdWpo0XAsXh/9U+nIwAAADbAX1V/Wn3tdAgb40AGAGA3nFk9ZDqCRft09czpCABgdxgAAGAdXDkdAAAAG+zm0wEAAAA77FbV/a513b/Vk8iAZfvO6j3TEQAAABviYAYA2DkXVt9VXTMdAhvmcdUp0xEs2nOqD05HAAC7wwAAAOvgqukAAADYYAYAAACAJTu9Or+6z7Wuu48WAbvh4jypCgAAYCc9rfqF6uTpEDbCrVoNSvzJdAhsmIumA1i8Q9MBAMDuMQAAwDq4cjoAAAA22JdPBwAAANxA51RfXZ1X3bO6X3W36sTJKGDXXV49eToCAABgw7yv+v3qG6dD2BgHMgAAO+mU6jHTESzah6vnTkcAALvHAAAA6+Cq6QAAANhg504HAAAAfJ6zWh30v3urw/53P/L5OZNRwIjD1bdXH5oOAQAA2ECHMgDAzrmo+tHpCNggD69uOh3Boj2jumI6AgDYPQYAAFgHV04HAADABjMAAAAATDi7umN1h2t9vHOrg/5nj1UB6+ap1fOmIwAAADbUs6qP5IApO+Nu1VdUb5oOgQ2xfzqAxTs0HQAA7C4DAACsg6umAwAAYIPdsjo5w1sAAMDOObXV7xq3qm7T5x7yv+OR67ShNmA5Xl/94HQEAADABvtE9czqHw13sDkOVL8wHQEbYF8GADg+f1398XQEALC7DAAAsA4+NR0AAAAb7ITqnOqd0yEAAMDaull1+pHry6ovb3W4/5bX8edzqzNmMoEN8qnq21odRgEAAGD3HMwAADvHAADsjPtWt56OYNGeVl0zHQEA7C4DAACsg09OBwAAwIa7dQYAAABqdXj58dMRcJxOr06+1udn9Lnv+960OvFan5/V/3+4/4zqzCPfc+2vAey1f169ejoCAABgC/xhq6cE32Y6hI3wgFZDoe+dDoGFOzAdwOIdmg4AAHafAQAA1oEBAAAA2F3nTgcAAKyJu1eXTkcAwJZ7fvWU6QgAAIAtcU2rpwT/0HQIG+HE6oLqP02HwMJdNB3Aov1l9ZrpCABg950wHQAA1SemAwAAYMN5mgMAAACwDt5TPak6PB0CAACwRQ5OB7BRPLkcjs+dq/OmI1i0i6cDAIC9YQAAgHXwyekAAADYcHeYDgAAAAC23uHqH7caAQAAAGDv/HmeFMzOeVR14+kIWLD90wEs2uHqadMRAMDeMAAAwDowAAAAALvrjtMBAAAAwNb7P6vnTUcAAABsqUPTAWyM06pHT0fAgl00HcCi/ffq7dMRAMDeMAAAwDowAAAAALvrTtMBAAAAwFZ7afVj0xEAAABb7FB19XQEG+PAdAAs1M2rr5+OYNEM+gDAFjEAAMA6+MR0AAAAbDgDAAAAAMCU91aPr66cDgEAANhi76r+aDqCjXFhddJ0BCyQvzscj09Xvz0dAQDsHQMAAKyDT04HAADAhju9+vLpCAAAAGDrXFM9obp8OgQAAIAOTgewMW5ePWA6AhbowHQAi/bc6gPTEQDA3jEAAMA6+Ph0AAAAbIE7TQcAAAAAW+enqt+fjgAAAKBaPTX4E9MRbAwHmeHonFY9ajqCRTs0HQAA7C0DAACsg49OBwAAwBa483QAAAAAsFV+v/pX0xEAAAB81kerZ01HsDEMAMDReWR1k+kIFutvq+dMRwAAe8sAAADr4CPTAQAAsAW+ajoAAAAA2Bpvr/6n6prpEAAAAD7HwekANsZdqvOmI2BBjGZwPJ5ZXTEdAQDsLQMAAKyDj04HAADAFrjbdAAAAACwFa6ovrl6/3QIAAAAX+D3q/dMR7AxHGiGG+aE6hunI1g0Az4AsIUMAACwDgwAAADA7jMAAAAAAOyF76r+bDoCAACA63RV9fTpCDaGAQC4YR5QnTMdwWK9q3rRdAQAsPcMAACwDq6sPjkdAQAAG+4rqpOmIwAAAICN9ovVf56OAAAA4IvyFGF2ytdWt5mOgAUwlsHxuKS6ejoCANh7BgAAWBcfnQ4AAIANd0p1p+kIAAAAYGP9QfXD0xEAAAB8SX9avWE6go2wr7pgOgIWwAAAx+PQdAAAMMMAAADr4iPTAQAAsAXuPh0AAAAAbKS3V3+/umo6BAAAgBvEYUJ2ioPN8MXdrfrK6QgW6/XVq6cjAIAZBgAAWBcGAAAAYPedNx0AAAAAbJyPtrrZ/33TIQAAANxgB6vD0xFshIdVZ0xHwBrbPx3Aoh2cDgAA5hgAAGBdGAAAAIDdd/50AAAAALBRrq6+rXrNdAgAAABH5W3Vi6cj2Ag3qh4zHQFr7MB0AIt1uLpkOgIAmGMAAIB18cHpAAAA2AIGAAAAAICd9L9Wz5mOAAAA4Jh4qjA7xQFnuG5nV183HcFivaTVYA8AsKUMAACwLgwAAADA7rtzdeZ0BAAAALAR/mP1S9MRAAAAHLPfqj41HcFGuKA6ZToC1tCBnNvi2B2aDgAAZvlBEoB1YQAAAAB2377qntMRAAAAwOK9qPru6QgAAACOy4eq35uOYCOcWf2d6QhYQwemA1isK6tnTEcAALMMAACwLj40HQAAAFvi/OkAAAAAYNHeUP3d6tPTIQAAABy3g9MBbAwHneFznV49fDqCxXpe9f7pCABglgEAANbFB6YDAABgS9x7OgAAAABYrHdXj8u4NwAAwKb4veqD0xFshP3VvukIWCPfUJ06HcFiXTIdAADMMwAAwLrwAjIAAOyN+04HAAAAAIv00VaH/y+bDgEAAGDHfLq6dDqCjXD76vzpCFgjB6YDWKyPVs+ejgAA5hkAAGBdGAAAAIC9cV510+kIAAAAYFGurB5fvWo6BAAAgB13cDqAjeHAM6yc2GpIE47Fb1efmI4AAOYZAABgXRgAAACAvXFC9bXTEQAAAMBiHK6+vXr+dAgAAAC74n9Ub52OYCMYAICVB1c3n45gsQ5NBwAA68EAAADr4gPTAQAAsEXuPx0AAAAALMaPVr8+HQEAAMCuOZzDhuyM86vbT0fAGtg/HcBi/U31h9MRAMB6MAAAwLp4/3QAAABsEQMAAAAAwA3x76qfm44AAABg1x2cDmBjHJgOgDXg7wHH6jerq6cjAID1YAAAgHVxRfW30xEAALAlHlDtm44AAAAA1tqh6snTEQAAAOyJN1Yvn45gIzj4zLa7V3XH6QgWyyAPAPBZBgAAWCfvnQ4AAIAtcfPqK6YjAAAAgLX1u9U/qq4Z7gAAAGDvXDwdwEb4O9XNpiNg0P7pABbrDdUrpyMAgPVhAACAdfKe6QAAANgiD54OAAAAANbSH1bfWl01HQIAAMCeenp15XQEi3dy9bjpCBh00XQAi3VoOgAAWC8GAABYJ++dDgAAgC3ykOkAAAAAYO28vDpQXTEdAgAAwJ57X/X70xFshAPTATDk1tW9pyNYrKdPBwAA68UAAADrxAAAAADsnYdOBwAAAABr5S+qx1YfnQ4BAABgzMHpADbCY6tTpyNgwN+t9k1HsEj/o3rTdAQAsF4MAACwTt4zHQAAAFvkNtWdpyMAAACAtfBX1aOrD06HAAAAMOpZ1UemI1i806uHTUfAgP3TASzWoekAAGD9GAAAYJ28dzoAAAC2zEOmAwAAAIBxb6oeXr17OgQAAIBxn6x+ezqCjXBgOgD22Jm5D4djc1X1jOkIAGD9GAAAYJ0YAAAAgL310OkAAAAAYNSbWz2R713TIQAAAKyNg9MBbIQLq33TEbCHHludMh3BIj0/5ygAgOtgAACAdfI30wEAALBlHpU33AEAAGBbvb3VawOXT4cAAACwVv6oeud0BIt3bnW/6QjYQxdNB7BYhncAgOtkAACAdeLmIgAA2FvnVPeYjgAAAAD23Duqh1VvG+4AAABg/VxTPW06go1wYDoA9sjJ1TdMR7BIH6+ePR0BAKwnAwAArJO/qQ5PRwAAwJbxBiQAAABsl7dVD64uG+4AAABgfV08HcBGMADAtnhYddZ0BIv0zFYjAAAAX8AAAADr5FPV+6cjAABgyxgAAAAAgO1xWfXw6u3TIQAAAKy111avmY5g8e5efeV0BOyBi6YDWKyD0wEAwPo6aToAAD7P5dWXT0cAAMAWeVB1k6xJAwAAwKZ7XfWo6m+mQwAA1swp1ZnXum72eZ9f13XWkevWA70Ae+Xi6l7TESzehdW/mY6AXbSv1b/ncLTeXf3BdAQAsL4MAACwbi6vzp+OAACALXKj6qHV7w13AAAAALvnldU3VO+fDgEA2GEndPSH9j//66fteTWyVKLsAAAgAElEQVTAMjyt+tfVidMhLNqBDACw2e5T3WY6gkV6enXVdAQAsL4MAACwbt41HQAAAFvoggwAAAAAwKb60+ox1QenQwAArsNprQ7vf+Y69Tq+9vnXtb/nljmYCrBb3tXqycSPmg5h0R5YnV29ZzoEdslF0wEs1qHpAABgvRkAAGDdXD4dAAAAW2h/9d3V4ekQAAAAYEe9qLqw+uh0CACwkT5zCP9oD+1/5vry3McKsO4OZgCA43NC9bjq/50OgV1yYDqARXpzq+FWAIDr5YVTANaNAQAAANh751b3zRtLAAAAsEmeVf396orpEABgLZ1anfl5182u42uff5115DqzOnHPqwHYa8+sfqW6yXQIi3YgAwBspjtVXz0dwSJdPB0AAKw/AwAArJu/ng4AAIAttT8DAAAAALAp/mP1ndXV0yEAwK44qWM/tP+Z60Z7Xg3AEn2s1cDct02HsGiPqm5cfWI6BHbY/ukAFulwdcl0BACw/gwAALBuLpsOAACALXWg+pfTEQAAAMBx++Xq+1vdSAoArKfTWh3c/8x16nV87bquz3zf2dUJe14NwLY6mAEAjs+NW40APGs6BHbYRdMBLNKfVG+ejgAA1p8BAADWzdura/ImJQAA7LV7VHeq3jodAgAAAByTa6p/1moAAADYPTetzvwi182+xD+/6d4nA8BxeUH1nlYDNHCsDmQAgM1y8+qB0xEs0iXTAQDAMhgAAGDdXFG9uzp3OgQAALbQ36t+YToCAAAAOGqfrv5R9bThDgBYd6dVpx75eLMvcl3f99yiOnnPqwFg1lXVb1ZPng5h0fZXJ1ZXT4fADrkgZ7I4eldVl05HAADL4IdNANbRWzMAAAAAE741AwAAAACwNB+pvrnVExkBYJOdUp15retmn/f5dV1nHbk+87nD+wBwbC7OAADH5+bVA6oXT4fADtk/HcAivaB6z3QEALAMBgAAWEdvqx40HQEAAFvovtVdqjdPhwAAAAA3yOXVhdWrpkMA4AY4rdWh/Wtfp17P16/r+86q9u15NQBQ9YrqddV50yEs2oEMALAZTqu+YTqCRTo4HQAALIcBAADW0WXTAQAAsMUeX/3sdAQAAADwJb2i1eH/v5kOAWBrnF592ZHr5te6zqrOvNbH67rOGOgFAHbWJdXPTEewaAeqH5qOgB3wiOom0xEszserZ01HAADLYQAAgHVkAAAAAOZ8awYAAAAAYN39bvVtrW4aBYBjcdPq3Ors6hatDvV/5uNnDvh/2ed9fspIKQCwLg5VP13tmw5hsb6iunv1l9MhcJwOTAewSP8lr+cCAEfBAAAA68gAAAAAzLlXddfqr6ZDAAAAgOv0y9UPVFdPhwCwls6ublnd+sifz63OqW515DrnyNduPBUIACzW26v/Xj14OoRFO5ABAJbthOrC6QgW6dB0AACwLAYAAFhHBgAAAGDWP6z+5XQEAAAA8Dmuqv5Z9e+mQwAYc/PqDtXtj1x3OHJ95lD/2dXJM2kAwJY4mAEAjs+B6menI+A43L/V715wNN5bvWA6AgBYFgMAAKyjv66uzJvSAAAw5QnVj1fXTIcAAAAAVX2w+tbqhdMhAOyqm1V3anWY/1ZH/vyZ6y7VmXNpAABVXVo9pTptOoTFul91m1b3CsMSHZgOYJGe3mrgFQDgBjMAAMA6urp6Z6s3sAEAgL13+1ZPbfij4Q4AAACgXtfqxuK3TIcAsCNuVd21+soj11e1uj/iDjlIBwCsv7+tfq/65ukQFmtf9Y3VU6dD4Bjtnw5gkQ5NBwAAy2MAAIB1dVkGAAAAYNITMgAAAAAA055X/YNWBywAWI5TWj3V9Lzq7q3ufziv+urqzMEuAICdcCgDAByfAxkAYJm+6sgFR+Mt1cunIwCA5TEAAMC6umw6AAAAttzjq++tPjEdAgAAAFvocPVz1Y9V1wy3AHD9blrds7pHq8P9X3nkum2rJ5sCAGyi51YfqG4+HcJiPbw6o/rodAgcpf3TASzSoVav9wIAHBUDAACsKwMAAAAw64zq71UHp0MAAABgy3ys+ifVpdMhAHzWCdWdq3u1OvD/meuOk1EAAEM+3ep31u+aDmGxblQ9pvqt6RA4SgemA1ikQ9MBAMAyGQAAYF0ZAAAAgHn/cwYAAAAAYC+9sfqm6rXTIQBb7KbVV1TnVfc5ct2rOn0yCgBgzRzMAADH50AGAFiWs6v7T0ewOC9v9ZovAMBRMwAAwLoyAAAAAPMeXN2tev10CAAAAGyBS6t/Un1sOgRgi9yk1QH/+1VfV923usNkEADAQry0ekt15+kQFutx1cnVldMhcANdWJ0wHcHiXDIdAAAslwEAANbV26YDAACA9lXfXv3AdAgAAABssKuqH6t+vjo83AKwyU6svqrVgf/PXF9bnTIZBQCwUIerQ9X/Ph3CYt2s+jvVH0yHwA100XQAi3N19fTpCABguQwAALCu3lN9vNXaPgAAMOdJ1Y9WV0yHAAAAwAZ6V/Ut1UumQwA20G2q+1X3P/LxPtXpo0UAAJvlYAYAOD4HMgDAMtykesR0BIvzwurd0xEAwHKdMB0AANfjcPXG6QgAAKAvq755OgIAAAA20H+t7p3D/wA7YV919+q7qt+sLq/eWf129UPVQ3L4HwBgp72petl0BIt2YDoAbqBvqE6djmBxDk0HAADLZgAAgHX2+ukAAACgWt00CwAAAOyMK6v/rXpc9d7hFoCl2ld9dfW91W+1eqLe66pfqb61OncuDQBgqxycDmDRbl+dPx0BN4CxCo7WJ6rfmY4AAJbtpOkAAPgiDAAAAMB6+PrqftXLp0MAAABg4d5RfVv1kukQgAW6U/XII9fDqlvM5gAAUP1m9YvVKdMhLNaB6tXTEfBFnNhqyBOOxrOqj01HAADLdsJ0AAB8EQYAAABgfXzPdAAAAAAs3DOqe+XwP8ANddfq+1vdNP/B6i3VU6vH5/A/AMC6+ED1/OkIFs2T1Vl3D8rvoBy9Q9MBAMDyGQAAYJ0ZAAAAgPXxrdU50xEAAACwQJ+o/mmrA6sfHm4BWGc3qS6sfqV6a/WG6t9W+6ubDXYBAPDFHZwOYNHOr243HQFfhJEKjtb7qt+fjgAAls8AAADr7E3VldMRAABAVae0OqwAAAAA3HB/Wn1NqydWA/CF7lR9R/XsVk+P/d3qu6o7TkYBAHBUfjeDdxy7fTlgzXq7aDqAxbk0ZyAAgB1gAACAdXZl9ZbpCAAA4LO+s7rRdAQAAAAswFXVT1VfX/3VcAvAOjmj1eGJf1+9vdU9AU+tLshrjwAAS3VF9czpCBbNAADr6h4ZqOPoHZoOAAA2gwEAANbd66cDAACAzzqneuJ0BAAAAKy5N1UPqn681RAAwLY7t/ru6oXV+6vfaTU2ervJKAAAdtTB6QAW7cHVWdMRcB2MU3C03lq9bDoCANgMBgAAWHcGAAAAYL38cHXidAQAAACsqYurr6n+ZDoEYNjtqidXL67eWf3f1SOqUyajAADYNS9q9XMfHIuTq8dNR8B1uGg6gMU5VB2ejgAANoMBAADWnQEAAABYL3fOG5wAAADw+d5RPaZ6YvWx4RaAKXer/kX1iurt1S9VD8w9agAA2+CaVoce4Vh50jrr5jathj7haFwyHQAAbA5vrgCw7gwAAADA+vnh6QAAAABYE4eri6vzq+cPtwBMOK/6ierPqr+sfjoHJAAAttXF0wEs2uOqU6cj4FoOVPumI1iUP6veMB0BAGwOAwAArLs3tFqGBQAA1sfXVg+fjgAAAIBhl1WPqp5YfWi4BWAvfVX1M9VbqtdWP17dZ7QIAIB18JfVq6YjWKzTq4dOR8C17J8OYHEumQ4AADaLAQAA1t3Hq3dORwAAAF/gJ6cDAAAAYMjh6teqe1X/bbgFYK/crPqO6sWtDnb9aHWn0SIAANbRoekAFu3AdAAccWYGKTg611SXTkcAAJvFAAAAS/CX0wEAAMAXeFD1iOkIAAAA2GNvaHXz73dWH51NAdh1N6q+qXpW9Z7qqdUDq32TUQAArLVLqqunI1is/fl9g/Xw2OqU6QgW5b9Vl09HAACbxQAAAEvwhukAAADgOv3UdAAAAADskSuqn6zOr/54uAVgt92nekr1zuoZrQ7hnDxaBADAUvxNq0OQcCzOrb52OgKqA9MBLM6h6QAAYPMYAABgCf5yOgAAALhOX99q9RwAAAA22Qure1Q/UX1qNgVg19yu+vHqTdWfVd9XffloEQAAS3VwOoBFc/CaaafkXhiOzier35mOAAA2jwEAAJbg9dMBAADA9fpX1b7pCAAAANgF76meVD26evNwC8BuOKF6ZHVp9ZZWQyd3mQwCAGAjPLP62HQEi2UAgGkPrc6cjmBRfrf6yHQEALB5DAAAsAQGAAAAYH3dp9o/HQEAAAA76OrqV6u7Vb9RHZ7NAdhxt6h+qHpj9YLq8dVJo0UAAGySj1f/ZTqCxTovw2TMcg8MR+vQdAAAsJkMAACwBB+s3jUdAQAAXK+fyutMAAAAbIYXV/etvrv60HALwE57QKthk3dWP1/deTYHAIANdnA6gEU7MB3A1tqXAQCOzgeq509HAACbyY3ZACzFq6YDAACA63XP6pumIwAAAOA4vKt6UvXg6tXDLQA76dTqidUrq/9RPeHI1wAAYDe9sHr3dASLZQCAKfepbjsdwaJcWn16OgIA2EwGAABYCgMAAACw3n6yOnE6AgAAAI7Sp6qfq+7a6qnYh2dzAHbMHauntDp09evVvWdzAADYMldXT5uOYLG+vrrFdARbaf90AItzyXQAALC5DAAAsBQGAAAAYL3drfoH0xEAAABwFJ5V3aP6kepjwy0AO+XerW4+f2P1fdWZszkAAGyxg9MBLNaJ1QXTEWylA9MBLMrbqpdMRwAAm8sAAABL8erpAAAA4Ev6yerU6QgAAAD4Ev6semh1UfWm2RSAHfOI6vnVK1sNdZ40mwMAAL2y+svpCBbLQWz22h2re05HsCiXVIenIwCAzWUAAICluKz60HQEAADwRd2p+oHpCAAAALgef119Z/V11YuGWwB2wgnVhdVLqxdWj57NAQCAL3BoOoDFenR14+kItspF0wEsziXTAQDAZjMAAMBSHK5eMx0BAAB8ST9SnTsdAQAAANfyseonq6+ofq26ZjYH4LjdqHpiq6ep/m51/9kcAAC4XgfzezjH5sbVI6cj2CoHpgNYlFdVr5uOAAA2mwEAAJbkVdMBAADAl3R69bPTEQAAAFBdUf1idcfqJ458DrBkZ1Q/Wr2j+vXqrrM5AADwJb2j+uPpCBbLgWz2ypdVD5yOYFEOTQcAAJvPAAAAS2IAAAAAluEJ1YOmIwAAANhaV1YXV3evfrB6/2wOwHE7pfqO6o3Vz1S3nM0BAICj4pAkx+qC6sTpCLbCBdVJ0xEsxjXVb05HAACbzwAAAEtiAAAAAJZhX/VLee0JAACAvXVN9VvVedUTq8tmcwCO28mtDv5fVj21Omc2BwAAjsml1SenI1ikW1YPmI5gKxyYDmBR/rC6fDoCANh8bsIGYEneUF0xHQEAANwg96n+4XQEAAAAW+Fw9azqXtW3VG+azQE4bidV/7h6Y6uD/+fO5gAAwHH5SPWc6QgWy8Fsdtup1aOnI1iUQ9MBAMB2MAAAwJJcVb12OgIAALjBfr666XQEAAAAG+twqwMEX1ddlPeRgOXbVz2+1X/P/lN1h9EaAADYOQ5Lcqwumg5g4z2yOn06gsW4ovqd6QgAYDsYAABgaV41HQAAANxgZ1c/PB0BAADAxrmm1cH/+1YXVn86mwOwIx5ZvaK6tLrrcAsAAOy051bvn45gke5S3W06go12YDqARXl29eHpCABgOxgAAGBpDAAAAMCy/EB1p+kIAAAANsJV1cHqvFYH/185mwOwI+5Tvbh6QXXv4RYAANgtV7Yau4Jj4YA2u+WE6oLpCBbl0HQAALA9DAAAsDQGAAAAYFlOrX51OgIAAIBF+1T1/7R62tsTqjfM5gDsiLOr/1C9vHrgcAsAAOyFg9MBLJYBAHbL11XnTEewGB+snjcdAQBsDwMAACzNn1dXT0cAAABH5dHVk6YjAAAAWJyPVL9c3bn6jurNszkAO+Lk6snVX1X/JPdvAQCwPV5avXE6gkW6X3Wr6Qg20kXTASzKb1Wfno4AALaHN5AAWJpP5AVgAABYol9s9VQzAAAA+FLeXf1kdftWh2Qvn80B2DGPrF5V/VJ15nALAABMeNp0AIt0QnXhdAQb6cB0AItyaDoAANguBgAAWKJXTQcAAABH7ctaPbURAAAArs+fV0+qblf9RPXh0RqAnXPX6rnVC6rzhlsAAGDSwerwdASL5KA2O+2uRy64Id5evXg6AgDYLgYAAFiiV0wHAAAAx+Rb8qY8AAAAn+vq6pnVw6p7Vb9RXTlaBLBzzqz+TfUX1WOHWwAAYB28uXrZdASL9PDq9OkINsr+6QAW5WkZsAEA9pgBAACWyIu/AACwXL9a3Ww6AgAAgHF/W/1ydZfqm6o/Gq0B2Hnf2Org/w9UJw+3AADAOjk0HcAinVo9ZjqCjXLRdACL4v9dAMCeMwAAwBK9svr0dAQAAHBMblX97HQEAAAAY15bfWd1bvXk6m2jNQA775bVb1bPqW473AIAAOvo6bkHlGNzYDqAjXF2df/pCBbjNa1e1wYA2FMGAABYoiuqV09HAAAAx+w7qkdMRwAAALBnPlX9VvWo6p7Vr1WfGC0C2B2Pr15Xfet0CAAArLH3V/91OoJFuqA6eTqCjXBhzlNxwx2aDgAAtpMfWAFYqpdOBwAAAMdsX/Wr1WnTIQAAAOyqN1Q/UJ1bfUv1wurwaBHA7rhd9dzq0uoWwy0AALAEB6cDWKSzqgdNR7AR9k8HsBjXVE+bjgAAtpMBAACW6mXTAQAAwHH5iupfT0cAAACw4z7V6obIh1Z3r/5t9cHJIIBddEL1PdVrq8cOtwAAwJI8u/rb6QgW6cB0AIt3k+qR0xEsxouqv56OAAC2kwEAAJbqpdMBAADAcfue6oLpCAAAAHbEK6rvr25bfVurGyMPjxYB7K67VC+s/q/qjOEWAABYmiuqZ0xHsEgXVfumI1i0b6hOm45gMQ5NBwAA28sAAABL9fbqXdMRAADAcdlX/afqnOkQAAAAjsm7ql+uzq/uWz2let9oEcDeeGL1muph0yEAALBgB6cDWKTbV/ecjmDRDkwHsBhXVL89HQEAbC8DAAAs2Z9MBwAAAMfty1uNAFjoBwAAWIaPtbpB/9HVbasntzoEC7ANblE9q/r16sbDLQAAsHR/XL1jOoJFcoCbY3Vi9bjpCBbjudWHpyMAgO1lAACAJXvpdAAAALAjHlt973QEAAAA1+tT1XOqJ1W3qp5QvaC6ZjIKYI89rHp1tX86BAAANsQ11SXTESySAQCO1YNajfvBDXFwOgAA2G4GAABYspdNBwAAADvm56t7TUcAAADwWVdXL6m+v7pNdWH1G9XHJqMABpxU/USr4ZNbz6YAAMDG+Y3pABbpa6o7TEewSMYjuKE+XD13OgIA2G4GAABYsj+rrpyOAAAAdsSNWi1nnzYdAgAAsMWubHXA9buqW7V6ItZTqvdPRgEMulP14urHqxOHWwAAYBO9vnrldASLdMF0AIu0fzqAxXhG9anpCABguxkAAGDJPlm9ZjoCAADYMV9d/fx0BAAAwJa5onpO9Z3VbapHV/++et9kFMAaeHz1iurrpkMAAGDDHZoOYJE8yZ2jdY/qztMRLMbB6QAAAAMAACzdS6cDAACAHfW/VBdORwAAAGy4D7W6gfGbqpu3+j3s16r3TkYBrIkbV/+5urQ6azYFAAC2wiXV1dMRLM5D8jsbR2f/dACL8c7qv09HAAAYAABg6V42HQAAAOyofdVvVHeZDgEAANgwb61+uXpUdU71hOqZ1ScmowDWzF1ajdA/aToEAAC2yLurF05HsDgnV4+bjmBRLpoOYDEuqa6ZjgAAMAAAwNK9dDoAAADYcWe1OoRyk+kQAACABftkq5vn/3n1VdWdqycf+dqnB7sA1tU3Vi+v7jkdAgAAW+jgdACLdGA6gMW4dXWf6QgW49B0AABA1UnTAQBwnC6r3lOdPR0CAADsqHtU/77VEykBAAD40g5Xf97qgP8Lqxe1GgEA4Is7ofqJ6seqfbMpAACwtX6n+lh1+nQIi/KY6pSMXfKl7c/v/Nwwf3HkAgAYd8J0AADsgJdNBwAAALviH1bfOx0BAACwxt79/7F359G+33V9758hExBkBplnRUQZhCKITMoMyQmli7bXa3vvrdXba9Wr5S473dbW2ltdOFEcULGVnBMSQoAwJ0xhFGQUy6gQlUGjQAIJZM65f/xCCSHDGfbe7+/vtx+PtX7rdzjrhPWEBcnev/39vD7VadWPVXetHlQ9u3pdDv8DHIibVadX/28OAgAAwKSvVC+fjmDt3Lx63HQEa+HE6QDWxr7pAACArzEAAMAm+MPpAAAAYNv8cvXI6QgAAICF+HSrBxD/z+rbqztWz6p+p/rsYBfAOrpH9c4cAgAAgKU4aTqAtbRnOoDFu3n12OkI1sKV1YumIwAAvuao6QAA2AJvnQ4AAAC2zdGtbmH7nupzwy0AAAA77VPVO6q3X/X+4dkcgI3xiOpl1bdOhwAAAP/TG1oNHN55OoS1sqf68Wr/dAiL9dTqmOkI1sLbqr+cjgAA+BoDAABsgvdWF1Y3mw4BAAC2xbdWp1Q/WF023AIAALBdvlq9v3p39YetDvz/9WgRwGb6X6vfq46dDgEAAL7BldWp1c9Mh7BW7lQ9tHrPdAiLdcJ0AGvj5OkAAICru9F0AABsgcta3XwDAABsrkdVz5mOAAAA2CL7q49VL2x1Q9lDqlu0+t7n2dXpOfwPsB3+Tau/9zr8DwAAy7R3OoC1tGc6gMU6unrKdARr4ZLqtOkIAICrO2o6AAC2yNnVk6cjAACAbfWTrQ7I/NZ0CAAAwEG4ovp49YGrvd5fnT8ZBbDLHFX9ZvVPp0MAAIDr9YHqw9X9p0NYK3uqfzsdwSI9prrldARr4bXVedMRAABXZwAAgE1x9nQAAACwI55bnVO9bjoEAADgWlxQfbT6475+2P9D1VcnowB2uZtVL86NfwAAsC72Vv/fdARr5buqe1efnA5hcfZMB7A29k0HAABckwEAADbF+6ovVzefDgEAALbVUa0e2H5UqwM1AAAAEy6t/qzVjXQfudr7R6srB7sA+Ea3ql5dPWI6BAAAOGAnVb9Q3Wg6hLWyp/qV6QgW5YgMAHBgvtzq8yMAgEUxAADApri8ekdubQAAgN3gW6pXVg+vPjfcAgAAbK7LqnOqP60+cdX7n131/hfV/rk0AA7AnaqzqvtPhwAAAAfls9VbqsdNh7BWDABwTQ+u7jodwVp4SXXRdAQAwDUZAABgk5ydAQAAANgt7lq9onpM9ZXhFgAAYD3tr/66+vNWB/r/8qr3T/X1Q/6XT8UBcFju3erw/72mQwAAgEOyNwMAHJxHVretPj8dwmLsmQ5gbeybDgAAuDYGAADYJGdPBwAAADvqIa1+CPfM6orhFgAAYFkuq86tPnPV+2dbHfb/TPXpvn7g/5KpQAC2zf2r11d3nA4BAAAO2enV86qbTIewNo6snlb9wXQIi2EAgAPx2ZxBAAAWygAAAJvk/dWXq5tPhwAAADtmT/Wc6qenQwAAgG11QXV+9YVWt3h9/qpfX/P1uVYH/s+dyQRg2AOqN1S3mw4BAAAOy5eqV1bPmg5hrezJAAAr96geOB3BWnhRdeV0BADAtTEAAMAmubx6e/XU6RAAAGBH/d/Vn1e/PtwBACzfBdUnpiNgw513Lb/3lerSq71/tbqkuqi6+KrXV1sd8P9Sq/+vXtBq9PeC6/j3BIBrelD1+uq20yEAAMCW2JsBAA7OE6ubtPrckd1tz3QAa2PfdAAAwHUxAADApnlLBgAAAGA3+tXqwuoF0yEAwKK9v3rsdAQAAFvuwa0O/99mOgQAANgyr6s+n5EvDtxx1eOrV06HMO7E6QDWwkerD05HAABclxtNBwDAFnvzdAAAADDiiOq380NcAAAAgN3mwdUbc/gfAAA2zWXVqdMRrB03v3Pr6vunI1gLJ00HAABcHwMAAGya91fnT0cAAAAjjmr1AMiTpkMAAAAA2BH3rV5b3Wo6BAAA2BZ7pwNYO8dXR05HMOpprZ4fgeuzvzplOgIA4PoYAABg01xRvWM6AgAAGHNM9dLqkdMhAAAAAGyr+1Rvqr51OgQAANg276o+MR3BWrl99fDpCEbtmQ5gLby9Omc6AgDg+hgAAGATvWU6AAAAGHXT6lXVA6dDAAAAANgWd61eX91pOgQAANh2J08HsHYcAN+9jq2eOB3BWtg3HQAAcEMMAACwic6eDgAAAMbdsnpNdc/pEAAAAAC21G1bHf6/x3AHAACwM/ZW+6cjWCvPmA5gzOOrb5mOYPEuq14yHQEAcEMMAACwid5fnT8dAQAAjLtTq4fB7zIdAgAAAMCWOK56dXXf6RAAAGDHfLL6w+kI1sp9qvtNRzDihOkA1sJrqy9MRwAA3BADAABsoiuqN01HAAAAi3Dv6m25EQ4AAABg3R1dnVY9bDoEAADYcfumA1g7e6YD2HE3qo6fjmAt7J0OAAA4EAYAANhUr5sOAAAAFuMe1Ruquw13AAAAAHBojqheUD1lOgQAABhxanXpdARrxU3wu8/DqjtOR7B4X65eNR0BAHAgDAAAsKleOx0AAAAsyr2rt1b3nA4BAAAA4KD9h+qHpyMAAIAxX8hzoRyc781h8N3mxOkA1sJLq4umIwAADoQBAAA21Weqj05HAAAAi3L36uzqXsMdAAAAABy4Z1X/djoCAAAYt3c6gLVyo+rp0xHsqD3TAayFfdMBAAAHygAAAJvsddMBAADA4tytenN17+kQAAAAAG7QI6sXVkdMhwAAAONeVR2/fw0AACAASURBVJ0/HcFacSB897hP9R3TESzeX7V6ZggAYC0YAABgk505HQAAACzS3ao3VveaDgEAAADgOt2rell17HQIAACwCBdXp01HsFZ+sLrZdAQ74hnTAayFF1VXTEcAABwoAwAAbLK3VhdNRwAAAIt09+ot1XdOhwAAAADwTY6rXl7dbjoEAK7FFdUXqk9WXxpuAdht9k4HsFZuXD1pOoIdccJ0AGth33QAAMDBOGo6AAC20UWtDvQ8eToEAABYpLtUb6+eXr1zuAUAAACAlSOq36++ezoEgI12cXXeDbwuuo4/d25fvzn0NdVTdjIcYJd7W3VOdc/pENbGnur06Qi21e2rR0xHsHgfq94/HQEAcDAMAACw6c7MAAAAAHDdblWdVf296nXDLQAAAADUv6yeNR0BwOJd1wH+6zq0f/XX31SX73wyAFtgf/Wi6l9Ph7A2ntbq3Ix/9m+u46sjpyNYvL3TAQAAB8sAAACb7nXVr05HAAAAi3ZcdUb1w9WLh1sAAAAAdrMnVz8/HQHAjvhKdf5BvM67xvuVO58MwELszQAAB+7W1aOqN0+HsG1OmA5g8b42HgMAsFYMAACw6T5WnVPdczoEAABYtGOqU6o7Vb823AIAAACwG92lOim39gGsk4tbHcg/2NcXqksGegHYDB+t3lc9ZDqEtXFCBgA21U2rx09HsHjvrD41HQEAcLAMAACwG5xV/dh0BAAAsHhHVL9a3aH6l8MtAAAAALvJUa3GGW87HQKwyxzqAf6LrnoHgCn7MgDAgXtG9dPTEWyLJ7UaAYDrs286AADgUBgAAGA3ODMDAAAAwIH72erm1U9Wlw+3AAAAAOwG/7l65HQEwJq5sjr/qtd5V/v19b2u/ue+svPJALBlTq5+KechODB3rx5QfWg6hC13wnQAi3dZddp0BADAofANLwC7wRtbffN+9HQIAACwNv5Zde/qWdWXhlsAAAAANtlTq2dPRwAMubjVofzrel10PX/m3OqKnU8GgEU4t3pD9eTpENbGngwAbJojq6dPR7B4Z1afn44AADgUBgAA2A2+XP1h9ejpEAAAYK08sXpLqx8Yf2a4BQAAAGAT3bH6g+qI6RCAQ3RBdf5BvM67xq8BgEO3NwMAHLg91c9PR7ClHlnddjqCxds3HQAAcKgMAACwW5yZAQAAAODgPbD6o+qE6r3DLQAAAACb5Ijq9/KwPjDr4uqiq97PO8jX56tLdz4ZALjKy6oLq5tNh7AWvqe6a/Xp6RC2zInTASzeV6pXTkcAABwqAwAA7Bavq35hOgIAAFhLd6zOrn6oOmM2BQAAAGBj/ET11OkIYCMcyuH986ovXvXXAgDr6autRgB+eDqEtXBEq+H/35gOYcs8fTqAxTu91QgAAMBaMgAAwG7xgeqvqztMhwAAAGvpuFY/GPyZ6rnDLQAAAADr7v7VL05HAItxYavD+F/s6wfzv1idfy2v867xry8a6AUAluOkDABw4PZkAGBT3L/6tukIFm/fdAAAwOEwAADAbrG/elX1I9MhAADA2jqy+vXqAdWPV5fM5gAAAACspaNbHdK58XQIsG0uqf6m+uxV73991etrv/e3feNh/0tnMgGADfCm6nPVnaZDWAuPqW5RfWk6hMO2ZzqAxfvr6o3TEQAAh8MAAAC7yRkZAAAAAA7fP2m1Jv/MVg+TAAAAAHDgfrZ68HQEcMiuaHWQ4s+rT1/1+svqL656/0z1hak4AGDXuaJ6UfUvpkNYC8dUT231vxnWmwEAbsiprf4ZAQCwtgwAALCbvL66oPqW6RAAAGDtPbz6YPWs6uzZFAAAAIC1cb/q30xHAAfkr6oPV5+6xusj1UWDXQAA17Q3AwAcuD0ZAFh3d6r+znQEi7d3OgAA4HAZAABgN7mkekP1jOkQAABgI9yuOrP6yer5wy0AAAAAS3dk9d+rGw93AF93WfWJVgf9/0erw/1fO/R/6WAXAMDB+GCrr2W+azqEtfDk6ph8vbvOTqiOmI5g0T5evXc6AgDgcBkAAGC3OSMDAAAAwNY5pvrt6nuqn8hDAgAAAADX5aerh01HwC72N9X7qvdXf9LqoP8n8pkmALAZ9lb/ZTqCtXCL6rHVWcMdHLo90wEs3snTAQAAW8EAAAC7zaurK1rdLgEAALBVfrR6YPX3q78YbgEAAABYmrtXPzcdAbvI51vddvi+q17vrT49WgQAsL1Orv5zdaPpENbCngwArKubV4+bjmDR9lf7piMAALaCAQAAdpvPV++oHj0dAgAAbJzvbXV71v9evWK4BQAAAGBJnlsdNx0BG2p/9ZFWz0J87fXJ0SIAgJ336ers6geGO1gPJ1T/vNXX0qyXJ1fHTkewaO/O98QAwIYwAADAbnRGBgAAAIDtcevq5dWvVP+qumw2BwAAAGDcia0OVwBb4/JWBxre1uqw/zurL44WAQAsw94MAHBg7lI9pHrvdAgHbc90AIu3bzoAAGCr3Gg6AAAGuIkTAADYTkdU/6J6S3W34RYAAACASTerfn06AjbAR6rnthrTuHX1/a0GSF+Vw/8AAF9zenXRdARrw0Hy9XN09dTpCBbt8urU6QgAgK1iAACA3ejPqg9PRwAAABvvEdX7q6dNhwAAAAAM+VcZSIRD8cXq5OofV3eu7l/9VPXK6oLBLgCAJftyLojiwBkAWD+PqW45HcGinVX97XQEAMBWMQAAwG51xnQAAACwK9ym1UO5z69uOtwCAAAAsJPuWf3MdASskU9Vz62eUN2h+qHqhdXnJqMAANbM3ukA1sZ3V982HcFBMdrADdk3HQAAsJUMAACwW1l5BQAAdsoR1Y9Wf1Q9cLgFAAAAYKf8YnXj6QhYsP3VH1b/T3Xf6t7VT1VvqC4b7AIAWGdn5vZnDtzTpwM4YEdUJ0xHsGgX5oJAAGDDGAAAYLf6o+oz0xEAAMCucv/qPdXP5XM5AAAAYLM9svp70xGwUB+p/kP17dX3Vc+pPjFaBACwOS6rTp2OYG24UX59PLi623QEi/by6ivTEQAAW8mDxgDsVvurV09HAAAAu87R1b+vzqruMtwCAAAAsB2OqH79qndg5WOtPhe8b6uh0J+r/mwyCABgg+2dDmBtPLK6zXQEB+SE6QAWb990AADAVjMAAMBu9orpAAAAYNf6weqPqx+aDgEAAADYYn+/esh0BCzABdULWh0qul/1H6tPjBYBAOwO787XXRyYo6qnTUdwQPZMB7Bof1O9YToCAGCrGQAAYDd7Y6sfuAMAAEy4davbJ15d3Xm4BQAAAGArHF39/HQEDHtf9WPVnaofqd45mwMAsCu5CZoD5WD58t29euB0BIt2SnX5dAQAwFYzAADAbnZJdeZ0BAAAsOs9tfpQ9UPTIQAAAACH6Ueq+0xHwIALq9+s7lc9tPqdq34PAIAZe6v90xGshSdVN5mO4HrtqY6YjmDRTp4OAADYDgYAANjtXjYdAAAAUN261UMoL6/uMNwCAAAAcCiOq/7ddATssHOqZ1d3rX68+thsDgAAV/lU9c7pCNbCcdUPTkdwvfZMB7Bof1q9ezoCAGA7GAAAYLd7ZXXRdAQAAMBV9lQfrv6PLNgDAAAA6+XHM2zI7vG26u9W31b9cnX+bA4AANdi73QAa8MB8+W6VfWo6QgW7eTpAACA7WIAAIDd7oLqtdMRAAAAV3Pr6gXVW6vvHG4BAAAAOBA3rf7FdATsgHdUj68eXb2sumI2BwCA6/Hi6pLpCNbCCdWR0xFcq6dXR09HsGgGAACAjWUAAABWH/ICAAAszfdXH6z+S3XscAsAAADA9fln1e2nI2AbvaF6eKvP7N443AIAwIH5Yi6I4sDcvvre6Qiu1Z7pABbtj6pPTEcAAGwXAwAAUK+qvjodAQAAcC2Orn621RDAY4ZbAAAAAK7NTapnT0fANjmjemD1hOrdwy0AABy8vdMBrA0HzZfn2OqJ0xEs2r7pAACA7WQAAADqK61GAAAAAJbqO6o3t/rh5Z2GWwAAAACu7p9Wd5iOgC32h9WjqhOrDw23AABw6F5VnTcdwVowALA8P1B9y3QEi3V5dep0BADAdjIAAAArL54OAAAAuAFHVP9L9Ynq51qt3QMAAABMOqr6mekI2EKfqJ5VPbJ6+3ALAACH75LqJdMRrIX7XvViOU6cDmDR3lCdOx0BALCdDAAAwMprqgunIwAAAA7AcdW/rz5QPWG4BQAAANjdnlXdfToCtsD51T+v7l+dVu2fzQEAYAudNB3A2nDgfDluVB0/HcGi7ZsOAADYbgYAAGDlouqV0xEAAAAH4X7VWdXrqwcMtwAAAAC707OnA2ALnFZ9R/Ub1eXDLQAAbL23V+dMR7AW9kwH8D89rLrjdASL9dXq5dMRAADbzQAAAHzdi6cDAAAADsHjqw9UL8wPwAEAAICd88TqwdMRcBg+Xj2helZ17nALAADbZ3918nQEa+F7qztMR1AZY+D6vby6cDoCAGC7GQAAgK97XfXl6QgAAIBDcKPqh6s/rX6uOm60BgAAANgNfmY6AA7RxdXPVt9VvWG4BQCAnbF3OoC1cKPq6dMRVHXCdACLZtQFANgVDAAAwNddXJ0xHQEAAHAYjqv+ffXn1U9VR43WAAAAAJvq21rdnA7r5kPVI6pfqi4fbgEAYOd8rHrvdARrwc3z8+5Tfed0BIv1t9VZ0xEAADvBAAAAfKMXTwcAAABsgdtWv1Z9sHrqcAsAAACweX48zx2xXi6r/l31kFafmQEAsPvsnQ5gLTy+1fA+c06cDmDRXtzqe3wAgI3nB3EA8I3Oqs6fjgAAANgi969eXb0jt/IBAAAAW+Nm1f82HQEH4SPVw6qfry4fbgEAYM4p+XqQG3bj6knTEbvcnukAFm3fdAAAwE4xAAAA3+jS6uXTEQAAAFvs+1oNnr2zOn64BQAAAFhvP1zdYjoCDtDeVof/PzgdAgDAuHOr109HsBYcQJ9z++oR0xEs1ierd01HAADsFAMAAPDNXjwdAAAAsE0eUb2iemv1A8MtAAAAwHr60ekAOAAXVT/SarDiK8MtAAAsx0nTAayFp1VHTUfsUk+rjpyOYLFOrvZPRwAA7BQDAADwzd5QfXE6AgAAYBs9qnpj9ZbqCcMtAAAAwPp4SPWg6Qi4AR+vHl69YDoEAIDFOaO6YDqCxbtN9f3TEbvUnukAFm3fdAAAwE4yAAAA3+yy6qXTEQAAADvg0dVZ1XuqZ+bzQgAAAOD6/ZPpALgBZ1bfW31oOgQAgEX6ap4P5cA4iL7zbpoLDLhu7201+AcAsGt4oBcArt3e6QAAAIAd9NDqJdUnqp+qbjybAwAAACzQTap/MB0B1+N3q+OrL02HAACwaG6Q5kCcOB2wCz2x1QgAXBt/7wYAdh0DAABw7d5anTMdAQAAsMPuXf1aqyGAhw23AAAAAMvyzOpW0xFwLa5oNWr5o9Vlwy0AACzfm6rPTkewePeoHjAdscucMB3AYl1RnTodAQCw0wwAAMC121/tnY4AAAAYctfq89MRAAAAwKL8o+kAuBZfrfZUz50OAQBgbVxRvWg6grXgQPrOObJ6+nQEi/XG6q+mIwAAdpoBAAC4bi9sNQQAAACw23yw+tR0BAAAALAYt68eNx0B13BhdXz16ukQAADWjguiOBB7pgN2kUdWt5uOYLH2TQcAAEwwAAAA1+3PqndNRwAAAAw4YzoAAAAAWJR/WB01HQFXc171hOpN0yEAAKylP67+ZDqCxXtIddfpiF3C2ALX5aI8wwIA7FIGAADg+p00HQAAADDg5dMBAAAAwKL8w+kAuJpzq8dm0B8AgMOzdzqAxTuiOn46Ypfw3zPX5YzqS9MRAAATDAAAwPU7tbpkOgIAAGAHfar64HQEAAAAsBj3rh42HQFX+ZvqMdWHpkMAAFh7J1dXTkeweG6m3373r75tOoLFOnk6AABgigEAALh+X6xePR0BAACwg06bDgAAAAAW5Vmtbj2EaV+unlJ9fDoEAICN8Jnq7OkIFu9x1S2nIzbcidMBLNYXqzOnIwAAphgAAIAbdtJ0AAAAwA56yXQAAAAAsCjPmA6A6qLq+Or90yEAAGyUvdMBLN7R1ZOnIzbcnukAFuuU6tLpCACAKQYAAOCGvab6/HQEAADADvjz6n3TEQAAAMBi3LV66HQEu95l1TOrt06HAACwcU6vvjodweI5oL597pTPHbhuJ08HAABMMgAAADfs0lYLggAAAJvuJdX+6QgAAABgMU6sjpiOYNf7seq10xEA1+Ho6QAADsuXq1dMR7B4T6mOmY7YUMfncweu3TnVO6cjAAAmGQAAgANz0nQAAADADnjxdAAAAACwKM+cDmDX++Xqv01HAFyPG08HAHDY9k4HsHi3qB4zHbGhTpwOYLH25QILAGCXMwAAAAfmj6qPTUcAAABso09X752OAAAAABbjVtUjpyPY1c6sfnY6AuAGHDsdAMBhO7M6dzqCxdszHbCBblY9djqCxTplOgAAYJoBAAA4cFZeAQCATXZq1tMBAACAr3tiddR0BLvWx6p/UF0xHQJwAwwAAKy/y6sXT0eweCdWR0xHbJinVjeejmCR3l99eDoCAGCaAQAAOHAnVVdORwAAAGyT06cDAAAAgEV58nQAu9ZXWh2uOX86BOAAHDMdAMCWOGk6gMW7c/U90xEb5oTpABZr33QAAMASGAAAgAP3l9VbpiMAAAC2waerd09HAAAAAItxRPWk6Qh2rZ+sPj4dAXCA3FoLsBneU31sOoLF2zMdsEGOrp46HcEiXVmdOh0BALAEBgAA4OD8wXQAAADANjit2j8dAQAAACzGg6s7TkewK51a/f50BMBBOGY6AIAt48ZpbogBgK3z6OpW0xEs0purz05HAAAsgQEAADg4p1Vfmo4AAADYYnunAwAAAIBFeeJ0ALvSOdWPTUcAHKRjpwMA2DL7MprO9XtAdc/piA1hTIHrYowFAOAqBgAA4OB8tTp5OgIAAGAL/Y/qA9MRAAAAwKI8bjqAXefK6h9lkB9YPzeZDgBgy5xTvWM6gsVzcH1rnDAdwCJdVJ0+HQEAsBQGAADg4P32dAAAAMAWOmk6AAAAAFiUY6pHTkew6zy/evt0BMBBukl10+kIALaUm6e5IQYADt+Dq7tPR7BIr6y+PB0BALAUBgAA4OB9qHrfdAQAAMAWuLJ60XQEAAAAsCgPr46bjmBX+avqX09HAByC200HALDlTq0umY5g0R5V3XY6Ys0ZUeC6GGEBALgaAwAAcGh+dzoAAABgC7y5+vR0BAAAALAoj5sOYNf559X50xEAh8AAAMDmOa96zXQEi3Zk9dTpiDVnAIBrc1515nQEAMCSGAAAgENzcnXhdAQAAMBh2jsdAAAAACzOY6YD2FVeUb10OgLgEN1mOgCAbeFnqNwQB9gP3d2rB05HsEgvri6ZjgAAWBIDAABwaC6oTp2OAAAAOAwXVS+bjgAAAAAW5ajq70xHsGtcVj17OgLgMNx+OgCAbfGq6gvTESzak6obT0esqT3VEdMRLJLxFQCAazAAAACH7nenAwAAAA7Dy6svTUcAAAAAi/LA6mbTEewaz6/+dDoC4DDcbjoAgG1xaXX6dASLdlz1g9MRa2rPdACL9JfVO6YjAACWxgAAABy6d1cfnI4AAAA4RCdNBwAAAACL84jpAHaNC6v/NB0BcJhuOx0AwLZxEzU3xEH2g3fL6lHTESzSvmr/dAQAwNIYAACAw/OC6QAAAIBDcG71+ukIAAAAYHEePh3ArvFLrT6jAlhnt5kOAGDbvL06ZzqCRTs+53EO1tOqo6cjWKR90wEAAEvkGw4AODx7q4umIwAAAA7SKdXl0xEAAADA4hgAYCd8ofrV6QiALXDX6QAAts3+HEjl+t2h+t7piDWzZzqARfpg9eHpCACAJTIAAACH5/zqtOkIAACAg3TSdAAAAACwOLeq7jUdwa7wvOrC6QiALXCf6QAAttULpwNYPAfaD9yx1ZOnI1gkYysAANfBAAAAHL7fnQ4AAAA4CB+t3jcdAQAAACzOQ6ojpiPYeF9tNQAAsO5uVN19OgKAbfWn1XumI1g0AwAH7geqb5mOYHGurE6ZjgAAWCoDAABw+N5efWQ6AgAA4ACdNB0AAAAALNJDpgPYFX6n+vx0BMAWuGurm2wB2Gx7pwNYtO+o7jsdsSaMJXBtzq4+Mx0BALBUBgAAYGv83nQAAADAAbiyOnk6AgAAAFikB00HsPEuq35lOgJgi9xrOgCAHXFKdfl0BIvmYPsNO6I6fjqCRfL8CgDA9TAAAABb44XVxdMRAAAAN+AN1V9MRwAAAACL9NDpADbeK6pPT0cAbJF7TwcAsCP+pjprOoJFMwBwwx5W3Wk6gsW5uDp9OgIAYMkMAADA1vhC9dLpCAAAgBvwgukAAAAAYJGOy03GbD+fTQGbxAAAwO5x0nQAi/bw6lunIxbuhOkAFunV1fnTEQAAS2YAAAC2zn+dDgAAALgeX6jOmI4AAAAAFul+eY6I7fWZ3JwKbBYDAAC7xxnVl6YjWKwbVU+fjli4E6cDWKS90wEAAEvnB3cAsHXeVb13OgIAAOA6vLC6ZDoCAAAAWKT7Twew8V5QXTEdAbCF7jsdAMCOuah62XQEi7ZnOmDB7lN953QEi3Ne9drpCACApTMAAABb63nTAQAAANfhBdMBAAAAwGJ5GJ/tdGX1+9MRAFvo2Op+0xEA7Cg3VXN9Hl8dNx2xUMYRuDYvyQUWAAA3yAAAAGytU6pzpyMAAACu4Z3Vh6cjAAAAgMX6rukANtq7qr+cjgDYQt9dHT0dAcCOenP1mekIFusm1ROnIxbqxOkAFmnfdAAAwDowAAAAW+uS3KoJAAAsjxvWAAAAgOvzHdMBbLSXTgcAbLEHTQcAsOOurF40HcGiuen+m92uesR0BIvz6ept0xEAAOvAAAAAbL3fqC6bjgAAALjKhdWLpyMAAACAxTqqutt0BBvtZdMBAFvse6YDABjxB9MBLNrTW31/zdcdXx05HcHinNxqVAUAgBtgAAAAtt7nqjOmIwAAAK5ySnXBdAQAAACwWHfJIQW2zweqT01HAGyxB00HADDiw9UfT0ewWLepHjkdsTDHTwewSPumAwAA1oUBAADYHs+bDgAAALjK86cDAAAAgEW7x3QAG+1l0wEAW+zI6gHTEQCM2TsdwKLtmQ5YkJtUT5yOYHH+5KoXAAAHwAAAAGyPt1Qfmo4AAAB2vfdU752OAAAAABbtntMBbLSzpgMAtti3V8dNRwAw5uTqiukIFusZ0wEL8sTqptMRLI4RFQCAg2AAAAC2z/OmAwAAgF3vt6cDAAAAgMW7+3QAG+uC6n3TEQBb7KHTAQCM+lz15ukIFuse1XdPRyzECdMBLM6V1YumIwAA1okBAADYPnurL0xHAAAAu9b51SnTEQAAAMDi3XM6gI311ury6QiALfaY6QAAxu2bDmDR9kwHLMCR1fHTESzOW6tPT0cAAKwTAwAAsH0uqv7bdAQAALBr/ffqq9MRAAAAwOLdYzqAjeVmVGATPXY6AIBxL6m+Mh3BYhkAqO+rbjcdweIYTwEAOEgGAABge/1mdcV0BAAAsOvsr54/HQEAAACshXtMB7Cxzp4OANhid63uPR0BwLgLq1dMR7BYD6nuPB0x7ITpABbnkur06QgAgHVjAAAAttc51WumIwAAgF3n7Opj0xEAAADA4h2dgwlsj4urD01HAGyxx00HALAYe6cDWKwjcgB+t//n55u9pjpvOgIAYN0YAACA7fdfpwMAAIBd57emAwAAAIC1cLfqyOkINtKHqsumIwC22GOmAwBYjLOqc6cjWKw90wGDvrP69ukIFmffdAAAwDoyAAAA2+8N1UenIwAAgF3jr6uXT0cAAAAAa+Fu0wFsrA9OBwBsg8dOBwCwGJdXp05HsFiPq24+HTFkN48fcO3Or149HQEAsI4MAADA9ttf/ep0BAAAsGv8Xm5XAwAAAA7M7aYD2Fjvnw4A2GJ3q+41HQHAopw0HcBiHVM9ZTpiyAnTASzO6dXF0xEAAOvIAAAA7IyTqnOnIwAAgI13WfX86QgAAABgbdxmOoCN9YHpAIAt9vjpAAAW573Vx6YjWKw90wED7lg9bDqCxdk3HQAAsK4MAADAzri4et50BAAAsPFeWn1mOgIAAABYGwYA2A77qw9PRwBsMbfZAnBt9k4HsFhPqY6ejthhx+eMEt/oM9VbpiMAANaVL64BYOf8ZnXhdAQAALDRfm06AAAAAFgrt50OYCOdW31lOgJgC920esJ0BACLtLfVABZc0y2rx05H7LATpwNYnBdVV05HAACsKwMAALBzvlj9t+kIAABgY72vetd0BAAAALBWbj0dwEb65HQAwBZ7YqsRAAC4pr+o3j4dwWLtmQ7YQTerHjcdweLsmw4AAFhnBgAAYGf9anX5dAQAALCRfmU6AAAAAFg7t5kOYCN9ajoAYIvtpsN7ABy8vdMBLNaJ1RHTETvkKdWNpyNYlI9UfzwdAQCwzgwAAMDOOqd66XQEAACwcf6qesl0BAAAALB2DACwHQwAAJvkyOpp0xEALNpp1SXTESzSnasHT0fsEINJXNNJ0wEAAOvOAAAA7Lxfng4AAAA2zm9Vl05HAAAAAGvn1tMBbKQ/nw4A2EKPqG43HQHAop1XvXo6gsXaDQfjj6qeMh3BouyvXjQdAQCw7gwAAMDO+6PqrdMRAADAxrik+p3pCAAAAGAt3XY6gI107nQAwBY6cToAgLWwdzqAxdoNAwCPycAg3+jt1V9MRwAArDsDAAAw4znTAQAAwMZ4UR6qBgAAAA7ekdUtpiPYSJ+fDgDYIkdUf3c6AoC18OrqC9MRLNIDq3tOR2yz3TBywMHZNx0AALAJDAAAwIxXVR+ZjgAAADbCb0wHAAAAAGvpVnl2iO3xt9MBAFvkcW3+gT0Atsal1UumI1isE6YDttnTpwNYrN+QCAAAIABJREFUFH8/BADYIn6IBwAz9le/Mh0BAACsvbdV752OAAAAANbSzaYD2FhuPgU2xT+aDgBgrZw0HcBi7ZkO2EYPymAS3+i1+VwAAGBLGAAAgDl7q7+ajgAAANbac6YDAAAAgLV1zHQAG+mS6oLpCIAtcFz1zOkIANbKO6tPTUewSI+ubjsdsU1OnA5gcfZNBwAAbAoDAAAw55LqedMRAADA2vp49arpCAAAAGBtGQBgO5w/HQCwRf5edbPpCADWyv4cfOXaHVk9ZTpimxw/HcCifDnPsQAAbBkDAAAw67eqC6cjAACAtfSc6srpCAAAAGBtHTsdwEa6ZDoAYIv84+kAANbSSa2GAOCa9kwHbIO7VQ+ejmBRTq8umo4AANgUBgAAYNZ51e9PRwAAAGvn3GrvdAQAAACw1o6eDmAjXTodALAF7l49ZjoCgLX0p9V7piNYpCdVN56O2GInVEdMR7Ao+6YDAAA2iQEAAJj3nDwEAQAAHJz/Wl08HQEAAACstWOmA9hIfvYNbIJ/nOdrATh0hty5NjerfnA6YovtmQ5gUT5XnT0dAQCwSXxACQDzPl399+kIAABgbXyl+u3pCAAAAGDtHTsdwEa6ZDoA4DAdXf3odAQAa+2U6rLpCBbphOmALXTL6tHTESzKi6orpiMAADaJAQAAWIZfrC6fjgAAANbC71VfmI4AAAAA1t4x0wFspEunAwAO07OqO09HALDW/rY6azqCRdrT5pzheVo+V+Ab7ZsOAADYNJvyzQMArLtPVSdPRwAAAIt3RfXc6QgAAABgI3hQHwC+2U9OBwCwEfZOB7BI31o9bDpii+yZDmBRPlZ9YDoCAGDTGAAAgOX4+VaHeQAAAK7Li1sNiAEAAAAcLgMAbIdjpwMADsP3tTmH8gCY9fLqS9MRLNImHJw/tnrSdASLYvQEAGAbGAAAgOX4s+ol0xEAAMCiPWc6AAAAANgYBgDYDgYAgHX2U9MBAGyMi6uXTkewSJswAPAD1c2nI1iM/dXJ0xEAAJvIAAAALMsvtPogBAAA4JreVL1/OgIAAADYGAYA2A4GAIB1dZfqGdMRAGwUN2Jzbe5Xfft0xGE6YTqARXlndc50BADAJjIAAADL8ifVy6cjAACARfql6QAAAABgo1wxHcBGMgAArKv/qzp6OgKAjXJ29ZnpCBZpz3TAYTiiOn46gkXZNx0AALCpDAAAwPL8QrV/OgIAAFiU91ZnTkcAAAAAG+XS6QA20o2nAwAOwa1aDQAAwFa6sjp5OoJFWucBgIdWd56OYDEuq06bjgAA2FQGAABged5XvW46AgAAWJRfmA4AAAAANs5l0wFspFtWR05HABykn65uMR0BwEb6g+kAFukR1R2mIw7RidMBLMrrqs9PRwAAbCoDAACwTP9xOgAAAFiMj1SvmI4AAAAANs6l0wFspCNb3aQNsC5uWf3EdAQAG+sj1R9PR7A4N6qeNh1xiPZMB7Ao+6YDAAA2mQEAAFimd1Vvmo4AAAAW4eerK6cjAAAAgI1jAIDtcrvpAICD8OxWIwAAsF32TgewSOt4kP7e1f2nI1iMC6pXTkcAAGwyAwAAsFz/aToAAAAY98nqtOkIAAAAYCMZAGC73H46AOAA3ab6iekIADbeyf8/e3cedvld1/f/GUIQCiIVEKy44FJLrdbW+mvFrSq2LkBSVBQR64oiCqKyilAFBSyiVNAitYKZycISVllkUSAICpFNQUAgrBKWsIUQssz8/jhDITBJZrnv+/095zwe13WuO1cyc87zD4Y5932+n9e3umw6gsW5RXXt6YijdMp0AItyVnXhdAQAwCYzAAAAy/UX1dnTEQAAwKjfzMUgAAAAwO74+HQAG+uG0wEAR+iXq+tORwCw8d5VPX86gsW5VvWd0xFH6eTpABZl/3QAAMCmMwAAAMv2m9MBAADAmHOrfdMRAAAAwMa6eDqAjfUF0wEAR+AG1c9PRwCwNXzuy+Gs04H6G1Q3n45gMf4pwyYAALvOAAAALNuzqpdORwAAACN+u7pkOgIAAADYWAYA2C1fPh0AcATuW332dAQAW+Os6qPTESzOLasTpyOO0Dq1svvOrC6bjgAA2HQGAABg+X5tOgAAANhz764eMx0BAAAAbDQDAOyWL5sOALgK/6r6uekIALbKBdVTpiNYnBtU3zgdcYROmQ5gUfZNBwAAbAMDAACwfM+t/mI6AgAA2FO/XX1sOgIAAADYaAYA2C1fPh0AcBUeVp00HQHA1nFglsM5eTrgCFyrusV0BIvxxuqc6QgAgG1gAAAA1sN9pwMAAIA9897qj6YjAAAAgI338ekANtaXVFefjgC4At996AEAe+051XnTESzOOgwA3KK69nQEi3HqdAAAwLYwAAAA6+Gvqj+bjgAAAPbE71YfnY4AAAAANt4HpwPYWCdVXzodAXAYJ1W/Mx0BwNa6tDp9OoLF+bLq30xHXIV1GClgbxysTpuOAADYFgYAAGB93Kc6MB0BAADsqvdXj5iOAAAAALbCR6qLpyPYWP9+OgDgMH6uutl0BABbbf90AIu05AP2V6tuOR3BYry0etN0BADAtjAAAADr49XVWdMRAADArnpIq4vvAQAAAPbC+6cD2FhfNx0A8GmuX91vOgKArffy6nXTESzOkgcAvqG60XQEi2HEBABgDxkAAID1ct/q0ukIAABgV7y7euR0BAAAALBVDACwWwwAAEvzO9XnTkcAQA7Q8pn+Q3WT6YgrcMp0AItxafWE6QgAgG1iAAAA1svrq9OnIwAAgF3xoOrC6QgAAABgqxgAYLd8Xa5NA5bj26sfnY4AgEP2VQemI1iUE6pbTkdcgVtPB7AYz67Om44AANgmPmQBgPXz69Ul0xEAAMCOekf1R9MRAAAAwNYxAMBuuW71FdMRANW1qz9udbAOAJbgrdXZ0xEszsnTAYdxs+pfTkewGPunAwAAto0BAABYP2+q/mQ6AgAA2FG/WV00HQEAAABsHQMA7KZvnQ4AqH6r+pLpCAD4NPumA1icb281pLYkp0wHsBgfrZ42HQEAsG0MAADAevr16mPTEQAAwI54a/V/pyMAAACArWQAgN30HdMBwNb7j9WdpyMA4DAel2tAubxrVN81HfFpTp4OYDHOqi6YjgAA2DYGAABgPb2retR0BAAAsCN+vbp4OgIAAADYSgYA2E3fkevTgDnXqP64OnE6BAAO40PVM6YjWJwlHbi/UfX10xEsxv7pAACAbeQDFgBYXw/KmiIAAKy7N1anTkcAAAAAW8sAALvp+tXXTEcAW+t+1VdNRwDAlfA5MZ/ue6qTpiMOuXXOG7FyXvW86QgAgG3kDTkArK/3VP9rOgIAADguv15dOh0BAAAAbC0DAOy2W0wHAFvpW6p7TUcAwFV4Zr4n4/KuV33rdMQhJ08HsBhn5roWAIARBgAAYL09tDp/OgIAADgmr63OmI4AAAAAttp7pwPYeA6NAHvtc6t91YnTIQBwFS6uHjcdweIs4Xuo61TfMR3BYuyfDgAA2FYGAABgvX2geuB0BAAAcEzuX102HQEAAABstbdOB7Dxbl79i+kIYKv8QfWF0xEAcIT2TQewOP+tOmG44buqaw43sAz/WL1sOgIAYFsZAACA9ffI6s3TEQAAwFF5ZfXE6QgAAABg6727umg6go12tVYHWAD2ws9WPzgdAQBH4SXVm6YjWJQvqP7dcMOth1+f5dhXHZyOAADYVgYAAGD9XVzdezoCAAA4KvfKh6QAAADAvAPV26Yj2HjfPx0AbIV/Xf3OdAQAHKWD1f7pCBZn8gD+1avvGXx9luX06QAAgG1mAAAANsPjq5dORwAAAEfkudWzpyMAAAAADjl3OoCN983VjaYjgI12zeqM6p9NhwDAMdg3HcDinDz42t9cXX/w9VmOv67eMB0BALDNDAAAwGY4WN0tdxAFAIClO1jdazoCAAAA4FOcOx3Axjux+tHpCGCjPaL66ukIADhGb2x10BY+4Wurmw699ilDr8vy7J8OAADYdgYAAGBzvLR68nQEAABwpfZX50xHAAAAAHyKt04HsBV+pjphOgLYSL9U/eR0BAAcp33TASzOrbbsdVmWS6vHTUcAAGw7AwAAsFnuXl08HQEAABzWxdX9pyMAAAAAPs250wFshS+rvnU6Atg431E9ZDoCAHbA6bn2k8s7eeA1/21104HXZXmeU503HQEAsO0MAADAZnlT9UfTEQAAwGH9fvXm6QgAAACAT/OW6QC2xk9PBwAb5UuqM6qrD3cAwE54f/Xn0xEsyrdU19/j1zxlj1+P5do/HQAAgAEAANhEv1F9aDoCAAC4nA9WvzUdAQAAAHAY504HsDVuU33edASwEa5TPa26wXQIAOygU6cDWJSrV9+9x695qz1+PZbpo9VTpiMAADAAAACb6L3Vg6cjAACAy/mt6vzpCAAAAIDDeHd10XQEW+Ga1V2mI4C1d0L1f6t/Mx0CADvsqa2G5eETTt7D1/qC6t/v4euxXE+uLpiOAADAAAAAbKqHV2+fjgAAAKrVe/Pfn44AAAAAuAIHq3OnI9gad64+ezoCWGu/Vf3AdAQA7IKLqrOmI1iU/1p91h691imthpbgtOkAAABWDAAAwGb6WHXf6QgAAKBavTd3Fz0AAABgyV47HcDWuF71s9MRwNq6U3Wv6QgA2EX7pgNYlM+uvmOPXuuUPXodlu291XOmIwAAWDEAAACba191znQEAABsuVfnIg0AAABg+V4zHcBW+aXqmtMRwNq5bfWI6QgA2GUvqN4+HcGinLwHr/E51bfsweuwfGdUl0xHAACwYgAAADbXgeqe0xEAALDl7tnqvTkAAADAkhkAYC/duPrp6QhgrXxb9ae55hWAzXegOm06gkW5Vbv/Huh7qmvs8muwHvz/DwDAgvhhKABstudVT56OAACALfWX1bOmIwAAAACOwKunA9g696uuOx0BrIWvrs6qPms6BAD2yJ9OB7Aon199/S6/xim7/PyshzdVfz0dAQDAJxkAAIDNd9fqwukIAADYMpdVvzgdAQAAAHCE3lRdMB3BVrlBdffpCGDxvrB6RnW96RAA2EOvrV45HcGinLyLz32N6rt28flZH/urg9MRAAB8kgEAANh8b6seOh0BAABb5tHVq6YjAAAAAI7QgVaHTGAv3a36F9MRwGLdpHr+oa8AsG1OnQ5gUXZzAODbquvu4vOzPk6bDgAA4PIMAADAdnhI9dbpCAAA2BIfqH5tOgIAAADgKL16OoCtc+3qN6YjgEW6SfUX1ZdPhwDAkNOry6YjWIx/XX3FLj33bo4LsD5eXr1+OgIAgMszAAAA2+HC6lemIwAAYEv8j+p90xEAAAAAR+k10wFspR+vvnE6AlgUh/8BoP6pet50BIuyGwf1T6hutQvPy/rZNx0AAMBnMgAAANvjCdWfT0cAAMCGe131h9MRAAAAAMfg1dMBbKWrVY+qTpoOARbB4X8A+CQHcvlUuzEA8B9avf9iu11WnTkdAQDAZzIAAADb5W7VJdMRAACwwbznBgAAANaVAQCmfFX1K9MRwDiH/wHg8s6qLpiOYDFuXt14h59zN0YFWD/Prd49HQEAwGcyAAAA2+W11R9MRwAAwIZ6avXs6QgAAACAY3R+9c7pCLbWr1VfNh0BjHH4HwA+00erp0xHsBhXq75nh5/z1jv8fKyn06YDAAA4PAMAALB97pelRgAA2GkX5y5lAAAAwPr76+kAtta1qsdWJ06HAHvuZtWLc/gfAA5n33QAi3LyDj7Xl1VfvYPPx3q6sHrSdAQAAIdnAAAAts+Hq/tORwAAwIZ5WPXG6QgAAACA4/Ti6QC22jdW95mOAPbU11cvqL5oOgQAFuo5ueETn/Sd1bV36Ll2ckyA9fWU6iPTEQAAHJ4BAADYTn+Su3cAAMBOOa960HQEAAAAwA4wAMC0+1XfMB0B7IlbVM+rbjgdAgALdll1xnQEi3GtVu+hdoIBAKr2TwcAAHDFDAAAwHY6UN350FcAAOD43KP68HQEAAAAwA742+rC6Qi22tWrfdV1p0OAXXWH6hnVZ0+HAMAa2DcdwKLsxMH961c334HnYb2dXz1nOgIAgCtmAAAAttc51WOnIwAAYM39TXXqdAQAAADADrmk1c87YNKXVo+uTpgOAXbFPVpdr3LSdAgArIlzqtdOR7AYt6xO3IHnuPoOtLDezqguno4AAOCKGQAAgO127+pD0xEAALCmDlZ3PfQVAAAAYFOcPR0A1W2re05HADvqpOoPqodk4AMAjta+6QAW44bVzY/zOU7eiRDW3v7pAAAArpwBAADYbudV95mOAACANXVq9dLpCAAAAIAd9uLpADjkgdV3T0cAO+IG1XOqO02HAMCa2l8dmI5gMY7nAP+1qv+yUyGsrTdXL5mOAADgyhkAAAD+d/VX0xEAALBmPlDdfToCAAAAYBf8VXXZdARUJ1ZnVDebDgGOy9dUf1N963QIAKyxt1Uvmo5gMU45jt97i+raOxXC2jqtOjgdAQDAlTMAAAAcaLWwfsl0CAAArJF7Ve+ZjgAAAADYBR+u/m46Ag65bvWkVncPB9bP97calrnpdAgAbIB90wEsxpdVX3WMv/fWOxnC2jptOgAAgKtmAAAAqHp19bvTEQAAsCb+pvo/0xEAAAAAu+js6QD4FF9ZPaO6znQIcMROqO5ZnZk7zALATnlc9bHpCBbj5GP4PVerbrnTIaydc6rXTUcAAHDVDAAAAJ/w69VbpiMAAGDhLql+qjowHQIAAACwi148HQCf5utbHXg6aToEuEqfWz2lenCuUQWAnfTh6unTESzGsQwA/Kfqxjsdwto5bToAAIAj44erAMAnXFjdaToCAAAW7ner10xHAAAAAOyy52QAkeX57uoxueYNluz/q15e3Wo6BAA21P7pABbj66ubHOXvOWU3QlgrB6ozpyMAADgyPgwBAD7Vs/ODHQAAuCJvqx4wHQEAAACwB95XvWw6Ag7jh6v/neveYGlOqO5anV3ddLgFADbZM1p9vwYnVLc8yt9z690IYa08r3rndAQAAEfGByEAwKe7a/WB6QgAAFigX6gumI4AAAAA2CPPmA6AK/DT1R/l2jdYis9r9XfG71UnDbcAwKa7pHrcdASLcfJR/NqbVV+5WyGsjf3TAQAAHDkfggAAn+686t7TEQAAsDBnVU+djgAAAADYQ382HQBX4ierU6sTp0Ngy3179crqu6ZDAGCL7JsOYDG+rbruEf7aoxkLYDNdVD15OgIAgCNnAAAAOJxHVy+ejgAAgIX4SHXX6QgAAACAPfa31bunI+BK/HD1J9XVp0NgC12relj1nOrzh1sAYNu8tPrH6QgW4bOq/3qEv/bWuxnCWnhq9aHpCAAAjpwBAADgcA5UP1NdPB0CAAALcP/qHdMRAAAAAHvsYPXM6Qi4CneontGR3/USOH7fUL2iuluuQQWACQer/dMRLMbJR/BrblT9x90OYfH8/wYAwJrxw1cA4Ir8ffXQ6QgAABj26uoR0xEAAAAAQ54xHQBH4DurF1VfMB0CG+5a1YNb/Xn7yuEWANh2p7YaAoDvra5xFb/m5Jwd2nbnV8+ajgAA4Oh4Ew8AXJkHVm+ajgAAgCEHqp+tLpkOAQAAABjy5/nZCOvha6qzq381HQIb6ubVK6p7VicOtwAAq+s6/3o6gkW4XvXNV/FrTt6LEBbtcdXF0xEAABwdAwAAwJX5WHXHLMUCALCd/nf1kukIAAAAgEEfbnWoGtbBl1Qvrr5zuAM2ybWr36teVH3lcAsAcHmnTgewGFd2wP861bfvVQiLtX86AACAo2cAAAC4Ks9vdfAJAAC2ybuq+0xHAAAAACzAM6cD4Ch8bqv/zd6rOmG4Bdbdbat/qO6aa00BYInc0ZtPuHVX/P3Pf6muuYctLM+5rcbyAABYM34oCwAciXtUb5mOAACAPXSn6kPTEQAAAAAL8JTpADhKJ1YPqh5fffZwC6yjm1XPrc6sbjLcAgBcsfdVz5qOYBG+uPraK/hvJ+9lCIt0enVwOgIAgKNnAAAAOBIXVD+ZHwABALAd9ldPnY4AAAAAWIg3VOdMR8Ax+L7qr6t/PR0Ca+I61UOqV1XfMdwCAByZfdMBLMbhDvpfvfrevQ5hcfZPBwAAcGwMAAAAR+ovqj+cjgAAgF323upu0xEAAAAAC3P6dAAco5u1GrC4Z66Vgytzq+rvq3tUJw23AABH7mnVB6cjWITDDQB8c3X9vQ5hUV7R6n0+AABryIcaAMDRuGf15ukIAADYRT/TagQAAAAAgE86szowHQHH6JrVg6unVzceboGl+cbqRdVTqy8abgEAjt5F1ROnI1iEr62++NP+3a0nQliU06YDAAA4dgYAAICjcUH1k7m4BwCAzXRG9aTpCAAAAIAFekf1wukIOE7fXb26w98ZE7bNV1VPqc6uvmm4BQA4PvumA1iMTz/w73uf7XagOn06AgCAY2cAAAA4Wn9Z/cF0BAAA7LD3VL8wHQEAAACwYC4aZxPcsHpyqzHQGw+3wIQvqv6kelXuCAsAm+KF1dumI1iEX64ed+jxlOqmszkM+8vqndMRAAAcOwMAAMCxuGf1j9MRAACwg+5cvW86AgAAAGDBHl9dPB0BO+QHq9dVd801dGyHz60eXP1D9WPViaM1AMBOOlCdNh3BInxx9QOHHsae2D8dAADA8fHhBQBwLC6sfqrVD44BAGDdPb56wnQEAAAAwMJ9oHr2dATsoOtVv1c9r/rK4RbYLTdqdfD/La1u9nCt2RwAYJecOh0ALMpF1ROnIwAAOD4GAACAY/WC6pHTEQAAcJzeV/3CdAQAAADAmjh9OgB2wX+uXlM9qrrhbArsmC+qHl69udXB/+vO5gAAu+y11SumI4DFeHr1oekIAACOjwEAAOB43Kt643QEAAAch5+vzpuOAAAAAFgTT6kumI6AXXBSdcfq9a0OS19jNgeO2Ze2Ovj/huou1T+bzQEA9tC+6QBgMfZPBwAAcPwMAAAAx+PC6qeqA9MhAABwDJ5UnTkdAQAAALBGLqyePB0Bu+ifVw+uXlWdXJ0wmwNH7N9XZ7S6icNdqs+azQEABpxWXTYdAYz7QPXM6QgAAI6fAQAA4Hi9sNV6PAAArJP3Vz83HQEAAACwhh41HQB74F+1Grt4WfW9wy1wRa5R3a56cXVO9YO5JhQAttm7q+dORwDjHl99fDoCAIDj54e9AMBOuHf1mukIAAA4CndpdQEEAAAAAEfn7OoV0xGwR76uenr1yuoHhlvgE25U3bP6x1Z3+r35bA4AsCD7pgOAcfunAwAA2BkGAACAnfDx6serS6ZDAADgCDyl1UWRAAAAABybR08HwB77t9XjqpdW31+dOJvDlrp5dXr1turB1RfO5gAAC/Sk6oLpCGDM21oNNwIAsAEMAAAAO+Wc6oHTEQAAcBXeW/3MdAQAAADAmttXfXg6Agb8x+rx1Ruqu1TXmc1hC9y4+qXq1dWLqx+qrjFaBAAs2UdbjQAA2+n06sB0BAAAO8MAAACwk36r+pvpCAAAuBI/W503HQEAAACw5j7SagQAttWXVg/vk3di/+LZHDbMZ1U/UD29env1O9VXjxYBAOvE92qwvfZPBwAAsHMMAAAAO+nS6kerC6dDAADgMP60Oms6AgAAAGBDPLI6OB0Bw/55dc/qzdVzWh3aPmm0iHX2da2GJd5ePa763urqo0UAwDp6bvXO6Qhgz726es10BAAAO8cAAACw015f3Ws6AgAAPs3bq7tORwAAAABskNdWL5qOgIW4WnWLVoe231o9sLrpaBHr4ITq66sHVW+oXl7dpbrhZBQAsPYOVGdORwB7bv90AAAAO8sAAACwGx5RPWs6AgAADjlY3bH64HQIAAAAwIb5w+kAWKDPr361enOrA913rW48WsSSXK36purBrQ79/02rmyx8xWQUALBx9k0HAHvqQHX6dAQAADvLAAAAsBscsAIAYEkemYEqAAAAgN1wVnXedAQs2NdVv1e9vdXPKP979TmjRUy4ZvVfq0dV/1S9qLpn9eWTUQDARntF9ffTEcCeeWGr7zsBANggBgAAgN3y9uou0xEAAGy9N7S6kBIAAACAnXdx9UfTEbAGrt7qAPhjqvdUz67uXH3xYBO766uqu1XPrN7fagDijtXnTUaxq/5ndcKnPC6dzQGA9k0HAHtm/3QAAAA774TpAABg4z2xus10BAAAW+nS6puqv54OAeCIfGN19nQEG+8F1X+ejgAA2DA3qM6trj3cAevqldXTqqdX51SXzeZwjK5f3aL6L4ceN5nNYY+9v/ry6oOf8u8uaTX+AUfjodXdpyOAjfGFrb5Xc9NI2Gwfr27c5d+LAgCwAfxwEQDYbT/b6gL+G02HAACwdR6cw/8AAAAAu+191aOrX5wOgTX1tYcev9bqwMZfVs+rnl+9di6Lq3CTVtdC3PzQ13+Xw3Xb7IE5cAXA8ry91Sjut02HALvqz/JeFABgIxkAAAB223urn66eOh0CAMBWeUX1G9MRAAAAAFviodWdqs+aDoE1d73qlEOPqn9qNQTwV4cef1ddOpO21U6svqbLH/j/otEiluRN1R9MRwDAFdiXAQDYdPunAwAA2B0GAACAvfC06lHVz0yHAACwFS6qfrS6ZDoEAAAAYEu8s/rTVsPgwM75/Or2hx5VH61eXr3k0OPl1btm0jbWNap/U/3bQ4+vrb6uus5kFIt27+ri6QgAuAJPrB5ZXXM6BNgVH6yeMR0BAMDuMAAAAOyVX261JPsvp0MAANh4v9bqTlgAAAAA7J2HVD/R6k7ZwO64dvWthx6f8N7qVYcerz70eG0OJB+JG1df1eqQ/ycO/N+sOmkyirXy0uoJ0xEAcCU+VD21uu10CLArntjqJhkAAGwgAwAAwF75aPUj1YvzYTkAALvnBdXDpiMAAAAAttCbqjOrH54OgS2qcp0GAAAgAElEQVRzw+oWhx6fcEn15uoNhx5v/JSv79jrwGGfX31F9eWHeXz2YBeb4Veqg9MRAHAV9mUAADbV/ukAAAB2jwEAAGAvvaz6jeoB0yEAAGykD1X/vTowHQIAAACwpR5c3a46YToEttxJ1Vceeny6j1XnVu9sNQbwtkNf33non99bva+6bC9Cj8NJ1edVN6lufOjrjaovPPT1JtVNq+tMBbLxHtfqJhgAsHTPavX+7gbTIcCOekerm2QAALChDAAAAHvtQdV3Vd84HQIAwMb5heqt0xEAAAAAW+w11dOqW0+HAFfoWtXNDj2uzPmtDoq9r3r/oa/nVxdWH201yHphq0GBDxz6etGn/P7Lqg9fwetf89A/n1hd91P+2+cc+m+fU13vU75+4p8/8fjnrQ75GxthygXVL01HAMARuqQ6s7rzdAiwo07PDTIAADaaAQAAYK9dVt2hemWX/yAfAACOx1nVqdMRAAAAAPSbGQCATfC5hx7/cjoEFujXq3dORwDAUdiXAQDYNPumAwAA2F1Xmw4AALbSW6q7TEcAALAx3ln99HQEAAAAAFX9TfVn0xEAsEv+vnr4dAQAHKWXVm+cjgB2zN9Vr56OAABgdxkAAACmPLY6czoCAIC1d7DV4f/zp0MAAAAA+H9+pbp0OgIAdsEvVJdMRwDAMdg/HQDsGH+eAQC2gAEAAGDSz1Zvm44AAGCt/X71zOkIAAAAAC7nH6rHTEcAwA7bX/3FdAQAHKN9rQb2gfV2sDpjOgIAgN1nAAAAmPTB6ierA9MhAACspddV95qOAAAAAOCw7l99dDoCAHbIR6p7TEcAwHF4U/XS6QjguL2oOnc6AgCA3WcAAACY9tzq96YjAABYOxdXP1J9bDoEAAAAgMN6V/W70xEAsEPu3ervNgBYZ6dOBwDHbf90AAAAe8MAAACwBPepXjUdAQDAWrl/9bfTEQAAAABcqYdU756OAIDj9OLqD6cjAGAHnFl9fDoCOGYXV0+cjgAAYG8YAAAAluDj1e2rC6dDAABYCy+u/ud0BAAAAABX6YLqgdMRAHAcLqx+rDow3AEAO+H86lnTEcAxe0b1/ukIAAD2hgEAAGAp/r76lekIAAAW74JWF9pdNtwBAAAAwJF5VPW66QgAOEb3qf5xOgIAdtC+6QDgmO2fDgAAYO8YAAAAluQPq7OmIwAAWLSfz4V2AAAAAOvk0uq+0xEAcAz+qvr96QgA2GFPrz44HQEctQ+1+vMLAMCWMAAAACzNT1Vvm44AAGCRzqgeOx0BAAAAwFE7q3rhdAQAHIWLqp+oDkyHAMAOu6h6wnQEcNTOavXnFwCALWEAAABYmg9Ut68umw4BAGBR3l793HQEAAAAAMfsjtXHpyMA4Ajdr3r9dAQA7JJ90wHAUfPnFgBgyxgAAACW6OzqAdMRAAAsxqXV7VqNRQEAAACwnl5fPXQ6AgCOwNnVw6YjAGAXvbA6dzoCOGLvql4wHQEAwN4yAAAALNUDqudPRwAAsAgPqF48HQEAAADAcXtA7qYMwLJ9qLpDddl0CADsooPV6dMRwBE7Le9PAQC2jgEAAGCpDlT/vXr/dAgAAKPOrn5zOgIAAACAHfHx6i7TEQBwJe6UOyIDsB1OnQ4Ajtj+6QAAAPaeAQAAYMne0WoE4OB0CAAAIz6Yu+wAAAAAbJo/r86YjgCAw3hs7oYMwPZ4XfW30xHAVXpd9crpCAAA9p4BAABg6f6s+sPpCAAARvxc7rIDAAAAsIl+sfrAdAQAfIo3V3eZjgCAPbZvOgC4Sv6cAgBsKQMAAMA6+OXqVdMRAADsqT/OXXYAAAAANtV51X2mIwDgkEurH6k+PB0CAHtsf6u/B4FlOphrZwAAtpYBAABgHVxU3b66cDoEAIA98YZWd4EDAAAAYHP9UfWS6QgAqP5H/k4CYDu9p3rudARwhV5cvWU6AgCAGQYAAIB18fc5BAYAsA0urn64umA6BAAAAIBddaC6Y/Xx6RAAttozqwdNRwDAoH3TAcAV2j8dAADAHAMAAMA6eXT12OkIAAB21a9W50xHAAAAALAn/q66z3QEAFvrrdWPthqlAYBt9aQM9MMSXVI9YToCAIA5BgAAgHVz5+rvpyMAANgVz64eNh0BAAAAwJ76ver50xEAbJ2Lqu+r3jcdAgDDLqzOmo4APsOz8l4VAGCrGQAAANbNR6vbVB+ZDgEAYEe9u/qx3GUHAAAAYNscaHX35fOnQwDYKj9fnTMdAQALsX86APgM+6YDAACYZQAAAFhHb6juOB0BAMCOuay6fasRAAAAAAC2zzvz+R8Ae+fU6o+nIwBgQZ7b6vsyYBk+Uj19OgIAgFkGAACAdXVG9ajpCAAAdsT9qudPRwAAAAAw6om56yQAu+9V1c9MRwDAwhxodU0msAxPrC6cjgAAYJYBAABgnf1i9bfTEQAAHJdnVw+ejgAAAABgEX6+ett0BAAb6z3VKdXHpkMAYIH2TQcA/89p0wEAAMwzAAAArLOLqttU50+HAABwTN5d/ViruwkAAAAAwAer21eXTYcAsHEurm5bnTvcAQBL9crqNdMRQP9UPX86AgCAeQYAAIB199bqJ6qD0yEAAByVS6sfbDUCAAAAAACfcHb1sOkIADbOT1cvmI4AgIXbPx0AdEaGEQEAyAAAALAZnpKLgAAA1s39qxdORwAAAACwSL9avWg6AoCN8aDqT6cjAGAN7MvBY5hmiAMAgMoAAACwOe7V6m4gAAAs3/Orh0xHAAAAALBYl1S3rd41HQLA2ntSdd/pCABYE+/MkD9M+ofqnOkIAACWwQAAALApLq1uV713OgQAgCv17ur2uWsAAAAAAFfu3dX3VxdPhwCwtl5Z3aE6MB0CAGtk33QAbLH90wEAACyHAQAAYJO8o/qRHCYDAFiqS6sfbHXxNgAAAABclZdU95iOAGAtvbO6dfXR6RAAWDNPqD42HQFb6GB12nQEAADLYQAAANg0f1796nQEAACHdf/qhdMRAAAAAKyVh1ePnY4AYK18qPre6u3TIQCwhj5cPXU6ArbQS6o3T0cAALAcBgAAgE3029UZ0xEAAFzO06sHT0cAAAAAsJbuVL1iOgKAtfCx6lbVq6ZDAGCN7Z8OgC3kzx0AAJdjAAAA2EQHq5+ozpkOAQCgqjdWd6gOTIcAAAAAsJY+Vt2mev90CACLdln1I9WLpkMAYM09szpvOgK2yKXVE6YjAABYFgMAAMCm+lj1fdX7pkMAALbcBa0uzv7gdAgAAAAAa+3c6scyMgnAFfvF6qzpCADYAJdWj5+OgC3yrOo90xEAACyLAQAAYJO9tbpdq4V3AAD23sHqx6u/mw4BAAAAYCM8vfq16QgAFul+1SOmIwBgg+ybDoAtctp0AAAAy2MAAADYdM+t7jkdAQCwpX67esJ0BAAAAAAb5beqR09HALAoj6weMB0BABvmr6s3TEfAFrigesp0BAAAy2MAAADYBr9TPWY6AgBgyzyv+tXpCAAAAAA20p1ycTwAK4+p7jIdAQAbav90AGyBs6oLpyMAAFgeAwAAwLa4U/Xy6QgAgC3x1uqHqsumQwAAAADYSJdVP9zqjpQAbK/HVT9VHZgOAYAN9afVwekI2HCGNgAAOCwDAADAtrio+r7qPdMhAAAb7hPvu943HQIAAADARruwulX1j9MhAIx4YnX7jBEDwG46t3rJdARssPdUz5+OAABgmQwAAADb5G2tDqNdPB0CALDB7lSdMx0BAAAAwFZ4b3Xr6vzpEAD21JOr21WXTocAwBbYNx0AG+z0vKcFAOAKGAAAALbN2dU9piMAADbUw6vHTEcAAAAAsFVeV51SXTQdAsCe+PPqh6pLpkMAYEucWX18OgI21P7pAAAAlssAAACwjRxMAwDYeS+s7j4dAQAAAMBWelH1Y9WB4Q4Adtezq5NzCBEA9tL51TOnI2ADvaF62XQEAADLZQAAANhWP1P9xXQEAMCGeGt129xtBwAAAIA5Z1Z3rQ5OhwCwK57c6vD/RdMhALCF9k0HwAbaPx0AAMCyGQAAALbVxdV/q/5uOgQAYM19pLp1dd50CAAAAABb7xHV3aYjANhxZ7QaIv74dAgAbKmnVedPR8CGOWM6AACAZTMAAABssw+1Oqz2nukQAIA1dVl1++rV0yEAAAAAcMjDq1+ejgBgx5xa/Uh1yXQIAGyxi6snTkfABnlp9YbpCAAAls0AAACw7d5S3bK6cDoEAGAN3a3V0j8AAAAALMnDqrtPRwBw3P6g+rFWg8QAwKx90wGwQfZPBwAAsHwGAAAA6mWtPjA+MNwBALBO/k/1+9MRAAAAAHAFHlo9YDoCgGP2kOrOuZYDAJbiRa1uuAQcn0urx09HAACwfAYAAABWHl/ddzoCAGBNPKu603QEAAAAAFyF+1UPno4A4KgcrO5e3Ws6BAC4nIPVadMRsAGeU503HQEAwPIZAAAA+KQHVY+ajgAAWLjXVrdrtUgOAAAAAEt371Z3kQZg+S6ufqR66HQIAHBY+6YDYAP4cwQAwBExAAAAcHm/UD1vOgIAYKHOq763+uB0CAAAAAAchXtX/2s6AoAr9eHqe3JnYQBYsn+oXj4dAWvso9VTpiMAAFgPBgAAAC7vkuo21WumQwAAFuai6r9V5w53AAAAAMDROljdtbrXdAgAh/VP1X/ODRsAYB3snw6ANfbkViMAAABwlQwAAAB8pg9Xt251h1sAAFYXSP9U9ZLpEAAAAAA4Dg+pfr46MB0CwP/zuuobqldMhwAAR+S06tLpCFhTBjQAADhiBgAAAA7v3OqU6mPDHQAAS3D/fAgJAAAAwGZ4ZHWH6pLpEAA6u/qm6q3TIQDAEXtP9efTEbCG3lM9ZzoCAID1YQAAAOCKvbT6oazVAgDb7THVA6cjAAAAAGAHnVadXF04HQKwxR5T3aI6f7gDADh6+6YDYA2dmeuRAQA4CgYAAACu3FOrn6gOTocAAAx4RvXTeS8EAAAAwOZ5ZvVt1fumQwC2zGXVvaofrz4+3AIAHJunVB+ZjoA1s386AACA9WIAAADgqp1a/ep0BADAHntZ9YNZHwcAAABgc/1N9a3VO6ZDALbER6rbVA+ZDgEAjsuF1ZOmI2CNvKnVzyAAAOCIGQAAADgyD6oePh0BALBHXl99T3XBdAgAAAAA7LLXVt9SvXE6BGDDvan6T9VTp0MAgB1x6nQArJH91cHpCAAA1osBAACAI3e36rHTEQAAu+xd1XdV75sOAQAAAIA98pZWh1KfPx0CsKHOrm7eanQFANgMz6/eMR0Ba+K06QAAANaPAQAAgCN3sLpj9czpEACAXfKB6rurc4c7AAAAAGCvnd9qGPNR0yEAG+Rg9bvVt1fvGW4BAHbWgeqM6QhYAy+rXj8dAQDA+jEAAAD/P3t3Hm3pXRb4/ltFGhCZG0ggYRKJAQIqhBmaISgXaJFAAFsRaUWhsUWudt8bHEF7IXBbXREamb2SVMAAYUY60BCGyCSEKQMkARIykIHMc6Wq7h/vTufckPHUOee3z9mfz1rv2qdOncA3a506qb33+zw/uGmuqJ5VHTG4AwBgpV1SPb36xugQAAAAABhka/Xi6kWzjwFYvgurX6n+ID9TAWCjOnh0AKwD/pwAALAsFgAAANx0l1b/vjpydAgAwAq5asnR50aHAAAAAMAceHO1b3XW6BCAdeq46pHVoaNDAIBV9fXqm6MjYI5ty9+JAQBYJgsAAACW5+KmJQBHjQ4BANhJ26pfqz42OgQAAAAA5shnm4ZXjxkdArDOvLPapzp6dAgAsCacbg7X7RPVD0dHAACwPlkAAACwfOdVT66OHR0CALBMO6rfqd4zOgQAAAAA5tCJ1SOqD40OAVgHrqwOqH616VAFAGAxHNx08ADw47aMDgAAYP2yAAAAYOec1bQE4PuDOwAAbqod1Uuqt48OAQAAAIA5dmG1X/U3Ta+pAfDjTqgeVb1mdAgAsOZOq44YHQFz6JLqfaMjAABYvywAAADYeT+oHld9b3QIAMCNtKP6/eqNo0MAAAAAYB3YVv1h0yKAcwa3AMybg6qfr748OgQAGMYp5/DjPlBdNDoCAID1ywIAAICVcXL1+CwBAADWhwOq142OAAAAAIB15gNNQ67/MjoEYA5cUD2ven4GmwBg0b236bRz4GoWYwAAsFMsAAAAWDknV0/IEgAAYL4dUL12dAQAAAAArFMnV4+rXlltH9wCMMoXqwdnqAkAmFxQfXB0BMyRs6rDR0cAALC+WQAAALCyTmpaAvD9wR0AANfm5dVrRkcAAAAAwDp3ZfWK6snVGWNTANbUlU3vMzy2OnFwCwAwXw4eHQBz5NBq6+gIAADWNwsAAABW3knVk6pTRocAAMzsqP5L9erRIQAAAACwgXyi2qf67OgQgDVwbPWY6oAMMwEAP+7w6szRETAnDhkdAADA+mcBAADA6jix6Y3vE0aHAAALb0f1f1Z/PToEAAAAADagU6onVK+stg9uAVgN26s3Ny08+eLgFgBgfm2t/ml0BMyBE6vPj44AAGD9swAAAGD1nNR0s89xo0MAgIW1rfqt6sDRIQAAAACwgW2rXlE9qfre2BSAFXVc9ejqRdUlg1sAgPl38OgAmAPvbDqsAwAAdooFAAAAq+uU6nHV10eHAAALZ1v1H6t/GB0CAAAAAAviU9Xe1WuaTswGWK+2V2+uHlJ9YXALALB+fKn69ugIGOydowMAANgYdhkdAACwAM6snlj9c/WwwS3A2rukOnt2nVddPLsuqC6cfXzJ7PGKJf/clbPfX+rfVLde8uubVbdd8ni72e9fdd1+9vm7VrdZwX8nYP5dXj23+sDoEAAAAABYMJdUB1SHV2+t7j02B+AmO676zerzo0MAgHXp4OovR0fAIF+pjhkdAQDAxmABAADA2jin+oXqQ9W/G9wCrIwLq5OrH8yuU6qTqrNm1xlNQ/+XjAq8hltUd67uUu06+3j36p6z6x5NNyH+xKhAYMVcWD2z+sToEAAAAABYYJ+sHlS9tnpxtWlsDsANuqx6VdPPrcsHtwAA69eW6i/yHIjFtGV0AAAAG4cFAAAAa+eC6v+o3lU9fXALcOOcWx1ffWfJdXz13eq8gV3LcXnTkoJTbuDr7lzdq9qzul/1M7Nrz6YlAsB8O6N6WtNGcQAAAABgrIuql1Tvqd7W9Po7wDz6eNPPqxNGhwAA6973qiOrx4wOgTW2ren+YAAAWBEWAAAArK1Lq2dVf1+9cHALcLUrqmOqb8yur88ezxwZNchZs+vL1/j8zZpuTHxg9fNLrj3WMg64XidWT549AgAAAADz45NNr6+/tnpxTsIE5scPqz+sDhkdAgBsKAdnAQCL55PV6aMjAADYOCwAAABYe1dWv9P0RvqfDG6BRbStOrr6UvXF2eOx1daRUevAtqah4hOr9y/5/J2aFgE8rHrk7LrjmtcBX62eWp0xOgQAAAAAuFYXNZ2u/U/V66u9x+YAC2579ebq5dV5g1sAgI3n3dWB1S1Gh8Aa2jI6AACAjcUCAACAMXZUf9q07fN11eaxObChXVB9rvpM9fnqK9XFQ4s2lrOrj8+umk4t2qt6RPXo6nHVT49Jg4Xx8epZ1YWjQwAAAACAG/TppsW6v1e9orrt0BpgEX2xemnTonQAgNVwTvXRar/RIbBGLq3eNzoCAICNxQIAAICx3tB0Uu/B1S0Ht8BGcX7TsP+nZ9dRTafXszZ2VMfOrn+Yfe4e1RNn177V3cakwYb05uo/V1tHhwAAAAAAN9qV1d9W76peW/1a04JdgNV0avXypvsTdgxuAQA2voOzAIDF8cGmg4oAAGDFWAAAADDee6vTqvdXdxncAuvRturL1eGz64tNN84xP06u/t/ZVbVX9dTZ9djq5kOqYH3bVv1f1d+MDgEAAAAAlu306tert1Svqx40NgfYoC6r/rr6q+riwS0AwOL4SHVudYfRIbAGtowOAABg49k8OgAAgKo+Xz28Onp0CKwTP6reUT2nunP1yOrPqyMz/L8eHNc0tPyk6k7VM6u3Vj8cGQXryEVNf24M/wMAAADAxvCZ6iHVy6rzB7cAG8t7qvtXf5LhfwBgbV1eHTo6AtbAj6r/OToCAICNxwIAAID58f3q0U0nmAM/7vjqv1ePq3atfqN6d9OmaNavC6v3Vb9d7V49tvrbpp+JwI87penPyQdHhwAAAAAAK+rK6sDqZ5qWIG8fmwOsc1+unlA9u/re4BYAYHEdPDoA1sC7qytGRwAAsPFYAAAAMF/Or55W/f3oEJgT361eVT2o2rP6r02n4GwbGcWq2V59rvqD6t7VPtVfVSePjII58tnqodXXRocAAAAAAKvmjKYlyPtUHxvcAqw/x1X7Vw+vjhibAgDQkVlGxMa3ZXQAAAAbkwUAAADz58rqJdXvVVsHt8AIpzSdAP/w6j7VH1ffHFrEKF+p/qhpGcDjq7dW540MgoFeV+1b/XB0CAAAAACwJo6qntJ0gvcXB7cA8++U6oXVA6v3VjvG5gAAVNPfSQxHs5F9v2nRBQAArDgLAAAA5tfrqydm0I/FcE51UPX06l5NJ8B/aWQQc2V79enqt6vdmr5P3l1dMTIK1shl1W9VL81iIAAAAABYREdUj6h+ofrG2BRgDp1THVDtWb2t6cABAIB58o7RAbCKtmT5FgAAq8QCAACA+fa56qE51YON6eLqH6snV7tWz68+VG0bGcXcu7zp++Q51d2q363+JW+ksDF9r3pU9fbRIQAAAADAcJ+oHty0MPQHg1uA8c6r/qL6qeo11aVjcwAArtPx1ZdHR8AqOWR0AAAAG5cFAAAA8++U6nHVW0eHwAr5QvU7TcPbL6gOz0kULM+PqjdUj2461eQ11ZlDi2DlfKDapzpqdAgAAAAAMDe2NS0M3bP6L02vkwOL5Zzqz6p7VX9enT+0BgDgxjl4dACsgqOqY0ZHAACwcVkAAACwPlxe/Xb14uqywS2wHGdVf1PtXT2yekt1wdAiNpoTqgOqu1fPrT5Z7RhaBMtzefXSar+mm/gAAAAAAK7psuqvm07+fnl1xtgcYA2cXf1R0+D/X2bwHwBYX96VA2LYeLaMDgAAYGOzAAAAYH15U9NpwN8aHQI3wvbqE9Vzqj2qP6yOHlrEIriiOrTat+kEpNc0LaCA9eA7TUtSXpcFFgAAAADADbugenV1j+o3quPH5gCr4OzqldVPV39VXTg2BwBgWc6sDh8dAStoe9NiCwAAWDUWAAAArD9HNw0HHjQ6BK7DKdWfNd1s9gvVu5uGsmGtnVAdUN29+tXqiAxVM78Oqh5SHTU6BAAAAABYd66o3lHdv/r1LGSGjeDE6j9X96xeUZ0/tAYAYOe535GN5FPVqaMjAADY2CwAAABYny6qnl+9oLp4bApU01D1x6tnVveu/jIvcDM/Lq/eWT2hekD1hqafozAPzq6e2/Tfdd+XAAAAAMDOuLI6uHpg9Yzqi2NzgGX4fLV/tWf1P6pLxuYAAKyYD1YXjo6AFbJldAAAABufBQAAAOvbP1b7VF8fHcLCOrf622qv6her9zXdXAbz6tjqd6s9qpdVx4/NYcG9v9q7OnR0CAAAAACwoeyoPlA9onpi0xJnYH5tb3qf9THVo6r3zj4HALCRXNL09xxY7y6rDhsdAQDAxmcBAADA+ndc9bDqVRm8Zu18pfqtpiHqP6i+MzYHbrLzqwObllc8rfpY0w2RsBbOrZ5f7VedMbgFAAAAANjYPtW0xHnv6g05cRPmyUXV3ze9X/XM6sixOQAAq+7g0QGwAj7cdO8ZAACsKgsAAAA2hiuqP64eW317cAsb12XVO6qHV/tUb2/azAzr2fbqo9VTmm6u+rvqgqFFbHQfrh5YHTQ6BAAAAABYKEdXv1vtPns8emwOLLRjq5c2LVt/SXX82BwAgDXzqerU0RGwk7aMDgAAYDFYAAAAsLF8oXpw0wCrk6xZKSdVL6/uXv1G9aWxObBqvlP9ftPNVr83+zWslFOr/atfypvZAAAAAMA4F1ZvqPaunlC9u9o6tAgWw5XVe6p9qwdUr8upoQDA4tleHTI6AnbCOU2HzQAAwKqzAAAAYOO5pGmA9UnViYNbWL92VIdXz6juU726OntoEaydC6vXV3tVT60+lqUqLN+26sDqftV7B7cAAAAAACx1RPWc6l7VK6vTRsbABnVa05+ve1bPrj6Z950AgMV28OgA2Anvqa4YHQEAwGKwAAAAYOP6ZPXA6lU5tYMb7/ymQdW9qidXH2gaXoVFtKP65+opTcPbr29aDgA31r9WD6telu8dAAAAAGB+nVa9omkRwLOqD2agAXbGFdX7qqc3Df6/Igs2AACu8o3ZBevRltEBAAAsDgsAAAA2tkurP64e0LQQAK7Lt5sGVPeYPX5nbA7MnW9Xv1ftXr2oOnZsDnPu9Kbvk0dUXx3cAgAAAABwY22tDqt+udqt6XXOI3NaOdxYx1QHVHevnll9qLpyaBEAwHw6eHQALMPJ1edGRwAAsDgsAAAAWAzHV0+qfqc6d3AL82NrdWj1uGqv6sDqoqFFMP8urN5c7V09rfpItX1oEfPkkuovqvs2fZ9sG5sDAAAAALBs5za9zvmY6n7Vf6u+PzII5tTZTe+z/nzTYv7XVGcOLQIAmH+H5J4K1p9Dcp8YAABryAIAAIDFsaN6S/Uz1Rtz0sAiO616RXXP6rnVZ4bWwPq0vfpo9e+r+1Svrs4aWsRI26t/bPpv7J9XF4/NAQAAAABYUd+u/rT6qabF0m+rzh9aBGNdUB3U9D7R7tXLqq8NLQIAWF9OrT41OgJuoi2jAwAAWCwWAAAALJ6zqv9U/Vz1scEtrK3PNA3836t6ZXX60BrYOL5fvby6e/W86sihNaylHdUHqodUL6hOGVoDAAAAALC6djS93/TCardq/6YBiPNGRsEauaQ6tHpWtWv1/Ooj1RUjowAA1rGDR6kAVFYAACAASURBVAfATfD16lujIwAAWCwWAAAALK6jq6fMrqMHt7B6zqn+rnpg04ksh1ZbhxbBxnV5042Oj2lasvKm6qKhRayWHdWHqn2qZ+RUHwAAAABg8VxWvbdpMe5dql+s3tB0kidsFJdVH6x+tWno/7nVYbPPAwCwcw5rWrIE68GW0QEAACweCwAAAPhY06Dqi6qTB7ewMrZXn6j+Q3W36vezfRbW2terFzf9Gfyt6nNNQ+Osfx+uHlo9vfrq4BYAAAAAgHmwtfp49bvV3auHV6+ujhsZBcv0o+od1f7Vnatfrt6Zpc8AACvtwuoDoyPgRtje9JwAAADW1KbRAQAAzJWbNw2q/lG1x+AWbrpTqn+YXd8b3AL8uPtWv1E9v+kGSNaPy6t3VX/btNwBAFgdj25anASr6dPV40dHAADAAtmrekbTEPVDq5uNzYFrdULT8NmHml6b2DY2hw1ia7XL6AjWnf9e/dfREQBr6KnVR0ZHwA04onrC6AgAABaPBQAAAFybW1QvrF5e7T64het3RdNp1G+r/mduRoH1YHO1b/WCar/qJ4bWcH3Oqt5U/Y/qh4NbAGARWADAWrAAAAAAxrl909DEk2bXnmNzWGCXV0dWhzcN/R8zNocNygIAlsMCAGDR7FKdWt1ldAhcjxc23Z8JAABrygIAAACuzy2r/1i9LDfgzJNtTQML76reW50zNgfYCberfqV6XvWopuUAjPe16g3VwdWlg1sAYJFYAMBasAAAAADmxz26ehnAvhn6YXUd0zTwf3jTc8NLxuawACwAYDksAAAW0YHVS0dHwHW4rLprdd7oEAAAFo8FAAAA3Bibq6c1LQJ44uCWRbWj+kLT0P+7q9PH5gCrYI9q/+q51cPznH2tnVcdUr29+srgFgBYVBYAsBYsAAAAgPm0qXpQVy8EeEx166FFrHenNz0H/HjT0P8pY3NYQBYAsBwWAACLaJ/qy6Mj4DocVj1rdAQAAIvJMAEAADfVzzUtAviV6haDWxbB15uG/t9VfX9sCrCG7lntVz29emxuDlot26sjmob+D6suHVoDADy8+ufREWx4R1a/NDoCAAC4QTer9q4eVT1idu05tIh5d0LTYsHPzB6PH5sDnZn3+LjpXl/92egIgAGOrfYaHQHX4llN9xQBAMCaswAAAIDlulP1a9VvNp3GwcrYXv1r9YHqfU1vbgCL7Y7V05qWAfxidduxOevetuqz1Xubfs6eOjYHAAAAAIAb6U5NiwAe3rQY4KHVbYYWMcqV1Teblrx9dnadPrQIAICd8cfVfxsdAddwXrVbdfnoEAAAFpMFAAAArISHVC9oWghwh7Ep69LF1f+qPlR9uPrh2Bxgju1SPbL6haZlAPs0nYLE9dtaHVG9p3p/04krAAAAAACsbzerHtD0uvmDm5aW713demQUK25H9Z3qy0uur1WXjowCAGBF3av6buZbmC9vrX57dAQAAIvLEyQAAFbSLZtOqX7m7PF2Y3Pm1vamm1IOn13/ki2xwPLcsXpc9e+qx1Q/17QkgDqu+vjsOqK6cGgNAAAAAABrYVP1U03LAB44u3529jkLdeff1qZh/29VRzUN+3+lOn9kFAAAa+LYaq/REbDEE5ruOQIAgCEsAAAAYLXcvHpitV/1y9WuY3OG2l59s/pM9bnqU9VZQ4uAjerW1aOqR1cPmV27DS1aO8dXX6w+WX2i+sHYHAAAAAAA5sitqgd09WKA+zctBbhnFuuO8v2mQf9vNb2X+q2m5b5XDGwCAGCMu1SnZWkX8+MH1b2a7v0EAIAhLAAAAGAtbK4eUe3btBX1kdUthxatrvOaTqL4UnXk7DpvaBGwyHbv6mUAezfd4Hif1vcNjWc2/Yxdep07tAgAAAAAgPVol6YlAPdpWghwnyXXTzUt3mX5zmpa4Ht8dcLsuurXFwzsAgBgvrywesvoCFjitdX/PToCAIDFZgEAAAAj3LJpIcATZteDq58cWrR8P6yOrr5R/evsOr7aMTIK4AbcvLpvdb9qr6aN1fes7jG75mFJy7am03++XR07ezxudp01LgsAAAAAgAWya1cvBLjr7Nq1afnuXWaPtxlWN9a26ozq5OrU6pSmUzJP6eph//OH1QEAsJ58pHrq6AhY4meb7gkFAIBhLAAAAGAe3KxpEPXnqp9fct1pZNQSl1Xfrb5Xndg0iHpM9a3qnIFdAKvlrl198+KdqjvPPr5zdcempS23rm5R3a5pYcBP3MD/5mXVpdW5s8dLq/Nm12lNC1VOq06fPZ5RXbmC/04AAAAAALAablXdrdptdt2taUnArtVtqzvMHpdetx5SesMurc6uflSdOfv47CWfO6PpdfwfzB69jg8AwM66bdPfPW8xOgRmvlk9aHQEAABYAAAAwDy7c9Op1PeePS697lDdvp07pfri6oKmG1auGjY9Y/bxmdVJTUP/p+3E/wcAAAAAAADAUpubFuzevv//YoBbNQ0+3WrJ196+q+/zu+ZC3qW/d9Ui3qtcXl2y5NfnzX59SXV+ddHs6y9ses/0kmt8PQAArIX/UB0yOgKWOKB6zegIAACwAAAAgPXull19c8ztq9tcx9dtbbqR5YKm06cvqLatRSAAAAAAAAAAAADwY95d7T86Ama2Nx1YdfLoEAAAsAAAAAAAAAAAAAAAAACAtfQT1VnVT44OgZlPV48fHQEAAFWbRwcAAAAAAAAAAAAAAACwUH4xw//Mly2jAwAA4CoWAAAAAAAAAAAAAAAAALCW9hsdAEtcUR02OgIAAK5iAQAAAAAAAAAAAAAAAABrZZfqaaMjYImPVD8aHQEAAFexAAAAAAAAAAAAAAAAAIC18vjqTqMjYIktowMAAGApCwAAAAAAAAAAAAAAAABYK/uNDoAlLqg+OjoCAACWsgAAAAAAAAAAAAAAAACAtbCpevroCFjiPdWloyMAAGApCwAAAAAAAAAAAAAAAABYC4+o9hgdAUtsGR0AAADXZAEAAAAAAAAAAAAAAAAAa2G/0QGwxGnVp0dHAADANVkAAAAAAAAAAAAAAAAAwFr45dEBsMQh1bbREQAAcE0WAAAAAAAAAAAAAAAAALDaHlTtOToCltgyOgAAAK6NBQAAAAAAAAAAAAAAAACstmeODoAljq2+NjoCAACujQUAAAAAAAAAAAAAAAAArLb9RgfAEgeNDgAAgOuyaXQAAAAAAAAAAAAAAAAAG9q9q++OjoCZHdV9qu+NDgEAgGuzeXQAAAAAAAAAAAAAAAAAG9r+owNgic9l+B8AgDlmAQAAAAAAAAAAAAAAAACrab/RAbDEltEBAABwfTaNDgAAAAAAAAAAAAAAAGDD2q06NYdYMh+2VnetfjQ6BAAArosnTwAAAAAAAAAAAAAAAKyW/TK/wvz4aIb/AQCYc55AAQAAAAAAAAAAAAAAsFr2Gx0AS2wZHQAAADdk0+gAAAAAAAAAAAAAAAAANqTbV2dUNx8dAtUF1W7VpaNDAADg+mweHQAAAAAAAAAAAAAAAMCG9PQM/zM/DsvwPwAA64AFAAAAAAAAAAAAAAAAAKyG/UYHwBJbRgcAAMCNsWl0AAAAAAAAAAAAAAAAABvOraqzZo8w2unV3atto0MAAOCGbB4dAAAAAAAAAAAAAAAAwIbzlAz/Mz/emeF/AADWCQsAAAAAAAAAAAAAAAAAWGn7jQ6AJbaMDgAAgBtr0+gAAAAAAAAAAAAAAAAANpR/U51R3WF0CFTHVfcbHQEAADfW5tEBAAAAAAAAAAAAAAAAbCj7Zvif+XHw6AAAALgpLAAAAAAAAAAAAAAAAABgJe03OgBmdlTvHB0BAAA3xabRAQAAAAAAAAAAAAAAAGwYm6tTq91Gh0B1ZPWY0REAAHBTbB4dAAAAAAAAAAAAAAAAwIbxqAz/Mz8OGR0AAAA3lQUAAAAAAAAAAAAAAAAArJRnjg6AmSuqQ0dHAADATWUBAAAAAAAAAAAAAAAAACvlGaMDYOaj1dmjIwAA4KayAAAAAAAAAAAAAAAAAICV8ODq3qMjYOag0QEAALAcFgAAAAAAAAAAAAAAAACwEvYbHQAz51YfGR0BAADLYQEAAAAAAAAAAAAAAAAAK+GZowNg5l3V5aMjAABgOSwAAAAAAAAAAAAAAAAAYGfdt7r/6AiYOWh0AAAALJcFAAAAAAAAAAAAAAAAAOys/UcHwMwJ1RdGRwAAwHJZAAAAAAAAAAAAAAAAAMDOesboAJg5uNoxOgIAAJZr0+gAAAAAAAAAAAAAAAAA1rW7VydlToXxdlT3rU4cHQIAAMu1eXQAAAAAAAAAAAAAAAAA69pzMvzPfDgyw/8AAKxzFgAAAAAAAAAAAAAAAACwM/YfHQAzB40OAACAnWW7GgAAAAAAAAAAAAAAAMu1R3VyZlQY77LqbtW5o0MAAGBnbB4dAAAAAAAAAAAAAAAAwLq1f4b/mQ8fzvA/AAAbgAUAAAAAAAAAAAAAAAAALNf+owNg5qDRAQAAsBJsWAMAAAAAAAAAAAAAAGA59qhOygGVjHd2dbdq6+gQAADYWZ5gAQAAAAAAAAAAAAAAsBy/ktkU5sO7MvwPAMAG4UkWAAAAAAAAAAAAAAAAy/G80QEw8/bRAQAAsFI2jQ4AAAAAAAAAAAAAAABg3blfdczoCKi+Uf3s6AgAAFgpm0cHAAAAAAAAAAAAAAAAsO78+ugAmHnL6AAAAFhJm0YHAAAAAAAAAAAAAAAAsK5sqk6s7j06hIV3RbV7dfboEAAAWCmbRwcAAAAAAAAAAAAAAACwrjw6w//Mh8My/A8AwAZjAQAAAAAAAAAAAAAAAAA3xQtGB8DM20cHAADASts0OgAAAAAAAAAAAAAAAIB14yer06vbjA5h4f2gune1bXQIAACspM2jAwAAAAAAAAAAAAAAAFg3npvhf+bD2zL8DwDABrRpdAAAAAAAAAAAAAAAAADrxueqR4+OYOHtqH66+u7oEAAAWGmbRwcAAAAAAAAAAAAAAACwLuxZPWp0BFSfyPA/AAAblAUAAAAAAAAAAAAAAAAA3BgvrDaNjoDq7aMDAABgtXjSBQAAAAAAAAAAAAAAwA25eXVytevoEBbeOdXu1WWjQwAAYDVsHh0AAAAAAAAAAAAAAADA3Ht2hv+ZD1sy/A8AwAZmAQAAAAAAAAAAAAAAAAA35CWjA2DmH0YHAADAato0OgAAAAAAAAAAAAAAAIC59rPV10ZHQPXV6iGjIwAAYDVtHh0AAAAAAAAAAAAAAADAXHvJ6ACYecvoAAAAWG2bRgcAAAAAAAAAAAAAAAAwt25XnVr95OgQFt5F1e7VBaNDAABgNW0eHQAAAAAAAAAAAAAAAMDc+s0M/zMftmT4HwCABbBpdAAAAAAAAAAAAAAAAABzaZfqhOqeo0Ogekj11dERAACw2jaPDgAAAAAAAAAAAAAAAGAuPSfD/8yHz2f4HwCABWEBAAAAAAAAAAAAAAAAANfm90cHwMybRgcAAMBa2TQ6AAAAAAAAAAAAAAAAgLnzhOqToyOgOq/avbpkdAgAAKyFzaMDAAAAAAAAAAAAAAAAmDt/ODoAZt6e4X8AABbIptEBAAAAAAAAAAAAAAAAzJUHVN/IwZOMt6O6X/Xt0SEAALBWPBEDAAAAAAAAAAAAAABgqT/PzAnz4ZMZ/gcAYMF4MgYAAAAAAAAAAAAAAMBV7lc9a3QEzLxpdAAAAKw1CwAAAAAAAAAAAAAAAAC4yp9k3oT58MPq/aMjAABgrXlCBgAAAAAAAAAAAAAAQNV9queMjoCZt1ZbR0cAAMBaswAAAAAAAAAAAAAAAACAqj+tdhkdAdW2pgUAAACwcCwAAAAAAAAAAAAAAAAA4P7V80ZHwMw/VyeNjgAAgBEsAAAAAAAAAAAAAAAAAOBV1c1GR8DMgaMDAABglE2jAwAAAAAAAAAAAAAAABjqYdUXMmfCfDim2rvaMToEAABG2Dw6AAAAAAAAAAAAAAAAgKFeneF/5sffZfgfAIAF5skZAAAAAAAAAAAAAADA4npK9dHRETBzbnX36uLRIQAAMMrm0QEAAAAAAAAAAAAAAAAMsUv1mtERsMRbMvwPAMCCswAAAAAAAAAAAAAAAABgMb2keuDoCJjZVr1xdAQAAIy2aXQAAAAAAAAAAAAAAAAAa+6O1Xeqfzs6BGbeUz17dAQAAIy2eXQAAAAAAAAAAAAAAAAAa+4vM/zPfDlwdAAAAMyDTaMDAAAAAAAAAAAAAAAAWFMPrL5a7TI6BGaOqh48OgIAAObB5tEBAAAAAAAAAAAAAAAArJnN1Rsy/M98OXB0AAAAzItNowMAAAAAAAAAAAAAAABYMy+q3jg6ApY4q7pHddnoEAAAmAebRwcAAAAAAAAAAAAAAACwJnar/mp0BFzD32f4HwAA/jcLAAAAAAAAAAAAAAAAABbD31V3GB0BS2yt3jw6AgAA5okFAAAAAAAAAAAAAAAAABvfL1XPHh0B13BoderoCAAAmCebRgcAAAAAAAAAAAAAAACwqu5QfaPaY3QIXMODq6NGRwAAwDzZPDoAAAAAAAAAAAAAAACAVfXGDP8zfz6W4X8AAPgxFgAAAAAAAAAAAAAAAABsXL9WPWd0BFyL144OAACAebRpdAAAAAAAAAAAAAAAAACrYvfqG9UdR4fANfxr9dDREQAAMI82jw4AAAAAAAAAAAAAAABgxW2u/jHD/8yn144OAACAeWUBAAAAAAAAAAAAAAAAwMbzp9W+oyPgWny3Omx0BAAAzCsLAAAAAAAAAAAAAAAAADaWxzctAIB59P9U20ZHAADAvNo0OgAAAAAAAAAAAAAAAIAVs2t1VHXX0SFwLc6s7lVdOrgDAADm1ubRAQAAAAAAAAAAAAAAAKyIXap3Zvif+fW6DP8DAMD12jQ6AAAAAAAAAAAAAAAAgBVxYPXS0RFwHS6u7ln9aHQIAADMs82jAwAAAAAAAAAAAAAAANhpL8jwP/PtzRn+BwCAG7RpdAAAAAAAAAAAAAAAAAA75dHV/6puMToErsPW6r7VSaNDAABg3m0eHQAAAAAAAAAAAAAAAMCy3as6LMP/zLd/yvA/AADcKJtGBwAAAAAAAAAAAAAAALAsd6o+W+01OgSux//H3t3Hel/XdRx/nsPpQiBBMxvhNFtZKQvvBpl5V601501rSjcyMYvCdNN1o5tDS0xNpm6Ymol5l5gmaZI3iTlwIukSWJmK4sASBRTlVri84OI6/XF+4AVewHVzzvn8bh6P7bvv73euw/Zk4x/+eL+uHdWDq8+PDgEAgFmwPDoAAAAAAAAAAAAAAACAPXZgdXqO/5l+p+X4HwAAdtvS6AAAAAAAAAAAAAAAAAD2yA+1dvz/+NEhcBdWq4dUnxsdAgAAs2J5dAAAAAAAAAAAAAAAAAC7bbl6a47/mQ3vz/E/AADskaXRAQAAAAAAAAAAAAAAAOyWpeqN1fGjQ2A3rFZHVueNDgEAgFmyPDoAAAAAAAAAAAAAAACAu7RUvSHH/8yO03P8DwAAe2xpdAAAAAAAAAAAAAAAAAB3aql6ffXs0SGwB46szh0dAQAAs2ZldAAAAAAAAAAAAAAAAAB3aL/qlOr3R4fAHvhQjv8BAGCvLI0OAAAAAAAAAAAAAAAAYJf2r06tnjo6BPbQL1T/OToCAABm0croAAAAAAAAAAAAAAAAAH7AParTq8eMDoE99JEc/wMAwF5bGh0AAAAAAAAAAAAAAADAbRzW2hH1g0eHwF74xeozoyMAAGBWLY8OAAAAAAAAAAAAAAAA4FYPqT6d439m0xk5/gcAgH1iAAAAAAAAAAAAAAAAAGA6/E71H9X9RofAXvqr0QEAADDrDAAAAAAAAAAAAAAAAACMtdza4fQ/VgcMboG99eHqnNERAAAw65ZGBwAAAAAAAAAAAAAAACywe1fvqB4/OgT2wY7q4dV/jQ4BAIBZtzI6AAAAAAAAAAAAAAAAYEH9cnVqddjoENhH787xPwAArIul0QEAAAAAAAAAAAAAAAALZqX6i+qEanlwC+yrm6oHVheNDgEAgHmwMjoAAAAAAAAAAAAAAABggRxeva06cnQIrJNTcvwPAADrZml0AAAAAAAAAAAAAAAAwAJYqf6sOrHaf3ALrJet1QOqb4wOAQCAebEyOgAAAAAAAAAAAAAAAGDOHVG9uTpqdAiss5Nz/A8AAOtqaXQAAAAAAAAAAAAAAADAnDqoen71wmrL4BZYb1dXP1VdOToEAADmycroAAAAAAAAAAAAAAAAgDmzVD29Oqk6dHALbJS/zvE/AACsu6XRAQAAAAAAAAAAAAAAAHPksdUrq0eMDoENdGn1gOqG0SEAADBvlkcHAAAAAAAAAAAAAAAAzIHDq/dWn8jxP/PvpTn+BwCADbE0OgAAAAAAAAAAAAAAAGCGHV6dUP12/qJGFsNXWvvv/qbRIQAAMI9WRgcAAAAAAAAAAAAAAADMoIdXz6uOyeE/i+UFOf4HAIANszQ6AAAAAAAAAAAAAAAAYEbsVz2h+vPq0YNbYISzql8ZHQEAAPPMAAAAAAAAAAAAAAAAAMCd+7HqmdWzqvuPTYFhdlRHVeeNDgEAgHm2MjoAAAAAAAAAAAAAAABgCu1XPa76g+op1ZahNTDeW3L8DwAAG25pdAAAAAAAAAAAAAAAAMAUObw6unpGdf+xKTA1rqt+prp8dAgAAMy7ldEBAAAAAAAAAAAAAAAAgz2s+o3qqdWDBrfANHp5jv8BAGBTLI0OAAAAAAAAAAAAAAAA2GRbqse0dvT/5Op+Y3Ngql3c2jDGttEhAACwCFZGBwAAAAAAAAAAAAAAAGyw/aqHVI+qfqn69ergoUUwO16Q438AANg0S6MDAAAAAAAAAAAAAAAA1tnB1VHVIybPI6t7Di2C2fTJ6rGjIwAAYJEYAAAAAAAAAAAAAAAAgOmxUm0fHTFj7l0dUf385H1U9cBqeWQUzIEd1ZHV+aNDAABgkayMDgAAAAAAAAAAAAAAAG710eqh1WW3ey7d6f2t6orqqkGNI2ypfrL66Z2en23t6P/QgV0wz96e438AANh0S6MDAAAAAAAAAAAAAACAW328+tU9+P2r7uS5cvK+evLeOnmurm6YfL5mvcL30kHVj0yee1WHVT9e3Wfy+bDqvpPv+w1qhEV0dfXA6vLRIQAAsGhWRgcAAAAAAAAAAAAAAAB77Z6TZ19cX32vtTGA61sbBrh28mfXVDt2+r0bJ5+3tTYisCt3qw6YfN6/OrA6uNoyeR9YHdLa0f/d9rEd2Bgn5PgfAACGMAAAAAAAAAAAAAAAAACL7aDJc6/RIcBUOLd60+gIAABYVMujAwAAAAAAAAAAAAAAAICpcHN1/OQNAAAMYAAAAAAAAAAAAAAAAAAAqHpddf7oCAAAWGQGAAAAAAAAAAAAAAAAAIDLq5eMjgAAgEVnAAAAAAAAAAAAAAAAAAB4bnXN6AgAAFh0BgAAAAAAAAAAAAAAAABgsZ1RnTY6AgAAMAAAAAAAAAAAAAAAAAAAi2xr9ZzREQAAwBoDAAAAAAAAAAAAAAAAALC4Xl5dNDoCAABYYwAAAAAAAAAAAAAAAAAAFtOF1atHRwAAAN9nAAAAAAAAAAAAAAAAAAAWz47quGrb6BAAAOD7DAAAAAAAAAAAAAAAAADA4nltdfboCAAA4LYMAAAAAAAAAAAAAAAAAMBiubh68egIAADgBxkAAAAAAAAAAAAAAAAAgMWxo/q96vrBHQAAwC4YAAAAAAAAAAAAAAAAAIDFcXJ19ugIAABg1wwAAAAAAAAAAAAAAAAAwGK4sHrR6AgAAOCOGQAAAAAAAAAAAAAAAACA+bejOq7aOjoEAAC4YwYAAAAAAAAAAAAAAAAAYP6dXJ09OgIAALhzBgAAAAAAAAAAAAAAAABgvl1YvWh0BAAAcNcMAAAAAAAAAAAAAAAAAMD82lEdV20dHQIAANw1AwAAAAAAAAAAAAAAAAAwv15TnT06AgAA2D0GAAAAAAAAAAAAAAAAAGA+nVe9aHQEAACw+wwAAAAAAAAAAAAAAAAAwPz5bnVMdePoEAAAYPcZAAAAAAAAAAAAAAAAAID58+zqy6MjAACAPWMAAAAAAAAAAAAAAAAAAObLe6t3jo4AAAD2nAEAAAAAAAAAAAAAAAAAmB8XV384OgIAANg7BgAAAAAAAAAAAAAAAABgPmyvjqmuHR0CAADsHQMAAAAAAAAAAAAAAAAAMB9OqD4zOgIAANh7BgAAAAAAAAAAAAAAAABg9n2ievXoCAAAYN8YAAAAAAAAAAAAAAAAAIDZdkX1tGrH6BAAAGDfGAAAAAAAAAAAAAAAAACA2bWjOra6bHQIAACw7wwAAAAAAAAAAAAAAAAAwOx6YfXR0REAAMD6MAAAAAAAAAAAAAAAAAAAs+kD1atGRwAAAOvHAAAAAAAAAAAAAAAAAADMni9Xz6hWR4cAAADrxwAAAAAAAAAAAAAAAAAAzJbrqt+srh0dAgAArC8DAAAAAAAAAAAAAAAAADA7VqtnVheMDgEAANafAQAAAAAAAAAAAAAAAACYHS+r3jc6AgAA2BgGAAAAAAAAAAAAAAAAAGA2/Ht14ugIAABg4xgAAAAAAAAAAAAAAAAAgOn3v9XvVjcP7gAAADaQAQAAAAAAAAAAAAAAAACYbtdWT66+MzoEAADYWAYAAAAAAAAAAAAAAAAAYHrdVB1d/c/oEAAAYOMZAAAAAAAAAAAAAAAAAIDp9dzqY6MjAACAzWEAAAAAAAAAAAAAAAAAAKbTy6u/Gx0BAABsHgMAAAAAAAAAAAAAAAAAMH3eW714dAQAALC5DAAAAAAAAAAAAAAAAADAdDm7OrZaHR0CAABsLgMAAAAAAAAAAAAAAAAAMD0uqp5SbRsdAgAAbD4DAAAAAAAAAAAAAAAAADAdvlM9vrpidAgAADCGAQAAAAAAAAAAAAAAAAAY/DH7sgAAIABJREFUb2v1pOoro0MAAIBxDAAAAAAAAAAAAAAAAADAWDdVR1efHh0CAACMZQAAAAAAAAAAAAAAAAAAxrm5enr14dEhAADAeAYAAAAAAAAAAAAAAAAAYIzV6vjqn0aHAAAA08EAAAAAAAAAAAAAAAAAAGy+1eo51VtGhwAAANPDAAAAAAAAAAAAAAAAAABsvhdWbxwdAQAATBcDAAAAAAAAAAAAAAAAALC5XlqdNDoCAACYPgYAAAAAAAAAAAAAAAAAYPO8rvrL0REAAMB0MgAAAAAAAAAAAAAAAAAAm+PN1fNGRwAAANPLAAAAAAAAAAAAAAAAAABsvDdVz6pWR4cAAADTywAAAAAAAAAAAAAAAAAAbKxXVX9c7RgdAgAATDcDAAAAAAAAAAAAAAAAALBxTqpeUK2ODgEAAKbfyugAAAAAAAAAAAAAAAAAmEOr1fOr14wOAQAAZocBAAAAAAAAAAAAAAAAAFhfq9XzqteNDgEAAGaLAQAAAAAAAAAAAAAAAABYPzdXx1VvH9wBAADMIAMAAAAAAAAAAAAAAAAAsD5urJ5WvW90CAAAMJsMAAAAAAAAAAAAAAAAAMC+u646ujpjdAgAADC7DAAAAAAAAAAAAAAAAADAvrm0elJ1/ugQAABgthkAAAAAAAAAAAAAAAAAgL33ueqJ1SWjQwAAgNm3PDoAAAAAAAAAAAAAAAAAZtQZ1aNz/A8AAKwTAwAAAAAAAAAAAAAAAACw595cPbG6dnQIAAAwPwwAAAAAAAAAAAAAAAAAwO5brU6s/qjaPrgFAACYMyujAwAAAAAAAAAAAAAAAGBGXF8dU50+OgQAAJhPBgAAAAAAAAAAAAAAAADgrn2tekp17ugQAABgfi2PDgAAAAAAAAAAAAAAAIApd1Z1VI7/AQCADWYAAAAAAAAAAAAAAAAAAHZttTqp+rXqm4NbAACABbAyOgAAAAAAAAAAAAAAAACm0Heqp1f/NjoEAABYHAYAAAAAAAAAAAAAAAAA4LbOr55afXV0CAAAsFiWRwcAAAAAAAAAAAAAAADAFHln9agc/wMAAAMYAAAAAAAAAAAAAAAAAIC6oXpGdWy1dXALAACwoFZGBwAAAAAAAAAAAAAAAMBgn23t8P9Lo0MAAIDFtjw6AAAAAAAAAAAAAAAAAAbZXp1UPSrH/wAAwBRYGR0AAAAAAAAAAAAAAAAAA1xcHVudMzoEAADgFsujAwAAAAAAAAAAAAAAAGATrVanVEfk+B8AAJgyK6MDAAAAAAAAAAAAAAAAYJNcXh1XfXh0CAAAwK4sjw4AAAAAAAAAAAAAAACATfDu6kE5/gcAAKaYAQAAAAAAAAAAAAAAAADm2VerJ1RPq64a3AIAAHCnDAAAAAAAAAAAAAAAAAAwj26q/qY6ovrI4BYAAIDdsjI6AAAAAAAAAAAAAAAAANbZJ6tnV18YHQIAALAnlkcHAAAAAAAAAAAAAAAAwDq5sjq+elyO/wEAgBm0MjoAAAAAAAAAAAAAAAAA9tFqdWr1p9W3B7cAAADsNQMAAAAAAAAAAAAAAAAAzLLPVH8yeQMAAMy05dEBAAAAAAAAAAAAAAAAsBcurH6remSO/wEAgDmxMjoAAAAAAAAAAAAAAAAA9sC3q1dXJ1fbBrcAAACsKwMAAAAAAAAAAAAAAAAAzILrq9dXr6iuHdwCAACwIQwAAAAAAAAAAAAAAAAAMM22V2+tXlJdNjYFAABgYxkAAAAAAAAAAAAAAAAAYBrdXJ1WnVh9aXALAADApjAAAAAAAAAAAAAAAAAAwDS5qXpP9Yoc/gMAAAvGAAAAAAAAAAAAAAAAAADTYFv1jupl1SWDWwAAAIYwAAAAAAAAAAAAAAAAAMBI11Vvq15ZXTa4BQAAYCgDAAAAAAAAAAAAAAAAAIxwRfW31Wurqwa3AAAATAUDAAAAAAAAAAAAAAAAAGymc1s7/H9PtXVwCwAAwFQxAAAAAAAAAAAAAAAAAMBG21b9a3VK9fHBLQAAAFPLAAAAAAAAAAAAAAAAAAAb5RvV31dvqK4Y3AIAADD1DAAAAAAAAAAAAAAAAACwnnZUZ1anVP9SbR+bAwAAMDsMAAAAAAAAAAAAAAAAALAeLqzeVZ1aXTy4BQAAYCYZAAAAAAAAAAAAAAAAAGBvXVn9c/XO6pxqdWwOAADAbDMAAAAAAAAAAAAAAAAAwJ74XvXx6h+qD1Q3jc0BAACYHwYAAAAAAAAAAAAAAAAAuCvbqzOrd1Xvr747NgcAAGA+GQAAAAAAAAAAAAAAAABgV25o7ej/g9UHqm+NzQEAAJh/BgAAAAAAAAAAAAAAAAC4xbeqM6rTqo9V28bmAAAALBYDAAAAAAAAAAAAAAAAAIvti9UHqw9V51SrY3MAAAAWlwEAAAAAAAAAAAAAAACAxXJJdVZ15uS5ZGwOAAAAtzAAAAAAAAAAAAAAAAAAMN+uqD5RnVN9qjpvaA0AAAB3yAAAAAAAAAAAAAAAAADAfLmy+mR1VnVm9YVqdWgRAAAAu8UAAAAAAAAAAAAAAAAAwOzaXl1YnVd9qjqnuqDaMTIKAACAvWMAAAAAAAAAAAAAAAAAYHZc1m2P/c+rtg4tAgAAYN0YAAAAAAAAAAAAAAAAAJg+W6sLqs9XX6j+u/psdeXIKAAAADaWAQAAAAAAAAAAAAAAAIBxtldfq77Y2qH/F6vzqi9VNw/sAgAAYAADAAAAAAAAAAAAAAAAMD0cfM+nm6uvVxdXF+30vqD6cnXjuDQAAACmydLoAAAAAAAAAAAAAAAA4FYr1c9VD6seOnkeUB1aLQ/s4q5d19ph/+2fi6r/y5E/AAAAu8EAAAAAAAAAAAAAAAAATL8t1X2q+1Y/Ud1v8v3Q6kdv97gVWF/XVJdWl1ffqL650/vrO72vHxUIAADA/PA/9QAAAAAAAAAAAAAAMD+Wu+0YwD2rQybPPXb6fMjkz+5RHVjdbfKz/asf3vTqzbG1uqq6cvK+6g6+3/KzK1o7/N86IhYAAIDFZAAAAAAAAAAAAAAAAAC4vYOqLa2NBGyZfL/75PMhrQ0GHFAd3NpowN0n/9xSa6MCtzhg8ru7+rPbu7pavd3PvlvdtNP31cnv3VhdX91QbauuqbZP3tsmP79+8ntXT75/767+pQEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAOD/2YMDAQAAAAAg/9dGUFVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVWEPDgQAAAAAgPxfG0FVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVWSX4b6AAAgAElEQVRVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVWFPTgQAAAAAADyf20EVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVFfbgQAAAAAAAyP+1EVRVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVXKB3p8AAB8PSURBVFVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVXYgwMBAAAAACD/10ZQVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVaQ8OSAAAAAAE/X/djkAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAICdAPss7zmiR8rOAAAAAElFTkSuQmCC\"\n  }))));\n}\nconst ForwardRef = /*#__PURE__*/React.forwardRef(SvgAp);\nexport default __webpack_public_path__ + \"static/media/ap.2109e5935d49d084151c95d5e1eb1d9d.svg\";\nexport { ForwardRef as ReactComponent };", "import React, { useState, useEffect } from 'react';\r\nimport '../style.css';\r\nimport { motion } from \"framer-motion\";\r\n// import Header from '../../header/headerlogo';\r\nimport { FaApple, FaInfoCircle } from 'react-icons/fa';\r\nimport { FcGoogle } from \"react-icons/fc\";\r\nimport { Auth } from '../../core/utils/auth';\r\nimport { GetCookie, RemoveCookie } from '../../core/utils/cookies';\r\nimport { useQuery } from \"react-query\";\r\nimport axios from 'axios';\r\nimport toastr from 'toastr';\r\nimport 'toastr/build/toastr.min.css';\r\nimport { getCountry } from '../../core/utils/main';\r\nimport { useStripe } from '@stripe/react-stripe-js';\r\nimport { SetCookie } from '../../core/utils/cookies';\r\n// import cc05webP from '../../assets/images/index_05_cc.webp';\r\n// import cc05PNG from '../../assets/images/index_05_cc.png';\r\n// import ppwebP from '../../assets/images/index_05_pp.webp';\r\n// import ppPNG from '../../assets/images/index_05_pp.png';\r\n// import gpwebP from '../../assets/images/index_05_gp.webp';\r\n// import gpPNG from '../../assets/images/index_05_gp.png';\r\n// import apwebP from '../../assets/images/index_05_ap.webp';\r\n// import apPNG from '../../assets/images/index_05_ap.png';\r\nimport pplogowebP from \"../../assets/images/index_05_m_pp.webp\"\r\nimport pplogoPNG from \"../../assets/images/index_05_m_pp.png\"\r\n// import cc05mwebP from \"../../assets/images/index_05_m_cc.webp\"\r\n// import cc05mPNG from \"../../assets/images/index_05_m_cc.png\"\r\nimport { CiCreditCard1 } from \"react-icons/ci\";\r\nimport Loader from \"./loader\";\r\nimport PaymentButton from './index_05_payment_button';\r\nimport ccIcon from './mpay-assets/cc.svg';\r\nimport ppIcon from './mpay-assets/pp.svg';\r\nimport gpIcon from './mpay-assets/gp.svg';\r\nimport apIcon from './mpay-assets/ap.svg';\r\n\r\n\r\n\r\n\r\n\r\n\r\nconst pricing = GetCookie(\"pricing\") ? GetCookie(\"pricing\") : \"\";\r\nconst tk = GetCookie(\"access\") ? GetCookie(\"access\") : \"\";\r\nconst pmt = GetCookie(\"pmt\") ? GetCookie(\"pmt\") : \"\";\r\nconst cta_pmt = GetCookie(\"cta_pmt\") ? GetCookie(\"cta_pmt\") : \"\";\r\nvar plan = null;\r\n\r\nconst isWebpSupported = () => {\r\n  const elem = document.createElement('canvas');\r\n\r\n  if (!!(elem.getContext && elem.getContext('2d'))) {\r\n    return elem.toDataURL('image/webp').indexOf('data:image/webp') === 0;\r\n  }\r\n\r\n  return false;\r\n};\r\n\r\nconst convertWebpToPng = (webpSrc, pngSrc) => {\r\n  if (!isWebpSupported()) {\r\n    return pngSrc;\r\n  }\r\n  return webpSrc;\r\n};\r\n\r\n// const cc05SRC = convertWebpToPng(cc05webP, cc05PNG);\r\n// const ppSRC = convertWebpToPng(ppwebP, ppPNG);\r\n// const gpSRC = convertWebpToPng(gpwebP, gpPNG);\r\n// const apSRC = convertWebpToPng(apwebP, apPNG);\r\nconst pplogoSRC = convertWebpToPng(pplogowebP, pplogoPNG);\r\n// const cc05mSRC = convertWebpToPng(cc05mwebP, cc05mPNG);\r\n\r\nasync function getPlan() {\r\n  if (plan) return plan;\r\n  const response = await axios.post(`${process.env.REACT_APP_API_URL}/get-plan`, { plan_id: pricing }, { headers: { 'content-type': 'application/x-www-form-urlencoded' } });\r\n  const output = response.data;\r\n  if (output.success) {\r\n    plan = output.data;\r\n    return output.data;\r\n  } else {\r\n    return [];\r\n  }\r\n}\r\n\r\n// const paymentOptions = [\r\n//   { id: 'pp', imgSrc: ppSRC, alt: 'paypal', label: 'Paypal' },\r\n//   { id: 'gp', imgSrc: gpSRC, alt: 'google-pay', label: 'Google Pay' },\r\n//   { id: 'ap', imgSrc: apSRC, alt: 'apple-pay', label: 'Apple Pay' },\r\n\r\n// ];\r\n\r\n\r\nfunction Payment({ selectedMethod, setSelectedMethod }) {\r\n  const auth = Auth('/register-auth');\r\n  const stripe = useStripe();\r\n  const { data } = useQuery(\"users\", getPlan);\r\n  const [name, setName] = useState(\"\");\r\n  const [cardNumber, setCardNumber] = useState(\"\");\r\n  const [cardDate, setCardDate] = useState(\"\");\r\n  const [cvv, setCVV] = useState(\"\");\r\n  const [nameError, setNameError] = useState(\"\");\r\n  const [cardNumberError, setCardNumberError] = useState(\"\");\r\n  const [cardDateError, setCardDateError] = useState(\"\");\r\n  const [cvvError, setCVVError] = useState(\"\");\r\n  const [willRedirect, setWillRedirect] = useState(true);\r\n  const [isRedirecting, setRedirecting] = useState(false);\r\n  const [isGooglePay, setIsGooglePay] = useState(false);\r\n  const [isApplePay, setIsApplePay] = useState(false);\r\n  const [isGoogleSupported, setIsGoogleSupported] = useState(false);\r\n  const [isAppleSupported, setIsAppleSupported] = useState(false);\r\n  const [paymentRequest, setPaymentRequest] = useState(null);\r\n  // const [loading, setLoading] = useState(true);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [isLoadingCreditCard, setIsLoadingCreditCard] = useState(false);\r\n  const [isLoadingPaypal, setIsLoadingPaypal] = useState(false);\r\n  const [isLoadingGooglePay, setIsLoadingGooglePay] = useState(false);\r\n  const [isLoadingApplePay, setIsLoadingApplePay] = useState(false);\r\n\r\n  useEffect(() => {\r\n    toastr.options = {\r\n      positionClass: 'toast-top-center'\r\n    };\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    //if error occured in 3d secure\r\n    let threed_error = GetCookie('threed_error');\r\n\r\n    if (threed_error!==undefined && threed_error!==''){\r\n      setTimeout(function(){ \r\n        SetCookie('threed_error','');\r\n        toastr.error(threed_error);\r\n      }, 2000);      \r\n    }\r\n  }, []);\r\n\r\n  /**GooglePay and ApplePay Logic**/\r\n  useEffect(() => {\r\n    if ((pmt === 'mpay_st' || pmt === 'mpay_rec') && !!data && !!stripe) {\r\n      var price = '0';\r\n\r\n      if (data.trial_price !== '' && data.trial_price > 0) {\r\n        price = data.trial_price * 100;\r\n      } else {\r\n        price = data.price * 100;\r\n      }\r\n\r\n      var country = getCountry(data.currency);\r\n      var pr = stripe.paymentRequest({\r\n        country: country,\r\n        currency: data.currency.toLowerCase(),\r\n        total: {\r\n          label: data.plan_type,\r\n          amount: price,\r\n        },\r\n        requestPayerName: true,\r\n        requestPayerEmail: true,\r\n      });\r\n        // console.log(paymentOption);\r\n        pr.canMakePayment().then(result => {\r\n          if (!result) {\r\n            console.log('Does not support GooglePay or ApplePay payment');\r\n            return;\r\n          }\r\n\r\n          // console.log(pr)\r\n\r\n          setPaymentRequest(pr);\r\n          if (result.googlePay) {\r\n            setIsGooglePay(true);\r\n            setIsGoogleSupported(true);\r\n            // document.getElementById(\"payment-option\").style.display = 'block';\r\n            // console.log('isGooglePay')\r\n\r\n          } else if (result.applePay) {\r\n            setIsApplePay(true);\r\n            setIsAppleSupported(true);\r\n            // document.getElementById(\"payment-option\").style.display = 'block';\r\n            // console.log('isApplePay')\r\n          }\r\n        });\r\n    }\r\n  }, [data, stripe]);\r\n\r\n  useEffect(() => {\r\n    if (paymentRequest) {\r\n        paymentRequest.on('paymentmethod', async (e) => {\r\n            const paymentMethodId = e.paymentMethod.id;\r\n            const payer_email = e.paymentMethod.payerEmail;\r\n            const payerName = e.paymentMethod.payerName;\r\n\r\n            let clientReferenceId = \"\";\r\n            let elements = document.getElementsByName(\"referral\");\r\n            if (elements[0]) {\r\n                clientReferenceId = elements[0].value;\r\n            }\r\n\r\n            document.querySelector(\".loader-container\").classList.add('active');\r\n            try {\r\n                const response = await axios.post(`${process.env.REACT_APP_API_URL}/t/create-subscription-stripe-apple-google`, {\r\n                    tk,\r\n                    payment_menthod_id: paymentMethodId,\r\n                    plan_id: pricing,\r\n                    email: payer_email,\r\n                    cus_name: payerName,\r\n                    client_reference_id: clientReferenceId\r\n                }, { headers: { 'content-type': 'application/x-www-form-urlencoded' } });\r\n\r\n                let output = response.data;\r\n                if ( output.success ) {\r\n                    // console.log(output);\r\n                    e.complete('success');\r\n                    toastr.success(\"Success\");\r\n                    RemoveCookie('pricing');\r\n                    window.location.href = '/thankyou/?plan=' + plan.label.replace(\" \", \"\").replace(\" \", \"\");\r\n                    return; // Ensure no further code execution after redirection\r\n                } else {\r\n                    if (output.data) \r\n                    toastr.error(output.data.msg);\r\n                    document.querySelector(\".loader-container\").classList.remove('active');\r\n                    e.complete('fail');\r\n                }\r\n            } catch (error) {\r\n                if (error.response && error.response.status === 429) {\r\n                    toastr.error(\"Sorry, too many requests. Please try again in a bit!\");\r\n                }\r\n                // Handle other errors if needed\r\n            }\r\n        });\r\n    }\r\n}, [paymentRequest]);\r\n\r\n\r\n  const handleGoogleButtonClick = () => {\r\n    if (paymentRequest && isGooglePay ) {\r\n      paymentRequest.show();\r\n    }\r\n    else{\r\n      setIsGoogleSupported(false);\r\n    }\r\n  };\r\n\r\n  const handleAppleButtonClick = () => {\r\n    if (paymentRequest && isApplePay) {\r\n      paymentRequest.show();\r\n    }\r\n    else{\r\n      setIsAppleSupported(false);\r\n    }\r\n  };\r\n\r\n\r\n  if (auth === undefined || auth === false) return;\r\n\r\n  if (willRedirect) {\r\n    if (auth.status === 'active' && auth.expired === 'no') {\r\n      window.location.href = '/my-account';\r\n      return;\r\n    }\r\n  }\r\n\r\n\r\n  var date = new Date();\r\n  date.setTime(date.getTime() + 30 * 24 * 60 * 60 * 1000);\r\n  var today = new Date();\r\n  var expire_date = new Date();\r\n  expire_date.setDate(today.getDate() + 30);\r\n\r\n  if (data && data.currency && data.price) {\r\n    var amount = data.price;\r\n    if (data.trial_price !== '') {\r\n      amount = data.trial_price;\r\n    }\r\n    SetCookie('currency', data.currency, { expires: expire_date, path: '/' });\r\n    SetCookie('currency', data.currency, { expires: expire_date, domain: '.ai-pro.org', path: '/' });\r\n    SetCookie('amount', amount, { expires: expire_date, path: '/' });\r\n    SetCookie('amount', amount, { expires: expire_date, domain: '.ai-pro.org', path: '/' });\r\n  }\r\n\r\n\r\n  const handleResponse = (res) => {\r\n    let output = res.data;\r\n\r\n    if (output.success) {\r\n      window.location.replace(output.data.link);\r\n      setIsLoading(false);\r\n      return;\r\n    }\r\n\r\n    setIsLoading(false);\r\n    if (output.data) toastr.error(output.data.msg);\r\n  };\r\n\r\n\r\n  const handleNameChange = (event) => {\r\n    let input = event.target.value;\r\n    input = input.replace(/[^A-Za-z ]/g, \"\");\r\n    input = input.slice(0, 50);\r\n    setName(input);\r\n  };\r\n  \r\n  const handleCardNumberChange = (event) => {\r\n    let input = event.target.value;\r\n    input = input.replace(/\\D/g, \"\");\r\n    input = input.replace(/-/g, \"\");\r\n    input = input.replace(/(\\d{4})/g, \"$1-\");\r\n    input = input.replace(/-$/, \"\");\r\n    input = input.slice(0, 19);\r\n    setCardNumber(input);\r\n  };\r\n\r\n  const handleCardDateChange = (event) => {\r\n    let input = event.target.value;\r\n    input = input.replace(/\\D/g, \"\");\r\n    input = input.slice(0, 4);\r\n    if (input.length >= 3) {\r\n      input = input.slice(0, 2) + \"/\" + input.slice(2);\r\n    }\r\n    setCardDate(input);\r\n  };\r\n\r\n  const handleCVVChange = (event) => {\r\n    let input = event.target.value;\r\n    input = input.replace(/\\D/g, \"\");\r\n    input = input.slice(0, 5);\r\n    setCVV(input);\r\n  };\r\n\r\n  const submitPayment = () => {\r\n    setWillRedirect(false);\r\n    setNameError(\"\");\r\n    setCardNumberError(\"\");\r\n    setCardDateError(\"\");\r\n    setCVVError(\"\");\r\n\r\n    // Perform validation\r\n    let isValid = true;\r\n\r\n    let clientReferenceId = \"\";\r\n    let elements = document.getElementsByName(\"referral\");\r\n    if (elements[0]) {\r\n      clientReferenceId = elements[0].value;\r\n    }\r\n\r\n    if (!name.includes(\" \")) {\r\n      setNameError(\"enter at least two names separated by a space\");\r\n      isValid = false;\r\n    }\r\n    if (!cardNumber) {\r\n      setCardNumberError(\"required\");\r\n      isValid = false;\r\n    }\r\n    if (!cardDate || !/^(0[1-9]|1[0-2])\\/\\d{2}$/.test(cardDate)) {\r\n      setCardDateError(\"MM/YY\");\r\n      isValid = false;\r\n    }\r\n    if (!cvv || !/^\\d{3,5}$/.test(cvv)) {\r\n      setCVVError(\"required\");\r\n      isValid = false;\r\n    }\r\n    var name_split = name.split(\" \");\r\n    var first_name = name_split[0];\r\n    var last_name = name_split[name_split.length - 1];\r\n    var ccmonth = cardDate.split(\"/\")[0];\r\n    var ccyr = cardDate.split(\"/\")[1];\r\n    if (first_name === '' && last_name === '') {\r\n      setNameError(\"required\");\r\n      isValid = false;\r\n    } else if (first_name === '' || last_name === '') {\r\n      setNameError(\"enter at least two names separated by a space\");\r\n      isValid = false;\r\n    }\r\n    // If any validation error occurred, stop further processing\r\n    if (!isValid) {\r\n      return;\r\n    }\r\n\r\n\r\n    document.querySelector(\".loader-container\").classList.add('active');\r\n\r\n    const url = pmt === 'mpay_st'\r\n      ? `${process.env.REACT_APP_API_URL}/t/create-subscription-stripe`\r\n      : `${process.env.REACT_APP_API_URL}/t/create-subscription`;\r\n\r\n    axios.post(url, {\r\n      tk,\r\n      first_name,\r\n      last_name,\r\n      cc: cardNumber,\r\n      ccmonth: ccmonth,\r\n      ccyr: \"20\" + ccyr,\r\n      cvv: cvv,\r\n      plan_id: pricing,\r\n      client_reference_id: clientReferenceId,\r\n      psource: 'mpay'\r\n    }, { headers: { 'content-type': 'application/x-www-form-urlencoded' } }).then(function (res) {\r\n      let output = res.data;\r\n      if (output.success) {\r\n        if (output.redirect && output.redirect!==''){\r\n          window.location.href = output.redirect;\r\n          return;\r\n        }\r\n        toastr.success(\"Success\");\r\n        RemoveCookie('pricing');\r\n        window.location.href = '/thankyou/?plan=' + plan.label.replace(\" \", \"\").replace(\" \", \"\");\r\n        return;\r\n      }\r\n      document.querySelector(\".loader-container\").classList.remove('active');\r\n      if (output.data) toastr.error(output.data.msg);\r\n    }).catch(function (error) {\r\n      if (error.response && error.response.status === 429) {\r\n        document.querySelector(\".loader-container\").classList.remove('active');\r\n        toastr.error(\"Sorry, too many requests. Please try again in a bit!\");\r\n      }\r\n    });\r\n  }\r\n\r\n  const submitPayPal = () => {\r\n    setRedirecting(true)\r\n\r\n    axios.post(`${process.env.REACT_APP_API_URL}/create-subscription-paypal`, {\r\n      tk,\r\n      plan_id: pricing\r\n    }, { headers: { 'content-type': 'application/x-www-form-urlencoded' } }).then(function (res) {\r\n      handleResponse(res, 'paypal');\r\n    });\r\n  };\r\n\r\n  const handlePaymentMethodClick = (method) => {\r\n    setSelectedMethod(method);\r\n    setIsLoadingCreditCard(true);\r\n    setIsLoadingPaypal(true);\r\n    setIsLoadingGooglePay(true);\r\n    setIsLoadingApplePay(true);\r\n    setIsLoading(true);\r\n    setTimeout(() => {\r\n      setIsLoading(false);\r\n      setIsLoadingCreditCard(false);\r\n      setIsLoadingPaypal(false);\r\n      setIsLoadingGooglePay(false);\r\n      setIsLoadingApplePay(false);\r\n    }, 2000);\r\n  };\r\n\r\n  // console.log(paymentRequest);\r\n\r\n  return (\r\n    <>\r\n      {/* <Header auth={auth} /> */}\r\n      {data ? (\r\n        <div className=\"Payment flex\">\r\n          <div className={`information-container flex flex-col ${selectedMethod !== null ? `bg-white` : ``} w-full p-0`}>\r\n            <div className=\"flex flex-col items-center\">\r\n              <div className=\"pay w-full\">\r\n                <div className=\"overflow-hidden w-full\">\r\n                  <div className=\"\">\r\n                    <div className={`header-container text-center text-white lg:text-black mb-5 lg:mb-0 ${selectedMethod !== null ? `hidden lg:block` : ``}`}>\r\n                      <h1 className=\"font-semibold text-[20px] px-1 text-xl lg:text-4xl mb-0 lg:mb-2\">Choose Payment Method</h1>\r\n                      <span className=\"text-[12px] lg:text-base mb-5\">\r\n                        All transactions are secure and encrypted.\r\n                      </span>\r\n                    </div>\r\n                    <hr className=\"my-5 h-[1px] border-t-0 bg-black/10 hidden lg:block\" />\r\n\r\n                    {/* {loading ? (\r\n                      <SkeletonLoader googlePayAvailable={isGooglePay} applePayAvailable={isApplePay} />\r\n                    ) : (\r\n                      <> */}\r\n                    <div className={`payment-options overflow-hidden grid grid-cols-2 lg:grid-cols-4 gap-4 px-5 lg:px-0 justify-center mb-3 lg:mb-10 lg:gap-3 ${selectedMethod !== null ? 'hidden lg:flex' : ''}`}>\r\n                      {/* CREDIT CARD */}\r\n                      <PaymentButton\r\n                        onClick={() => handlePaymentMethodClick('creditCard')}\r\n                        isLoading={isLoadingCreditCard}\r\n                        isSelected={selectedMethod === 'creditCard'}\r\n                        buttonClass={`ml-auto lg:ml-0`}\r\n                        method=\"creditCard\"\r\n                        htmlFor=\"paypal\"\r\n                        label=\"Credit or <br />Debit Card\"\r\n                        src={ccIcon}\r\n                        alt=\"credit-card\"\r\n                        selectedMethod={selectedMethod}\r\n                      />\r\n\r\n                      {/* PAYPAL */}\r\n                      <PaymentButton\r\n                        onClick={() => handlePaymentMethodClick('paypal')}\r\n                        isLoading={isLoadingPaypal}\r\n                        isSelected={selectedMethod === 'paypal'}\r\n                        method=\"paypal\"\r\n                        htmlFor=\"credit-card\"\r\n                        label=\"Paypal\"\r\n                        src={ppIcon}\r\n                        alt=\"paypal\"\r\n                        selectedMethod={selectedMethod}\r\n                      />\r\n\r\n                      {/* GOOGLE PAY */}\r\n                      <PaymentButton\r\n                        onClick={() => handlePaymentMethodClick('google-pay')}\r\n                        isSelected={selectedMethod === 'google-pay'}\r\n                        isLoading={isLoadingGooglePay}\r\n                        buttonClass={`ml-auto lg:ml-0`}\r\n                        method=\"google-pay\"\r\n                        htmlFor=\"google-pay\"\r\n                        label=\"Google Pay\"\r\n                        src={gpIcon}\r\n                        alt=\"google-pay\"\r\n                        selectedMethod={selectedMethod}\r\n                      />\r\n\r\n                      {/* APPLE PAY */}\r\n                      <PaymentButton\r\n                        onClick={() => handlePaymentMethodClick('apple-pay')}\r\n                        isLoading={isLoadingApplePay}\r\n                        isSelected={selectedMethod === 'apple-pay'}\r\n                        method=\"apple-pay\"\r\n                        htmlFor=\"apple-pay\"\r\n                        label=\"Apple Pay\"\r\n                        src={apIcon}\r\n                        alt=\"apple-pay\"\r\n                        selectedMethod={selectedMethod}\r\n                      />\r\n                    </div>\r\n\r\n                    <div id=\"card-section\" className={`card-section p-5 lg:p-0 ${selectedMethod !== \"creditCard\" ? \"!hidden\" : \"block\"} transition-opacity duration-300 w-full`}>\r\n                      {isLoading ? (\r\n                        <div className=\"flex justify-center items-center h-full\">\r\n                          <div className=\"animate-pulse flex flex-col bg-white w-full lg:p-0 p-5 lg:mt-3\">\r\n                            <div className=\"lg:hidden block h-20 bg-gray-200 rounded mb-3\"></div>\r\n                            <div className=\"h-4 bg-gray-200 w-4/12 rounded mb-4\"></div>\r\n                            <div className=\"h-8 bg-gray-200 rounded mb-4\"></div>\r\n                            <div className=\"h-4 bg-gray-200 w-4/12 rounded mb-4\"></div>\r\n                            <div className=\"h-8 bg-gray-200 rounded mb-4\"></div>\r\n\r\n                            <div className=\"flex justify-between mt-2\">\r\n                              <div className=\"h-4 bg-gray-200 w-4/12 rounded mb-4\"></div>\r\n                              <div className=\"h-4 bg-gray-200 w-4/12 rounded mb-4 mr-[87px]\"></div>\r\n                            </div>\r\n                            <div className=\"flex justify-between mt-2\">\r\n                              <div className=\"h-8 bg-gray-200 w-full rounded mb-4\"></div>\r\n                            </div>\r\n                            <div className=\"h-4 bg-gray-200 rounded mb-4 mt-3\"></div>\r\n                            <div className=\"h-4 bg-gray-200 w-2/12 rounded mb-3\"></div>\r\n                            <div className=\"h-11 bg-gray-200 rounded mb-3 mt-3\"></div>\r\n                          </div>\r\n                        </div>\r\n                      ) : (\r\n                        <>\r\n                          <div className=\"mb-4\">\r\n                            <div className=\"lg:hidden mx-auto max-w-[360px] max-h-[65px]\">\r\n                              <label key={'creditCard'} className=\"payment-section lg:h-auto bg-white flex lg:p-5 items-center justify-center w-full\">\r\n                                <img src={ccIcon} alt={'creditCard'} className=\"w-[49px] mr-2 h-[31px]\" loading=\"lazy\" />\r\n                                <span className='w-24 text-center text-sm'>Credit or Debit Card</span>\r\n                              </label>\r\n                            </div>\r\n                            <hr className=\"border-black/5 rounded-full border-2 my-4 block lg:hidden\" />\r\n                            <label className=\"text-sm font-bold pb-3 block mb-0 mt-1\" htmlFor=\"name\">Cardholder Name <span className=\"text-red-500\">*</span>\r\n                              {nameError && <span className=\"text-red-500 text-xs text-left w-full mb-2 inline font-normal\">{nameError}</span>}</label>\r\n                            <input className=\"w-full px-5 py-2 border border-gray-300 rounded fs-exclude\"\r\n                              type=\"text\"\r\n                              id=\"name\"\r\n                              name=\"name\"\r\n                              placeholder=\"John Doe\"\r\n                              value={name}\r\n                              onChange={handleNameChange}\r\n                              onKeyUp={(event) => {\r\n                                setName(event.target.value);\r\n                              }}\r\n                              disabled={isLoading} />\r\n                          </div>\r\n                          <div className=\"mb-4\">\r\n                            <label className=\"text-sm font-bold pb-3 block mb-0 mt-1\" htmlFor=\"card-number\">Card Number <span className=\"text-red-500\">*</span>\r\n                              {cardNumberError && <span className=\"text-red-500 text-xs text-left w-full mb-2 inline font-normal\">{cardNumberError}</span>}\r\n                            </label>\r\n                            <div className=\"relative\">\r\n                              <input className=\"w-full px-5 py-2 border border-gray-300 rounded fs-exclude\"\r\n                                type=\"text\"\r\n                                id=\"card-number\"\r\n                                name=\"card-number\"\r\n                                placeholder=\"1234 5678 9012 3456\"\r\n                                value={cardNumber}\r\n                                onChange={handleCardNumberChange}\r\n                                onKeyUp={(event) => {\r\n                                  setCardNumber(event.target.value);\r\n                                }}\r\n                                disabled={isLoading} />\r\n                              <CiCreditCard1 className=\"absolute right-3 top-1.5 text-[#E4E4E4]\" size={30} />\r\n                            </div>\r\n                          </div>\r\n                          <div className=\"mb-4 lg:flex\">\r\n                            <div className=\"expdate w-full mr-2 md:mr-5\">\r\n                              <label className=\"text-sm font-bold pb-3 block mb-0 mt-1\" htmlFor=\"expiration-date\">Expiration Date <span className=\"text-red-500\">*</span>\r\n                                {cardDateError && <span className=\"text-red-500 text-xs text-left w-full mb-2 inline font-normal\">{cardDateError}</span>}</label>\r\n                              <input className=\"w-full px-5 py-2 mb-3 lg:mb-0 border border-gray-300 rounded fs-exclude\"\r\n                                type=\"text\"\r\n                                id=\"expiration-date\"\r\n                                name=\"expiration-date\"\r\n                                placeholder=\"MM/YY\"\r\n                                value={cardDate}\r\n                                onChange={handleCardDateChange}\r\n                                onKeyUp={(event) => {\r\n                                  setCardDate(event.target.value);\r\n                                }}\r\n                                disabled={isLoading} />\r\n                            </div>\r\n                            <div className=\"cvv w-full\">\r\n                              <label className=\"text-sm pb-3 block mb-0 mt-1\" htmlFor=\"cvv\"> <strong>Security Code </strong>(CVV)<span className=\"text-red-500\">*</span>\r\n                                {cvvError && <span className=\"text-red-500 text-xs text-left w-full mb-2 inline font-normal\">{cvvError}</span>}</label>\r\n                              <input className=\"w-full px-5 py-2 border border-gray-300 rounded fs-exclude\"\r\n                                type=\"text\"\r\n                                id=\"cvv\"\r\n                                name=\"cvv\"\r\n                                placeholder=\"CVV\"\r\n                                value={cvv}\r\n                                onChange={handleCVVChange}\r\n                                onKeyUp={(event) => {\r\n                                  setCVV(event.target.value);\r\n                                }}\r\n                                disabled={isLoading} />\r\n                            </div>\r\n                          </div>\r\n                          <span className=\"text-[12px] text-gray-600\"><FaInfoCircle className=\"inline text-xs mb-1 lg:mb-0 mr-1\" /> By clicking the “{cta_pmt ? cta_pmt : \"Complete Purchase\"}” button, I have read and agreed to the&nbsp;<a href=\"https://ai-pro.org/member-tos-page/\" rel=\"noopener noreferrer\" target=\"_blank\" className=\"font-medium text-[#0070BA] underline\">Terms and Conditions</a>.</span>\r\n                          <motion.button\r\n                            className=\"bg-blue-500 text-white font-bold text-center py-3 px-6 rounded-lg w-full my-4 proceed-pmt\"\r\n                            whileHover={{ backgroundColor: \"#5997fd\" }}\r\n                            whileTap={{ scale: 0.9 }}\r\n                            onClick={submitPayment}\r\n                            disabled={isLoading}\r\n                          >\r\n                            {cta_pmt ? cta_pmt : \"Complete Purchase\"}\r\n                          </motion.button>\r\n                        </>\r\n                      )}\r\n                    </div>\r\n\r\n                    <div id=\"paypal-section\" className={`paypal-form flex flex-col bg-white w-full lg:p-0 p-5 ${selectedMethod !== \"paypal\" ? \"!hidden\" : \"block\"}`}>\r\n                      {isLoading ? (\r\n                        <div className=\"flex justify-center items-center h-full\">\r\n                          <div className=\"animate-pulse flex flex-col bg-white w-full lg:p-0 p-5 lg:mt-3\">\r\n                            <div className=\"lg:hidden block h-20 bg-gray-200 rounded mb-3\"></div>\r\n                            <div className=\"h-4 bg-gray-200 rounded mb-3\"></div>\r\n                            <div className=\"h-4 bg-gray-200 w-2/12 rounded mb-7\"></div>\r\n                            <div className=\"h-10 bg-gray-200 rounded mb-7\"></div>\r\n                            <div className=\"h-4 bg-gray-200 w-8/12 mx-auto rounded py-2\"></div>\r\n                          </div>\r\n                        </div>\r\n                      ) : (\r\n                        <>\r\n                          <div className=\"lg:hidden block\">\r\n                              <label\r\n                                key={'paypal'}\r\n                                className={`payment-section lg:h-auto lg:p-5 items-center`}\r\n                              >\r\n                                <img\r\n                                  src={pplogoSRC}\r\n                                  alt={'paypal'}\r\n                                  className={`mx-auto mt-2 h-12`}\r\n                                  loading=\"lazy\"\r\n                                />\r\n                              </label>\r\n                            <hr className=\"border-black/5 rounded-full border-2 my-4 block lg:hidden\" />\r\n                          </div>\r\n\r\n                          <span className=\"text-[12px] text-gray-600 py-2 mb-5\">\r\n                            <FaInfoCircle className=\"inline text-xs mb-1 lg:mb-0 mr-1\" />\r\n                            By clicking the \"Pay with <strong className=\"italic\">Paypal</strong> \" button, I have read and agreed to the <a href=\"https://ai-pro.org/member-tos-page/\" rel=\"noopener noreferrer\" target=\"_blank\" className=\"font-medium text-[#0070BA] underline\">Terms and Conditions</a>.\r\n                          </span>\r\n                          <button className=\"cta-btn-pp border rounded-lg p-3 bg-[#0070BA] text-white text-md lg:text-xl font-medium mb-4 disabled:bg-opacity-50\" disabled={isLoading} onClick={() => submitPayPal()}>\r\n                            Pay with <strong className=\"italic\">PayPal</strong>\r\n                          </button>\r\n                          \r\n                          <span className=\"text-center text-xs py-1\">You will complete the checkout process using PayPal.</span>\r\n                        </>\r\n                      )}\r\n                    </div>\r\n\r\n                    <div id=\"googlepay-section\" className={`googlepay-form flex flex-col bg-white w-full lg:p-0 p-5 ${selectedMethod !== \"google-pay\" ? \"!hidden\" : \"block\"}`}>\r\n                      {isLoading ? (\r\n                        <div className=\"flex justify-center items-center h-full\">\r\n                          <div className=\"animate-pulse flex flex-col bg-white w-full lg:p-0 p-5 lg:mt-3\">\r\n                            <div className=\"lg:hidden block h-20 bg-gray-200 rounded mb-3\"></div>\r\n                            <div className=\"h-4 bg-gray-200 rounded mb-3\"></div>\r\n                            <div className=\"h-4 bg-gray-200 w-2/12 rounded mb-7\"></div>\r\n                            <div className=\"h-10 bg-gray-200 rounded mb-7\"></div>\r\n                            <div className=\"h-4 bg-gray-200 w-8/12 mx-auto rounded py-2\"></div>\r\n                          </div>\r\n                        </div>\r\n                      ) : (\r\n                        <>\r\n                          <div className=\"lg:hidden block\">\r\n                            <label\r\n                              key={`google-pay`}\r\n                              className={`payment-section lg:h-auto lg:p-5 items-center`}\r\n                            >\r\n                              <img\r\n                                src={gpIcon}\r\n                                alt={`google-pay`}\r\n                                className={`mx-auto w-1/2 py-2 h-14`}\r\n                                loading=\"lazy\"\r\n                              />\r\n                            </label>\r\n                            <hr className=\"border-black/5 rounded-full border-2 my-4 block lg:hidden\" />\r\n                          </div>\r\n                          <div className=\"flex items-center\">\r\n                            <span className={`text-[12px] text-gray-600 py-2 ${!isGoogleSupported ? 'mb-[1.1rem]' : 'mb-[2.25rem]'}`}>\r\n                            <FaInfoCircle className=\"inline text-xs mb-1 lg:mb-0 mr-1\" />\r\n                              By clicking the \"Buy with\r\n                              <FcGoogle className=\"inline mb-1 mx-0.5 filter grayscale-100 contrast-50\" size={15} />\r\n                              Pay\" button, I have read and agreed to the&nbsp;\r\n                              <a href=\"https://ai-pro.org/member-tos-page/\" rel=\"noopener noreferrer\" target=\"_blank\" className=\"font-medium text-[#0070BA] underline\">Terms and Conditions</a>.\r\n                            </span>\r\n                          </div>\r\n                          {/* <button className=\"p-3 mb-5 disabled:bg-opacity-50\" disabled={isLoading} id=\"googlepay-request-button\"></button> */}\r\n                          <button className={`cta-btn-gp border rounded-lg p-3 bg-[#0B0B0B] text-white text-md lg:text-xl font-medium ${!isGooglePay ? \"cursor-not-allowed opacity-25\" : \"\"}`} id=\"googlepay-request-button\" disabled={isLoading} onClick={handleGoogleButtonClick}>\r\n                            Buy with\r\n                            <FcGoogle className=\"inline mb-1 mx-0.5 ml-1\" size={20} />Pay\r\n                          </button>\r\n                          {/* {isNotSupported && (\r\n                           <span className=\"text-center text-xs text-red-500 py-2\">This Payment is not available on this device.</span>\r\n                          )} */}\r\n                          {/* {paymentRequest && isGooglePay && <PaymentRequestButtonElement options={{paymentRequest}} />} */}\r\n                          {!isGoogleSupported ? (\r\n                          <span className=\"text-center text-xs text-red-500 pt-7\">Sorry, but we couldn't detect Google Pay on your device. Please verify if your device supports Google Pay or try an alternative payment method.</span>\r\n                          ):(\r\n                            <span className=\"text-center text-xs pt-7\">You will complete the checkout process using Google Pay.</span>\r\n                          )}\r\n                        </>\r\n                      )}\r\n                    </div>\r\n\r\n                    <div id=\"applepay-section\" className={`applepay-form flex flex-col bg-white w-full lg:p-0 p-5 ${selectedMethod !== \"apple-pay\" ? \"!hidden\" : \"block\"}`}>\r\n                      {isLoading ? (\r\n                        <div className=\"flex justify-center items-center h-full\">\r\n                          <div className=\"animate-pulse flex flex-col bg-white w-full lg:p-0 p-5 lg:mt-3\">\r\n                            <div className=\"lg:hidden block h-20 bg-gray-200 rounded mb-3\"></div>\r\n                            <div className=\"h-4 bg-gray-200 rounded mb-3\"></div>\r\n                            <div className=\"h-4 bg-gray-200 w-2/12 rounded mb-7\"></div>\r\n                            <div className=\"h-10 bg-gray-200 rounded mb-7\"></div>\r\n                            <div className=\"h-4 bg-gray-200 w-8/12 mx-auto rounded py-2\"></div>\r\n                          </div>\r\n                        </div>\r\n                      ) : (\r\n                        <>\r\n                          <div className=\"lg:hidden block\">\r\n                            <label\r\n                              key={`apple-pay`}\r\n                              className={`payment-section lg:h-auto lg:p-5 items-center`}\r\n                            >\r\n                              <img\r\n                                src={apIcon}\r\n                                alt={`apple-pay`}\r\n                                className={`mx-auto w-[30%] py-2 pb-4 h-14`}\r\n                                loading=\"lazy\"\r\n                              />\r\n                            </label>\r\n                            <hr className=\"border-black/5 rounded-full border-2 mb-4 block lg:hidden\" />\r\n                          </div>\r\n                          <div className=\"flex items-center\">\r\n                            <span className={`text-[12px] text-gray-600 py-2 ${isAppleSupported ? 'mb-[1.1rem]' : 'mb-4'}`}>\r\n                              <FaInfoCircle className=\"inline text-sm mb-1 lg:mb-0 mr-1\" />\r\n                              By clicking the \"Buy with\r\n                              <FaApple className=\"inline mb-2 mx-0.5\" size={18} />\r\n                              Pay\" button, I have read and agreed to the&nbsp;\r\n                              <a href=\"https://ai-pro.org/member-tos-page/\" rel=\"noopener noreferrer\" target=\"_blank\" className=\"font-medium text-[#0070BA] underline\">Terms and Conditions</a>.\r\n                            </span>\r\n                          </div>\r\n                          {/* <button className=\"p-3 mb-5 disabled:bg-opacity-50\" disabled={isLoading} id=\"applepay-request-button\"></button> */}\r\n                          <button className={`cta-btn-ap border rounded-lg p-3 bg-[#0B0B0B] text-white text-md lg:text-xl font-medium ${!isApplePay ? \"cursor-not-allowed opacity-25\" : \"\"}`} id=\"applepay-request-button\" disabled={isLoading} onClick={handleAppleButtonClick}>\r\n                            Buy with\r\n                            <FaApple className=\"inline mb-1 mx-0.5 ml-1\" size={20} />Pay\r\n                          </button>\r\n                          {!isAppleSupported ? (\r\n                          <span className=\"text-center text-xs text-red-500 pt-5\">Sorry, but we couldn't detect Apple Pay on your device. Please verify if your device supports Apple Pay or try an alternative payment method.</span>\r\n                          ):(\r\n                            <span className=\"text-center text-xs pt-5\">You will complete the checkout process using Apple Pay.</span>\r\n                          )}\r\n                        </>\r\n                      )}\r\n                    </div>\r\n\r\n                    {/* </>\r\n                    )} */}\r\n                    {isRedirecting && (\r\n                      <div className=\"fixed top-0 left-0 w-full h-full flex flex-col items-center justify-center bg-white opacity-75 z-50\">\r\n                        <Loader />\r\n                        <span>Redirecting...</span>\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      ) : \"\"}\r\n    </>\r\n  );\r\n}\r\n\r\nexport default Payment;", "import React, { useState, useEffect } from 'react';\r\nimport '../style.css';\r\nimport { FaLock } from 'react-icons/fa';\r\nimport aiproLogo from '../../assets/images/AIPRO.svg';\r\nimport { Helmet } from 'react-helmet';\r\nimport ccv4 from '../../assets/images/ccv4.png';\r\nimport ccAuth from '../../assets/images/secure90x72.gif';\r\nimport PaymentForm from './index_05_payment_form';\r\nimport { GetCookie, SetCookie } from '../../core/utils/cookies';\r\nimport { useQuery } from \"react-query\";\r\nimport { Auth } from '../../core/utils/auth';\r\nimport axios from 'axios';\r\nimport { getPricePlan } from '../../core/utils/main';\r\n\r\nconst pricing = GetCookie(\"pricing\") ? GetCookie(\"pricing\") : \"\";\r\n\r\nlet plan = null;\r\n\r\nasync function getPlan() {\r\n  if (plan) return plan;\r\n  const response = await axios.post(\r\n    `${process.env.REACT_APP_API_URL}/get-plan`,\r\n    { plan_id: pricing },\r\n    { headers: { 'content-type': 'application/x-www-form-urlencoded' } }\r\n  );\r\n  const output = response.data;\r\n  if (output.success) {\r\n    plan = output.data;\r\n    return output.data;\r\n  } else {\r\n    return [];\r\n  }\r\n}\r\n\r\nconst SecureCheckout = () => (\r\n  <>\r\n    <div className=\"securetext flex mb-2 w-full text-white mt-5 lg:mt-14 lg:px-0 px-5  md:mb-5 lg:mb-0 justify-start text-center my-auto lg:mx-0 text-xl\"><FaLock className=\"inline mr-1 text-[#F97316] mt-0.5 lg:block\" /> Secure Checkout</div>\r\n   <div className=\"md:flex md:flex-1 lg:block lg:flex-none \">\r\n    <img src={ccv4} alt=\"secure-checkout\" className=\"secure-checkout h-auto lg:px-0 px-5 mb-5 w-full md:w-7/12 md:h-10 lg:h-auto lg:mx-0 lg:mt-2 lg:w-full\" />\r\n    <div className=\"flex justify-center md:justify-start lg:justify-center gap-3 h-16 w-full mb-5 md:mb-5 lg:mb-5 md:-mt-2 lg:mt-0\">\r\n    <img src={ccAuth} alt=\"Authorize\" className=\"h-12 mt-1 lg:h-12 \"/>\r\n    <button\r\n      onClick={() => {\r\n        window.open(\r\n          '//www.securitymetrics.com/site_certificate?id=1982469&tk=d41c416c6208f1136426bc8038178d2e',\r\n          'Verification',\r\n          'location=no, toolbar=no, resizable=no, scrollbars=yes, directories=no, status=no,top=100,left=100, width=700, height=600'\r\n        );\r\n      }}\r\n      title=\"SecurityMetrics card safe certification\"\r\n      className=\"\" >\r\n      <img loading=\"lazy\"\r\n        src=\"https://www.securitymetrics.com/portal/app/ngsm/assets/img/GreyContent_Credit_Card_Safe_White_Sqr.png\"\r\n        alt=\"SecurityMetrics card safe certification logo\"\r\n        className=\"h-12 lg:h-12\" />\r\n    </button>\r\n    </div>\r\n    </div>\r\n  </>\r\n\r\n)\r\n\r\nfunction PaymentV5() {\r\n  const { data } = useQuery(\"users\", getPlan);\r\n  const [windowWidth, setWindowWidth] = useState(window.innerWidth);\r\n  const [selectedMethod, setSelectedMethod] = useState(null);\r\n  const auth = Auth('/register-auth');\r\n  const willRedirect = auth && auth.status === 'active' && auth.expired === 'no';\r\n\r\n\r\n  useEffect(() => {\r\n    const handleResize = () => {\r\n      setWindowWidth(window.innerWidth);\r\n    };\r\n\r\n    window.addEventListener('resize', handleResize);\r\n\r\n    return () => {\r\n      window.removeEventListener('resize', handleResize);\r\n    };\r\n  }, []);\r\n\r\n  const handlePaymentClick = () => {\r\n    if (windowWidth >= 1024) {\r\n      setSelectedMethod('creditCard');\r\n      // console.log('selectedmethod=', selectedMethod);\r\n    } else {\r\n      setSelectedMethod(null);\r\n      // console.log('selectedmethod: null');\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (windowWidth >= 1024 && selectedMethod === null) {\r\n      setSelectedMethod('creditCard');\r\n    }\r\n  }, [windowWidth, selectedMethod]);\r\n\r\n\r\n\r\n  if (!auth) return null;\r\n\r\n  if (willRedirect) {\r\n    window.location.href = '/my-account';\r\n    return null;\r\n  }\r\n\r\n  const date = new Date();\r\n  date.setTime(date.getTime() + 30 * 24 * 60 * 60 * 1000);\r\n  const today = new Date();\r\n  const expire_date = new Date();\r\n  expire_date.setDate(today.getDate() + 30);\r\n\r\n  if (data && data.currency && data.price) {\r\n    let amount = data.price;\r\n    let planName = data.plan_name;\r\n    if (data.trial_price !== '') {\r\n      amount = data.trial_price;\r\n    }\r\n    SetCookie('currency', data.currency, { expires: expire_date, path: '/' });\r\n    SetCookie('currency', data.currency, { expires: expire_date, domain: '.ai-pro.org', path: '/' });\r\n    SetCookie('amount', amount, { expires: expire_date, path: '/' });\r\n    SetCookie('amount', amount, { expires: expire_date, domain: '.ai-pro.org', path: '/' });\r\n    SetCookie('planName', planName, { expires: expire_date, path: '/' });\r\n    SetCookie('planName', planName, { expires: expire_date, domain: '.ai-pro.org', path: '/' });\r\n  }\r\n\r\n  return (\r\n    <>\r\n      <Helmet>\r\n        <title>AI Pro | Payment Option</title>\r\n        <meta name=\"description\" content=\"Safely complete your purchase with our secure payment options. Buy now with confidence!\" />\r\n      </Helmet>\r\n      {/* <Header auth={auth} /> */}\r\n      {data ?\r\n        <div className=\"Payment flex w-full md:pt-[50px]\">\r\n          <div className=\"mx-auto w-full\">\r\n            <div className=\"flex flex-col items-center lg:py-8\">\r\n              <div className=\"flex flex-wrap md:flex-wrap justify-center w-full\">\r\n                <div className=\"pay_left px-2 lg:px-4 mb-8 w-full\">\r\n                  <div className=\"lg:bg-[#D9D9D9] lg:bg-opacity-20 rounded-lg max-w-[1336px] mx-auto\">\r\n                    <div className=\"p-2 lg:p-16 w-full\">\r\n                      <div className=\"block\">\r\n                      <img src={aiproLogo} alt=\"AI-Pro Logo\" className=\"aiprologo text-center mb-1\" width=\"200\" height=\"52\" />\r\n                      </div>\r\n                      <div className=\"container-form flex lg:flex-row flex-col gap-10 justify-between w-full\">\r\n                        <div className=\"flex-none bg-gradient-to-br from-[#2872FA] via-[#2A73FA]/75 to-[#2A73FA] rounded-3xl shadow-lg flex flex-col lg:p-10 w-full lg:w-5/12 lg:h-[570px] xl:h-[530px]\">\r\n                          <div className=\"flex bg-white p-2 px-5 mt-7 lg:mt-0 -ml-[10px] lg:!-ml-[50px] text-[#2872FA] w-8/12 md:w-4/12 lg:w-3/5 font-bold border-[4px] border-[#D9D9D9] rounded-tl-lg\">\r\n                            <div className={`flex flex-row mx-auto ${selectedMethod !== null ? `hidden lg:block` : `block`}`}>\r\n                              <div className=\"flex justify-center items-center\">\r\n                                <svg width=\"16\" height=\"16\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                                  <path d=\"M13.1121 15.8429H2.89323C2.16759 15.8429 1.47168 15.5547 0.958582 15.0416C0.445483 14.5285 0.157227 13.8326 0.157227 13.1069V2.88808C0.158589 2.16334 0.447446 1.46875 0.960398 0.956763C1.47335 0.444776 2.16848 0.157225 2.89323 0.157227H13.1121C13.4707 0.157227 13.8258 0.227862 14.1571 0.365101C14.4885 0.502339 14.7895 0.703493 15.0431 0.957076C15.2967 1.21066 15.4978 1.51171 15.6351 1.84303C15.7723 2.17435 15.8429 2.52946 15.8429 2.88808V13.1069C15.8429 13.8317 15.5554 14.5268 15.0434 15.0398C14.5314 15.5527 13.8368 15.8416 13.1121 15.8429ZM2.89323 1.44294C2.70302 1.44226 2.51454 1.47914 2.33861 1.55147C2.16269 1.62379 2.00277 1.73013 1.86803 1.86439C1.73329 1.99865 1.62638 2.15819 1.55344 2.33386C1.48049 2.50953 1.44294 2.69787 1.44294 2.88808V13.1069C1.44294 13.4916 1.59574 13.8605 1.86772 14.1324C2.1397 14.4044 2.50859 14.5572 2.89323 14.5572H13.1121C13.3023 14.5572 13.4906 14.5197 13.6663 14.4467C13.842 14.3738 14.0015 14.2669 14.1358 14.1321C14.27 13.9974 14.3764 13.8375 14.4487 13.6616C14.521 13.4856 14.5579 13.2972 14.5572 13.1069V2.88808C14.5572 2.50481 14.405 2.13723 14.134 1.86621C13.8629 1.5952 13.4954 1.44294 13.1121 1.44294H2.89323Z\" fill=\"#3775FF\" />\r\n                                  <path d=\"M5.95314 8.64294C5.77561 8.64234 5.60025 8.60376 5.43886 8.5298C5.21313 8.42725 5.02202 8.26138 4.88873 8.05232C4.75545 7.84326 4.68571 7.60001 4.688 7.35208V0.800084C4.688 0.629587 4.75573 0.466074 4.87629 0.345515C4.99685 0.224956 5.16036 0.157227 5.33086 0.157227C5.50135 0.157227 5.66487 0.224956 5.78543 0.345515C5.90599 0.466074 5.97371 0.629587 5.97371 0.800084V7.35208L7.18229 6.28237C7.40942 6.0796 7.70324 5.96752 8.00771 5.96752C8.31219 5.96752 8.60601 6.0796 8.83314 6.28237L10.0726 7.35723L10.0314 0.800084C10.0314 0.629587 10.0992 0.466074 10.2197 0.345515C10.3403 0.224956 10.5038 0.157227 10.6743 0.157227C10.8448 0.157227 11.0083 0.224956 11.1289 0.345515C11.2494 0.466074 11.3171 0.629587 11.3171 0.800084V7.35208C11.3194 7.60001 11.2497 7.84326 11.1164 8.05232C10.9831 8.26138 10.792 8.42725 10.5663 8.5298C10.3473 8.63052 10.1037 8.66519 9.86525 8.62956C9.62684 8.59394 9.404 8.48957 9.224 8.32923L8 7.2698L6.776 8.33437C6.54861 8.53428 6.25591 8.64404 5.95314 8.64294ZM8.77143 12.6647H4.14286C3.97236 12.6647 3.80885 12.5969 3.68829 12.4764C3.56773 12.3558 3.5 12.1923 3.5 12.0218C3.5 11.8513 3.56773 11.6878 3.68829 11.5672C3.80885 11.4467 3.97236 11.3789 4.14286 11.3789H8.77143C8.94193 11.3789 9.10544 11.4467 9.226 11.5672C9.34656 11.6878 9.41429 11.8513 9.41429 12.0218C9.41429 12.1923 9.34656 12.3558 9.226 12.4764C9.10544 12.5969 8.94193 12.6647 8.77143 12.6647Z\" fill=\"#3775FF\" />\r\n                                </svg>\r\n                                <p className=\"flex text-sm md:text-lg lg:text-base justify-center items-center my-auto ml-2\">Order Summary</p>\r\n                              </div>\r\n                            </div>\r\n                            <div className={`flex flex-row mx-auto ${selectedMethod !== null ? `block lg:hidden` : `hidden`}`}>\r\n                              <div className=\"flex justify-center items-center\">\r\n                                <button className=\"flex text-xs md:text-lg gap-2 lg:text-base justify-center items-center my-auto\" onClick={handlePaymentClick}>\r\n                                <svg width=\"16\" height=\"16\" viewBox=\"0 0 17 13\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                                  <path d=\"M0.46967 5.96967C0.176777 6.26256 0.176777 6.73744 0.46967 7.03033L5.24264 11.8033C5.53553 12.0962 6.01041 12.0962 6.3033 11.8033C6.59619 11.5104 6.59619 11.0355 6.3033 10.7426L2.06066 6.5L6.3033 2.25736C6.59619 1.96447 6.59619 1.48959 6.3033 1.1967C6.01041 0.903806 5.53553 0.903806 5.24264 1.1967L0.46967 5.96967ZM15.5 7.25C15.9142 7.25 16.25 6.91421 16.25 6.5C16.25 6.08579 15.9142 5.75 15.5 5.75V7.25ZM1 7.25H15.5V5.75H1V7.25Z\" fill=\"#407FF5\" />\r\n                                </svg>Payment Method</button>\r\n                              </div>\r\n                            </div>\r\n                          </div>\r\n\r\n                          <div className=\"w-0 h-0 -ml-[10px] lg:!-ml-[50px] border-l-[10px] border-l-transparent border-t-[5px] border-t-[#D9D9D9] border-r-[0px] border-r-transparent mb-7\">\r\n                          </div>\r\n                          <p className=\"uppercase font-bold text-3xl lg:text-[32px] text-white text-center lg:text-start\">\r\n                            {data.plan_type_display} PLAN\r\n                          </p>\r\n                          <span className=\"text-sm text-center lg:text-start font-light text-[#FFFFFF] my-3 mx-5 lg:mx-0\">\r\n                            { data.display_txt3 ? data.display_txt3 : \"Your subscription will renew monthly until you cancel it.\"}\r\n                          </span>\r\n                          <div className=\"lg:px-0 px-5\">\r\n                            <div className=\"border bg-transparent p-1 px-5 justify-between items-center text-white text-[20px] font-bold flex flex-wrap mb-2 mt-4 mr-6 w-full\">\r\n                              <span className=\"total\">\r\n                                Total:\r\n                              </span>\r\n                              <span className=\"price\">\r\n                              {getPricePlan(data.currency, data.price)}\r\n                              </span>\r\n                            </div>\r\n                          </div>\r\n                          <div className={`pay_right_m block lg:hidden items-center ${selectedMethod !== null ? \"my-5\" : \"mt-5\"}`}>\r\n                            {windowWidth < 1024 && <PaymentForm selectedMethod={selectedMethod} setSelectedMethod={setSelectedMethod} />}\r\n                          </div>\r\n                          <SecureCheckout />\r\n                        </div>\r\n                        <div className=\"pay_right flex-none bg-white rounded-3xl shadow items-center w-7/12 hidden min-h-auto lg:block p-8 min-h-[570px] xl:min-h-[530px]\">\r\n                          {windowWidth >= 1024 && <PaymentForm selectedMethod={selectedMethod} setSelectedMethod={setSelectedMethod} />}\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div> : \"\"}\r\n    </>\r\n  );\r\n}\r\n\r\nexport default PaymentV5;", "import React from 'react';\r\nimport PaymentV5 from './components/index_05_payment_receipt';\r\nimport { loadStripe } from \"@stripe/stripe-js\";\r\nimport { Elements } from \"@stripe/react-stripe-js\";\r\nimport { GetCookie } from '../core/utils/cookies';\r\n\r\nconst mode = GetCookie(\"mode\") ? GetCookie(\"mode\") : \"\";\r\n\r\nvar publishableKey = '';\r\nif (mode==='test'){\r\n  publishableKey = process.env.REACT_APP_STRIPE_PUBLIC_KEY_TEST;\r\n}else{\r\n  publishableKey = process.env.REACT_APP_STRIPE_PUBLIC_KEY_LIVE;\r\n}\r\n\r\nconst stripePromise = loadStripe(publishableKey)\r\n\r\nfunction Payment() {\r\n  return (\r\n    <>\r\n  <Elements stripe={stripePromise}>\r\n    <PaymentV5 />\r\n  </Elements>\r\n    </>\r\n  );\r\n}\r\n\r\nexport default Payment;"], "names": ["getCurrency", "currency", "toLowerCase", "getCountry", "formatDate", "dateStr", "date", "Date", "replace", "getMonth", "getDate", "getFullYear", "getPrice", "data", "trial_price", "price", "numberWithCommas", "x", "toString", "formatNumber", "number", "floatNumber", "parseFloat", "toFixed", "getPricePlan", "isNaN", "toLocaleString", "diffMin", "dt1", "dt2", "diff", "getTime", "Math", "abs", "round", "formatDateTime", "getHours", "getMinutes", "DailyPrice", "_ref", "plan", "dailyPrice", "interval", "payment_interval", "_jsxs", "class", "children", "trial_days", "_Fragment", "className", "Get<PERSON><PERSON><PERSON>", "_jsx", "PriceFormatted", "_ref2", "getPrefixLocation", "currentLocation", "window", "location", "href", "indexOf", "Loader", "viewBox", "fill", "xmlns", "role", "d", "<PERSON><PERSON><PERSON><PERSON>", "onClick", "isLoading", "isSelected", "buttonClass", "method", "htmlFor", "label", "src", "alt", "disabled", "FaCircleCheck", "loading", "id", "dangerouslySetInnerHTML", "__html", "pricing", "tk", "pmt", "cta_pmt", "pplogoSRC", "webpSrc", "pplogowebP", "pngSrc", "pplogoPNG", "isWebpSupported", "elem", "document", "createElement", "getContext", "toDataURL", "convertWebpToPng", "async", "getPlan", "output", "axios", "post", "plan_id", "headers", "success", "setSelectedMethod", "auth", "<PERSON><PERSON>", "stripe", "useStripe", "useQuery", "name", "setName", "useState", "cardNumber", "setCardNumber", "cardDate", "setCardDate", "cvv", "setCVV", "nameError", "setNameError", "cardNumberError", "setCardNumberError", "cardDateError", "setCardDateError", "cvvError", "setCVVError", "willRedirect", "setWillRedirect", "isRedirecting", "setRedirecting", "isGooglePay", "setIsGooglePay", "isApplePay", "setIsApplePay", "isGoogleSupported", "setIsGoogleSupported", "isAppleSupported", "setIsAppleSupported", "paymentRequest", "setPaymentRequest", "setIsLoading", "isLoadingCreditCard", "setIsLoadingCreditCard", "is<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setIsLoadingPaypal", "isLoadingGooglePay", "setIsLoadingGooglePay", "isLoadingApplePay", "setIsLoadingApplePay", "useEffect", "toastr", "positionClass", "threed_error", "undefined", "setTimeout", "<PERSON><PERSON><PERSON><PERSON>", "country", "pr", "total", "plan_type", "amount", "requestPayerName", "requestPayerEmail", "canMakePayment", "then", "result", "googlePay", "applePay", "console", "log", "on", "paymentMethodId", "e", "paymentMethod", "payer_email", "payerEmail", "payerName", "clientReferenceId", "elements", "getElementsByName", "value", "querySelector", "classList", "add", "payment_menthod_id", "email", "cus_name", "client_reference_id", "complete", "RemoveCookie", "msg", "remove", "error", "response", "status", "expired", "setTime", "today", "expire_date", "setDate", "expires", "path", "domain", "submitPayPal", "res", "link", "handleResponse", "handlePaymentMethodClick", "PaymentButton", "ccIcon", "ppIcon", "gpIcon", "apIcon", "type", "placeholder", "onChange", "event", "input", "target", "slice", "onKeyUp", "CiCreditCard1", "size", "length", "FaInfoCircle", "rel", "motion", "button", "whileHover", "backgroundColor", "whileTap", "scale", "submitPayment", "<PERSON><PERSON><PERSON><PERSON>", "includes", "test", "name_split", "split", "first_name", "last_name", "ccmonth", "ccyr", "url", "cc", "psource", "redirect", "catch", "FcGoogle", "handleGoogleButtonClick", "show", "FaApple", "handleAppleButtonClick", "SecureCheckout", "FaLock", "ccv4", "ccAuth", "open", "title", "windowWidth", "setW<PERSON>owWidth", "innerWidth", "handleResize", "addEventListener", "removeEventListener", "planName", "plan_name", "<PERSON><PERSON><PERSON>", "content", "aiproLogo", "width", "height", "handlePaymentClick", "plan_type_display", "display_txt3", "PaymentForm", "publishableKey", "process", "stripePromise", "loadStripe", "Elements", "PaymentV5"], "sourceRoot": ""}