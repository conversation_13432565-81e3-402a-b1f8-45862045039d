{"version": 3, "file": "static/js/7919.7cc9a7ba.chunk.js", "mappings": "oIAEA,MAQA,EARkBA,IAA6B,IAA5B,IAAEC,EAAG,IAAEC,KAAQC,GAAOH,EACvC,OACEI,EAAAA,EAAAA,KAACC,EAAAA,SAAQ,CAACC,UAAUF,EAAAA,EAAAA,KAAA,OAAAG,SAAK,eAAiBA,UACxCH,EAAAA,EAAAA,KAAA,OAAKH,IAAKA,EAAKC,IAAKA,KAASC,M,uFCH5B,SAASK,IACd,MAAMC,EAAOC,SAASC,cAAc,UACpC,SAAOF,EAAKG,aAAcH,EAAKG,WAAW,QAC2B,IAA5DH,EAAKI,UAAU,cAAcC,QAAQ,kBAGhD,CAgDO,SAASC,EAAQC,GAEtB,OAAW,MAAPA,GAAeA,EAAIC,OAAS,IADf,eAC8BC,KAAKF,GAC3C,UAKF,IAFPA,EAAMA,EAAIG,MAAM,EAAG,IAGrB,CAGO,SAASC,EAAYJ,IAEf,MAAPA,GAAeA,EAAIC,OAAS,IADf,eAC8BC,KAAKF,MAClDA,EAAM,UAGR,IAAIK,EAAIC,SAASN,EAAIG,MAAM,EAAG,GAAI,IAC9BI,EAAID,SAASN,EAAIG,MAAM,EAAG,GAAI,IAC9BK,EAAIF,SAASN,EAAIG,MAAM,EAAG,GAAI,IAElCE,EAAII,KAAKC,IAAI,EAAGD,KAAKE,IAAI,IAAKN,EAAS,IAAJA,IACnCE,EAAIE,KAAKC,IAAI,EAAGD,KAAKE,IAAI,IAAKJ,EAAS,IAAJA,IACnCC,EAAIC,KAAKC,IAAI,EAAGD,KAAKE,IAAI,IAAKH,EAAS,IAAJA,IAEnC,MAAMI,EAASC,GACIJ,KAAKK,MAAMD,GAAOE,SAAS,IAC5BC,SAAS,EAAG,KAG9B,MAAO,IAAIJ,EAAMP,KAAKO,EAAML,KAAKK,EAAMJ,IACzC,CAEO,SAASS,IACd,MAAOC,EAAUC,IAAgBC,EAAAA,EAAAA,WAAS,IACnCC,EAAUC,IAAeF,EAAAA,EAAAA,WAAS,IAClCG,EAAWC,IAAgBJ,EAAAA,EAAAA,WAAS,GAmB3C,OAjBAK,EAAAA,EAAAA,WAAU,KACR,MAAMC,EAAeA,KACnB,MAAMC,EAAQC,OAAOC,WAErBV,EAAaQ,EAAQ,KACrBL,EAAYK,EAAQ,KAAOA,GAAS,KACpCH,EAAaG,EAAQ,MAMvB,OAHAD,IACAE,OAAOE,iBAAiB,SAAUJ,GAE3B,KACLE,OAAOG,oBAAoB,SAAUL,KAEtC,IAEI,CAAER,WAAUG,WAAUE,YAC/B,C,6DC9GO,SAASS,IACd,IAAIC,GAAiBC,EAAAA,EAAAA,IAAU,kBAC3BC,GAAa,EACjB,GAAIF,SAAwE,KAAjBA,EAAoB,CAC7E,IAAIG,EAAQH,EAAeI,MAAM,UACV,IAAbD,EAAM,IACC,MAAXA,EAAM,KACRD,GAAa,EAGnB,CAEA,IAAKA,EACH,OAGF,MAAMG,EAAMC,CAAAA,SAAAA,aAAAA,WAAAA,eAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,cAAAA,EAAAA,kBAAAA,4BAAAA,mBAAAA,yBAAAA,oCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,kCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,2BAAAA,iBAAAA,4BAAAA,cAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,oBAAAA,uBAAAA,yBAAAA,8BAAAA,yBAAAA,gFAAAA,uBAAAA,6CAAAA,sBAAAA,KAAAA,uBAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,wBAAAA,KAAAA,mBAAAA,aAAAA,qCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,mCAAAA,KAAAA,8BAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,4BAAAA,4BAAAA,gCAAAA,oBAAAA,gCAAAA,oBAAAA,gCAAAA,oBAAAA,gCAAAA,oBAAAA,kCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,gCAAAA,KAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,iCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,6BAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,yBAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,iCAAAA,8GAAAA,iCAAAA,8GAAAA,oBAAAA,8jBAAYC,qBAClBC,GAASC,EAAAA,EAAAA,IAAGJ,GAClB,IAAIK,EAAO,GAOX,OANGT,EAAAA,EAAAA,IAAU,aACXS,GAAOT,EAAAA,EAAAA,IAAU,YAGnBO,EAAOG,KAAK,mBAAoB,CAAEC,GAAIF,EAAMG,OAAOZ,EAAAA,EAAAA,IAAU,cAAea,WAAYR,CAAAA,SAAAA,aAAAA,WAAAA,eAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,cAAAA,EAAAA,kBAAAA,4BAAAA,mBAAAA,yBAAAA,oCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,kCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,2BAAAA,iBAAAA,4BAAAA,cAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,oBAAAA,uBAAAA,yBAAAA,8BAAAA,yBAAAA,gFAAAA,uBAAAA,6CAAAA,sBAAAA,KAAAA,uBAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,wBAAAA,KAAAA,mBAAAA,aAAAA,qCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,mCAAAA,KAAAA,8BAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,4BAAAA,4BAAAA,gCAAAA,oBAAAA,gCAAAA,oBAAAA,gCAAAA,oBAAAA,gCAAAA,oBAAAA,kCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,gCAAAA,KAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,iCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,6BAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,yBAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,iCAAAA,8GAAAA,iCAAAA,8GAAAA,oBAAAA,8jBAAYS,uBAE7F,CAAEP,SAAQE,OACnB,C,6QCHA,MAAMM,EAAS,IAAIC,gBAAgBtB,OAAOuB,SAASC,QAEnD,IAAIC,EA4tBJ,QA1tBA,WACE,MAAMC,GAAU9D,EAAAA,EAAAA,MAAoB+D,EAAWC,EACzCC,GAAcjE,EAAAA,EAAAA,MAAoBkE,EAAeC,GAChDb,EAAOc,IAAYxC,EAAAA,EAAAA,UAAS,KAC5ByC,EAAUC,IAAe1C,EAAAA,EAAAA,UAAS,KAClC2C,EAAYC,IAAiB5C,EAAAA,EAAAA,UAAS,KACtC6C,EAAeC,IAAoB9C,EAAAA,EAAAA,UAAS,KAC5C+C,EAAYC,IAAiBhD,EAAAA,EAAAA,YAClCQ,OAAOyC,UAAU,kBAEZC,EAAeC,IAAoBnD,EAAAA,EAAAA,UAAS,KAC5CoD,EAAQC,IAAarD,EAAAA,EAAAA,WAAS,GAE/BsD,GAAOC,EAAAA,EAAAA,MACPC,GAAe1C,EAAAA,EAAAA,IAAU,iBAAmB,MAC5C2C,GAAgBC,EAAAA,EAAAA,QAAO,IACvBC,GAAUD,EAAAA,EAAAA,QAAO,MACjBE,GAAaF,EAAAA,EAAAA,QAAO,MAG1B,IAAIG,EAAkBrD,OAAOuB,SAAS+B,KAClCC,EAAS,GACTF,EAAgBnF,QAAQ,YAAc,EACxCqF,EAAS,WACAF,EAAgBnF,QAAQ,QAAU,IAC3CqF,EAAS,QAGX,MAAMC,EAAkBC,IACtBzD,OAAOuB,SAAS+B,KAAOG,GAGnBC,EAAYA,CAACC,EAAKC,KACtB,MAAMC,EAAc7D,OAAO8D,OAAO/D,MAM5BgE,EAAiB,6BALF/D,OAAO8D,OAAOE,OAEd,KAEuB,WAD9BH,EAFM,KAEuB,IAI3C,IACoB7D,OAAOiE,IAAIC,KAAKP,EAAKC,EAAMG,KAG3C/D,OAAOiE,IAAI1C,SAAS+B,KAAOK,EAE/B,CAAE,MAAOQ,GAEPnE,OAAOiE,IAAI1C,SAAS+B,KAAOK,CAC7B,GAGIS,GAAiBC,EAAAA,EAAAA,aAAY,KACZ,OAAjBrB,GACFC,EAAcqB,QAAQC,QAAQ,CAACC,EAAKC,KAClC,GAAID,EAAK,CAAC,IAADE,EACP,MAAMC,EAASH,EAAII,YACbC,EAAaL,EAAIM,aACjBd,EAAWW,EACE,IAAfE,EACEA,GACiB,QAAjBH,EAACF,EAAIO,oBAAY,IAAAL,EAAAA,EAAI,GAAK,GAC5B,EAEJM,WAAW,KACTR,EAAIS,MAAMjB,OAAS,GAAGA,OACrB,IACL,KAGH,CAAChB,IAgEJ,GA9DAgC,WAAW,KACT,MAAME,EAAO/B,EAAQmB,QACfa,EACJC,UAAUC,UAAUC,SAAS,WAC7BF,UAAUC,UAAUC,SAAS,OAE/B,GAAIJ,GAAQC,EAAY,CACtB,MAAMI,EAASL,EAAKM,iBAAiB,SAEjCD,GAAUA,EAAOlH,OAAS,GAC5BkH,EAAOhB,QAASkB,IACVA,EAAMC,QAAQ,iCAChBD,EAAME,UAAUC,IAAI,eAI5B,GACC,MAEH/F,EAAAA,EAAAA,WAAU,KACR4B,GAAYrB,EAAAA,EAAAA,KACZyF,IAAAA,QAAiB,CACfC,cAAe,qBAIbxF,EAAAA,EAAAA,IAAU,QACZyF,EAAAA,EAAAA,IAAU,KAAM,GAAI,CAAEtC,KAAM,IAAKuC,QAAS,IAAIC,KAAK,MAErD,KAEFpG,EAAAA,EAAAA,WAAU,KAER,IAAIqG,EAAoB7E,EAAO8E,IAAI,KAC/BC,EAAe,GACK,OAApBF,GAAgD,KAApBA,IAC9BE,EAAeC,EAAOC,KAAKlJ,IAAA,IAAC,WAAEmJ,GAAYnJ,EAAA,OAAKmJ,IAAeL,SAC3CM,IAAfJ,EACFP,IAAAA,MAAaO,EAAaK,KAE1BZ,IAAAA,MAAaK,KAGhB,KAEHrG,EAAAA,EAAAA,WAAU,KACR,GAAqB,OAAjBmD,EAIF,OAHAoB,IACApE,OAAOE,iBAAiB,SAAUkE,GAE3B,KACLpE,OAAOG,oBAAoB,SAAUiE,KAGxC,CAACpB,EAAcoB,KAElBvE,EAAAA,EAAAA,WAAU,KACa,OAAjBmD,GACFoB,KAED,CAACjC,EAAYE,EAAeW,EAAcoB,SAEhCoC,IAAT1D,EAAoB,OAAO,KAC/B,GAAIA,EAEF,OADAU,EAAe,eACR,KAGT,MAyBMkD,EAA+B,KAAjBxF,EAAMyF,QAAqC,KAApB1E,EAAS0E,OAC9CC,GAAWtG,EAAAA,EAAAA,IAAU,MAE3B,SAASuG,IACP,IAAIC,EA7BgBC,MACpB,IAAIC,GAAW,EASf,OARK9F,EAEO,eAAe5C,KAAK4C,IAG9BkB,EAAc,IACd4E,GAAW,GAHX5E,EAAc,wBAFdA,EAAc,qBAOT4E,GAmBaD,GAChBE,EAjBmBC,MACvB,IAAIF,GAAW,EAOf,OANK/E,GAGHK,EAAiB,IACjB0E,GAAW,GAHX1E,EAAiB,wBAKZ0E,GASgBE,GAClBJ,GAAkBG,IAEvBnJ,SAASqJ,cAAc,qBAAqBxB,UAAUC,IAAI,UAC1DwB,EAAAA,EACGC,KACC,kCACA,CACEnG,QACAe,YAEF,CAAEqF,QAAS,CAAE,eAAgB,uCAE9BC,KAAK,SAAUC,GACd,IAAIC,EAASD,EAAIE,KAMjB,GAJ2B,eAAvBD,EAAOC,KAAKC,SACd3H,OAAOuB,SAAS+B,KAAO,GAAGmE,EAAOC,KAAKE,uBAAuBH,EAAOC,KAAKxG,SAGvEuG,EAAOI,QAAS,CAClB,GAAuB,IAAnBJ,EAAOI,QAAe,CAOxB,GANA/J,SACGqJ,cAAc,qBACdxB,UAAUmC,OAAO,UACpBtF,GAAc,GACdG,EAAiB8E,EAAOC,KAAKjB,KAEzBhF,EAAW,CACAA,EAAUZ,OAChBG,KAAK,iBAAkB,CAC5BC,GAAI,KACJC,QACAC,WAAYR,CAAAA,SAAAA,aAAAA,WAAAA,eAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,cAAAA,EAAAA,kBAAAA,4BAAAA,mBAAAA,yBAAAA,oCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,kCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,2BAAAA,iBAAAA,4BAAAA,cAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,oBAAAA,uBAAAA,yBAAAA,8BAAAA,yBAAAA,gFAAAA,uBAAAA,6CAAAA,sBAAAA,KAAAA,uBAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,wBAAAA,KAAAA,mBAAAA,aAAAA,qCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,mCAAAA,KAAAA,8BAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,4BAAAA,4BAAAA,gCAAAA,oBAAAA,gCAAAA,oBAAAA,gCAAAA,oBAAAA,gCAAAA,oBAAAA,kCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,gCAAAA,KAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,iCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,6BAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,yBAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,iCAAAA,8GAAAA,iCAAAA,8GAAAA,oBAAAA,8jBAAYS,sBAE5B,CACA,MACF,CAWA,OAVAyE,IAAAA,QAAe,YACfE,EAAAA,EAAAA,IAAU,SAAU0B,EAAOC,KAAM,CAAEjE,KAAM,OACzCsC,EAAAA,EAAAA,IAAU,aAAc,EAAG,CAAEtC,KAAM,WAGlB,UAAbmD,EACF5G,OAAOiE,IAAI1C,SAAS+B,KAAO,cAE3BE,EAAe,eAGnB,CACF1F,SAASqJ,cAAc,qBAAqBxB,UAAUmC,OAAO,UAC1DL,EAAOC,KAAKjB,KAAKZ,IAAAA,MAAa4B,EAAOC,KAAKjB,IAC/C,GACF,CAEA,MAIMsB,EAAeA,CAACC,EAAOC,KAC3B,MAAMC,EAAKF,EAAMG,OACXlJ,EAAQiJ,EAAGjJ,MACXmJ,EAAyB,UAAfJ,EAAMK,KAChBC,EAAyB,WAAfN,EAAMK,MAAoC,UAAfL,EAAMK,KAQjD,GANMpJ,GAASmJ,GAAWE,EACxBJ,EAAGvC,UAAUC,IAAI,cAEjBsC,EAAGvC,UAAUmC,OAAO,cAGD,OAAjB9E,GAA2C,OAAjBA,GAAyBsF,EACrD,OAAQL,GACN,IAAK,QACH7F,EAAc,IACdJ,EAAS/C,GACT,MACF,IAAK,WACHqD,EAAiB,IACjBJ,EAAYjD,GACZ,MACF,QACwB,KAAlB+I,EAAMO,SACR1B,MAOV,OACE2B,EAAAA,EAAAA,MAAAC,EAAAA,SAAA,CAAA9K,SAAA,EACE6K,EAAAA,EAAAA,MAACE,EAAAA,EAAM,CAAA/K,SAAA,EACLH,EAAAA,EAAAA,KAAA,SAAAG,SAAO,oBACPH,EAAAA,EAAAA,KAAA,QACEoG,KAAK,cACL+E,QAAQ,wJAGE,UAAb/B,GAEC4B,EAAAA,EAAAA,MAAA,OAAKI,UAAU,sEAAqEjL,SAAA,EAClFH,EAAAA,EAAAA,KAAA,OAAKoL,UAAU,kBAAiBjL,UAC9B6K,EAAAA,EAAAA,MAAA,OAAKI,UAAU,+BAA8BjL,SAAA,EAC3C6K,EAAAA,EAAAA,MAAA,OAAKI,UAAU,mBAAkBjL,SAAA,EAC/BH,EAAAA,EAAAA,KAAA,OAAKH,IAAKwL,EAAWvL,IAAI,cAAcsL,UAAU,uBACjDpL,EAAAA,EAAAA,KAAA,MAAIoL,UAAU,wCAAuCjL,SAAC,eAIxD6K,EAAAA,EAAAA,MAAA,OAAKI,UAAU,YAAWjL,SAAA,EACxB6K,EAAAA,EAAAA,MAAA,SAAOI,UAAU,wBAAuBjL,SAAA,EACtCH,EAAAA,EAAAA,KAAA,SACEoL,UAAW,wLACTzG,EACI,gCACA,mBAEN2G,YAAY,sBACZ,aAAW,sBACXT,KAAK,QACLzE,KAAK,QACLmF,SAAWf,GAAUD,EAAaC,EAAO,SACzCgB,QAASjB,IAEV5F,IACC3E,EAAAA,EAAAA,KAAA,QAAMoL,UAAU,6CAA4CjL,SACzDwE,QAKPqG,EAAAA,EAAAA,MAAA,SAAOI,UAAU,eAAcjL,SAAA,EAC7B6K,EAAAA,EAAAA,MAAA,OAAKI,UAAU,wBAAuBjL,SAAA,EACpCH,EAAAA,EAAAA,KAAA,SACEoL,UAAW,yLACTvG,EACI,gCACA,mBAENyG,YAAY,iBACZ,aAAW,iBACXT,KAAK,WACLzE,KAAK,WACLoF,QAASjB,EACTgB,SAAWf,GAAUD,EAAaC,EAAO,YACzCxD,IAAKpB,KAEP5F,EAAAA,EAAAA,KAACyL,EAAAA,EAAe,CACdL,UAAW,kBAA8B,UAAbhC,EAAuB,WAAa,IAChExD,WAAYA,EACZP,UAAWA,EACXD,OAAQA,OAGXP,IACC7E,EAAAA,EAAAA,KAAA,QAAMoL,UAAU,6CAA4CjL,SACzD0E,QAMP7E,EAAAA,EAAAA,KAAC0L,EAAAA,EAAOC,OAAM,CACZC,QAASvC,EACTwC,UAAW3C,EACXkC,UAAW,oDACTlC,EACI,6CACA,iDAEN4C,WAAY5C,EAAc,CAAE6C,MAAO,MAAS,CAAC,EAC7CC,SAAU9C,EAAc,CAAE6C,MAAO,KAAS,CAAC,EAC3C,aAAW,QAAO5L,SACnB,cAID6K,EAAAA,EAAAA,MAAA,OAAKI,UAAU,kDAAiDjL,SAAA,EAC9DH,EAAAA,EAAAA,KAAA,OAAKoL,UAAU,mCACfpL,EAAAA,EAAAA,KAAA,QAAMoL,UAAU,wBAAuBjL,SAAC,QACxCH,EAAAA,EAAAA,KAAA,OAAKoL,UAAU,sCAGjBpL,EAAAA,EAAAA,KAAA,OAAKoL,UAAU,yBAAwBjL,UAErC6K,EAAAA,EAAAA,MAAA,UACEH,KAAK,SACLO,UAAU,wDACVQ,QAASA,IAAM1F,EAAU,eAAgB,cACzC+F,UAAYC,IACI,UAAVA,EAAEC,KACJjG,EAAU,eAAgB,eAG9BkG,SAAU,EAAEjM,SAAA,EAEZH,EAAAA,EAAAA,KAAA,OAAKoL,UAAU,sDAAqDjL,UAClEH,EAAAA,EAAAA,KAACqM,EAAAA,EAAS,CACRxM,IAAKwE,EACLvE,IAAI,aACJsL,UAAU,6BACV7I,MAAM,KACNiE,OAAO,UAGXxG,EAAAA,EAAAA,KAAA,OAAKoL,UAAU,8CAA6CjL,SAAC,8BAKjEH,EAAAA,EAAAA,KAAA,OAAKoL,UAAU,0BAAyBjL,UACtC6K,EAAAA,EAAAA,MAAA,UACEH,KAAK,SACLO,UAAU,wDACVQ,QAASA,IAAM1F,EAAU,gBAAiB,eAC1C+F,UAAYC,IACI,UAAVA,EAAEC,KACJjG,EAAU,gBAAiB,gBAG/BkG,SAAU,EAAEjM,SAAA,EAEZH,EAAAA,EAAAA,KAAA,OAAKoL,UAAU,uDAAsDjL,UACnEH,EAAAA,EAAAA,KAACqM,EAAAA,EAAS,CACRxM,IAAKqE,EACLpE,IAAI,cACJsL,UAAU,6BACV7I,MAAM,KACNiE,OAAO,UAGXxG,EAAAA,EAAAA,KAAA,OAAKoL,UAAU,+CAA8CjL,SAAC,uCAexEH,EAAAA,EAAAA,KAAA,OAAKoL,UAAU,+FAA8FjL,UAC3G6K,EAAAA,EAAAA,MAAA,QAAMI,UAAU,yBAAwBjL,SAAA,EACtCH,EAAAA,EAAAA,KAAA,UACE6K,KAAK,SACLuB,SAAU,EACVhB,UAAU,0IACVQ,QAAUM,IACRA,EAAEI,iBACF,IACoB9J,OAAOiE,IAAIC,KAAK,WAAWX,8BAAoC,YAG/EvD,OAAOiE,IAAI1C,SAAS+B,KAAO,WAAWC,8BAE1C,CAAE,MAAOY,GAEPnE,OAAOiE,IAAI1C,SAAS+B,KAAO,WAAWC,6BACxC,GAEFkG,UAAYC,IACV,GAAc,UAAVA,EAAEC,IAAiB,CACrBD,EAAEI,iBACF,IACoB9J,OAAOiE,IAAIC,KAAK,WAAWX,8BAAoC,YAG/EvD,OAAOiE,IAAI1C,SAAS+B,KAAO,WAAWC,8BAE1C,CAAE,MAAOY,GAEPnE,OAAOiE,IAAI1C,SAAS+B,KAAO,WAAWC,6BACxC,CACF,GACA5F,SACH,iBAGA,OACDH,EAAAA,EAAAA,KAAA,UACE6K,KAAK,SACLuB,SAAU,EACVhB,UAAU,0IACVQ,QAAUM,IACRA,EAAEI,iBACF,IACoB9J,OAAOiE,IAAIC,KAAK,WAAWX,6BAAmC,YAG9EvD,OAAOiE,IAAI1C,SAAS+B,KAAO,WAAWC,6BAE1C,CAAE,MAAOY,GAEPnE,OAAOiE,IAAI1C,SAAS+B,KAAO,WAAWC,4BACxC,GAEFkG,UAAYC,IACV,GAAc,UAAVA,EAAEC,IAAiB,CACrBD,EAAEI,iBACF,IACoB9J,OAAOiE,IAAIC,KAAK,WAAWX,6BAAmC,YAG9EvD,OAAOiE,IAAI1C,SAAS+B,KAAO,WAAWC,6BAE1C,CAAE,MAAOY,GAEPnE,OAAOiE,IAAI1C,SAAS+B,KAAO,WAAWC,4BACxC,CACF,GACA5F,SACH,4BAQT6K,EAAAA,EAAAA,MAAA,OAAKI,UAAU,oBAAmBjL,SAAA,CAC/B4E,IACC/E,EAAAA,EAAAA,KAAA,OAAKoL,UAAU,uBAAsBjL,UACnC6K,EAAAA,EAAAA,MAAA,OAAKI,UAAU,+DAA8DjL,SAAA,EAC3EH,EAAAA,EAAAA,KAAA,QACEoL,UAAU,sDACVQ,QApRMW,KAClBvH,GAAc,IAoRF,aAAW,QAAO7E,SACnB,OAGDH,EAAAA,EAAAA,KAAA,OAAAG,SACG+E,GAEG,iHAKZlF,EAAAA,EAAAA,KAAA,OACEoL,WACErG,EAAa,kBAAoB,mBADxB,+DAEoD5E,UAE/DH,EAAAA,EAAAA,KAAA,OAAKoL,UAAU,0EAAyEjL,UACtF6K,EAAAA,EAAAA,MAAA,OAAKI,UAAU,yCAAwCjL,SAAA,EACrDH,EAAAA,EAAAA,KAAA,MAAIoL,UAAU,0DAAyDjL,SAAC,YAGxEH,EAAAA,EAAAA,KAAA,OACEoL,UAAU,+EACVpE,IAAKrB,EAAQxF,UAEb6K,EAAAA,EAAAA,MAAA,OAAKI,UAAU,wBAAuBjL,SAAA,CAClB,OAAjBqF,GACCwF,EAAAA,EAAAA,MAAAC,EAAAA,SAAA,CAAA9K,SAAA,EACE6K,EAAAA,EAAAA,MAAA,OAAKI,UAAU,2CAA0CjL,SAAA,EACvDH,EAAAA,EAAAA,KAAA,SACEoL,UAAU,wNACV,aAAW,QACXE,YAAY,UACZT,KAAK,QACLzE,KAAK,QACLoG,OAASN,GAAM3B,EAAa2B,EAAG,SAC/BX,SAAWW,GAAM3B,EAAa2B,EAAG,SACjCO,QAAUP,GAAM3B,EAAa2B,EAAG,SAChCV,QAASjB,KAEXvK,EAAAA,EAAAA,KAAA,SACEoL,UAAU,iMACVsB,IAAI,QAAOvM,SACZ,cAIHH,EAAAA,EAAAA,KAAA,QACEoL,UAAU,6GACVpE,IAAM0D,GAAQjF,EAAcqB,QAAQ,GAAK4D,EAAIvK,SAE5CwE,KAEHqG,EAAAA,EAAAA,MAAA,OAAKI,UAAU,2CAA0CjL,SAAA,EACvDH,EAAAA,EAAAA,KAAA,SACEoL,UAAU,8NACV,aAAW,WACXE,YAAY,aACZT,KAAK,WACLzE,KAAK,WACLoG,OAASN,GAAM3B,EAAa2B,EAAG,YAC/BX,SAAWW,GAAM3B,EAAa2B,EAAG,YACjCO,QAAUP,GAAM3B,EAAa2B,EAAG,YAChCV,QAASjB,EACTvD,IAAKpB,KAEP5F,EAAAA,EAAAA,KAAA,SACEoL,UAAU,iMACVsB,IAAI,WAAUvM,SACf,cAGDH,EAAAA,EAAAA,KAACyL,EAAAA,EAAe,CACdL,UAAU,mCACVxF,WAAYA,EACZP,UAAWA,EACXD,OAAQA,QAGZpF,EAAAA,EAAAA,KAAA,QACEoL,UAAU,6GACVpE,IAAM0D,GAAQjF,EAAcqB,QAAQ,GAAK4D,EAAIvK,SAE5C0E,QAILmG,EAAAA,EAAAA,MAAAC,EAAAA,SAAA,CAAA9K,SAAA,EACE6K,EAAAA,EAAAA,MAAA,SAAOI,UAAU,wBAAuBjL,SAAA,CACrCwE,IACC3E,EAAAA,EAAAA,KAAA,QAAMoL,UAAU,6CAA4CjL,SACzDwE,KAGL3E,EAAAA,EAAAA,KAAA,SACEoL,UAAU,0NACV,aAAW,gBACXE,YAAY,kBACZT,KAAK,QACLzE,KAAK,QAELoF,QAASjB,EACTgB,SAAWf,GAAUD,EAAaC,EAAO,eAG7CQ,EAAAA,EAAAA,MAAA,SAAOI,UAAU,eAAcjL,SAAA,CAC5B0E,IACC7E,EAAAA,EAAAA,KAAA,QAAMoL,UAAU,6CAA4CjL,SACzD0E,KAGLmG,EAAAA,EAAAA,MAAA,OAAKI,UAAU,wBAAuBjL,SAAA,EACpCH,EAAAA,EAAAA,KAAA,SACEoL,UAAU,2NACV,aAAW,WACXE,YAAY,aACZT,KAAK,WACLzE,KAAK,WAELoF,QAASjB,EACTgB,SAAWf,GACTD,EAAaC,EAAO,YAEtBxD,IAAKpB,KAEP5F,EAAAA,EAAAA,KAACyL,EAAAA,EAAe,CACdL,UAAU,gBACVxF,WAAYA,EACZP,UAAWA,EACXD,OAAQA,cAMlBpF,EAAAA,EAAAA,KAAC0L,EAAAA,EAAOC,OAAM,CACZC,QAASvC,EACT+B,UAAU,8EACVU,WAAY,CAAEC,MAAO,KACrBC,SAAU,CAAED,MAAO,IACnB,aAAW,QAAO5L,SACnB,WAGDH,EAAAA,EAAAA,KAAC0L,EAAAA,EAAOC,OAAM,CACZC,QAASA,IAAM5F,EAAe,kBAC9BoF,UAAU,oDACVU,WAAY,CAAEa,gBAAiB,QAC/BX,SAAU,CAAED,MAAO,IACnB,aAAW,WAAU5L,SACtB,cAGDH,EAAAA,EAAAA,KAAC0L,EAAAA,EAAOC,OAAM,CACZC,QAASA,IAAM5F,EAAe,WAC9BoF,UAAU,oDACVU,WAAY,CAAEa,gBAAiB,QAC/BX,SAAU,CAAED,MAAO,IACnB,aAAW,kBAAiB5L,SAC7B,qBAIDH,EAAAA,EAAAA,KAAA,OAAKoL,UAAU,gBAAejL,UAC5B6K,EAAAA,EAAAA,MAAA,UACEH,KAAK,SACLO,UAAU,0DACVQ,QAASA,IAAM1F,EAAU,gBAAiB,eAC1C+F,UAAYC,IACI,UAAVA,EAAEC,KACJjG,EAAU,gBAAiB,gBAG7BkG,SAAU,EAAEjM,SAAA,EAEdH,EAAAA,EAAAA,KAAA,OAAKoL,UAAU,qBAAoBjL,UACjCH,EAAAA,EAAAA,KAACqM,EAAAA,EAAS,CACNxM,IAAKqE,EACPpE,IAAI,cACFsL,UAAU,6BACZ7I,MAAM,KACNiE,OAAO,UAGXxG,EAAAA,EAAAA,KAAA,OAAKoL,UAAU,qBAAoBjL,SAAC,2BAIxCH,EAAAA,EAAAA,KAAA,OAAKoL,UAAU,eAAcjL,UAC3B6K,EAAAA,EAAAA,MAAA,UACEH,KAAK,SACLO,UAAU,0DACVQ,QAASA,IAAM1F,EAAU,eAAgB,cACzC+F,UAAYC,IACI,UAAVA,EAAEC,KACJjG,EAAU,eAAgB,eAG5BkG,SAAU,EAAEjM,SAAA,EAEdH,EAAAA,EAAAA,KAAA,OAAKoL,UAAU,oBAAmBjL,UAChCH,EAAAA,EAAAA,KAACqM,EAAAA,EAAS,CACRxM,IAAKwE,EACLvE,IAAI,aACJsL,UAAU,wBACV7I,MAAM,KACNiE,OAAO,UAGXxG,EAAAA,EAAAA,KAAA,OAAKoL,UAAU,oBAAmBjL,SAAC,0CAezD,C,wECjtBA,QAlCA,SAAwBP,GAAgD,IAA/C,UAAEwL,EAAS,WAAExF,EAAU,UAAEP,EAAS,OAAED,GAAQxF,EACnE,MAAMgN,GAAc/F,EAAAA,EAAAA,aAAY,KAC9B,MAAMgG,EAAUjH,EAAWkB,QAE3BzB,GAAWD,GAKTyH,EAAQhC,KAHLzF,EAGY,WAFA,OAKjByH,EAAQC,SACP,CAACzH,EAAWO,EAAYR,IAE3B,OACE4F,EAAAA,EAAAA,MAAAC,EAAAA,SAAA,CAAA9K,SAAA,EACEH,EAAAA,EAAAA,KAAA,UACEoL,UAAW,mPAAmPA,IAC9PQ,QAASgB,EACT/B,KAAK,SACLkC,OAAU3H,EAAS,OAAS,QAArB,YAAuCjF,UAE9CH,EAAAA,EAAAA,KAACgN,EAAAA,IAAK,CAACvF,MAAO,CAAEwF,MAAO,aAEzBjN,EAAAA,EAAAA,KAAA,QACEoL,UAAW,uIAAuIA,qHAChJhG,EAAS,GAAK,gCAKxB,C", "sources": ["LazyImage.jsx", "core/utils/helper.jsx", "core/utils/socket.jsx", "login/index.jsx", "login/passwordtoggler.jsx"], "sourcesContent": ["import React, { Suspense } from 'react';\r\n\r\nconst LazyImage = ({ src, alt, ...props }) => {\r\n  return (\r\n    <Suspense fallback={<div>Loading...</div>}>\r\n      <img src={src} alt={alt} {...props} />\r\n    </Suspense>\r\n  );\r\n};\r\n\r\nexport default LazyImage;\r\n", "import { useState, useEffect } from 'react';\r\n//check if browser supports WEBP\r\nexport function isWebpSupported() {\r\n  const elem = document.createElement('canvas');\r\n  if (!!(elem.getContext && elem.getContext('2d'))) {\r\n    return elem.toDataURL('image/webp').indexOf('data:image/webp') === 0;\r\n  }\r\n  return false;\r\n}\r\n// Observe sections/divs for lazy loading\r\nexport function observeSections(callback) {\r\n  const observer = new IntersectionObserver(entries => {\r\n    entries.forEach(entry => {\r\n      if (entry.isIntersecting) {\r\n        callback(entry.target.id);\r\n        // observer.unobserve(entry.target); // Stop observing once the section is intersecting\r\n      }\r\n    });\r\n  });\r\n  document.querySelectorAll('.lazy-section').forEach(section => {\r\n    observer.observe(section);\r\n  });\r\n  return () => {\r\n    observer.disconnect();\r\n  };\r\n}\r\n\r\n// Observe videos for lazy loading\r\nexport function observeVideos(callback) {\r\n  const observer = new IntersectionObserver(entries => {\r\n    entries.forEach(entry => {\r\n      if (entry.isIntersecting) {\r\n        const video = entry.target.querySelector('video');\r\n        if (video) {\r\n          const source = video.querySelector('source');\r\n          if (source && source.getAttribute('data-src')) {\r\n            source.setAttribute('src', source.getAttribute('data-src'));\r\n            video.load();\r\n            callback(entry.target.id);\r\n          }\r\n        }\r\n        observer.unobserve(entry.target);\r\n      }\r\n    });\r\n  });\r\n\r\n  document.querySelectorAll('.lazy-video').forEach(video => {\r\n    observer.observe(video);\r\n  });\r\n\r\n  return () => {\r\n    observer.disconnect();\r\n  };\r\n}\r\n\r\n// Hash pp_ctaclr\r\nexport function hexHash(hex) {\r\n  const hexRegex = /^[0-9a-f]*$/i;\r\n  if (hex == null || hex.length < 6 || !hexRegex.test(hex)) {\r\n    return `#1559ED`;\r\n  }\r\n\r\n  hex = hex.slice(0, 6);\r\n\r\n  return `#${hex}`;\r\n}\r\n\r\n// Darken color when hovering\r\nexport function hoverDarken(hex) {\r\n  const hexRegex = /^[0-9a-f]*$/i;\r\n  if (hex == null || hex.length < 6 || !hexRegex.test(hex)) {\r\n    hex = '1559ED';\r\n  }\r\n\r\n  let r = parseInt(hex.slice(0, 2), 16);\r\n  let g = parseInt(hex.slice(2, 4), 16);\r\n  let b = parseInt(hex.slice(4, 6), 16);\r\n\r\n  r = Math.max(0, Math.min(255, r - (r * 0.15)));\r\n  g = Math.max(0, Math.min(255, g - (g * 0.15)));\r\n  b = Math.max(0, Math.min(255, b - (b * 0.15)));\r\n\r\n  const toHex = (value) => {\r\n    const hexValue = Math.round(value).toString(16);\r\n    return hexValue.padStart(2, '0');\r\n  };\r\n  \r\n  return `#${toHex(r)}${toHex(g)}${toHex(b)}`;\r\n}\r\n\r\nexport function useDeviceSize() {\r\n  const [isMobile, setIsMobile_] = useState(false);\r\n  const [isTablet, setIsTablet] = useState(false);\r\n  const [isDesktop, setIsDesktop] = useState(false);\r\n\r\n  useEffect(() => {\r\n    const handleResize = () => {\r\n      const width = window.innerWidth;\r\n\r\n      setIsMobile_(width > 430);\r\n      setIsTablet(width > 729 && width <= 828); \r\n      setIsDesktop(width > 901); \r\n    };\r\n\r\n    handleResize(); \r\n    window.addEventListener('resize', handleResize);\r\n\r\n    return () => {\r\n      window.removeEventListener('resize', handleResize); \r\n    };\r\n  }, []);\r\n\r\n  return { isMobile, isTablet, isDesktop };\r\n}\r\n\r\nexport function renderContent(isTablet, isMobile, isDesktop, Infinite) {\r\n  if (isTablet) {\r\n    return <img src={Infinite} alt=\"Infinite\" className=\"mx-auto w-[22px]\" />;\r\n  } else {\r\n    return <span className=\"text-[#3C57BB] font-bold\">Unlimited</span>;\r\n  }\r\n}", "import { io } from 'socket.io-client';\r\nimport { Get<PERSON>ookie } from './cookies';\r\n\r\nexport function initSocket() {\r\n  let aiwp_logged_in = GetCookie('aiwp_logged_in');\r\n  let run_socket = true;\r\n  if (aiwp_logged_in!==undefined && aiwp_logged_in!==null && aiwp_logged_in!==\"\"){\r\n    let myarr = aiwp_logged_in.split(\"|\");\r\n    if(typeof myarr[2] !== 'undefined') {\r\n      if (myarr[2]==='1'){\r\n        run_socket = false;\r\n      }\r\n    }\r\n  }\r\n  \r\n  if (!run_socket){\r\n    return;\r\n  }\r\n\r\n  const URL = process.env.REACT_APP_SOCKET_URL;\r\n  const socket = io(URL);\r\n  let sidx = ''; // user_ip\r\n  if(GetCookie('user_ip')) {\r\n    sidx = GetCookie('user_ip');\r\n  }\r\n\r\n  socket.emit('register-session', { id: sidx, email: GetCookie('user_email'), socket_key: process.env.REACT_APP_SOCKET_KEY });\r\n\r\n  return { socket, sidx };\r\n}", "import React, { useState, useEffect, useRef, useCallback } from \"react\";\r\nimport \"./style.css\";\r\nimport { motion } from \"framer-motion\";\r\nimport axios from \"axios\";\r\nimport { Auth } from \"../core/utils/auth\";\r\nimport { <PERSON><PERSON><PERSON><PERSON>, Get<PERSON><PERSON>ie } from \"../core/utils/cookies\";\r\nimport { Helmet } from \"react-helmet\";\r\nimport { initSocket } from \"../core/utils/socket\";\r\nimport errors from \"../locales/errors.json\";\r\n// import img1 from \"../assets/images/google_icon.png\";\r\nimport img2 from \"../assets/images/google_icon_black.png\";\r\nimport imgApple from \"../assets/images/apple_ico.png\";\r\n// import img1webp from '../assets/images/google_icon.webp';\r\nimport img2webp from '../assets/images/google_icon_black.webp';\r\nimport imgApplewebp from '../assets/images/apple_ico.webp';\r\nimport LazyImage from '../LazyImage';\r\nimport toastr from 'toastr';\r\nimport 'toastr/build/toastr.min.css';\r\nimport { isWebpSupported } from '../core/utils/helper';\r\nimport PasswordToggler from './passwordtoggler';\r\nimport loginLogo from '../assets/images/reglogo.png';\r\n// const Header = GetSubdomain() ? null : require(\"../header\").default;\r\n// const Footer = GetSubdomain() ? null : lazy(() => import('../footer'));\r\n// const PasswordToggler = lazy(() => import(\"./passwordtoggler\"));\r\n\r\n\r\nconst params = new URLSearchParams(window.location.search);\r\n\r\nvar client_io;\r\n\r\nfunction Login() {\r\n  const img2Src = isWebpSupported() ? img2webp : img2;\r\n  const imgAppleSrc = isWebpSupported() ? imgApplewebp : imgApple;\r\n  const [email, setEmail] = useState(\"\");\r\n  const [password, setPassword] = useState(\"\");\r\n  const [emailError, setEmailError] = useState(\"\");\r\n  const [passwordError, setPasswordError] = useState(\"\");\r\n  const [showBanner, setShowBanner] = useState(\r\n    window.view_data[\"stop-session\"] ? true : false\r\n  );\r\n  const [bannerMessage, setBannerMessage] = useState(\"\");\r\n  const [showPW, setShowPW] = useState(false);\r\n  // const renderHeader = !GetSubdomain();\r\n  const auth = Auth();\r\n  const smooth_login = GetCookie(\"smooth_login\") || \"off\";\r\n  const spanErrorRefs = useRef([]);\r\n  const formRef = useRef(null);\r\n  const pwInputRef = useRef(null);\r\n\r\n  // Global prefix logic\r\n  let currentLocation = window.location.href;\r\n  let prefix = \"\";\r\n  if (currentLocation.indexOf(\"staging\") > -1) {\r\n    prefix = \"staging.\";\r\n  } else if (currentLocation.indexOf(\"dev\") > -1) {\r\n    prefix = \"dev.\";\r\n  }\r\n\r\n  const handleRedirect = (path) => {\r\n    window.location.href = path;\r\n  };\r\n\r\n  const openPopup = (url, name) => {\r\n    const screenWidth = window.screen.width;\r\n    const screenHeight = window.screen.height;\r\n    const windowWidth = 600;\r\n    const windowHeight = 700;\r\n    const left = (screenWidth - windowWidth) / 2;\r\n    const top = (screenHeight - windowHeight) / 2;\r\n    const windowFeatures = `width=${windowWidth},height=${windowHeight},top=${top},left=${left}`;\r\n\r\n    try {\r\n      const newWindow = window.top.open(url, name, windowFeatures);\r\n      if (!newWindow) {\r\n        // Fallback to direct redirect if popup is blocked\r\n        window.top.location.href = url;\r\n      }\r\n    } catch (error) {\r\n      // Fallback to direct redirect if there's an error\r\n      window.top.location.href = url;\r\n    }\r\n  };\r\n\r\n  const setSpanHeights = useCallback(() => {\r\n    if (smooth_login === \"on\") {\r\n      spanErrorRefs.current.forEach((ref, i) => {\r\n        if (ref) {\r\n          const errMsg = ref.textContent;\r\n          const currHeight = ref.offsetHeight;\r\n          const height = !!errMsg\r\n            ? currHeight !== 0\r\n              ? currHeight\r\n              : (ref.scrollHeight ?? 0) + 12\r\n            : 0;\r\n\r\n          setTimeout(() => {\r\n            ref.style.height = `${height}px`;\r\n          }, 100);\r\n        }\r\n      });\r\n    }\r\n  }, [smooth_login]);\r\n\r\n  setTimeout(() => {\r\n    const form = formRef.current;\r\n    const isChromium =\r\n      navigator.userAgent.includes(\"Chrome\") ||\r\n      navigator.userAgent.includes(\"Edg\");\r\n\r\n    if (form && isChromium) {\r\n      const inputs = form.querySelectorAll(\"input\");\r\n\r\n      if (inputs && inputs.length > 0) {\r\n        inputs.forEach((input) => {\r\n          if (input.matches(\":-internal-autofill-selected\")) {\r\n            input.classList.add(\"autofilled\");\r\n          }\r\n        });\r\n      }\r\n    }\r\n  }, 200);\r\n\r\n  useEffect(()=>{\r\n    client_io = initSocket();\r\n    toastr.options = {\r\n      positionClass: 'toast-top-center'\r\n    };\r\n    \r\n    // Remove lp cookie when login page loads\r\n    if (GetCookie(\"lp\")) {\r\n      SetCookie(\"lp\", \"\", { path: \"/\", expires: new Date(0) });\r\n    }\r\n  },[]);\r\n\r\n  useEffect(() => {\r\n    //Display google error from google signin\r\n    let google_error_code = params.get(\"e\");\r\n    let google_error = \"\";\r\n    if (google_error_code!==null && google_error_code!==''){\r\n      google_error = errors.find(({ error_code }) => error_code === google_error_code)\r\n      if (google_error!==undefined){\r\n        toastr.error(google_error.msg);\r\n      }else{\r\n        toastr.error(google_error_code);\r\n      }\r\n    }\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    if (smooth_login === \"on\") {\r\n      setSpanHeights();\r\n      window.addEventListener(\"resize\", setSpanHeights);\r\n\r\n      return () => {\r\n        window.removeEventListener(\"resize\", setSpanHeights);\r\n      };\r\n    }\r\n  }, [smooth_login, setSpanHeights]);\r\n\r\n  useEffect(() => {\r\n    if (smooth_login === \"on\") {\r\n      setSpanHeights();\r\n    }\r\n  }, [emailError, passwordError, smooth_login, setSpanHeights]);\r\n\r\n  if (auth === undefined) return null;\r\n  if (auth) {\r\n    handleRedirect(\"/my-account\");\r\n    return null;\r\n  }\r\n\r\n  const validateEmail = () => {\r\n    let isPassed = false;\r\n    if (!email) {\r\n      setEmailError(\"Email is required\");\r\n    } else if (!/\\S+@\\S+\\.\\S+/.test(email)) {\r\n      setEmailError(\"Invalid email format\");\r\n    } else {\r\n      setEmailError(\"\");\r\n      isPassed = true;\r\n    }\r\n    return isPassed;\r\n  };\r\n\r\n  const validatePassword = () => {\r\n    let isPassed = false;\r\n    if (!password) {\r\n      setPasswordError(\"Password is required\");\r\n    } else {\r\n      setPasswordError(\"\");\r\n      isPassed = true;\r\n    }\r\n    return isPassed;\r\n  };\r\n\r\n  // Check if both email and password have values\r\n  const isFormValid = email.trim() !== \"\" && password.trim() !== \"\";\r\n  const lpCookie = GetCookie(\"lp\");\r\n\r\n  function loginUser() {\r\n    var isEmailPassed = validateEmail();\r\n    var isPasswordPassed = validatePassword();\r\n    if (!isEmailPassed || !isPasswordPassed) return;\r\n\r\n    document.querySelector(\".loader-container\").classList.add(\"active\");\r\n    axios\r\n      .post(\r\n        `${process.env.REACT_APP_API_URL}/login`,\r\n        {\r\n          email,\r\n          password,\r\n        },\r\n        { headers: { \"content-type\": \"application/x-www-form-urlencoded\" } }\r\n      )\r\n      .then(function (res) {\r\n        let output = res.data;\r\n\r\n        if (output.data.status === 'unverified') {\r\n          window.location.href = `${output.data.redirect_url}/?email=${output.data.email}`;\r\n        }\r\n\r\n        if (output.success) {\r\n          if (output.success === 2) {\r\n            document\r\n              .querySelector(\".loader-container\")\r\n              .classList.remove(\"active\");\r\n            setShowBanner(true);\r\n            setBannerMessage(output.data.msg);\r\n\r\n            if (client_io) {\r\n              let socket = client_io.socket;\r\n              socket.emit(\"logout-account\", {\r\n                id: null,\r\n                email,\r\n                socket_key: process.env.REACT_APP_SOCKET_KEY,\r\n              });\r\n            }\r\n            return;\r\n          }\r\n          toastr.success(\"Success\");\r\n          SetCookie('access', output.data, { path: '/' });\r\n          SetCookie('isloggedin', 1, { path: '/' });\r\n          \r\n          // Check if we're in AI Hub mode and redirect outside iframe\r\n          if (lpCookie === \"aihub\") {\r\n            window.top.location.href = \"/my-account\";\r\n          } else {\r\n            handleRedirect(\"/my-account\");\r\n          }\r\n          return;\r\n        }\r\n      document.querySelector(\".loader-container\").classList.remove('active');\r\n      if(output.data.msg) toastr.error(output.data.msg);\r\n    });\r\n  }\r\n\r\n  const closeBanner = () => {\r\n    setShowBanner(false);\r\n  };\r\n\r\n  const handleChange = (event, inputType) => {\r\n    const el = event.target;\r\n    const value = el.value;\r\n    const isFocus = event.type === \"focus\";\r\n    const isInput = event.type === \"change\" || event.type === \"keyup\";\r\n\r\n    if (!!value || isFocus || isInput) {\r\n      el.classList.add(\"autofilled\");\r\n    } else {\r\n      el.classList.remove(\"autofilled\");\r\n    }\r\n\r\n    if (smooth_login !== \"on\" || (smooth_login === \"on\" && isInput)) {\r\n      switch (inputType) {\r\n        case \"email\":\r\n          setEmailError(\"\");\r\n          setEmail(value);\r\n          break;\r\n        case \"password\":\r\n          setPasswordError(\"\");\r\n          setPassword(value);\r\n          break;\r\n        default:\r\n          if (event.keyCode === 13) {\r\n            loginUser();\r\n          }\r\n          break;\r\n      }\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <Helmet>\r\n        <title>AI Pro | Login</title>\r\n        <meta\r\n          name=\"description\"\r\n          content=\"Login to access your account and unlock a world of possibilities. Your gateway to exclusive applications and special offers awaits. Join us now!\"\r\n        />\r\n      </Helmet>\r\n      {lpCookie === \"aihub\" ? (\r\n        // AI Hub specific design\r\n        <div className=\"min-h-screen bg-white flex flex-col items-center justify-center p-4\">\r\n          <div className=\"max-w-md w-full\">\r\n            <div className=\"bg-[#FCFCFC] rounded-2xl p-8\">\r\n              <div className=\"text-center mb-8\">\r\n                <img src={loginLogo} alt=\"AI Pro Logo\" className=\"mx-auto mb-2 h-12\" />\r\n                <h1 className=\"text-[20px] font-bold text-black mb-2\">\r\n                  Log In\r\n                </h1>\r\n              </div>\r\n              <div className=\"space-y-4\">\r\n                <label className=\"relative block w-full\">\r\n                  <input\r\n                    className={`placeholder:text-gray-400 block w-full border min-h-[50px] rounded-md py-2 pl-3 pr-3 shadow-sm focus:outline-none focus:border-blue-500 focus:ring-blue-500 focus:ring-1 sm:text-sm ${\r\n                      emailError \r\n                        ? 'border-[#E28C8C] bg-[#FCF3F3]' \r\n                        : 'border-gray-200'\r\n                    }`}\r\n                    placeholder=\"Enter email address\"\r\n                    aria-label=\"Enter email address\"\r\n                    type=\"email\"\r\n                    name=\"email\"\r\n                    onChange={(event) => handleChange(event, \"email\")}\r\n                    onKeyUp={handleChange}\r\n                  />\r\n                  {emailError && (\r\n                    <span className=\"text-red-400 text-xs text-left w-full mb-2\">\r\n                      {emailError}\r\n                    </span>\r\n                  )}\r\n\r\n                </label>\r\n                <label className=\"block w-full\">\r\n                  <div className=\"relative block w-full\">\r\n                    <input\r\n                      className={`placeholder:text-gray-400 min-h-[50px] block w-full border rounded-md py-2 pl-3 shadow-sm focus:outline-none focus:border-blue-500 focus:ring-blue-500 focus:ring-1 sm:text-sm pr-10 ${\r\n                        passwordError \r\n                          ? 'border-[#E28C8C] bg-[#FCF3F3]' \r\n                          : 'border-gray-200'\r\n                      }`}\r\n                      placeholder=\"Enter password\"\r\n                      aria-label=\"Enter Password\"\r\n                      type=\"password\"\r\n                      name=\"password\"\r\n                      onKeyUp={handleChange}\r\n                      onChange={(event) => handleChange(event, \"password\")}\r\n                      ref={pwInputRef}\r\n                    />\r\n                    <PasswordToggler\r\n                      className={`right-3 top-3 ${lpCookie === \"aihub\" ? \"mt-[5px]\" : \"\"}`}\r\n                      pwInputRef={pwInputRef}\r\n                      setShowPW={setShowPW}\r\n                      showPW={showPW}\r\n                    />\r\n                  </div>\r\n                  {passwordError && (\r\n                    <span className=\"text-red-400 text-xs text-left w-full mb-2\">\r\n                      {passwordError}\r\n                    </span>\r\n                  )}\r\n\r\n                </label>\r\n\r\n                <motion.button\r\n                  onClick={loginUser}\r\n                  disabled={!isFormValid}\r\n                  className={`mb-1 font-bold py-3 px-6 my-3 rounded-lg w-full ${\r\n                    isFormValid \r\n                      ? \"text-white bg-[#3073D5] hover:bg-[#2563eb]\" \r\n                      : \"text-gray-400 bg-[#F1F1F1] cursor-not-allowed\"\r\n                  }`}\r\n                  whileHover={isFormValid ? { scale: 1.02 } : {}}\r\n                  whileTap={isFormValid ? { scale: 0.98 } : {}}\r\n                  aria-label=\"login\"\r\n                >\r\n                  Continue\r\n                </motion.button>\r\n\r\n                <div className=\"flex items-center justify-center space-x-4 mb-4\">\r\n                  <div className=\"w-24 border-t border-gray-200\"></div>\r\n                  <span className=\"text-gray-400 text-sm\">or</span>\r\n                  <div className=\"w-24 border-t border-gray-200\"></div>\r\n                </div>\r\n\r\n                <div className=\"apple-button space-y-3\">\r\n\r\n                  <button\r\n                    type=\"button\"\r\n                    className=\"w-full py-3 border rounded-lg bg-[#EDEDED] text-black\"\r\n                    onClick={() => openPopup(\"/apple/login\", \"AppleLogin\")}\r\n                    onKeyDown={(e) => {\r\n                      if (e.key === 'Enter') {\r\n                        openPopup(\"/apple/login\", \"AppleLogin\");\r\n                      }\r\n                    }}\r\n                    tabIndex={0}\r\n                  >\r\n                    <div className=\"apple-button-icon inline-flex align-middle mr-[5px]\">\r\n                      <LazyImage\r\n                        src={imgAppleSrc}\r\n                        alt=\"AppleLogin\"\r\n                        className=\"w-[16px] max-h-[20px] mb-1\"\r\n                        width=\"20\"\r\n                        height=\"20\"\r\n                      />\r\n                    </div>\r\n                    <div className=\"apple-button-text inline-flex font-semibold\">\r\n                      Continue with Apple\r\n                    </div>\r\n                  </button>\r\n                </div>\r\n                <div className=\"google-button space-y-3\">\r\n                  <button\r\n                    type=\"button\"\r\n                    className=\"w-full py-3 border rounded-lg bg-[#EDEDED] text-black\"\r\n                    onClick={() => openPopup(\"/google/login\", \"GoogleLogin\")}\r\n                    onKeyDown={(e) => {\r\n                      if (e.key === 'Enter') {\r\n                        openPopup(\"/google/login\", \"GoogleLogin\");\r\n                      }\r\n                    }}\r\n                    tabIndex={0}\r\n                  >\r\n                    <div className=\"google-button-icon inline-flex align-middle mr-[5px]\">\r\n                      <LazyImage\r\n                        src={img2Src}\r\n                        alt=\"GoogleLogin\"\r\n                        className=\"w-[20px] max-h-[20px] mb-1\"\r\n                        width=\"20\"\r\n                        height=\"20\"\r\n                      />\r\n                    </div>\r\n                    <div className=\"google-button-text inline-flex font-semibold\">\r\n                      Continue with Google\r\n                    </div>\r\n                  </button>\r\n\r\n                </div>\r\n\r\n                {/* <div className=\"text-center mt-6\">\r\n                  <span className=\"text-sm\">\r\n                    Don't have an account? <span className=\"text-[#3073D5] hover:text-blue-600 cursor-pointer\" tabIndex={0} onClick={() => window.location.href = '/register-auth'}>Create it for free!</span>\r\n                  </span>\r\n                </div> */}\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div className=\"text-center mt-8 absolute bottom-[15px] left-0 right-0 max-h-600:relative max-h-600:bottom-0\">\r\n            <span className=\"text-xs text-[#3073D5]\">\r\n              <button \r\n                type=\"button\"\r\n                tabIndex={0}\r\n                className=\"cursor-pointer hover:underline hover:text-blue-700 transition-colors duration-200 bg-transparent border-none p-0 text-xs text-[#3073D5]\"\r\n                onClick={(e) => {\r\n                  e.preventDefault();\r\n                  try {\r\n                    const newWindow = window.top.open(`https://${prefix}ai-pro.org/member-tos-page`, '_blank');\r\n                    if (!newWindow) {\r\n                      // If popup is blocked, redirect the entire iframe\r\n                      window.top.location.href = `https://${prefix}ai-pro.org/member-tos-page`;\r\n                    }\r\n                  } catch (error) {\r\n                    // If cross-origin blocks window.top.open, redirect the entire iframe\r\n                    window.top.location.href = `https://${prefix}ai-pro.org/member-tos-page`;\r\n                  }\r\n                }}\r\n                onKeyDown={(e) => {\r\n                  if (e.key === 'Enter') {\r\n                    e.preventDefault();\r\n                    try {\r\n                      const newWindow = window.top.open(`https://${prefix}ai-pro.org/member-tos-page`, '_blank');\r\n                      if (!newWindow) {\r\n                        // If popup is blocked, redirect the entire iframe\r\n                        window.top.location.href = `https://${prefix}ai-pro.org/member-tos-page`;\r\n                      }\r\n                    } catch (error) {\r\n                      // If cross-origin blocks window.top.open, redirect the entire iframe\r\n                      window.top.location.href = `https://${prefix}ai-pro.org/member-tos-page`;\r\n                    }\r\n                  }\r\n                }}\r\n              >\r\n                Terms of Use\r\n              </button>\r\n              {\" / \"}\r\n              <button \r\n                type=\"button\"\r\n                tabIndex={0}\r\n                className=\"cursor-pointer hover:underline hover:text-blue-700 transition-colors duration-200 bg-transparent border-none p-0 text-xs text-[#3073D5]\"\r\n                onClick={(e) => {\r\n                  e.preventDefault();\r\n                  try {\r\n                    const newWindow = window.top.open(`https://${prefix}ai-pro.org/privacy-policy`, '_blank');\r\n                    if (!newWindow) {\r\n                      // If popup is blocked, redirect the entire iframe\r\n                      window.top.location.href = `https://${prefix}ai-pro.org/privacy-policy`;\r\n                    }\r\n                  } catch (error) {\r\n                    // If cross-origin blocks window.top.open, redirect the entire iframe\r\n                    window.top.location.href = `https://${prefix}ai-pro.org/privacy-policy`;\r\n                  }\r\n                }}\r\n                onKeyDown={(e) => {\r\n                  if (e.key === 'Enter') {\r\n                    e.preventDefault();\r\n                    try {\r\n                      const newWindow = window.top.open(`https://${prefix}ai-pro.org/privacy-policy`, '_blank');\r\n                      if (!newWindow) {\r\n                        // If popup is blocked, redirect the entire iframe\r\n                        window.top.location.href = `https://${prefix}ai-pro.org/privacy-policy`;\r\n                      }\r\n                    } catch (error) {\r\n                      // If cross-origin blocks window.top.open, redirect the entire iframe\r\n                      window.top.location.href = `https://${prefix}ai-pro.org/privacy-policy`;\r\n                    }\r\n                  }\r\n                }}\r\n              >\r\n                Privacy Policy\r\n              </button>\r\n            </span>\r\n          </div>\r\n        </div>\r\n      ) : (\r\n        // Original design\r\n      <div className=\"login bg-gray-100\">\r\n        {showBanner && (\r\n          <div className=\"absolute info-banner\">\r\n            <div className=\"relative px-8 py-4 mb-6 bg-[#f7ead9] block w-full rounded-md\">\r\n              <span\r\n                className=\"absolute top-1 right-4 text-lg cursor-pointer close\"\r\n                onClick={closeBanner}\r\n                aria-label=\"close\"\r\n              >\r\n                x\r\n              </span>\r\n              <div>\r\n                {bannerMessage\r\n                  ? bannerMessage\r\n                  : \"You have been automatically logged out as a result of multiple active sessions linked to your account.\"}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        )}\r\n        <div\r\n          className={`${\r\n            showBanner ? \"login-container\" : \"md:min-h-[85vh]\"\r\n          } flex justify-center items-center sm:mt-10 md:mt-10 lg:mt-14`}\r\n        >\r\n          <div className=\"container mx-auto py-10 px-0 md:px-4 sm:px-0 w-96 mb-20 mt-[50px] md:mt\">\r\n            <div className=\"reg_col text-center mb-8 min-h-[551px]\">\r\n              <h1 className=\"text-2xl lg:text-2xl font-bold text-center mb-6 lg:mb-8\">\r\n                  Log In\r\n              </h1>\r\n              <div\r\n                className=\"bg-white rounded-lg shadow-lg overflow-hidden min-h-[495px] md:min-h-[487px]\"\r\n                ref={formRef}\r\n              >\r\n                <div className=\"px-6 py-10 text-black\">\r\n                  {smooth_login === \"on\" ? (\r\n                    <>\r\n                      <div className=\"bg-white w-full relative mb-3 sm:text-sm\">\r\n                        <input\r\n                          className=\"peer block border-2 border-slate-300 rounded-md shadow-sm focus:outline-none focus:border-sky-500 focus:ring-sky-500 focus:ring-1  w-full pt-6 pb-2 px-6 placeholder:text-transparent placeholder:[user-select:_none]\"\r\n                          aria-label=\"Email\"\r\n                          placeholder=\"Email *\"\r\n                          type=\"email\"\r\n                          name=\"email\"\r\n                          onBlur={(e) => handleChange(e, \"email\")}\r\n                          onChange={(e) => handleChange(e, \"email\")}\r\n                          onFocus={(e) => handleChange(e, \"email\")}\r\n                          onKeyUp={handleChange}\r\n                        />\r\n                        <label\r\n                          className=\"transition-all duration-100 ease-in absolute top-1/2 -translate-y-1/2 left-6 pointer-events-none [.autofilled~&]:top-[17px] [.autofilled~&]:text-xs [.autofilled~&]:left-[25px] text-[#597291]\"\r\n                          for=\"email\"\r\n                        >\r\n                          Email\r\n                        </label>\r\n                      </div>\r\n                      <span\r\n                        className=\"block text-red-500 text-xs text-center w-full h-0 transition-[height] duration-200 ease-in overflow-hidden\"\r\n                        ref={(el) => (spanErrorRefs.current[0] = el)}\r\n                      >\r\n                        {emailError}\r\n                      </span>\r\n                      <div className=\"bg-white w-full relative mb-3 sm:text-sm\">\r\n                        <input\r\n                          className=\"peer block border-2 border-slate-300 rounded-md shadow-sm focus:outline-none focus:border-sky-500 focus:ring-sky-500 focus:ring-1  w-full pt-6 pb-2 pl-6 pr-16 placeholder:text-transparent placeholder:[user-select:_none]\"\r\n                          aria-label=\"Password\"\r\n                          placeholder=\"Password *\"\r\n                          type=\"password\"\r\n                          name=\"password\"\r\n                          onBlur={(e) => handleChange(e, \"password\")}\r\n                          onChange={(e) => handleChange(e, \"password\")}\r\n                          onFocus={(e) => handleChange(e, \"password\")}\r\n                          onKeyUp={handleChange}\r\n                          ref={pwInputRef}\r\n                        />\r\n                        <label\r\n                          className=\"transition-all duration-100 ease-in absolute top-1/2 -translate-y-1/2 left-6 pointer-events-none [.autofilled~&]:top-[17px] [.autofilled~&]:text-xs [.autofilled~&]:left-[25px] text-[#597291]\"\r\n                          for=\"password\"\r\n                        >\r\n                          Password\r\n                        </label>\r\n                        <PasswordToggler\r\n                          className=\"right-6 top-1/2 -translate-y-1/2\"\r\n                          pwInputRef={pwInputRef}\r\n                          setShowPW={setShowPW}\r\n                          showPW={showPW}\r\n                        />\r\n                      </div>\r\n                      <span\r\n                        className=\"block text-red-500 text-xs text-center w-full h-0 transition-[height] duration-200 ease-in overflow-hidden\"\r\n                        ref={(el) => (spanErrorRefs.current[1] = el)}\r\n                      >\r\n                        {passwordError}\r\n                      </span>\r\n                    </>\r\n                  ) : (\r\n                    <>\r\n                      <label className=\"relative block w-full\">\r\n                        {emailError && (\r\n                          <span className=\"text-red-500 text-xs text-left w-full mb-2\">\r\n                            {emailError}\r\n                          </span>\r\n                        )}\r\n                        <input\r\n                          className=\"placeholder:italic placeholder:text-[#597291] block bg-white w-full border border-slate-300 rounded-md mb-3 py-2 pl-3 pr-3 shadow-sm focus:outline-none focus:border-sky-500 focus:ring-sky-500 focus:ring-1 sm:text-sm\"\r\n                          aria-label=\"Email Address\"\r\n                          placeholder=\"Email Address *\"\r\n                          type=\"email\"\r\n                          name=\"email\"\r\n                          // onBlur={validateEmail}\r\n                          onKeyUp={handleChange}\r\n                          onChange={(event) => handleChange(event, \"email\")}\r\n                        />\r\n                      </label>\r\n                      <label className=\"block w-full\">\r\n                        {passwordError && (\r\n                          <span className=\"text-red-500 text-xs text-left w-full mb-2\">\r\n                            {passwordError}\r\n                          </span>\r\n                        )}\r\n                        <div className=\"relative block w-full\">\r\n                          <input\r\n                            className=\"placeholder:italic placeholder:text-[#597291] block bg-white w-full border border-slate-300 rounded-md mb-3 py-2 pl-3 shadow-sm focus:outline-none focus:border-sky-500 focus:ring-sky-500 focus:ring-1 sm:text-sm pr-10\"\r\n                            aria-label=\"Password\"\r\n                            placeholder=\"Password *\"\r\n                            type=\"password\"\r\n                            name=\"password\"\r\n                            // onBlur={validatePassword}\r\n                            onKeyUp={handleChange}\r\n                            onChange={(event) =>\r\n                              handleChange(event, \"password\")\r\n                            }\r\n                            ref={pwInputRef}\r\n                          />\r\n                          <PasswordToggler\r\n                            className=\"right-3 top-3\"\r\n                            pwInputRef={pwInputRef}\r\n                            setShowPW={setShowPW}\r\n                            showPW={showPW}\r\n                          />\r\n                        </div>\r\n                      </label>\r\n                    </>\r\n                  )}\r\n                  <motion.button\r\n                    onClick={loginUser}\r\n                    className=\"bg-blue-600 text-white font-bold py-3 px-6 my-3 rounded-lg w-full login-btn\"\r\n                    whileHover={{ scale: 1.1 }}\r\n                    whileTap={{ scale: 0.9 }}\r\n                    aria-label=\"login\"\r\n                  >\r\n                    Login\r\n                  </motion.button>\r\n                  <motion.button\r\n                    onClick={() => handleRedirect(\"/register-auth\")}\r\n                    className=\"bg-gray-50 mb-1 text-black py-3 rounded-lg w-full\"\r\n                    whileHover={{ backgroundColor: \"#eee\" }}\r\n                    whileTap={{ scale: 0.9 }}\r\n                    aria-label=\"register\"\r\n                  >\r\n                    Register\r\n                  </motion.button>\r\n                  <motion.button\r\n                    onClick={() => handleRedirect(\"/forgot\")}\r\n                    className=\"bg-gray-50 mb-1 text-black py-3 rounded-lg w-full\"\r\n                    whileHover={{ backgroundColor: \"#eee\" }}\r\n                    whileTap={{ scale: 0.9 }}\r\n                    aria-label=\"forgot password\"\r\n                  >\r\n                    Forgot Password\r\n                  </motion.button>\r\n\r\n                  <div className=\"google-button\">\r\n                    <button\r\n                      type=\"button\"\r\n                      className=\"bg-white w-full py-3 border border-slate-300 rounded-lg\"\r\n                      onClick={() => openPopup(\"/google/login\", \"GoogleLogin\")}\r\n                      onKeyDown={(e) => {\r\n                        if (e.key === 'Enter') {\r\n                          openPopup(\"/google/login\", \"GoogleLogin\");\r\n                        }\r\n                      }}\r\n                        tabIndex={0}\r\n                    >\r\n                      <div className=\"google-button-icon\">\r\n                        <LazyImage\r\n                            src={img2Src}\r\n                          alt=\"GoogleLogin\"\r\n                            className=\"w-[20px] max-h-[20px] mb-1\"\r\n                          width=\"20\"\r\n                          height=\"20\"\r\n                        />\r\n                      </div>\r\n                      <div className=\"google-button-text\">Login via Google</div>\r\n                    </button>\r\n                  </div>\r\n\r\n                  <div className=\"apple-button\">\r\n                    <button\r\n                      type=\"button\"\r\n                      className=\"bg-white w-full py-3 border border-slate-300 rounded-lg\"\r\n                      onClick={() => openPopup(\"/apple/login\", \"AppleLogin\")}\r\n                      onKeyDown={(e) => {\r\n                        if (e.key === 'Enter') {\r\n                          openPopup(\"/apple/login\", \"AppleLogin\");\r\n                        }\r\n                      }}\r\n                        tabIndex={0}\r\n                    >\r\n                      <div className=\"apple-button-icon\">\r\n                        <LazyImage\r\n                          src={imgAppleSrc}\r\n                          alt=\"AppleLogin\"\r\n                          className=\"w-[16px] max-h-[20px]\"\r\n                          width=\"16\"\r\n                          height=\"20\"\r\n                        />\r\n                      </div>\r\n                      <div className=\"apple-button-text\">Login via Apple</div>\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      )}\r\n      {/* <Suspense fallback={null}>\r\n        {renderHeader && <Footer />}\r\n      </Suspense> */}\r\n    </>\r\n  );\r\n}\r\n\r\nexport default Login;\r\n", "import React, { useCallback } from \"react\";\r\nimport { FiEye } from \"react-icons/fi\";\r\n\r\nfunction PasswordToggler({ className, pwInputRef, setShowPW, showPW }) {\r\n  const handleClick = useCallback(() => {\r\n    const pwInput = pwInputRef.current;\r\n\r\n    setShowPW(!showPW);\r\n\r\n    if (!showPW) {\r\n      pwInput.type = \"text\";\r\n    } else {\r\n      pwInput.type = \"password\";\r\n    }\r\n\r\n    pwInput.focus();\r\n  }, [setShowPW, pwInputRef, showPW]);\r\n\r\n  return (\r\n    <>\r\n      <button\r\n        className={`absolute cursor-pointer w-4 h-4 [&_svg]:w-auto [&_svg]:h-auto [&_svg]:max-w-full [&_svg]:max-h-full z-10 fill-[#597291] fill-gray-150 hover:ring-8 hover:ring-slate-300 rounded-full hover:bg-slate-300 transition-all duration-200 ease-linear ${className}`}\r\n        onClick={handleClick}\r\n        type=\"button\"\r\n        title={`${showPW ? \"Hide\" : \"Show\"} Password`}\r\n      >\r\n        <FiEye style={{ color: 'gray' }} />\r\n      </button>\r\n      <span\r\n        className={`absolute z-10 w-[18px] h-[18px] pointer-events-none -rotate-45 mr-[-1px] mt-[-1px] flex items-center justify-center overflow-hidden ${className} after:block after:w-0.5 after:bg-[#597291] after:transition-all after:duration-200 after:ease-in after:h-[18px] ${\r\n          showPW ? \"\" : \"after:-translate-y-full\"\r\n        }`}\r\n      />\r\n    </>\r\n  );\r\n}\r\n\r\nexport default PasswordToggler;\r\n"], "names": ["_ref", "src", "alt", "props", "_jsx", "Suspense", "fallback", "children", "isWebpSupported", "elem", "document", "createElement", "getContext", "toDataURL", "indexOf", "hexHash", "hex", "length", "test", "slice", "hoverDarken", "r", "parseInt", "g", "b", "Math", "max", "min", "toHex", "value", "round", "toString", "padStart", "useDeviceSize", "isMobile", "setIsMobile_", "useState", "isTablet", "setIsTablet", "isDesktop", "setIsDesktop", "useEffect", "handleResize", "width", "window", "innerWidth", "addEventListener", "removeEventListener", "initSocket", "aiwp_logged_in", "Get<PERSON><PERSON><PERSON>", "run_socket", "myarr", "split", "URL", "process", "REACT_APP_SOCKET_URL", "socket", "io", "sidx", "emit", "id", "email", "socket_key", "REACT_APP_SOCKET_KEY", "params", "URLSearchParams", "location", "search", "client_io", "img2Src", "img2webp", "img2", "imgAppleSrc", "imgApplewebp", "imgApple", "setEmail", "password", "setPassword", "emailError", "setEmailError", "passwordError", "setPasswordError", "showBanner", "setShowBanner", "view_data", "bannerMessage", "setBannerMessage", "showPW", "setShowPW", "auth", "<PERSON><PERSON>", "smooth_login", "spanErrorRefs", "useRef", "formRef", "pwInputRef", "currentLocation", "href", "prefix", "handleRedirect", "path", "openPopup", "url", "name", "screenWidth", "screen", "windowFeatures", "height", "top", "open", "error", "setSpanHeights", "useCallback", "current", "for<PERSON>ach", "ref", "i", "_ref$scrollHeight", "errMsg", "textContent", "currHeight", "offsetHeight", "scrollHeight", "setTimeout", "style", "form", "isChromium", "navigator", "userAgent", "includes", "inputs", "querySelectorAll", "input", "matches", "classList", "add", "toastr", "positionClass", "<PERSON><PERSON><PERSON><PERSON>", "expires", "Date", "google_error_code", "get", "google_error", "errors", "find", "error_code", "undefined", "msg", "isFormValid", "trim", "lpC<PERSON>ie", "loginUser", "isEmailPassed", "validateEmail", "isPassed", "isPasswordPassed", "validatePassword", "querySelector", "axios", "post", "headers", "then", "res", "output", "data", "status", "redirect_url", "success", "remove", "handleChange", "event", "inputType", "el", "target", "isFocus", "type", "isInput", "keyCode", "_jsxs", "_Fragment", "<PERSON><PERSON><PERSON>", "content", "className", "loginLogo", "placeholder", "onChange", "onKeyUp", "Pass<PERSON><PERSON>oggler", "motion", "button", "onClick", "disabled", "whileHover", "scale", "whileTap", "onKeyDown", "e", "key", "tabIndex", "LazyImage", "preventDefault", "closeBanner", "onBlur", "onFocus", "for", "backgroundColor", "handleClick", "pwInput", "focus", "title", "FiEye", "color"], "sourceRoot": ""}