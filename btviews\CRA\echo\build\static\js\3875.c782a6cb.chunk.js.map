{"version": 3, "file": "static/js/3875.c782a6cb.chunk.js", "mappings": "gKA0BA,QAxBA,WAGE,MAAMA,EAAWC,yBAWjB,OAVAC,EAAAA,EAAAA,WAAU,KACR,MAAMC,EAASC,SAASC,cAAc,UACtCF,EAAOG,IAAMN,EAAW,iDACxBG,EAAOI,OAAQ,EACfJ,EAAOK,OAAS,OAIhBJ,SAASK,KAAKC,YAAYP,IACzB,CAACH,KAEFW,EAAAA,EAAAA,KAAAC,EAAAA,SAAA,CAAAC,UACEF,EAAAA,EAAAA,KAAA,UAAQG,UAAW,iHAMzB,C,2JCfIC,EAAmB,IACnBC,EAAU,GAEP,SAASC,EAAkBC,GAA0H,IAAxH,kBAACC,EAAiB,qBAAEC,EAAoB,mBAAEC,EAAkB,8BAAEC,EAA6B,wBAAEC,GAAwBL,EACvJ,MAAOM,EAAcC,IAAmBC,EAAAA,EAAAA,UAAS,IAC1CC,EAAaC,IAAkBF,EAAAA,EAAAA,UAAS,IACxCG,EAAiBC,IAAsBJ,EAAAA,EAAAA,UAAS,IAEjDK,GAAOC,EAAAA,EAAAA,OAEb9B,EAAAA,EAAAA,WAAU,UACK+B,IAATF,GAAkC,eAAZA,EAAKG,OAC7BnB,EAAmBgB,EAAKhB,iBAEU,YAA9BgB,EAAKI,SAASC,cAChBN,EAAmB,SAEnBA,EAAmB,UAItB,CAACC,IAGJ,MAOMM,EAA2BA,KAC/B,IAAIC,EAAQlC,SAASmC,eAAe,uBACxB,OAARD,IACFA,EAAME,MAAMC,QAAU,OACtBrB,GAAqB,MAczBlB,EAAAA,EAAAA,WAAU,KACRwC,IAAAA,QAAiB,CACfC,cAAe,qBAEhB,KAEHzC,EAAAA,EAAAA,WAAU,KAER0B,EADaJ,EAAeT,IAE3B,CAACS,KAEJtB,EAAAA,EAAAA,WAAU,KACRuB,EAAgB,GAGhBG,EADaJ,EAAeT,IAG3B,CAACI,IAoBJ,YAPwBc,IAApBd,IAAuD,IAAtBA,GAvDLyB,MAC9B,IAAIN,EAAQlC,SAASmC,eAAe,uBACxB,OAARD,IACFA,EAAME,MAAMC,QAAU,UAqDxBG,QAEsBX,IAApBd,IAAuD,IAAtBA,GACnCkB,KAIA1B,EAAAA,EAAAA,KAAAC,EAAAA,SAAA,CAAAC,UACFF,EAAAA,EAAAA,KAAA,OAAKkC,GAAG,sBAAsB/B,UAAU,iBAAgBD,UACvDF,EAAAA,EAAAA,KAAA,OAAKmC,MAAM,yDAAwDjC,UAC9DkC,EAAAA,EAAAA,MAAA,OAAAlC,SAAA,EACEkC,EAAAA,EAAAA,MAAA,OAAKjC,UAAU,0BAAyBD,SAAA,EACtCF,EAAAA,EAAAA,KAAA,OAAKG,UAAU,uDAAsDD,SAAC,sBAGtEF,EAAAA,EAAAA,KAAA,OAAKG,UAAU,wCAAuCD,UACpDF,EAAAA,EAAAA,KAAA,QAAMmC,MAAM,QAAQE,QAASA,IAAKX,IAA2BxB,SAAC,YAGlEkC,EAAAA,EAAAA,MAAA,OAAKjC,UAAU,gDAA+CD,SAAA,EAC5DF,EAAAA,EAAAA,KAAA,OAAAE,SAAK,gEACLF,EAAAA,EAAAA,KAAA,OAAAE,SAAK,kDACLkC,EAAAA,EAAAA,MAAA,OAAKjC,UAAU,mBAAkBD,SAAA,EAC/BF,EAAAA,EAAAA,KAAA,QAAMG,UAAU,uDAAsDD,SAAC,SACvEkC,EAAAA,EAAAA,MAAA,OAAKjC,UAAU,kCAAiCD,SAAA,EAC9CF,EAAAA,EAAAA,KAAA,SAAOG,UAAU,8DAA8DmC,KAAK,SAASC,MAAM,IAAIF,QAASA,KA7DxHxB,EAAa,GACfC,EAAgBD,EAAa,OA6DnBb,EAAAA,EAAAA,KAAA,QAAMG,UAAU,sCAAqCD,SAAEW,KACvDb,EAAAA,EAAAA,KAAA,SAAOG,UAAU,8DAA8DmC,KAAK,SAASC,MAAM,IAAIF,QAASA,KAnE5HvB,EAAgBD,EAAa,UAqEnBb,EAAAA,EAAAA,KAAA,QAAMG,UAAU,uDAAsDD,SAAC,iBAEzEkC,EAAAA,EAAAA,MAAA,OAAKjC,UAAU,oBAAmBD,SAAA,CAAC,kBACjBc,MAElBoB,EAAAA,EAAAA,MAAA,OAAKjC,UAAU,oBAAmBD,SAAA,CAAC,OAC5BgB,MAEPlB,EAAAA,EAAAA,KAAA,OAAAE,UACEF,EAAAA,EAAAA,KAACwC,EAAAA,EAAOC,OAAM,CACdtC,UAAU,oFACVuC,WAAY,CAAEC,gBAAiB,WAC/BC,SAAU,CAAEC,MAAO,IACnBR,QAASA,KArDnBS,OAAOC,SAASC,KAAO,gBAAgBnC,GAqDKX,SACjC,6BAUf,CAEO,SAAS+C,EAAsBC,GAAiG,IAA/F,gBAACC,EAAe,2BAAEC,EAA0B,wBAAExC,EAAuB,qBAAEyC,GAAqBH,EAElI,MAAM9B,GAAOC,EAAAA,EAAAA,OAEb9B,EAAAA,EAAAA,WAAU,UACK+B,IAATF,GAAkC,eAAZA,EAAKG,OAC7BlB,EAAUe,EAAKf,UAEhB,CAACe,IAEJ,MAOMkC,EAAaA,KACjB,IAAI3B,EAAQlC,SAASmC,eAAe,iBACxB,OAARD,IACFA,EAAME,MAAMC,QAAU,OACtBlB,GAAwB,KA8C5B,YAP2BU,IAAvB+B,IAA6D,IAAzBA,GAlDtBE,MAChB,IAAI5B,EAAQlC,SAASmC,eAAe,iBACxB,OAARD,IACFA,EAAME,MAAMC,QAAU,UAgDxByB,QAEyBjC,IAAvB+B,IAA6D,IAAzBA,GACtCC,KAIAtD,EAAAA,EAAAA,KAAAC,EAAAA,SAAA,CAAAC,UACAF,EAAAA,EAAAA,KAAA,OAAKkC,GAAG,gBAAgB/B,UAAU,iBAAgBD,UAChDkC,EAAAA,EAAAA,MAAA,OAAKD,MAAM,4EAA2EjC,SAAA,EACpFF,EAAAA,EAAAA,KAAA,QAAMmC,MAAM,QAAQE,QAASA,IAAKiB,IAAapD,SAAC,OAChDF,EAAAA,EAAAA,KAAA,OAAKG,UAAU,sCAAqCD,UAACF,EAAAA,EAAAA,KAAA,OAAKL,IAAK6D,EAAAA,EAAWC,IAAI,cAActD,UAAU,yBACtGiC,EAAAA,EAAAA,MAAA,MAAIjC,UAAU,qEAAoED,SAAA,CAAC,mBAAeF,EAAAA,EAAAA,KAAA,SAAK,2BACvGA,EAAAA,EAAAA,KAAA,OAAKG,UAAU,yCAAwCD,SAAC,kEACxDF,EAAAA,EAAAA,KAAA,OAAKG,UAAU,yCAAwCD,SAAC,sFACxDkC,EAAAA,EAAAA,MAAA,OAAKjC,UAAU,OAAMD,SAAA,EACnBkC,EAAAA,EAAAA,MAAA,OAAKjC,UAAU,wBAAuBD,SAAA,CAAC,mBAAiBiD,MACxDf,EAAAA,EAAAA,MAAA,OAAKjC,UAAU,wBAAuBD,SAAA,CAAC,wBAAsBkD,SAE/DhB,EAAAA,EAAAA,MAAA,OAAKjC,UAAU,+DAA8DD,SAAA,EAC3EF,EAAAA,EAAAA,KAAA,OAAKG,UAAU,YAAWD,SAAC,sBAC3BF,EAAAA,EAAAA,KAAA,OAAKG,UAAU,4EAA4EkC,QAASA,KAAKqB,OAnD3GC,EAAUR,EACVS,EAAeR,EAGnBE,IACA7D,SAASoE,cAAc,qBAAqBC,UAAUC,IAAI,eAE1DC,EAAAA,EAAMC,KALI,2DAKM,CACdN,UACAC,gBACC,CAAEM,QAAS,CAAE,eAAgB,uCAAyCC,KAAK,SAASC,GACrF,IAAIC,EAASD,EAAIE,KAEjB7E,SAASoE,cAAc,qBAAqBC,UAAUS,OAAO,UAE1DF,EAAOG,QACRzC,IAAAA,QAAe,iBAAiBsC,EAAOC,MAEvCvC,IAAAA,MAAa,gBAEjB,GAAG0C,MAAM,SAAUC,GACjBjF,SAASoE,cAAc,qBAAqBC,UAAUS,OAAO,UACzDG,EAAMC,UAAoC,MAAxBD,EAAMC,SAASC,SACnCnF,SAASoE,cAAc,qBAAqBC,UAAUS,OAAO,UAC7DxC,IAAAA,MAAa,wDAEjB,GA3BmB2B,IACfC,EACAC,GAkD0H1D,SAAC,oBACzHkC,EAAAA,EAAAA,MAAA,OAAAlC,SAAA,EAAKF,EAAAA,EAAAA,KAAA,QAAMG,UAAU,YAAWD,SAAC,iBAAmB,uCACpDkC,EAAAA,EAAAA,MAAA,OAAAlC,SAAA,EAAKF,EAAAA,EAAAA,KAAA,QAAMG,UAAU,YAAWD,SAAC,WAAa,gBAC9CkC,EAAAA,EAAAA,MAAA,OAAAlC,SAAA,EAAKF,EAAAA,EAAAA,KAAA,QAAMG,UAAU,YAAWD,SAAC,eAAiB,uBAClDkC,EAAAA,EAAAA,MAAA,OAAAlC,SAAA,EAAKF,EAAAA,EAAAA,KAAA,QAAMG,UAAU,YAAWD,SAAC,oBAAsB,iBACvDkC,EAAAA,EAAAA,MAAA,OAAAlC,SAAA,EAAKF,EAAAA,EAAAA,KAAA,QAAMG,UAAU,YAAWD,SAAC,yCAA2C,iBAC5EkC,EAAAA,EAAAA,MAAA,OAAAlC,SAAA,EAAKF,EAAAA,EAAAA,KAAA,QAAMG,UAAU,YAAWD,SAAC,oBAAsB,sBACvDkC,EAAAA,EAAAA,MAAA,OAAAlC,SAAA,EAAKF,EAAAA,EAAAA,KAAA,QAAMG,UAAU,YAAWD,SAAC,qBAAuB,IAAEG,MAC1D+B,EAAAA,EAAAA,MAAA,OAAKjC,UAAU,kDAAiDD,SAAA,EAACF,EAAAA,EAAAA,KAAC6E,EAAAA,IAAY,CAAC1E,UAAU,wBAAuB,0HAGlHH,EAAAA,EAAAA,KAAA,OAAKG,UAAU,8CAA6CD,SAAC,8IAG7DkC,EAAAA,EAAAA,MAAA,OAAKjC,UAAU,yCAAwCD,SAAA,CAAC,iBACzCF,EAAAA,EAAAA,KAAA,KAAAE,SAAG,qBAAoB,kDAEtCF,EAAAA,EAAAA,KAAA,OAAKG,UAAU,cAAaD,UAC1BF,EAAAA,EAAAA,KAACwC,EAAAA,EAAOC,OAAM,CACZtC,UAAU,sEACVuC,WAAY,CAAEC,gBAAiB,WAC/BC,SAAU,CAAEC,MAAO,IACnBR,QA/EuByC,KAC/BhC,OAAOC,SAASC,KAAO,sBA8EmB9C,SACnC,sCAQX,C,4IC3PA,MAyFA,EAzFaK,IAWN,IAXO,KACZwE,EAAI,IACJC,EAAG,KACHC,EAAI,YACJC,EAAW,KACXlC,EAAI,OACJmC,EAAM,QACN9C,EAAO,gBACP+C,EAAe,aACfC,EAAY,WACZC,GACD/E,EACC,MAqBMgF,EAAoB,SAASR,IAEnC,OACE3C,EAAAA,EAAAA,MAAA,OAAKjC,UAAU,sFAAqFD,SAAA,CACjGsF,QAAQR,KACP5C,EAAAA,EAAAA,MAAA,OAAKjC,UAAU,mBAAkBD,SAAA,CACtB,SAAR8E,IAAkBhF,EAAAA,EAAAA,KAAA,OAAKG,UAAU,cAAaD,SAAC,SACvC,YAAR8E,IAAqBhF,EAAAA,EAAAA,KAAA,OAAKG,UAAU,iBAAgBD,SAAC,YAC7C,QAAR8E,IAAiBhF,EAAAA,EAAAA,KAAA,OAAKG,UAAU,SAAQD,SAAC,YAG9CkC,EAAAA,EAAAA,MAAA,OAAKjC,UAAU,4BAA2BD,SAAA,EACxCF,EAAAA,EAAAA,KAAA,OACEL,IAAKsF,EACLxB,IAAK,GAAGsB,SACR,aAAY,GAAGA,SACf5E,UAAU,kBArCDsF,MACf,MAAMC,GACJ1F,EAAAA,EAAAA,KAAA,MACEG,UAAW,sCACTiF,EAAkB,GAAK,aACtBlF,SAEF6E,IAGL,OAAKK,GAEHpF,EAAAA,EAAAA,KAAA,OAAKG,UAAU,iBAAgBD,SAC5BwF,IAHwBA,GA6BxBD,OAEHzF,EAAAA,EAAAA,KAAA,KAAGG,UAAU,6JAA4JD,SACtKgF,IAEFG,GACCrF,EAAAA,EAAAA,KAACwC,EAAAA,EAAOmD,EAAC,CACPxF,UAAU,4HACV4E,KAAK,cACL,aAAW,cACXa,KAAK,SAAQ1F,SACd,iBAIDkC,EAAAA,EAAAA,MAACI,EAAAA,EAAOmD,EAAC,CACPxF,UAAU,2HACVuC,WAAY,CAAEC,gBAAiB,WAC/BC,SAAU,CAAEC,MAAO,IACnBG,KAAMA,EACNmC,OAAQA,EACR9C,QAASA,EACT0C,KAAMQ,EACN,aAAYA,EACZK,KAAK,SAAQ1F,SAAA,CAEZoF,GAAcC,EAAmB,KAClCvF,EAAAA,EAAAA,KAAC6F,EAAAA,IAAY,CACX,aAAW,cACXD,KAAK,MACLzF,UAAU,wD,qGCrEtB,MAAM2F,EACFC,KAAKC,MAAM1G,8jBAgMf,QA7LA,SAAe2G,GACb,MAAOC,EAAUC,IAAepF,EAAAA,EAAAA,WAAS,GACnCK,EAAO6E,GAASA,EAAM7E,KAAO6E,EAAM7E,KAAO,KAC1CgF,EAAOhF,GAAQA,EAAKiF,SAAW,SAAWjF,EAAKiF,SAAW,GAE1DC,EAAiBR,EAAMS,QACzBT,EAAMS,QAAUH,EAChB,qCAAuCA,EACrCI,EAAkBV,EAAMW,YAC1BX,EAAMW,YAAcL,EACpB,iCAAmCA,EACjCM,EAAmBZ,EAAMa,aAC3Bb,EAAMa,aAAeP,EACrB,mCAAqCA,EACnCQ,EAAWd,EAAMc,SACnBd,EAAMc,SAAWR,EACjB,6BAA+BA,EAC7BS,EAAoBf,EAAMgB,YAC5BhB,EAAMgB,YAAcV,EACpB,iCAAmCA,EACjCW,EAAkBjB,EAAMkB,UAC1BlB,EAAMkB,UAAYZ,EAClB,+BAAiCA,EAC/Ba,EAAYnB,EAAMkB,UACpBlB,EAAMkB,UAAYZ,EAClB,0BAA4BA,GAEhC7G,EAAAA,EAAAA,WAAU,KACR,MAAM2H,EAAeA,KACnBf,EAAYrD,OAAOqE,YAAc,MAMnC,OAHAD,IAEApE,OAAOsE,iBAAiB,SAAUF,GAC3B,KACLpE,OAAOuE,oBAAoB,SAAUH,KAEtC,IAEH,MAAMI,EAAiBC,KACrBC,EAAAA,EAAAA,IAAYpG,EAAMmG,IAGdE,EAAmBF,IAChBG,EAAAA,EAAAA,IAActG,EAAMmG,GA0BvBI,EAAYzB,EAvBH3F,IAAmB,IAAlB,SAAEL,GAAUK,EAC1B,OACEP,EAAAA,EAAAA,KAAC4H,EAAAA,GAAQ,CACPzH,UAAU,OACV0H,cAAc,EACdC,YAAY,EACZC,cAAc,EACdC,UAAU,EACVC,uBAAuB,EAAK/H,SAE3BA,KAKKgD,IAAmB,IAAlB,SAAEhD,GAAUgD,EACvB,OACElD,EAAAA,EAAAA,KAAA,OAAKG,UAAU,+CAA8CD,SAC1DA,KAOP,OACEF,EAAAA,EAAAA,KAAA,OAAKG,UAAU,mDAAkDD,UAC/DF,EAAAA,EAAAA,KAAA,OAAKG,UAAU,4BAA2BD,UACxCF,EAAAA,EAAAA,KAAA,OAAKG,UAAU,yBAAwBD,UACrCkC,EAAAA,EAAAA,MAAA,OAAKjC,UAAU,4FAA2FD,SAAA,EACxGkC,EAAAA,EAAAA,MAAA,OAAKjC,UAAU,0BAAyBD,SAAA,EACtCF,EAAAA,EAAAA,KAAA,MAAIG,UAAU,8CAA6CD,SAAC,sBAG5DF,EAAAA,EAAAA,KAAA,QAAMG,UAAU,sDAAqDD,SAAC,gIAMxEkC,EAAAA,EAAAA,MAACuF,EAAS,CAAAzH,SAAA,EACRF,EAAAA,EAAAA,KAACkI,EAAI,CACHnD,KAAK,aACLE,KAAMkD,EACNjD,YAAY,uGACZlC,KAAMyE,EAAgBnB,GACtBnB,OACEsC,EAAgBnB,KAAoBA,EAChC,SACA,GAENjE,QAASA,IAAMiF,EAAchB,MAE/BtG,EAAAA,EAAAA,KAACkI,EAAI,CACHnD,KAAK,cACLE,KAAMmD,EACNlD,YAAY,gIACZlC,KAAMyE,EAAgBjB,GACtBrB,OACEsC,EAAgBjB,KAAqBA,EACjC,SACA,GAENnE,QAASA,IAAMiF,EAAcd,MAE/BxG,EAAAA,EAAAA,KAACkI,EAAI,CACHnD,KAAK,gBACLE,KAAMoD,EACNnD,YAAY,8FACZlC,KAAMyE,EAAgBf,GACtBvB,OACEsC,EAAgBf,KAAsBA,EAClC,SACA,GAENrE,QAASA,IAAMiF,EAAcZ,MAE/B1G,EAAAA,EAAAA,KAACkI,EAAI,CACHnD,KAAK,oBACLO,WAAW,kBACXL,KAAMqD,EACNpD,YAAY,kFACZlC,KAAMyE,EAAgBb,GACtBzB,OAAQsC,EAAgBb,KAAcA,EAAW,SAAW,GAC5DvE,QAASA,IAAMiF,EAAcV,MAE/B5G,EAAAA,EAAAA,KAACkI,EAAI,CACHlD,IAAI,MACJD,KAAK,eACLE,KAAMsD,EACNrD,YAAY,0IAGZlC,KAAMyE,EAAgBZ,GACtB1B,OACEsC,EAAgBZ,KAAuBA,EACnC,SACA,GAENxE,QAASA,IAAMiF,EAAcT,MAE/B7G,EAAAA,EAAAA,KAACkI,EAAI,CACHlD,IAAI,MACJD,KAAK,YACLE,KAAMuD,EACNtD,YAAY,6KACZlC,KAAMyE,EAAgBV,GACtB5B,OACEsC,EAAgBV,KAAqBA,EACjC,SACA,GAEN1E,QAASA,IAAMiF,EAAcP,MAE/B/G,EAAAA,EAAAA,KAACkI,EAAI,CACHlD,IAAI,OACJD,KAAK,gBACLE,KAAMwD,EACNvD,YAAY,iMACZlC,KAAMyE,EAAgBR,GACtB9B,OACEsC,EAAgBR,KAAeA,EAC3B,SACA,GAEN5E,QAASA,IAAMiF,EAAcL,MAE/BjH,EAAAA,EAAAA,KAACkI,EAAI,CACH7C,cAAY,EACZN,KAAK,eACLE,KAAMyD,EACNxD,YAAY,wGAQ5B,E,gJC1LA,MAAMY,EACFC,KAAKC,MAAM1G,8jBA+Tf,QA5TA,SAAoB2G,GAClB,MAAOC,EAAUC,IAAepF,EAAAA,EAAAA,WAAS,GACnCK,EAAO6E,GAASA,EAAM7E,KAAO6E,EAAM7E,KAAO,KAC1CgF,EAAOhF,GAAQA,EAAKiF,SAAW,SAAWjF,EAAKiF,SAAW,GAC1DsC,EAAiB7C,EAAM8C,QACzB9C,EAAM8C,QAAUxC,EAChB,kCAAoCA,EAClCyC,EAAc/C,EAAMgD,QACtBhD,EAAMgD,QAAU1C,EAChB,6BAA+BA,EAC7B2C,EAAgBjD,EAAMkD,UACxBlD,EAAMkD,UAAY5C,EAClB,+BAA+BA,EAC7B6C,EAAqBnD,EAAMoD,gBAC7BpD,EAAMoD,gBAAkB9C,EACxB,mDAAqDA,EACnD+C,EAAkBrD,EAAMsD,WAC1BtD,EAAMsD,WAAahD,EACnB,6BAA+BA,EAC7BiD,EAAgBvD,EAAMwD,SACxBxD,EAAMwD,SAAWlD,EACjB,2CAA6CA,EAC3CmD,EAAoBzD,EAAM0D,aAC5B1D,EAAM0D,aAAepD,EACrB,2CAA6CA,EAC3CqD,EAAqB3D,EAAM4D,cAC7B5D,EAAM4D,cAAgBtD,EACtB,sDAAwDA,EACtDuD,EAAgB7D,EAAM8D,SACxB9D,EAAM8D,SAAWxD,EACjB,4BAA8BA,EAC5ByD,EAAgB/D,EAAMgE,SACxBhE,EAAMgE,SAAW1D,EACjB,8BAAgCA,EAC9B2D,EAAejE,EAAMkE,QACvBlE,EAAMkE,QAAU5D,EAChB,6BAA+BA,EAC7B6D,EAAgBnE,EAAMoE,SACxBpE,EAAMoE,SAAW9D,EACjB,4BAA8BA,EAC5B+D,EAAgBrE,EAAMsE,SACxBtE,EAAMsE,SAAWhE,EACjB,2BAA6BA,GAEjC7G,EAAAA,EAAAA,WAAU,KACR,MAAM2H,EAAeA,KACnBf,EAAYrD,OAAOqE,YAAc,MAMnC,OAHAD,IAEApE,OAAOsE,iBAAiB,SAAUF,GAC3B,KACLpE,OAAOuE,oBAAoB,SAAUH,KAEtC,IAEH,MAAMI,EAAiBC,KACrBC,EAAAA,EAAAA,IAAYpG,EAAMmG,IAGdE,EAAmBF,IAChBG,EAAAA,EAAAA,IAActG,EAAMmG,GA0BvBI,EAAYzB,EAvBH3F,IAAmB,IAAlB,SAAEL,GAAUK,EAC1B,OACEP,EAAAA,EAAAA,KAAC4H,EAAAA,GAAQ,CACPzH,UAAU,OACV0H,cAAc,EACdC,YAAY,EACZC,cAAc,EACdC,UAAU,EACVC,uBAAuB,EAAK/H,SAE3BA,KAKKgD,IAAmB,IAAlB,SAAEhD,GAAUgD,EACvB,OACElD,EAAAA,EAAAA,KAAA,OAAKG,UAAU,+CAA8CD,SAC1DA,KAOP,OACEF,EAAAA,EAAAA,KAAA,OAAKG,UAAU,uDAAsDD,UACnEF,EAAAA,EAAAA,KAAA,OAAKG,UAAU,4BAA2BD,UACxCF,EAAAA,EAAAA,KAAA,OAAKG,UAAU,yBAAwBD,UACrCkC,EAAAA,EAAAA,MAAA,OACEF,GAAG,4BACH/B,UAAU,4FAA2FD,SAAA,EAErGkC,EAAAA,EAAAA,MAAA,OAAKjC,UAAU,0BAAyBD,SAAA,EACtCF,EAAAA,EAAAA,KAAA,MAAIG,UAAU,8CAA6CD,SAAC,iBAG5DF,EAAAA,EAAAA,KAAA,QAAMG,UAAU,sDAAqDD,SAAC,wGAMxEkC,EAAAA,EAAAA,MAACuF,EAAS,CAAAzH,SAAA,EACRF,EAAAA,EAAAA,KAACkI,EAAI,CACH9C,iBAAe,EACfL,KAAK,cACLC,IAAI,UACJC,KAAMoF,EACNnF,YAAY,+GACZlC,KAAMyE,EAAgBkB,GACtBxD,OACEsC,EAAgBkB,KAAoBA,EAChC,SACA,GAENtG,QAASA,IAAMiF,EAAcqB,MAE/B3I,EAAAA,EAAAA,KAACkI,EAAI,CACHnD,KAAK,UACLE,KAAMqF,EACNpF,YAAY,yJACZlC,KAAMyE,EAAgBoB,GACtB1D,OACEsC,EAAgBoB,KAAiBA,EAAc,SAAW,GAE5DxG,QAASA,KACPiF,EAAcuB,OAGlB7I,EAAAA,EAAAA,KAACkI,EAAI,CACHlD,IAAI,MACJD,KAAK,aACLE,KAAMsF,EACNrF,YAAY,0LACZlC,KAAMyE,EAAgBsB,GACtB5D,OACEsC,EAAgBsB,KAAmBA,EAAgB,SAAW,GAEhE1G,QAASA,KACPiF,EAAcyB,OAGlB/I,EAAAA,EAAAA,KAACkI,EAAI,CACHnD,KAAK,aACLE,KAAMuF,EACNtF,YAAY,sIAGZlC,KAAMyE,EAAgBwB,GACtB9D,OACEsC,EAAgBwB,KAAwBA,EACpC,SACA,GAEN5G,QAASA,KACPiF,EAAc2B,OAGlBjJ,EAAAA,EAAAA,KAACkI,EAAI,CACHnD,KAAK,YACLE,KAAMwF,EACNvF,YAAY,iLAGZlC,KAAMyE,EAAgB0B,GACtBhE,OACEsC,EAAgB0B,KAAqBA,EACjC,SACA,GAEN9G,QAASA,KACPiF,EAAc6B,OAGlBnJ,EAAAA,EAAAA,KAACkI,EAAI,CACHnD,KAAK,WACLE,KAAMyF,EACNxF,YAAY,uGAEZlC,KAAMyE,EAAgB4B,GACtBlE,OACEsC,EAAgB4B,KAAmBA,EAC/B,SACA,GAENhH,QAASA,KACPiF,EAAc+B,OAGlBrJ,EAAAA,EAAAA,KAACkI,EAAI,CACHnD,KAAK,eACLE,KAAM0F,EACNzF,YAAY,iIAGZlC,KAAMyE,EAAgB8B,GACtBpE,OACEsC,EAAgB8B,KAAuBA,EACnC,SACA,GAENlH,QAASA,KACPiF,EAAciC,OAGlBvJ,EAAAA,EAAAA,KAACkI,EAAI,CACHnD,KAAK,eACLE,KAAM2F,EACN1F,YAAY,gGAEZlC,KAAMyE,EAAgBgC,GACtBtE,OACEsC,EAAgBgC,KAAwBA,EACpC,SACA,GAENpH,QAASA,KACPiF,EAAcmC,OAGlBzJ,EAAAA,EAAAA,KAACkI,EAAI,CACHlD,IAAI,MACJD,KAAK,WACLE,KAAM4F,EACN3F,YAAY,yIAGZlC,KAAMyE,EAAgBkC,GACtBxE,OACEsC,EAAgBkC,KAAmBA,EAC/B,SACA,GAENtH,QAASA,KACPiF,EAAcqC,OAGlB3J,EAAAA,EAAAA,KAACkI,EAAI,CACHlD,IAAI,MACJD,KAAK,aACLE,KAAM6F,EACN5F,YAAY,yKAGZlC,KAAMyE,EAAgBoC,GACtB1E,OACEsC,EAAgBoC,KAAmBA,EAC/B,SACA,GAENxH,QAASA,KACPiF,EAAcuC,OAGlB7J,EAAAA,EAAAA,KAACkI,EAAI,CACHlD,IAAI,OACJD,KAAK,UACLE,KAAM8F,EACN7F,YAAY,+JAGZlC,KAAMyE,EAAgBsC,GACtB5E,OACEsC,EAAgBsC,KAAkBA,EAAe,SAAW,GAE9D1H,QAASA,KACPiF,EAAcyC,OAGlB/J,EAAAA,EAAAA,KAACkI,EAAI,CACHlD,IAAI,OACJD,KAAK,YACLE,KAAM+F,EACN9F,YAAY,kLAGZlC,KAAMyE,EAAgBwC,GACtB9E,OACEsC,EAAgBwC,KAAmBA,EAC/B,SACA,GAEN5H,QAASA,KACPiF,EAAc2C,OAGlBjK,EAAAA,EAAAA,KAACkI,EAAI,CACHnD,KAAK,gBACLE,KAAMgG,EACN/F,YAAY,iLAGZlC,KAAMyE,EAAgB0C,GACtBhF,OACEsC,EAAgB0C,KAAmBA,EAC/B,SACA,GAEN9H,QAASA,KACPiF,EAAc6C,iBAShC,E,2DCvUA,MAAMe,EAAU5L,4BAwWhB,MAAM6L,EAAyB5K,IAAwB,IAAvB,cAAE6K,GAAe7K,EAK/C,OACEP,EAAAA,EAAAA,KAAAC,EAAAA,SAAA,CAAAC,SACIkL,GACApL,EAAAA,EAAAA,KAAA,OAAKG,UAAU,4DAA2DD,UACxEF,EAAAA,EAAAA,KAAA,OAAKqL,wBAAyB,CAAEC,OAAQF,QAExCpL,EAAAA,EAAAA,KAAAC,EAAAA,SAAA,OAKV,EAzWA,WACE,MAAMmB,GAAOC,EAAAA,EAAAA,OAEN+J,EAAeG,IAAoBxK,EAAAA,EAAAA,WAAS,IAC5CyK,EAAwBC,IAA6B1K,EAAAA,EAAAA,UAAS2K,IAAW,IACzEC,EAAsBC,IAA2B7K,EAAAA,EAAAA,UAAS2K,IAAW,IACrEG,EAAYC,IAAiB/K,EAAAA,EAAAA,WAAS,IACtCgL,EAAQC,IAAajL,EAAAA,EAAAA,WAAS,IAcrCxB,EAAAA,EAAAA,WAAU,KACR,MAAMC,EAASC,SAASC,cAAc,UACtCF,EAAOG,IAAML,6CACbE,EAAO4H,iBAAiB,OAAQ,IAAI4E,GAAU,IAC9CvM,SAASwM,KAAKlM,YAAYP,IACzB,KAEHD,EAAAA,EAAAA,WAAU,KACJwM,GAnBiBG,MACrB,GAAIpJ,OAAOqJ,qBAAsB,CAC/B,MAAMC,EAAa,CACjBC,MAAO/M,wBAETwD,OAAOqJ,qBAAqBC,EAC9B,MACEE,QAAQ5H,MAAM,yCAahBwH,IACC,CAACH,KAEJxM,EAAAA,EAAAA,WAAU,MACRK,iBACE,MAAM2M,QAhDZ3M,iBACE,MAGMyE,SAHiBL,EAAAA,EAAMC,KAAK,GAAGiH,eAAsB,CAC3D,EAAG,CAAEhH,QAAS,CAAE,eAAgB,wCAERI,KACxB,GAAID,EAAOG,QAET,OADoBH,EAAOC,KAAK,GAAGkI,eAGnC,MAAO,EAEX,CAqCkCC,QACNnL,IAAlBiL,GACFhB,EAAiBgB,EAErB,CACAG,IACC,KAEHnN,EAAAA,EAAAA,WAAU,KACR,QAAa+B,IAATF,IAA+B,IAATA,EAAgB,CACxC,IAAIuL,EAAYvL,EAAKwL,WACjBC,EAAczL,EAAKyL,YACnBC,EAAoC,GAAZH,EAE5BlB,GAA0B,GAC1BG,GAAwB,GAEpBe,GAAaE,EACfjB,GAAwB,GACfkB,GAAwBD,GACjCpB,GAA0B,GAEX,WAAdrK,EAAKwD,QACNkH,GAAc,EAGlB,GACC,CAAC1K,KAEJ7B,EAAAA,EAAAA,WAAU,KACRwN,WAAW,KACT,MAAMC,GAAQC,EAAAA,EAAAA,IAAU,aAClBC,GAAcD,EAAAA,EAAAA,IAAU,eACxBE,GAASF,EAAAA,EAAAA,IAAU,UAErBE,GAAWD,GAAeC,EAAOC,SAAS,WAAuB,QAAVJ,GACzDhJ,EAAAA,EAAMC,KAAK,GAAGiH,eAAsB,CAClCmC,OAAQH,GACP,CAAEhJ,QAAS,CAAE,eAAgB,uCAAyCC,KAAMC,IAC7E,MAAME,EAAOF,EAAIE,KACjB,GAAGA,EAAKE,QAAS,CACf,MAAM+C,EAAMjD,EAAKgJ,KACL,KAAR/F,GACFgG,MAAMhG,GACLpD,KAAKQ,GAAYA,EAAS6I,QAC1BrJ,KAAKqJ,IACJ,MAAMC,EAAUC,IAAIC,gBAAgBH,GAC9BF,EAAO7N,SAASC,cAAc,KACpC4N,EAAKtK,KAAOyK,EACZH,EAAKM,aAAa,WAAY,oBAC9BnO,SAASK,KAAKC,YAAYuN,GAC1BA,EAAKO,QACLpO,SAASK,KAAKgO,YAAYR,IAGhC,EACAS,EAAAA,EAAAA,IAAa,cAAe,CAAEC,OAAQ,cAAeC,KAAM,SAG9D,MACF,IAEH,MAAOzN,EAAmBC,IAAwBM,EAAAA,EAAAA,WAAS,IACpDsC,EAAsBzC,IAA2BG,EAAAA,EAAAA,WAAS,IAC1DoC,EAAiBzC,IAAsBK,EAAAA,EAAAA,UAAS,MAChDqC,EAA4BzC,IAAiCI,EAAAA,EAAAA,UAAS,KAE7E,IAAImN,GAAc,EACdC,GAAwB,EACxBC,GAAe,EACfC,EAAkB,GAEtB,QAAa/M,IAATF,EAAoB,OACxB,OAAa,IAATA,OACF0B,OAAOC,SAASC,KAAO,WAGF,QAAjB5B,EAAKkN,SACPJ,GAAc,EAEI,eAAd9M,EAAKG,OACP4M,GAAwB,EACxBE,EAAkBjN,EAAKmN,qBAGP,eAAdnN,EAAKG,OACP6M,GAAe,IAgEnBhM,EAAAA,EAAAA,MAAAnC,EAAAA,SAAA,CAAAC,SAAA,EACEkC,EAAAA,EAAAA,MAACoM,EAAAA,EAAM,CAAAtO,SAAA,EACLF,EAAAA,EAAAA,KAAA,SAAAE,SAAO,4BACPF,EAAAA,EAAAA,KAAA,QAAM+E,KAAK,cAAc0J,QAAQ,sJAChCnP,CAAAA,SAAAA,aAAAA,WAAAA,eAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,cAAAA,EAAAA,kBAAAA,4BAAAA,mBAAAA,yBAAAA,oCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,kCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,2BAAAA,iBAAAA,4BAAAA,cAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,oBAAAA,uBAAAA,yBAAAA,8BAAAA,yBAAAA,gFAAAA,uBAAAA,6CAAAA,sBAAAA,KAAAA,uBAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,wBAAAA,KAAAA,mBAAAA,aAAAA,qCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,mCAAAA,KAAAA,8BAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,4BAAAA,4BAAAA,gCAAAA,oBAAAA,gCAAAA,oBAAAA,gCAAAA,oBAAAA,gCAAAA,oBAAAA,kCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,gCAAAA,KAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,iCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,6BAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,yBAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,iCAAAA,8GAAAA,iCAAAA,8GAAAA,oBAAAA,8jBAAYoP,6BAA+BtN,EAAKuN,QAAUrP,CAAAA,SAAAA,aAAAA,WAAAA,eAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,cAAAA,EAAAA,kBAAAA,4BAAAA,mBAAAA,yBAAAA,oCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,kCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,2BAAAA,iBAAAA,4BAAAA,cAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,oBAAAA,uBAAAA,yBAAAA,8BAAAA,yBAAAA,gFAAAA,uBAAAA,6CAAAA,sBAAAA,KAAAA,uBAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,wBAAAA,KAAAA,mBAAAA,aAAAA,qCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,mCAAAA,KAAAA,8BAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,4BAAAA,4BAAAA,gCAAAA,oBAAAA,gCAAAA,oBAAAA,gCAAAA,oBAAAA,gCAAAA,oBAAAA,kCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,gCAAAA,KAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,iCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,6BAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,yBAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,iCAAAA,8GAAAA,iCAAAA,8GAAAA,oBAAAA,8jBAAYoP,4BAA4BtB,SAAShM,EAAKuN,SAC/G3O,EAAAA,EAAAA,KAAA,UAAQsC,KAAK,kBAAkB3C,IAAI,kEAAkEC,OAAK,QAG9GI,EAAAA,EAAAA,KAAC4O,EAAAA,QAAM,CAACxN,KAAMA,EAAMX,qBAAsBA,KAC1CT,EAAAA,EAAAA,KAAA,OAAKG,UAAU,wDAAuDD,UACpEF,EAAAA,EAAAA,KAAA,OAAKG,UAAU,+BAA8BD,UAC3CkC,EAAAA,EAAAA,MAAA,OAAKjC,UAAU,0DAAyDD,SAAA,EACtEF,EAAAA,EAAAA,KAAA,MAAIG,UAAU,mFAEG,IAAf0L,GACA7L,EAAAA,EAAAA,KAAAC,EAAAA,SAAA,CAAAC,UACAF,EAAAA,EAAAA,KAAA,OAAKG,UAAU,gDAA+CD,UAC5DkC,EAAAA,EAAAA,MAAA,QAAAlC,SAAA,CAAM,+DACyDkB,EAAKyN,WAAW,MAAE7O,EAAAA,EAAAA,KAAA,QAAMqC,QA7E3EyM,KACxBhM,OAAOC,SAASC,KAAO,WA4EwG7C,UAAU,UAASD,SAAC,gBAAkB,0CAI3J,IAEiB,IAAhBgO,IAAkD,IAA1BC,IAAgD,IAAbtC,GAC1D7L,EAAAA,EAAAA,KAAAC,EAAAA,SAAA,CAAAC,UACEF,EAAAA,EAAAA,KAAA,OAAKG,UAAU,gDAA+CD,UAC5DkC,EAAAA,EAAAA,MAAA,QAAAlC,SAAA,CAAM,sFAC8EF,EAAAA,EAAAA,KAAA,QAAMqC,QAnFjF0M,KACzB,MACMC,EAAW5N,EAAK4N,SAASvN,eAE/BwN,EAAAA,EAAAA,IAAU,yBAA0B,2BACpCA,EAAAA,EAAAA,IAAU,yBAA0B,yBAA0B,CAAEhB,KAAM,OACtEgB,EAAAA,EAAAA,IAAU,yBAA0B,yBAA0B,CAAEhB,KAAM,gBACtEgB,EAAAA,EAAAA,IAAU,yBAA0B,yBAA0B,CAAEhB,KAAM,gBAEtE,MAAMiB,EAAU5P,CAAAA,SAAAA,aAAAA,WAAAA,eAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,cAAAA,EAAAA,kBAAAA,4BAAAA,mBAAAA,yBAAAA,oCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,kCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,2BAAAA,iBAAAA,4BAAAA,cAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,oBAAAA,uBAAAA,yBAAAA,8BAAAA,yBAAAA,gFAAAA,uBAAAA,6CAAAA,sBAAAA,KAAAA,uBAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,wBAAAA,KAAAA,mBAAAA,aAAAA,qCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,mCAAAA,KAAAA,8BAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,4BAAAA,4BAAAA,gCAAAA,oBAAAA,gCAAAA,oBAAAA,gCAAAA,oBAAAA,gCAAAA,oBAAAA,kCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,gCAAAA,KAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,iCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,6BAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,yBAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,iCAAAA,8GAAAA,iCAAAA,8GAAAA,oBAAAA,8jBAAY6P,mBAAqB,gBACjDF,EAAAA,EAAAA,IAAU,UAAWC,IACrBD,EAAAA,EAAAA,IAAU,UAAWC,EAAS,CAAEjB,KAAM,OACtCgB,EAAAA,EAAAA,IAAU,UAAWC,EAAS,CAAEjB,KAAM,gBACtCgB,EAAAA,EAAAA,IAAU,UAAWC,EAAS,CAAEjB,KAAM,iBACtCF,EAAAA,EAAAA,IAAa,SAGXjL,OAAOC,SAASC,KADD,QAAbgM,EACqB,0BACD,QAAbA,EACc,0BACD,QAAbA,EACc,0BACD,QAAbA,EACc,0BACD,QAAbA,EACc,0BACD,QAAbA,EACc,0BACD,QAAbA,EACc,0BACD,QAAbA,EACc,0BACD,QAAbA,EACc,0BACD,QAAbA,EACc,0BACD,QAAbA,EACc,0BAEA,oBA4C8G7O,UAAU,UAASD,SAAC,gBAAkB,uDAIhJ,IAAhBgO,IAAkD,IAA1BC,IAA+C,IAAbtC,GAA0C,KAApBwC,GACjFrO,EAAAA,EAAAA,KAAAC,EAAAA,SAAA,CAAAC,UACEF,EAAAA,EAAAA,KAAA,OAAKG,UAAU,gDAA+CD,UAC5DkC,EAAAA,EAAAA,MAAA,QAAAlC,SAAA,CAAM,wIACgIF,EAAAA,EAAAA,KAAA,KAAGgD,KAAK,iCAAiCoM,IAAI,aAAajK,OAAO,SAAQjF,UAACF,EAAAA,EAAAA,KAAA,QAAMG,UAAU,UAASD,SAAC,mBAAuB,YAIpP,IAAhBgO,IAAkD,IAA1BC,GAAsD,KAApBE,GAC3DrO,EAAAA,EAAAA,KAAAC,EAAAA,SAAA,CAAAC,UACEF,EAAAA,EAAAA,KAAA,OAAKG,UAAU,gDAA+CD,UAC5DF,EAAAA,EAAAA,KAAA,QAAAE,SAAM,8JAKO,WAAhBkB,EAAKwD,QA1DA,WAAdxD,EAAKG,MAAsC,OAAjBH,EAAKkN,SAAoC,WAAhBlN,EAAKwD,QAK1C,eAAdxD,EAAKG,MAA0D,eAAjCH,EAAKiO,UAAU5N,eAAmD,OAAjBL,EAAKkN,SAAoC,WAAhBlN,EAAKwD,QA8FrG5E,EAAAA,EAAAA,KAACmL,EAAsB,CAACC,cAAeA,KAxCnB,IAAjBgD,GAC2B,IAAzBzC,GACC3L,EAAAA,EAAAA,KAAAC,EAAAA,SAAA,CAAAC,UACEF,EAAAA,EAAAA,KAAA,OAAKG,UAAU,gDAA+CD,UAC5DkC,EAAAA,EAAAA,MAAA,QAAAlC,SAAA,EACEF,EAAAA,EAAAA,KAAA,UAAAE,SAAQ,0BAA8B,uDACtCF,EAAAA,EAAAA,KAAA,KAAGgD,KAAK,iCAAiC7C,UAAU,iBAAgBD,UAACF,EAAAA,EAAAA,KAAA,UAAAE,SAAQ,iBAAuB,iFAK/E,IAA3BsL,GACCxL,EAAAA,EAAAA,KAAAC,EAAAA,SAAA,CAAAC,UACEF,EAAAA,EAAAA,KAAA,OAAKG,UAAU,gDAA+CD,UAC5DkC,EAAAA,EAAAA,MAAA,QAAAlC,SAAA,EACEF,EAAAA,EAAAA,KAAA,UAAAE,SAAQ,yBAA6B,2EACrCF,EAAAA,EAAAA,KAAA,KAAGgD,KAAK,iCAAiC7C,UAAU,iBAAgBD,UAACF,EAAAA,EAAAA,KAAA,UAAAE,SAAQ,iBAAuB,iFAGnGF,EAAAA,EAAAA,KAACmL,EAAsB,CAACC,cAAeA,KAErB,IAAzBO,GACC3L,EAAAA,EAAAA,KAAAC,EAAAA,SAAA,CAAAC,UACEF,EAAAA,EAAAA,KAAA,OAAKG,UAAU,gDAA+CD,UAC5DkC,EAAAA,EAAAA,MAAA,QAAAlC,SAAA,EACEF,EAAAA,EAAAA,KAAA,UAAAE,SAAQ,0BAA8B,uDACtCF,EAAAA,EAAAA,KAAA,KAAGgD,KAAK,iBAAiB7C,UAAU,iBAAgBD,UAACF,EAAAA,EAAAA,KAAA,UAAAE,SAAQ,kBAAwB,iFAKhE,IAA3BsL,GACCxL,EAAAA,EAAAA,KAAAC,EAAAA,SAAA,CAAAC,UACEF,EAAAA,EAAAA,KAAA,OAAKG,UAAU,gDAA+CD,UAC5DkC,EAAAA,EAAAA,MAAA,QAAAlC,SAAA,EACEF,EAAAA,EAAAA,KAAA,UAAAE,SAAQ,yBAA6B,2EACrCF,EAAAA,EAAAA,KAAA,KAAGgD,KAAK,iBAAiB7C,UAAU,iBAAgBD,UAACF,EAAAA,EAAAA,KAAA,UAAAE,SAAQ,kBAAwB,iFAGpFF,EAAAA,EAAAA,KAACmL,EAAsB,CAACC,cAAeA,KAsBjDpL,EAAAA,EAAAA,KAAAC,EAAAA,SAAA,KACND,EAAAA,EAAAA,KAAA,QAAMG,UAAU,+EAA8ED,SAAC,6CAC/FF,EAAAA,EAAAA,KAACsP,EAAU,CAAClO,KAAMA,KAClBpB,EAAAA,EAAAA,KAACuP,EAAM,CAACnO,KAAMA,IAEb9B,CAAAA,SAAAA,aAAAA,WAAAA,eAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,cAAAA,EAAAA,kBAAAA,4BAAAA,mBAAAA,yBAAAA,oCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,kCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,2BAAAA,iBAAAA,4BAAAA,cAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,oBAAAA,uBAAAA,yBAAAA,8BAAAA,yBAAAA,gFAAAA,uBAAAA,6CAAAA,sBAAAA,KAAAA,uBAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,wBAAAA,KAAAA,mBAAAA,aAAAA,qCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,mCAAAA,KAAAA,8BAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,4BAAAA,4BAAAA,gCAAAA,oBAAAA,gCAAAA,oBAAAA,gCAAAA,oBAAAA,gCAAAA,oBAAAA,kCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,gCAAAA,KAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,iCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,6BAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,yBAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,iCAAAA,8GAAAA,iCAAAA,8GAAAA,oBAAAA,8jBAAYoP,6BAA+BtN,EAAKuN,QAAUrP,CAAAA,SAAAA,aAAAA,WAAAA,eAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,cAAAA,EAAAA,kBAAAA,4BAAAA,mBAAAA,yBAAAA,oCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,kCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,2BAAAA,iBAAAA,4BAAAA,cAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,oBAAAA,uBAAAA,yBAAAA,8BAAAA,yBAAAA,gFAAAA,uBAAAA,6CAAAA,sBAAAA,KAAAA,uBAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,wBAAAA,KAAAA,mBAAAA,aAAAA,qCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,mCAAAA,KAAAA,8BAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,4BAAAA,4BAAAA,gCAAAA,oBAAAA,gCAAAA,oBAAAA,gCAAAA,oBAAAA,gCAAAA,oBAAAA,kCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,gCAAAA,KAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,iCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,6BAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,yBAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,iCAAAA,8GAAAA,iCAAAA,8GAAAA,oBAAAA,8jBAAYoP,4BAA4BtB,SAAShM,EAAKuN,SAC/G3O,EAAAA,EAAAA,KAAA,OAAKG,UAAU,oDAAmDD,UAChEF,EAAAA,EAAAA,KAAA,OAAKG,UAAU,iCAAgCD,UAC7CF,EAAAA,EAAAA,KAAA,OAAKG,UAAU,eAAcD,UAC3BkC,EAAAA,EAAAA,MAAA,OAAKjC,UAAU,sFAAqFD,SAAA,EAClGF,EAAAA,EAAAA,KAAA,OAAKG,UAAU,iBAAgBD,UAC7BF,EAAAA,EAAAA,KAAA,MAAIG,UAAU,kCAAiCD,SAAC,uBAIlDkC,EAAAA,EAAAA,MAAA,OAAKjC,UAAU,2BAA0BD,SAAA,EACvCF,EAAAA,EAAAA,KAAA,OAAKG,UAAU,+BAA8BD,UAC3CF,EAAAA,EAAAA,KAAA,QAAMG,UAAU,gCAA+BD,SAAC,2FAGlDF,EAAAA,EAAAA,KAAA,OAAKG,UAAU,wDAAuDD,UACpEkC,EAAAA,EAAAA,MAAA,KACEjC,UAAU,6HACV0B,MAAO,CAAE2N,eAAgB,QACzBxM,KA7TL,2CA8TKmC,OAAO,SACPiK,IAAI,aACJxJ,KAAK,SACLlD,WAAY,CAAEC,gBAAiB,WAC/BC,SAAU,CAAEC,MAAO,IAAM3C,SAAA,CAC1B,mBACgBF,EAAAA,EAAAA,KAAC6F,EAAAA,IAAY,CAAC,aAAW,cAAcD,KAAK,MAAMzF,UAAU,0EAYjGH,EAAAA,EAAAA,KAACM,EAAAA,EAAkB,CAACE,kBAAmBA,EAAmBC,qBAAsBA,EAAsBC,mBAAoBA,EAAoBC,8BAA+BA,EAA+BC,wBAAyBA,KACrOZ,EAAAA,EAAAA,KAACiD,EAAAA,EAAsB,CAACE,gBAAiBA,EAAiBC,2BAA4BA,EAA4BxC,wBAAyBA,EAAyByC,qBAAsBA,KAC1LrD,EAAAA,EAAAA,KAACyP,EAAAA,QAAM,CAACrO,KAAMA,OAGpB,C,gDCnXA,SAAiB,C", "sources": ["footer/index.jsx", "modal/enterprise.jsx", "aiapps/Card.jsx", "aiapps/aiarts.jsx", "aiapps/aichatbots.jsx", "my-account/index.jsx", "webpack://v1/./node_modules/react-responsive-carousel/lib/styles/carousel.css?c782"], "sourcesContent": ["import React, { useEffect } from 'react';\r\n\r\nfunction Powered() {\r\n  // const [isHidden, setIsHidden] = useState(false);\r\n\r\n  const base_url = process.env.REACT_APP_BASE_URL;\r\n  useEffect(() => {\r\n    const script = document.createElement('script');\r\n    script.src = base_url + 'snippets/com.global.vuzo/js/com.global.vuzo.js';\r\n    script.async = true;\r\n    script.onload = () => {\r\n      // window.displayGooglePlay();\r\n      // window.displayQR();\r\n    };\r\n    document.body.appendChild(script);\r\n  }, [base_url]);\r\n  return (\r\n    <>\r\n      <footer className={`relative hidden md:block md:absolute bottom-[10px] right-[5px] w-full mt-3 py-12 md:py-2 z-[8888] md:hidden`}>\r\n      {/* <footer className=\"relative hidden md:block md:absolute bottom-[10px] right-[5px] w-full mt-3 py-12 md:py-2 z-[8888]\"> */}\r\n\r\n      </footer>\r\n    </>\r\n  );\r\n}\r\n\r\nexport default Powered;\r\n", "import { useEffect, useState } from 'react';\r\nimport { motion } from \"framer-motion\";\r\nimport axios from 'axios';\r\nimport aiproLogo from '../assets/images/AIPRO.svg';\r\nimport { FaInfoCircle } from 'react-icons/fa';\r\nimport { Auth } from '../core/utils/auth';\r\nimport toastr from 'toastr';\r\nimport 'toastr/build/toastr.min.css';\r\n\r\nvar price_per_member = '0';\r\nvar user_id = '';\r\n\r\nexport function AddMoreMemberModal ({showAddMoreMember, setshowAddMoreMember, setMoreToAddMember, setMoreToAddMemberTotalAmount, setShowCompletePurchase}) {\r\n  const [membersToAdd, setMembersToAdd] = useState(1);\r\n  const [totalAmount, setTotalAmount] = useState(0);\r\n  const [paymentInterval, setpaymentInterval] = useState(\"\");\r\n\r\n  const auth = Auth();\r\n\r\n  useEffect(() => {\r\n    if (auth !== undefined && auth.plan==='enterprise'){\r\n      price_per_member = auth.price_per_member;\r\n\r\n      if (auth.interval.toLowerCase()===\"monthly\"){\r\n        setpaymentInterval(\"MONTH\");        \r\n      }else{\r\n        setpaymentInterval(\"YEAR\");       \r\n      }\r\n\r\n    }\r\n  }, [auth]);\r\n\r\n\r\n  const modalAddMoreMembersOpen = () => {\r\n    let modal = document.getElementById(\"modalAddMoreMembers\");\t\t\r\n    if (modal!==null){\r\n      modal.style.display = \"block\";\r\n    }\r\n  }\r\n\r\n  const modalAddMoreMembersClose = () => {\r\n    let modal = document.getElementById(\"modalAddMoreMembers\");\t\t\r\n    if (modal!==null){\r\n      modal.style.display = \"none\";\t\t \r\n      setshowAddMoreMember(false);\r\n    }\r\n  }\r\n\r\n  const handlePlus = () => {\r\n    setMembersToAdd(membersToAdd+1);\r\n  }\r\n\r\n  const handleMinus = () => {\r\n    if (membersToAdd>1){\r\n      setMembersToAdd(membersToAdd-1);\r\n    }\r\n  }\r\n  \r\n  useEffect(() => {\r\n    toastr.options = {\r\n      positionClass: 'toast-top-center'\r\n    };\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    let amount = membersToAdd * price_per_member;\r\n    setTotalAmount(amount);\r\n  }, [membersToAdd]);\r\n\r\n  useEffect(() => {\r\n    setMembersToAdd(1);\r\n\r\n    let amount = membersToAdd * price_per_member;\r\n    setTotalAmount(amount);\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps    \r\n  }, [showAddMoreMember]);\r\n\r\n  const handleUupgradeNow = () => {\r\n    window.location.href = '/upgrade-ent/'+membersToAdd;\r\n    return;\r\n\r\n    // setMoreToAddMember(membersToAdd);\r\n    // setMoreToAddMemberTotalAmount(totalAmount);\r\n\r\n    // setshowAddMoreMember(false);\r\n    // setShowCompletePurchase(true)\r\n  }\r\n\r\n  if (showAddMoreMember!==undefined && showAddMoreMember === true){\r\n    modalAddMoreMembersOpen();\r\n  }\r\n  if (showAddMoreMember!==undefined && showAddMoreMember === false){\r\n    modalAddMoreMembersClose();\r\n  }\r\n\r\n  return (\r\n    <>\r\n\t\t<div id=\"modalAddMoreMembers\" className=\"modal z-[9999]\">\r\n\t\t\t<div class=\"modal-content w-full md:w-[60%] max-w-[90%] p-3 md:p-4\">\r\n        <div>\r\n          <div className=\"mb-4 flex border-b pb-2\">\r\n            <div className=\"text-blue-500 w-full md:w-2/3 mr-2 md:mr-5 font-bold\">\r\n            Add More Members\r\n            </div>\r\n            <div className=\"float-right mt-[-5px] w-full md:w-1/3\">\r\n              <span class=\"close\" onClick={()=> modalAddMoreMembersClose()}>&times;</span>\r\n            </div>\r\n          </div>\r\n          <div className=\"p-2 md:p-4 border rounded text-sm text-center\">\r\n            <div>Your enterprise account has hit its maximum user capacity.</div>\r\n            <div>Add more members to your Enterprise Account.</div>\r\n            <div className=\"py-4 text-center\">\r\n              <span className=\"font-bold text-[12px] sm:text-sm inline px-1 sm:px-2\">Add</span>\r\n              <div className=\"border rounded px-2 py-4 inline\">\r\n                <input className=\"bg-blue-500 rounded px-3 py-2 m-2 text-white cursor-pointer\" type=\"button\" value=\"-\" onClick={()=>handleMinus()}/>\r\n                <span className=\"text-blue-500 p-2 mx-auto font-bold\">{membersToAdd}</span>\r\n                <input className=\"bg-blue-500 rounded px-3 py-2 m-2 text-white cursor-pointer\" type=\"button\" value=\"+\" onClick={()=>handlePlus()} />\r\n              </div>\r\n              <span className=\"font-bold text-[12px] sm:text-sm inline px-1 sm:px-2\">Member/s</span>\r\n            </div>\r\n            <div className=\"font-bold text-sm\">\r\n              Total Amount: ${totalAmount}\r\n            </div>\r\n            <div className=\"font-bold text-sm\">\r\n              PER {paymentInterval}\r\n            </div>\r\n            <div>\r\n              <motion.button\r\n              className=\"bg-sky-600 w-full md:w-70 text-white font-bold my-4 py-2 px-6 rounded proceed-pmt\"\r\n              whileHover={{ backgroundColor: \"#49b1df\" }}\r\n              whileTap={{ scale: 0.9 }}\r\n              onClick={()=> handleUupgradeNow()}\r\n              >\r\n              UPGRADE NOW\r\n              </motion.button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n    </>\r\n  );\r\n}\r\n\r\nexport function MemberCompletePurchase ({moreToAddMember, moreToAddMemberTotalAmount, setShowCompletePurchase, showCompletePurchase}) {\r\n\r\n  const auth = Auth();\r\n\r\n  useEffect(() => {\r\n    if (auth !== undefined && auth.plan==='enterprise'){\r\n      user_id = auth.user_id;\r\n    }\r\n  }, [auth]);\r\n\r\n  const modalOpen = () => {\r\n    let modal = document.getElementById(\"modalComplete\");\t\t\r\n    if (modal!==null){\r\n      modal.style.display = \"block\";\t\t\r\n    }\r\n  }\r\n\r\n  const modalClose = () => {\r\n    let modal = document.getElementById(\"modalComplete\");\t\t\r\n    if (modal!==null){\r\n      modal.style.display = \"none\";\t\t \r\n      setShowCompletePurchase(false);\r\n    }\r\n  }\r\n\r\n  const submitPaymentInformation = () => {\r\n    window.location.href = '/payment-reference';\r\n    return;\r\n  }\r\n\r\n  const sendViaEmail = () => {\r\n    var members = moreToAddMember;\r\n    var total_amount = moreToAddMemberTotalAmount;\r\n    var url = `${process.env.REACT_APP_API_URL}/t/send-enterprise-payment-info`;\r\n\r\n    modalClose();    \r\n    document.querySelector(\".loader-container\").classList.add('active');\r\n\r\n    axios.post(url, {\r\n      members,\r\n      total_amount\r\n    }, { headers: { 'content-type': 'application/x-www-form-urlencoded' } }).then(function(res) {\r\n      let output = res.data;\r\n\r\n      document.querySelector(\".loader-container\").classList.remove('active');\r\n\r\n      if(output.success) {\r\n        toastr.success(\"Email sent to \"+output.data);\r\n      }else{\r\n        toastr.error(\"Email Failed.\");\r\n      }\r\n    }).catch(function (error) {\r\n      document.querySelector(\".loader-container\").classList.remove('active');\r\n      if (error.response && error.response.status===429) {\r\n        document.querySelector(\".loader-container\").classList.remove('active');\r\n        toastr.error(\"Sorry, too many requests. Please try again in a bit!\");\r\n      }\r\n    });\r\n  }\r\n\r\n  if (showCompletePurchase!==undefined && showCompletePurchase === true){\r\n    modalOpen();\r\n  }\r\n  if (showCompletePurchase!==undefined && showCompletePurchase === false){\r\n    modalClose();\r\n  }\r\n\r\n  return (\r\n    <>\r\n    <div id=\"modalComplete\" className=\"modal z-[9999]\">\r\n      <div class=\"w-full md:w-[600px] border-[#888] md:mt-[15px] mx-[auto] bg-[#fefefe] p-6\">\r\n        <span class=\"close\" onClick={()=> modalClose()}>&times;</span>\r\n        <div className=\"border-b pb-[10px] border-[#d5d5d5]\"><img src={aiproLogo} alt=\"AI-Pro Logo\" className=\"aiprologo mx-auto\"/></div>\r\n        <h1 className=\"font-bold text-center p-2 text-gray-700 text-[20px] md:text-[24px]\">Payment Details<br/>for Enterprise Order</h1>\r\n        <div className=\"text-center text-[12px] md:text-[14px]\">Adding more than 10 Enterprise users requires prior payment.</div>\r\n        <div className=\"text-center text-[12px] md:text-[14px]\">Please use the provided payment details below to settle your Enterprise account.</div>\r\n        <div className=\"py-2\">\r\n          <div className=\"font-bold text-[11px]\">No. of Members: {moreToAddMember}</div>\r\n          <div className=\"font-bold text-[11px]\">Enterprise - Total: ${moreToAddMemberTotalAmount}</div>\r\n        </div>\r\n        <div className=\"border rounded p-2 text-[12px] md:text-[14px] leading-7 my-2\">\r\n          <div className=\"font-bold\">Bank Information</div>\r\n          <div className=\"float-right text-blue-400 font-bold cursor-pointer mt-[-28px] text-[12px]\" onClick={()=> sendViaEmail()}>Send via Email</div>\r\n          <div><span className=\"font-bold\">Beneficiary:</span> TELECOM BUSINESS SOLUTIONS INC.</div>\r\n          <div><span className=\"font-bold\">SWIFT:</span> BOFAUS3N</div>\r\n          <div><span className=\"font-bold\">Bank Name:</span> Bank of America</div>\r\n          <div><span className=\"font-bold\">Routing (Wire):</span> *********</div>\r\n          <div><span className=\"font-bold\">Routing Number (Paper & Electronic):</span> *********</div>\r\n          <div><span className=\"font-bold\">Account Number:</span> 3810-6766-2647</div>\r\n          <div><span className=\"font-bold\">Customer Number:</span> {user_id}</div>\r\n          <div className=\"bg-[#dddddd] px-4 py-2 rounded text-center mt-4\"><FaInfoCircle className=\"inline text-lg mr-2\"/>Customer Number must be included in the bank transfer description field for your funds to transfer successfully.</div>\r\n        </div>\r\n\r\n        <div className=\"text-center text-[12px] md:text-[14px] mt-4\">\r\n          Once the payment is received, our dedicated account manager will contact you to assist in the seamless setup of your Enterprise account.\r\n        </div>\r\n        <div className=\"text-center text-[12px] md:text-[14px]\">\r\n          Please allow <b>2-3 banking days</b> for the payment to reflect in the account.\r\n        </div>\r\n        <div className=\"text-center\">\r\n          <motion.button\r\n            className=\"bg-blue-500 text-white font-bold py-3 px-4 rounded my-4 proceed-pmt\"\r\n            whileHover={{ backgroundColor: \"#5997fd\" }}\r\n            whileTap={{ scale: 0.9 }}\r\n            onClick={submitPaymentInformation}\r\n          >\r\n            Send Payment Confirmation\r\n          </motion.button>\r\n        </div>\r\n      </div>\r\n    </div>    \r\n    </>\r\n  )\r\n}\r\n", "import React from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport { FaArrowRight } from 'react-icons/fa';\r\n\r\nimport './style.css';\r\n\r\nconst Card = ({\r\n  name,\r\n  tag,\r\n  icon,\r\n  description,\r\n  href,\r\n  target,\r\n  onClick,\r\n  poweredByOpenAI,\r\n  isComingSoon,\r\n  buttonText,\r\n}) => {\r\n  const getTitle = () => {\r\n    const title = (\r\n      <h4\r\n        className={`text-gray-800 font-bold text-left ${\r\n          poweredByOpenAI ? '' : 'leading-5'\r\n        }`}\r\n      >\r\n        {name}\r\n      </h4>\r\n    );\r\n    if (!poweredByOpenAI) return title;\r\n    return (\r\n      <div className=\"leading-[13px]\">\r\n        {title}\r\n        {/* <small className=\"block text-left\">\r\n          powered by <b>OpenAI</b>\r\n        </small> */}\r\n      </div>\r\n    );\r\n  };\r\n\r\n  const defaultButtonText = `Go to ${name}`;\r\n\r\n  return (\r\n    <div className=\"appbox relative overflow-hidden border border-gray-200 rounded-md p-4 flex flex-col\">\r\n      {Boolean(tag) && (\r\n        <div className=\"ribbon-container\">\r\n          {tag === 'beta' && <div className=\"ribbon beta\">Beta</div>}\r\n          {tag === 'popular' && <div className=\"ribbon popular\">Popular</div>}\r\n          {tag === 'new' && <div className=\"ribbon\">NEW</div>}\r\n        </div>\r\n      )}\r\n      <div className=\"flex mytitle items-center\">\r\n        <img\r\n          src={icon}\r\n          alt={`${name} logo`}\r\n          aria-label={`${name} logo`}\r\n          className=\"mr-1 w-[50px]\"\r\n        />\r\n        {getTitle()}\r\n      </div>\r\n      <p className=\"mydesc text-[13px] min-[375px]:text-sm min-[768px]:text-base lg:text-sm text-black text-left grow min-h-[137px] min-[375px]:min-h-[120px] lg:min-h-[150px]\">\r\n        {description}\r\n      </p>\r\n      {isComingSoon ? (\r\n        <motion.a\r\n          className=\"bg-slate-400 cursor-default text-white text-left text-[12px] font-bold py-2 pl-3 pr-6 mt-5 w-full rounded-md no-underline\"\r\n          name=\"Coming Soon\"\r\n          aria-label=\"Coming Soon\"\r\n          role=\"button\"\r\n        >\r\n          Coming Soon\r\n        </motion.a>\r\n      ) : (\r\n        <motion.a\r\n          className=\"bg-blue-500 text-white text-left text-[12px] font-bold py-2 pl-3 pr-6 mt-5 w-full rounded-md cursor-pointer no-underline\"\r\n          whileHover={{ backgroundColor: '#5997fd' }}\r\n          whileTap={{ scale: 0.9 }}\r\n          href={href}\r\n          target={target}\r\n          onClick={onClick}\r\n          name={defaultButtonText}\r\n          aria-label={defaultButtonText}\r\n          role=\"button\"\r\n        >\r\n          {buttonText || defaultButtonText}{' '}\r\n          <FaArrowRight\r\n            aria-label=\"right arrow\"\r\n            role=\"img\"\r\n            className=\"inline text-xs mt-0.5 float-right mr-[-16px]\"\r\n          />\r\n        </motion.a>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Card;\r\n", "import React, { useState, useEffect } from 'react';\r\nimport { Carousel } from 'react-responsive-carousel';\r\n\r\nimport { returnAppHref, redirectApp } from '../core/utils/app';\r\nimport Card from './Card';\r\n\r\nimport dream_photo_icon from '../assets/images/dream-photo-icon.png';\r\nimport interior_icon from '../assets/images/interior-ai-icon.png';\r\nimport restore_photo_icon from '../assets/images/restore-photo-icon.png';\r\nimport remove_background_icon from '../assets/images/remove-background-icon.png';\r\nimport avatar_maker_icon from '../assets/images/avatar-maker-icon.png';\r\nimport story_book_icon from '../assets/images/story-book-icon.png';\r\nimport ask_vid_icon from '../assets/images/ask-vid-icon.png';\r\nimport flux_icon from '../assets/images/flux_icon.png';\r\n\r\nimport 'react-responsive-carousel/lib/styles/carousel.css';\r\nimport './style.css';\r\n\r\nconst tools = process.env.REACT_APP_TOOLS_URL\r\n  ? JSON.parse(process.env.REACT_APP_TOOLS_URL)\r\n  : {};\r\n\r\nfunction AiArt(props) {\r\n  const [isMobile, setIsMobile] = useState(false);\r\n  const auth = props && props.auth ? props.auth : null;\r\n  const upid = auth && auth.user_pid ? '?upid=' + auth.user_pid : '';\r\n\r\n  const text2imagelink = tools.txt2img\r\n    ? tools.txt2img + upid\r\n    : 'https://app.ai-pro.org/dream-photo' + upid;\r\n  const interiorgptlink = tools.interiorgpt\r\n    ? tools.interiorgpt + upid\r\n    : 'https://interiorgpt.ai-pro.org' + upid;\r\n  const restorephotolink = tools.restorephoto\r\n    ? tools.restorephoto + upid\r\n    : 'https://restorephotos.ai-pro.org' + upid;\r\n  const removebg = tools.removebg\r\n    ? tools.removebg + upid\r\n    : 'https://clearbg.ai-pro.org' + upid;\r\n  const avatar_maker_link = tools.avatar_make\r\n    ? tools.avatar_make + upid\r\n    : 'https://avatarmaker.ai-pro.org' + upid;\r\n  const story_book_link = tools.storybook\r\n    ? tools.storybook + upid\r\n    : 'https://storybook.ai-pro.org' + upid;\r\n  const flux_link = tools.storybook\r\n    ? tools.storybook + upid\r\n    : 'https://flux.ai-pro.org' + upid;\r\n\r\n  useEffect(() => {\r\n    const handleResize = () => {\r\n      setIsMobile(window.innerWidth <= 850);\r\n    };\r\n\r\n    handleResize(); // Check on initial render\r\n\r\n    window.addEventListener('resize', handleResize);\r\n    return () => {\r\n      window.removeEventListener('resize', handleResize);\r\n    };\r\n  }, []);\r\n\r\n  const fnRedirectApp = (url) => {\r\n    redirectApp(auth, url);\r\n  };\r\n\r\n  const fnReturnAppHref = (url) => {\r\n    return returnAppHref(auth, url);\r\n  };\r\n\r\n  const Mobile = ({ children }) => {\r\n    return (\r\n      <Carousel\r\n        className=\"my-4\"\r\n        infiniteLoop={true}\r\n        showArrows={true}\r\n        emulateTouch={true}\r\n        autoPlay={true}\r\n        centerSlidePercentage={true}\r\n      >\r\n        {children}\r\n      </Carousel>\r\n    );\r\n  };\r\n\r\n  const Web = ({ children }) => {\r\n    return (\r\n      <div className=\"apps md:grid md:grid-cols-4 md:gap-2 md:mt-4\">\r\n        {children}\r\n      </div>\r\n    );\r\n  };\r\n\r\n  const Container = isMobile ? Mobile : Web;\r\n\r\n  return (\r\n    <div className=\"aiart bg-gray-100 flex items-center mt-4 md:mt-0\">\r\n      <div className=\"container mx-auto sm:px-0\">\r\n        <div className=\"max-w-6xl mx-auto pb-5\">\r\n          <div className=\"bg-white drop-shadow-sm py-6 px-4 rounded-tr-md rounded-br-md rounded-bl-md flex flex-col\">\r\n            <div className=\"apptitle flex flex-wrap\">\r\n              <h1 className=\"text-xl font-bold text-gray-800 inline w-56\">\r\n                AI Art-Generator\r\n              </h1>\r\n              <span className=\"text-[12px] text-left md:text-right ml-auto md:w-96\">\r\n                Access the latest AI Art generation software. Describe what you\r\n                want to create, and within seconds you have your image.\r\n              </span>\r\n            </div>\r\n\r\n            <Container>\r\n              <Card\r\n                name=\"DreamPhoto\"\r\n                icon={dream_photo_icon}\r\n                description=\"Create amazing digital artwork from text and phrases using the latest AI Image Generation Technology\"\r\n                href={fnReturnAppHref(text2imagelink)}\r\n                target={\r\n                  fnReturnAppHref(text2imagelink) === text2imagelink\r\n                    ? '_blank'\r\n                    : ''\r\n                }\r\n                onClick={() => fnRedirectApp(text2imagelink)}\r\n              />\r\n              <Card\r\n                name=\"Interior AI\"\r\n                icon={interior_icon}\r\n                description=\"Generate dream rooms using AI. Upload a photo of your room and see how artificial intelligence remodels your space virtually.\"\r\n                href={fnReturnAppHref(interiorgptlink)}\r\n                target={\r\n                  fnReturnAppHref(interiorgptlink) === interiorgptlink\r\n                    ? '_blank'\r\n                    : ''\r\n                }\r\n                onClick={() => fnRedirectApp(interiorgptlink)}\r\n              />\r\n              <Card\r\n                name=\"Restore Photo\"\r\n                icon={restore_photo_icon}\r\n                description=\"Effortlessly restore and enhance your old photos with cutting-edge artificial intelligence.\"\r\n                href={fnReturnAppHref(restorephotolink)}\r\n                target={\r\n                  fnReturnAppHref(restorephotolink) === restorephotolink\r\n                    ? '_blank'\r\n                    : ''\r\n                }\r\n                onClick={() => fnRedirectApp(restorephotolink)}\r\n              />\r\n              <Card\r\n                name=\"Remove Background\"\r\n                buttonText=\"Go to Remove BG\"\r\n                icon={remove_background_icon}\r\n                description=\"Seamlessly remove backgrounds from your photos, making your subjects stand out.\"\r\n                href={fnReturnAppHref(removebg)}\r\n                target={fnReturnAppHref(removebg) === removebg ? '_blank' : ''}\r\n                onClick={() => fnRedirectApp(removebg)}\r\n              />\r\n              <Card\r\n                tag=\"new\"\r\n                name=\"Avatar Maker\"\r\n                icon={avatar_maker_icon}\r\n                description=\"Transform user-uploaded images into customized artworks,\r\n                    adhering to specific prompts and delivering unique visual\r\n                    interpretations.\"\r\n                href={fnReturnAppHref(avatar_maker_link)}\r\n                target={\r\n                  fnReturnAppHref(avatar_maker_link) === avatar_maker_link\r\n                    ? '_blank'\r\n                    : ''\r\n                }\r\n                onClick={() => fnRedirectApp(avatar_maker_link)}\r\n              />\r\n              <Card\r\n                tag=\"new\"\r\n                name=\"StoryBook\"\r\n                icon={story_book_icon}\r\n                description=\"Seamlessly create captivating visuals from simple prompts, opening up a realm of boundless creative potential, perfect for crafting stunning illustrations for your books.\"\r\n                href={fnReturnAppHref(story_book_link)}\r\n                target={\r\n                  fnReturnAppHref(story_book_link) === story_book_link\r\n                    ? '_blank'\r\n                    : ''\r\n                }\r\n                onClick={() => fnRedirectApp(story_book_link)}\r\n              />\r\n              <Card\r\n                tag='beta'\r\n                name=\"Flux ImageGen\"\r\n                icon={flux_icon}\r\n                description=\"Experience the future of AI-driven creativity with Flux ImageGen. Generate stunning, high-quality images effortlessly using the cutting-edge Flux models for limitless artistic possibilities.\"\r\n                href={fnReturnAppHref(flux_link)}\r\n                target={\r\n                  fnReturnAppHref(flux_link) === flux_link\r\n                    ? '_blank'\r\n                    : ''\r\n                }\r\n                onClick={() => fnRedirectApp(flux_link)}\r\n              />\r\n              <Card\r\n                isComingSoon\r\n                name=\"Ask My Video\"\r\n                icon={ask_vid_icon}\r\n                description=\"Engage in video interaction and deliver precise answers derived from the content itself.\"\r\n              />\r\n            </Container>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default AiArt;\r\n", "import React, { useState, useEffect } from 'react';\r\nimport { Carousel } from 'react-responsive-carousel';\r\n\r\nimport { returnAppHref, redirectApp } from '../core/utils/app';\r\nimport Card from './Card';\r\n\r\nimport chatbot_pro_icon from '../assets/images/chatbot-pro-icon.png';\r\nimport chatpdf_icon from '../assets/images/chatpdf-icon.png';\r\nimport chatpdfv2_icon from '../assets/images/chatpdfv2-icon.png';\r\nimport grammar_icon from '../assets/images/grammar-ai-icon.png';\r\nimport teacher_ai_icon from '../assets/images/teacher-ai-icon.png';\r\nimport trips_ai_icon from '../assets/images/trips-ai-icon.png';\r\nimport recipe_maker_icon from '../assets/images/recipe-maker-icon.png';\r\nimport translate_now_icon from '../assets/images/translate-now-icon.png';\r\nimport searchai_icon from '../assets/images/searchai-icon.png';\r\nimport multillm_icon from '../assets/images/multillm-icon.png';\r\nimport sitebot_icon from '../assets/images/sitebot-icon.png';\r\nimport codingai_icon from '../assets/images/codingai_icon.png';\r\nimport homework_help_icon from '../assets/images/homework-help-icon.png';\r\n\r\nimport './style.css';\r\nimport 'react-responsive-carousel/lib/styles/carousel.css';\r\n\r\nconst tools = process.env.REACT_APP_TOOLS_URL\r\n  ? JSON.parse(process.env.REACT_APP_TOOLS_URL)\r\n  : {};\r\n\r\nfunction AiChatbots(props) {\r\n  const [isMobile, setIsMobile] = useState(false);\r\n  const auth = props && props.auth ? props.auth : null;\r\n  const upid = auth && auth.user_pid ? '?upid=' + auth.user_pid : '';\r\n  const chatbotprolink = tools.chatpro\r\n    ? tools.chatpro + upid\r\n    : 'https://chatpro.ai-pro.org/chat' + upid;\r\n  const chatpdflink = tools.chatpdf\r\n    ? tools.chatpdf + upid\r\n    : 'https://chatpdf.ai-pro.org' + upid;\r\n  const chatpdfv2link = tools.chatpdfv2 \r\n    ? tools.chatpdfv2 + upid \r\n    : 'https://chatpdfv2.ai-pro.org'+upid;\r\n  const convertenglishlink = tools.convert2english\r\n    ? tools.convert2english + upid\r\n    : 'https://app.ai-pro.org/convert-to-proper-english' + upid;\r\n  const teacher_ai_link = tools.teacher_ai\r\n    ? tools.teacher_ai + upid\r\n    : 'https://teacher.ai-pro.org' + upid;\r\n  const trips_ai_link = tools.trips_ai\r\n    ? tools.trips_ai + upid\r\n    : 'https://chatlibrary.ai-pro.org/ai/travel' + upid;\r\n  const recipe_maker_link = tools.recipe_maker\r\n    ? tools.recipe_maker + upid\r\n    : 'https://chatlibrary.ai-pro.org/ai/recipe' + upid;\r\n  const translate_now_link = tools.translate_now\r\n    ? tools.translate_now + upid\r\n    : 'https://chatlibrary.ai-pro.org/ai/writing/translate' + upid;\r\n  const searchai_link = tools.searchai\r\n    ? tools.searchai + upid\r\n    : 'https://search.ai-pro.org' + upid;\r\n  const multillm_link = tools.multillm\r\n    ? tools.multillm + upid\r\n    : 'https://multillm.ai-pro.org' + upid;\r\n  const sitebot_link = tools.sitebot\r\n    ? tools.sitebot + upid\r\n    : 'https://sitebot.ai-pro.org' + upid;\r\n  const codingai_link = tools.codingai\r\n    ? tools.codingai + upid\r\n    : 'https://coding.ai-pro.org' + upid;\r\n  const homework_link = tools.homework\r\n    ? tools.homework + upid\r\n    : 'https://tutor.ai-pro.org' + upid;\r\n\r\n  useEffect(() => {\r\n    const handleResize = () => {\r\n      setIsMobile(window.innerWidth <= 850);\r\n    };\r\n\r\n    handleResize(); // Check on initial render\r\n\r\n    window.addEventListener('resize', handleResize);\r\n    return () => {\r\n      window.removeEventListener('resize', handleResize);\r\n    };\r\n  }, []);\r\n\r\n  const fnRedirectApp = (url) => {\r\n    redirectApp(auth, url);\r\n  };\r\n\r\n  const fnReturnAppHref = (url) => {\r\n    return returnAppHref(auth, url);\r\n  };\r\n\r\n  const Mobile = ({ children }) => {\r\n    return (\r\n      <Carousel\r\n        className=\"my-4\"\r\n        infiniteLoop={true}\r\n        showArrows={true}\r\n        emulateTouch={true}\r\n        autoPlay={true}\r\n        centerSlidePercentage={true}\r\n      >\r\n        {children}\r\n      </Carousel>\r\n    );\r\n  };\r\n\r\n  const Web = ({ children }) => {\r\n    return (\r\n      <div className=\"apps md:grid md:grid-cols-4 md:gap-2 md:mt-4\">\r\n        {children}\r\n      </div>\r\n    );\r\n  };\r\n\r\n  const Container = isMobile ? Mobile : Web;\r\n\r\n  return (\r\n    <div className=\"aichatbot bg-gray-100 flex items-center mt-4 md:mt-0\">\r\n      <div className=\"container mx-auto sm:px-0\">\r\n        <div className=\"max-w-6xl mx-auto pb-5\">\r\n          <div\r\n            id=\"carousel-aichat-container\"\r\n            className=\"bg-white drop-shadow-sm py-6 px-4 rounded-tr-md rounded-br-md rounded-bl-md flex flex-col\"\r\n          >\r\n            <div className=\"apptitle flex flex-wrap\">\r\n              <h1 className=\"text-xl font-bold text-gray-800 inline w-40\">\r\n                Chatbot Pro\r\n              </h1>\r\n              <span className=\"text-[12px] text-left md:text-right ml-auto md:w-96\">\r\n                Generates essays, reports, website content,\r\n                contracts, poems, or ask questions and get answers.\r\n              </span>\r\n            </div>\r\n\r\n            <Container>\r\n              <Card\r\n                poweredByOpenAI\r\n                name=\"Chatbot Pro\"\r\n                tag=\"popular\"\r\n                icon={chatbot_pro_icon}\r\n                description=\"Chat with the latest AI chatbots. Generate captivating content, technical information, and programming code.\"\r\n                href={fnReturnAppHref(chatbotprolink)}\r\n                target={\r\n                  fnReturnAppHref(chatbotprolink) === chatbotprolink\r\n                    ? '_blank'\r\n                    : ''\r\n                }\r\n                onClick={() => fnRedirectApp(chatbotprolink)}\r\n              />\r\n              <Card\r\n                name=\"ChatPDF\"\r\n                icon={chatpdf_icon}\r\n                description=\"Upload your PDF File and have a chat with it. Ask questions, get a summary, get feedback, and many more. Start meaningful conversations with your PDF.\"\r\n                href={fnReturnAppHref(chatpdflink)}\r\n                target={\r\n                  fnReturnAppHref(chatpdflink) === chatpdflink ? '_blank' : ''\r\n                }\r\n                onClick={() => {\r\n                  fnRedirectApp(chatpdflink);\r\n                }}\r\n              />\r\n              <Card\r\n                tag=\"new\"\r\n                name=\"ChatPDF v2\"\r\n                icon={chatpdfv2_icon}\r\n                description=\"The improved ChatPDF is here! Upload any PDF to get answers, summarize content, and extract key insights. Now faster, more accurate, and equipped with a PDF viewer for cross-checking.\"\r\n                href={fnReturnAppHref(chatpdfv2link)}\r\n                target={\r\n                  fnReturnAppHref(chatpdfv2link) === chatpdfv2link ? '_blank' : ''\r\n                }\r\n                onClick={() => {\r\n                  fnRedirectApp(chatpdfv2link);\r\n                }}\r\n              />\r\n              <Card\r\n                name=\"Grammar AI\"\r\n                icon={grammar_icon}\r\n                description=\"Convert texts to proper English without slang or\r\n                      grammatical errors. Translate languages to proper English\r\n                      language in seconds.\"\r\n                href={fnReturnAppHref(convertenglishlink)}\r\n                target={\r\n                  fnReturnAppHref(convertenglishlink) === convertenglishlink\r\n                    ? '_blank'\r\n                    : ''\r\n                }\r\n                onClick={() => {\r\n                  fnRedirectApp(convertenglishlink);\r\n                }}\r\n              />\r\n              <Card\r\n                name=\"TeacherAI\"\r\n                icon={teacher_ai_icon}\r\n                description=\"Your all-in-one learning partner, offering a seamless\r\n                      fusion of written inquiries and interactive text-to-speech\r\n                      outputs, for an enhanced and effortless learning journey.\"\r\n                href={fnReturnAppHref(teacher_ai_link)}\r\n                target={\r\n                  fnReturnAppHref(teacher_ai_link) === teacher_ai_link\r\n                    ? '_blank'\r\n                    : ''\r\n                }\r\n                onClick={() => {\r\n                  fnRedirectApp(teacher_ai_link);\r\n                }}\r\n              />\r\n              <Card\r\n                name=\"Trips AI\"\r\n                icon={trips_ai_icon}\r\n                description=\"Let AI create customized itineraries for you. Plan your\r\n                      perfect getaway with AI-powered precision!\"\r\n                href={fnReturnAppHref(trips_ai_link)}\r\n                target={\r\n                  fnReturnAppHref(trips_ai_link) === trips_ai_link\r\n                    ? '_blank'\r\n                    : ''\r\n                }\r\n                onClick={() => {\r\n                  fnRedirectApp(trips_ai_link);\r\n                }}\r\n              />\r\n              <Card\r\n                name=\"Recipe Maker\"\r\n                icon={recipe_maker_icon}\r\n                description=\"Turn your ingredients into culinary masterpieces. Just\r\n                      enter your ingredients, and we'll do the rest! Start\r\n                      cooking today.\"\r\n                href={fnReturnAppHref(recipe_maker_link)}\r\n                target={\r\n                  fnReturnAppHref(recipe_maker_link) === recipe_maker_link\r\n                    ? '_blank'\r\n                    : ''\r\n                }\r\n                onClick={() => {\r\n                  fnRedirectApp(recipe_maker_link);\r\n                }}\r\n              />\r\n              <Card\r\n                name=\"TranslateNow\"\r\n                icon={translate_now_icon}\r\n                description=\"Experience lightning-fast text translation in over 100\r\n                      languages, right at your fingertips!\"\r\n                href={fnReturnAppHref(translate_now_link)}\r\n                target={\r\n                  fnReturnAppHref(translate_now_link) === translate_now_link\r\n                    ? '_blank'\r\n                    : ''\r\n                }\r\n                onClick={() => {\r\n                  fnRedirectApp(translate_now_link);\r\n                }}\r\n              />\r\n              <Card\r\n                tag=\"new\"\r\n                name=\"SearchAI\"\r\n                icon={searchai_icon}\r\n                description=\"Stay ahead of the curve with SearchAI's ability to fetch\r\n                      and deliver current data, revolutionizing the way you\r\n                      access information.\"\r\n                href={fnReturnAppHref(searchai_link)}\r\n                target={\r\n                  fnReturnAppHref(searchai_link) === searchai_link\r\n                    ? '_blank'\r\n                    : ''\r\n                }\r\n                onClick={() => {\r\n                  fnRedirectApp(searchai_link);\r\n                }}\r\n              />\r\n              <Card\r\n                tag=\"new\"\r\n                name=\"Multi-Chat\"\r\n                icon={multillm_icon}\r\n                description=\"Explore diverse answers effortlessly with Multi-Chat—an\r\n                      app leveraging collective intelligence from various\r\n                      language models for enriched and dynamic interactions.\"\r\n                href={fnReturnAppHref(multillm_link)}\r\n                target={\r\n                  fnReturnAppHref(multillm_link) === multillm_link\r\n                    ? '_blank'\r\n                    : ''\r\n                }\r\n                onClick={() => {\r\n                  fnRedirectApp(multillm_link);\r\n                }}\r\n              />\r\n              <Card\r\n                tag=\"beta\"\r\n                name=\"SiteBot\"\r\n                icon={sitebot_icon}\r\n                description=\"Upload large documents and get a comprehensive analysis in\r\n                      a matter of minutes. Design and train a chatbot for your\r\n                      business with ease—no code required.\"\r\n                href={fnReturnAppHref(sitebot_link)}\r\n                target={\r\n                  fnReturnAppHref(sitebot_link) === sitebot_link ? '_blank' : ''\r\n                }\r\n                onClick={() => {\r\n                  fnRedirectApp(sitebot_link);\r\n                }}\r\n              />\r\n              <Card\r\n                tag=\"beta\"\r\n                name=\"Coding AI\"\r\n                icon={codingai_icon}\r\n                description=\"Accelerate software development with Coding AI. Describe\r\n                      your app idea, and our AI platform instantly generates the\r\n                      code you need. Turn concepts into reality effortlessly.\"\r\n                href={fnReturnAppHref(codingai_link)}\r\n                target={\r\n                  fnReturnAppHref(codingai_link) === codingai_link\r\n                    ? '_blank'\r\n                    : ''\r\n                }\r\n                onClick={() => {\r\n                  fnRedirectApp(codingai_link);\r\n                }}\r\n              />\r\n              <Card\r\n                name=\"Homework Help\"\r\n                icon={homework_help_icon}\r\n                description=\"Struggling with homework? Homework Help is a LLaMa-powered\r\n                      app you can use to understand lessons, tackle assignments,\r\n                      and improve learning outcomes. Start using it today!\"\r\n                href={fnReturnAppHref(homework_link)}\r\n                target={\r\n                  fnReturnAppHref(homework_link) === homework_link\r\n                    ? '_blank'\r\n                    : ''\r\n                }\r\n                onClick={() => {\r\n                  fnRedirectApp(homework_link);\r\n                }}\r\n              />\r\n            </Container>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default AiChatbots;\r\n", "import React, { useEffect, useState } from 'react';\r\nimport './style.css';\r\nimport Header from '../header';\r\nimport Footer from '../footer';\r\nimport AiArts from '../aiapps/aiarts';\r\n// import AiEducations from '../aiapps/aieducations';\r\nimport AiChatbots from '../aiapps/aichatbots';\r\nimport { Auth } from '../core/utils/auth';\r\nimport { GetCookie, SetCookie, RemoveCookie } from '../core/utils/cookies';\r\nimport { FaArrowRight } from 'react-icons/fa';\r\nimport { Helmet } from 'react-helmet';\r\nimport axios from 'axios';\r\nimport { AddMoreMemberModal } from '../modal/enterprise';\r\nimport { MemberCompletePurchase } from '../modal/enterprise';\r\nconst api_url = process.env.REACT_APP_API_URL || \"https://start.ai-pro.org/api\";\r\n\r\nasync function checkBanner() {\r\n  const response = await axios.post(`${api_url}/get-banner`, {\r\n  }, { headers: { 'content-type': 'application/x-www-form-urlencoded' } })\r\n\r\n  const output = response.data;\r\n  if (output.success) {\r\n    const banner_data = output.data[0].banner_content;\r\n    return banner_data;\r\n  } else {\r\n    return '';\r\n  }\r\n}\r\n\r\nfunction MyAccount() {\r\n  const auth = Auth();\r\n  const feat_request = 'https://pages.ai-pro.org/request-feature';\r\n  const [downAppBanner, setDownAppBanner] = useState(false);\r\n  const [showTokenMaxoutWarning, setshowTokenMaxoutWarning] = useState(current => false);\r\n  const [showTokenMaxoutFinal, setshowTokenMaxoutFinal] = useState(current => false);\r\n  const [showPaused, setShowPaused] = useState(false);\r\n  const [loaded, setLoaded] = useState(false);\r\n\r\n\r\n  const initializeChat = () => {\r\n    if (window.initializeChatWidget) {\r\n      const chatConfig = {\r\n        token: process.env.REACT_APP_BOT_TOKEN // Assuming bot_token is available as an environment variable\r\n      };\r\n      window.initializeChatWidget(chatConfig);\r\n    } else {\r\n      console.error('initializeChatWidget is not defined.');\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    const script = document.createElement('script');\r\n    script.src = process.env.REACT_APP_CHATHEAD_URL;\r\n    script.addEventListener('load', ()=>setLoaded(true));\r\n    document.head.appendChild(script);\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    if(!loaded) return;\r\n    initializeChat();\r\n  }, [loaded]);\r\n\r\n  useEffect(() => {\r\n    async function fetchBanner() {\r\n      const fetchedBanner = await checkBanner();\r\n      if (fetchedBanner !== undefined) {\r\n        setDownAppBanner(fetchedBanner);\r\n      }\r\n    }\r\n    fetchBanner();\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    if (auth !== undefined && auth !== false) {\r\n      let max_token = auth.max_tokens;\r\n      let total_usage = auth.total_usage;\r\n      let eightyPercentOfToken = (max_token * 0.8);\r\n      //\r\n      setshowTokenMaxoutWarning(false);\r\n      setshowTokenMaxoutFinal(false);\r\n\r\n      if (max_token <= total_usage) {\r\n        setshowTokenMaxoutFinal(true);\r\n      } else if (eightyPercentOfToken <= total_usage) {\r\n        setshowTokenMaxoutWarning(true);\r\n      }\r\n      if(auth.status==='paused'){\r\n        setShowPaused(true);\r\n      }\r\n\r\n    }\r\n  }, [auth]);\r\n\r\n  useEffect(() => {\r\n    setTimeout(() => {\r\n      const isPro = GetCookie('user_plan');\r\n      const avatarMaker = GetCookie('avatarmaker');\r\n      const appUrl = GetCookie('appurl');\r\n\r\n      if (appUrl && (avatarMaker && appUrl.includes('avatar') && isPro === 'pro')) {\r\n        axios.post(`${api_url}/get-avatar`, {\r\n          folder: avatarMaker\r\n        }, { headers: { 'content-type': 'application/x-www-form-urlencoded' } }).then((res) => {\r\n          const data = res.data;\r\n          if(data.success) {\r\n            const url = data.link;\r\n            if (url !== '') {\r\n              fetch(url)\r\n              .then(response => response.blob())\r\n              .then(blob => {\r\n                const blobUrl = URL.createObjectURL(blob);\r\n                const link = document.createElement('a');\r\n                link.href = blobUrl;\r\n                link.setAttribute('download', 'highresimage.png');\r\n                document.body.appendChild(link);\r\n                link.click();\r\n                document.body.removeChild(link);\r\n              });\r\n            }\r\n          }\r\n          RemoveCookie('avatarmaker', { domain: '.ai-pro.org', path: '/' });\r\n        });\r\n      }\r\n    }, 500);\r\n  }, [])\r\n\r\n  const [showAddMoreMember, setshowAddMoreMember] = useState(false);\r\n  const [showCompletePurchase, setShowCompletePurchase] = useState(false);\r\n  const [moreToAddMember, setMoreToAddMember] = useState(\"1\");\r\n  const [moreToAddMemberTotalAmount, setMoreToAddMemberTotalAmount] = useState(\"0\");\r\n\r\n  var ShowUpgrade = false;\r\n  var ShowExpiredEnterprise = false;\r\n  var isEnterprise = false;\r\n  var entParentUserID = \"\";\r\n\r\n  if (auth === undefined) return;\r\n  if (auth === false) {\r\n    window.location.href = '/login';\r\n    return;\r\n  } else {\r\n    if (auth.expired === 'yes') {\r\n      ShowUpgrade = true;\r\n\r\n      if (auth.plan === 'enterprise') {\r\n        ShowExpiredEnterprise = true;\r\n        entParentUserID = auth.ent_parent_user_id\r\n      }\r\n    } else {\r\n      if (auth.plan === 'enterprise') {\r\n        isEnterprise = true;\r\n      }\r\n    }\r\n  }\r\n\r\n  const handleResumeClick = () => {\r\n    window.location.href = '/resume';\r\n  }\r\n\r\n  const handleUpgradeClick = () => {\r\n    const DEFAULT_PPG = process.env.REACT_APP_DEFAULT_PPG ? process.env.REACT_APP_DEFAULT_PPG : \"14\";\r\n    const currency = auth.currency.toLowerCase();\r\n\r\n    SetCookie('reactivateSubscription', \"reactivateSubscription\");\r\n    SetCookie('reactivateSubscription', \"reactivateSubscription\", { path: '/' });\r\n    SetCookie('reactivateSubscription', \"reactivateSubscription\", { path: 'ai-pro.org' });\r\n    SetCookie('reactivateSubscription', \"reactivateSubscription\", { path: '.ai-pro.org' });\r\n\r\n    const emailid = process.env.REACT_APP_EMAILID || \"comeback5sys\";\r\n    SetCookie('emailid', emailid);\r\n    SetCookie('emailid', emailid, { path: '/' });\r\n    SetCookie('emailid', emailid, { path: 'ai-pro.org' });\r\n    SetCookie('emailid', emailid, { path: '.ai-pro.org' });\r\n    RemoveCookie(\"daily\");\r\n\r\n    if (currency === 'gbp') {\r\n      window.location.href = '/pricing/?ppg=24&pmt=st';\r\n    } else if (currency === 'eur') {\r\n      window.location.href = '/pricing/?ppg=26&pmt=st';\r\n    } else if (currency === 'brl') {\r\n      window.location.href = '/pricing/?ppg=28&pmt=st';\r\n    } else if (currency === 'sar') {\r\n      window.location.href = '/pricing/?ppg=30&pmt=st';\r\n    } else if (currency === 'aed') {\r\n      window.location.href = '/pricing/?ppg=32&pmt=st';\r\n    } else if (currency === 'pln') {\r\n      window.location.href = '/pricing/?ppg=73&pmt=st';\r\n    } else if (currency === 'ron') {\r\n      window.location.href = '/pricing/?ppg=76&pmt=st';\r\n    } else if (currency === 'czk') {\r\n      window.location.href = '/pricing/?ppg=79&pmt=st';\r\n    } else if (currency === 'huf') {\r\n      window.location.href = '/pricing/?ppg=82&pmt=st';\r\n    } else if (currency === 'dkk') {\r\n      window.location.href = '/pricing/?ppg=85&pmt=st';\r\n    } else if (currency === 'bgn') {\r\n      window.location.href = '/pricing/?ppg=88&pmt=st';\r\n    }else {\r\n      window.location.href = '/pricing/?ppg=' + DEFAULT_PPG;\r\n    }\r\n    return;\r\n  };\r\n\r\n  const isProMax = () => {\r\n    if (auth.plan === 'promax' && auth.expired === 'no' && auth.status === 'active') return true;\r\n    return false;\r\n  }\r\n\r\n  const isEnterpriseCluter = () => {\r\n    if (auth.plan === 'enterprise' && auth.plan_name.toLowerCase() !== 'enterprise' && auth.expired === 'no' && auth.status === 'active') return true;\r\n    return false;\r\n  }\r\n\r\n  return (\r\n    <>\r\n      <Helmet>\r\n        <title>AI Pro | Member's Area</title>\r\n        <meta name=\"description\" content=\"Welcome to your Dashboard, your central hub to access key information and manage all your activities in one place. Stay organized and in control.\" />\r\n        {process.env.REACT_APP_CONSORTIUM_EMAILS && auth.email && !process.env.REACT_APP_CONSORTIUM_EMAILS.includes(auth.email) && (\r\n          <script type=\"text/javascript\" src=\"//widget.trustpilot.com/bootstrap/v5/tp.widget.bootstrap.min.js\" async></script>\r\n        )}\r\n      </Helmet>\r\n      <Header auth={auth} setshowAddMoreMember={setshowAddMoreMember} />\r\n      <div className=\"myaccount bg-gray-100 min-h-[500px] flex items-center\">\r\n        <div className=\"container mx-auto py-10 px-0\">\r\n          <div className=\"max-w-6xl mx-auto py-8 px-2 md:p-8 md:flex md:flex-wrap\">\r\n            <h1 className=\"text-2xl font-bold text-blue-600 mt-6 mb-0 text-center md:text-left md:inline\">\r\n            </h1>\r\n            { showPaused === true ?\r\n              <>\r\n              <div className=\"p-6 bg-[#f7ead9] block w-full rounded-md mt-5\">\r\n                <span>\r\n                  Account Update: Your subscription is currently Paused until {auth.resumed_at}. <span onClick={handleResumeClick} className=\"pointer\"> Resume Now</span> and enjoy uninterrupted access!\r\n                </span>\r\n              </div>\r\n            </>\r\n            : \"\"}\r\n            {\r\n              (ShowUpgrade === true && ShowExpiredEnterprise === false && showPaused===false) ?\r\n                <>\r\n                  <div className=\"p-6 bg-[#f7ead9] block w-full rounded-md mt-5\">\r\n                    <span>\r\n                      Important Account Update: Your Subscription Has Expired or Is Currently Inactive. <span onClick={handleUpgradeClick} className=\"pointer\"> Reactivate</span> Now for Uninterrupted Access and Enjoyment!\r\n                    </span>\r\n                  </div>\r\n                </>\r\n              : (ShowUpgrade === true && ShowExpiredEnterprise === true && showPaused===false && entParentUserID === '') ?\r\n                <>\r\n                  <div className=\"p-6 bg-[#f7ead9] block w-full rounded-md mt-5\">\r\n                    <span>\r\n                      Important Account Update: Your subscription is either expired or currently inactive. For continuous access, kindly reach out to our <a href=\"https://ai-pro.org/contact-us/\" rel=\"noreferrer\" target='_blank'><span className=\"pointer\">support team</span></a>.\r\n                    </span>\r\n                  </div>\r\n                </>\r\n              : (ShowUpgrade === true && ShowExpiredEnterprise === true && entParentUserID !== '') ?\r\n                <>\r\n                  <div className=\"p-6 bg-[#f7ead9] block w-full rounded-md mt-5\">\r\n                    <span>\r\n                      Important Account Update: Your subscription Has Expired or Is Currently Inactive. Please contact your account administrator for uninterrupted access.\r\n                    </span>\r\n                  </div>\r\n                </>\r\n              : (auth.status === 'active' && !isProMax() && !isEnterpriseCluter()) ?\r\n                  (isEnterprise === true) ?\r\n                    (showTokenMaxoutFinal === true) ?\r\n                      <>\r\n                        <div className=\"p-6 bg-[#f7ead9] block w-full rounded-md mt-5\">\r\n                          <span>\r\n                            <strong>Token Limit Exceeded:</strong> You have reached your token limit for this month.&nbsp;\r\n                            <a href=\"https://ai-pro.org/contact-us/\" className=\"text-[#3b82f6]\"><strong>CONTACT US</strong></a> to continue accessing and enjoying our services without interruption.\r\n                          </span>\r\n                        </div>\r\n                      </>\r\n                    :\r\n                    (showTokenMaxoutWarning === true) ?\r\n                      <>\r\n                        <div className=\"p-6 bg-[#f7ead9] block w-full rounded-md mt-5\">\r\n                          <span>\r\n                            <strong>Token Limit Warning:</strong> You're just a few tokens away from reaching you limit for this month.&nbsp;\r\n                            <a href=\"https://ai-pro.org/contact-us/\" className=\"text-[#3b82f6]\"><strong>CONTACT US</strong></a> to continue accessing and enjoying our services without interruption.\r\n                          </span>\r\n                        </div>\r\n                      </> : <DownAppBannerContainer downAppBanner={downAppBanner}></DownAppBannerContainer>\r\n                  :\r\n                    (showTokenMaxoutFinal === true) ?\r\n                      <>\r\n                        <div className=\"p-6 bg-[#f7ead9] block w-full rounded-md mt-5\">\r\n                          <span>\r\n                            <strong>Token Limit Exceeded:</strong> You have reached your token limit for this month.&nbsp;\r\n                            <a href=\"/upgrade/?mx=1\" className=\"text-[#3b82f6]\"><strong>UPGRADE NOW</strong></a> to continue accessing and enjoying our services without interruption.\r\n                          </span>\r\n                        </div>\r\n                      </>\r\n                    :\r\n                    (showTokenMaxoutWarning === true) ?\r\n                      <>\r\n                        <div className=\"p-6 bg-[#f7ead9] block w-full rounded-md mt-5\">\r\n                          <span>\r\n                            <strong>Token Limit Warning:</strong> You're just a few tokens away from reaching you limit for this month.&nbsp;\r\n                            <a href=\"/upgrade/?mx=1\" className=\"text-[#3b82f6]\"><strong>UPGRADE NOW</strong></a> to continue accessing and enjoying our services without interruption.\r\n                          </span>\r\n                        </div>\r\n                      </> : <DownAppBannerContainer downAppBanner={downAppBanner}></DownAppBannerContainer>\r\n              : <DownAppBannerContainer downAppBanner={downAppBanner}></DownAppBannerContainer>\r\n            }\r\n            { false && auth.plan && auth.status === 'active' ? <>\r\n              {process.env.REACT_APP_CONSORTIUM_EMAILS && auth.email && !process.env.REACT_APP_CONSORTIUM_EMAILS.includes(auth.email) && (\r\n                <div id=\"tp-container\" className=\"flex flex-row text-center md:text-left w-[205px] mx-auto md:mx-0 md:mt-[10px]\">\r\n                  <div className='flex flex-col justify-start'>\r\n                    {/* <!-- TrustBox widget - Mini -->  */}\r\n                      <div id=\"tp-star-container\">\r\n                        <div class=\"trustpilot-widget\" data-locale=\"en-US\" data-template-id=\"53aa8807dec7e10d38f59f32\" data-businessunit-id=\"63f8938353044ed29109ad33\" data-style-height=\"150px\" data-style-width=\"100%\" data-theme=\"light\">\r\n                          <a href=\"https://www.trustpilot.com/review/ai-pro.org\" target=\"_blank\" rel=\"noreferrer\">Trustpilot</a>\r\n                        </div>\r\n                      </div>\r\n                    {/* <!-- End TrustBox widget --> */}\r\n                  </div>\r\n                  <div className='flex flex-col justify-start'>\r\n                    <div className=\"trustpilot-widget ml-[-10px] sm:ml-[-15px] mt-[27px] h-[40px]\" data-locale=\"en-US\" data-template-id=\"56278e9abfbbba0bdcd568bc\" data-businessunit-id=\"63f8938353044ed29109ad33\" data-style-height=\"52px\" data-style-width=\"225px\" data-font-family=\"Poppins\" data-border-color=\"#00b67a\">\r\n                      <a href=\"https://www.trustpilot.com/review/ai-pro.org\" target=\"_blank\" rel=\"noreferrer\">Trustpilot</a>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              )}\r\n            </> : <></>}\r\n            <span className=\"text-sm text-center md:text-right md:ml-auto font-bold py-8 hidden md:inline\">Your headquarters for powerful AI apps.</span>\r\n            <AiChatbots auth={auth} />\r\n            <AiArts auth={auth} />\r\n            {/*<AiEducations />*/}\r\n            {process.env.REACT_APP_CONSORTIUM_EMAILS && auth.email && !process.env.REACT_APP_CONSORTIUM_EMAILS.includes(auth.email) && (\r\n              <div className=\"bg-gray-100 flex items-center mt-4 md:mt-0 w-full\">\r\n                <div className=\"container mx-auto px-4 sm:px-0\">\r\n                  <div className=\"mx-auto pb-5\">\r\n                    <div className=\"bg-white drop-shadow-sm p-6 rounded-tr-md rounded-br-md rounded-bl-md flex flex-col\">\r\n                      <div className=\"apptitle block\">\r\n                        <h1 className=\"text-xl font-bold text-gray-800\">\r\n                          Feature Request\r\n                        </h1>\r\n                      </div>\r\n                      <div className=\"container flex flex-wrap\">\r\n                        <div className=\"w-full sm:w-1/2 lg:w-3/4 p-4\">\r\n                          <span className=\"text-[14px] text-left ml-auto\">Is there a feature you are looking for or need for your business? Tell us about it.\r\n                          </span>\r\n                        </div>\r\n                        <div className=\"w-full sm:w-1/2 lg:w-1/4 p-4 text-right flex flex-col\">\r\n                          <a\r\n                            className=\"bg-blue-500 text-blue-500 bg-white border border-blue-500 text-left text-[12px] font-bold py-2 pl-3 pr-6 w-full rounded-md\"\r\n                            style={{ textDecoration: 'none' }}\r\n                            href={feat_request}\r\n                            target=\"_blank\"\r\n                            rel=\"noreferrer\"\r\n                            role=\"button\"\r\n                            whileHover={{ backgroundColor: \"#5997fd\" }}\r\n                            whileTap={{ scale: 0.9 }}\r\n                          >\r\n                            Send a Request <FaArrowRight aria-label=\"right arrow\" role=\"img\" className=\"inline text-xs mt-0.5 float-right mr-[-16px]\" />\r\n                          </a>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <AddMoreMemberModal showAddMoreMember={showAddMoreMember} setshowAddMoreMember={setshowAddMoreMember} setMoreToAddMember={setMoreToAddMember} setMoreToAddMemberTotalAmount={setMoreToAddMemberTotalAmount} setShowCompletePurchase={setShowCompletePurchase} />\r\n      <MemberCompletePurchase moreToAddMember={moreToAddMember} moreToAddMemberTotalAmount={moreToAddMemberTotalAmount} setShowCompletePurchase={setShowCompletePurchase} showCompletePurchase={showCompletePurchase} />\r\n      <Footer auth={auth} />\r\n    </>\r\n  );\r\n}\r\n\r\nconst DownAppBannerContainer = ({ downAppBanner }) => {\r\n  //   <div className=\"p-6 bg-[#f7ead9] block w-full rounded-md mt-5\">\r\n  //     <div>Happy to see you back! Unfortunately, our <b>{ downAppBanner === 'chat' ? 'ChatGPT tools' : 'Stable Diffusion-powered tools' }</b> are experiencing issues due to a global ChatGPT outage. We expect to be fully online shortly.</div>\r\n  //     <div className=\"mt-5\">In the mean time, check out our <b>{ downAppBanner === 'sd' ? 'ChatGPT powered tools' : 'Stable Diffusion-powered tools' }</b> to discover other fun things to do here in AI-Pro.</div>\r\n  //   </div>\r\n  return (\r\n    <>\r\n      { downAppBanner ? (\r\n        <div className=\"p-6 bg-[#f7ead9] block w-full rounded-md mt-5 info-banner\">\r\n          <div dangerouslySetInnerHTML={{ __html: downAppBanner }} />\r\n        </div>\r\n      ) : <></> }\r\n    </>\r\n  )\r\n}\r\n\r\nexport default MyAccount;", "// extracted by mini-css-extract-plugin\nexport default {};"], "names": ["base_url", "process", "useEffect", "script", "document", "createElement", "src", "async", "onload", "body", "append<PERSON><PERSON><PERSON>", "_jsx", "_Fragment", "children", "className", "price_per_member", "user_id", "AddMoreMemberModal", "_ref", "showAddMoreMember", "setshowAddMoreMember", "setMoreToAddMember", "setMoreToAddMemberTotalAmount", "setShowCompletePurchase", "membersToAdd", "setMembersToAdd", "useState", "totalAmount", "setTotalAmount", "paymentInterval", "setpaymentInterval", "auth", "<PERSON><PERSON>", "undefined", "plan", "interval", "toLowerCase", "modalAddMoreMembersClose", "modal", "getElementById", "style", "display", "toastr", "positionClass", "modalAddMoreMembersOpen", "id", "class", "_jsxs", "onClick", "type", "value", "motion", "button", "whileHover", "backgroundColor", "whileTap", "scale", "window", "location", "href", "MemberCompletePurchase", "_ref2", "moreToAddMember", "moreToAddMemberTotalAmount", "showCompletePurchase", "modalClose", "modalOpen", "aiproLogo", "alt", "sendViaEmail", "members", "total_amount", "querySelector", "classList", "add", "axios", "post", "headers", "then", "res", "output", "data", "remove", "success", "catch", "error", "response", "status", "FaInfoCircle", "submitPaymentInformation", "name", "tag", "icon", "description", "target", "poweredByOpenAI", "isComingSoon", "buttonText", "defaultButtonText", "Boolean", "getTitle", "title", "a", "role", "FaArrowRight", "tools", "JSON", "parse", "props", "isMobile", "setIsMobile", "upid", "user_pid", "text2imagelink", "txt2img", "interiorgptlink", "interiorgpt", "restorephotolink", "restorephoto", "removebg", "avatar_maker_link", "avatar_make", "story_book_link", "storybook", "flux_link", "handleResize", "innerWidth", "addEventListener", "removeEventListener", "fnRedirectApp", "url", "redirectApp", "fnReturnAppHref", "returnAppHref", "Container", "Carousel", "infiniteLoop", "showArrows", "emulate<PERSON><PERSON><PERSON>", "autoPlay", "centerSlidePercentage", "Card", "dream_photo_icon", "interior_icon", "restore_photo_icon", "remove_background_icon", "avatar_maker_icon", "story_book_icon", "flux_icon", "ask_vid_icon", "chatbotprolink", "chatpro", "chatpdflink", "chatpdf", "chatpdfv2link", "chatpdfv2", "convertenglishlink", "convert2english", "teacher_ai_link", "teacher_ai", "trips_ai_link", "trips_ai", "recipe_maker_link", "recipe_maker", "translate_now_link", "translate_now", "searchai_link", "searchai", "multillm_link", "multillm", "sitebot_link", "sitebot", "codingai_link", "codingai", "homework_link", "homework", "chatbot_pro_icon", "chatpdf_icon", "chatpdfv2_icon", "grammar_icon", "teacher_ai_icon", "trips_ai_icon", "recipe_maker_icon", "translate_now_icon", "searchai_icon", "multillm_icon", "sitebot_icon", "codingai_icon", "homework_help_icon", "api_url", "DownAppBannerContainer", "downAppBanner", "dangerouslySetInnerHTML", "__html", "setDownAppBanner", "showTokenMaxoutWarning", "setshowTokenMaxoutWarning", "current", "showTokenMaxoutFinal", "setshowTokenMaxoutFinal", "showPaused", "setShowPaused", "loaded", "setLoaded", "head", "initializeChat", "initializeChatWidget", "chatConfig", "token", "console", "fetchedBanner", "banner_content", "checkBanner", "fetchBanner", "max_token", "max_tokens", "total_usage", "eightyPercentOfToken", "setTimeout", "isPro", "Get<PERSON><PERSON><PERSON>", "avatar<PERSON><PERSON>", "appUrl", "includes", "folder", "link", "fetch", "blob", "blobUrl", "URL", "createObjectURL", "setAttribute", "click", "<PERSON><PERSON><PERSON><PERSON>", "RemoveCookie", "domain", "path", "ShowUpgrade", "ShowExpiredEnterprise", "isEnterprise", "entParentUserID", "expired", "ent_parent_user_id", "<PERSON><PERSON><PERSON>", "content", "REACT_APP_CONSORTIUM_EMAILS", "email", "Header", "resumed_at", "handleResumeClick", "handleUpgradeClick", "currency", "<PERSON><PERSON><PERSON><PERSON>", "emailid", "REACT_APP_EMAILID", "rel", "plan_name", "AiChatbots", "AiArts", "textDecoration", "Footer"], "sourceRoot": ""}