"use strict";(self.webpackChunkv1=self.webpackChunkv1||[]).push([[7670,9044,7250],{72608:(e,t,a)=>{a.d(t,{Z:()=>r});a(72791);const r=a.p+"static/media/AIPRO.0580715cba3cbc2179c46b033d087db9.svg"},34492:(e,t,a)=>{a.d(t,{aS:()=>l,mD:()=>P,mW:()=>m,o0:()=>p,oB:()=>R,p6:()=>c,rZ:()=>o,tN:()=>A,x6:()=>i,yt:()=>d});var r=a(74335),s=a(80184);function n(e){return e?"usd"===e.toLowerCase()?"$":"eur"===e.toLowerCase()?"€":"gbp"===e.toLowerCase()?"£":"brl"===e.toLowerCase()||"sar"===e.toLowerCase()?"R$":"":""}function o(e){return e?"usd"===e.toLowerCase()||"eur"===e.toLowerCase()?"US":"gbp"===e.toLowerCase()?"GB":"brl"===e.toLowerCase()?"US":"sar"===e.toLowerCase()?"AE":"chf"===e.toLowerCase()?"CH":"sek"===e.toLowerCase()?"SE":"US":""}function c(e){if(!e)return"";const t=new Date(e.replace(/ /g,"T"));return["January","February","March","April","May","June","July","August","September","October","November","December"][t.getMonth()]+" "+t.getDate()+" "+t.getFullYear()}function l(e){return e.trial_price?e.trial_price:e.price?e.price:"0"}function i(e){return e.toString().replace(/\B(?=(\d{3})+(?!\d))/g,",")}function _(e){const t=parseFloat(e);return i(t%1==0?t.toFixed(0):t.toFixed(2))}function d(e,t){return e&&t?isNaN(t)?"":parseFloat(t)>=0?"sar"===e.toLowerCase()?_(t).toLocaleString("en-US")+"ر.س.":"aed"===e.toLowerCase()?_(t).toLocaleString("en-US")+"AED":"chf"===e.toLowerCase()?_(t).toLocaleString("en-US")+"CHF":"sek"===e.toLowerCase()?_(t).toLocaleString("en-US")+"SEK":"pln"===e.toLowerCase()?_(t).toLocaleString("en-US")+"zł":"ron"===e.toLowerCase()?_(t).toLocaleString("en-US")+"LEI":"czk"===e.toLowerCase()?"Kč"+_(t).toLocaleString("en-US"):"huf"===e.toLowerCase()?_(t).toLocaleString("en-US")+"Ft":"dkk"===e.toLowerCase()?_(t).toLocaleString("en-US")+"kr.":"bgn"===e.toLowerCase()?_(t).toLocaleString("en-US")+"лв":"try"===e.toLowerCase()?_(t).toLocaleString("en-US")+"₺":n(e)+_(t).toLocaleString("en-US"):"-"+n(e)+(-1*_(t)).toLocaleString("en-US"):""}function A(e,t){e=new Date(e);var a=((t=new Date(t)).getTime()-e.getTime())/1e3;return a/=60,Math.abs(Math.round(a))}function p(e){if(!e)return"";const t=new Date(e.replace(/ /g,"T"));return["January","February","March","April","May","June","July","August","September","October","November","December"][t.getMonth()]+" "+t.getDate()+" "+t.getFullYear()+" "+t.getHours()+":"+t.getMinutes()}function m(e){let{plan:t}=e,a="",n="";return"Yearly"===t.payment_interval&&(a=d(t.currency,parseFloat(t.price/365).toFixed(2)),n=(0,s.jsxs)("div",{class:"text-xs mb-4",children:["billed ",d(t.currency,t.price),"/year"]})),"Monthly"===t.payment_interval&&(a=d(t.currency,parseFloat(t.price/30).toFixed(2)),n=(0,s.jsxs)("div",{class:"text-xs mb-4",children:["billed ",d(t.currency,t.price),"/Month"]})),t.trial_price&&(a=d(t.currency,parseFloat(t.trial_price/t.trial_days).toFixed(2)),n=(0,s.jsxs)("div",{class:"text-xs mb-4",children:["billed ",d(t.currency,t.trial_price)]})),(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("p",{className:"text-4xl font-bold text-gray-800 "+(""===(0,r.bG)("p_toggle")?"mb-4":""),children:[a," ",(0,s.jsx)("span",{className:"text-sm",children:" per Day"})]}),n]})}function P(e){let{plan:t}=e;return"on"===(0,r.bG)("daily")?m({plan:t}):t.trial_price?(0,s.jsx)("p",{className:"text-4xl font-bold text-gray-800 mb-4",children:d(t.currency,t.trial_price)}):(0,s.jsxs)("p",{className:"text-4xl font-bold text-gray-800 mb-4",children:[d(t.currency,t.price),(0,s.jsxs)("span",{className:"text-sm",children:["/","Monthly"===t.payment_interval?"month":"year"]})]})}function R(){const e=window.location.href;return e.indexOf("staging")>-1?"staging.":e.indexOf("dev")>-1?"dev.":""}},27250:(e,t,a)=>{a.r(t),a.d(t,{default:()=>l});var r=a(72791),s=a(72608),n=a(19878),o=a(28891),c=(a(29534),a(80184));const l=function(e){let{hideNavLink:t,auth:l=(0,o.gx)()}=e;const i=window.location.pathname,_=/\/start-chatgpt-go\/?$/.test(i),d=/\/text-to-image\/?$/.test(i),A=/\/start-chatgpt-v2\/?$/.test(i),p=i.includes("/register-auth"),m=!t,P={NODE_ENV:"production",PUBLIC_URL:"/themes/echo",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_API_URL:"http://localhost:9002/api",REACT_APP_BASE_URL:"http://localhost:9002/",REACT_APP_BASIC_ANNUAL_DOWNGRADE_ID:"15",REACT_APP_BASIC_ANNUAL_DOWNGRADE_ID_AED:"45",REACT_APP_BASIC_ANNUAL_DOWNGRADE_ID_BRL:"37",REACT_APP_BASIC_ANNUAL_DOWNGRADE_ID_EUR:"33",REACT_APP_BASIC_ANNUAL_DOWNGRADE_ID_GBP:"29",REACT_APP_BASIC_ANNUAL_DOWNGRADE_ID_SAR:"41",REACT_APP_BASIC_ANNUAL_UPGRADE_ID:"16,20,73,75",REACT_APP_BASIC_ANNUAL_UPGRADE_ID_AED:"46,48,84,85",REACT_APP_BASIC_ANNUAL_UPGRADE_ID_BRL:"38,40,80,81",REACT_APP_BASIC_ANNUAL_UPGRADE_ID_EUR:"34,36,78,79",REACT_APP_BASIC_ANNUAL_UPGRADE_ID_GBP:"30,32,76,77",REACT_APP_BASIC_ANNUAL_UPGRADE_ID_SAR:"42,44,82,83",REACT_APP_BASIC_UPGRADE_ID:"16,19,20,73,75",REACT_APP_BASIC_UPGRADE_ID2:"16,20,73,75",REACT_APP_BASIC_UPGRADE_ID_AED:"46,47,48,84,85",REACT_APP_BASIC_UPGRADE_ID_BRL:"38,39,40,80,81",REACT_APP_BASIC_UPGRADE_ID_EUR:"34,35,36,78,79",REACT_APP_BASIC_UPGRADE_ID_GBP:"30,31,32,76,77",REACT_APP_BASIC_UPGRADE_ID_SAR:"42,43,44,82,83",REACT_APP_BOT_TOKEN:"XXLx73ThdbDdKDmqajsv",REACT_APP_BTUTIL_API_URL:"https://dev.api.ai-pro.org/",REACT_APP_BTUTIL_CSS_URL:"https://dev.api.ai-pro.org/ext-app/css/btutil-regUpgradeModal-v1.min.css?ver=",REACT_APP_CHATHEAD_URL:"https://staging.sitebot.ai-pro.org/chat.js",REACT_APP_DEFAULT_PPG:"12",REACT_APP_DOWNGRADE_ID:"29,30,31,32",REACT_APP_DOWNGRADE_ID_AED:"45,46,47,48",REACT_APP_DOWNGRADE_ID_BRL:"37,38,39,40",REACT_APP_DOWNGRADE_ID_EUR:"33,34,35,36",REACT_APP_DOWNGRADE_ID_GBP:"29,30,31,32",REACT_APP_DOWNGRADE_ID_SAR:"41,42,43,44",REACT_APP_ENTERPRISE_ID:"62",REACT_APP_HOST_ENV:"production",REACT_APP_PROMAX_ANNUAL_DOWNGRADE_ID:"15,16,19,20,73",REACT_APP_PROMAX_ANNUAL_DOWNGRADE_ID_AED:"45,46,47,48,84",REACT_APP_PROMAX_ANNUAL_DOWNGRADE_ID_BRL:"37,38,39,40,80",REACT_APP_PROMAX_ANNUAL_DOWNGRADE_ID_EUR:"33,34,35,36,78",REACT_APP_PROMAX_ANNUAL_DOWNGRADE_ID_GBP:"29,30,31,32,76",REACT_APP_PROMAX_ANNUAL_DOWNGRADE_ID_SAR:"41,42,43,44,82",REACT_APP_PROMAX_ANNUAL_UPGRADE_ID:"62",REACT_APP_PROMAX_DOWNGRADE_ID:"70,74 #PRO YEARLY, PRO MONTHLY",REACT_APP_PROMAX_DOWNGRADE_ID_AED:"48,46 #PRO YEARLY, PRO MONTHLY",REACT_APP_PROMAX_DOWNGRADE_ID_BRL:"40,38 #PRO YEARLY, PRO MONTHLY",REACT_APP_PROMAX_DOWNGRADE_ID_EUR:"36,34 #PRO YEARLY, PRO MONTHLY",REACT_APP_PROMAX_DOWNGRADE_ID_GBP:"32,30 #PRO YEARLY, PRO MONTHLY",REACT_APP_PROMAX_DOWNGRADE_ID_SAR:"44,42 #PRO YEARLY, PRO MONTHLY",REACT_APP_PROMAX_UPGRADE_ID:"75,62  YEARLY, ENTERPRISE",REACT_APP_PROMAX_UPGRADE_ID_BRL:"81 #PROMAX YEARLY",REACT_APP_PROMAX_UPGRADE_ID_EUR:"79 #PROMAX YEARLY",REACT_APP_PROMAX_UPGRADE_ID_GBP:"77 #PROMAX YEARLY",REACT_APP_PROMAX_UPGRADE_ID_SAR:"85 #PROMAX YEARLY",REACT_APP_PRO_ANNUAL_DOWNGRADE_ID:"15,16,19",REACT_APP_PRO_ANNUAL_DOWNGRADE_ID_AED:"45,46,47",REACT_APP_PRO_ANNUAL_DOWNGRADE_ID_BRL:"37,38,39",REACT_APP_PRO_ANNUAL_DOWNGRADE_ID_EUR:"33,34,35",REACT_APP_PRO_ANNUAL_DOWNGRADE_ID_GBP:"29,30,31",REACT_APP_PRO_ANNUAL_DOWNGRADE_ID_SAR:"41,42,43",REACT_APP_PRO_ANNUAL_UPGRADE_ID:"62",REACT_APP_PRO_ANNUAL_UPGRADE_ID_AED:"84,85",REACT_APP_PRO_ANNUAL_UPGRADE_ID_BRL:"80,81",REACT_APP_PRO_ANNUAL_UPGRADE_ID_EUR:"78,79",REACT_APP_PRO_ANNUAL_UPGRADE_ID_GBP:"76,77",REACT_APP_PRO_ANNUAL_UPGRADE_ID_SAR:"82,83",REACT_APP_PRO_DOWNGRADE_BASIC_ID:"15,19",REACT_APP_PRO_DOWNGRADE_BASIC_ID_AED:"45,47",REACT_APP_PRO_DOWNGRADE_BASIC_ID_BRL:"37,39",REACT_APP_PRO_DOWNGRADE_BASIC_ID_EUR:"33,35",REACT_APP_PRO_DOWNGRADE_BASIC_ID_GBP:"29,31",REACT_APP_PRO_DOWNGRADE_BASIC_ID_SAR:"41,43",REACT_APP_PRO_MAX_UPGRADE_ID:"73,75",REACT_APP_PRO_MAX_UPGRADE_ID_AED:"84,85",REACT_APP_PRO_MAX_UPGRADE_ID_BRL:"80,81",REACT_APP_PRO_MAX_UPGRADE_ID_EUR:"78,79",REACT_APP_PRO_MAX_UPGRADE_ID_GBP:"76,77",REACT_APP_PRO_MAX_UPGRADE_ID_SAR:"80,81",REACT_APP_PRO_UPGRADE_ID:"20,73,75",REACT_APP_PRO_UPGRADE_ID_AED:"48,84,85",REACT_APP_PRO_UPGRADE_ID_BRL:"40,80,81",REACT_APP_PRO_UPGRADE_ID_EUR:"36,78,79",REACT_APP_PRO_UPGRADE_ID_GBP:"32,76,77",REACT_APP_PRO_UPGRADE_ID_SAR:"44,82,83",REACT_APP_STRIPE_PUBLIC_KEY_LIVE:"pk_live_51MH0TVLtUaKDxQEZNwiJOL1O8aLIA6fQEyI7cqjDnuSnMXwnOjOtu2JHjRD6PhF6hyoQAfM91dxrxNUEdyoCv9m900CPfMnfSk",REACT_APP_STRIPE_PUBLIC_KEY_TEST:"pk_test_51MH0TVLtUaKDxQEZH5ODSKmyw5TSm1lEVwyKkhVbNPZCv83lu4xL2aK8NbiJkkeG9XJt6td7kRLET8gpby37dKTs00uLTgkXVr",REACT_APP_TOOLS_URL:'{"chatbot":"https://chat.ai-pro.org/chatbot","chat":"https://chat.ai-pro.org/chat","chatpro":"https://chatpro.ai-pro.org/chat","chatgpt":"https://chatgpt.ai-pro.org","chatpdf":"https://chatpdf.ai-pro.org","convert2english":"https://app.ai-pro.org/convert-to-proper-english","interiorgpt":"https://interiorgpt.ai-pro.org","promptlibrary":"https://chatlibrary.ai-pro.org","removebg":"https://clearbg.ai-pro.org","restorephoto":"https://restorephotos.ai-pro.org","stablediffusion":"https://ai-pro.org/ai-tool-stable-diffusion","txt2img":"https://app.ai-pro.org/create-art"}'}.REACT_APP_ShowMaintenanceBanner||"";(0,r.useEffect)(()=>{const e=R(s.Z,"image"),t=R(n,"image");return document.head.append(e,t),p||Promise.all([a.e(7749),a.e(1707)]).then(a.bind(a,51707)),()=>{e.remove(),t.remove()}},[p]);const R=(e,t)=>{const a=document.createElement("link");return a.rel="preload",a.href=e,a.as=t,a};return(0,c.jsxs)(c.Fragment,{children:[P&&(0,c.jsx)("div",{id:"maintenance-container",className:"p-[5px] bg-[#f7a73f] text-center",children:"We are currently undergoing maintenance. We're expecting to be back at 4 AM EST."}),(0,c.jsx)("header",{id:"header",className:"headernav border-gray-500 bg-white text-black w-3/4 mx-auto rounded-3xl mt-3 py-2 z-[8888] "+(P?"top-[60px]":""),children:(0,c.jsxs)("div",{className:"container mx-auto flex justify-between items-center px-4",children:[(0,c.jsxs)("picture",{className:"aiprologo",children:[(0,c.jsx)("source",{type:"image/webp",srcSet:n,width:"150",height:"52",className:"aiprologo"}),(0,c.jsx)("img",{src:s.Z,alt:"AI-Pro Logo",className:"aiprologo"})]}),(_||d||A)&&m&&(0,c.jsx)("nav",{className:"text-xs lg:text-sm block inline-flex",id:"menu",children:(0,c.jsx)("ul",{className:"headnav flex inline-flex",children:(0,c.jsx)("li",{className:"mr-1 md:mr-2 lg:mr-6",children:(0,c.jsx)("a",{href:l?"/my-account":"/login",className:"font-bold","aria-label":l?"my-account":"login",children:l?"My Apps":"LOG IN"})})})})]})})]})}},60180:(e,t,a)=>{a.r(t),a.d(t,{default:()=>O});var r=a(72791),s=(a(39832),a(96347)),n=a(27250),o=a(56355),c=a(53647),l=a(28891),i=a(74335),_=a(91933),d=a(31243),A=a(34492),p=a(65764),m=a(95828),P=a.n(m),R=(a(92831),a(39230)),x=a(80184);const u=(0,i.bG)("pricing")?(0,i.bG)("pricing"):"",h=(0,i.bG)("access")?(0,i.bG)("access"):"",E=(0,i.bG)("pmt")?(0,i.bG)("pmt"):"",D=(0,i.bG)("cta_pmt")?(0,i.bG)("cta_pmt"):"",y=(0,i.bG)("locales")?(0,i.bG)("locales"):"",g=(0,i.bG)("ppg")?(0,i.bG)("ppg"):"";var N=null;async function b(){if(N)return N;const e=(await d.Z.post("http://localhost:9002/api/get-plan",{plan_id:u},{headers:{"content-type":"application/x-www-form-urlencoded"}})).data;return e.success?(N=e.data,e.data):[]}const C=function(){const{t:e}=(0,R.$G)();(0,r.useEffect)(()=>{P().options={positionClass:"toast-top-center"}},[]),(0,r.useEffect)(()=>{let e=(0,i.bG)("threed_error");void 0!==e&&""!==e&&setTimeout(function(){(0,i.I1)("threed_error",""),P().error(e)},2e3)},[]);const{data:t}=(0,_.useQuery)("users",b),[a,m]=(0,r.useState)("creditCard"),[C,T]=(0,r.useState)(""),[f,L]=(0,r.useState)(""),[O,I]=(0,r.useState)(""),[S,v]=(0,r.useState)(""),[U,w]=(0,r.useState)(""),[G,j]=(0,r.useState)(""),[M,B]=(0,r.useState)(""),[k,W]=(0,r.useState)(""),[Y,X]=(0,r.useState)(!0),F=(0,l.gx)("/register-auth"),H=(0,p.useStripe)();if(void 0===F||!1===F)return;if(Y&&"active"===F.status&&"no"===F.expired)return void(window.location.href="/my-account");var K=new Date;K.setTime(K.getTime()+2592e6);var q=new Date,J=new Date;if(J.setDate(q.getDate()+30),t&&t.currency&&t.price){var Z=t.price;""!==t.trial_price&&(Z=t.trial_price),(0,i.I1)("currency",t.currency,{expires:J,path:"/"}),(0,i.I1)("currency",t.currency,{expires:J,domain:".ai-pro.org",path:"/"}),(0,i.I1)("amount",Z,{expires:J,path:"/"}),(0,i.I1)("amount",Z,{expires:J,domain:".ai-pro.org",path:"/"})}if("pay2"===E&&t){var V="0";""!==t.trial_days&&t.trial_days>0?""!==t.trial_price&&t.trial_price>=0&&(V=100*t.trial_price):V=100*t.price,setTimeout(function(){var e=(0,A.rZ)(t.currency),a=H.paymentRequest({country:e,currency:t.currency.toLowerCase(),total:{label:t.plan_type,amount:V},requestPayerName:!0,requestPayerEmail:!0});const r=H.elements().create("paymentRequestButton",{paymentRequest:a});(async()=>{const e=await a.canMakePayment();e?(r.mount("#payment-request-button"),e.applePay&&(document.getElementById("payment-option").style.display="block",document.getElementById("other-label").innerHTML="ApplePay"),e.googlePay&&(document.getElementById("payment-option").style.display="block",document.getElementById("other-label").innerHTML="GooglePay")):(document.getElementById("payment-option").style.display="none",console.log("Does not support ApplePay or GooglePay"))})(),a.on("paymentmethod",async e=>{const t=e.paymentMethod.id,a=e.paymentMethod.payerEmail,r=e.paymentMethod.payerName;let s="",n=document.getElementsByName("referral");n[0]&&(s=n[0].value),document.querySelector(".loader-container").classList.add("active"),d.Z.post("http://localhost:9002/api/t/create-subscription-stripe-apple-google",{tk:h,payment_menthod_id:t,plan_id:u,email:a,cus_name:r,client_reference_id:s},{headers:{"content-type":"application/x-www-form-urlencoded"}}).then(function(e){let t=e.data;if(t.success)return P().success("Success"),(0,i.Sz)("pricing"),void(window.location.href="/thankyou/?plan="+N.label.replace(" ","").replace(" ",""));document.querySelector(".loader-container").classList.remove("active"),t.data&&P().error(t.data.msg)}).catch(function(e){e.response&&429===e.response.status&&(document.querySelector(".loader-container").classList.remove("active"),P().error("Sorry, too many requests. Please try again in a bit!"))})})},5e3)}const $=e=>{"creditCard"===e&&(document.getElementById("card-section").style.display="block",document.getElementById("other-section").style.display="none"),"other"===e&&(document.getElementById("card-section").style.display="none",document.getElementById("other-section").style.display="block"),m(e)},Q=function(e){return e.trial_price?e.trial_price:e.price?e.price:"0"};return(0,x.jsxs)(x.Fragment,{children:[(0,x.jsx)(n.default,{auth:F}),t?(0,x.jsx)("div",{className:"Payment bg-gray-100 md:min-h-[90vh] flex StripePaymentForm md:pt-[50px]",children:(0,x.jsx)("div",{className:"container mx-auto py-10",children:(0,x.jsx)("div",{className:"flex flex-col items-center py-10 lg:py-16",children:(0,x.jsxs)("div",{className:"flex flex-wrap md:flex-wrap justify-center w-full",children:[(0,x.jsx)("div",{className:"pay_left px-4 mb-8 w-full md:w-1/2",children:(0,x.jsx)("div",{className:"bg-white rounded-lg shadow-lg overflow-hidden",children:(0,x.jsxs)("div",{className:"px-6 pb-10",children:["pay2"===E?(0,x.jsxs)("div",{id:"payment-option",className:"",children:[(0,x.jsx)("h1",{className:"text-xl font-bold mb-4 mt-4",children:"Choose Payment Method"}),(0,x.jsx)("button",{className:`bg-white border ${"creditCard"===a?"text-blue-500":"text-gray-500"} font-bold py-2 px-3 rounded-lg mr-2 sm:py-3 sm:px-6`,onClick:()=>$("creditCard"),children:"Credit Card"}),(0,x.jsx)("button",{className:`bg-white border ${"other"===a?"text-blue-500":"text-gray-500"} font-bold py-2 px-3 rounded-lg mr-2 sm:py-3 sm:px-6`,onClick:()=>$("other"),children:(0,x.jsx)("span",{id:"other-label",children:"GooglePay"})})]}):"",(0,x.jsxs)("div",{id:"card-section",className:"card-section",children:[(0,x.jsx)("h2",{className:"text-xl font-bold mb-4 pt-10",children:e("echo.payment.index.billingDetailsText")}),(0,x.jsxs)("div",{className:"mb-4",children:[(0,x.jsxs)("label",{className:"text-xs block mb-0 mt-1",htmlFor:"name",children:[e("echo.payment.index.nameCardText")," ",(0,x.jsx)("span",{className:"text-red-500",children:"*"}),U&&(0,x.jsx)("span",{className:"text-red-500 text-xs text-left w-full mb-2 inline",children:U})]}),(0,x.jsx)("input",{className:"w-full px-3 py-2 border border-gray-300 rounded fs-exclude",type:"text",id:"name",name:"name",placeholder:"John Doe",value:C,onChange:e=>{let t=e.target.value;t=t.replace(/[^A-Za-z ]/g,""),t=t.slice(0,50),T(t)},onKeyUp:e=>{T(e.target.value)}})]}),(0,x.jsxs)("div",{className:"mb-4",children:[(0,x.jsxs)("label",{className:"text-xs block mb-0 mt-1",htmlFor:"card-number",children:[e("echo.payment.index.cardNumberText")," ",(0,x.jsx)("span",{className:"text-red-500",children:"*"}),G&&(0,x.jsx)("span",{className:"text-red-500 text-xs text-left w-full mb-2 inline",children:G})]}),(0,x.jsx)("input",{className:"w-full px-3 py-2 border border-gray-300 rounded fs-exclude",type:"text",id:"card-number",name:"card-number",placeholder:"1234 5678 9012 3456",value:f,onChange:e=>{let t=e.target.value;t=t.replace(/\D/g,""),t=t.replace(/-/g,""),t=t.replace(/(\d{4})/g,"$1-"),t=t.replace(/-$/,""),t=t.slice(0,19),L(t)},onKeyUp:e=>{L(e.target.value)}})]}),(0,x.jsxs)("div",{className:"mb-4 flex",children:[(0,x.jsxs)("div",{className:"expdate w-full md:w-2/3 mr-2 md:mr-5",children:[(0,x.jsxs)("label",{className:"text-xs block mb-0 mt-1",htmlFor:"expiration-date",children:[e("echo.payment.index.cardDateText")," ",(0,x.jsx)("span",{className:"text-red-500",children:"*"}),M&&(0,x.jsx)("span",{className:"text-red-500 text-xs text-left w-full mb-2 inline",children:M})]}),(0,x.jsx)("input",{className:"w-full px-3 py-2 border border-gray-300 rounded fs-exclude",type:"text",id:"expiration-date",name:"expiration-date",placeholder:"MM/YY",value:O,onChange:e=>{let t=e.target.value;t=t.replace(/\D/g,""),t=t.slice(0,4),t.length>=3&&(t=t.slice(0,2)+"/"+t.slice(2)),I(t)},onKeyUp:e=>{I(e.target.value)}})]}),(0,x.jsxs)("div",{className:"cvv w-full md:w-1/3",children:[(0,x.jsxs)("label",{className:"text-xs block mb-0 mt-1",htmlFor:"cvv",children:[e("echo.payment.index.cvvText")," ",(0,x.jsx)("span",{className:"text-red-500",children:"*"}),k&&(0,x.jsx)("span",{className:"text-red-500 text-xs text-left w-full mb-2 inline",children:k})]}),(0,x.jsx)("input",{className:"w-full px-3 py-2 border border-gray-300 rounded fs-exclude",type:"text",id:"cvv",name:"cvv",placeholder:e("echo.payment.index.cvvText"),value:S,onChange:e=>{let t=e.target.value;t=t.replace(/\D/g,""),t=t.slice(0,5),v(t)},onKeyUp:e=>{v(e.target.value)}})]})]}),(0,x.jsx)(s.E.button,{className:"bg-blue-500 text-white font-bold py-3 px-6 rounded-lg w-full my-4 proceed-pmt",whileHover:{backgroundColor:"#5997fd"},whileTap:{scale:.9},onClick:()=>{X(!1),w(""),j(""),B(""),W("");let t=!0,a="",r=document.getElementsByName("referral");r[0]&&(a=r[0].value),C.includes(" ")||(w(e("echo.payment.index.nameTwoErrorText")),t=!1),f?/^\d{4}(-\d{4}){3}$/.test(f)||(j(e("echo.payment.index.invalidCCText")),t=!1):(j(e("echo.payment.index.requiredText")),t=!1),O&&/^(0[1-9]|1[0-2])\/\d{2}$/.test(O)||(B("MM/YY"),t=!1),S&&/^\d{3,5}$/.test(S)||(W(e("echo.payment.index.requiredText")),t=!1);var s=C.split(" "),n=s[0],o=s[s.length-1],c=O.split("/")[0],l=O.split("/")[1];""===n&&""===o?(w(e("echo.payment.index.requiredText")),t=!1):""!==n&&""!==o||(w(e("echo.payment.index.nameTwoErrorText")),t=!1),t&&(document.querySelector(".loader-container").classList.add("active"),d.Z.post("http://localhost:9002/api/t/create-subscription-stripe",{tk:h,first_name:n,last_name:o,cc:f,ccmonth:c,ccyr:"20"+l,cvv:S,plan_id:u,client_reference_id:a},{headers:{"content-type":"application/x-www-form-urlencoded"}}).then(function(e){let t=e.data;if(t.success)return t.redirect&&""!==t.redirect?void(window.location.href=t.redirect):(P().success("Success"),(0,i.Sz)("pricing"),void(window.location.href="/thankyou/?plan="+N.label.replace(" ","").replace(" ","")));document.querySelector(".loader-container").classList.remove("active"),t.data&&P().error(t.data.msg)}).catch(function(e){e.response&&429===e.response.status&&(document.querySelector(".loader-container").classList.remove("active"),P().error("Sorry, too many requests. Please try again in a bit!"))}))},children:D||e("echo.payment.index.completePurText")})]}),(0,x.jsx)("div",{id:"other-section",className:"other-section p-8",children:(0,x.jsx)("div",{className:"mt-10 mb-10",id:"payment-request-button"})}),(0,x.jsxs)("span",{className:"text-[12px] text-gray-600",children:[(0,x.jsx)(o.DAO,{className:"inline text-lg mr-1"})," ",e("echo.payment.index.termsText")]})]})})}),(0,x.jsxs)("div",{className:"pay_right px-4 mb-8 md:w-2/5",children:[(0,x.jsxs)("div",{className:"border px-8 rounded border-gray-300",children:[(0,x.jsx)("h2",{className:"text-xl font-bold mb-4 pt-10 pb-0",children:e("echo.payment.index.orderSummaryText")}),(0,x.jsxs)("div",{className:"py-5",children:[(0,x.jsx)("div",{className:"mb-2 text-sm pb-4 border-b border-gray-300",children:(0,x.jsx)("b",{className:"text-lg "+("en"===y?"text-uppercase":""),children:"en"===y?t.plan_type_display:e("echo.pricing.vprice.localesLabel")})}),(0,x.jsxs)("div",{className:"flex flex-wrap mb-2 text-sm mt-4 mr-6",children:[(0,x.jsxs)("div",{className:"text-lg font-bold mt-4 w-1/2",children:[e("echo.payment.index.totalText"),":"]}),(0,x.jsx)("div",{className:"text-lg font-bold mt-4 w-1/2 text-right",children:"es"===y&&"03"===g?"$"+Q(t):(0,A.yt)(t.currency,Q(t))})]}),(0,x.jsx)("div",{className:"mb-8 text-sm mt-6",children:t.display_txt3&&"en"===y?t.display_txt3:e("pl"===y&&"72"===g||"es"===y&&"03"===g?"echo.payment.index.renewalText2":"echo.payment.index.renewalText")})]})]}),(0,x.jsxs)("div",{className:"securecont block p-5 mx-auto text-left",children:[(0,x.jsxs)("div",{className:"securetext mb-2 text-sm w-full",children:[(0,x.jsx)(o.kUi,{className:"inline text-lg mr-1 text-orange-500 text-xs"})," ",e("echo.payment.index.secureText")]}),(0,x.jsxs)("div",{className:"securelogo mb-2 text-sm w-full flex flex-wrap justify-center items-center",children:[(0,x.jsx)("img",{src:c,alt:"Secure Logo",className:"cclogo inline"}),(0,x.jsx)("button",{onClick:()=>{window.open("//www.securitymetrics.com/site_certificate?id=1982469&tk=d41c416c6208f1136426bc8038178d2e","Verification","location=no, toolbar=no, resizable=no, scrollbars=yes, directories=no, status=no,top=100,left=100, width=700, height=600")},title:"SecurityMetrics card safe certification",className:"h-20",children:(0,x.jsx)("img",{loading:"lazy",src:"https://www.securitymetrics.com/portal/app/ngsm/assets/img/GreyContent_Credit_Card_Safe_White_Sqr.png",alt:"SecurityMetrics card safe certification logo",className:"max-h-full",width:"80",height:"80"})})]})]})]})]})})})}):""]})};var T=a(53473);var f="";f="test"===((0,i.bG)("mode")?(0,i.bG)("mode"):"")?"pk_test_51MH0TVLtUaKDxQEZH5ODSKmyw5TSm1lEVwyKkhVbNPZCv83lu4xL2aK8NbiJkkeG9XJt6td7kRLET8gpby37dKTs00uLTgkXVr":"pk_live_51MH0TVLtUaKDxQEZNwiJOL1O8aLIA6fQEyI7cqjDnuSnMXwnOjOtu2JHjRD6PhF6hyoQAfM91dxrxNUEdyoCv9m900CPfMnfSk";const L=(0,T.J)(f);const O=function(){return(0,x.jsx)(x.Fragment,{children:(0,x.jsx)(p.Elements,{stripe:L,children:(0,x.jsx)(C,{})})})}},29534:()=>{},39832:()=>{},19878:(e,t,a)=>{e.exports=a.p+"static/media/AIPRO.84104dfd05446283b05c.webp"},53647:(e,t,a)=>{e.exports=a.p+"static/media/cc_v3.6ab0d1e0b9d27a1d2bc0.png"}}]);
//# sourceMappingURL=7670.f9704334.chunk.js.map