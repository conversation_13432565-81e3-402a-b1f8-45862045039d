{"version": 3, "file": "static/css/9665.779a5f63.chunk.css", "mappings": "AAAA,iBAII,YAAa,CAIb,MAAO,CAFP,aAAc,CALd,iBAAkB,CAMlB,OAAQ,CAJR,SAAU,CAEV,SAAU,CAHV,mBAOJ,CACA,yBACI,iBAEI,YAAa,CADb,SAEJ,CACJ,CACA,yBACI,iBACI,YAAa,CACb,SAAU,CACV,UACJ,CACJ,CCtBA,WACE,gBAAiB,CAEjB,KAAM,CADN,UAEF,CACA,WAAY,WAAa,CACzB,OAGE,QAAS,CAFT,cAAe,CACf,QAAS,CAGT,8BAAgC,CADhC,UAEF,CACA,UACE,SACF,CACA,WAEE,eAAmB,CADpB,gCAED,CACA,cAEC,4BAA8B,CAC9B,eAAiB,CAFjB,0BAGD,CAEA,yBACE,uBACE,cACF,CACF,CACA,yBACE,wBACE,kBACF,CACF,CAEA,yBACE,WACE,mBACF,CACF,CAEA,yBACE,WACE,mBACF,CACF,CAEA,qDAEE,WAAY,CACZ,oBAAqB,CAErB,gBAAiB,CACjB,SAAU,CACV,kCAAoC,CAHpC,SAIF,CAEA,iEAEE,SACF,CAEA,wBACE,GAEE,SAAU,CADV,2BAEF,CACA,GAEE,SAAU,CADV,uBAEF,CACF,CAEA,kBACE,iBACF,CAEA,UASE,qBAAuB,CACvB,iBAAkB,CAElB,gCAAkC,CAPlC,YAAa,CAGb,MAAO,CAJP,eAAgB,CADhB,iBAAkB,CAFlB,2BAA4B,CAU5B,cAAe,CALf,iBAAkB,CAJlB,oBAAqB,CAKrB,QAMF,CAEA,aACE,QACF,CAEA,YAEE,aAAe,CACf,WACF,CAEA,8CALE,aAOF,CAEA,gBACE,8BAA+B,CAC/B,eACF,CAQA,yBACE,WACE,YACF,CACF,CAEA,yBACE,aACE,yBACF,CACF,CAEA,oBAEI,eAAgB,CAKhB,kBAAmB,CACnB,+BAAiC,CAHjC,cAAe,CAIf,aAAc,CACd,eAAgB,CAChB,iBAAkB,CAPlB,YAAa,CAHb,OAAQ,CAER,eAAgB,CAGhB,QAMJ,CAEA,6CACE,SACE,YACF,CACA,cAAe,kBAAqB,CACpC,gBACE,iBAAkB,CAClB,UACF,CACA,mBAIE,qBAAsB,CACtB,YACF,CACA,mCAJE,kBAAmB,CAFnB,YAAa,CACb,qBAgBF,CAXA,gBAQE,eAAgB,CAChB,WAAY,CAFZ,cAAe,CADf,WAAY,CAHZ,sBAAuB,CAOvB,SAAU,CALV,UAMF,CACA,gBAIE,+BAAiC,CAFjC,WAAY,CACZ,oBAAsB,CAEtB,iBAAkB,CAJlB,UAKF,CACF,CACA,yBACE,WAAY,WAAa,CACzB,cAAe,eAAkB,CACjC,yFACI,QACJ,CACA,gBACE,iBAAkB,CAClB,UACF,CACF", "sources": ["home/style.css", "header/style.css"], "sourcesContent": ["div#particles-js {\r\n    position: absolute;\r\n    z-index: 0 !important;\r\n    top: 250px;\r\n    height: 200px;\r\n    width: 80%;\r\n    margin: 0 auto;\r\n    right: 0;\r\n    left: 0;\r\n}\r\n@media (max-width: 991px) {\r\n    div#particles-js {\r\n        top: 150px;\r\n        height: 100px;\r\n    }\r\n}\r\n@media (max-width: 500px) {\r\n    div#particles-js {\r\n        height: 100px;\r\n        top: 182px;\r\n        width: 100%;\r\n    }\r\n}\r\n", ".headerRef {\r\n  display: absolute;\r\n  width: 100%;\r\n  top: 0;\r\n}\r\n.aiprologo {width: 150px;}\r\nheader {\r\n  position: fixed;\r\n  top: 25px;\r\n  left: 50%;\r\n  width: 100%;\r\n  transform: translate(-50%, -50%);\r\n}\r\n.svg-icon {\r\n  fill: white;\r\n}\r\n.headernav {\r\n\tbox-shadow: 0 3px 19px -14px black;\r\n  background: #ffffff;\r\n}\r\n.headerctabtn {\r\n\tpadding: 5px 10px !important;\r\n\tborder-radius: 15px !important;\r\n\tfont-size: 0.9rem;\r\n}\r\n\r\n@media (max-width: 680px) {\r\n  #maintenance-container {\r\n    font-size: 14px;\r\n  }\r\n}\r\n@media (max-width: 590px) {\r\n  .headernav.top-\\[60px\\] {\r\n    top: 70px !important;\r\n  }\r\n}\r\n\r\n@media (max-width: 991px) {\r\n  .headernav {\r\n    width: 98% !important;\r\n  }\r\n}\r\n\r\n@media (max-width: 500px) {\r\n  .headernav {\r\n    width: 90% !important;\r\n  }\r\n}\r\n\r\n.headernav nav#menu ul li a:before,\r\n.menuArrow:before {\r\n  content: \">\";\r\n  display: inline-block;\r\n  width: 7px;\r\n  margin-right: 2px;\r\n  opacity: 0;\r\n  transition: opacity 0.2s ease-in-out;\r\n}\r\n\r\n.headernav nav#menu ul li a:hover:before,\r\n.menuArrow:hover:before {\r\n  opacity: 1;\r\n}\r\n\r\n@keyframes slideInRight {\r\n  from {\r\n    transform: translateX(-100%);\r\n    opacity: 0;\r\n  }\r\n  to {\r\n    transform: translateX(0);\r\n    opacity: 1;\r\n  }\r\n}\r\n\r\n.dropdown-wrapper {\r\n  position: relative;\r\n}\r\n\r\n.dropdown {\r\n  overscroll-behavior: contain;\r\n  scrollbar-width: none;\r\n  overflow-y: scroll;\r\n  max-height: 75vh;\r\n  display: none;\r\n  position: absolute;\r\n  top: 100%;\r\n  left: 0;\r\n  background-color: white;\r\n  border-radius: 5px;\r\n  padding: 1rem 0;\r\n  box-shadow: 0 7px 17px -10px black;\r\n}\r\n\r\n.dropdown li {\r\n  margin: 0;\r\n}\r\n\r\n.dropdown a {\r\n  display: block;\r\n  padding: 0.5rem;\r\n  width: 250px;\r\n}\r\n\r\n.dropdown-wrapper:hover .dropdown {\r\n  display: block;\r\n}\r\n\r\n.menu-container {\r\n  max-height: calc(90vh - 3.5rem);\r\n  overflow-y: auto;\r\n}\r\n\r\n\r\n/* .dropdown-wrapper:focus-within .dropdown {\r\n  display: block;\r\n} */\r\n/*WILL FIX SHIFT+TAB ACCESSIBILITY IN ANOTHER TICKET*/\r\n\r\n@media (min-width: 768px) {\r\n  .mobilenav {\r\n    display: none;\r\n  }\r\n}\r\n\r\n@media (max-width: 991px){\r\n  .headerStyle {\r\n    max-width: 980px !important;\r\n  }\r\n}\r\n\r\n.mobilenav .headnav{\r\n    right: 0;\r\n    background: #fff;\r\n    text-align: left;\r\n    padding: 15px;\r\n    font-size: 14px;\r\n    top: 72px;\r\n    border-radius: 15px;\r\n    box-shadow: 0 2px 13px -9px black;\r\n    line-height: 2;\r\n    max-height: 83vh;\r\n    overflow-y: scroll;\r\n}\r\n\r\n@media (min-width: 0px) and (max-width: 991px) {\r\n  .headnav {\r\n    display: none;\r\n  }\r\n  .ctaStartHere {margin: 0 !important;}\r\n  .hamburger-menu {\r\n    position: absolute;\r\n    right: 25px;\r\n  }\r\n  .headnav.show-menu {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    background-color: #fff;\r\n    padding: 10px;\r\n  }\r\n  .hamburger-menu {\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: center;\r\n    align-items: center;\r\n    width: 30px;\r\n    height: 30px;\r\n    cursor: pointer;\r\n    background: none;\r\n    border: none;\r\n    padding: 0;\r\n  }\r\n  .hamburger-line {\r\n    width: 30px;\r\n    height: 100%;\r\n    height: 3px !important;\r\n    background-color: #000 !important;\r\n    margin-bottom: 4px;\r\n  }\r\n}\r\n@media (max-width: 639px) {\r\n  .aiprologo {width: 110px;}\r\n  .headerctabtn {font-size: 0.8rem;}\r\n  .headerctabtn.gradient-hover-effect.text-white.rounded-3xl.block.sm\\:hidden.ml-auto.mr-3 {\r\n      margin: 0;\r\n  }\r\n  .hamburger-menu {\r\n    position: absolute;\r\n    right: 25px;\r\n  }\r\n}"], "names": [], "sourceRoot": ""}