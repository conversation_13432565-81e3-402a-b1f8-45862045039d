{"version": 3, "file": "static/js/4532.084f501a.chunk.js", "mappings": "2IAEO,SAASA,EAAiBC,GAA+B,IACxDC,EAAM,GAMV,OALKD,EAEMA,EAASE,OAAS,IACzBD,EAAME,EAAAA,EAAKC,EAAE,8CAFbH,EAAME,EAAAA,EAAKC,EAAE,4CAIVH,CACX,CAEO,SAASI,EAAwBL,EAAUM,GAC9C,IAAIL,EAAM,GAMV,OAJGD,IAAaM,IACZL,EAAME,EAAAA,EAAKC,EAAE,iDAGVH,CACX,C,0ECMA,QAxBA,WAGE,MAAMM,EAAWC,yBAWjB,OAVAC,EAAAA,EAAAA,WAAU,KACR,MAAMC,EAASC,SAASC,cAAc,UACtCF,EAAOG,IAAMN,EAAW,iDACxBG,EAAOI,OAAQ,EACfJ,EAAOK,OAAS,OAIhBJ,SAASK,KAAKC,YAAYP,IACzB,CAACH,KAEFW,EAAAA,EAAAA,KAAAC,EAAAA,SAAA,CAAAC,UACEF,EAAAA,EAAAA,KAAA,UAAQG,UAAW,iHAMzB,C,2KCdA,MAAMC,GAASC,EAAAA,EAAAA,MAAiB,KAAOC,EAAAA,OAAAA,QACjCC,GAASF,EAAAA,EAAAA,MAAiB,KAAOC,EAAAA,OAAAA,QAEvC,IAAIE,EAAQ,GACRC,EAAK,GACTb,eAAec,IAMb,eALuBC,EAAAA,EAAMC,KAAK,8CAAsD,CACtFJ,QAAOC,MACN,CAAEI,QAAS,CAAE,eAAgB,wCAERC,KACdC,OAKZ,CAoIA,QAlIA,YACExB,EAAAA,EAAAA,WAAU,KACRyB,IAAAA,QAAiB,CACfC,cAAe,qBAEhB,IAEH,IAAKC,IAAgBC,EAAAA,EAAAA,MACrBV,EAAKS,EAAaE,IAAI,MACtBZ,EAAQU,EAAaE,IAAI,SACzB,MAAM,KAAEN,IAASO,EAAAA,EAAAA,UAAS,QAASX,IAC5B5B,EAAUwC,IAAeC,EAAAA,EAAAA,UAAS,KAClCC,EAAiBC,IAAsBF,EAAAA,EAAAA,UAAS,KAChDG,EAAeC,IAAoBJ,EAAAA,EAAAA,UAAS,IAC7CK,IAAgBvB,EAAAA,EAAAA,MAEhBwB,EAAkBC,IACtBC,OAAOC,SAASC,KAAOH,GAGzB,QAAYI,IAATpB,EAAoB,OACvB,IAAY,IAATA,EAED,YADAe,EAAe,UAIjB,MAAMM,EAAmBA,KACvB,IAAIpD,GAAMF,EAAAA,EAAAA,GAAiBC,GAC3B,OAAIC,IACF4C,EAAiB5C,IACV,IAgBLqD,EAAuBC,IAC3B,IAAIC,EAAmBH,IACnBI,EAZ0BC,MAC9B,IAAIzD,GAAMI,EAAAA,EAAAA,GAAwBL,EAAU0C,GAC5C,OAAIzC,IACF4C,EAAiB5C,IACV,IAQ6ByD,GAClCF,GAAqBC,IAEzB9C,SAASgD,cAAc,qBAAqBC,UAAUC,IAAI,UAC1DhC,EAAAA,EAAMC,KAAK,4CAAoD,CAC7DgC,aAAcnC,EACd3B,SAAUA,EACVM,aAAcoC,GACb,CAAEX,QAAS,CAAE,eAAgB,uCAAyCgC,KAAK,SAASC,GACrF,IAAIC,EAASD,EAAIhC,KACjB,GAAGiC,EAAOhC,QAKR,OAJAC,IAAAA,QAAe,gBACfgC,WAAW,WACTnB,EAAe,SACjB,EAAG,KAILpC,SAASgD,cAAc,qBAAqBC,UAAUO,OAAO,UAC1DF,EAAOjC,MAAME,IAAAA,MAAa+B,EAAOjC,KAAK/B,IAC3C,KAGF,OACEmE,EAAAA,EAAAA,MAAAjD,EAAAA,SAAA,CAAAC,SAAA,CACG0B,IAAgB5B,EAAAA,EAAAA,KAACI,EAAM,KACxBJ,EAAAA,EAAAA,KAAA,OAAKG,UAAU,wFAAuFD,UACpGF,EAAAA,EAAAA,KAAA,OAAKG,UAAU,4DAA2DD,UACxEgD,EAAAA,EAAAA,MAAA,OAAK/C,UAAU,2BAA0BD,SAAA,EACvCF,EAAAA,EAAAA,KAAA,MAAIG,UAAU,0DAAyDD,SAAC,oBACxEF,EAAAA,EAAAA,KAAA,OAAKG,UAAU,gDAA+CD,UAC5DgD,EAAAA,EAAAA,MAAA,OAAK/C,UAAU,aAAYD,SAAA,EAEvBgD,EAAAA,EAAAA,MAAA,SAAO/C,UAAU,iBAAgBD,SAAA,CAC9BwB,IAAiB1B,EAAAA,EAAAA,KAAA,QAAMG,UAAU,6CAA4CD,SAAEwB,KAChF1B,EAAAA,EAAAA,KAAA,SACEG,UAAU,0NACVgD,YAAY,uBACZ,aAAW,eACXC,KAAK,WACLC,KAAK,WACLC,QAAUjB,IACRV,EAAiB,IACjBL,EAAYe,EAAEkB,OAAOC,OACJ,KAAdnB,EAAEoB,SAAgBrB,KAEvBsB,OAAQvB,EACRwB,UAAQ,KAEV3D,EAAAA,EAAAA,KAAA,SACEG,UAAU,0NACVgD,YAAY,yBACZ,aAAW,mBACXC,KAAK,WACLC,KAAK,kBACLC,QAAUjB,IACRZ,EAAmBY,EAAEkB,OAAOC,OACX,KAAdnB,EAAEoB,SAAgBrB,KAEvBuB,UAAQ,QAGZ3D,EAAAA,EAAAA,KAAC4D,EAAAA,EAAOC,OAAM,CACZT,KAAK,SACLjD,UAAU,oEACV2D,WAAY,CAAEC,MAAO,KACrBC,SAAU,CAAED,MAAO,IACnBE,QAAS7B,EACT,aAAW,iBAAgBlC,SAC5B,+BAQZ0B,IAAgB5B,EAAAA,EAAAA,KAACO,EAAM,MAG9B,C", "sources": ["core/utils/validation.jsx", "footer/index.jsx", "resetpass/index.jsx"], "sourcesContent": ["import i18n from '../../i18n';\r\n\r\nexport function ValidatePassword(password, label = 'Password') {\r\n    let msg = '';\r\n    if (!password) {\r\n        msg = i18n.t('echo.register.validation.passwordReqText');\r\n    } else if (password.length < 6) {\r\n        msg = i18n.t('echo.register.validation.passwordCharText');\r\n    }\r\n    return msg;\r\n}\r\n\r\nexport function ValidateConfirmPassword(password, confpassword) {\r\n    let msg = '';\r\n\r\n    if(password !== confpassword) {\r\n        msg = i18n.t('echo.register.validation.passwordsDoNotMatch');\r\n    }\r\n\r\n    return msg;\r\n}", "import React, { useEffect } from 'react';\r\n\r\nfunction Powered() {\r\n  // const [isHidden, setIsHidden] = useState(false);\r\n\r\n  const base_url = process.env.REACT_APP_BASE_URL;\r\n  useEffect(() => {\r\n    const script = document.createElement('script');\r\n    script.src = base_url + 'snippets/com.global.vuzo/js/com.global.vuzo.js';\r\n    script.async = true;\r\n    script.onload = () => {\r\n      // window.displayGooglePlay();\r\n      // window.displayQR();\r\n    };\r\n    document.body.appendChild(script);\r\n  }, [base_url]);\r\n  return (\r\n    <>\r\n      <footer className={`relative hidden md:block md:absolute bottom-[10px] right-[5px] w-full mt-3 py-12 md:py-2 z-[8888] md:hidden`}>\r\n      {/* <footer className=\"relative hidden md:block md:absolute bottom-[10px] right-[5px] w-full mt-3 py-12 md:py-2 z-[8888]\"> */}\r\n\r\n      </footer>\r\n    </>\r\n  );\r\n}\r\n\r\nexport default Powered;\r\n", "import React, { useState, useEffect } from 'react';\r\nimport { motion } from \"framer-motion\";\r\nimport { useSearchParams } from \"react-router-dom\";\r\nimport { useQuery } from \"react-query\";\r\nimport axios from 'axios';\r\nimport { ValidatePassword, ValidateConfirmPassword } from '../core/utils/validation';\r\nimport { GetSubdomain } from '../core/utils/cookies';\r\nimport toastr from 'toastr';\r\nimport 'toastr/build/toastr.min.css';\r\n\r\nconst Header = GetSubdomain() ? null : require('../header').default;\r\nconst Footer = GetSubdomain() ? null : require('../footer').default;\r\n\r\nvar email = '';\r\nvar tk = '';\r\nasync function checkAccessLink() {\r\n  const response = await axios.post(`${process.env.REACT_APP_API_URL}/check-access-link`, {\r\n    email, tk\r\n  }, { headers: { 'content-type': 'application/x-www-form-urlencoded' } })\r\n\r\n  const output = response.data;\r\n  if(output.success) {\r\n    return true;\r\n  } else {\r\n    return false;\r\n  }\r\n}\r\n\r\nfunction Resetpass() {\r\n  useEffect(() => {\r\n    toastr.options = {\r\n      positionClass: 'toast-top-center'\r\n    };\r\n  }, []);\r\n  \r\n  let [searchParams] = useSearchParams();\r\n  tk = searchParams.get('tk');\r\n  email = searchParams.get('email');\r\n  const { data } = useQuery(\"users\", checkAccessLink);\r\n  const [password, setPassword] = useState('');\r\n  const [confirmPassword, setConfirmPassword] = useState('');\r\n  const [passwordError, setPasswordError] = useState('');\r\n  const renderHeader = !GetSubdomain();\r\n\r\n  const handleRedirect = (path) => {\r\n    window.location.href = path;\r\n  };\r\n\r\n  if(data === undefined) return;\r\n  if(data === false) {\r\n    handleRedirect('/login');\r\n    return;\r\n  }\r\n\r\n  const validatePassword = () => {\r\n    let msg = ValidatePassword(password);\r\n    if (msg) {\r\n      setPasswordError(msg);\r\n      return false;\r\n    }\r\n\r\n    return true;\r\n  };\r\n\r\n  const validateConfirmPassword = () => {\r\n    let msg = ValidateConfirmPassword(password, confirmPassword);\r\n    if (msg) {\r\n      setPasswordError(msg);\r\n      return false;\r\n    }\r\n\r\n    return true;\r\n  }\r\n\r\n  const handleResetPassword = (e) => {\r\n    let isPassedValidate = validatePassword();\r\n    let isConfirmPasswordPassedValidate = validateConfirmPassword();\r\n    if(!isPassedValidate || !isConfirmPasswordPassedValidate) return;\r\n\r\n    document.querySelector(\".loader-container\").classList.add('active');\r\n    axios.post(`${process.env.REACT_APP_API_URL}/change-password`, {\r\n      access_token: tk,\r\n      password: password,\r\n      confpassword: confirmPassword\r\n    }, { headers: { 'content-type': 'application/x-www-form-urlencoded' } }).then(function(res) {\r\n      let output = res.data;\r\n      if(output.success) {\r\n        toastr.success(\"Success\");\r\n        setTimeout(function(){\r\n          handleRedirect('/login');\r\n        }, 3000);\r\n        return;\r\n      }\r\n\r\n      document.querySelector(\".loader-container\").classList.remove('active');\r\n      if(output.data) toastr.error(output.data.msg);\r\n    });\r\n  };\r\n\r\n  return (\r\n    <>\r\n      {renderHeader && <Header />}\r\n      <div className=\"register bg-gray-100 h-screen flex justify-center items-center pt-10 p-[20px] md:pt-2\">\r\n        <div className=\"container mx-auto py-10 px-0 md:px-4 sm:px-0 w-96 mx-auto\">\r\n          <div className=\"reg_col text-center mb-8\">\r\n            <h1 className=\"text-2xl lg:text-2xl font-bold text-center mb-6 lg:mb-8\">Reset Password</h1>\r\n            <div className=\"bg-white rounded-lg shadow-lg overflow-hidden\">\r\n              <div className=\"px-6 py-10\">\r\n\r\n                  <label className=\"relative block\">\r\n                    {passwordError && <span className=\"text-red-500 text-xs text-left w-full mb-2\">{passwordError}</span>}\r\n                    <input\r\n                      className=\"placeholder:italic placeholder:text-slate-400 block bg-white w-full border border-slate-300 rounded-md my-3 py-2 pl-3 pr-3 shadow-sm focus:outline-none focus:border-sky-500 focus:ring-sky-500 focus:ring-1 sm:text-sm\"\r\n                      placeholder=\"Enter New Password *\"\r\n                      aria-label=\"New Password\"\r\n                      type=\"password\"\r\n                      name=\"password\"\r\n                      onKeyUp={(e) => {\r\n                        setPasswordError('');\r\n                        setPassword(e.target.value);\r\n                        if(e.keyCode === 13) handleResetPassword();\r\n                      }}\r\n                      onBlur={validatePassword}\r\n                      required\r\n                    />\r\n                    <input\r\n                      className=\"placeholder:italic placeholder:text-slate-400 block bg-white w-full border border-slate-300 rounded-md my-3 py-2 pl-3 pr-3 shadow-sm focus:outline-none focus:border-sky-500 focus:ring-sky-500 focus:ring-1 sm:text-sm\"\r\n                      placeholder=\"Confirm New Password *\"\r\n                      aria-label='Confirm Password'\r\n                      type=\"password\"\r\n                      name=\"confirmpassword\"\r\n                      onKeyUp={(e) => {\r\n                        setConfirmPassword(e.target.value);\r\n                        if(e.keyCode === 13) handleResetPassword();\r\n                      }}\r\n                      required\r\n                    />\r\n                  </label>\r\n                  <motion.button\r\n                    type=\"submit\"\r\n                    className=\"bg-blue-600 text-white font-bold py-3 px-6 my-3 rounded-lg w-full\"\r\n                    whileHover={{ scale: 1.1 }}\r\n                    whileTap={{ scale: 0.9 }}\r\n                    onClick={handleResetPassword}\r\n                    aria-label=\"Reset Password\"\r\n                  >\r\n                    Reset Password\r\n                  </motion.button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      {renderHeader && <Footer />}\r\n    </>\r\n  );\r\n}\r\n\r\nexport default Resetpass;\r\n"], "names": ["ValidatePassword", "password", "msg", "length", "i18n", "t", "ValidateConfirmPassword", "confpassword", "base_url", "process", "useEffect", "script", "document", "createElement", "src", "async", "onload", "body", "append<PERSON><PERSON><PERSON>", "_jsx", "_Fragment", "children", "className", "Header", "GetSubdomain", "require", "Footer", "email", "tk", "checkAccessLink", "axios", "post", "headers", "data", "success", "toastr", "positionClass", "searchParams", "useSearchParams", "get", "useQuery", "setPassword", "useState", "confirmPassword", "setConfirmPassword", "passwordError", "setPasswordError", "renderHeader", "handleRedirect", "path", "window", "location", "href", "undefined", "validatePassword", "handleResetPassword", "e", "isPassedValidate", "isConfirmPasswordPassedValidate", "validateConfirmPassword", "querySelector", "classList", "add", "access_token", "then", "res", "output", "setTimeout", "remove", "_jsxs", "placeholder", "type", "name", "onKeyUp", "target", "value", "keyCode", "onBlur", "required", "motion", "button", "whileHover", "scale", "whileTap", "onClick"], "sourceRoot": ""}