{"version": 3, "file": "static/js/728.a688990d.chunk.js", "mappings": "kHA0FA,MACA,EAAe,IAA0B,yD,6DCxFlC,SAASA,IACd,IAAIC,GAAiBC,EAAAA,EAAAA,IAAU,kBAC3BC,GAAa,EACjB,GAAIF,SAAwE,KAAjBA,EAAoB,CAC7E,IAAIG,EAAQH,EAAeI,MAAM,UACV,IAAbD,EAAM,IACC,MAAXA,EAAM,KACRD,GAAa,EAGnB,CAEA,IAAKA,EACH,OAGF,MAAMG,EAAMC,CAAAA,SAAAA,aAAAA,WAAAA,eAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,cAAAA,EAAAA,kBAAAA,4BAAAA,mBAAAA,yBAAAA,oCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,kCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,2BAAAA,iBAAAA,4BAAAA,cAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,oBAAAA,uBAAAA,yBAAAA,8BAAAA,yBAAAA,gFAAAA,uBAAAA,6CAAAA,sBAAAA,KAAAA,uBAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,wBAAAA,KAAAA,mBAAAA,aAAAA,qCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,mCAAAA,KAAAA,8BAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,4BAAAA,4BAAAA,gCAAAA,oBAAAA,gCAAAA,oBAAAA,gCAAAA,oBAAAA,gCAAAA,oBAAAA,kCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,gCAAAA,KAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,iCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,6BAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,yBAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,iCAAAA,8GAAAA,iCAAAA,8GAAAA,oBAAAA,8jBAAYC,qBAClBC,GAASC,EAAAA,EAAAA,IAAGJ,GAClB,IAAIK,EAAO,GAOX,OANGT,EAAAA,EAAAA,IAAU,aACXS,GAAOT,EAAAA,EAAAA,IAAU,YAGnBO,EAAOG,KAAK,mBAAoB,CAAEC,GAAIF,EAAMG,OAAOZ,EAAAA,EAAAA,IAAU,cAAea,WAAYR,CAAAA,SAAAA,aAAAA,WAAAA,eAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,cAAAA,EAAAA,kBAAAA,4BAAAA,mBAAAA,yBAAAA,oCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,kCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,2BAAAA,iBAAAA,4BAAAA,cAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,oBAAAA,uBAAAA,yBAAAA,8BAAAA,yBAAAA,gFAAAA,uBAAAA,6CAAAA,sBAAAA,KAAAA,uBAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,wBAAAA,KAAAA,mBAAAA,aAAAA,qCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,mCAAAA,KAAAA,8BAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,4BAAAA,4BAAAA,gCAAAA,oBAAAA,gCAAAA,oBAAAA,gCAAAA,oBAAAA,gCAAAA,oBAAAA,kCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,gCAAAA,KAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,iCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,6BAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,yBAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,iCAAAA,8GAAAA,iCAAAA,8GAAAA,oBAAAA,8jBAAYS,uBAE7F,CAAEP,SAAQE,OACnB,C,8MCfA,MAAMM,EAA0CC,KAAKC,MAAMZ,8jBACrDa,EAAQb,CAAAA,SAAAA,aAAAA,WAAAA,eAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,cAAAA,EAAAA,kBAAAA,4BAAAA,mBAAAA,yBAAAA,oCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,kCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,2BAAAA,iBAAAA,4BAAAA,cAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,oBAAAA,uBAAAA,yBAAAA,8BAAAA,yBAAAA,gFAAAA,uBAAAA,6CAAAA,sBAAAA,KAAAA,uBAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,wBAAAA,KAAAA,mBAAAA,aAAAA,qCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,mCAAAA,KAAAA,8BAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,4BAAAA,4BAAAA,gCAAAA,oBAAAA,gCAAAA,oBAAAA,gCAAAA,oBAAAA,gCAAAA,oBAAAA,kCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,gCAAAA,KAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,iCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,6BAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,yBAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,iCAAAA,8GAAAA,iCAAAA,8GAAAA,oBAAAA,8jBAAYc,0BAA4BH,KAAKC,MAAMZ,CAAAA,SAAAA,aAAAA,WAAAA,eAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,cAAAA,EAAAA,kBAAAA,4BAAAA,mBAAAA,yBAAAA,oCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,kCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,2BAAAA,iBAAAA,4BAAAA,cAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,oBAAAA,uBAAAA,yBAAAA,8BAAAA,yBAAAA,gFAAAA,uBAAAA,6CAAAA,sBAAAA,KAAAA,uBAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,wBAAAA,KAAAA,mBAAAA,aAAAA,qCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,mCAAAA,KAAAA,8BAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,4BAAAA,4BAAAA,gCAAAA,oBAAAA,gCAAAA,oBAAAA,gCAAAA,oBAAAA,gCAAAA,oBAAAA,kCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,gCAAAA,KAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,iCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,6BAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,yBAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,iCAAAA,8GAAAA,iCAAAA,8GAAAA,oBAAAA,8jBAAYc,2BAA6B,CAAC,EAC3G,IAAIC,EAyvBJ,QAvvBA,SAAgBC,GACd,MAAMC,EAAOD,GAASA,EAAMC,KAAOD,EAAMC,KAAO,KAC1CC,GAAWF,EAAMG,aAChBC,EAAOC,IAAYC,EAAAA,EAAAA,UAAS,IAC7BC,GAAWC,EAAAA,EAAAA,MACXC,EAAoBF,EAASG,SAASC,SAAS,mBAC/CC,EAAuBL,EAASG,SAASC,SAAS,eAClDE,EAAmC,MAAtBN,EAASG,UACrBI,EAAUC,IAAeT,EAAAA,EAAAA,WAAS,IAGzCU,EAAAA,EAAAA,WAAU,MACRC,EAAAA,EAAAA,OACC,KAEHD,EAAAA,EAAAA,WAAU,KACR,MAAME,EAAeA,KACfC,OAAOC,QAAU,GACnBL,GAAY,GAEZA,GAAY,IAKhB,OADAI,OAAOE,iBAAiB,SAAUH,GAC3B,KACLC,OAAOG,oBAAoB,SAAUJ,KAEtC,KAEHF,EAAAA,EAAAA,WAAU,KACR,MAAMO,EAAUC,SAASC,cAAc,QACvCF,EAAQG,IAAM,UACdH,EAAQI,KAAOC,EAAAA,EACfL,EAAQM,GAAK,QACbL,SAASM,KAAKC,YAAYR,GAE1B,MAAMS,EAAWR,SAASC,cAAc,QAMxC,OALAO,EAASN,IAAM,UACfM,EAASL,KAAOM,EAChBD,EAASH,GAAK,QACdL,SAASM,KAAKC,YAAYC,GAEnB,KACLR,SAASM,KAAKI,YAAYX,GAC1BC,SAASM,KAAKI,YAAYF,KAE3B,KACHhB,EAAAA,EAAAA,WAAU,KACR,MAAMmB,EAAkBnC,EAAMmC,gBAAkBnC,EAAMmC,gBAAmBC,MACnEC,EAAgBrC,EAAMqC,cAAgBrC,EAAMqC,cAAiBD,MA4BnE,GA1BAE,iBACE,IACE,MAAMC,QAAiBC,EAAAA,EAAMC,KAC3B,6CACA,CAAEC,IAAI/D,EAAAA,EAAAA,IAAU,WAChB,CAAEgE,QAAS,CAAE,eAAgB,wCAGzB,KAAEP,GAASG,EAEjB,GAAIH,EAAKQ,QAAS,CAChB,MAAMxC,EAAQyC,OAAOC,OAAOV,EAAKA,MACjC/B,EAASD,GACT+B,EAAgBC,EAAKA,MACrBC,EAAcD,EAAKW,SACrB,MACE1C,EAAS,GAEb,CAAE,MAAO2C,GACPC,QAAQD,MAAM,wBAAyBA,EACzC,CACF,CAEAE,GAEAnD,GAAYtB,EAAAA,EAAAA,KACE,CACZ,IAAIS,EAASa,EAAUb,OACnBE,EAAOW,EAAUX,KACrBF,EAAOiE,GAAG,gBAAkBf,IACtBA,EAAK9C,KAAOF,IACd+B,OAAOZ,SAASoB,KAAO,qBAG3BzC,EAAOiE,GAAG,iBAAmBf,IACvBA,EAAK7C,SAAUZ,EAAAA,EAAAA,IAAU,eAAoByD,EAAK9C,IAAM8C,EAAK9C,KAAOF,IACtE+B,OAAOZ,SAASoB,KAAO,mBAG3BzC,EAAOiE,GAAG,kBAAoBf,IACxBA,EAAK7C,SAAUZ,EAAAA,EAAAA,IAAU,eAAoByD,EAAK9C,IAAM8C,EAAK9C,KAAOF,IACtE+B,OAAOZ,SAASoB,KAAO,YAG7B,GACC,CAAC3B,EAAMmC,gBAAiBnC,EAAMqC,gBAEjC,MAAMe,EAAoBA,KAEtBjC,OAAOZ,SAASoB,KADd1B,EACqB,cAEA,kBAIrBoD,EAAOpD,GAAQA,EAAKqD,SAAW,SAAWrD,EAAKqD,SAAW,GAEhE,IAAIC,EAAiB7D,EAAM8D,QAAU9D,EAAM8D,QAAUH,EAAO,qCAAqCA,EACjG,MAAMI,EAAsB5D,EAAM2D,QAAU3D,EAAM2D,QAAUH,EAAO,uCAAuCA,EAC1GE,EAAiBtD,IAA0B,WAAhBA,EAAKyD,SAAuBC,EAAAA,EAAAA,IAAa1D,IAAUsD,EAAiBE,EAC/F,MAAMG,EAAkBlE,EAAMmE,YAAcnE,EAAMmE,YAAcR,EAAO,iCAAiCA,EAClGS,EAAmBpE,EAAMqE,aAAerE,EAAMqE,aAAeV,EAAO,mCAAmCA,EACvGW,EAAgBtE,EAAMuE,UAAYvE,EAAMuE,UAAYZ,EAAO,+BAA+BA,EAC1Fa,EAAWxE,EAAMyE,KAAOzE,EAAMyE,KAAOd,EAAO,0BAA0BA,EAG5E,IAAIe,EAAc1E,EAAM2E,QAAU3E,EAAM2E,QAAUhB,EAAO,iCAAiCA,EAC1F,MAAMiB,EAAmBzE,EAAMwE,QAAUxE,EAAMwE,QAAUhB,EAAO,uCAAuCA,EACvGe,EAAcnE,IAA0B,WAAhBA,EAAKyD,SAAuBC,EAAAA,EAAAA,IAAa1D,IAAUmE,EAAcE,EACzF,MAAMC,EAAiB7E,EAAM8E,QAAU9E,EAAM8E,QAAUnB,EAAO,kCAAkCA,EAE1FoB,EAAkB/E,EAAMgF,YAAchF,EAAMgF,YAAcrB,EAAO,2CAA2CA,EAC5GsB,EAAcjF,EAAMkF,QAAUlF,EAAMkF,QAAUvB,EAAO,2CAA2CA,EAChGwB,EAAmBnF,EAAMoF,aAAepF,EAAMoF,aAAezB,EAAO,sDAAsDA,EAC1H0B,EAAqBrF,EAAMsF,gBAAkBtF,EAAMsF,gBAAkB3B,EAAO,mDAAmDA,EAC/H4B,EAAWvF,EAAMuF,SAAWvF,EAAMuF,SAAW5B,EAAO,6BAA6BA,EACjF6B,EAAcxF,EAAMyF,QAAUzF,EAAMyF,QAAU9B,EAAO,iCAAiCA,EACtF+B,EAAgB1F,EAAM2F,UAAY3F,EAAM2F,UAAYhC,EAAO,+BAA+BA,EAC1FiC,EAAgB5F,EAAM6F,UAAY7F,EAAM6F,UAAYlC,EAAO,6BAA6BA,EACxFmC,EAAkB9F,EAAM+F,YAAc/F,EAAM+F,YAAcpC,EAAO,iCAAiCA,EAClGqC,EAAehG,EAAMgG,aAAehG,EAAMgG,aAAerC,EAAO,4BAA4BA,EAC5FsC,EAAgBjG,EAAMkG,SAAWlG,EAAMkG,SAAWvC,EAAO,8BAA8BA,EACvFwC,EAAenG,EAAMoG,QAAUpG,EAAMoG,QAAUzC,EAAO,6BAA6BA,EACnF0C,EAAgBrG,EAAMsG,SAAWtG,EAAMsG,SAAW3C,EAAO,4BAA4BA,EACrF4C,EAAgBvG,EAAMwG,SAAWxG,EAAMwG,SAAW7C,EAAO,2BAA2BA,EAIpF8C,EAAwBnH,CAAAA,SAAAA,aAAAA,WAAAA,eAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,cAAAA,EAAAA,kBAAAA,4BAAAA,mBAAAA,yBAAAA,oCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,kCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,2BAAAA,iBAAAA,4BAAAA,cAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,oBAAAA,uBAAAA,yBAAAA,8BAAAA,yBAAAA,gFAAAA,uBAAAA,6CAAAA,sBAAAA,KAAAA,uBAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,wBAAAA,KAAAA,mBAAAA,aAAAA,qCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,mCAAAA,KAAAA,8BAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,4BAAAA,4BAAAA,gCAAAA,oBAAAA,gCAAAA,oBAAAA,gCAAAA,oBAAAA,gCAAAA,oBAAAA,kCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,gCAAAA,KAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,iCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,6BAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,yBAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,iCAAAA,8GAAAA,iCAAAA,8GAAAA,oBAAAA,8jBAAYoH,gCAAkCpH,CAAAA,SAAAA,aAAAA,WAAAA,eAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,cAAAA,EAAAA,kBAAAA,4BAAAA,mBAAAA,yBAAAA,oCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,kCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,2BAAAA,iBAAAA,4BAAAA,cAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,oBAAAA,uBAAAA,yBAAAA,8BAAAA,yBAAAA,gFAAAA,uBAAAA,6CAAAA,sBAAAA,KAAAA,uBAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,wBAAAA,KAAAA,mBAAAA,aAAAA,qCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,mCAAAA,KAAAA,8BAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,4BAAAA,4BAAAA,gCAAAA,oBAAAA,gCAAAA,oBAAAA,gCAAAA,oBAAAA,gCAAAA,oBAAAA,kCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,gCAAAA,KAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,iCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,6BAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,yBAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,iCAAAA,8GAAAA,iCAAAA,8GAAAA,oBAAAA,8jBAAYoH,gCAAkC,IAEnHC,EAAUC,IAAehG,EAAAA,EAAAA,WAAS,GAKnCiG,GAA8BA,KAClCvG,EAAMwG,sBAAqB,IAGvBC,GAAiBC,KACrBC,EAAAA,EAAAA,IAAY1G,EAAMyG,IAGdE,GAAmBF,IAChBG,EAAAA,EAAAA,IAAc5G,EAAMyG,GAGvBI,GAAgBA,CAACC,EAAGL,KACV,UAAVK,EAAEC,KACJP,GAAcC,IAIZO,GAAkBA,KACtB,MAAMC,EAAc,CAAC,gBAAiB,YAAa,WAG7CC,EAAY/G,EAAMgH,KAAMC,GAC5BH,EAAYI,SAAwBD,EAAKE,MAHLC,cAAcC,QAAQ,OAAQ,MAKpE,OAAOC,QAAQP,IAEjB,OACEQ,EAAAA,EAAAA,MAAAC,EAAAA,SAAA,CAAAC,SAAA,CACE1B,GACF2B,EAAAA,EAAAA,KAAA,OAAKxI,GAAG,wBAAwByI,UAAU,mCAAkCF,SAAC,sFAEpEC,EAAAA,EAAAA,KAAAF,EAAAA,SAAA,IACR/G,IACGiH,EAAAA,EAAAA,KAAA,OAAKxI,GAAG,aAAayI,UAAU,qEAAoEF,UACjGC,EAAAA,EAAAA,KAAA,KAAGnG,KAAK,iCAAiCqG,OAAO,SAASD,UAAU,aAAarG,IAAI,aAAYmG,SAAC,iFAGvGC,EAAAA,EAAAA,KAAA,UAAQC,UAAW,+FAA+F5B,GAA0BtF,IAAeC,EAAY,aAAe,IAAK+G,UACzLF,EAAAA,EAAAA,MAAA,OAAKI,UAAU,4EAA2EF,SAAA,EACxFF,EAAAA,EAAAA,MAAA,WAASI,UAAU,YAAWF,SAAA,EAC5BC,EAAAA,EAAAA,KAAA,UACEG,KAAK,aACLC,OAAQjG,EACRkG,MAAM,MACNC,OAAO,KACPL,UAAU,eAEZD,EAAAA,EAAAA,KAAA,OAAKO,IAAKzG,EAAAA,EAAW0G,IAAI,cAAcP,UAAU,iBAEjD7H,GAAWyH,EAAAA,EAAAA,MAAAC,EAAAA,SAAA,CAAAC,SAAA,CACZ5H,EAAO,IAAK6H,EAAAA,EAAAA,KAAA,UAAQ,aAAW,kBAAkBC,UAAU,iHAAiHQ,QAASnF,EAAkByE,SAAC,gBACzMF,EAAAA,EAAAA,MAAA,OAAKI,UAAU,iDAAiDzI,GAAG,OAAMuI,SAAA,CACtEpH,GAAqBG,EACpBR,EAAMoI,IAAI,CAACC,EAAYC,KACrBf,EAAAA,EAAAA,MAAA,OAAKI,UAAU,2BAA0BF,SAAA,EACX,UAAzBY,EAAWE,WAA+C,WAAtBF,EAAW/E,QACtB,QAAzB+E,EAAWE,WAA6C,WAAtBF,EAAW/E,QACpB,aAAzB+E,EAAWE,WAAkD,WAAtBF,EAAW/E,QACzB,WAAzB+E,EAAWE,WAA4C,OAAlB1I,EAAK2I,UAAqD,WAAhCH,EAAWI,kBAAuD,WAAtBJ,EAAW/E,UAEvHoE,EAAAA,EAAAA,KAAA,KAAGnG,KAAK,WAAWmH,SAAU,EAAGC,KAAK,eAAe,aAAW,eAAehB,UAAU,yFAAwFF,SAAC,YAExJ,eAAzBY,EAAWE,WAAoD,WAAtBF,EAAW/E,SAAyBuD,OAC7Ea,EAAAA,EAAAA,KAAA,UAAQC,UAAU,yFAAyFQ,QAASA,IAAMhC,KAA8BsB,SAAC,cAT9Ga,IAahD,IAEHf,EAAAA,EAAAA,MAAA,MAAII,UAAU,2BAA0BF,SAAA,EACtCC,EAAAA,EAAAA,KAAA,MAAIC,UAAU,uBAAsBF,UAClCF,EAAAA,EAAAA,MAAA,OAAKI,UAAU,mBAAmBe,SAAU,EAAEjB,SAAA,EAC5CC,EAAAA,EAAAA,KAAA,MAAAD,UAAIC,EAAAA,EAAAA,KAAA,QAAMC,UAAU,yBAAyBgB,KAAK,cAAc,aAAW,cAAcD,SAAU,EAAEjB,SAAC,mBACtGF,EAAAA,EAAAA,MAAA,MAAII,UAAU,WAAUF,SAAA,EACtBC,EAAAA,EAAAA,KAAA,MAAAD,UACEC,EAAAA,EAAAA,KAACkB,EAAAA,EAAOC,EAAC,CAAClB,UAAU,eAAemB,MAAO,CAACC,OAAO,WAClDJ,KAAK,cAAc,aAAW,cAC9BpH,KAAMiF,GAAgBrC,GACtByD,OAASpB,GAAgBrC,KAAoBA,EAAiB,SAAW,GACzEgE,QAASA,KAAQ9B,GAAclC,IAC/BuE,SAAU,EACVM,UAAYrC,GAAMD,GAAcC,EAAGxC,GAAgBsD,SAClD,mBAYHC,EAAAA,EAAAA,KAAA,MAAAD,UACEF,EAAAA,EAAAA,MAACqB,EAAAA,EAAOC,EAAC,CAACC,MAAO,CAACC,OAAO,WACzBJ,KAAK,YAAY,aAAW,YAC5BpH,KAAMiF,GAAgBxB,GACtB4C,OAASpB,GAAgBxB,KAAmBA,EAAgB,SAAW,GACvEmD,QAASA,KAAQ9B,GAAcrB,IAC/B0D,SAAU,EACVM,UAAYrC,GAAMD,GAAcC,EAAG3B,GAAeyC,SAAA,CACjD,eAAWC,EAAAA,EAAAA,KAAA,QAAMC,UAAU,wGAAuGF,SAAC,gBAEtIC,EAAAA,EAAAA,KAAA,MAAAD,UACEC,EAAAA,EAAAA,KAACkB,EAAAA,EAAOC,EAAC,CAACC,MAAO,CAACC,OAAO,WACzBJ,KAAK,aAAa,aAAW,aAC7BpH,KAAMiF,GAAgB7B,GACtBiD,OAASpB,GAAgB7B,KAAwBA,EAAqB,SAAW,GACjFwD,QAASA,KAAQ9B,GAAc1B,IAC/B+D,SAAU,EACVM,UAAYrC,GAAMD,GAAcC,EAAGhC,GAAoB8C,SACtD,kBAEHC,EAAAA,EAAAA,KAAA,MAAAD,UACEC,EAAAA,EAAAA,KAACkB,EAAAA,EAAOC,EAAC,CAACC,MAAO,CAACC,OAAO,WACzBJ,KAAK,eAAe,aAAW,eAC/BpH,KAAMiF,GAAgBnC,GACtBuD,OAASpB,GAAgBnC,KAAqBA,EAAkB,SAAW,GAC3E8D,QAASA,KAAQ9B,GAAchC,IAC/BqE,SAAU,EACVM,UAAYrC,GAAMD,GAAcC,EAAGtC,GAAiBoD,SACnD,oBAEHC,EAAAA,EAAAA,KAAA,MAAAD,UACEC,EAAAA,EAAAA,KAACkB,EAAAA,EAAOC,EAAC,CAACC,MAAO,CAACC,OAAO,WACzBJ,KAAK,WAAW,aAAW,WAC3BpH,KAAMiF,GAAgBjC,GACtBqD,OAASpB,GAAgBjC,KAAiBA,EAAc,SAAW,GACnE4D,QAASA,KAAQ9B,GAAc9B,IAC/BmE,SAAU,EACVM,UAAYrC,GAAMD,GAAcC,EAAGpC,GAAakD,SAC/C,gBAEHC,EAAAA,EAAAA,KAAA,MAAAD,UACEC,EAAAA,EAAAA,KAACkB,EAAAA,EAAOC,EAAC,CAACC,MAAO,CAACC,OAAO,WACzBJ,KAAK,eAAe,aAAW,eAC/BpH,KAAMiF,GAAgB/B,GACtBmD,OAASpB,GAAgB/B,KAAsBA,EAAmB,SAAW,GAC7E0D,QAASA,KAAQ9B,GAAc5B,IAC/BiE,SAAU,EACVM,UAAYrC,GAAMD,GAAcC,EAAGlC,GAAkBgD,SACpD,oBAEHC,EAAAA,EAAAA,KAAA,MAAAD,UACEC,EAAAA,EAAAA,KAACkB,EAAAA,EAAOC,EAAC,CAACC,MAAO,CAACC,OAAO,WACzBJ,KAAK,YAAY,aAAW,YAC5BpH,KAAMiF,GAAgBtB,GACtB0C,OAASpB,GAAgBtB,KAAmBA,EAAgB,SAAW,GACvEiD,QAASA,KAAQ9B,GAAcnB,IAC/BwD,SAAU,EACVM,UAAYrC,GAAMD,GAAcC,EAAGzB,GAAeuC,SACjD,iBAEHC,EAAAA,EAAAA,KAAA,MAAAD,UACEC,EAAAA,EAAAA,KAACkB,EAAAA,EAAOC,EAAC,CAACC,MAAO,CAACC,OAAO,WACzBJ,KAAK,WAAW,aAAW,WAC3BpH,KAAMiF,GAAgBlB,GACtBsC,OAASpB,GAAgBlB,KAAkBA,EAAe,SAAW,GACrE6C,QAASA,KAAQ9B,GAAcf,IAC/BoD,SAAU,EACVM,UAAYrC,GAAMD,GAAcC,EAAGrB,GAAcmC,SAChD,gBAEHC,EAAAA,EAAAA,KAAA,MAAAD,UACEC,EAAAA,EAAAA,KAACkB,EAAAA,EAAOC,EAAC,CAACC,MAAO,CAACC,OAAO,WACzBJ,KAAK,aAAa,aAAW,aAC7BpH,KAAMiF,GAAgBjB,GACtBqC,OAASpB,GAAgBjB,KAAmBA,EAAgB,SAAW,GACvE4C,QAASA,KAAQ9B,GAAcd,IAC/BmD,SAAU,EACVM,UAAYrC,GAAMD,GAAcC,EAAGpB,GAAekC,SACjD,kBAEHC,EAAAA,EAAAA,KAAA,MAAAD,UACEC,EAAAA,EAAAA,KAACkB,EAAAA,EAAOC,EAAC,CAACC,MAAO,CAACC,OAAO,WACzBJ,KAAK,UAAU,aAAW,UAC1BpH,KAAMiF,GAAgBf,GACtBmC,OAASpB,GAAgBf,KAAkBA,EAAe,SAAW,GACrE0C,QAASA,KAAQ9B,GAAcZ,IAC/BiD,SAAU,EACVM,UAAYrC,GAAMD,GAAcC,EAAGlB,GAAcgC,SAChD,eAEHC,EAAAA,EAAAA,KAAA,MAAAD,UACEC,EAAAA,EAAAA,KAACkB,EAAAA,EAAOC,EAAC,CAACC,MAAO,CAACC,OAAO,WACzBJ,KAAK,UAAU,aAAW,UAC1BpH,KAAMiF,GAAgBxC,GACtB4D,OAASpB,GAAgBxC,KAAiBA,EAAc,SAAW,GACnEmE,QAASA,KAAQ9B,GAAcrC,IAC/B0E,SAAU,EACVM,UAAYrC,GAAMD,GAAcC,EAAG3C,GAAayD,SAC/C,eAEHC,EAAAA,EAAAA,KAAA,MAAAD,UACEC,EAAAA,EAAAA,KAACkB,EAAAA,EAAOC,EAAC,CAAClB,UAAU,eAAemB,MAAO,CAACC,OAAO,WAClDJ,KAAK,WAAW,aAAW,WAC3BpH,KAAMiF,GAAgB1B,GACtB8C,OAASpB,GAAgB1B,KAAiBA,EAAc,SAAW,GACnEqD,QAASA,KAAQ9B,GAAcvB,IAC/B4D,SAAU,EACVM,UAAYrC,GAAMD,GAAcC,EAAG7B,GAAa2C,SAC/C,gBAEHC,EAAAA,EAAAA,KAAA,MAAAD,UACEC,EAAAA,EAAAA,KAACkB,EAAAA,EAAOC,EAAC,CAACC,MAAO,CAACC,OAAO,WACzBJ,KAAK,YAAY,aAAW,YAC5BpH,KAAMiF,GAAgBb,GACtBiC,OAASpB,GAAgBb,KAAmBA,EAAgB,SAAW,GACvEwC,QAASA,KAAQ9B,GAAcV,IAC/B+C,SAAU,EACVM,UAAYrC,GAAMD,GAAcC,EAAGhB,GAAe8B,SACjD,iBAEHC,EAAAA,EAAAA,KAAA,MAAAD,UACEC,EAAAA,EAAAA,KAACkB,EAAAA,EAAOC,EAAC,CAACC,MAAO,CAACC,OAAO,WACzBJ,KAAK,gBAAgB,aAAW,gBAChCpH,KAAMiF,GAAgBX,GACtB+B,OAASpB,GAAgBX,KAAmBA,EAAgB,SAAW,GACvEsC,QAASA,KAAQ9B,GAAcR,IAC/B6C,SAAU,EACVM,UAAYrC,GAAMD,GAAcC,EAAGd,GAAe4B,SACjD,qBAEHC,EAAAA,EAAAA,KAAA,MAAAD,UAAIC,EAAAA,EAAAA,KAAA,KAAGnG,KAAK,cAAcoH,KAAK,aAAa,aAAW,aAAaD,SAAU,EAAEjB,SAAC,wBAIvFC,EAAAA,EAAAA,KAAA,MAAIC,UAAU,uBAAsBF,UAClCF,EAAAA,EAAAA,MAAA,OAAKI,UAAU,mBAAkBF,SAAA,EAC/BC,EAAAA,EAAAA,KAAA,QAAMC,UAAU,YAAYgB,KAAK,SAAS,aAAW,SAASD,SAAU,EAAEjB,SAAC,YAC3EF,EAAAA,EAAAA,MAAA,MAAII,UAAU,WAAUF,SAAA,EACtBC,EAAAA,EAAAA,KAAA,MAAAD,UACEC,EAAAA,EAAAA,KAACkB,EAAAA,EAAOC,EAAC,CAACC,MAAO,CAACC,OAAO,WACzBJ,KAAK,aAAa,aAAW,aAC7BpH,KAAMiF,GAAgBrD,GACtByE,OAASpB,GAAgBrD,KAAoBA,EAAiB,SAAW,GACzEgF,QAASA,KAAQ9B,GAAclD,IAC/BuF,SAAU,EACVM,UAAYrC,GAAMD,GAAcC,EAAGxD,GAAgBsE,SAClD,kBAEHC,EAAAA,EAAAA,KAAA,MAAAD,UACEC,EAAAA,EAAAA,KAACkB,EAAAA,EAAOC,EAAC,CAACC,MAAO,CAACC,OAAO,WACzBJ,KAAK,cAAc,aAAW,cAC9BpH,KAAMiF,GAAgBhD,GACtBoE,OAASpB,GAAgBhD,KAAqBA,EAAkB,SAAW,GAC3E2E,QAASA,KAAQ9B,GAAc7C,IAC/BkF,SAAU,EACVM,UAAYrC,GAAMD,GAAcC,EAAGnD,GAAiBiE,SACnD,mBAEHC,EAAAA,EAAAA,KAAA,MAAAD,UACEC,EAAAA,EAAAA,KAACkB,EAAAA,EAAOC,EAAC,CAACC,MAAO,CAACC,OAAO,WACzBJ,KAAK,gBAAgB,aAAW,gBAChCpH,KAAMiF,GAAgB9C,GACtBkE,OAASpB,GAAgB9C,KAAsBA,EAAmB,SAAW,GAC7EyE,QAASA,KAAQ9B,GAAc3C,IAC/BgF,SAAU,EACVM,UAAYrC,GAAMD,GAAcC,EAAGjD,GAAkB+D,SACpD,qBAEHC,EAAAA,EAAAA,KAAA,MAAAD,UACEC,EAAAA,EAAAA,KAACkB,EAAAA,EAAOC,EAAC,CAACC,MAAO,CAACC,OAAO,WACzBJ,KAAK,oBAAoB,aAAW,oBACpCpH,KAAMiF,GAAgB3B,GACtB+C,OAASpB,GAAgB3B,KAAcA,EAAW,SAAW,GAC7DsD,QAASA,KAAQ9B,GAAcxB,IAC/B6D,SAAU,EACVM,UAAYrC,GAAMD,GAAcC,EAAG9B,GAAU4C,SAC5C,yBAEHC,EAAAA,EAAAA,KAAA,MAAAD,UACEC,EAAAA,EAAAA,KAACkB,EAAAA,EAAOC,EAAC,CAACC,MAAO,CAACC,OAAO,WACzBJ,KAAK,eAAe,aAAW,eAC/BpH,KAAMiF,GAAgBpB,GACtBwC,OAASpB,GAAgBpB,KAAqBA,EAAkB,SAAW,GAC3E+C,QAASA,KAAQ9B,GAAcjB,IAC/BsD,SAAU,EACVM,UAAYrC,GAAMD,GAAcC,EAAGvB,GAAiBqC,SACnD,oBAEHC,EAAAA,EAAAA,KAAA,MAAAD,UACEC,EAAAA,EAAAA,KAACkB,EAAAA,EAAOC,EAAC,CAACC,MAAO,CAACC,OAAO,WACzBJ,KAAK,YAAY,aAAW,YAC5BpH,KAAMiF,GAAgB5C,GACtBgE,OAASpB,GAAgB5C,KAAmBA,EAAgB,SAAW,GACvEuE,QAASA,KAAQ9B,GAAczC,IAC/B8E,SAAU,EACVM,UAAYrC,GAAMD,GAAcC,EAAG/C,GAAe6D,SACjD,iBAEHC,EAAAA,EAAAA,KAAA,MAAAD,UACEC,EAAAA,EAAAA,KAACkB,EAAAA,EAAOC,EAAC,CAACC,MAAO,CAACC,OAAO,WACzBJ,KAAK,gBAAgB,aAAW,gBAChCpH,KAAMiF,GAAgB1C,GACtB8D,OAASpB,GAAgB1C,KAAcA,EAAW,SAAW,GAC7DqE,QAASA,KAAQ9B,GAAcvC,IAC/B4E,SAAU,EACVM,UAAYrC,GAAMD,GAAcC,EAAG7C,GAAU2D,SAC5C,6BAKTC,EAAAA,EAAAA,KAAA,MAAIC,UAAU,uBAAsBF,UAACC,EAAAA,EAAAA,KAAA,KAAGnG,KAAK,gCAAgCoH,KAAK,aAAa,aAAW,aAAaD,SAAU,EAAEjB,SAAC,iBAClI5H,GAAwB,WAAhBA,EAAKyD,OAAkJ,IAA5HoE,EAAAA,EAAAA,KAAA,MAAIC,UAAU,uBAAsBF,UAACC,EAAAA,EAAAA,KAAA,KAAGnG,KAAK,WAAWoH,KAAK,UAAU,aAAW,UAAUD,SAAU,EAAEjB,SAAC,cAC7I5H,GAAO6H,EAAAA,EAAAA,KAAA,MAAIC,UAAU,uBAAsBF,UAACC,EAAAA,EAAAA,KAAA,KAAGnG,KAAK,cAAcoH,KAAK,SAAS,aAAW,SAASD,SAAU,EAAEjB,SAAC,cAAmB,GACpI5H,GAAO6H,EAAAA,EAAAA,KAAA,MAAIC,UAAU,uBAAsBF,UAACC,EAAAA,EAAAA,KAAA,KAAGnG,KAAK,kBAAkBoH,KAAK,iBAAiB,aAAW,iBAAiBD,SAAU,EAAEjB,SAAC,sBAA0BC,EAAAA,EAAAA,KAAA,MAAIC,UAAU,uBAAsBF,UAACC,EAAAA,EAAAA,KAAA,KAAGnG,KAAK,SAASoH,KAAK,UAAU,aAAW,UAAUD,SAAU,EAAEjB,SAAC,iBAExQ5H,EAAO,IAAK6H,EAAAA,EAAAA,KAAA,UAAQC,UAAU,iHAAiHQ,QAASnF,EAAmB2F,KAAK,aAAa,aAAW,kBAAkBD,SAAU,EAAEjB,SAAC,kBAEzOpH,GAAqBG,EACpBR,EAAMoI,IAAI,CAACC,EAAYC,KACrBf,EAAAA,EAAAA,MAAA,OAAKI,UAAW,wEAAuE1B,EAAW,SAAW,IAAKwB,SAAA,EACpF,UAAzBY,EAAWE,WAA+C,WAAtBF,EAAW/E,QACtB,QAAzB+E,EAAWE,WAAuD,YAAhCF,EAAWI,kBAAwD,WAAtBJ,EAAW/E,UAC3FoE,EAAAA,EAAAA,KAAA,KAAGnG,KAAK,WAAWoG,UAAU,gCAAgCgB,KAAK,UAAU,aAAW,UAASlB,UAACC,EAAAA,EAAAA,KAACuB,EAAAA,IAAgB,CAACtB,UAAU,yCAEpG,eAAzBU,EAAWE,WAAoD,WAAtBF,EAAW/E,SACpDoE,EAAAA,EAAAA,KAAA,UAAQC,UAAU,mEAAmEQ,QAASA,IAAMhC,KAA8BsB,UAACC,EAAAA,EAAAA,KAACuB,EAAAA,IAAgB,CAACtB,UAAU,2CAN3CW,IAWzH,IACHf,EAAAA,EAAAA,MAAA,UACE,aAAW,iBACXI,UAAW,6BAA6B1B,EAAW,SAAW,MAAMpG,EAAO,SAAW,KACtFsI,QA9UWe,KACjBhD,GAAaD,IA6UawB,SAAA,EAEpBC,EAAAA,EAAAA,KAAA,QAAMC,UAAU,oBAChBD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,oBAChBD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,uBAGlBD,EAAAA,EAAAA,KAAA,OAAKC,UAAW,iCAAgC1B,EAAW,GAAK,UAAY/G,GAAG,OAAMuI,UACnFC,EAAAA,EAAAA,KAAA,OAAKC,UAAU,iBAAgBF,UAC7BF,EAAAA,EAAAA,MAAA,MAAII,UAAU,mDAAkDF,SAAA,EAC9DC,EAAAA,EAAAA,KAAA,MAAIC,UAAU,gDAA+CF,UAC3DF,EAAAA,EAAAA,MAAA,OAAKI,UAAU,UAAUe,SAAU,EAAEjB,SAAA,EACnCC,EAAAA,EAAAA,KAAA,MAAAD,UAAIC,EAAAA,EAAAA,KAAA,QAAMC,UAAU,mCAAmCgB,KAAK,cAAc,aAAW,cAAcD,SAAU,EAAEjB,SAAC,mBAChHF,EAAAA,EAAAA,MAAA,MAAII,UAAU,mDAAkDF,SAAA,EAC9DC,EAAAA,EAAAA,KAAA,MAAAD,UACEC,EAAAA,EAAAA,KAACkB,EAAAA,EAAOC,EAAC,CAAClB,UAAU,eAAemB,MAAO,CAACC,OAAO,WAClDJ,KAAK,cAAc,aAAW,cAC9BpH,KAAMiF,GAAgBrC,GACtByD,OAASpB,GAAgBrC,KAAoBA,EAAiB,SAAW,GACzEgE,QAASA,KAAQ9B,GAAclC,IAC/BuE,SAAU,EACVM,UAAYrC,GAAMD,GAAcC,EAAGxC,GAAgBsD,SAClD,mBAYHC,EAAAA,EAAAA,KAAA,MAAAD,UACEF,EAAAA,EAAAA,MAACqB,EAAAA,EAAOC,EAAC,CAACC,MAAO,CAACC,OAAO,WACzBJ,KAAK,YAAY,aAAW,YAC5BpH,KAAMiF,GAAgBxB,GACtB4C,OAASpB,GAAgBxB,KAAmBA,EAAgB,SAAW,GACvEmD,QAASA,KAAQ9B,GAAcrB,IAC/B0D,SAAU,EACVM,UAAYrC,GAAMD,GAAcC,EAAG3B,GAAeyC,SAAA,CACjD,eAAWC,EAAAA,EAAAA,KAAA,QAAMC,UAAU,wGAAuGF,SAAC,gBAEtIC,EAAAA,EAAAA,KAAA,MAAAD,UACEC,EAAAA,EAAAA,KAACkB,EAAAA,EAAOC,EAAC,CAAClB,UAAU,eAAemB,MAAO,CAACC,OAAO,WAClDJ,KAAK,aAAa,aAAW,aAC7BpH,KAAMiF,GAAgB7B,GACtBiD,OAASpB,GAAgB7B,KAAwBA,EAAqB,SAAW,GACjFwD,QAASA,KAAQ9B,GAAc1B,IAC/B+D,SAAU,EACVM,UAAYrC,GAAMD,GAAcC,EAAGhC,GAAoB8C,SACtD,kBAEHC,EAAAA,EAAAA,KAAA,MAAAD,UACEC,EAAAA,EAAAA,KAACkB,EAAAA,EAAOC,EAAC,CAAClB,UAAU,eAAemB,MAAO,CAACC,OAAO,WAClDJ,KAAK,eAAe,aAAW,eAC/BpH,KAAMiF,GAAgBnC,GACtBuD,OAASpB,GAAgBnC,KAAqBA,EAAkB,SAAW,GAC3E8D,QAASA,KAAQ9B,GAAchC,IAC/BqE,SAAU,EACVM,UAAYrC,GAAMD,GAAcC,EAAGtC,GAAiBoD,SACnD,oBAEHC,EAAAA,EAAAA,KAAA,MAAAD,UACEC,EAAAA,EAAAA,KAACkB,EAAAA,EAAOC,EAAC,CAAClB,UAAU,eAAemB,MAAO,CAACC,OAAO,WAClDJ,KAAK,WAAW,aAAW,WAC3BpH,KAAMiF,GAAgBjC,GACtBqD,OAASpB,GAAgBjC,KAAiBA,EAAc,SAAW,GACnE4D,QAASA,KAAQ9B,GAAc9B,IAC/BmE,SAAU,EACVM,UAAYrC,GAAMD,GAAcC,EAAGpC,GAAakD,SAC/C,gBAEHC,EAAAA,EAAAA,KAAA,MAAAD,UACEC,EAAAA,EAAAA,KAACkB,EAAAA,EAAOC,EAAC,CAAClB,UAAU,eAAemB,MAAO,CAACC,OAAO,WAClDJ,KAAK,eAAe,aAAW,eAC/BpH,KAAMiF,GAAgB/B,GACtBmD,OAASpB,GAAgB/B,KAAsBA,EAAmB,SAAW,GAC7E0D,QAASA,KAAQ9B,GAAc5B,IAC/BiE,SAAU,EACVM,UAAYrC,GAAMD,GAAcC,EAAGlC,GAAkBgD,SACpD,oBAEHC,EAAAA,EAAAA,KAAA,MAAAD,UACEC,EAAAA,EAAAA,KAACkB,EAAAA,EAAOC,EAAC,CAAClB,UAAU,eAAemB,MAAO,CAACC,OAAO,WAClDJ,KAAK,YAAY,aAAW,YAC5BpH,KAAMiF,GAAgBtB,GACtB0C,OAASpB,GAAgBtB,KAAmBA,EAAgB,SAAW,GACvEiD,QAASA,KAAQ9B,GAAcnB,IAC/BwD,SAAU,EACVM,UAAYrC,GAAMD,GAAcC,EAAGzB,GAAeuC,SACjD,iBAEHC,EAAAA,EAAAA,KAAA,MAAAD,UACEC,EAAAA,EAAAA,KAACkB,EAAAA,EAAOC,EAAC,CAAClB,UAAU,eAAemB,MAAO,CAACC,OAAO,WAClDJ,KAAK,WAAW,aAAW,WAC3BpH,KAAMiF,GAAgBlB,GACtBsC,OAASpB,GAAgBlB,KAAkBA,EAAe,SAAW,GACrE6C,QAASA,KAAQ9B,GAAcf,IAC/BoD,SAAU,EACVM,UAAYrC,GAAMD,GAAcC,EAAGrB,GAAcmC,SAChD,gBAEHC,EAAAA,EAAAA,KAAA,MAAAD,UACEC,EAAAA,EAAAA,KAACkB,EAAAA,EAAOC,EAAC,CAAClB,UAAU,eAAemB,MAAO,CAACC,OAAO,WAClDJ,KAAK,aAAa,aAAW,aAC7BpH,KAAMiF,GAAgBjB,GACtBqC,OAASpB,GAAgBjB,KAAmBA,EAAgB,SAAW,GACvE4C,QAASA,KAAQ9B,GAAcd,IAC/BmD,SAAU,EACVM,UAAYrC,GAAMD,GAAcC,EAAGpB,GAAekC,SACjD,kBAEHC,EAAAA,EAAAA,KAAA,MAAAD,UACEC,EAAAA,EAAAA,KAACkB,EAAAA,EAAOC,EAAC,CAAClB,UAAU,eAAemB,MAAO,CAACC,OAAO,WAClDJ,KAAK,UAAU,aAAW,UAC1BpH,KAAMiF,GAAgBf,GACtBmC,OAASpB,GAAgBf,KAAkBA,EAAe,SAAW,GACrE0C,QAASA,KAAQ9B,GAAcZ,IAC/BiD,SAAU,EACVM,UAAYrC,GAAMD,GAAcC,EAAGlB,GAAcgC,SAChD,eAEHC,EAAAA,EAAAA,KAAA,MAAAD,UACEC,EAAAA,EAAAA,KAACkB,EAAAA,EAAOC,EAAC,CAAClB,UAAU,eAAemB,MAAO,CAACC,OAAO,WAClDJ,KAAK,UAAU,aAAW,UAC1BpH,KAAMiF,GAAgBxC,GACtB4D,OAASpB,GAAgBxC,KAAiBA,EAAc,SAAW,GACnEmE,QAASA,KAAQ9B,GAAcrC,IAC/B0E,SAAU,EACVM,UAAYrC,GAAMD,GAAcC,EAAG3C,GAAayD,SAC/C,eAEHC,EAAAA,EAAAA,KAAA,MAAAD,UACEC,EAAAA,EAAAA,KAACkB,EAAAA,EAAOC,EAAC,CAAClB,UAAU,eAAemB,MAAO,CAACC,OAAO,WAClDJ,KAAK,WAAW,aAAW,WAC3BpH,KAAMiF,GAAgB1B,GACtB8C,OAASpB,GAAgB1B,KAAiBA,EAAc,SAAW,GACnEqD,QAASA,KAAQ9B,GAAcvB,IAC/B4D,SAAU,EACVM,UAAYrC,GAAMD,GAAcC,EAAG7B,GAAa2C,SAC/C,gBAEHC,EAAAA,EAAAA,KAAA,MAAAD,UACEC,EAAAA,EAAAA,KAACkB,EAAAA,EAAOC,EAAC,CAACC,MAAO,CAACC,OAAO,WACzBJ,KAAK,YAAY,aAAW,YAC5BpH,KAAMiF,GAAgBb,GACtBiC,OAASpB,GAAgBb,KAAmBA,EAAgB,SAAW,GACvEwC,QAASA,KAAQ9B,GAAcV,IAC/B+C,SAAU,EACVM,UAAYrC,GAAMD,GAAcC,EAAGhB,GAAe8B,SACjD,iBAEHC,EAAAA,EAAAA,KAAA,MAAAD,UACEC,EAAAA,EAAAA,KAACkB,EAAAA,EAAOC,EAAC,CAACC,MAAO,CAACC,OAAO,WACzBJ,KAAK,gBAAgB,aAAW,gBAChCpH,KAAMiF,GAAgBX,GACtB+B,OAASpB,GAAgBX,KAAmBA,EAAgB,SAAW,GACvEsC,QAASA,KAAQ9B,GAAcR,IAC/B6C,SAAU,EACVM,UAAYrC,GAAMD,GAAcC,EAAGd,GAAe4B,SACjD,qBAEHC,EAAAA,EAAAA,KAAA,MAAAD,UAAIC,EAAAA,EAAAA,KAAA,KAAGnG,KAAK,cAAcoG,UAAU,eAAee,SAAU,EAAEjB,SAAC,wBAItEC,EAAAA,EAAAA,KAAA,MAAIC,UAAU,gDAA+CF,UAC3DF,EAAAA,EAAAA,MAAA,OAAKI,UAAU,UAASF,SAAA,EACtBC,EAAAA,EAAAA,KAAA,QAAMC,UAAU,mCAAmCgB,KAAK,SAAS,aAAW,SAASD,SAAU,EAAEjB,SAAC,YAClGF,EAAAA,EAAAA,MAAA,MAAII,UAAU,mDAAkDF,SAAA,EAC9DC,EAAAA,EAAAA,KAAA,MAAAD,UACEC,EAAAA,EAAAA,KAACkB,EAAAA,EAAOC,EAAC,CAAClB,UAAU,eAAemB,MAAO,CAACC,OAAO,WAClDJ,KAAK,aAAa,aAAW,aAC7BpH,KAAMiF,GAAgBrD,GACtByE,OAASpB,GAAgBrD,KAAoBA,EAAiB,SAAW,GACzEgF,QAASA,KAAQ9B,GAAclD,IAC/BuF,SAAU,EACVM,UAAYrC,GAAMD,GAAcC,EAAGxD,GAAgBsE,SAClD,kBAEHC,EAAAA,EAAAA,KAAA,MAAAD,UACEC,EAAAA,EAAAA,KAACkB,EAAAA,EAAOC,EAAC,CAAClB,UAAU,eAAemB,MAAO,CAACC,OAAO,WAClDJ,KAAK,cAAc,aAAW,cAC9BpH,KAAMiF,GAAgBhD,GACtBoE,OAASpB,GAAgBhD,KAAqBA,EAAkB,SAAW,GAC3E2E,QAASA,KAAQ9B,GAAc7C,IAC/BkF,SAAU,EACVM,UAAYrC,GAAMD,GAAcC,EAAGnD,GAAiBiE,SACnD,mBAEHC,EAAAA,EAAAA,KAAA,MAAAD,UACEC,EAAAA,EAAAA,KAACkB,EAAAA,EAAOC,EAAC,CAAClB,UAAU,eAAemB,MAAO,CAACC,OAAO,WAClDJ,KAAK,gBAAgB,aAAW,gBAChCpH,KAAMiF,GAAgB9C,GACtBkE,OAASpB,GAAgB9C,KAAsBA,EAAmB,SAAW,GAC7EyE,QAASA,KAAQ9B,GAAc3C,IAC/BgF,SAAU,EACVM,UAAYrC,GAAMD,GAAcC,EAAGjD,GAAkB+D,SACpD,qBAEHC,EAAAA,EAAAA,KAAA,MAAAD,UACAC,EAAAA,EAAAA,KAACkB,EAAAA,EAAOC,EAAC,CAAClB,UAAU,eAAemB,MAAO,CAACC,OAAO,WAChDJ,KAAK,oBAAoB,aAAW,oBACpCpH,KAAMiF,GAAgB3B,GACtB+C,OAASpB,GAAgB3B,KAAcA,EAAW,SAAW,GAC7DsD,QAASA,KAAQ9B,GAAcxB,IAC/B6D,SAAU,EACVM,UAAYrC,GAAMD,GAAcC,EAAG9B,GAAU4C,SAC5C,yBAEHC,EAAAA,EAAAA,KAAA,MAAAD,UACEC,EAAAA,EAAAA,KAACkB,EAAAA,EAAOC,EAAC,CAACC,MAAO,CAACC,OAAO,WACzBJ,KAAK,eAAe,aAAW,eAC/BpH,KAAMiF,GAAgBpB,GACtBwC,OAASpB,GAAgBpB,KAAqBA,EAAkB,SAAW,GAC3E+C,QAASA,KAAQ9B,GAAcjB,IAC/BsD,SAAU,EACVM,UAAYrC,GAAMD,GAAcC,EAAGvB,GAAiBqC,SACnD,oBAEHC,EAAAA,EAAAA,KAAA,MAAAD,UACEC,EAAAA,EAAAA,KAACkB,EAAAA,EAAOC,EAAC,CAAClB,UAAU,eAAemB,MAAO,CAACC,OAAO,WAClDJ,KAAK,YAAY,aAAW,YAC5BpH,KAAMiF,GAAgB5C,GACtBgE,OAASpB,GAAgB5C,KAAmBA,EAAgB,SAAW,GACvEuE,QAASA,KAAQ9B,GAAczC,IAC/B8E,SAAU,EACVM,UAAYrC,GAAMD,GAAcC,EAAG/C,GAAe6D,SACjD,iBAEHC,EAAAA,EAAAA,KAAA,MAAAD,UACAC,EAAAA,EAAAA,KAACkB,EAAAA,EAAOC,EAAC,CAACC,MAAO,CAACC,OAAO,WACzBJ,KAAK,gBAAgB,aAAW,gBAChCpH,KAAMiF,GAAgB1C,GACtB8D,OAASpB,GAAgB1C,KAAcA,EAAW,SAAW,GAC7DqE,QAASA,KAAQ9B,GAAcvC,IAC/B4E,SAAU,EACVM,UAAYrC,GAAMD,GAAcC,EAAG7C,GAAU2D,SAC5C,4BAMN5H,GACC0H,EAAAA,EAAAA,MAAAC,EAAAA,SAAA,CAAAC,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIC,UAAU,gDAA+CF,UAC3DC,EAAAA,EAAAA,KAAA,KAAGnG,KAAK,gCAAgCoG,UAAU,yBAAyBgB,KAAK,aAAa,aAAW,aAAaD,SAAU,EAAEjB,SAAC,kBAEpIC,EAAAA,EAAAA,KAAA,MAAIC,UAAU,gDAA+CF,UAC3DC,EAAAA,EAAAA,KAAA,KAAGnG,KAAK,cAAcoG,UAAU,yBAAyBgB,KAAK,UAAU,aAAW,UAAUD,SAAU,EAAEjB,SAAC,eAE5GC,EAAAA,EAAAA,KAAA,MAAIC,UAAU,gDAA+CF,UAC3DC,EAAAA,EAAAA,KAAA,KAAGnG,KAAK,kBAAkBoG,UAAU,yBAAyBgB,KAAK,iBAAiB,aAAW,iBAAiBD,SAAU,EAAEjB,SAAC,yBAIhIF,EAAAA,EAAAA,MAAAC,EAAAA,SAAA,CAAAC,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIC,UAAU,gDAA+CF,UAC3DC,EAAAA,EAAAA,KAAA,KAAGnG,KAAK,gCAAgCoG,UAAU,yBAAyBgB,KAAK,aAAa,aAAW,aAAaD,SAAU,EAAEjB,SAAC,kBAEpIC,EAAAA,EAAAA,KAAA,MAAIC,UAAU,gDAA+CF,UAC3DC,EAAAA,EAAAA,KAAA,KAAGnG,KAAK,WAAWoG,UAAU,yBAAyBgB,KAAK,UAAU,aAAW,UAAUD,SAAU,EAAEjB,SAAC,eAEzGC,EAAAA,EAAAA,KAAA,MAAIC,UAAU,gDAA+CF,UAC3DC,EAAAA,EAAAA,KAAA,KAAGnG,KAAK,SAASoG,UAAU,yBAAyBgB,KAAK,UAAU,aAAW,UAAUD,SAAU,EAAEjB,SAAC,cAEtG5H,EAAO,IAAK6H,EAAAA,EAAAA,KAAA,UAAQ,aAAW,kBAAkBC,UAAU,oGAAoGe,SAAU,EAAGP,QAASnF,EAAkByE,SAAC,6BAM7MC,EAAAA,EAAAA,KAAAF,EAAAA,SAAA,WAKd,C", "sources": ["assets/images/AIPRO.svg", "core/utils/socket.jsx", "header/index.jsx"], "sourcesContent": ["var _g, _defs;\nfunction _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\nimport * as React from \"react\";\nfunction SvgAipro(_ref, svgRef) {\n  let {\n    title,\n    titleId,\n    ...props\n  } = _ref;\n  return /*#__PURE__*/React.createElement(\"svg\", _extends({\n    width: 451,\n    height: 157,\n    viewBox: \"0 0 451 157\",\n    fill: \"none\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, _g || (_g = /*#__PURE__*/React.createElement(\"g\", {\n    clipPath: \"url(#clip0_2054_286)\"\n  }, /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M92.717 79.8955C91.7079 79.898 90.8221 79.5666 90.0844 78.8815C89.438 78.2806 88.7889 77.6721 88.2042 77.0144C87.6096 76.3467 86.6769 76.3763 86.107 77.0193C85.6035 77.5881 85.0483 78.1124 84.5007 78.6392C83.2546 79.8361 81.7939 80.17 80.1927 79.5345C78.5839 78.8964 77.7647 77.6277 77.6759 75.9088C77.5969 74.3657 78.5568 72.8595 79.9853 72.1993C81.4707 71.5118 83.1683 71.7739 84.3896 72.909C85.0064 73.4827 85.6159 74.0689 86.1736 74.6996C86.7732 75.3747 87.6441 75.4266 88.3054 74.697C88.8433 74.1034 89.4207 73.5446 90.0029 72.9931C91.1724 71.8876 92.9095 71.5982 94.3554 72.2437C95.8358 72.9065 96.7735 74.4077 96.7265 76.0424C96.6649 78.1989 94.8908 79.9029 92.717 79.898V79.8955Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M67.7897 52.4641C66.3315 52.4716 65.4408 52.1476 64.703 51.4625C64.0418 50.8467 63.4125 50.1963 62.7685 49.5607C61.9938 48.7965 61.3917 48.799 60.6096 49.5805C60.0273 50.1592 59.4575 50.7528 58.8652 51.324C57.6735 52.479 55.9735 52.7807 54.4807 52.1204C52.9607 51.4453 52.0034 49.9465 52.1022 48.2797C52.2008 46.6375 53.0151 45.4208 54.5324 44.7876C56.0894 44.1372 57.555 44.4093 58.8159 45.5469C59.4253 46.0959 60.0125 46.6771 60.5579 47.2904C61.2882 48.114 62.1296 48.1486 62.9068 47.2706C63.4373 46.6722 64.0196 46.1182 64.6019 45.5667C65.7887 44.4414 67.5455 44.1595 69.0036 44.8371C70.4841 45.5246 71.3971 47.0258 71.3305 48.6605C71.2441 50.7874 69.4824 52.4667 67.7873 52.4667L67.7897 52.4641Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M68.2449 83.3704C68.3484 82.7224 68.504 81.7086 68.6792 80.697C68.8543 79.688 68.5286 79.1711 67.5268 78.9733C66.725 78.8125 65.9107 78.7061 65.1113 78.533C63.2213 78.125 61.8889 76.426 61.9703 74.5142C62.0665 72.2217 63.8307 70.9234 65.3531 70.686C68.0179 70.2704 70.3421 72.6297 69.8979 75.2563C69.7722 76.0055 69.6439 76.7548 69.5205 77.5042C69.4884 77.7069 69.4564 77.9123 69.4515 78.1176C69.4317 78.7557 69.7771 79.2354 70.4063 79.3689C71.1268 79.5223 71.857 79.6335 72.5849 79.7448C74.3713 80.0168 75.6544 81.1248 76.0813 82.7794C76.6538 85.0002 75.1831 87.2457 72.9379 87.6885C70.616 88.146 68.1757 86.2887 68.2399 83.3728L68.2449 83.3704Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M89.2851 68.3141C87.6986 68.3438 85.8727 67.2137 85.4114 65.188C84.9944 63.3655 85.8802 61.518 87.558 60.7093C88.3525 60.326 89.1692 59.9946 89.9711 59.6286C90.8741 59.2181 91.0986 58.6493 90.7163 57.7392C90.4128 57.0194 90.0845 56.3122 89.7835 55.59C88.7645 53.1467 90.0994 50.5919 92.6827 50.033C95.1897 49.4889 97.6644 51.7048 97.4003 54.267C97.24 55.8274 96.4528 56.9577 95.0316 57.623C94.289 57.9716 93.5316 58.2858 92.7839 58.6196C91.8512 59.0375 91.6218 59.6088 92.0214 60.5487C92.3275 61.2658 92.6532 61.9781 92.9517 62.6977C93.9337 65.057 92.6654 67.587 90.1931 68.193C89.8971 68.2647 89.5862 68.277 89.2827 68.3141H89.2851Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M80.3604 40.3706C80.2444 41.0903 80.0816 42.1265 79.9089 43.1603C79.7558 44.0629 80.1382 44.6194 81.0462 44.7851C81.9321 44.9458 82.8252 45.0843 83.7085 45.2574C85.5937 45.6309 86.9063 47.2532 86.9014 49.1699C86.8964 51.1014 85.5715 52.6668 83.7085 53.0946C81.4238 53.6189 78.458 51.7147 79.0032 48.3587C79.1315 47.5673 79.2722 46.7784 79.4079 45.987C79.5584 45.1189 79.181 44.5699 78.2952 44.4018C77.4093 44.2361 76.5186 44.0951 75.6329 43.9269C73.7748 43.5757 72.4475 41.993 72.4105 40.0986C72.3734 38.1646 73.6565 36.5447 75.5119 36.102C77.9028 35.5332 80.422 37.3955 80.3604 40.3681V40.3706Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M58.2714 57.0669C60.1491 57.0916 61.7134 58.3331 62.1574 60.1755C62.5794 61.9363 61.7208 63.8159 60.0749 64.6395C59.2682 65.0426 58.4417 65.4135 57.6002 65.7351C56.781 66.0491 56.3443 66.7812 56.8279 67.7803C57.1857 68.5198 57.5114 69.2764 57.8199 70.0382C58.755 72.3357 57.4621 74.8657 55.0663 75.4494C53.191 75.9069 51.2838 74.967 50.4917 73.1939C49.7145 71.4578 50.2722 69.3951 51.829 68.3071C52.13 68.0969 52.4657 67.9336 52.8011 67.7803C53.5093 67.4563 54.2323 67.1645 54.9404 66.838C55.7571 66.4621 55.989 65.8513 55.6362 65.0179C55.2908 64.2041 54.9034 63.4079 54.5777 62.5867C53.6673 60.2991 54.9231 57.8162 57.2991 57.1979C57.6126 57.1163 57.9482 57.1088 58.2714 57.0669Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M69.1035 62.9101C69.111 59.992 71.492 57.6202 74.4036 57.6276C77.3026 57.6351 79.6812 60.034 79.6812 62.9473C79.6812 65.8804 77.2731 68.2719 74.3344 68.257C71.4378 68.2421 69.0937 65.8458 69.1035 62.9101Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M83.8945 39.572C83.8994 37.7988 85.3526 36.3594 87.1168 36.3792C88.8465 36.399 90.2702 37.8408 90.2801 39.5844C90.2899 41.3427 88.8341 42.8068 87.0749 42.8068C85.3107 42.8068 83.8895 41.36 83.8945 39.5745V39.572Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M58.7456 39.5941C58.7456 37.8233 60.1077 36.3865 61.7929 36.3791C63.4929 36.3716 64.9043 37.8505 64.887 39.6238C64.8697 41.3747 63.4756 42.8116 61.7953 42.8042C60.1053 42.7967 58.7482 41.3673 58.7456 39.5941Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M45.4366 62.2305C45.4292 60.5191 46.8256 59.1342 48.58 59.1143C50.3466 59.0945 51.8271 60.5068 51.8222 62.2106C51.8173 63.8973 50.3788 65.2871 48.6317 65.2947C46.8751 65.302 45.4439 63.9294 45.4366 62.2329V62.2305Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M98.659 59.1122C100.401 59.1071 101.852 60.4946 101.869 62.1787C101.886 63.8827 100.421 65.2973 98.6441 65.2899C96.8899 65.2825 95.4785 63.9025 95.481 62.1961C95.4834 60.4946 96.9022 59.1171 98.659 59.1122Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M87.3443 80.8603C89.0837 80.8678 90.5297 82.2699 90.5297 83.9517C90.5297 85.6606 89.059 87.0579 87.2826 87.0381C85.5307 87.0208 84.1341 85.631 84.1441 83.9196C84.1539 82.2229 85.5899 80.8529 87.3468 80.8603H87.3443Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M58.7471 83.9715C58.7422 82.2502 60.1214 80.8529 61.819 80.8603C63.482 80.8678 64.8712 82.2576 64.8859 83.9318C64.9007 85.6234 63.4893 87.0455 61.7968 87.0406C60.1165 87.0357 58.752 85.6631 58.7446 83.9739L58.7471 83.9715Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M17.2184 120.924L17.2141 116.161H67.6866C68.6013 114.482 69.2906 111.828 70.1946 110.17C70.186 110.154 70.169 110.074 70.1245 110.023C68.151 107.75 65.9356 105.851 63.2682 104.656C61.2906 103.772 59.2301 103.403 57.1039 103.581C55.3319 103.728 53.63 104.228 51.9983 105C46.1564 107.764 40.1531 109.997 33.978 111.664C31.2385 112.406 28.482 113.063 25.6873 113.491C23.994 113.75 22.2942 113.972 20.5903 114.141C19.5038 114.248 18.3472 114.336 17.214 114.373C17.1949 114.221 17.2184 113.963 17.2184 113.818C17.2141 111.968 17.2099 110.119 17.2226 108.269C17.2226 108.049 17.2969 107.813 17.3903 107.612C22.5595 96.3247 27.733 85.0422 32.9064 73.7573C33.3351 72.8219 33.7574 71.8795 34.2115 70.8764C34.6974 71.0845 35.1961 71.2974 35.7415 71.5311C31.3425 83.7116 26.9606 95.8407 22.5404 108.073C22.986 108.024 23.3574 107.996 23.7266 107.937C32.7792 106.473 41.4667 103.606 49.872 99.6569C51.8306 98.7357 53.8592 98.0996 55.9749 97.833C59.3828 97.4028 62.655 97.9967 65.7957 99.5026C68.5989 100.85 71.0242 102.802 73.1993 105.154C73.3373 105.304 73.4434 105.489 73.5897 105.692C74.0269 105.239 74.3941 104.853 74.7675 104.474C77.1378 102.054 79.7882 100.129 82.8461 98.9275C85.1823 98.0083 87.595 97.5876 90.0715 97.7209C92.5966 97.8565 95.0221 98.5111 97.3436 99.6102C103.139 102.355 109.098 104.554 115.23 106.169C117.904 106.876 120.599 107.481 123.328 107.876C123.78 107.942 124.23 108.017 124.786 108.103C120.367 95.8711 115.981 83.7303 111.589 71.5686C111.71 71.4961 111.78 71.4399 111.858 71.4072C112.263 71.2272 112.673 71.0541 113.114 70.8647C113.246 71.1757 113.359 71.4563 113.484 71.73C117.185 79.802 120.887 87.874 124.588 95.9436C126.356 99.7973 128.119 103.653 129.891 107.504C130.029 107.806 130.101 108.106 130.099 108.449C130.086 110.163 130.093 111.875 130.093 113.589V114.26C129.885 114.26 129.73 114.265 129.577 114.26C129.153 114.241 128.73 114.204 128.306 114.199C126.326 114.176 124.363 113.935 122.405 113.626C116.578 112.707 110.895 111.129 105.299 109.141C101.925 107.942 98.6148 106.558 95.3553 105.019C93.5409 104.163 91.6545 103.628 89.6725 103.55C86.933 103.443 84.4035 104.236 82.0418 105.73C80.2338 106.873 78.655 108.33 77.2184 109.986C77.1717 110.039 77.1336 110.107 77.1081 110.144C78.0078 111.795 78.7033 114.468 79.6264 116.161H130.093L130.093 120.906H76.3271C75.9431 119.963 75.5569 118.967 75.1325 117.99C74.7059 117.012 74.237 116.058 73.6577 115.104C72.5351 116.961 71.7564 118.934 70.9966 120.924L17.2184 120.92V120.924Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M197.092 84.997L190.692 67.0263C190.51 66.4944 190.275 65.5826 189.989 64.2908C189.703 62.9991 189.404 61.4161 189.092 59.5417C188.753 61.34 188.427 62.961 188.117 64.4047C187.804 65.8231 187.57 66.7729 187.414 67.2542L181.248 84.997H197.092ZM160.72 106.159L182.457 50.5374H196.428L218.477 106.159H204.584L199.94 94.3432H177.814L173.833 106.159H160.72Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M224.722 106.159V50.5374H236.937V106.159H224.722Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M249.347 92.4056V82.8313H271.201V92.4056H249.347Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M295.63 75.6507H297.386C300.716 75.6507 303.135 74.9795 304.645 73.6371C306.153 72.2946 306.908 70.1417 306.908 67.1782C306.908 64.4428 306.153 62.4545 304.645 61.2133C303.135 59.947 300.716 59.3137 297.386 59.3137H295.63V75.6507ZM283.338 106.159V50.5374H297.386C304.879 50.5374 310.407 51.9304 313.971 54.7165C317.562 57.5028 319.358 61.8086 319.358 67.6342C319.358 73.0291 317.55 77.2591 313.933 80.3238C310.343 83.3886 305.36 84.9209 298.986 84.9209H295.63V106.159H283.338Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M369.154 106.159H355.066L340.041 80.1719V106.159H327.825V50.5374H345.309C352.256 50.5374 357.459 51.8544 360.919 54.4886C364.38 57.0975 366.11 61.0361 366.11 66.3044C366.11 70.1291 364.926 73.3965 362.559 76.1066C360.19 78.8168 357.134 80.3998 353.387 80.8557L369.154 106.159ZM340.041 74.245H341.875C346.819 74.245 350.083 73.7257 351.67 72.6873C353.257 71.6235 354.05 69.7871 354.05 67.1782C354.05 64.4428 353.192 62.5052 351.475 61.3654C349.785 60.2002 346.585 59.6176 341.875 59.6176H340.041V74.245Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M434.793 78.4622C434.793 82.4388 434.026 86.2 432.49 89.746C430.982 93.2921 428.797 96.4075 425.936 99.0923C422.968 101.853 419.626 103.968 415.905 105.437C412.184 106.906 408.335 107.641 404.354 107.641C400.868 107.641 397.447 107.071 394.091 105.931C390.761 104.766 387.703 103.107 384.92 100.954C381.329 98.1678 378.571 94.837 376.647 90.9618C374.747 87.0866 373.797 82.92 373.797 78.4622C373.797 74.4603 374.552 70.7116 376.06 67.2163C377.57 63.6955 379.781 60.5674 382.695 57.832C385.556 55.1218 388.874 53.0195 392.647 51.5251C396.444 50.0308 400.347 49.2836 404.354 49.2836C408.335 49.2836 412.198 50.0308 415.945 51.5251C419.716 53.0195 423.046 55.1218 425.936 57.832C428.823 60.5674 431.02 63.6955 432.53 67.2163C434.038 70.7369 434.793 74.4856 434.793 78.4622ZM404.354 97.0406C409.531 97.0406 413.798 95.2804 417.155 91.7596C420.537 88.2137 422.227 83.7811 422.227 78.4622C422.227 73.1938 420.523 68.7612 417.115 65.1646C413.706 61.5679 409.453 59.7696 404.354 59.7696C399.176 59.7696 394.884 61.5679 391.476 65.1646C388.068 68.7359 386.363 73.1684 386.363 78.4622C386.363 83.8318 388.042 88.2769 391.398 91.7977C394.754 95.293 399.073 97.0406 404.354 97.0406Z\",\n    fill: \"black\"\n  }))), _defs || (_defs = /*#__PURE__*/React.createElement(\"defs\", null, /*#__PURE__*/React.createElement(\"clipPath\", {\n    id: \"clip0_2054_286\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    width: 418,\n    height: 86,\n    fill: \"white\",\n    transform: \"translate(17 36)\"\n  })))));\n}\nconst ForwardRef = /*#__PURE__*/React.forwardRef(SvgAipro);\nexport default __webpack_public_path__ + \"static/media/AIPRO.0580715cba3cbc2179c46b033d087db9.svg\";\nexport { ForwardRef as ReactComponent };", "import { io } from 'socket.io-client';\r\nimport { Get<PERSON>ookie } from './cookies';\r\n\r\nexport function initSocket() {\r\n  let aiwp_logged_in = GetCookie('aiwp_logged_in');\r\n  let run_socket = true;\r\n  if (aiwp_logged_in!==undefined && aiwp_logged_in!==null && aiwp_logged_in!==\"\"){\r\n    let myarr = aiwp_logged_in.split(\"|\");\r\n    if(typeof myarr[2] !== 'undefined') {\r\n      if (myarr[2]==='1'){\r\n        run_socket = false;\r\n      }\r\n    }\r\n  }\r\n  \r\n  if (!run_socket){\r\n    return;\r\n  }\r\n\r\n  const URL = process.env.REACT_APP_SOCKET_URL;\r\n  const socket = io(URL);\r\n  let sidx = ''; // user_ip\r\n  if(GetCookie('user_ip')) {\r\n    sidx = GetCookie('user_ip');\r\n  }\r\n\r\n  socket.emit('register-session', { id: sidx, email: GetCookie('user_email'), socket_key: process.env.REACT_APP_SOCKET_KEY });\r\n\r\n  return { socket, sidx };\r\n}", "import React, { useState, useEffect } from 'react';\r\nimport './style.css';\r\nimport aiproLogo from '../assets/images/AIPRO.svg';\r\nimport '@fortawesome/fontawesome-free/css/all.css';\r\nimport aiproLogoWebP from \"../assets/images/AIPRO.webp\";\r\nimport axios from 'axios';\r\nimport { GetCookie } from '../core/utils/cookies';\r\nimport { useLocation } from 'react-router-dom';\r\nimport { AiOutlineArrowUp } from \"react-icons/ai\";\r\nimport { motion } from \"framer-motion\";\r\nimport { initSocket } from '../core/utils/socket';\r\nimport { returnAppHref, redirectApp, getLocales } from '../core/utils/app';\r\nimport { isSubscriber } from '../core/utils/auth';\r\n\r\nconst tools = process.env.REACT_APP_TOOLS_URL ? JSON.parse(process.env.REACT_APP_TOOLS_URL) : {};\r\nconst trial = process.env.REACT_APP_TOOLS_TRIAL_URL ? JSON.parse(process.env.REACT_APP_TOOLS_TRIAL_URL) : {};\r\nvar client_io;\r\n\r\nfunction Header(props) {\r\n  const auth = props && props.auth ? props.auth : null;\r\n  const showLink = props.hideNavLink ? false : true;\r\n  const [plans, setPlans] = useState([]);\r\n  const location = useLocation();\r\n  const urlEndsWithManage = location.pathname.endsWith('/manage-account');\r\n  const urlEndsWithMyAccount = location.pathname.endsWith('/my-account');\r\n  const isHomePage = location.pathname === '/';\r\n  const [scrolled, setScrolled] = useState(false);\r\n  \r\n  // Get locales from URL or set default\r\n  useEffect(() => {\r\n    getLocales();\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    const handleScroll = () => {\r\n      if (window.scrollY > 50) {\r\n        setScrolled(true);\r\n      } else {\r\n        setScrolled(false);\r\n      }\r\n    };\r\n\r\n    window.addEventListener('scroll', handleScroll);\r\n    return () => {\r\n      window.removeEventListener('scroll', handleScroll);\r\n    };\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    const linkPNG = document.createElement('link');\r\n    linkPNG.rel = 'preload';\r\n    linkPNG.href = aiproLogo;\r\n    linkPNG.as = 'image';\r\n    document.head.appendChild(linkPNG);\r\n\r\n    const linkWebP = document.createElement('link');\r\n    linkWebP.rel = 'preload';\r\n    linkWebP.href = aiproLogoWebP;\r\n    linkWebP.as = 'image';\r\n    document.head.appendChild(linkWebP);\r\n\r\n    return () => {\r\n      document.head.removeChild(linkPNG);\r\n      document.head.removeChild(linkWebP);\r\n    };\r\n  }, []);\r\n  useEffect(() => {\r\n    const setSubscription = props.setSubscription ? props.setSubscription : (data)=>{};\r\n    const setGetDateNow = props.setGetDateNow ? props.setGetDateNow : (data)=>{};\r\n\r\n    async function fetchPlans() {\r\n      try {\r\n        const response = await axios.post(\r\n          `${process.env.REACT_APP_API_URL}/get-subscription`,\r\n          { tk: GetCookie(\"access\") },\r\n          { headers: { \"content-type\": \"application/x-www-form-urlencoded\" } }\r\n        );\r\n\r\n        const { data } = response;\r\n\r\n        if (data.success) {\r\n          const plans = Object.values(data.data);\r\n          setPlans(plans);\r\n          setSubscription(data.data)\r\n          setGetDateNow(data.date_now)\r\n        } else {\r\n          setPlans([]);\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error fetching plans:\", error);\r\n      }\r\n    }\r\n\r\n    fetchPlans();\r\n\r\n    client_io = initSocket();\r\n    if(client_io) {\r\n      let socket = client_io.socket;\r\n      let sidx = client_io.sidx;\r\n      socket.on('session-exist', (data) => {\r\n        if( data.id === sidx ) {\r\n          window.location.href = '/active-session';\r\n        }\r\n      });\r\n      socket.on('logout-account', (data) => {\r\n        if( data.email === GetCookie('user_email') && ( !data.id || data.id !== sidx ) ) {\r\n          window.location.href = '/stop-session';\r\n        }\r\n      });\r\n      socket.on('logout-on-admin', (data) => {\r\n        if( data.email === GetCookie('user_email') && ( !data.id || data.id !== sidx ) ) {\r\n          window.location.href = `/logout`\r\n        }\r\n      });\r\n    }\r\n  }, [props.setSubscription, props.setGetDateNow]);\r\n\r\n  const checkSubscription = () => {\r\n    if (auth) {\r\n      window.location.href = '/my-account';\r\n    } else {\r\n      window.location.href = '/register-auth';\r\n    }\r\n  };\r\n\r\n  const upid = auth && auth.user_pid ? \"?upid=\" + auth.user_pid : \"\";\r\n  //use AI Art\r\n  let text2imagelink = tools.txt2img ? tools.txt2img + upid : 'https://app.ai-pro.org/dream-photo'+upid;\r\n  const text2imagelinktrial = trial.txt2img ? trial.txt2img + upid : 'https://app.ai-pro.org/text-to-image'+upid;\r\n  text2imagelink = auth && ( auth.status === 'active' || isSubscriber(auth) ) ? text2imagelink : text2imagelinktrial;\r\n  const interiorgptlink = tools.interiorgpt ? tools.interiorgpt + upid : 'https://interiorgpt.ai-pro.org'+upid;\r\n  const restorephotolink = tools.restorephoto ? tools.restorephoto + upid : 'https://restorephotos.ai-pro.org'+upid;\r\n  const storybooklink = tools.storybook ? tools.storybook + upid : 'https://storybook.ai-pro.org'+upid;\r\n  const fluxlink = tools.flux ? tools.flux + upid : 'https://flux.ai-pro.org'+upid;\r\n\r\n  //use Chatbot+\r\n  let chatbotlink = tools.chatbot ? tools.chatbot + upid : 'https://app.ai-pro.org/chatbot'+upid;\r\n  const chatbotlinktrial = trial.chatbot ? trial.chatbot + upid : 'https://app.ai-pro.org/start-chatbot'+upid;\r\n  chatbotlink = auth && ( auth.status === 'active' || isSubscriber(auth) ) ? chatbotlink : chatbotlinktrial;\r\n  const chatbotprolink = tools.chatpro ? tools.chatpro + upid : 'https://chatpro.ai-pro.org/chat'+upid;\r\n  // const chatpdflink = tools.chatpdf ? tools.chatpdf + upid : 'https://chatpdf.ai-pro.org'+upid;\r\n  const recipemakerlink = tools.recipemaker ? tools.recipemaker + upid : 'https://chatlibrary.ai-pro.org/ai/recipe'+upid;\r\n  const tripsailink = tools.tripsai ? tools.tripsai + upid : 'https://chatlibrary.ai-pro.org/ai/travel'+upid;\r\n  const translatenowlink = tools.translatenow ? tools.translatenow + upid : 'https://chatlibrary.ai-pro.org/ai/writing/translate'+upid;\r\n  const convertenglishlink = tools.convert2english ? tools.convert2english + upid : 'https://app.ai-pro.org/convert-to-proper-english'+upid;\r\n  const removebg = tools.removebg ? tools.removebg + upid : 'https://clearbg.ai-pro.org'+upid;\r\n  const chatgptlink = tools.chatgpt ? tools.chatgpt + upid : 'https://chatbotplus.ai-pro.org'+upid;\r\n  const chatpdfv2link = tools.chatpdfv2 ? tools.chatpdfv2 + upid : 'https://chatpdfv2.ai-pro.org'+upid;\r\n  const teacherailink = tools.teacherai ? tools.teacherai + upid : 'https://teacher.ai-pro.org'+upid;\r\n  const avatarmakerlink = tools.avatarmaker ? tools.avatarmaker + upid : 'https://avatarmaker.ai-pro.org'+upid;\r\n  const searchailink = tools.searchailink ? tools.searchailink + upid : 'https://search.ai-pro.org'+upid;\r\n  const multillm_link = tools.multillm ? tools.multillm + upid : 'https://multillm.ai-pro.org'+upid;\r\n  const sitebot_link = tools.sitebot ? tools.sitebot + upid : 'https://sitebot.ai-pro.org'+upid;\r\n  const codingai_link = tools.codingai ? tools.codingai + upid : 'https://coding.ai-pro.org'+upid;\r\n  const homework_link = tools.homework ? tools.homework + upid : 'https://tutor.ai-pro.org'+upid;\r\n\r\n\r\n  //\r\n  const showMaintenanceBanner = process.env.REACT_APP_ShowMaintenanceBanner ? process.env.REACT_APP_ShowMaintenanceBanner : \"\";\r\n\r\n  const [showMenu, setShowMenu] = useState(false);\r\n  const toggleMenu = () => {\r\n    setShowMenu(!showMenu);\r\n  };\r\n\r\n  const handleShowEnterpriseUpgrade = () => {\r\n    props.setshowAddMoreMember(true);\r\n  }\r\n\r\n  const fnRedirectApp = (url) => {\r\n    redirectApp(auth, url);\r\n  };\r\n\r\n  const fnReturnAppHref = (url) => {\r\n    return returnAppHref(auth, url);\r\n  };\r\n\r\n  const handleKeyDown = (e, url) => {\r\n    if (e.key === 'Enter') { // Trigger the link action on Enter key press30169_wcag_aipro\r\n      fnRedirectApp(url);\r\n    }\r\n  };\r\n\r\n  const checkEntMaxPlan = () => {\r\n    const targetPlans = ['enterprisemax', 'officemax', 'teammax'];\r\n    const formatPlanName = (name) => name.toLowerCase().replace(/\\s+/g, '');\r\n\r\n    const user_plan = plans.find((item) =>\r\n      targetPlans.includes(formatPlanName(item.label))\r\n    );\r\n    return Boolean(user_plan);\r\n  }\r\n  return (\r\n    <>\r\n    { showMaintenanceBanner ?\r\n    <div id='maintenance-container' className='p-[5px] bg-[#f7a73f] text-center'>\r\n      We are currently undergoing maintenance. We're expecting to be back at 4 AM EST.\r\n    </div> : <></> }\r\n    {isHomePage && (\r\n        <div id=\"top-banner\" className=\"bg-[#1559ED] text-white text-[12px] text-center py-2 px-6 relative\">\r\n          <a href=\"https://ai-pro.org/disclaimer/\" target=\"_blank\" className='text-white' rel=\"noreferrer\">AI-PRO is Powered by OpenAI. We are not affiliated nor related to OpenAI.</a>\r\n        </div>\r\n      )}\r\n    <header className={`headernav border-gray-500 bg-white text-black w-3/4 mx-auto rounded-3xl mt-3 py-2 z-[8888] ${ showMaintenanceBanner || (isHomePage && !scrolled) ? \"top-[60px]\" : \"\"}`}>\r\n      <div className=\"headerStyle container mr-auto ml-0 flex justify-between items-center px-4\">\r\n        <picture className=\"aiprologo\">\r\n          <source\r\n            type=\"image/webp\"\r\n            srcSet={aiproLogoWebP}\r\n            width=\"150\"\r\n            height=\"52\"\r\n            className=\"aiprologo\"\r\n          />\r\n          <img src={aiproLogo} alt=\"AI-Pro Logo\" className=\"aiprologo\"/>\r\n        </picture>\r\n        { showLink ? <>\r\n        {auth ? '' : <button aria-label=\"Register Button\" className=\"headerctabtn gradient-hover-effect font-semibold text-white rounded-3xl hidden sm:block sm:hidden ml-auto mr-3\" onClick={checkSubscription}>START HERE</button> }\r\n        <nav className=\"text-xs lg:text-sm hidden md:block inline-flex\" id=\"menu\">\r\n          {urlEndsWithManage || urlEndsWithMyAccount ? (\r\n            plans.map((singlePlan, index) => (\r\n              <div className=\"flex inline-flex sm:mr-2\" key={index}>\r\n                {((singlePlan.plan_type === 'Basic' && singlePlan.status === 'active') ||\r\n                  (singlePlan.plan_type === 'Pro' && singlePlan.status === 'active') ||\r\n                  (singlePlan.plan_type === 'Advanced' && singlePlan.status === 'active') ||\r\n                  (singlePlan.plan_type === 'ProMax' && auth.user_ppg === '97' && singlePlan.payment_interval !== 'Yearly' && singlePlan.status === 'active')\r\n                  ) && (\r\n                  <a href=\"/upgrade\" tabIndex={0} name=\"upgrade link\" aria-label=\"upgrade link\" className=\"block w-full font-bold text-white py-2 px-4 rounded-md bg-[#DB7E00] hover:bg-[#f99d1f]\">Upgrade</a>\r\n                )}\r\n                {(singlePlan.plan_type === 'Enterprise' && singlePlan.status === 'active') && !checkEntMaxPlan() && (\r\n                  <button className=\"block w-full font-bold text-white py-2 px-4 rounded-md bg-[#ffa500] hover:bg-[#FFC034]\" onClick={() => handleShowEnterpriseUpgrade()}>Upgrade</button>\r\n                )}\r\n              </div>\r\n            ))\r\n          ):('')}\r\n\r\n          <ul className=\"headnav flex inline-flex\">\r\n            <li className=\"mr-1 md:mr-2 lg:mr-3\">\r\n              <div className=\"dropdown-wrapper\" tabIndex={0}>\r\n                <li><span className=\"block w-full menuArrow\" name=\"AI Chatbots\" aria-label=\"AI Chatbots\" tabIndex={0}>AI Chatbots</span></li>\r\n                <ul className=\"dropdown\">\r\n                  <li>\r\n                    <motion.a className=\"block w-full\" style={{cursor:'pointer'}}\r\n                    name=\"Chatbot Pro\" aria-label=\"Chatbot Pro\"\r\n                    href={fnReturnAppHref(chatbotprolink)}\r\n                    target={ fnReturnAppHref(chatbotprolink) === chatbotprolink ? \"_blank\" : \"\" }\r\n                    onClick={() => { fnRedirectApp(chatbotprolink) }}\r\n                    tabIndex={0}\r\n                    onKeyDown={(e) => handleKeyDown(e, chatbotprolink)}\r\n                    >Chatbot Pro</motion.a>\r\n                  </li>\r\n                  {/* <li>\r\n                    <motion.a style={{cursor:'pointer'}}\r\n                    name=\"ChatPDF\" aria-label=\"ChatPDF\"\r\n                    href={fnReturnAppHref(chatpdflink)}\r\n                    target={ fnReturnAppHref(chatpdflink) === chatpdflink ? \"_blank\" : \"\" }\r\n                    onClick={() => { fnRedirectApp(chatpdflink) }}\r\n                    tabIndex={0}\r\n                    onKeyDown={(e) => handleKeyDown(e, chatpdflink)}\r\n                    >ChatPDF</motion.a>\r\n                  </li> */}\r\n                  <li>\r\n                    <motion.a style={{cursor:'pointer'}}\r\n                    name=\"ChatPDFv2\" aria-label=\"ChatPDFv2\"\r\n                    href={fnReturnAppHref(chatpdfv2link)}\r\n                    target={ fnReturnAppHref(chatpdfv2link) === chatpdfv2link ? \"_blank\" : \"\" }\r\n                    onClick={() => { fnRedirectApp(chatpdfv2link) }}\r\n                    tabIndex={0}\r\n                    onKeyDown={(e) => handleKeyDown(e, chatpdfv2link)}\r\n                    >ChatPDF v2 <span className='rounded-[12px] bg-gradient-to-r from-[#3f51b5] to-[#2196f3] pr-[6px] pl-[10px] text-white font-medium'>NEW ✨</span></motion.a>\r\n                  </li>\r\n                  <li>\r\n                    <motion.a style={{cursor:'pointer'}}\r\n                    name=\"Grammar AI\" aria-label=\"Grammar AI\"\r\n                    href={fnReturnAppHref(convertenglishlink)}\r\n                    target={ fnReturnAppHref(convertenglishlink) === convertenglishlink ? \"_blank\" : \"\" }\r\n                    onClick={() => { fnRedirectApp(convertenglishlink) }}\r\n                    tabIndex={0}\r\n                    onKeyDown={(e) => handleKeyDown(e, convertenglishlink)}\r\n                    >Grammar AI</motion.a>\r\n                  </li>\r\n                  <li>\r\n                    <motion.a style={{cursor:'pointer'}}\r\n                    name=\"Recipe Maker\" aria-label=\"Recipe Maker\"\r\n                    href={fnReturnAppHref(recipemakerlink)}\r\n                    target={ fnReturnAppHref(recipemakerlink) === recipemakerlink ? \"_blank\" : \"\" }\r\n                    onClick={() => { fnRedirectApp(recipemakerlink) }}\r\n                    tabIndex={0}\r\n                    onKeyDown={(e) => handleKeyDown(e, recipemakerlink)}\r\n                    >Recipe Maker</motion.a>\r\n                  </li>\r\n                  <li>\r\n                    <motion.a style={{cursor:'pointer'}}\r\n                    name=\"Trips AI\" aria-label=\"Trips AI\"\r\n                    href={fnReturnAppHref(tripsailink)}\r\n                    target={ fnReturnAppHref(tripsailink) === tripsailink ? \"_blank\" : \"\" }\r\n                    onClick={() => { fnRedirectApp(tripsailink) }}\r\n                    tabIndex={0}\r\n                    onKeyDown={(e) => handleKeyDown(e, tripsailink)}\r\n                    >Trips AI</motion.a>\r\n                  </li>\r\n                  <li>\r\n                    <motion.a style={{cursor:'pointer'}}\r\n                    name=\"TranslateNow\" aria-label=\"TranslateNow\"\r\n                    href={fnReturnAppHref(translatenowlink)}\r\n                    target={ fnReturnAppHref(translatenowlink) === translatenowlink ? \"_blank\" : \"\" }\r\n                    onClick={() => { fnRedirectApp(translatenowlink) }}\r\n                    tabIndex={0}\r\n                    onKeyDown={(e) => handleKeyDown(e, translatenowlink)}\r\n                    >TranslateNow</motion.a>\r\n                  </li>\r\n                  <li>\r\n                    <motion.a style={{cursor:'pointer'}}\r\n                    name=\"TeacherAI\" aria-label=\"TeacherAI\"\r\n                    href={fnReturnAppHref(teacherailink)}\r\n                    target={ fnReturnAppHref(teacherailink) === teacherailink ? \"_blank\" : \"\" }\r\n                    onClick={() => { fnRedirectApp(teacherailink) }}\r\n                    tabIndex={0}\r\n                    onKeyDown={(e) => handleKeyDown(e, teacherailink)}\r\n                    >TeacherAI</motion.a>\r\n                  </li>\r\n                  <li>\r\n                    <motion.a style={{cursor:'pointer'}}\r\n                    name=\"SearchAI\" aria-label=\"SearchAI\"\r\n                    href={fnReturnAppHref(searchailink)}\r\n                    target={ fnReturnAppHref(searchailink) === searchailink ? \"_blank\" : \"\" }\r\n                    onClick={() => { fnRedirectApp(searchailink) }}\r\n                    tabIndex={0}\r\n                    onKeyDown={(e) => handleKeyDown(e, searchailink)}\r\n                    >SearchAI</motion.a>\r\n                  </li>\r\n                  <li>\r\n                    <motion.a style={{cursor:'pointer'}}\r\n                    name=\"Multi-Chat\" aria-label=\"Multi-Chat\"\r\n                    href={fnReturnAppHref(multillm_link)}\r\n                    target={ fnReturnAppHref(multillm_link) === multillm_link ? \"_blank\" : \"\" }\r\n                    onClick={() => { fnRedirectApp(multillm_link) }}\r\n                    tabIndex={0}\r\n                    onKeyDown={(e) => handleKeyDown(e, multillm_link)}\r\n                    >Multi-Chat</motion.a>\r\n                  </li>\r\n                  <li>\r\n                    <motion.a style={{cursor:'pointer'}}\r\n                    name=\"SiteBot\" aria-label=\"SiteBot\"\r\n                    href={fnReturnAppHref(sitebot_link)}\r\n                    target={ fnReturnAppHref(sitebot_link) === sitebot_link ? \"_blank\" : \"\" }\r\n                    onClick={() => { fnRedirectApp(sitebot_link) }}\r\n                    tabIndex={0}\r\n                    onKeyDown={(e) => handleKeyDown(e, sitebot_link)}\r\n                    >SiteBot</motion.a>\r\n                  </li>\r\n                  <li>\r\n                    <motion.a style={{cursor:'pointer'}}\r\n                    name=\"Chatbot\" aria-label=\"Chatbot\"\r\n                    href={fnReturnAppHref(chatbotlink)}\r\n                    target={ fnReturnAppHref(chatbotlink) === chatbotlink ? \"_blank\" : \"\" }\r\n                    onClick={() => { fnRedirectApp(chatbotlink) }}\r\n                    tabIndex={0}\r\n                    onKeyDown={(e) => handleKeyDown(e, chatbotlink)}\r\n                    >Chatbot</motion.a>\r\n                  </li>\r\n                  <li>\r\n                    <motion.a className=\"block w-full\" style={{cursor:'pointer'}}\r\n                    name=\"Chatbot+\" aria-label=\"Chatbot+\"\r\n                    href={fnReturnAppHref(chatgptlink)}\r\n                    target={ fnReturnAppHref(chatgptlink) === chatgptlink ? \"_blank\" : \"\" }\r\n                    onClick={() => { fnRedirectApp(chatgptlink) }}\r\n                    tabIndex={0}\r\n                    onKeyDown={(e) => handleKeyDown(e, chatgptlink)}\r\n                    >Chatbot+</motion.a>\r\n                  </li>\r\n                  <li>\r\n                    <motion.a style={{cursor:'pointer'}}\r\n                    name=\"Coding AI\" aria-label=\"Coding AI\"\r\n                    href={fnReturnAppHref(codingai_link)}\r\n                    target={ fnReturnAppHref(codingai_link) === codingai_link ? \"_blank\" : \"\" }\r\n                    onClick={() => { fnRedirectApp(codingai_link) }}\r\n                    tabIndex={0}\r\n                    onKeyDown={(e) => handleKeyDown(e, codingai_link)}\r\n                    >Coding AI</motion.a>\r\n                  </li>\r\n                  <li>\r\n                    <motion.a style={{cursor:'pointer'}}\r\n                    name=\"Homework Help\" aria-label=\"Homework Help\"\r\n                    href={fnReturnAppHref(homework_link)}\r\n                    target={ fnReturnAppHref(homework_link) === homework_link ? \"_blank\" : \"\" }\r\n                    onClick={() => { fnRedirectApp(homework_link) }}\r\n                    tabIndex={0}\r\n                    onKeyDown={(e) => handleKeyDown(e, homework_link)}\r\n                    >Homework Help</motion.a>\r\n                  </li>\r\n                  <li><a href=\"/my-account\" name=\"my-account\" aria-label=\"my-account\" tabIndex={0}>AI Tools</a></li>\r\n                </ul>\r\n              </div>\r\n            </li>\r\n            <li className=\"mr-1 md:mr-2 lg:mr-3\">\r\n              <div className=\"dropdown-wrapper\">\r\n                <span className=\"menuArrow\" name=\"AI Art\" aria-label=\"AI Art\" tabIndex={0}>AI Art</span>\r\n                <ul className=\"dropdown\">\r\n                  <li>\r\n                    <motion.a style={{cursor:'pointer'}}\r\n                    name=\"DreamPhoto\" aria-label=\"DreamPhoto\"\r\n                    href={fnReturnAppHref(text2imagelink)}\r\n                    target={ fnReturnAppHref(text2imagelink) === text2imagelink ? \"_blank\" : \"\" }\r\n                    onClick={() => { fnRedirectApp(text2imagelink) }}\r\n                    tabIndex={0}\r\n                    onKeyDown={(e) => handleKeyDown(e, text2imagelink)}\r\n                    >DreamPhoto</motion.a>\r\n                  </li>\r\n                  <li>\r\n                    <motion.a style={{cursor:'pointer'}}\r\n                    name=\"Interior AI\" aria-label=\"Interior AI\"\r\n                    href={fnReturnAppHref(interiorgptlink)}\r\n                    target={ fnReturnAppHref(interiorgptlink) === interiorgptlink ? \"_blank\" : \"\" }\r\n                    onClick={() => { fnRedirectApp(interiorgptlink) }}\r\n                    tabIndex={0}\r\n                    onKeyDown={(e) => handleKeyDown(e, interiorgptlink)}\r\n                    >Interior AI</motion.a>\r\n                  </li>\r\n                  <li>\r\n                    <motion.a style={{cursor:'pointer'}}\r\n                    name=\"Restore Photo\" aria-label=\"Restore Photo\"\r\n                    href={fnReturnAppHref(restorephotolink)}\r\n                    target={ fnReturnAppHref(restorephotolink) === restorephotolink ? \"_blank\" : \"\" }\r\n                    onClick={() => { fnRedirectApp(restorephotolink) }}\r\n                    tabIndex={0}\r\n                    onKeyDown={(e) => handleKeyDown(e, restorephotolink)}\r\n                    >Restore Photo</motion.a>\r\n                  </li>\r\n                  <li>\r\n                    <motion.a style={{cursor:'pointer'}}\r\n                    name=\"Remove Background\" aria-label=\"Remove Background\"\r\n                    href={fnReturnAppHref(removebg)}\r\n                    target={ fnReturnAppHref(removebg) === removebg ? \"_blank\" : \"\" }\r\n                    onClick={() => { fnRedirectApp(removebg) }}\r\n                    tabIndex={0}\r\n                    onKeyDown={(e) => handleKeyDown(e, removebg)}\r\n                    >Remove Background</motion.a>\r\n                  </li>\r\n                  <li>\r\n                    <motion.a style={{cursor:'pointer'}}\r\n                    name=\"Avatar Maker\" aria-label=\"Avatar Maker\"\r\n                    href={fnReturnAppHref(avatarmakerlink)}\r\n                    target={ fnReturnAppHref(avatarmakerlink) === avatarmakerlink ? \"_blank\" : \"\" }\r\n                    onClick={() => { fnRedirectApp(avatarmakerlink) }}\r\n                    tabIndex={0}\r\n                    onKeyDown={(e) => handleKeyDown(e, avatarmakerlink)}\r\n                    >Avatar Maker</motion.a>\r\n                  </li>\r\n                  <li>\r\n                    <motion.a style={{cursor:'pointer'}}\r\n                    name=\"StoryBook\" aria-label=\"StoryBook\"\r\n                    href={fnReturnAppHref(storybooklink)}\r\n                    target={ fnReturnAppHref(storybooklink) === storybooklink ? \"_blank\" : \"\" }\r\n                    onClick={() => { fnRedirectApp(storybooklink) }}\r\n                    tabIndex={0}\r\n                    onKeyDown={(e) => handleKeyDown(e, storybooklink)}\r\n                    >StoryBook</motion.a>\r\n                  </li>\r\n                  <li>\r\n                    <motion.a style={{cursor:'pointer'}}\r\n                    name=\"Flux ImageGen\" aria-label=\"Flux ImageGen\"\r\n                    href={fnReturnAppHref(fluxlink)}\r\n                    target={ fnReturnAppHref(fluxlink) === fluxlink ? \"_blank\" : \"\" }\r\n                    onClick={() => { fnRedirectApp(fluxlink) }}\r\n                    tabIndex={0}\r\n                    onKeyDown={(e) => handleKeyDown(e, fluxlink)}\r\n                    >Flux ImageGen</motion.a>\r\n                  </li>\r\n                </ul>\r\n              </div>\r\n            </li>\r\n            <li className=\"mr-1 md:mr-2 lg:mr-3\"><a href=\"https://ai-pro.org/enterprise\" name=\"Enterprise\" aria-label=\"Enterprise\" tabIndex={0}>Enterprise</a></li>\r\n            {!auth || auth.status !== 'active' ? <li className=\"mr-1 md:mr-2 lg:mr-3\"><a href=\"/pricing\" name=\"Pricing\" aria-label=\"Pricing\" tabIndex={0}>Pricing</a></li> : '' }\r\n            {auth ? <li className=\"mr-1 md:mr-2 lg:mr-3\"><a href=\"/my-account\" name=\"My App\" aria-label=\"My App\" tabIndex={0}>My Apps</a></li> : '' }\r\n            {auth ? <li className=\"mr-1 md:mr-2 lg:mr-3\"><a href=\"/manage-account\" name=\"Manage Account\" aria-label=\"Manage Account\" tabIndex={0}>Manage Account</a></li> : <li className=\"mr-1 md:mr-2 lg:mr-6\"><a href=\"/login\" name=\"Sign In\" aria-label=\"Sign In\" tabIndex={0}>Sign In</a></li> }\r\n          </ul>\r\n          {auth ? '' : <button className=\"text-[12px] md:text-[12px] headerctabtn inline-flex gradient-hover-effect font-semibold text-white rounded-3xl\" onClick={checkSubscription} name=\"Start Here\" aria-label=\"Register Button\" tabIndex={0}>START HERE</button> }\r\n        </nav>\r\n        {urlEndsWithManage || urlEndsWithMyAccount ? (\r\n          plans.map((singlePlan, index) => (\r\n            <div className={`absolute right-16 md:hidden bg-[#FFC034] rounded mt-[-2px] px-[6px] ${showMenu ? 'active' : ''}`} key={index}>\r\n              {((singlePlan.plan_type === 'Basic' && singlePlan.status === 'active') ||\r\n                (singlePlan.plan_type === 'Pro' && singlePlan.payment_interval === 'Monthly' && singlePlan.status === 'active')) && (\r\n                <a href=\"/upgrade\" className=\"block w-full p-[2px] w-[35px]\" name=\"Upgrade\" aria-label=\"Upgrade\"><AiOutlineArrowUp className=\"mx-auto text-[18px] text-[#ffffff]\" /></a>\r\n              )}\r\n              {(singlePlan.plan_type === 'Enterprise' && singlePlan.status === 'active') && (\r\n                <button className=\"block w-full p-[2px] w-[35px] mx-auto text-[18px] text-[#ffffff]\" onClick={() => handleShowEnterpriseUpgrade()}><AiOutlineArrowUp className=\"mx-auto text-[18px] text-[#ffffff]\" /></button>\r\n              )}\r\n\r\n            </div>\r\n          ))\r\n        ):('')}\r\n        <button\r\n          aria-label=\"hamburger-menu\"\r\n          className={`hamburger-menu md:hidden  ${showMenu ? 'active' : ''} ${auth ? 'logged' : ''}`}\r\n          onClick={toggleMenu}\r\n        >\r\n          <span className=\"hamburger-line\"></span>\r\n          <span className=\"hamburger-line\"></span>\r\n          <span className=\"hamburger-line\"></span>\r\n        </button>\r\n\r\n        <nav className={`mobilenav text-xs lg:text-sm ${showMenu ? '' : 'hidden'}`} id=\"menu\">\r\n          <div className=\"menu-container\">\r\n            <ul className=\"headnav flex flex-wrap absolute w-full md:hidden\">\r\n              <li className=\"w-full md:w-auto mb-2 md:mb-0 md:mr-2 lg:mr-6\">\r\n                <div className=\"wrapper\" tabIndex={0}>\r\n                  <li><span className=\"block font-bold w-full menuArrow\" name=\"AI Chatbots\" aria-label=\"AI Chatbots\" tabIndex={0}>AI Chatbots</span></li>\r\n                  <ul className=\"pl-4 md:relative md:h-auto md:overflow-y-visible\">\r\n                    <li>\r\n                      <motion.a className=\"block w-full\" style={{cursor:'pointer'}}\r\n                      name=\"Chatbot Pro\" aria-label=\"Chatbot Pro\"\r\n                      href={fnReturnAppHref(chatbotprolink)}\r\n                      target={ fnReturnAppHref(chatbotprolink) === chatbotprolink ? \"_blank\" : \"\" }\r\n                      onClick={() => { fnRedirectApp(chatbotprolink) }}\r\n                      tabIndex={0}\r\n                      onKeyDown={(e) => handleKeyDown(e, chatbotprolink)}\r\n                      >Chatbot Pro</motion.a>\r\n                    </li>\r\n                    {/* <li>\r\n                      <motion.a className=\"block w-full\" style={{cursor:'pointer'}}\r\n                      name=\"ChatPDF\" aria-label=\"ChatPDF\"\r\n                      href={fnReturnAppHref(chatpdflink)}\r\n                      target={ fnReturnAppHref(chatpdflink) === chatpdflink ? \"_blank\" : \"\" }\r\n                      onClick={() => { fnRedirectApp(chatpdflink) }}\r\n                      tabIndex={0}\r\n                      onKeyDown={(e) => handleKeyDown(e, chatpdflink)}\r\n                      >ChatPDF</motion.a>\r\n                    </li> */}\r\n                    <li>\r\n                      <motion.a style={{cursor:'pointer'}}\r\n                      name=\"ChatPDFv2\" aria-label=\"ChatPDFv2\"\r\n                      href={fnReturnAppHref(chatpdfv2link)}\r\n                      target={ fnReturnAppHref(chatpdfv2link) === chatpdfv2link ? \"_blank\" : \"\" }\r\n                      onClick={() => { fnRedirectApp(chatpdfv2link) }}\r\n                      tabIndex={0}\r\n                      onKeyDown={(e) => handleKeyDown(e, chatpdfv2link)}\r\n                      >ChatPDF v2 <span className='rounded-[12px] bg-gradient-to-r from-[#3f51b5] to-[#2196f3] pr-[6px] pl-[10px] text-white font-medium'>NEW ✨</span></motion.a>\r\n                    </li>\r\n                    <li>\r\n                      <motion.a className=\"block w-full\" style={{cursor:'pointer'}}\r\n                      name=\"Grammar AI\" aria-label=\"Grammar AI\"\r\n                      href={fnReturnAppHref(convertenglishlink)}\r\n                      target={ fnReturnAppHref(convertenglishlink) === convertenglishlink ? \"_blank\" : \"\" }\r\n                      onClick={() => { fnRedirectApp(convertenglishlink) }}\r\n                      tabIndex={0}\r\n                      onKeyDown={(e) => handleKeyDown(e, convertenglishlink)}\r\n                      >Grammar AI</motion.a>\r\n                    </li>\r\n                    <li>\r\n                      <motion.a className=\"block w-full\" style={{cursor:'pointer'}}\r\n                      name=\"Recipe Maker\" aria-label=\"Recipe Maker\"\r\n                      href={fnReturnAppHref(recipemakerlink)}\r\n                      target={ fnReturnAppHref(recipemakerlink) === recipemakerlink ? \"_blank\" : \"\" }\r\n                      onClick={() => { fnRedirectApp(recipemakerlink) }}\r\n                      tabIndex={0}\r\n                      onKeyDown={(e) => handleKeyDown(e, recipemakerlink)}\r\n                      >Recipe Maker</motion.a>\r\n                    </li>\r\n                    <li>\r\n                      <motion.a className=\"block w-full\" style={{cursor:'pointer'}}\r\n                      name=\"Trips AI\" aria-label=\"Trips AI\"\r\n                      href={fnReturnAppHref(tripsailink)}\r\n                      target={ fnReturnAppHref(tripsailink) === tripsailink ? \"_blank\" : \"\" }\r\n                      onClick={() => { fnRedirectApp(tripsailink) }}\r\n                      tabIndex={0}\r\n                      onKeyDown={(e) => handleKeyDown(e, tripsailink)}\r\n                      >Trips AI</motion.a>\r\n                    </li>\r\n                    <li>\r\n                      <motion.a className=\"block w-full\" style={{cursor:'pointer'}}\r\n                      name=\"TranslateNow\" aria-label=\"TranslateNow\"\r\n                      href={fnReturnAppHref(translatenowlink)}\r\n                      target={ fnReturnAppHref(translatenowlink) === translatenowlink ? \"_blank\" : \"\" }\r\n                      onClick={() => { fnRedirectApp(translatenowlink) }}\r\n                      tabIndex={0}\r\n                      onKeyDown={(e) => handleKeyDown(e, translatenowlink)}\r\n                      >TranslateNow</motion.a>\r\n                    </li>\r\n                    <li>\r\n                      <motion.a className=\"block w-full\" style={{cursor:'pointer'}}\r\n                      name=\"TeacherAI\" aria-label=\"TeacherAI\"\r\n                      href={fnReturnAppHref(teacherailink)}\r\n                      target={ fnReturnAppHref(teacherailink) === teacherailink ? \"_blank\" : \"\" }\r\n                      onClick={() => { fnRedirectApp(teacherailink) }}\r\n                      tabIndex={0}\r\n                      onKeyDown={(e) => handleKeyDown(e, teacherailink)}\r\n                      >TeacherAI</motion.a>\r\n                    </li>\r\n                    <li>\r\n                      <motion.a className=\"block w-full\" style={{cursor:'pointer'}}\r\n                      name=\"SearchAI\" aria-label=\"SearchAI\"\r\n                      href={fnReturnAppHref(searchailink)}\r\n                      target={ fnReturnAppHref(searchailink) === searchailink ? \"_blank\" : \"\" }\r\n                      onClick={() => { fnRedirectApp(searchailink) }}\r\n                      tabIndex={0}\r\n                      onKeyDown={(e) => handleKeyDown(e, searchailink)}\r\n                      >SearchAI</motion.a>\r\n                    </li>\r\n                    <li>\r\n                      <motion.a className=\"block w-full\" style={{cursor:'pointer'}}\r\n                      name=\"Multi-Chat\" aria-label=\"Multi-Chat\"\r\n                      href={fnReturnAppHref(multillm_link)}\r\n                      target={ fnReturnAppHref(multillm_link) === multillm_link ? \"_blank\" : \"\" }\r\n                      onClick={() => { fnRedirectApp(multillm_link) }}\r\n                      tabIndex={0}\r\n                      onKeyDown={(e) => handleKeyDown(e, multillm_link)}\r\n                      >Multi-Chat</motion.a>\r\n                    </li>\r\n                    <li>\r\n                      <motion.a className=\"block w-full\" style={{cursor:'pointer'}}\r\n                      name=\"SiteBot\" aria-label=\"SiteBot\"\r\n                      href={fnReturnAppHref(sitebot_link)}\r\n                      target={ fnReturnAppHref(sitebot_link) === sitebot_link ? \"_blank\" : \"\" }\r\n                      onClick={() => { fnRedirectApp(sitebot_link) }}\r\n                      tabIndex={0}\r\n                      onKeyDown={(e) => handleKeyDown(e, sitebot_link)}\r\n                      >SiteBot</motion.a>\r\n                    </li>\r\n                    <li>\r\n                      <motion.a className=\"block w-full\" style={{cursor:'pointer'}}\r\n                      name=\"Chatbot\" aria-label=\"Chatbot\"\r\n                      href={fnReturnAppHref(chatbotlink)}\r\n                      target={ fnReturnAppHref(chatbotlink) === chatbotlink ? \"_blank\" : \"\" }\r\n                      onClick={() => { fnRedirectApp(chatbotlink) }}\r\n                      tabIndex={0}\r\n                      onKeyDown={(e) => handleKeyDown(e, chatbotlink)}\r\n                      >Chatbot</motion.a>\r\n                    </li>\r\n                    <li>\r\n                      <motion.a className=\"block w-full\" style={{cursor:'pointer'}}\r\n                      name=\"Chatbot+\" aria-label=\"Chatbot+\"\r\n                      href={fnReturnAppHref(chatgptlink)}\r\n                      target={ fnReturnAppHref(chatgptlink) === chatgptlink ? \"_blank\" : \"\" }\r\n                      onClick={() => { fnRedirectApp(chatgptlink) }}\r\n                      tabIndex={0}\r\n                      onKeyDown={(e) => handleKeyDown(e, chatgptlink)}\r\n                      >Chatbot+</motion.a>\r\n                    </li>\r\n                    <li>\r\n                      <motion.a style={{cursor:'pointer'}}\r\n                      name=\"Coding AI\" aria-label=\"Coding AI\"\r\n                      href={fnReturnAppHref(codingai_link)}\r\n                      target={ fnReturnAppHref(codingai_link) === codingai_link ? \"_blank\" : \"\" }\r\n                      onClick={() => { fnRedirectApp(codingai_link) }}\r\n                      tabIndex={0}\r\n                      onKeyDown={(e) => handleKeyDown(e, codingai_link)}\r\n                      >Coding AI</motion.a>\r\n                    </li>\r\n                    <li>\r\n                      <motion.a style={{cursor:'pointer'}}\r\n                      name=\"Homework Help\" aria-label=\"Homework Help\"\r\n                      href={fnReturnAppHref(homework_link)}\r\n                      target={ fnReturnAppHref(homework_link) === homework_link ? \"_blank\" : \"\" }\r\n                      onClick={() => { fnRedirectApp(homework_link) }}\r\n                      tabIndex={0}\r\n                      onKeyDown={(e) => handleKeyDown(e, homework_link)}\r\n                      >Homework Help</motion.a>\r\n                    </li>\r\n                    <li><a href=\"/my-account\" className=\"block w-full\" tabIndex={0}>AI Tools</a></li>\r\n                  </ul>\r\n                </div>\r\n              </li>\r\n              <li className=\"w-full md:w-auto mb-2 md:mb-0 md:mr-2 lg:mr-6\">\r\n                <div className=\"wrapper\">\r\n                  <span className=\"block w-full font-bold menuArrow\" name=\"AI Art\" aria-label=\"AI Art\" tabIndex={0}>AI Art</span>\r\n                  <ul className=\"pl-4 md:relative md:h-auto md:overflow-y-visible\">\r\n                    <li>\r\n                      <motion.a className=\"block w-full\" style={{cursor:'pointer'}}\r\n                      name=\"DreamPhoto\" aria-label=\"DreamPhoto\"\r\n                      href={fnReturnAppHref(text2imagelink)}\r\n                      target={ fnReturnAppHref(text2imagelink) === text2imagelink ? \"_blank\" : \"\" }\r\n                      onClick={() => { fnRedirectApp(text2imagelink) }}\r\n                      tabIndex={0}\r\n                      onKeyDown={(e) => handleKeyDown(e, text2imagelink)}\r\n                      >DreamPhoto</motion.a>\r\n                    </li>\r\n                    <li>\r\n                      <motion.a className=\"block w-full\" style={{cursor:'pointer'}}\r\n                      name=\"Interior AI\" aria-label=\"Interior AI\"\r\n                      href={fnReturnAppHref(interiorgptlink)}\r\n                      target={ fnReturnAppHref(interiorgptlink) === interiorgptlink ? \"_blank\" : \"\" }\r\n                      onClick={() => { fnRedirectApp(interiorgptlink) }}\r\n                      tabIndex={0}\r\n                      onKeyDown={(e) => handleKeyDown(e, interiorgptlink)}\r\n                      >Interior AI</motion.a>\r\n                    </li>\r\n                    <li>\r\n                      <motion.a className=\"block w-full\" style={{cursor:'pointer'}}\r\n                      name=\"Restore Photo\" aria-label=\"Restore Photo\"\r\n                      href={fnReturnAppHref(restorephotolink)}\r\n                      target={ fnReturnAppHref(restorephotolink) === restorephotolink ? \"_blank\" : \"\" }\r\n                      onClick={() => { fnRedirectApp(restorephotolink) }}\r\n                      tabIndex={0}\r\n                      onKeyDown={(e) => handleKeyDown(e, restorephotolink)}\r\n                      >Restore Photo</motion.a>\r\n                    </li>\r\n                    <li>\r\n                    <motion.a className=\"block w-full\" style={{cursor:'pointer'}}\r\n                      name=\"Remove Background\" aria-label=\"Remove Background\"\r\n                      href={fnReturnAppHref(removebg)}\r\n                      target={ fnReturnAppHref(removebg) === removebg ? \"_blank\" : \"\" }\r\n                      onClick={() => { fnRedirectApp(removebg) }}\r\n                      tabIndex={0}\r\n                      onKeyDown={(e) => handleKeyDown(e, removebg)}\r\n                      >Remove Background</motion.a>\r\n                    </li>\r\n                    <li>\r\n                      <motion.a style={{cursor:'pointer'}}\r\n                      name=\"Avatar Maker\" aria-label=\"Avatar Maker\"\r\n                      href={fnReturnAppHref(avatarmakerlink)}\r\n                      target={ fnReturnAppHref(avatarmakerlink) === avatarmakerlink ? \"_blank\" : \"\" }\r\n                      onClick={() => { fnRedirectApp(avatarmakerlink) }}\r\n                      tabIndex={0}\r\n                      onKeyDown={(e) => handleKeyDown(e, avatarmakerlink)}\r\n                      >Avatar Maker</motion.a>\r\n                    </li>\r\n                    <li>\r\n                      <motion.a className=\"block w-full\" style={{cursor:'pointer'}}\r\n                      name=\"StoryBook\" aria-label=\"StoryBook\"\r\n                      href={fnReturnAppHref(storybooklink)}\r\n                      target={ fnReturnAppHref(storybooklink) === storybooklink ? \"_blank\" : \"\" }\r\n                      onClick={() => { fnRedirectApp(storybooklink) }}\r\n                      tabIndex={0}\r\n                      onKeyDown={(e) => handleKeyDown(e, storybooklink)}\r\n                      >StoryBook</motion.a>\r\n                    </li>\r\n                    <li>\r\n                    <motion.a style={{cursor:'pointer'}}\r\n                    name=\"Flux ImageGen\" aria-label=\"Flux ImageGen\"\r\n                    href={fnReturnAppHref(fluxlink)}\r\n                    target={ fnReturnAppHref(fluxlink) === fluxlink ? \"_blank\" : \"\" }\r\n                    onClick={() => { fnRedirectApp(fluxlink) }}\r\n                    tabIndex={0}\r\n                    onKeyDown={(e) => handleKeyDown(e, fluxlink)}\r\n                    >Flux ImageGen</motion.a>\r\n                  </li>\r\n                  </ul>\r\n                </div>\r\n              </li>\r\n\r\n              {auth ? (\r\n                <>\r\n                  <li className=\"w-full md:w-auto mb-2 md:mb-0 md:mr-2 lg:mr-6\">\r\n                    <a href=\"https://ai-pro.org/enterprise\" className=\"block w-full font-bold\" name=\"Enterprise\" aria-label=\"Enterprise\" tabIndex={0}>Enterprise</a>\r\n                  </li>\r\n                  <li className=\"w-full md:w-auto mb-2 md:mb-0 md:mr-2 lg:mr-6\">\r\n                    <a href=\"/my-account\" className=\"block w-full font-bold\" name=\"My Apps\" aria-label=\"My Apps\" tabIndex={0}>My Apps</a>\r\n                  </li>\r\n                  <li className=\"w-full md:w-auto mb-2 md:mb-0 md:mr-2 lg:mr-6\">\r\n                    <a href=\"/manage-account\" className=\"block w-full font-bold\" name=\"Manage Account\" aria-label=\"Manage Account\" tabIndex={0}>Manage Account</a>\r\n                  </li>\r\n                </>\r\n              ) : (\r\n                <>\r\n                  <li className=\"w-full md:w-auto mb-2 md:mb-0 md:mr-2 lg:mr-6\">\r\n                    <a href=\"https://ai-pro.org/enterprise\" className=\"block w-full font-bold\" name=\"Enterprise\" aria-label=\"Enterprise\" tabIndex={0}>Enterprise</a>\r\n                  </li>\r\n                  <li className=\"w-full md:w-auto mb-2 md:mb-0 md:mr-2 lg:mr-6\">\r\n                    <a href=\"/pricing\" className=\"block w-full font-bold\" name=\"Pricing\" aria-label=\"Pricing\" tabIndex={0}>Pricing</a>\r\n                  </li>\r\n                  <li className=\"w-full md:w-auto mb-2 md:mb-0 md:mr-2 lg:mr-6\">\r\n                    <a href=\"/login\" className=\"block w-full font-bold\" name=\"Sign In\" aria-label=\"Sign In\" tabIndex={0}>Sign In</a>\r\n                  </li>\r\n                  {auth ? '' : <button aria-label=\"Register Button\" className=\"headerctabtn gradient-hover-effect font-semibold text-white rounded-3xl block md:hidden ml-0 mr-3\" tabIndex={0} onClick={checkSubscription}>START HERE</button> }\r\n                </>\r\n              )}\r\n            </ul>\r\n          </div>\r\n        </nav>\r\n        </> : <></>}\r\n      </div>\r\n    </header>\r\n    </>\r\n  );\r\n}\r\n\r\nexport default Header;\r\n"], "names": ["initSocket", "aiwp_logged_in", "Get<PERSON><PERSON><PERSON>", "run_socket", "myarr", "split", "URL", "process", "REACT_APP_SOCKET_URL", "socket", "io", "sidx", "emit", "id", "email", "socket_key", "REACT_APP_SOCKET_KEY", "tools", "JSON", "parse", "trial", "REACT_APP_TOOLS_TRIAL_URL", "client_io", "props", "auth", "showLink", "hideNavLink", "plans", "setPlans", "useState", "location", "useLocation", "urlEndsWithManage", "pathname", "endsWith", "urlEndsWithMyAccount", "isHomePage", "scrolled", "setScrolled", "useEffect", "getLocales", "handleScroll", "window", "scrollY", "addEventListener", "removeEventListener", "linkPNG", "document", "createElement", "rel", "href", "aiproLogo", "as", "head", "append<PERSON><PERSON><PERSON>", "linkWebP", "aiproLogoWebP", "<PERSON><PERSON><PERSON><PERSON>", "setSubscription", "data", "setGetDateNow", "async", "response", "axios", "post", "tk", "headers", "success", "Object", "values", "date_now", "error", "console", "fetchPlans", "on", "checkSubscription", "upid", "user_pid", "text2imagelink", "txt2img", "text2imagelinktrial", "status", "isSubscriber", "interiorgptlink", "interiorgpt", "restorephotolink", "restorephoto", "storybooklink", "storybook", "fluxlink", "flux", "chatbotlink", "chatbot", "chatbotlinktrial", "chatbotprolink", "chatpro", "recipemakerlink", "recipemaker", "tripsailink", "tripsai", "translatenowlink", "translatenow", "convertenglishlink", "convert2english", "removebg", "chatgptlink", "chatgpt", "chatpdfv2link", "chatpdfv2", "teacherailink", "<PERSON><PERSON>", "avatarmakerlink", "avat<PERSON><PERSON>", "searchailink", "multillm_link", "multillm", "sitebot_link", "sitebot", "codingai_link", "codingai", "homework_link", "homework", "showMaintenanceBanner", "REACT_APP_ShowMaintenanceBanner", "showMenu", "setShowMenu", "handleShowEnterpriseUpgrade", "setshowAddMoreMember", "fnRedirectApp", "url", "redirectApp", "fnReturnAppHref", "returnAppHref", "handleKeyDown", "e", "key", "checkEntMaxPlan", "targetPlans", "user_plan", "find", "item", "includes", "label", "toLowerCase", "replace", "Boolean", "_jsxs", "_Fragment", "children", "_jsx", "className", "target", "type", "srcSet", "width", "height", "src", "alt", "onClick", "map", "singlePlan", "index", "plan_type", "user_ppg", "payment_interval", "tabIndex", "name", "motion", "a", "style", "cursor", "onKeyDown", "AiOutlineArrowUp", "toggleMenu"], "sourceRoot": ""}