"use strict";(self.webpackChunkv1=self.webpackChunkv1||[]).push([[4532,7719,9044],{70572:(e,t,s)=>{s.d(t,{X:()=>a,t:()=>r});var o=s(79378);function a(e){let t="";return e?e.length<6&&(t=o.Z.t("echo.register.validation.passwordCharText")):t=o.Z.t("echo.register.validation.passwordReqText"),t}function r(e,t){let s="";return e!==t&&(s=o.Z.t("echo.register.validation.passwordsDoNotMatch")),s}},77719:(e,t,s)=>{s.r(t),s.d(t,{default:()=>r});var o=s(72791),a=s(80184);const r=function(){const e="http://localhost:9002/";return(0,o.useEffect)(()=>{const t=document.createElement("script");t.src=e+"snippets/com.global.vuzo/js/com.global.vuzo.js",t.async=!0,t.onload=()=>{},document.body.appendChild(t)},[e]),(0,a.jsx)(a.Fragment,{children:(0,a.jsx)("footer",{className:"relative hidden md:block md:absolute bottom-[10px] right-[5px] w-full mt-3 py-12 md:py-2 z-[8888] md:hidden"})})}},10877:(e,t,s)=>{s.r(t),s.d(t,{default:()=>b});var o=s(72791),a=s(96347),r=s(11087),l=s(91933),n=s(31243),c=s(70572),d=s(74335),i=s(95828),u=s.n(i),p=(s(92831),s(80184));const m=(0,d.Pd)()?null:s(10728).default,h=(0,d.Pd)()?null:s(77719).default;var f="",w="";async function x(){return!!(await n.Z.post("http://localhost:9002/api/check-access-link",{email:f,tk:w},{headers:{"content-type":"application/x-www-form-urlencoded"}})).data.success}const b=function(){(0,o.useEffect)(()=>{u().options={positionClass:"toast-top-center"}},[]);let[e]=(0,r.lr)();w=e.get("tk"),f=e.get("email");const{data:t}=(0,l.useQuery)("users",x),[s,i]=(0,o.useState)(""),[b,g]=(0,o.useState)(""),[y,v]=(0,o.useState)(""),k=!(0,d.Pd)(),j=e=>{window.location.href=e};if(void 0===t)return;if(!1===t)return void j("/login");const N=()=>{let e=(0,c.X)(s);return!e||(v(e),!1)},C=e=>{let t=N(),o=(()=>{let e=(0,c.t)(s,b);return!e||(v(e),!1)})();t&&o&&(document.querySelector(".loader-container").classList.add("active"),n.Z.post("http://localhost:9002/api/change-password",{access_token:w,password:s,confpassword:b},{headers:{"content-type":"application/x-www-form-urlencoded"}}).then(function(e){let t=e.data;if(t.success)return u().success("Success"),void setTimeout(function(){j("/login")},3e3);document.querySelector(".loader-container").classList.remove("active"),t.data&&u().error(t.data.msg)}))};return(0,p.jsxs)(p.Fragment,{children:[k&&(0,p.jsx)(m,{}),(0,p.jsx)("div",{className:"register bg-gray-100 h-screen flex justify-center items-center pt-10 p-[20px] md:pt-2",children:(0,p.jsx)("div",{className:"container mx-auto py-10 px-0 md:px-4 sm:px-0 w-96 mx-auto",children:(0,p.jsxs)("div",{className:"reg_col text-center mb-8",children:[(0,p.jsx)("h1",{className:"text-2xl lg:text-2xl font-bold text-center mb-6 lg:mb-8",children:"Reset Password"}),(0,p.jsx)("div",{className:"bg-white rounded-lg shadow-lg overflow-hidden",children:(0,p.jsxs)("div",{className:"px-6 py-10",children:[(0,p.jsxs)("label",{className:"relative block",children:[y&&(0,p.jsx)("span",{className:"text-red-500 text-xs text-left w-full mb-2",children:y}),(0,p.jsx)("input",{className:"placeholder:italic placeholder:text-slate-400 block bg-white w-full border border-slate-300 rounded-md my-3 py-2 pl-3 pr-3 shadow-sm focus:outline-none focus:border-sky-500 focus:ring-sky-500 focus:ring-1 sm:text-sm",placeholder:"Enter New Password *","aria-label":"New Password",type:"password",name:"password",onKeyUp:e=>{v(""),i(e.target.value),13===e.keyCode&&C()},onBlur:N,required:!0}),(0,p.jsx)("input",{className:"placeholder:italic placeholder:text-slate-400 block bg-white w-full border border-slate-300 rounded-md my-3 py-2 pl-3 pr-3 shadow-sm focus:outline-none focus:border-sky-500 focus:ring-sky-500 focus:ring-1 sm:text-sm",placeholder:"Confirm New Password *","aria-label":"Confirm Password",type:"password",name:"confirmpassword",onKeyUp:e=>{g(e.target.value),13===e.keyCode&&C()},required:!0})]}),(0,p.jsx)(a.E.button,{type:"submit",className:"bg-blue-600 text-white font-bold py-3 px-6 my-3 rounded-lg w-full",whileHover:{scale:1.1},whileTap:{scale:.9},onClick:C,"aria-label":"Reset Password",children:"Reset Password"})]})})]})})}),k&&(0,p.jsx)(h,{})]})}}}]);
//# sourceMappingURL=4532.084f501a.chunk.js.map