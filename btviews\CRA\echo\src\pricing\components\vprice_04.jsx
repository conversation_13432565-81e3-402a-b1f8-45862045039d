"use client"

import React, { lazy, Suspense, useState, useEffect, useRef, useCallback } from "react"
import { motion } from "framer-motion"
import { getPricePlan, DailyPrice } from "../../core/utils/main"
import { GetCookie } from "../../core/utils/cookies"
import { hexHash, hoverDarken, useDeviceSize } from "../../core/utils/helper"
import "../css/style.css"
import { useTranslation } from "react-i18next"
import CheckIcon from "../../assets/images/check-icon.svg"
import { HiMiniSparkles } from "react-icons/hi2"
import VPriceTrial from "./vprice_trial"
import PPEcho02 from "./PPEcho02"

const TpReviews = React.lazy(() => import("../../features/tpreviews"))

function VPrice04(props) {
  const data = props.data ? props.data : null
  const setPricing = props.setPricing ? props.setPricing : () => {}
  var ppg = GetCookie("ppg")
    ? GetCookie("ppg")
    : process.env.REACT_APP_DEFAULT_PPG
      ? process.env.REACT_APP_DEFAULT_PPG
      : "14"
  var pp_ctaclr = GetCookie("pp_ctaclr") ? GetCookie("pp_ctaclr") : "1559ED"

  const ppgArrayWithToggle = ["40", "48", "52", "97", "101", "109", "110"]
  const showToggle = ppgArrayWithToggle.includes(ppg) && props.showToggle ? props.showToggle : false
  const tpreviews = GetCookie("tp_reviews") ? GetCookie("tp_reviews") : ""
  const enterprise = GetCookie("enterprise") ? GetCookie("enterprise") : "off"
  const ptoggle = GetCookie("p_toggle") ? GetCookie("p_toggle") : ""
  const [desc_align] = useState(GetCookie("desc_align"))
  const [isMobile, setIsMobile] = useState(false)
  const { isTablet } = useDeviceSize()
  const allowTrialPlan = data && (data[0].plan_name.toLowerCase().startsWith("trial") || ["60", "62"].includes(ppg))
  const [appName] = useState((GetCookie('appName') ? GetCookie('appName') : "ChatBot Pro"))
  const [pricingText, setPricingText] = useState(`Unlock the full potential of ${appName}`)
  const [isChatPDFContext, setIsChatPDFContext] = useState(false)
  const [planInterval, setPlanInterval] = useState("monthly")

  // Swipe functionality
  const [currentPlan, setCurrentPlan] = useState(0)
  const carouselRef = useRef(null)
  const [touchStart, setTouchStart] = useState(null)
  const [touchEnd, setTouchEnd] = useState(null)
  const [showSwipe, setShowSwipe] = useState(false)
  const [visiblePlans, setVisiblePlans] = useState([])

  var billedAnnualDisplay = false
  const { t } = useTranslation()

  useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth <= 729)
    }

    checkScreenSize()
    window.addEventListener("resize", checkScreenSize)

    return () => window.removeEventListener("resize", checkScreenSize)
  })

  useEffect(() => {
    const referrer = document.referrer;
    const parentDomain = referrer ? new URL(referrer).hostname : "";

    const allowedDomains = [
      "staging.chatpdfv2.ai-pro.org",
      "chatpdfv2.ai-pro.org"
    ];

    if (
      GetCookie("chatpdfv2modal") === "true" ||
      allowedDomains.includes(parentDomain)
    ) {
      setPricingText("Unlock the full potential of ChatPDF v2");
      setIsChatPDFContext(true);
    }
  }, []);

  const checkPlanInterval = useCallback((plan) => {
    if (!showToggle) return true;
    if (plan.payment_interval.toLowerCase() === planInterval) return true;
    return false;
  }, [showToggle, planInterval]);

  // Set up visible plans and determine if swipe should be shown
  useEffect(() => {
    if (data) {
      const filtered = data.filter((plan) => checkPlanInterval(plan))
      setVisiblePlans(filtered)
      // Only show swipe on mobile when there are 2+ plans
      setShowSwipe(window.innerWidth <= 768 && filtered.length > 1)
    }
  }, [data, planInterval, isMobile, checkPlanInterval]);

  // Update showSwipe on window resize
  useEffect(() => {
    const handleResize = () => {
      if (data) {
        const filtered = data.filter((plan) => checkPlanInterval(plan))
        setShowSwipe(window.innerWidth <= 768 && filtered.length > 1)
      }
    }

    window.addEventListener("resize", handleResize)
    return () => window.removeEventListener("resize", handleResize)
  }, [data, planInterval, checkPlanInterval]);

  if (ppg === "48") {
    billedAnnualDisplay = true
  }

  if (ptoggle === "01" || ptoggle === "03") {
    billedAnnualDisplay = true
  }

  const [planIntervalState, setPlanIntervalState] = useState(billedAnnualDisplay ? "yearly" : "monthly")

  useEffect(() => {
    setPlanInterval(planIntervalState)
  }, [planIntervalState])

  const intervalChange = () => {
    if (planIntervalState === "monthly") {
      setPlanIntervalState("yearly")
    } else {
      setPlanIntervalState("monthly")
    }
    setCurrentPlan(0) // Reset to first plan when changing interval
  }

  // Swipe handlers
  const minSwipeDistance = 50

  const onTouchStart = (e) => {
    setTouchEnd(null)
    setTouchStart(e.targetTouches[0].clientX)
  }

  const onTouchMove = (e) => {
    setTouchEnd(e.targetTouches[0].clientX)
  }

  const onTouchEnd = () => {
    if (!touchStart || !touchEnd) return
    const distance = touchStart - touchEnd
    const isLeftSwipe = distance > minSwipeDistance
    const isRightSwipe = distance < -minSwipeDistance

    if (isLeftSwipe && currentPlan < visiblePlans.length - 1) {
      setCurrentPlan((prev) => prev + 1)
    }
    if (isRightSwipe && currentPlan > 0) {
      setCurrentPlan((prev) => prev - 1)
    }
  }

  const goToPrevPlan = () => {
    if (currentPlan > 0) {
      setCurrentPlan((prev) => prev - 1)
    }
  }

  const goToNextPlan = () => {
    if (currentPlan < visiblePlans.length - 1) {
      setCurrentPlan((prev) => prev + 1)
    }
  }

  const enterpriseTab = () => (
    <div className=" w-full lg:w-[330px] xl:w-[380px] text-center px-4 mb-8 relative">
      <div className="bg-white rounded-lg shadow-lg overflow-hidden">
        <div className="px-6 py-20 md:py-10 price-content">
          <h3 className="text-2xl font-bold mb-4">{t("echo.pricing.vprice_02.enterprise.title")}</h3>
          <p className={`text-4xl font-bold text-[#4285F4] ${planInterval === "yearly" && ppg === "48" ? "" : "mb-4"}`}>
            {t("echo.pricing.vprice_02.enterprise.price")}
          </p>
          {planInterval === "yearly" && ppg === "48" && GetCookie("daily") !== "on" ? (
            <div className="text-xs mb-4">(billed yearly)</div>
          ) : (
            ""
          )}
          <div className="py-4">
            <motion.button
              className="text-white font-bold py-3 px-3 rounded-lg"
              style={{ backgroundColor: hexHash(pp_ctaclr) }}
              whileHover={{ scale: 1.05, backgroundColor: hoverDarken(pp_ctaclr) }}
              whileTap={{ scale: 0.9 }}
              onClick={() => setPricing(62)}
            >
              {t("echo.pricing.vprice_02.enterprise.cta")}
            </motion.button>
          </div>
          <div className={`mb-6 pricing-description ${desc_align === "left" ? "text-left" : "text-center"} ppg-${ppg}`}>
            <ul className="text-sm text-gray-600">
              <li className="mb-2 font-bold">
                <div>{t("echo.pricing.vprice_02.enterprise.desc1")}</div>
              </li>
              <li className="">
                <div>{t("echo.pricing.vprice_02.enterprise.desc2")}</div>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )

  const trialPlanUseCustomComponent = false //Set to true if want to use the separate component
  if (allowTrialPlan && trialPlanUseCustomComponent) {
    return (
      <VPriceTrial
        data={data}
        checkPlanInterval={checkPlanInterval}
        getPricePlan={getPricePlan}
        planInterval={planInterval}
        ppg={ppg}
        billedAnnualDisplay={billedAnnualDisplay}
        ptoggle={ptoggle}
        pp_ctaclr={pp_ctaclr}
        enterpriseTab={enterpriseTab}
        enterprise={enterprise}
        tpreviews={tpreviews}
        setPricing={setPricing}
        isMobile={isMobile}
        desc_align={desc_align}
        CheckIcon={CheckIcon}
        TpReviews={TpReviews}
      />
    )
  }

  // Render a single plan card
  const renderPlanCard = (plan, index) => (
    <div key={index} className={`w-full text-center px-2 mb-8 relative ${!allowTrialPlan ? "lg:w-[340px] xl:w-[410px] lg:min-w-[300px]" : "md:w-[450px] md:min-w-[455px]"}`}>
      <div className="bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden">
        <div className="px-6 py-10 md:py-8 price-content shadow-md">

          {allowTrialPlan && (
            <div className={`flex justify-center`}>
              <div className="flex flex-col mb-[20px]">
                <h1 className={`text-center mb-2 text-[30px] bg-gradient-to-r from-[#305EE9] to-[#474DE7] text-transparent bg-clip-text lg:text-[30px] font-black`}>
                  Upgrade to Chatbot Pro
                </h1>
                <div className={`text-center text-[16px] text-gray-700 m-auto ${isChatPDFContext ? "max-w-500:w-[235px]" : ""}`}>
                  {pricingText}
                </div>
              </div>
            </div>
          )}
          <h3 className={`mb-2 ${!allowTrialPlan ? "text-[22px] font-bold text-blue-600" : "text-[14px] text-gray-500 font-bold"}`}>
            {plan.label}
            {/* {plan.payment_interval === "Yearly" ? (
              <>
                {plan.label.split(' ')[0]}<br />
                {plan.label.split(' ').slice(1).join(' ')}
              </>
            ) : (
              plan.label
            )} */}
          </h3>

          {GetCookie("daily") === "on" ? (
            <DailyPrice plan={plan} />
          ) : billedAnnualDisplay && plan.payment_interval === "Yearly" && (ppg === "48" || ptoggle === "02") ? (
            <p className="text-[36px] font-bold mb-6">
              {getPricePlan(plan.currency, Number.parseFloat(plan.price / 12).toFixed(2))}
              <span className="text-lg text-gray-400"> /month</span>
            </p>
          ) : plan.trial_price ? (
            <p className="text-[36px] font-bold mb-6">{getPricePlan(plan.currency, plan.trial_price)}</p>
          ) : (
            <p className="text-[36px] font-bold mb-6">
              {(ptoggle === "02" || ptoggle === "03") && plan.payment_interval === "Yearly"
                ? getPricePlan(plan.currency, Number.parseFloat(plan.price / 12).toFixed(2))
                : getPricePlan(plan.currency, plan.price)}
              <span className="text-lg text-gray-400">
                /
                {(ptoggle === "02" || ptoggle === "03") && plan.payment_interval === "Yearly"
                  ? " month"
                  : plan.payment_interval === "Monthly"
                    ? " month"
                    : " year"}
              </span>
            </p>
          )}

          <motion.button
            className="w-full py-3 bg-gradient-to-r from-[#305EE9] to-[#474DE7] text-white rounded-md font-medium hover:bg-blue-700 transition-colors mb-6 text-[18px]"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => setPricing(plan.plan_id)}
          >
            Continue
          </motion.button>

          {(ptoggle === "02" || ptoggle === "03") &&
            plan.payment_interval === "Yearly" &&
            GetCookie("daily") !== "on" && (
              <span className="text-sm text-gray-500 block mb-4">
                or {getPricePlan(plan.currency, plan.price)} annual
              </span>
            )}
          {((billedAnnualDisplay &&
            plan.payment_interval === "Yearly" &&
            !["01", "02", "03"].includes(ptoggle) &&
            GetCookie("daily") !== "on") ||
            (!["01", "02", "03"].includes(ptoggle) && ppg === 48 && GetCookie("daily") !== "on")) && (
            <div className="text-xs mb-4">(billed yearly)</div>
          )}

          <div className={`w-full text-left ${allowTrialPlan ? "" : "sm:min-h-[440px] md:min-h-[500px] lg:min-h-[480px] xl:min-h-[460px]"}`}>
            <ul className="space-y-4 w-full">
              <PPEcho02 planType={plan.plan_type} />
            </ul>
          </div>
        </div>
      </div>
      {plan.plan_type === "Pro" && !allowTrialPlan ? (
        <div className="absolute top-0 left-1/2 transform rounded-full min-w-[130px] max-w-[130px] max-w-768:min-w-[142px] -translate-x-1/2 -translate-y-1/2 bg-blue-600 text-white font-bold text-[16px] py-1 px-2 text-xs span-highlight">
          <HiMiniSparkles className="inline text-yellow-400" /> Most Popular{" "}
          <HiMiniSparkles className="inline text-yellow-400" />
        </div>
      ) : null}
    </div>
  )

  return (
    <div className="v-pricing pricing md:min-h-[90vh] bg-[#F6F7F8]">
      <div className={`pricing_columns mx-auto`}>
        <div className="w-full">
          <div className="pricing_header mb-[10px]">
            {!allowTrialPlan && (
              <div className={`flex justify-center`}>
                <div className="flex flex-col mb-[20px]">
                  <h1 className="text-4xl bg-gradient-to-r from-[#305EE9] to-[#474DE7] text-transparent bg-clip-text lg:text-4xl font-bold text-center mb-2 min-h-[44px]">
                    Pricing Plan
                  </h1>
                  <div className={`text-center text-[16px] ${isChatPDFContext ? "max-w-500:w-[235px]" : ""}`}>
                    {pricingText}
                  </div>
                </div>
              </div>
            )}
            {showToggle && (
              <div className="p-4">
                <div className="flex items-center justify-center w-full mb-4">
                  <label htmlFor="toggleB" className="flex items-center cursor-pointer">
                    <div
                      className={`${planIntervalState === "monthly" ? "text-gray-800 font-bold" : "text-gray-700"} mr-3 uppercase`}
                    >
                      Monthly
                    </div>
                    <div className="relative">
                      <input
                        type="checkbox"
                        id="toggleB"
                        className="sr-only toggle"
                        onChange={intervalChange}
                        defaultChecked={billedAnnualDisplay}
                      />
                      <div className="block bg-gradient-to-r from-[#268FED] to-[#3D56BA] w-12 h-6 rounded-full"></div>
                      <div
                        className={`dot absolute top-1 bg-white w-4 h-4 rounded-full transition-all duration-300 ease-in-out ${
                          planIntervalState === "yearly" ? "left-[0.3rem]" : "left-1"
                        }`}
                      ></div>
                    </div>
                    <div
                      className={`${planIntervalState === "yearly" ? "text-gray-800 font-bold" : "text-gray-700"} ml-3 uppercase`}
                    >
                      Yearly
                    </div>
                  </label>
                </div>
              </div>
            )}
          </div>
          <div
            className={`flex flex-col max-w-768:flex-row justify-center items-start ${isChatPDFContext ? "lg:ml-[5%]" : ""}`}
          >
            <div className={`pricing_columns w-full ${allowTrialPlan ? "md:w-[60%] sm:px-[5%]" : "lg:w-[78%]"}`}>
                <>
                  {showSwipe ? (
                    <div className="relative">
                      {/* Swipe Navigation - using simple HTML instead of lucide-react */}
                      <button
                        onClick={goToPrevPlan}
                        className={`absolute left-0 top-1/2 transform -translate-y-1/2 z-10 bg-white/80 p-2 rounded-full shadow ${
                          currentPlan === 0 ? "opacity-50 cursor-not-allowed" : "opacity-100"
                        }`}
                        disabled={currentPlan === 0}
                        aria-label="Previous plan"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="24"
                          height="24"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="text-blue-600"
                        >
                          <polyline points="15 18 9 12 15 6"></polyline>
                        </svg>
                      </button>
                      <button
                        onClick={goToNextPlan}
                        className={`absolute right-0 top-1/2 transform -translate-y-1/2 z-10 bg-white/80 p-2 rounded-full shadow ${
                          currentPlan === visiblePlans.length - 1 ? "opacity-50 cursor-not-allowed" : "opacity-100"
                        }`}
                        disabled={currentPlan === visiblePlans.length - 1}
                        aria-label="Next plan"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="24"
                          height="24"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="text-blue-600"
                        >
                          <polyline points="9 18 15 12 9 6"></polyline>
                        </svg>
                      </button>

                      {/* Swipeable Content */}
                      <div
                        ref={carouselRef}
                        className="overflow-hidden"
                        onTouchStart={onTouchStart}
                        onTouchMove={onTouchMove}
                        onTouchEnd={onTouchEnd}
                      >
                        <div
                          className="flex transition-transform duration-300 ease-in-out"
                          style={{ transform: `translateX(-${currentPlan * 100}%)` }}
                        >
                          {visiblePlans.map((plan, index) => (
                            <div key={index} className="w-full flex-shrink-0">
                              <div className="w-full text-center px-4 relative">
                                <div className="bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden">
                                  <div className="px-6 py-10 md:py-8 price-content shadow-md">

                                  {allowTrialPlan && (
                                    <div className={`flex justify-center`}>
                                      <div className="flex flex-col mb-[20px]">
                                        <h1 className={`text-center mb-2 text-2xl bg-gradient-to-r from-[#305EE9] to-[#474DE7] text-transparent bg-clip-text lg:text-2xl font-black`}>
                                          Upgrade to Chatbot Pro
                                        </h1>
                                        <div className={`text-center text-[16px] ${isChatPDFContext ? "max-w-500:w-[235px]" : ""}`}>
                                          {pricingText}
                                        </div>
                                      </div>
                                    </div>
                                  )}

                                    <h3 className="text-[22px] font-bold text-blue-600 mb-4">
                                      {plan.label}
                                      {/* {plan.payment_interval === "Yearly" ? (
                                        <>
                                          {plan.label.split(' ')[0]}<br />
                                          {plan.label.split(' ').slice(1).join(' ')}
                                        </>
                                      ) : (
                                        plan.label
                                      )} */}
                                    </h3>

                                    {GetCookie("daily") === "on" ? (
                                      <DailyPrice plan={plan} />
                                    ) : billedAnnualDisplay &&
                                      plan.payment_interval === "Yearly" &&
                                      (ppg === "48" || ptoggle === "02") ? (
                                      <p className="text-[36px] font-bold mb-6">
                                        {getPricePlan(plan.currency, Number.parseFloat(plan.price / 12).toFixed(2))}
                                        <span className="text-lg text-gray-400"> /month</span>
                                      </p>
                                    ) : plan.trial_price ? (
                                      <p className="text-[36px] font-bold mb-6">
                                        {getPricePlan(plan.currency, plan.trial_price)}
                                      </p>
                                    ) : (
                                      <p className="text-[36px] font-bold mb-6">
                                        {(ptoggle === "02" || ptoggle === "03") && plan.payment_interval === "Yearly"
                                          ? getPricePlan(plan.currency, Number.parseFloat(plan.price / 12).toFixed(2))
                                          : getPricePlan(plan.currency, plan.price)}
                                        <span className="text-lg text-gray-400">
                                          /
                                          {(ptoggle === "02" || ptoggle === "03") && plan.payment_interval === "Yearly"
                                            ? " month"
                                            : plan.payment_interval === "Monthly"
                                              ? " month"
                                              : " year"}
                                        </span>
                                      </p>
                                    )}

                                    <motion.button
                                      className="w-full py-3 bg-gradient-to-r from-[#4F46E5] to-[#3B82F6] text-white rounded-md font-medium hover:bg-blue-700 transition-colors mb-6 text-[16px]"
                                      whileHover={{ scale: 1.05 }}
                                      whileTap={{ scale: 0.95 }}
                                      onClick={() => setPricing(plan.plan_id)}
                                    >
                                      Continue
                                    </motion.button>

                                    {(ptoggle === "02" || ptoggle === "03") &&
                                      plan.payment_interval === "Yearly" &&
                                      GetCookie("daily") !== "on" && (
                                        <span className="text-sm text-gray-500 block mb-4">
                                          or {getPricePlan(plan.currency, plan.price)} annual
                                        </span>
                                      )}
                                    {((billedAnnualDisplay &&
                                      plan.payment_interval === "Yearly" &&
                                      !["01", "02", "03"].includes(ptoggle) &&
                                      GetCookie("daily") !== "on") ||
                                      (!["01", "02", "03"].includes(ptoggle) &&
                                        ppg === 48 &&
                                        GetCookie("daily") !== "on")) && (
                                      <div className="text-xs mb-4">(billed yearly)</div>
                                    )}

                                    <div className="w-full text-left md:min-h-[500px]">
                                      {/* Plan content - same as original */}
                                      <ul className="space-y-4 w-full">
                                        {plan.plan_type === "Basic" && (
                                          <>
                                            {/* Language Models Section */}
                                            <li className="w-full">
                                              <h3 className="font-semibold text-gray-800 mb-3">Language Models</h3>
                                              <ul className="space-y">
                                                <li className="flex items-center">
                                                  <span className="text-blue-600 mr-2">✓</span>
                                                  <span className="text-gray-700">GPT-4o</span>
                                                </li>
                                                <li className="flex items-center">
                                                  <span className="text-blue-600 mr-2">✓</span>
                                                  <span className="text-gray-700">DeepSeek</span>
                                                </li>
                                                <li className="flex items-center">
                                                  <span className="text-blue-600 mr-2">✓</span>
                                                  <span className="text-gray-700">
                                                    Claude:{" "}
                                                    <span className="text-gray-500">
                                                      25k tokens{isTablet && !allowTrialPlan ? "/mo." : "/month"}
                                                    </span>
                                                  </span>
                                                </li>
                                              </ul>
                                            </li>

                                            {/* Image Generation Section */}
                                            <li className="w-full">
                                              <h3 className="font-semibold text-gray-800 mb-3">Image Generation</h3>
                                              <ul className="space-y">
                                                <li className="flex items-center">
                                                  <span className="text-blue-600 mr-2">✓</span>
                                                  <span className="text-gray-700">
                                                    DALL-E 3:{" "}
                                                    <span className="text-gray-500">
                                                      20 images{isTablet && !allowTrialPlan ? "/mo." : "/month"}
                                                    </span>
                                                  </span>
                                                </li>
                                                <li className="flex items-center">
                                                  <span className="text-blue-600 mr-2">✓</span>
                                                  <span className="text-gray-700">
                                                    Flux:{" "}
                                                    <span className="text-gray-500">
                                                      15 images{isTablet && !allowTrialPlan ? "/mo." : "/month"}
                                                    </span>
                                                  </span>
                                                </li>
                                              </ul>
                                            </li>

                                            {/* Features Section */}
                                            <li className="w-full">
                                              <h3 className="font-semibold text-gray-800 mb-3 feat4">Features</h3>
                                              <ul className="space-y">
                                                <li className="flex items-center">
                                                  <span className="text-blue-600 mr-2">✓</span>
                                                  <span className="text-gray-700">
                                                    Dialogue Limit:{" "}
                                                    <span className="text-gray-500 samppp">
                                                      500,000 tokens{isTablet && !allowTrialPlan ? "" : "/month"}
                                                    </span>
                                                  </span>
                                                </li>
                                                {allowTrialPlan && (
                                                  <li className="flex items-center">
                                                    <span className="text-blue-600 mr-2">✓</span>
                                                    <span className="text-gray-500">{plan.trial_days}-Day Trial, then only {getPricePlan(plan.currency, plan.price)}/month</span>
                                                  </li>
                                                )}
                                              </ul>
                                            </li>
                                          </>
                                        )}

                                        {plan.plan_type === "Pro" && (
                                          <>
                                            {/* Language Models Section */}
                                            <li className="w-full">
                                              <h3 className="font-semibold text-gray-800 mb-3">Language Models</h3>
                                              <ul className="space-y">
                                                <li className="flex items-center">
                                                  <span className="text-blue-600 mr-2">✓</span>
                                                  <span className="text-gray-700">GPT-4o</span>
                                                </li>
                                                <li className="flex items-center">
                                                  <span className="text-blue-600 mr-2">✓</span>
                                                  <span className="text-gray-700">DeepSeek</span>
                                                </li>
                                                <li className="flex items-center">
                                                  <span className="text-blue-600 mr-2">✓</span>
                                                  <span className="text-gray-700">Grok</span>
                                                  <span className="ml-2 bg-blue-100 text-blue-600 text-xs px-2 py-0.5 rounded">
                                                    New
                                                  </span>
                                                </li>
                                                <li className="flex items-center">
                                                  <span className="text-blue-600 mr-2">✓</span>
                                                  <span className="text-gray-700">GPT-5</span>
                                                  <span className="ml-2 bg-blue-100 text-blue-600 text-xs px-2 py-0.5 rounded">
                                                    New
                                                  </span>
                                                </li>
                                                <li className="flex items-center">
                                                  <span className="text-blue-600 mr-2">✓</span>
                                                  <span className="text-gray-700">
                                                    Claude:{" "}
                                                    <span className="text-gray-500">
                                                      50k tokens{isTablet && !allowTrialPlan ? "/mo." : "/month"}
                                                    </span>
                                                  </span>
                                                </li>
                                              </ul>
                                            </li>

                                            {/* Image Generation Section */}
                                            <li className="w-full">
                                              <h3 className="font-semibold text-gray-800 mb-3">Image Generation</h3>
                                              <ul className="space-y">
                                                <li className="flex items-center">
                                                  <span className="text-blue-600 mr-2">✓</span>
                                                  <span className="text-gray-700">
                                                    DALL-E 3:{" "}
                                                    <span className="text-gray-500">
                                                      50 images{isTablet && !allowTrialPlan ? "/mo." : "/month"}
                                                    </span>
                                                  </span>
                                                </li>
                                                <li className="flex items-center">
                                                  <span className="text-blue-600 mr-2">✓</span>
                                                  <span className="text-gray-700">
                                                    Flux:{" "}
                                                    <span className="text-gray-500">
                                                      30 images{isTablet && !allowTrialPlan ? "/mo." : "/month"}
                                                    </span>
                                                  </span>
                                                </li>
                                              </ul>
                                            </li>

                                            {/* Video Generation Section */}
                                            <li className="w-full">
                                              <h3 className="font-semibold text-gray-800 mb-3">Video Generation</h3>
                                              <ul className="space-y">
                                                <li className="flex items-center">
                                                  <span className="text-blue-600 mr-2">✓</span>
                                                  <span className="text-gray-700">
                                                    KlingAI:{" "}
                                                    <span className="text-gray-500">
                                                      3 videos{isTablet && !allowTrialPlan ? "/mo." : "/month"}
                                                    </span>
                                                  </span>
                                                </li>
                                              </ul>
                                            </li>

                                            {/* Features Section */}
                                            <li className="w-full">
                                              <h3 className="font-semibold text-gray-800 mb-3 feat5">Features</h3>
                                              <ul className="space-y">
                                                <li className="flex items-center">
                                                  <span className="text-blue-600 mr-2">✓</span>
                                                  <span className="text-gray-700">
                                                    Dialogue Limit:{" "}
                                                    <span className="text-gray-500">
                                                      1,000,000 tokens{isTablet && !allowTrialPlan ? "" : "/month"}
                                                    </span>
                                                  </span>
                                                </li>
                                                {allowTrialPlan && (
                                                  <li className="flex items-center">
                                                    <span className="text-blue-600 mr-2">✓</span>
                                                    <span className="text-gray-500">{plan.trial_days}-Day Trial, then only {getPricePlan(plan.currency, plan.price)}/month</span>
                                                  </li>
                                                )}
                                              </ul>
                                            </li>
                                          </>
                                        )}

                                        {plan.plan_type === "ProMax" && (
                                          <>
                                            {/* Language Models Section */}
                                            <li className="w-full">
                                              <h3 className="font-semibold text-gray-800 mb-3">Language Models</h3>
                                              <ul className="space-y">
                                                <li className="flex items-center">
                                                  <span className="text-blue-600 mr-2">✓</span>
                                                  <span className="text-gray-700">GPT-4o</span>
                                                </li>
                                                <li className="flex items-center">
                                                  <span className="text-blue-600 mr-2">✓</span>
                                                  <span className="text-gray-700">DeepSeek</span>
                                                </li>
                                                <li className="flex items-center">
                                                  <span className="text-blue-600 mr-2">✓</span>
                                                  <span className="text-gray-700">Grok</span>
                                                  <span className="ml-2 bg-blue-100 text-blue-600 text-xs px-2 py-0.5 rounded">
                                                    New
                                                  </span>
                                                </li>
                                                <li className="flex items-center">
                                                  <span className="text-blue-600 mr-2">✓</span>
                                                  <span className="text-gray-700">GPT-5</span>
                                                  <span className="ml-2 bg-blue-100 text-blue-600 text-xs px-2 py-0.5 rounded">
                                                    New
                                                  </span>
                                                </li>
                                                <li className="flex items-center">
                                                  <span className="text-blue-600 mr-2">✓</span>
                                                  <span className="text-gray-700">OpenAI o1</span>
                                                </li>
                                                <li className="flex items-center">
                                                <span className="text-blue-600 mr-2">✓</span>
                                                  <span className="text-gray-700">
                                                    Claude: <span className="text-blue-600 font-medium">Unlimited</span>
                                                  </span>
                                                </li>
                                              </ul>
                                            </li>

                                            {/* Image Generation Section */}
                                            <li className="w-full">
                                              <h3 className="font-semibold text-gray-800 mb-3">Image Generation</h3>
                                              <ul className="space-y">
                                                <li className="flex items-center">
                                                  <span className="text-blue-600 mr-2">✓</span>
                                                  <span className="text-gray-700">
                                                    DALL-E 3:{" "}
                                                    <span className="text-blue-600 font-medium">Unlimited</span>
                                                  </span>
                                                </li>
                                                <li className="flex items-center">
                                                  <span className="text-blue-600 mr-2">✓</span>
                                                  <span className="text-gray-700">
                                                    Flux:{" "}
                                                    <span className="text-gray-500">
                                                      160 images{isTablet && !allowTrialPlan ? "/mo." : "/month"}
                                                    </span>
                                                  </span>
                                                </li>
                                              </ul>
                                            </li>

                                            {/* Video Generation Section */}
                                            <li className="w-full">
                                              <h3 className="font-semibold text-gray-800 mb-3">Video Generation</h3>
                                              <ul className="space-y">
                                                <li className="flex items-center">
                                                  <span className="text-blue-600 mr-2">✓</span>
                                                  <span className="text-gray-700">
                                                    KlingAI:{" "}
                                                    <span className="text-gray-500">
                                                      15 videos{isTablet && !allowTrialPlan ? "/mo." : "/month"}
                                                    </span>
                                                  </span>
                                                </li>
                                              </ul>
                                            </li>

                                            {/* Features Section */}
                                            <li className="w-full">
                                              <h3 className="font-semibold text-gray-800 mb-3 feat6">Features</h3>
                                              <ul className="space-y">
                                                <li className="flex items-center">
                                                  <span className="text-blue-600 mr-2">✓</span>
                                                  <span className="text-gray-700">
                                                    Dialogue Limit:{" "}
                                                    <span className="text-blue-600 font-medium">Unlimited</span>
                                                  </span>
                                                </li>
                                              </ul>
                                            </li>
                                          </>
                                        )}
                                      </ul>
                                    </div>
                                  </div>
                                </div>
                                {plan.plan_type === "Pro" && !allowTrialPlan ? (
                                  <div className="absolute top-0 left-1/2 transform rounded-full min-w-[130px] max-w-[130px] max-w-768:min-w-[142px] mt-[8px] md:mt-[0px] -translate-x-1/2 md:-translate-y-1/2 bg-blue-600 text-white font-bold text-[16px] py-1 px-2 text-xs span-highlight">
                                    <HiMiniSparkles className="inline text-yellow-400" /> Most Popular{" "}
                                    <HiMiniSparkles className="inline text-yellow-400" />
                                  </div>
                                ) : null}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>

                      {/* Pagination Dots */}
                      <div className="flex justify-center mt-4 space-x-2">
                        {visiblePlans.map((_, index) => (
                          <button
                            key={index}
                            className={`h-2 w-2 rounded-full ${index === currentPlan ? "bg-blue-600" : "bg-gray-300"}`}
                            onClick={() => setCurrentPlan(index)}
                            aria-label={`Go to plan ${index + 1}`}
                          />
                        ))}
                      </div>
                    </div>
                  ) : (
                    <div className={`pricing-toggle flex flex-col max-w-768:flex-row justify-center`}>
                      {data?.map((plan, index) => (checkPlanInterval(plan) ? renderPlanCard(plan, index) : ""))}
                      {enterprise === "on" &&
                      ppg !== "46" &&
                      planInterval === "yearly" &&
                      data[0].currency === "USD" ? (
                        <>{enterpriseTab()}</>
                      ) : null}
                    </div>
                  )}
                </>
            </div>
          </div>

          {tpreviews === "on" ? (
            <>
              <Suspense fallback={null}>
                <TpReviews />
              </Suspense>
            </>
          ) : null}
        </div>
      </div>
    </div>
  )
}

export default VPrice04
