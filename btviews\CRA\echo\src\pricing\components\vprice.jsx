import React, { Suspense, useState, useEffect } from 'react';
import { motion } from "framer-motion";
import { getPricePlan, DailyPrice, PriceFormatted } from '../../core/utils/main';
import { GetCookie } from '../../core/utils/cookies';
import { hexHash, hoverDarken, useDeviceSize } from '../../core/utils/helper';
import '../css/style.css';
import { useTranslation } from 'react-i18next';
import CheckIcon from '../../assets/images/check-icon.svg';
import { HiMiniSparkles } from "react-icons/hi2";
import VPriceTrial from './vprice_trial';

const TpReviews = React.lazy(() => import('../../features/tpreviews'));

function VPrice(props) {
  const { t } = useTranslation();
  const data = props.data ? props.data : null;
  const setPricing = props.setPricing ? props.setPricing : ()=>{};
  var ppg = GetCookie("ppg") ? GetCookie("ppg") : process.env.REACT_APP_DEFAULT_PPG ? process.env.REACT_APP_DEFAULT_PPG : "14";
  var pp_ctaclr = GetCookie("pp_ctaclr") ? GetCookie("pp_ctaclr") : "1559ED";

  const ppgArrayWithToggle = ['40','48','52','97','101','109','110'];
  const showToggle = (ppgArrayWithToggle.includes(ppg) && props.showToggle) ? props.showToggle : false;
  const isShowPPg = !['40','48','52'].includes(ppg); //Can add condition weather to show or not the ppg but we are currently displaying all active
  const tpreviews = GetCookie("tp_reviews") ? GetCookie("tp_reviews") : "";
  const enterprise = GetCookie("enterprise") ? GetCookie("enterprise") : "off";
  const ptoggle = GetCookie("p_toggle") ? GetCookie("p_toggle") : "";
  const [ desc_align ] = useState(GetCookie("desc_align"));
  const [isMobile, setIsMobile] = useState(false);
  const { 
    isTablet,  
    is785to810, 
    is850to885, 
    is885to900, 
    is910to925,
    is940to965,
    is970to1005,
    is1080to1090,
    is1125to1130
  } = useDeviceSize();
  const allowTrialPlan = data && ((data[0].trial_days && data[0].trial_price) || ["03","05","11","60","62","72"].includes(ppg));
  const [isChatPDFContext, setIsChatPDFContext] = useState(false);
  // const [isMobile768, setIVsMobile768] = useState(false);

  const localesCookie = GetCookie("locales") || "en";

  useEffect(() => {
    const referrer = document.referrer;
    const parentDomain = referrer ? new URL(referrer).hostname : "";

    const allowedDomains = [
      "staging.chatpdfv2.ai-pro.org",
      "chatpdfv2.ai-pro.org"
    ];
    if (
        GetCookie("chatpdfv2modal") === "true" ||
          allowedDomains.includes(parentDomain)
        ) {
      setIsChatPDFContext(true);
    }
  }, []);

  var billedAnnualDisplay = false;

  useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth <= 729);
      // setIsMobile768(window.innerWidth <= 768);
    };

    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);

    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);

  if (ppg==='48'){
    billedAnnualDisplay = true;
  }

  if (ptoggle ==='01' || ptoggle === '03'){
    billedAnnualDisplay = true;
  }

  const [ planInterval, setPlanInterval ] = useState(billedAnnualDisplay ? "yearly" : "monthly");

  const intervalChange = function() {
    if(planInterval === "monthly") {
      setPlanInterval("yearly");
    } else {
      setPlanInterval("monthly");
    }
  };

  const basicIsHidden = (plan) => {
    return plan.plan_type === 'Basic' && isChatPDFContext && !allowTrialPlan;
  }

  const checkPlanInterval = function(plan) {
    if (basicIsHidden(plan)) return false;
    if(!showToggle) return true;
    if(plan.payment_interval.toLowerCase() === planInterval) return true;
    return false;
  }

  const isSinglePlanWillRemain = () => {
    if (!data || data.length > 2) return false;

    const rem = data.length % 4; //Means 2 monthly and 2 yearly
    return data.length === 1 || ((data.length === 2 || rem === 0) && basicIsHidden(data[0]));
  }

  const shouldDisplayAllPlans = () => {
    if (!data) return false;

    return data.length >= 2 && !basicIsHidden(data[0]);      
  }

  const enterpriseTab = function() {
    return (

        <div className=" w-full lg:w-[330px] xl:w-[380px] text-center px-4 mb-8 relative">
          <div className="bg-white rounded-lg shadow-lg overflow-hidden">
            <div className="px-6 py-20 md:py-10 price-content">
              <h3 className="text-2xl font-bold mb-4">{t('echo.pricing.vprice_02.enterprise.title')}</h3>
              <p className={`text-4xl font-bold text-[#4285F4] ${planInterval === 'yearly' && ppg === '48'  ? "" :"mb-4"}`}>{t('echo.pricing.vprice_02.enterprise.price')}</p>
              { planInterval === 'yearly' && ppg === '48' && GetCookie("daily") !== 'on' ? <div className='text-xs mb-4'>(billed yearly)</div> : '' }
              <div className='py-4'>
                <motion.button
                  className="text-white font-bold py-3 px-3 rounded-lg"
                  style={{backgroundColor: hexHash(pp_ctaclr)}}
                  whileHover={{ scale: 1.05, backgroundColor: hoverDarken(pp_ctaclr) }}
                  whileTap={{ scale: 0.9 }}
                  onClick={() => setPricing(62)}
                >
                  {t('echo.pricing.vprice_02.enterprise.cta')}
                </motion.button>
              </div>
              <div className={`mb-6 pricing-description ${desc_align === 'left' ? 'text-left' : 'text-center'} ppg-${ppg}`}>
                <ul className="text-sm text-gray-600">
                  <li className="mb-2 font-bold"><div>{t('echo.pricing.vprice_02.enterprise.desc1')}</div></li>
                  <li className=""><div>{t('echo.pricing.vprice_02.enterprise.desc2')}</div></li>
                </ul>
              </div>

            </div>
          </div>
        </div>
    );
  };

  const trialPlanUseCustomComponent = false; //Set to true if want to use the separate component 
  if (allowTrialPlan && trialPlanUseCustomComponent) {
    return (
      <VPriceTrial
        data={data}
        checkPlanInterval={checkPlanInterval}
        getPricePlan={getPricePlan}
        planInterval={planInterval}
        ppg={ppg}
        billedAnnualDisplay={billedAnnualDisplay}
        ptoggle={ptoggle}
        pp_ctaclr={pp_ctaclr}
        enterpriseTab={enterpriseTab}
        enterprise={enterprise}
        tpreviews={tpreviews}
        setPricing={setPricing}
        isMobile={isMobile}
        desc_align={desc_align}
        CheckIcon={CheckIcon}
        TpReviews={TpReviews}
      />
    )
  }

  const isSinglePlan = isSinglePlanWillRemain();
  const isDoublePlan = data && (data.length === 2 || (data.length % 4 === 0) || ((data.length % 3 === 0) && basicIsHidden(data[0]))); //4 Means 2 monthly and 2 yearly
  const isShouldDisplayAllPlans = shouldDisplayAllPlans();
  const isPlanPro = (ndx) => {
    return data[ndx].plan_type === 'Pro' || data[ndx].plan_type.toLowerCase().startsWith('pro');
  };
  const isRemainedSinglePlanPro = isSinglePlan && isPlanPro(0);
  const isExtendedTrialPlanDetail = localesCookie !== 'en' && 
  (
    ['pl'].includes(localesCookie) || 
    (is785to810 && !['it'].includes(localesCookie)) ||
    (!is940to965 && !is785to810 && ['pt'].includes(localesCookie)) ||
    ((is850to885 || is885to900) && ['de'].includes(localesCookie)) ||
    (is850to885 && ['fr'].includes(localesCookie))
  );
  //const noBasicPlan = data.filter(pl => pl.plan_type.toLowerCase() === 'basic');
  const isChatPDFContextFull = isChatPDFContext;// && noBasicPlan.length <= 0;
  const isUseChatPdfContextDesign = !isChatPDFContextFull && isDoublePlan;
  const dallETitleHeightExtended = ['es','it'].includes(localesCookie) && !allowTrialPlan && !isSinglePlan && !isMobile && !is1080to1090 && !is970to1005 && !is1125to1130;

  //Below left padding only for trial or single plan display (will only work for allowTrialPlan and isSinglePlan)
  let plval = 20;
  if(is785to810) {
    switch (localesCookie) {
      case 'de': plval = 18; break;
      case 'es': 
      case 'it': 
        plval = 26; 
        break;
      case 'fr': plval = 23; break;
      case 'pl': 
      case 'pt': 
        plval = 25; 
        break;
      case 'tr': plval = 22; break;
      default:
    }
  } else if(is940to965) {
    switch (localesCookie) {
      case 'de': plval = 15; break;
      case 'es':
      case 'it': 
        plval = 23; break;
      case 'tr': plval = 18; break;
      case 'en': plval = 17; break;
      default:
    }  
  } else if(is910to925) {
    switch (localesCookie) {
      case 'de': plval = 15; break;
      case 'es':
      case 'it': 
        plval = 23; break;
      case 'tr': plval = 18; break;
      case 'en': plval = 17; break;
      default:
    }
  } else if(is850to885) {
    switch (localesCookie) {
      case 'de': plval = 16; break;
      case 'es':
      case 'it': 
        plval = 24; break;
      case 'fr': 
      case 'pl':
      case 'pt': 
        plval = 22; break;
      default:
    }  
  } else if(is885to900) {
    switch (localesCookie) {
      case 'en': plval = 18; break;
      case 'de': plval = 16; break;
      case 'es':
      case 'it': 
        plval = 24; break;
      case 'pl':
      case 'pt': 
        plval = 22; break;
      case 'tr': plval = 19; break;
      default:
    }
  }

  let langModelsTextPos = 'mt-[59px]';
  switch (localesCookie) {
    case 'en':
      langModelsTextPos = `${langModelsTextPos} min-970-max-1005:mt-[66px]`;
      break;
    case 'de':
      langModelsTextPos = `${langModelsTextPos} min-1080-max-1090:mt-[75px] min-970-max-1005:mt-[81px] min-1125-max-1130:mt-[77px]`;
      break;  
    case 'es':
      langModelsTextPos = `${langModelsTextPos} min-w-1300:mt-[35px] min-970-max-1005:mt-[81px]`;
      break;
    case 'fr':
      langModelsTextPos = `${langModelsTextPos} min-970-max-1005:mt-[66px]`;
      break;
    case 'it':
    case 'pl':
    case 'pt':
    case 'tr':
      langModelsTextPos = `${langModelsTextPos} min-970-max-1005:mt-[64px]`;
      break;
    default:  
  }

  const getPricingText = () => {
    if(GetCookie('appName')) {
      const text = t('echo.pricing.vprice.pricingText');
      return text.replace("ChatBot Pro", GetCookie('appName'));
    }
    return isChatPDFContext ? t('echo.pricing.vprice.pricingTextPDF') : t('echo.pricing.vprice.pricingText');
  }
  
  return (
    <div className="v-pricing pricing bg-white md:min-h-[90vh] ">
      <div className={`pricing_columns mx-auto ${allowTrialPlan || isSinglePlan ? "mt-[10px]" : ""} ${(allowTrialPlan || isSinglePlan) && !isMobile ? 'pl-[20px]' : ''}`}>
        <div className="w-full">

          <div className='pricing_header'>
            <div className={`flex justify-center ${allowTrialPlan || isSinglePlan || (!showToggle && isShouldDisplayAllPlans) ? "mb-5" : ""}`} 
              style={(allowTrialPlan || isSinglePlan) && !isMobile ? { paddingLeft: `${plval}%` } : {}}>
              <div className='flex flex-col'>
            <h1 className="text-4xl text-[#336CEB] lg:text-4xl font-bold text-center mb-4 min-h-[40px]">
              {t('echo.pricing.vprice.pricingPlanText')}
            </h1>
                <div className={`text-center text-[18px] ${(isChatPDFContextFull || isUseChatPdfContextDesign) && !isMobile ? 'max-w-500:w-[235px]' : ''}`}>{getPricingText()}</div>
              </div>
            </div>
            {showToggle && (
              <div className="p-4">
                <div className="text-1xl lg:text-1xl font-bold text-center mb-4">
                </div>
                <div className="flex items-center justify-center w-full mb-8">
                  <label htmlFor="toggleB" className="flex items-center cursor-pointer">
                    <div className={`${planInterval === 'monthly' ? "text-gray-800 font-bold" : "text-gray-700"} mr-3 uppercase`}>
                    {t('echo.pricing.vprice.monthlyText')}
                    </div>
                    <div className="relative">
                      <input
                        type="checkbox"
                        id="toggleB"
                        className="sr-only toggle"
                        onChange={intervalChange}
                        defaultChecked={billedAnnualDisplay}
                      />
                      <div className="block bg-gradient-to-r from-[#268FED] to-[#3D56BA] w-12 h-6 rounded-full"></div>
                      <div
                        className={`dot absolute top-1 bg-white w-4 h-4 rounded-full transition-all duration-300 ease-in-out ${planInterval === 'yearly' ? 'left-[0.3rem]' : 'left-1'
                          }`}
                      ></div>
                    </div>
                    <div className={`${planInterval === 'yearly' ? "text-gray-800 font-bold" : "text-gray-700"} ml-3 uppercase`}>
                      {t('echo.pricing.vprice.yearlyText')}
                    </div>
                  </label>
                </div>
              </div>
            )}
          </div>
          <div className={`flex flex-col max-w-768:flex-row justify-center items-start ${(isChatPDFContextFull && isDoublePlan && !is970to1005) || isUseChatPdfContextDesign ? 'lg:ml-[5%]' : ''}`}>
            <div className={`hidden max-w-768:block pricing_details lg:w-[20%] space-y-8 ${isChatPDFContextFull || isUseChatPdfContextDesign ? 'min-w-1370:ml-[0.5rem]' : ''} pl-6`}>
              <div className={`${allowTrialPlan || isSinglePlan ? 'pt-[37px]' : 'pt-[45px]'} pb-[50px] md:pb-[0px] lg:pb-[12px] xl:pb-[50px] min-w-1164:w-[175px]`}>
                {!allowTrialPlan && !isSinglePlan ? (
                  <>
                    <h3 className="font-bold mb-4 ">{t('echo.pricing.vprice.comparePlansText')}</h3>
                    <p className="text-sm sm:text-xs lg:text-sm text-gray-600">{t('echo.pricing.vprice.aiDescriptionText1')}<br/>{t('echo.pricing.vprice.aiDescriptionText2')}</p>
                  </>) : ( <div className={`md:mb-[180px] sm:mb-[150px] ${(allowTrialPlan || (isSinglePlan && !isRemainedSinglePlanPro)) ? "" : "lg:mb-[195px]"}`}></div> )
                }  
                {/* <h3 className="font-bold mb-4 sm:text-sm sm:mt-[58px] max-w-768:mt-[68px] md:mt-[70px] lg:mt-[73px] xl:mt-[112px]">Language Models</h3> */}
                <h3 className={`font-bold mb-4 sm:text-sm   ${allowTrialPlan || isSinglePlan ? 'sm:mt-[58px] md:mt-[70px] lg:mt-[73px] max-w-768:mt-[68px]' : langModelsTextPos } `}>{t('echo.pricing.vprice.languageModels')}</h3>
                <ul className="space-y-4 max-w-768:text-sm md:text-base lg:mb-[13px]">
                  <li className='h-[24px]'>GPT-4o</li>
                  <li className='h-[24px]'>
                    DeepSeek
                  </li>
                  <li className='h-[24px]'>
                    Grok
                    <div className="inline px-2 ml-[2px] text-white bg-[#4285F4] rounded-xl text-[12px]">
                      {t('echo.pricing.vprice.newBadgeText')}
                    </div>
                  </li>
                  <li className='h-[24px]'>
                    GPT-5
                    <div className="inline px-2 ml-[2px] text-white bg-[#4285F4] rounded-xl text-[12px]">
                      {t('echo.pricing.vprice.newBadgeText')}
                    </div>
                  </li>
                  <li className='h-[24px]'>OpenAI o1</li>
                  <li className='h-[24px]'>Claude</li>
                </ul>
                <h3 className={`font-bold mb-4 mt-2 max-w-768:text-sm md:text-base ${dallETitleHeightExtended ? 'h-[40px]' : 'h-[24px]'}`}>{t('echo.pricing.vprice.imageGeneration')}</h3>
                <ul className="space-y-4 mb-[17px] max-w-768:text-sm md:text-base ">
                  <li className='h-[24px]'>DALL-E 3</li>
                  <li className='h-[24px]'>Flux</li>
                </ul>
                <h3 className="font-bold mb-4 mt-2 max-w-768:text-sm md:text-base h-[24px]">{t('echo.pricing.vprice.videoGeneration')}</h3>
                <ul className="space-y-4 mb-[17px] max-w-768:text-sm md:text-base ">
                  <li className='h-[24px]'>KlingAI</li>
                </ul>
                <h3 className="font-bold mb-6 max-w-768:text-sm md:text-base ">{t('echo.pricing.vprice.features')}</h3>
                <ul className="space-y-4 max-w-768:text-sm md:text-base ">
                  <li className='h-[24px]'>{t('echo.pricing.vprice.dialogueLimitText')}</li>
                  {allowTrialPlan && <li className='h-[24px]'>{t('echo.pricing.vprice.trialPeriodText')}</li>}
                </ul>
              </div>
            </div>

            <div className={`pricing_columns w-full ${allowTrialPlan || isSinglePlan ? "md:w-[60%] sm:px-[5%]" : "lg:w-[78%]"}`}>
              { isShowPPg ? (
                <div className={`pricing-toggle flex flex-col max-w-768:flex-row justify-center`}>


                  {data?.map((plan, index) => (
                    checkPlanInterval(plan) ? (
                      <div key={index} className={` w-full lg:w-[340px] xl:w-[410px] text-center sm:px-1 mb-8 relative`}>
                        <div className={`rounded ${(index === 1 || index === 4) ? 'bg-[#F5F5F5]' : 'bg-gradient-to-r from-gray-50 to-[#FBFBFB]' } h-full`}>
                        <div className={`px-2 ${planInterval === 'yearly' ? 'sm:px-2' :'sm:px-4'} py-10 price-content`}>
                            <h3 className={`text-2xl font-bold mb-4 lg:text-2xl ${!allowTrialPlan || isSinglePlan ? '' : 'sm:text-[1.07rem]'}`}>{localesCookie !== 'en' && allowTrialPlan ? t('echo.pricing.vprice.localesLabel') : plan.label}</h3>
                            {GetCookie("daily") === 'on' ? (<DailyPrice plan={plan}/>) :
                              (billedAnnualDisplay && plan.payment_interval === 'Yearly' && (ppg === '48' || ptoggle === '02') ?
                                (
                                  <p className={`text-4xl font-bold text-[#4285F4] ${ppg === '48' && ptoggle === '01' ? 'mb-4' : ''}`}>
                                    {getPricePlan(plan.currency, parseFloat(plan.price / 12).toFixed(2))}
                                    <span className="text-lg text-gray-400"> /month</span>
                                  </p>
                                ) : plan.trial_price ?
                                  (
                                    <p className="text-4xl font-bold text-[#4285F4]">
                                      {getPricePlan(plan.currency, plan.trial_price)}
                                    </p>
                                  ) :
                                  (
                                    <p className={`text-2xl lg:text-4xl font-bold text-[#4285F4] mb-4 ${(ptoggle === '02' || ptoggle === '03') ? '' : ''}`}>
                                      {(ptoggle === '02' || ptoggle === '03') && plan.payment_interval === "Yearly"
                                        ? getPricePlan(plan.currency, parseFloat(plan.price / 12).toFixed(2))
                                        : getPricePlan(plan.currency, plan.price)}
                                      <span className="text-lg text-gray-400">
                                        /{(ptoggle === '02' || ptoggle === '03') && plan.payment_interval === "Yearly"
                                          ? ` ${t('echo.pricing.vprice.monthText')}`
                                          : plan.payment_interval === "Monthly"
                                            ? ` ${t('echo.pricing.vprice.monthText')}`
                                            : ` ${t('echo.pricing.vprice.yearText')}`}
                                      </span>
                                    </p>
                                  )
                              )
                            }
                            <motion.button
                              className={`block text-white font-bold py-3 mb-4 rounded-2xl text-[18px] sm:text-[16px] lg:text-[18px] px-3 lg:px-6 max-w-768:rounded-full bg-gradient-to-r from-[#3D56BA] to-[#268FED] w-full mb-[50px] max-w-768:mb-[24px] sm-[26px] md:mb-[30px] lg:mb-[40px] ${allowTrialPlan || isSinglePlan ? "mt-[10px]" : ""}`}
                              style={{backgroundColor: hexHash(pp_ctaclr)}}
                              whileHover={{ scale: 1.05, backgroundColor: hoverDarken(pp_ctaclr) }}
                              whileTap={{ scale: 0.9 }}
                              onClick={() => setPricing(plan.plan_id)}
                            >
                              {isTablet && !allowTrialPlan ? 'Choose This Plan' : t('echo.pricing.vprice.chooseThisPlanText')}
                            </motion.button>
                            {(ptoggle === "02" || ptoggle === "03") && plan.payment_interval === 'Yearly' && GetCookie("daily") !== 'on' && (
                              <span className={`absolute text-lg text-gray-400 text-gray-600 mb-4 absolute left-[0] right-[0] mt-[-20px]  ${ptoggle === "03" ? "lg:mt-[-8px]" : "lg:mt-[-30px]"}`}>
                                or {getPricePlan(plan.currency, plan.price)} annual
                              </span>
                            )}
                            {(
                              (billedAnnualDisplay && plan.payment_interval === 'Yearly' && !['01', '02', '03'].includes(ptoggle) && GetCookie("daily") !== 'on') ||
                              (!['01', '02', '03'].includes(ptoggle) && ppg === 48 && GetCookie("daily") !== 'on')
                            ) && (
                                <div className='text-xs mb-4'>(billed yearly)</div>
                              )}
                            <div className={`mb-6 ${(ptoggle === '02' || ptoggle === '03') ? 'mt-4' : ''} pricing-description ${desc_align === 'left' ? 'text-left' : 'text-center'} ppg-${ppg}`}>

                              <ul className="text-sm text-gray-600 space-y-4 ">
                                {plan.plan_type === 'Basic' && (
                                  <>
                                    <li className="flex items-center gap-2 justify-center text-[16px] h-[24px]"></li>
                                    <li className="flex items-center gap-2 justify-center text-[16px] h-[24px]">{isMobile && 'GPT-4o: '}<span className='text-[#3C57BB] font-bold'> <img src={CheckIcon} alt="Infinite" className="mx-auto w-[22px]"/></span></li>
                                    <li className="flex items-center gap-2 justify-center text-[16px] h-[24px]">{isMobile && 'DeepSeek:'}<span className='text-[#3C57BB] font-bold'> <img src={CheckIcon} alt="Infinite" className="mx-auto w-[22px]"/></span></li>
                                    {!isMobile && (
                                      <>
                                      <li className="flex items-center gap-2 justify-center text-[16px] h-[24px]">-</li>
                                      <li className="flex items-center gap-2 justify-center text-[16px] h-[24px]">-</li>
                                      <li className="flex items-center gap-2 justify-center text-[16px] h-[24px]">-</li>
                                      </>
                                    )}
                                    <li className="flex items-center gap-2 justify-center text-[16px] h-[24px]">
                                      {isMobile && 'Claude: '}
                                      {localesCookie !== 'en' && allowTrialPlan ? t('echo.pricing.vprice.claudeAmtText') : '25k tokens'} 
                                      {localesCookie === 'en' ? ((isTablet || isMobile) && !allowTrialPlan ? (GetCookie('promo') ? '/month' : '/mo.') : '/month') : null}</li>
                                    <li className={`flex items-center gap-2 justify-center text-[16px] ${dallETitleHeightExtended ? 'h-[40px]' : 'h-[24px]'}`}></li>
                                    <li className="flex items-center gap-2 justify-center text-[16px] h-[24px]">
                                      {isMobile && 'DALL-E 3: '}
                                      {localesCookie !== 'en' && allowTrialPlan ? t('echo.pricing.vprice.dalle3AmtText') : '20 images'} 
                                      {localesCookie === 'en' ? ((isTablet || isMobile) && !allowTrialPlan ? (GetCookie('promo') ? '/month' : '/mo.') : '/month') : null}
                                    </li>
                                    <li className="flex items-center gap-2 justify-center text-[16px] h-[24px]">
                                      {isMobile && 'Flux: '}
                                      {localesCookie !== 'en' && allowTrialPlan ? t('echo.pricing.vprice.fluxAmtText') : '15 images'} 
                                      {localesCookie === 'en' ? ((isTablet || isMobile) && !allowTrialPlan ? (GetCookie('promo') ? '/month' : '/mo.') : '/month') : null}
                                    </li>
                                    <li className="flex items-center gap-2 justify-center text-[16px] h-[24px]"></li>
                                    {!isMobile && (
                                      <>
                                        <li className="flex items-center gap-2 justify-center text-[16px] h-[24px]">-</li>
                                        <li className="flex items-center gap-2 justify-center text-[16px] h-[24px]"></li>
                                      </>
                                    )}
                                    <li className={`flex items-center gap-2 justify-center text-[16px] ${isMobile && !isTablet ? 'h-[40px]' : 'h-[24px]'}`}>
                                      {isMobile && `${t('echo.pricing.vprice.dialogueLimitText')}: `}
                                      {localesCookie !== 'en' && allowTrialPlan ? t('echo.pricing.vprice.dialogueLimitAmtText') : '500,000 tokens'}
                                      {(isTablet || isMobile) && !allowTrialPlan ? (GetCookie('promo') ? '/month' : '/mo.') : (localesCookie === 'en' ? '/month' : '')}
                                    </li>
                                    {allowTrialPlan && <li className={`flex items-center gap-2 justify-center text-[16px] ${(isMobile && !isTablet) || isExtendedTrialPlanDetail ? 'h-[40px]' : 'h-[24px]'}`}>
                                      {isMobile && `${t('echo.pricing.vprice.trialPeriodText')}: `}
                                      {localesCookie !== 'en' && allowTrialPlan ? 
                                      (localesCookie === 'it' || localesCookie === 'pt' || localesCookie === 'en' || localesCookie === 'fr' || localesCookie === 'tr'  || localesCookie === 'de'  ? 
                                        t('echo.pricing.vprice.trialPeriodAmtText1')
                                        :
                                        (localesCookie === 'es' ? 
                                          t('echo.pricing.vprice.trialPeriodAmtText1') + `${plan?.currency_symbol+plan?.price}` + t('echo.pricing.vprice.trialPeriodAmtText2') 
                                            : 
                                            (localesCookie === 'pl' ?
                                              (ppg === "72" ?
                                                t('echo.pricing.vprice.trialPeriodAmtText1') + `${plan?.price+plan?.currency_symbol}` + t('echo.pricing.vprice.trialPeriodAmtText2')
                                                :
                                                t('echo.pricing.vprice.trialPeriodAmtText1') + `${plan?.currency_symbol+plan?.price}` + t('echo.pricing.vprice.trialPeriodAmtText2')
                                              )
                                              :
                                              `${plan?.label}, then ${getPricePlan(plan.currency, plan.price)}/month`
                                            ))
                                      ) : `${plan?.label}, then ${getPricePlan(plan.currency, plan.price)}/month`}
                                    </li>}
                                  </>
                                )}
                                {(plan.plan_type === 'Pro') && (
                                  <>
                                    <li className={`flex items-center gap-2 justify-center text-[16px] ${false ? 'min-w-1315:mb-[-7px] min-w-1315:h-0 min-w-1204:h-[12px] min-w-995:h-[32px] h-[22px]' : 'h-[24px]'}`}></li>
                                    <li className="flex items-center gap-2 justify-center text-[16px] h-[24px]">{isMobile && 'GPT-4o: '}<span className='text-[#3C57BB] font-bold'> <img src={CheckIcon} alt="Infinite" className="mx-auto w-[22px]"/></span></li>
                                    <li className="flex items-center gap-2 justify-center text-[16px] h-[24px]">{isMobile && 'DeepSeek: '}<span className='text-[#3C57BB] font-bold'> <img src={CheckIcon} alt="Infinite" className="mx-auto w-[22px]"/></span></li>
                                    <li className="flex items-center gap-2 justify-center text-[16px] h-[24px]">{isMobile && 'Grok: '}<span className='text-[#3C57BB] font-bold'> <img src={CheckIcon} alt="Infinite" className="mx-auto w-[22px]"/></span></li>
                                    <li className="flex items-center gap-2 justify-center text-[16px] h-[24px]">{isMobile && 'GPT-5: '}<span className='text-[#3C57BB] font-bold'> <img src={CheckIcon} alt="Infinite" className="mx-auto w-[22px]"/></span></li>
                                    {!isMobile && (
                                      <>
                                      <li className="flex items-center gap-2 justify-center text-[16px] h-[24px]">-</li>
                                      </>
                                    )}
                                    <li className="flex items-center gap-2 justify-center text-[16px] h-[24px]">
                                      {isMobile && 'Claude: '}
                                      {localesCookie !== 'en' && allowTrialPlan ? t('echo.pricing.vprice.claudeAmtText') : '50k tokens'} 
                                      {localesCookie === 'en' ? ((isTablet || isMobile) && !allowTrialPlan ? (GetCookie('promo') ? '/month' : '/mo.') : '/month') : null}
                                    </li>
                                    <li className={`flex items-center gap-2 justify-center text-[16px] ${dallETitleHeightExtended ? 'h-[40px]' : 'h-[24px]'}`}></li>
                                    <li className="flex items-center gap-2 justify-center text-[16px] h-[24px]">
                                      {isMobile && 'DALL-E 3: '}
                                      {localesCookie !== 'en' && allowTrialPlan ? t('echo.pricing.vprice.dalle3AmtText') : '50 images'} 
                                      {localesCookie === 'en' ? ((isTablet || isMobile) && !allowTrialPlan ? (GetCookie('promo') ? '/month' : '/mo.') : '/month') : null}
                                    </li>
                                    <li className="flex items-center gap-2 justify-center text-[16px] h-[24px]">
                                      {isMobile && 'Flux: '}
                                      {localesCookie !== 'en' && allowTrialPlan ? t('echo.pricing.vprice.fluxAmtText') : '30 images'} 
                                      {localesCookie === 'en' ? ((isTablet || isMobile) && !allowTrialPlan ? (GetCookie('promo') ? '/month' : '/mo.') : '/month') : null}
                                    </li>
                                    <li className="flex items-center gap-2 justify-center text-[16px] h-[24px]"></li>
                                    <li className="flex items-center gap-2 justify-center text-[16px] h-[24px]">
                                      {isMobile && 'KlingAI: '}
                                      {localesCookie !== 'en' && allowTrialPlan ? t('echo.pricing.vprice.klingAIAmtText') : '3 videos'} 
                                      {localesCookie === 'en' ? ((isTablet || isMobile) && !allowTrialPlan ? (GetCookie('promo') ? '/month' : '/mo.') : '/month') : null}
                                    </li>
                                    <li className={`flex items-center gap-2 justify-center text-[16px] ${isChatPDFContextFull && !isSinglePlan ? 'h-[28px]' : 'h-[24px]'}`}></li>
                                    <li className={`flex items-center gap-2 justify-center text-[16px] ${isMobile && !isTablet ? 'h-[40px]' : 'h-[24px]'}`}>
                                      {isMobile && `${t('echo.pricing.vprice.dialogueLimitText')}: `}
                                      {localesCookie !== 'en' && allowTrialPlan ? t('echo.pricing.vprice.dialogueLimitAmtText') : '1,000,000 tokens'} 
                                      {(isTablet || isMobile) && !allowTrialPlan ? (GetCookie('promo') ? '/month' : '/mo.') : (localesCookie === 'en' ? '/month' : '')}
                                    </li>
                                    {allowTrialPlan && <li className={`flex items-center gap-2 justify-center text-[16px] ${(isMobile && !isTablet) || isExtendedTrialPlanDetail ? 'h-[40px]' : 'h-[24px]'}`}>
                                      {isMobile && `${t('echo.pricing.vprice.trialPeriodText')}: `}
                                      {localesCookie !== 'en' && allowTrialPlan ? 
                                      (localesCookie === 'it' || localesCookie === 'pt' || localesCookie === 'en' || localesCookie === 'fr' || localesCookie === 'tr'  || localesCookie === 'de'  ? 
                                        t('echo.pricing.vprice.trialPeriodAmtText1')
                                        :
                                        (localesCookie === 'es' ? 
                                          t('echo.pricing.vprice.trialPeriodAmtText1') + `${plan?.currency_symbol+plan?.price}` + t('echo.pricing.vprice.trialPeriodAmtText2')
                                            : 
                                            (localesCookie === 'pl' ?
                                              (ppg === "72" ?
                                                t('echo.pricing.vprice.trialPeriodAmtText1') + `${plan?.price+plan?.currency_symbol}` + t('echo.pricing.vprice.trialPeriodAmtText2')
                                                :
                                                t('echo.pricing.vprice.trialPeriodAmtText1') + `${plan?.currency_symbol+plan?.price}` + t('echo.pricing.vprice.trialPeriodAmtText2')
                                              )
                                              :
                                              `${plan?.label}, then ${getPricePlan(plan.currency, plan.price)}/month`
                                            ))
                                      ) : `${plan?.label}, then ${getPricePlan(plan.currency, plan.price)}/month`}
                                    </li>}
                                  </>
                                )}
                                {(plan.plan_type === 'Advanced') && (
                                  <>
                                    <li className={`flex items-center gap-2 justify-center text-[16px] ${false ? 'min-w-1315:mb-[-7px] min-w-1315:h-0 min-w-1204:h-[12px] min-w-995:h-[32px] h-[22px]' : 'h-[24px]'}`}></li>
                                    {!isMobile && <li className="flex items-center gap-2 justify-center text-[16px] h-[24px]">-</li>}
                                    <li className="flex items-center gap-2 justify-center text-[16px] h-[24px]">{isMobile && 'DeepSeek: '}<span className='text-[#3C57BB] font-bold'> <img src={CheckIcon} alt="Infinite" className="mx-auto w-[22px]"/></span></li>
                                    {!isMobile && <li className="flex items-center gap-2 justify-center text-[16px] h-[24px]">-</li>}
                                    <li className="flex items-center gap-2 justify-center text-[16px] h-[24px]">{isMobile && 'GPT-5: '}<span className='text-[#3C57BB] font-bold'> <img src={CheckIcon} alt="Infinite" className="mx-auto w-[22px]"/></span></li>
                                    {!isMobile && <li className="flex items-center gap-2 justify-center text-[16px] h-[24px]">-</li>}
                                    <li className="flex items-center gap-2 justify-center text-[16px] h-[24px]">
                                      {isMobile && 'Claude: '}
                                      {localesCookie !== 'en' && allowTrialPlan ? t('echo.pricing.vprice.claudeAmtText') : '50k tokens'} 
                                      {localesCookie === 'en' ? ((isTablet || isMobile) && !allowTrialPlan ? (GetCookie('promo') ? '/month' : '/mo.') : '/month') : null}
                                    </li>
                                    <li className={`flex items-center gap-2 justify-center text-[16px] ${dallETitleHeightExtended ? 'h-[40px]' : 'h-[24px]'}`}></li>
                                    <li className="flex items-center gap-2 justify-center text-[16px] h-[24px]">
                                      {isMobile && 'DALL-E 3: '}
                                      {localesCookie !== 'en' && allowTrialPlan ? t('echo.pricing.vprice.dalle3AmtTextAdv') : '80 images'} 
                                      {localesCookie === 'en' ? ((isTablet || isMobile) && !allowTrialPlan ? (GetCookie('promo') ? '/month' : '/mo.') : '/month') : null}
                                    </li>
                                    <li className="flex items-center gap-2 justify-center text-[16px] h-[24px]">
                                      {isMobile && 'Flux: '}
                                      {localesCookie !== 'en' && allowTrialPlan ? t('echo.pricing.vprice.fluxAmtTextAdv') : '80 images'} 
                                      {localesCookie === 'en' ? ((isTablet || isMobile) && !allowTrialPlan ? (GetCookie('promo') ? '/month' : '/mo.') : '/month') : null}
                                    </li>
                                    <li className="flex items-center gap-2 justify-center text-[16px] h-[24px]"></li>
                                    <li className="flex items-center gap-2 justify-center text-[16px] h-[24px]">
                                      {isMobile && 'KlingAI: '}
                                      {localesCookie !== 'en' && allowTrialPlan ? t('echo.pricing.vprice.klingAIAmtTextAdv') : '7 videos'} 
                                      {localesCookie === 'en' ? ((isTablet || isMobile) && !allowTrialPlan ? (GetCookie('promo') ? '/month' : '/mo.') : '/month') : null}
                                    </li>
                                    <li className={`flex items-center gap-2 justify-center text-[16px] ${isChatPDFContextFull && !isSinglePlan ? 'h-[28px]' : 'h-[24px]'}`}></li>
                                    <li className={`flex items-center gap-2 justify-center text-[16px] ${isMobile && !isTablet ? 'h-[40px]' : 'h-[24px]'}`}>
                                      {isMobile && `${t('echo.pricing.vprice.dialogueLimitText')}: `}
                                      {localesCookie !== 'en' && allowTrialPlan ? t('echo.pricing.vprice.dialogueLimitAmtTextAdv') : '2,500,000 tokens'} 
                                      {isTablet && !allowTrialPlan ? (GetCookie('promo') ? '/month' : '/mo.') : (localesCookie === 'en' ? '/month' : '')}
                                    </li>
                                    {allowTrialPlan && <li className={`flex items-center gap-2 justify-center text-[16px] ${(isMobile && !isTablet) || isExtendedTrialPlanDetail ? 'h-[40px]' : 'h-[24px]'}`}>
                                      {isMobile && `${t('echo.pricing.vprice.trialPeriodText')}: `}
                                      {localesCookie !== 'en' && allowTrialPlan ? 
                                      (localesCookie === 'it' || localesCookie === 'pt' || localesCookie === 'en' || localesCookie === 'fr' || localesCookie === 'tr'  || localesCookie === 'de'  ? 
                                        t('echo.pricing.vprice.trialPeriodAmtText1')
                                        :
                                        (localesCookie === 'es' ? 
                                          t('echo.pricing.vprice.trialPeriodAmtText1') + `${plan?.currency_symbol+plan?.price}` + t('echo.pricing.vprice.trialPeriodAmtText2')
                                            : 
                                            (localesCookie === 'pl' ?
                                              (ppg === "72" ?
                                                t('echo.pricing.vprice.trialPeriodAmtText1') + `${plan?.price+plan?.currency_symbol}` + t('echo.pricing.vprice.trialPeriodAmtText2')
                                                :
                                                t('echo.pricing.vprice.trialPeriodAmtText1') + `${plan?.currency_symbol+plan?.price}` + t('echo.pricing.vprice.trialPeriodAmtText2')
                                              )
                                              :
                                              `${plan?.label}, then ${getPricePlan(plan.currency, plan.price)}/month`
                                            ))
                                      ) : `${plan?.label}, then ${getPricePlan(plan.currency, plan.price)}/month`}
                                    </li>}
                                  </>
                                )}
                                {plan.plan_type === 'ProMax' && (
                                  <>
                                    <li className={`flex items-center gap-2 justify-center text-[16px] ${false ? 'min-w-1315:mb-[-7px] min-w-1315:h-0 min-w-1204:h-[12px] min-w-995:h-[32px] h-[22px]' : 'h-[24px]'}`}></li>
                                    <li className="flex items-center gap-2 justify-center text-[16px] h-[24px]">{isMobile && 'GPT-4o: '}<span className='text-[#3C57BB] font-bold'> <img src={CheckIcon} alt="Infinite" className="mx-auto w-[22px]"/></span></li>
                                    <li className="flex items-center gap-2 justify-center text-[16px] h-[24px]">{isMobile && 'DeepSeek: '}<span className='text-[#3C57BB] font-bold'> <img src={CheckIcon} alt="Infinite" className="mx-auto w-[22px]"/></span></li>
                                    <li className="flex items-center gap-2 justify-center text-[16px] h-[24px]">{isMobile && 'Grok: '}<span className='text-[#3C57BB] font-bold'> <img src={CheckIcon} alt="Infinite" className="mx-auto w-[22px]"/></span></li>
                                    <li className="flex items-center gap-2 justify-center text-[16px] h-[24px]">{isMobile && 'GPT-5: '}<span className='text-[#3C57BB] font-bold'> <img src={CheckIcon} alt="Infinite" className="mx-auto w-[22px]"/></span></li>
                                    <li className="flex items-center gap-2 justify-center text-[16px] h-[24px]">{isMobile && 'OpenAI o1: '}<span className='text-[#3C57BB] font-bold'> <img src={CheckIcon} alt="Infinite" className="mx-auto w-[22px]"/></span></li>
                                    <li className="flex items-center gap-2 justify-center text-[16px] h-[24px]">{isMobile && 'Claude: '}<span className='text-[#3C57BB] font-bold'>{t('echo.pricing.vprice.unlimitedText')}</span></li>
                                    <li className={`flex items-center gap-2 justify-center text-[16px] ${dallETitleHeightExtended ? 'h-[40px]' : 'h-[24px]'}`}></li>
                                    <li className="flex items-center gap-2 justify-center text-[16px] h-[24px]">{isMobile && 'DALL-E 3: '}<span className='text-[#3C57BB] font-bold'>{t('echo.pricing.vprice.unlimitedText')}</span></li>
                                    <li className="flex items-center gap-2 justify-center text-[16px] h-[24px]">{isMobile && 'Flux: '}160 images{isTablet && !allowTrialPlan ? '/mo.' : '/month'}</li>
                                    <li className="flex items-center gap-2 justify-center text-[16px] h-[24px]"></li>
                                    <li className="flex items-center gap-2 justify-center text-[16px] h-[24px]">{isMobile && 'KlingAI: '}15 videos{isTablet && !allowTrialPlan ? '/mo.' : '/month'}</li>
                                    <li className={`flex items-center gap-2 justify-center text-[16px] ${(isChatPDFContextFull && !isSinglePlan) || isUseChatPdfContextDesign ? 'h-[28px]' : 'h-[24px]'}`}></li>
                                    <li className={`flex items-center gap-2 justify-center text-[16px] ${isMobile && !isTablet ? 'h-[40px]' : 'h-[24px]'}`}>{isMobile && `${t('echo.pricing.vprice.dialogueLimitText')}: `}<span className='text-[#3C57BB] font-bold'> Unlimited</span></li>
                                    {allowTrialPlan && <li className={`flex items-center gap-2 justify-center text-[16px] ${(isMobile && !isTablet) || isExtendedTrialPlanDetail ? 'h-[40px]' : 'h-[24px]'}`}>{isMobile && `${t('echo.pricing.vprice.trialPeriodText')}: `}<span className='text-[#3C57BB] font-bold'>{`${plan?.label}, then ${getPricePlan(plan.currency, plan.price)}/month`}</span></li>}
                                  </>
                                )}
                              </ul>
                            </div>

                          </div>
                        </div>
                        {/* Class max-w-768:min-w-[142px] was originally in the classlist, removed from op 36193 */}
                        { plan.plan_type === 'Pro' && !allowTrialPlan && !GetCookie("promo") ? <div className={`absolute top-0 left-1/2 transform rounded-full ${!isMobile && (!localesCookie || localesCookie === 'en') ? 'min-w-[130px] max-w-[155px]' : 'min-w-[155px] max-w-[180px]'} -translate-x-1/2 -translate-y-1/2 bg-blue-600 text-white font-bold text-[16px] py-1 px-2 text-xs span-highlight`}><HiMiniSparkles className='inline text-yellow-400' /> {t('echo.pricing.vprice.mostPopularText')}<HiMiniSparkles className='inline text-yellow-400'/>
                        </div> : null }
                      </div>
                    ) : ""
                  ))}
                  {(enterprise === 'on' && ppg !== '46' && planInterval === 'yearly' && data[0].currency === 'USD') ?
                    <>
                    {enterpriseTab()}
                    </>
                  : null }
                </div>
              ) : (
                <div className="flex flex-col md:flex-row justify-center">
                  {data?.map((plan, index) => (
                    <div key={index} className={` w-full lg:w-[330px] xl:w-[400px] min-h-[474px] text-center sm:px-2 mb-8 relative`}>
                      <div className="rounded bg-[#F5F5F5] h-full">
                        <div className="px-2 sm:px-4 py-10 price-content">
                          <h3 className="text-2xl font-bold mb-4">{plan.label}</h3>
                            <PriceFormatted plan={plan}/>
                          <div className={`mb-6 pricing-description ${desc_align === 'left' ? 'text-left' : 'text-center'} ppg-${ppg}`}>
                            <motion.button
                              className="text-white font-bold py-3 mb-4 rounded-2xl px-3 lg:px-6 md:rounded-full bg-gradient-to-r from-[#3D56BA] to-[#268FED] w-full"
                              style={{backgroundColor: hexHash(pp_ctaclr)}}
                              whileHover={{ scale: 1.05, backgroundColor: hoverDarken(pp_ctaclr) }}
                              whileTap={{ scale: 0.9 }}
                              onClick={() => setPricing(plan.plan_id)}
                            >
                            {plan.label.toLowerCase()==="enterprise" ? "Build My Plan" : "Choose This Plan"}
                            </motion.button>
                            <ul className="text-sm text-gray-600">
                              { plan.display_txt2 ? <li className="asd"></li> : null }
                            </ul>
                          </div>
                        </div>
                      </div>
                      { plan.plan_type === 'Pro' ? <div className="absolute top-0 left-1/2 transform rounded-full min-w-[180px] -translate-x-1/2 -translate-y-1/2 bg-blue-600 text-white font-bold text-[16px] py-1 px-4 text-xs span-highlight"><HiMiniSparkles className='inline text-yellow-400' /> {t('echo.pricing.vprice.mostPopularText')} <HiMiniSparkles className='inline text-yellow-400'/>
                        </div> : null }
                    </div>
                  ))}
                  {(enterprise === 'on' && ppg !== '46' && data[0].currency === 'USD') ?
                    <>
                    {enterpriseTab()}
                    </>
                  : null }
                </div>
              )}
            </div>
          </div>

          { tpreviews === 'on' ?
            <>
            <Suspense fallback={null}>
              <TpReviews/>
            </Suspense>
            </>
          : null }
        </div>
      </div>
    </div>
  )
}

export default VPrice;