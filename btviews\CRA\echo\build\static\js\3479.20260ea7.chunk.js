"use strict";(self.webpackChunkv1=self.webpackChunkv1||[]).push([[3479],{3479:(t,e,r)=>{r.d(e,{drw:()=>l});var a=r(89983);function l(t){return(0,a.w_)({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{d:"M15.98 1.804a1 1 0 00-1.96 0l-.24 1.192a1 1 0 01-.784.785l-1.192.238a1 1 0 000 1.962l1.192.238a1 1 0 01.785.785l.238 1.192a1 1 0 001.962 0l.238-1.192a1 1 0 01.785-.785l1.192-.238a1 1 0 000-1.962l-1.192-.238a1 1 0 01-.785-.785l-.238-1.192zM6.949 5.684a1 1 0 00-1.898 0l-.683 2.051a1 1 0 01-.633.633l-2.051.683a1 1 0 000 1.898l2.051.684a1 1 0 01.633.632l.683 2.051a1 1 0 001.898 0l.683-2.051a1 1 0 01.633-.633l2.051-.683a1 1 0 000-1.898l-2.051-.683a1 1 0 01-.633-.633L6.95 5.684zM13.949 13.684a1 1 0 00-1.898 0l-.184.551a1 1 0 01-.632.633l-.551.183a1 1 0 000 1.898l.551.183a1 1 0 01.633.633l.183.551a1 1 0 001.898 0l.184-.551a1 1 0 01.632-.633l.551-.183a1 1 0 000-1.898l-.551-.184a1 1 0 01-.633-.632l-.183-.551z"}}]})(t)}},89983:(t,e,r)=>{r.d(e,{w_:()=>s});var a=r(72791),l={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},n=a.createContext&&a.createContext(l),o=function(){return o=Object.assign||function(t){for(var e,r=1,a=arguments.length;r<a;r++)for(var l in e=arguments[r])Object.prototype.hasOwnProperty.call(e,l)&&(t[l]=e[l]);return t},o.apply(this,arguments)},c=function(t,e){var r={};for(var a in t)Object.prototype.hasOwnProperty.call(t,a)&&e.indexOf(a)<0&&(r[a]=t[a]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols){var l=0;for(a=Object.getOwnPropertySymbols(t);l<a.length;l++)e.indexOf(a[l])<0&&Object.prototype.propertyIsEnumerable.call(t,a[l])&&(r[a[l]]=t[a[l]])}return r};function i(t){return t&&t.map(function(t,e){return a.createElement(t.tag,o({key:e},t.attr),i(t.child))})}function s(t){return function(e){return a.createElement(u,o({attr:o({},t.attr)},e),i(t.child))}}function u(t){var e=function(e){var r,l=t.attr,n=t.size,i=t.title,s=c(t,["attr","size","title"]),u=n||e.size||"1em";return e.className&&(r=e.className),t.className&&(r=(r?r+" ":"")+t.className),a.createElement("svg",o({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},e.attr,l,s,{className:r,style:o(o({color:t.color||e.color},e.style),t.style),height:u,width:u,xmlns:"http://www.w3.org/2000/svg"}),i&&a.createElement("title",null,i),t.children)};return void 0!==n?a.createElement(n.Consumer,null,function(t){return e(t)}):e(l)}}}]);
//# sourceMappingURL=3479.20260ea7.chunk.js.map