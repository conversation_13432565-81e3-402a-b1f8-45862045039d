{"version": 3, "file": "static/js/2695.8235d882.chunk.js", "mappings": "2KAGA,MAoCA,EApCwBA,IAA0B,IAAzB,gBAAEC,GAAiBD,EAC1C,MAAOE,EAAWC,IAAgBC,EAAAA,EAAAA,WAAS,IAE3CC,EAAAA,EAAAA,WAAU,KACR,MAAMC,EAAeA,KACnBH,EAAaI,OAAOC,YAAc,MAAQP,IAGtCQ,EAA+BA,KACnCN,GAAcF,IAMhB,OAHAM,OAAOG,iBAAiB,SAAUJ,GAClCC,OAAOG,iBAAiB,SAAUD,GAE3B,KACLF,OAAOI,oBAAoB,SAAUL,GACrCC,OAAOI,oBAAoB,SAAUF,KAEtC,CAACR,IAUJ,OACEW,EAAAA,EAAAA,KAAA,OAAKC,UAAW,gBAAeX,EAAY,UAAY,IAAKY,UAC1DF,EAAAA,EAAAA,KAACG,EAAAA,IAAe,CAACC,QATDC,KAClBV,OAAOW,SAAS,CACdC,IAAK,EACLC,SAAU,gB,0ECFhB,QAxBA,WAGE,MAAMC,EAAWC,yBAWjB,OAVAjB,EAAAA,EAAAA,WAAU,KACR,MAAMkB,EAASC,SAASC,cAAc,UACtCF,EAAOG,IAAML,EAAW,iDACxBE,EAAOI,OAAQ,EACfJ,EAAOK,OAAS,OAIhBJ,SAASK,KAAKC,YAAYP,IACzB,CAACF,KAEFT,EAAAA,EAAAA,KAAAmB,EAAAA,SAAA,CAAAjB,UACEF,EAAAA,EAAAA,KAAA,UAAQC,UAAW,iHAMzB,C,sHC4CA,QA9DA,SAAmBb,GAAkC,IAAjC,YAAEgC,EAAW,KAAEC,GAAOC,EAAAA,EAAAA,OAAQlC,EAChD,MAAMmC,EAAW5B,OAAO6B,SAASD,SAC3BE,EAAc,yBAAyBC,KAAKH,GAC5CI,EAAgB,sBAAsBD,KAAKH,GAC3CK,EAAc,yBAAyBF,KAAKH,GAC5CM,EAAiBN,EAASO,SAAS,kBACnCC,GAAYX,EACZY,EAAwBtB,CAAAA,SAAAA,aAAAA,WAAAA,eAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,cAAAA,EAAAA,kBAAAA,4BAAAA,mBAAAA,yBAAAA,oCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,kCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,2BAAAA,iBAAAA,4BAAAA,cAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,oBAAAA,uBAAAA,yBAAAA,8BAAAA,yBAAAA,gFAAAA,uBAAAA,6CAAAA,sBAAAA,KAAAA,uBAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,wBAAAA,KAAAA,mBAAAA,aAAAA,qCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,mCAAAA,KAAAA,8BAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,4BAAAA,4BAAAA,gCAAAA,oBAAAA,gCAAAA,oBAAAA,gCAAAA,oBAAAA,gCAAAA,oBAAAA,kCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,gCAAAA,KAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,iCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,6BAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,yBAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,iCAAAA,8GAAAA,iCAAAA,8GAAAA,oBAAAA,8jBAAYuB,iCAAmC,IAE7ExC,EAAAA,EAAAA,WAAU,KACR,MAAMyC,EAAUC,EAAkBC,EAAAA,EAAW,SACvCC,EAAWF,EAAkBG,EAAe,SAMlD,OALA1B,SAAS2B,KAAKC,OAAON,EAASG,GAEzBR,GACH,yDAEK,KACLK,EAAQO,SACRJ,EAASI,WAEV,CAACZ,IAGJ,MAAMM,EAAoBA,CAACO,EAAMC,KAC/B,MAAMC,EAAOhC,SAASC,cAAc,QAIpC,OAHA+B,EAAKC,IAAM,UACXD,EAAKF,KAAOA,EACZE,EAAKD,GAAKA,EACHC,GAGT,OACEE,EAAAA,EAAAA,MAAA3B,EAAAA,SAAA,CAAAjB,SAAA,CACG8B,IACChC,EAAAA,EAAAA,KAAA,OAAK+C,GAAG,wBAAwB9C,UAAU,mCAAkCC,SAAC,sFAI/EF,EAAAA,EAAAA,KAAA,UAAQ+C,GAAG,SAAS9C,UAAW,+FAA8F+B,EAAwB,aAAe,IAAK9B,UACvK4C,EAAAA,EAAAA,MAAA,OAAK7C,UAAU,2DAA0DC,SAAA,EACvE4C,EAAAA,EAAAA,MAAA,WAAS7C,UAAU,YAAWC,SAAA,EAC5BF,EAAAA,EAAAA,KAAA,UAAQgD,KAAK,aAAaC,OAAQX,EAAeY,MAAM,MAAMC,OAAO,KAAKlD,UAAU,eACnFD,EAAAA,EAAAA,KAAA,OAAKc,IAAKsB,EAAAA,EAAWgB,IAAI,cAAcnD,UAAU,kBAEjDwB,GAAeE,GAAiBC,IAAgBG,IAChD/B,EAAAA,EAAAA,KAAA,OAAKC,UAAU,uCAAuC8C,GAAG,OAAM7C,UAC7DF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,2BAA0BC,UACtCF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,uBAAsBC,UAClCF,EAAAA,EAAAA,KAAA,KAAG0C,KAAMrB,EAAO,cAAgB,SAAUpB,UAAU,YAAY,aAAYoB,EAAO,aAAe,QAAQnB,SACvGmB,EAAO,UAAY,wBAUxC,C,+SC6RA,QA1TA,WACE,MAAMA,GAAOC,EAAAA,EAAAA,OACN+B,EAASC,IAAc9D,EAAAA,EAAAA,UAAS,QACjC+D,GAAWC,EAAAA,EAAAA,QAAO,MAClBC,GAAYD,EAAAA,EAAAA,QAAO,MACnBE,GAAYF,EAAAA,EAAAA,QAAO,MACnBG,GAAaH,EAAAA,EAAAA,QAAO,MACpBI,GAAaJ,EAAAA,EAAAA,QAAO,MAEpBK,GAAYL,EAAAA,EAAAA,QAAO,OAClBnE,EAAiByE,IAAsBtE,EAAAA,EAAAA,WAAS,GACjDuE,MAAYC,EAAAA,EAAAA,IAAU,aAAyC,QAA1BA,EAAAA,EAAAA,IAAU,aAE/CC,EAAoBA,KACnB5C,EAEMA,GAAwB,WAAhBA,EAAK6C,OACtBvE,OAAO6B,SAASkB,KAAO,cAEvB/C,OAAO6B,SAASkB,KAAO,WAJvB/C,OAAO6B,SAASkB,KAAO,kBAQrByB,EAAaA,KACjBZ,EAASa,QAAQC,eAAe,CAAE7D,SAAU,aAkB9Cf,EAAAA,EAAAA,WAAU,KAER,MAAM6E,EAAU1D,SAAS2D,OAAOC,MAAM,KACtC,IAAK,IAAIC,EAAI,EAAGA,EAAIH,EAAQI,OAAQD,IAAK,CACvC,MAAMF,EAASD,EAAQG,GAAGE,OAC1B,GAAIJ,EAAOK,WAAW,YAAa,CACjCtB,EAAWiB,EAAOM,UAAU,IAC5B,KACF,CACF,CAGA,MAAMC,EAAgBlE,SAASmE,cAAc,mBAEvCrF,EAAeA,KACnB,MAAMsF,EAAiBrF,OAAOsF,QACxBC,EAAerB,EAAUO,QAAQe,wBAAwBC,OAC/DtB,EAAmBnE,OAAOC,YAAcsF,GACxCJ,EAAcO,MAAMC,UAAY,eAAgC,GAAjBN,QAKjD,OAFArF,OAAOG,iBAAiB,SAAUJ,GAE3B,KACLC,OAAOI,oBAAoB,SAAUL,KAGtC,CAAC2B,IAEJ,MAAMkE,EAAY7E,CAAAA,SAAAA,aAAAA,WAAAA,eAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,cAAAA,EAAAA,kBAAAA,4BAAAA,mBAAAA,yBAAAA,oCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,kCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,2BAAAA,iBAAAA,4BAAAA,cAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,oBAAAA,uBAAAA,yBAAAA,8BAAAA,yBAAAA,gFAAAA,uBAAAA,6CAAAA,sBAAAA,KAAAA,uBAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,wBAAAA,KAAAA,mBAAAA,aAAAA,qCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,mCAAAA,KAAAA,8BAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,4BAAAA,4BAAAA,gCAAAA,oBAAAA,gCAAAA,oBAAAA,gCAAAA,oBAAAA,gCAAAA,oBAAAA,kCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,gCAAAA,KAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,iCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,6BAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,yBAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,iCAAAA,8GAAAA,iCAAAA,8GAAAA,oBAAAA,8jBAAY8E,sBAO9B,QAAYC,IAATpE,EACH,OACEyB,EAAAA,EAAAA,MAAA3B,EAAAA,SAAA,CAAAjB,SAAA,EACE4C,EAAAA,EAAAA,MAAC4C,EAAAA,EAAM,CAAAxF,SAAA,EACLF,EAAAA,EAAAA,KAAA,QAAM2F,KAAK,SAASC,QAAQ,uBAC5B5F,EAAAA,EAAAA,KAAA,SAAAE,SAAO,gCACPF,EAAAA,EAAAA,KAAA,QAAM2F,KAAK,cAAcC,QAAQ,mJAEtB,SAAZvC,GACCrD,EAAAA,EAAAA,KAAC6F,EAAAA,QAAM,CAACzE,YAAa2C,KAErB/D,EAAAA,EAAAA,KAAC8F,EAAAA,QAAU,CAAC1E,YAAa2C,KAEzB/D,EAAAA,EAAAA,KAAA,OAAK+F,IAAKlC,KACV7D,EAAAA,EAAAA,KAACgG,EAAAA,QAAe,CAAC3G,gBAAiBA,KAClCyD,EAAAA,EAAAA,MAAA,OAAK7C,UAAU,6BAA4BC,SAAA,EAEzCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,2CAA0CC,UACvD4C,EAAAA,EAAAA,MAAA,OAAK7C,UAAU,uCAAsCC,SAAA,EACnD4C,EAAAA,EAAAA,MAAA,OAAK7C,UAAU,kEAAiEC,SAAA,EAC9EF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,4CAA2CC,SAAC,gCAC1DF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,mEAAkEC,SAAC,gCACjF4C,EAAAA,EAAAA,MAAA,KAAG7C,UAAU,mBAAkBC,SAAA,CAAC,sEACoCF,EAAAA,EAAAA,KAAA,UAAKA,EAAAA,EAAAA,KAAA,SAAK,8GAC8BA,EAAAA,EAAAA,KAAA,UAAKA,EAAAA,EAAAA,KAAA,SAAK,6DAGtHA,EAAAA,EAAAA,KAACiG,EAAAA,EAAOC,OAAM,CACZ9F,QAAS6D,EACThE,UAAU,sHACVkG,WAAY,CAAEC,MAAO,KACrBC,SAAU,CAAED,MAAO,IAAMlG,SAC1B,kBAIHF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gEAA+DC,UAC5EF,EAAAA,EAAAA,KAAA,UAAQC,UAAU,kBAAkB8C,GAAG,gBAAgBuD,MAAM,kBAAkBxF,IAAKyE,EAAWgB,YAAY,cAMjHzD,EAAAA,EAAAA,MAAA,OAAK7C,UAAU,qBAAoBC,SAAA,EACjCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oBACbD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mBAAkBC,UAC/BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gCAA+BC,UAC5C4C,EAAAA,EAAAA,MAAA,OAAK7C,UAAU,sCAAqCC,SAAA,EAClD4C,EAAAA,EAAAA,MAAA,OAAK7C,UAAU,cAAaC,SAAA,EAC1BF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,iCAAgCC,SAAC,sBAG/CF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,eAAcC,SAAC,mCAE/BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,kDAAiDC,UAE9D4C,EAAAA,EAAAA,MAAA,OAAK7C,UAAU,gEAA+DC,SAAA,EAC5E4C,EAAAA,EAAAA,MAAA,OAAK7C,UAAU,4IAA2IC,SAAA,EACxJF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,2BAA0BC,SAAC,yVAGxCF,EAAAA,EAAAA,KAACiG,EAAAA,EAAOC,OAAM,CACZ9F,QAAS+D,EACTlE,UAAU,qIACVkG,WAAY,CAAEC,MAAO,KACrBC,SAAU,CAAED,MAAO,IAAMlG,SAC1B,qBAKHF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,8EAA6EC,UAC1FF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mCAAkCC,UAC/CF,EAAAA,EAAAA,KAAA,OAAKc,IAAK0F,EAAOpD,IAAI,SAASnD,UAAU,gCAI5CD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mHAAkHC,UAC/H4C,EAAAA,EAAAA,MAAA,MAAI7C,UAAU,YAAWC,SAAA,EACvB4C,EAAAA,EAAAA,MAAA,MAAA5C,SAAA,EAAIF,EAAAA,EAAAA,KAACyG,EAAAA,IAAS,CAACxG,UAAU,yBAAwBD,EAAAA,EAAAA,KAAA,KAAG0C,KAAK,UAAUtC,QAAS+D,EAAWjE,SAAC,wBACxF4C,EAAAA,EAAAA,MAAA,MAAA5C,SAAA,EAAIF,EAAAA,EAAAA,KAAC0G,EAAAA,IAAe,CAACzG,UAAU,yBAAwBD,EAAAA,EAAAA,KAAA,KAAG0C,KAAK,UAAUtC,QApI/EuG,KAClBlD,EAAUW,QAAQC,eAAe,CAAE7D,SAAU,YAmIyEN,SAAC,uBAC/F4C,EAAAA,EAAAA,MAAA,MAAA5C,SAAA,EAAIF,EAAAA,EAAAA,KAAC4G,EAAAA,IAAU,CAAC3G,UAAU,yBAAwBD,EAAAA,EAAAA,KAAA,KAAG0C,KAAK,UAAUtC,QAlI1EyG,KAClBnD,EAAUU,QAAQC,eAAe,CAAE7D,SAAU,YAiIoEN,SAAC,qBAC1F4C,EAAAA,EAAAA,MAAA,MAAA5C,SAAA,EAAIF,EAAAA,EAAAA,KAAC8G,EAAAA,IAAU,CAAC7G,UAAU,yBAAwBD,EAAAA,EAAAA,KAAA,KAAG0C,KAAK,UAAUtC,QAhIzE2G,KACnBpD,EAAWS,QAAQC,eAAe,CAAE7D,SAAU,YA+HoEN,SAAC,sBAC3F4C,EAAAA,EAAAA,MAAA,MAAA5C,SAAA,EAAIF,EAAAA,EAAAA,KAACgH,EAAAA,IAAY,CAAC/G,UAAU,yBAAwBD,EAAAA,EAAAA,KAAA,KAAG0C,KAAK,UAAUtC,QA9H3E6G,KACnBrD,EAAWQ,QAAQC,eAAe,CAAE7D,SAAU,YA6HsEN,SAAC,wCAW/GF,EAAAA,EAAAA,KAAA,OAAK+F,IAAKxC,EAAUtD,UAAU,gEAA+DC,UAC3F4C,EAAAA,EAAAA,MAAA,OAAK7C,UAAU,wDAAuDC,SAAA,EACpE4C,EAAAA,EAAAA,MAAA,OAAK7C,UAAU,wDAAuDC,SAAA,EACpEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,mEAAkEC,SAAC,iCACjFF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,mBAAkBC,SAAC,wUAIlC4C,EAAAA,EAAAA,MAAA,OAAK7C,UAAU,sCAAqCC,SAAA,EAClDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,kBAAiBC,UAC9BF,EAAAA,EAAAA,KAAA,OAAKc,IAAKoG,EAAU9D,IAAI,SAASnD,UAAU,+BAE7CD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,kBAAiBC,UAC9BF,EAAAA,EAAAA,KAAA,OAAKc,IAAKqG,EAAW/D,IAAI,SAASnD,UAAU,uCAMpDD,EAAAA,EAAAA,KAAA,OAAK+F,IAAKtC,EAAWxD,UAAU,mEAAkEC,UAC/F4C,EAAAA,EAAAA,MAAA,OAAK7C,UAAU,0CAAyCC,SAAA,EACtDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,+BAA8BC,UAC3CF,EAAAA,EAAAA,KAAA,OAAKc,IAAKsG,EAAiBhE,IAAI,SAASnD,UAAU,YAEpD6C,EAAAA,EAAAA,MAAA,OAAK7C,UAAU,yDAAwDC,SAAA,EACrEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,8DAA6DC,SAAC,0BAC5EF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,mBAAkBC,SAAC,qWAOtCF,EAAAA,EAAAA,KAAA,OAAK+F,IAAKrC,EAAWzD,UAAU,iEAAgEC,UAC7F4C,EAAAA,EAAAA,MAAA,OAAK7C,UAAU,0CAAyCC,SAAA,EACtD4C,EAAAA,EAAAA,MAAA,OAAK7C,UAAU,wDAAuDC,SAAA,EACpEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,mEAAkEC,SAAC,iCACjFF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,mBAAkBC,SAAC,mVAIlCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,+BAA8BC,UAC3CF,EAAAA,EAAAA,KAAA,OAAKc,IAAKuG,EAAgBjE,IAAI,iBAAiBnD,UAAU,mCAM/DD,EAAAA,EAAAA,KAAA,OAAK+F,IAAKpC,EAAY1D,UAAU,kEAAiEC,UAC/F4C,EAAAA,EAAAA,MAAA,OAAK7C,UAAU,0CAAyCC,SAAA,EACtDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,+BAA8BC,UAC3CF,EAAAA,EAAAA,KAAA,OAAKc,IAAKwG,EAAiBlE,IAAI,SAASnD,UAAU,YAEpD6C,EAAAA,EAAAA,MAAA,OAAK7C,UAAU,yDAAwDC,SAAA,EACrEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,8DAA6DC,SAAC,kCAC5EF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,mBAAkBC,SAAC,yTAQtCF,EAAAA,EAAAA,KAAA,OAAK+F,IAAKnC,EAAY3D,UAAU,iEAAgEC,UAC9F4C,EAAAA,EAAAA,MAAA,OAAK7C,UAAU,2CAA0CC,SAAA,EACvD4C,EAAAA,EAAAA,MAAA,OAAK7C,UAAU,wDAAuDC,SAAA,EACpEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,mEAAkEC,SAAC,4BACjFF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,mBAAkBC,SAAC,gSAKlCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,uEAAsEC,UACjF4C,EAAAA,EAAAA,MAAA,OAAK7C,UAAU,qBAAoBC,SAAA,EACjCF,EAAAA,EAAAA,KAAA,OAAKc,IAAKyG,EAAcnE,IAAI,SAASnD,UAAU,+CAC/CD,EAAAA,EAAAA,KAAA,OAAKc,IAAK0G,EAAUpE,IAAI,SAASnD,UAAU,6DA8BrDD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gCAA+BC,UAC5C4C,EAAAA,EAAAA,MAAA,OAAK7C,UAAU,gEAA+DC,SAAA,EAC5E4C,EAAAA,EAAAA,MAAA,OAAK7C,UAAU,+CAA8CC,SAAA,EAC3DF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,4DAA2DC,SAAC,iCAG1EF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,kDAAiDC,SAAC,+VAG/DF,EAAAA,EAAAA,KAACiG,EAAAA,EAAOC,OAAM,CACZ9F,QAAS6D,EACThE,UAAU,mHACVkG,WAAY,CAAEC,MAAO,KACrBC,SAAU,CAAED,MAAO,IAAMlG,SAC1B,wBAKH4C,EAAAA,EAAAA,MAAA,OAAK7C,UAAU,2CAA0CC,SAAA,EACvDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gCAA+BC,UAC5CF,EAAAA,EAAAA,KAAA,OAAKc,IAAK2G,EAASrE,IAAI,SAASnD,UAAU,sCAE5CD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gCAA+BC,UAC5CF,EAAAA,EAAAA,KAAA,OAAKc,IAAK4G,EAAStE,IAAI,SAASnD,UAAU,iDAQtDD,EAAAA,EAAAA,KAAC2H,EAAAA,QAAM,CAACtG,KAAMA,EAAMD,YAAa2C,MAGvC,C,gDC5VA,SAAiB,C", "sources": ["footer/backtotop.jsx", "footer/index.jsx", "header/headerlogo.jsx", "lp/start-chatgpt-v2.jsx", "webpack://v1/./node_modules/react-responsive-carousel/lib/styles/carousel.css?c782"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { FaArrowCircleUp } from 'react-icons/fa';\r\n\r\nconst BackToTopButton = ({ isHeaderVisible }) => {\r\n  const [isVisible, setIsVisible] = useState(false);\r\n\r\n  useEffect(() => {\r\n    const handleScroll = () => {\r\n      setIsVisible(window.pageYOffset > 300 && !isHeaderVisible);\r\n    };\r\n\r\n    const handleHeaderVisibilityChange = () => {\r\n      setIsVisible(!isHeaderVisible);\r\n    };\r\n\r\n    window.addEventListener('scroll', handleScroll);\r\n    window.addEventListener('resize', handleHeaderVisibilityChange);\r\n\r\n    return () => {\r\n      window.removeEventListener('scroll', handleScroll);\r\n      window.removeEventListener('resize', handleHeaderVisibilityChange);\r\n    };\r\n  }, [isHeaderVisible]);\r\n\r\n\r\n  const scrollToTop = () => {\r\n    window.scrollTo({\r\n      top: 0,\r\n      behavior: 'smooth',\r\n    });\r\n  };\r\n\r\n  return (\r\n    <div className={`back-to-top ${isVisible ? 'visible' : ''}`}>\r\n      <FaArrowCircleUp onClick={scrollToTop} />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default BackToTopButton;\r\n", "import React, { useEffect } from 'react';\r\n\r\nfunction Powered() {\r\n  // const [isHidden, setIsHidden] = useState(false);\r\n\r\n  const base_url = process.env.REACT_APP_BASE_URL;\r\n  useEffect(() => {\r\n    const script = document.createElement('script');\r\n    script.src = base_url + 'snippets/com.global.vuzo/js/com.global.vuzo.js';\r\n    script.async = true;\r\n    script.onload = () => {\r\n      // window.displayGooglePlay();\r\n      // window.displayQR();\r\n    };\r\n    document.body.appendChild(script);\r\n  }, [base_url]);\r\n  return (\r\n    <>\r\n      <footer className={`relative hidden md:block md:absolute bottom-[10px] right-[5px] w-full mt-3 py-12 md:py-2 z-[8888] md:hidden`}>\r\n      {/* <footer className=\"relative hidden md:block md:absolute bottom-[10px] right-[5px] w-full mt-3 py-12 md:py-2 z-[8888]\"> */}\r\n\r\n      </footer>\r\n    </>\r\n  );\r\n}\r\n\r\nexport default Powered;\r\n", "import React, { useEffect } from 'react';\r\nimport aiproLogo from '../assets/images/AIPRO.svg';\r\nimport aiproLogoWebP from '../assets/images/AIPRO.webp';\r\nimport { Auth } from '../core/utils/auth';\r\nimport './style.css';\r\n\r\nfunction HeaderLogo({ hideNavLink, auth = Auth() }) {\r\n  const pathname = window.location.pathname;\r\n  const isChatGptGo = /\\/start-chatgpt-go\\/?$/.test(pathname);\r\n  const isTexttoImage = /\\/text-to-image\\/?$/.test(pathname);\r\n  const isChatGptV2 = /\\/start-chatgpt-v2\\/?$/.test(pathname);\r\n  const isRegisterPage = pathname.includes('/register-auth');\r\n  const showLink = !hideNavLink;\r\n  const showMaintenanceBanner = process.env.REACT_APP_ShowMaintenanceBanner || '';\r\n\r\n  useEffect(() => {\r\n    const linkPNG = createPreloadLink(aiproLogo, 'image');\r\n    const linkWebP = createPreloadLink(aiproLogoWebP, 'image');\r\n    document.head.append(linkPNG, linkWebP);\r\n\r\n    if (!isRegisterPage) {\r\n      import('@fortawesome/fontawesome-free/css/all.css');\r\n    }\r\n    return () => {\r\n      linkPNG.remove();\r\n      linkWebP.remove();\r\n    };\r\n  }, [isRegisterPage]);\r\n\r\n\r\n  const createPreloadLink = (href, as) => {\r\n    const link = document.createElement('link');\r\n    link.rel = 'preload';\r\n    link.href = href;\r\n    link.as = as;\r\n    return link;\r\n  };\r\n\r\n  return (\r\n    <>\r\n      {showMaintenanceBanner && (\r\n        <div id=\"maintenance-container\" className=\"p-[5px] bg-[#f7a73f] text-center\">\r\n          We are currently undergoing maintenance. We're expecting to be back at 4 AM EST.\r\n        </div>\r\n      )}\r\n      <header id=\"header\" className={`headernav border-gray-500 bg-white text-black w-3/4 mx-auto rounded-3xl mt-3 py-2 z-[8888] ${showMaintenanceBanner ? 'top-[60px]' : ''}`}>\r\n        <div className=\"container mx-auto flex justify-between items-center px-4\">\r\n          <picture className=\"aiprologo\">\r\n            <source type=\"image/webp\" srcSet={aiproLogoWebP} width=\"150\" height=\"52\" className=\"aiprologo\" />\r\n            <img src={aiproLogo} alt=\"AI-Pro Logo\" className=\"aiprologo\"/>\r\n          </picture>\r\n          {(isChatGptGo || isTexttoImage || isChatGptV2) && showLink && (\r\n            <nav className=\"text-xs lg:text-sm block inline-flex\" id=\"menu\">\r\n              <ul className=\"headnav flex inline-flex\">\r\n                <li className=\"mr-1 md:mr-2 lg:mr-6\">\r\n                  <a href={auth ? '/my-account' : '/login'} className=\"font-bold\" aria-label={auth ? 'my-account' : 'login'}>\r\n                    {auth ? 'My Apps' : 'LOG IN'}\r\n                  </a>\r\n                </li>\r\n              </ul>\r\n            </nav>\r\n          )}\r\n        </div>\r\n      </header>\r\n    </>\r\n  );\r\n}\r\n\r\nexport default HeaderLogo;\r\n", "import React, { useState, useEffect, useRef } from 'react';\r\nimport './start-chatgpt-v2.css';\r\nimport Header from '../header';\r\nimport Headerlogo from '../header/headerlogo';\r\nimport Footer from '../footer';\r\nimport \"particles.js\";\r\nimport { Auth } from '../core/utils/auth';\r\nimport 'react-responsive-carousel/lib/styles/carousel.css';\r\nimport { Helmet } from 'react-helmet';\r\nimport { motion } from \"framer-motion\";\r\nimport simpli4 from '../assets/images/simpli4.gif';\r\nimport simpli6 from '../assets/images/simpli6.gif';\r\n// import lpPic1 from '../assets/images/sd2.png';\r\n// import blkpic1 from '../assets/images/x1.gif';\r\n// import blkpic2 from '../assets/images/x2.gif';\r\n// import blkpic2a from '../assets/images/blk2.png';\r\n// import bkgpng from '../assets/images/bkg.png';\r\n// import elonmuska from '../assets/images/elonmusk.gif';\r\n// import aminpic1 from '../assets/images/cchat.gif';\r\nimport aminpic2 from '../assets/images/codex.png';\r\n\r\nimport imgDraft from '../assets/images/draft_contracts.png';\r\nimport imgDraft2 from '../assets/images/draft_contracts2.png';\r\nimport imgUsePrompt from '../assets/images/use_prompts.png';\r\nimport imgWriteStories from '../assets/images/write_stories.png';\r\nimport imgWrtiteEmail from '../assets/images/write_emails.png';\r\nimport imgCreateVideos from '../assets/images/create_videos.png';\r\n\r\n// import l1 from '../assets/images/l1.png';\r\n// import l2 from '../assets/images/l2.png';\r\n// import l3 from '../assets/images/l3.png';\r\n\r\nimport para1 from '../assets/images/parallaximage.png';\r\nimport { FaRegEdit, FaFileSignature, FaMailBulk, FaBookOpen, FaLaptopCode } from 'react-icons/fa';\r\nimport BackToTopButton from '../footer/backtotop';\r\nimport { GetCookie } from '../core/utils/cookies';\r\n\r\nfunction StartChatGptV2() {\r\n  const auth = Auth();\r\n  const [navmenu, setNavmenu] = useState('hide');\r\n  const draftRef = useRef(null);\r\n  const scriptRef = useRef(null);\r\n  const emailsRef = useRef(null);\r\n  const storiesRef = useRef(null);\r\n  const promptsRef = useRef(null);\r\n  // const moneyRef = useRef(null);\r\n  const headerRef = useRef(null);\r\n  const [isHeaderVisible, setIsHeaderVisible] = useState(true);\r\n  const hideLinks = GetCookie('qW1eMlya') && GetCookie('qW1eMlya') === 'on' ? true : false;\r\n\r\n  const checkSubscription = () => {\r\n    if(!(auth)){\r\n      window.location.href = '/register-auth';\r\n    } else if (auth && auth.status === 'active') {\r\n      window.location.href = '/my-account';\r\n    } else {\r\n      window.location.href = '/pricing';\r\n    }\r\n  };\r\n\r\n  const draftClick = () => {\r\n    draftRef.current.scrollIntoView({ behavior: 'smooth' });\r\n  };\r\n  const scriptClick = () => {\r\n    scriptRef.current.scrollIntoView({ behavior: 'smooth' });\r\n  };\r\n  const emailsClick = () => {\r\n    emailsRef.current.scrollIntoView({ behavior: 'smooth' });\r\n  };\r\n  const storiesClick = () => {\r\n    storiesRef.current.scrollIntoView({ behavior: 'smooth' });\r\n  };\r\n  const promptsClick = () => {\r\n    promptsRef.current.scrollIntoView({ behavior: 'smooth' });\r\n  };\r\n  // const moneyClick = () => {\r\n  //   moneyRef.current.scrollIntoView({ behavior: 'smooth' });\r\n  // };\r\n\r\n  useEffect(() => {\r\n    // checkflag\r\n    const cookies = document.cookie.split(';');\r\n    for (let i = 0; i < cookies.length; i++) {\r\n      const cookie = cookies[i].trim();\r\n      if (cookie.startsWith('navmenu=')) {\r\n        setNavmenu(cookie.substring('navmenu='.length));\r\n        break;\r\n      }\r\n    }\r\n    // end checkflag\r\n\r\n    const parallaxImage = document.querySelector('.parallax-image');\r\n\r\n    const handleScroll = () => {\r\n      const scrollPosition = window.scrollY;\r\n      const headerBottom = headerRef.current.getBoundingClientRect().bottom;\r\n      setIsHeaderVisible(window.pageYOffset < headerBottom);\r\n      parallaxImage.style.transform = `translateY(-${scrollPosition * 0.3}px)`;\r\n    };\r\n\r\n    window.addEventListener('scroll', handleScroll);\r\n\r\n    return () => {\r\n      window.removeEventListener('scroll', handleScroll);\r\n    };\r\n\r\n  }, [auth]);\r\n\r\n  const iframeSrc = process.env.REACT_APP_CHATBOT_URL;\r\n  // const fullHostname = window.location.hostname;\r\n  // const hostnameParts = fullHostname.split('.');\r\n  // const subdomain = hostnameParts.length > 2 ? hostnameParts[0] : '';\r\n  // const iframeSrc = (subdomain === 'dev' || subdomain === 'staging') ? 'https://staging.app.ai-pro.org/start-chatbot' : 'https://app.ai-pro.org/start-chatbot';\r\n\r\n\r\n  if(auth === undefined) return;\r\n  return (\r\n    <>\r\n      <Helmet>\r\n        <meta name=\"robots\" content=\"noindex, nofollow\" />\r\n        <title>AI Chat Using GPT-4o Power</title>\r\n        <meta name=\"description\" content=\"Welcome to ChatGPT, your reliable virtual assistant. Boost productivity with AI-powered support and intelligent responses. Get started now!\" />\r\n      </Helmet>\r\n      {navmenu === 'show' ? (\r\n        <Header hideNavLink={hideLinks} />\r\n      ) : (\r\n        <Headerlogo hideNavLink={hideLinks} />\r\n      )}\r\n        <div ref={headerRef}></div>\r\n        <BackToTopButton isHeaderVisible={isHeaderVisible} />\r\n        <div className=\"startchatgptv2 bg-gray-100\">\r\n\r\n          <div className=\"intro mx-auto block pt-10 pb-10 md:pt-20\">\r\n            <div className=\"mainbanner block md:flex md:flex-row\">\r\n              <div className=\"w-full md:w-1/2 text-left p-10 pt-16 sm:pr-10 sm:pl-20 sm:pt-20\">\r\n                <h4 className=\"text-md font-bold sm:pt-6 lg:pt-8 lg:mt-2\">ChatGPT AI is Finally Here</h4>\r\n                <h1 className=\"text-4xl md:text-5xl mb-4 sm:pt-4 pb-6 font-bold drop-shadow-2xl\">AI Chat Using GPT-4o Power</h1>\r\n                <p className=\"text-[16px] mb-4\">\r\n                  Automate your writing, answer questions, write code and much more.<br/><br/>\r\n                  Get more creative and free up your time using ChatGPT’s powerful language capabilities, powered by GPT-4o.<br/><br/>\r\n                  The most powerful AI Language ChatBot available today.\r\n                </p>\r\n                <motion.button\r\n                  onClick={checkSubscription}\r\n                  className=\"cta bg-blue-800 hover:bg-blue-700 mb-1 text-white mx-auto text-center font-bold py-3 px-6 my-3 rounded-2xl md:w-1/2\"\r\n                  whileHover={{ scale: 1.1 }}\r\n                  whileTap={{ scale: 0.9 }}\r\n                >\r\n                  Start Now\r\n                </motion.button>\r\n              </div>\r\n              <div className=\"w-full h-[auto] sm:h-[600px] md:w-1/2 banner_img pt-2 sm:pt-8\">\r\n                <iframe className=\"w-full md:w-5/6\" id=\"chatgptv2demo\" title=\"ChatGPT V2 Demo\" src={iframeSrc} frameBorder=\"0\">\r\n                </iframe>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"parallax-container\">\r\n            <div className=\"parallax-image\"></div>\r\n              <div className=\"parallax-content\">\r\n                <div className=\"lg:justify-center text-center\">\r\n                  <div className=\"pcont w-full mt-16 block text-white\">\r\n                    <div className=\"phead pt-10\">\r\n                      <h2 className=\"text-3xl lg:text-6xl font-bold\">\r\n                        ChatGPT Features\r\n                      </h2>\r\n                      <h4 className=\"text-2xl p-6\">Reinventing the Experience</h4>\r\n                    </div>\r\n                    <div className=\"flex flex-wrap justify-center text-left mx-auto\">\r\n\r\n                      <div className=\"pcontent grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4\">\r\n                        <div className=\"first col-span-1 sm:col-span-2 lg:col-span-1 block justify-center text-center lg:text-left items-end mx-8 p-8 sm:mr-0 lg:pt-[100px] pb-12\">\r\n                          <p className=\"block text-sm lg:text-md\">\r\n                            ChatGPT is a large language model trained by OpenAI that can generate human-like text. It can be fine-tuned for a variety of tasks such as conversation, summarization, question answering, and more. It's based on the transformer architecture and has 175 billion parameters. It can be used for both text generation and text completion tasks.\r\n                          </p>\r\n                          <motion.button\r\n                            onClick={draftClick}\r\n                            className=\"cta bg-white hover:bg-gray-100 mb-1 text-black block font-bold py-3 px-3 my-3 rounded-2xl w-1/2 mx-auto lg:mx-0 text-sm lg:text-md\"\r\n                            whileHover={{ scale: 1.1 }}\r\n                            whileTap={{ scale: 0.9 }}\r\n                          >\r\n                            See Features\r\n                          </motion.button>\r\n                        </div>\r\n\r\n                        <div className=\"second col-span-1 sm:col-span-1 lg:col-span-1 flex justify-center items-end\">\r\n                          <div className=\"flex justify-center items-center\">\r\n                            <img src={para1} alt=\"AI-Pro\" className=\"parallaximg max-w-full\" />\r\n                          </div>\r\n                        </div>\r\n\r\n                        <div className=\"third col-span-1 sm:col-span-1 lg:col-span-1 block justify-center items-end mx-8 p-8 lg:pt-[150px] pb-12 mx-auto\">\r\n                          <ul className=\"text-left\">\r\n                            <li><FaRegEdit className=\"inline text-lg mr-2\" /><a href=\"#anchor\" onClick={draftClick}>Draft Contracts</a></li>\r\n                            <li><FaFileSignature className=\"inline text-lg mr-2\" /><a href=\"#anchor\" onClick={scriptClick}>Create Scripts</a></li>\r\n                            <li><FaMailBulk className=\"inline text-lg mr-2\" /><a href=\"#anchor\" onClick={emailsClick}>Write Emails</a></li>\r\n                            <li><FaBookOpen className=\"inline text-lg mr-2\" /><a href=\"#anchor\" onClick={storiesClick}>Write Stories</a></li>\r\n                            <li><FaLaptopCode className=\"inline text-lg mr-2\" /><a href=\"#anchor\" onClick={promptsClick}>Use Prompts</a></li>\r\n                          </ul>\r\n                        </div>\r\n                      </div>\r\n\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n          </div>\r\n\r\n          <div ref={draftRef} className=\"cntblack bg-black text-white mx-auto block pt-0 pb-0 md:pt-20\">\r\n            <div className=\"block md:flex md:flex-row pt-20 pb-0 sm:py-20 md:py-0\">\r\n              <div className=\"w-full lg:w-1/2 text-left pt-0 px-10 lg:pt-8 lg:px-20\">\r\n                <h1 className=\"text-2xl lg:text-6xl mb-4 sm:pt-4 pb-8 font-bold drop-shadow-2xl\">Draft Contracts & Proposals</h1>\r\n                <p className=\"text-[16px] mb-4\">\r\n                  ChatGPT can help you create contracts and proposals more easily. This saves you time and effort because you don't have to start from scratch. ChatGPT can also check your drafts for mistakes and make sure they are formatted correctly. Make the process of creating contracts and proposals quicker and easier with ChatGPT.\r\n                </p>\r\n              </div>\r\n              <div className=\"lg:w-1/2 block sm:flex sm:flex-wrap\">\r\n                <div className=\"w-full md:w-1/2\">\r\n                  <img src={imgDraft} alt=\"AI-Pro\" className=\"blkpic h-[auto] mx-auto\" />\r\n                </div>\r\n                <div className=\"w-full md:w-1/2\">\r\n                  <img src={imgDraft2} alt=\"AI-Pro\" className=\"blkpic h-[auto] mx-auto\" />\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div ref={scriptRef} className=\"cntblack2 bg-black text-white mx-auto block pt-0 pb-10 md:pt-20 \">\r\n            <div className=\"block md:flex md:flex-row py-20 md:py-0\">\r\n              <div className=\"w-full lg:w-1/2 inline-block\">\r\n                <img src={imgCreateVideos} alt=\"AI-Pro\" className=\"p-12\" />\r\n              </div>\r\n              <div className=\"w-full lg:w-1/2 text-left pt-0 px-10 lg:pt-40 lg:px-20\">\r\n                <h1 className=\"text-2xl lg:text-6xl mb-4 sm:pt-4 font-bold drop-shadow-2xl\">Create Video Scripts</h1>\r\n                <p className=\"text-[16px] mb-4\">\r\n                  ChatGPT can assist you in creating video scripts by automating the process of generating draft scripts based on certain prompts or keywords. ChatGPT can also help with editing and proofreading the generated drafts to ensure they are accurate and well-written. Utilize ChatGPT's capabilities to streamline the process of creating video scripts.\r\n                </p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div ref={emailsRef} className=\"cntblack3 bg-black text-white mx-auto block pt-0 pb-0 md:pt-20\">\r\n            <div className=\"block md:flex md:flex-row py-20 md:py-0\">\r\n              <div className=\"w-full lg:w-1/2 text-left pt-0 px-10 lg:pt-8 lg:px-20\">\r\n                <h1 className=\"text-2xl lg:text-6xl mb-4 sm:pt-4 pb-8 font-bold drop-shadow-2xl\">Write Emails & Chat Replies</h1>\r\n                <p className=\"text-[16px] mb-4\">\r\n                  ChatGPT can help you save time and effort by writing emails and chat replies for your work or business. ChatGPT can also assist with editing and proofreading the generated responses to ensure they are accurate and professional. Let ChatGPT handle your email and chat communication, freeing you up to focus on more important tasks.\r\n                </p>\r\n              </div>\r\n              <div className=\"w-full lg:w-1/2 inline-block\">\r\n                <img src={imgWrtiteEmail} alt=\"elon musk chat\" className=\"blkpic w-full h-[auto]\" />\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n\r\n          <div ref={storiesRef} className=\"cntblack4 bg-black text-white mx-auto block pt-0 pb-10 md:pt-20\">\r\n            <div className=\"block md:flex md:flex-row py-20 md:py-0\">\r\n              <div className=\"w-full lg:w-1/2 inline-block\">\r\n                <img src={imgWriteStories} alt=\"AI-Pro\" className=\"p-12\" />\r\n              </div>\r\n              <div className=\"w-full lg:w-1/2 text-left pt-0 px-10 lg:pt-40 lg:px-20\">\r\n                <h1 className=\"text-2xl lg:text-6xl mb-4 sm:pt-4 font-bold drop-shadow-2xl\">Write Stories, Poems & Songs</h1>\r\n                <p className=\"text-[16px] mb-4\">\r\n                  ChatGPT is a powerful language model that can assist with writing tasks, including songwriting, poetry, stories, and books. Simply provide a prompt and let the model generate creative and original ideas. ChatGPT can write in various styles and tones. Try it out to boost creativity and productivity.\r\n                </p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n\r\n          <div ref={promptsRef} className=\"cntblack5 bg-black text-white mx-auto block pt-0 pb-0 md:pt-20\">\r\n            <div className=\"block md:flex md:flex-row py-20 md:py-0 \">\r\n              <div className=\"w-full lg:w-1/2 text-left pt-0 px-10 lg:pt-8 lg:px-20\">\r\n                <h1 className=\"text-2xl lg:text-6xl mb-4 sm:pt-4 pb-8 font-bold drop-shadow-2xl\">Use Prompts & Commands</h1>\r\n                <p className=\"text-[16px] mb-4\">\r\n                  Explore our comprehensive resources and enhance your understanding of AI prompts and commands. From beginner to advanced, our guides will help you improve your skills and strengthen your communication with your AI. Get started now and take your AI capabilities to the next level.\r\n                </p>\r\n              </div>\r\n\r\n              <div className=\"block sm:flex sm:flex-wrap max-h-auto xs:max-h-[200px] sm:max-h-auto\">\r\n                  <div className=\"inline-block pb-10\">\r\n                    <img src={imgUsePrompt} alt=\"AI-Pro\" className=\"p-2 w-full sm:w-1/2 h-[auto] inline-block\" />\r\n                    <img src={aminpic2} alt=\"AI-Pro\" className=\"p-2 w-full sm:w-1/2 max-h-[auto] inline-block\" />                    \r\n                  </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n{/*\r\n          <div ref={moneyRef} className=\"cntblack6 text-center bg-black text-white mx-auto block pt-0 pb-0 md:pt-20\">\r\n            <div className=\"block pb-[100px] mb-[300px]\">\r\n              <div className=\"w-full pt-0 px-10 lg:pt-8 lg:px-20\">\r\n                <h1 className=\"text-2xl lg:text-6xl mb-4 sm:pt-4 pb-8 font-bold drop-shadow-2xl\">Make Money with AI</h1>\r\n                <p className=\"text-[16px] mb-4\">\r\n                  Looking to leverage the power of AI to generate income? We will show you how to utilize AI to create new streams of revenue. You'll learn the skills and strategies you need to succeed in the lucrative world of AI. Don't miss out on this opportunity to transform your financial future with AI!\r\n                </p>\r\n              </div>\r\n              <div className=\"flex flex-wrap p-8 mt-8 mb-[-350px]\">\r\n                <div className=\"w-full sm:w-1/2 md:w-1/3 lg:w-1/3 xl:w-1/3\">\r\n                  <img src={l1} alt=\"AI-Pro\" className=\"mx-auto rounded-2xl\" />\r\n                </div>\r\n                <div className=\"w-full sm:w-1/2 md:w-1/3 lg:w-1/3 xl:w-1/3 my-6 md:mt-[120px]\">\r\n                  <img src={l2} alt=\"AI-Pro\" className=\"mx-auto rounded-2xl\" />\r\n                </div>\r\n                <div className=\"w-full sm:w-1/2 md:w-1/3 lg:w-1/3 xl:w-1/3\">\r\n                  <img src={l3} alt=\"AI-Pro\" className=\"mx-auto rounded-2xl\" />\r\n                </div>\r\n              </div>\r\n\r\n            </div>\r\n          </div>\r\n*/}\r\n          <div className=\"useai mx-auto pt-10 z-1 mb-12\">\r\n            <div className=\"flex flex-col items-center p-10 lg:pt-8 lg:pb-0 min-h-[470px]\">\r\n              <div className=\"w-full lg:w-5/6 p-6 text-center mx-4 lg:mx-0\">\r\n                <h2 className=\"text-white text-xl md:text-5xl font-bold text-center pb-6\">\r\n                  Use Artificial Intelligence\r\n                </h2>\r\n                <p className=\"text-md text-white text-center md:p-6 leading-8\">\r\n                  At AI-PRO, we believe that everyone should have access to the resources and guidance they need to succeed in the world of AI. That’s why we offer a variety of membership options to suit your needs and budget. Whether you’re an individual looking to learn about AI or a business looking to adopt AI solutions, we have a plan that’s right for you.\r\n                </p>\r\n                <motion.button\r\n                  onClick={checkSubscription}\r\n                  className=\"cta bg-white hover:bg-gray-100 mb-1 text-black mx-auto text-center font-bold py-3 px-6 my-3 rounded-2xl md:w-1/2\"\r\n                  whileHover={{ scale: 1.1 }}\r\n                  whileTap={{ scale: 0.9 }}\r\n                >\r\n                  Discover AI Now\r\n                </motion.button>\r\n              </div>\r\n\r\n              <div className=\"features block sm:flex sm:flex-wrap py-8\">\r\n                <div className=\"w-full mx-auto md:w-[74%] p-4\">\r\n                  <img src={simpli4} alt=\"AI-Pro\" className=\"simpli4 mx-auto drop-shadow-xl\" />\r\n                </div>\r\n                <div className=\"w-full mx-auto md:w-[25%] p-4\">\r\n                  <img src={simpli6} alt=\"AI-Pro\" className=\"simpli6 mx-auto drop-shadow-xl\" />\r\n                </div>\r\n              </div>\r\n\r\n            </div>\r\n\r\n          </div>\r\n        </div>\r\n      <Footer auth={auth} hideNavLink={hideLinks}/>\r\n    </>\r\n  );\r\n}\r\n\r\nexport default StartChatGptV2;", "// extracted by mini-css-extract-plugin\nexport default {};"], "names": ["_ref", "isHeaderVisible", "isVisible", "setIsVisible", "useState", "useEffect", "handleScroll", "window", "pageYOffset", "handleHeaderVisibilityChange", "addEventListener", "removeEventListener", "_jsx", "className", "children", "FaArrowCircleUp", "onClick", "scrollToTop", "scrollTo", "top", "behavior", "base_url", "process", "script", "document", "createElement", "src", "async", "onload", "body", "append<PERSON><PERSON><PERSON>", "_Fragment", "hideNavLink", "auth", "<PERSON><PERSON>", "pathname", "location", "isChatGptGo", "test", "isTexttoImage", "isChatGptV2", "isRegisterPage", "includes", "showLink", "showMaintenanceBanner", "REACT_APP_ShowMaintenanceBanner", "linkPNG", "createPreloadLink", "aiproLogo", "linkWebP", "aiproLogoWebP", "head", "append", "remove", "href", "as", "link", "rel", "_jsxs", "id", "type", "srcSet", "width", "height", "alt", "navmenu", "setNavmenu", "draftRef", "useRef", "scriptRef", "emailsRef", "storiesRef", "promptsRef", "headerRef", "setIsHeaderVisible", "hideLinks", "Get<PERSON><PERSON><PERSON>", "checkSubscription", "status", "draftClick", "current", "scrollIntoView", "cookies", "cookie", "split", "i", "length", "trim", "startsWith", "substring", "parallaxImage", "querySelector", "scrollPosition", "scrollY", "headerBottom", "getBoundingClientRect", "bottom", "style", "transform", "iframeSrc", "REACT_APP_CHATBOT_URL", "undefined", "<PERSON><PERSON><PERSON>", "name", "content", "Header", "Headerlogo", "ref", "BackToTopButton", "motion", "button", "whileHover", "scale", "whileTap", "title", "frameBorder", "para1", "FaRegEdit", "FaFileSignature", "scriptClick", "FaMailBulk", "emailsClick", "FaBookOpen", "storiesClick", "FaLaptopCode", "promptsClick", "imgDraft", "imgDraft2", "imgCreateVideos", "imgWrtiteEmail", "imgWriteStories", "imgUsePrompt", "aminpic2", "simpli4", "simpli6", "Footer"], "sourceRoot": ""}