{"version": 3, "file": "static/js/8859.ff178e08.chunk.js", "mappings": "mHAoCA,MACA,EAAe,IAA0B,8D,uFCnClC,SAASA,IACd,MAAMC,EAAOC,SAASC,cAAc,UACpC,SAAOF,EAAKG,aAAcH,EAAKG,WAAW,QAC2B,IAA5DH,EAAKI,UAAU,cAAcC,QAAQ,kBAGhD,CAgDO,SAASC,EAAQC,GAEtB,OAAW,MAAPA,GAAeA,EAAIC,OAAS,IADf,eAC8BC,KAAKF,GAC3C,UAKF,IAFPA,EAAMA,EAAIG,MAAM,EAAG,IAGrB,CAGO,SAASC,EAAYJ,IAEf,MAAPA,GAAeA,EAAIC,OAAS,IADf,eAC8BC,KAAKF,MAClDA,EAAM,UAGR,IAAIK,EAAIC,SAASN,EAAIG,MAAM,EAAG,GAAI,IAC9BI,EAAID,SAASN,EAAIG,MAAM,EAAG,GAAI,IAC9BK,EAAIF,SAASN,EAAIG,MAAM,EAAG,GAAI,IAElCE,EAAII,KAAKC,IAAI,EAAGD,KAAKE,IAAI,IAAKN,EAAS,IAAJA,IACnCE,EAAIE,KAAKC,IAAI,EAAGD,KAAKE,IAAI,IAAKJ,EAAS,IAAJA,IACnCC,EAAIC,KAAKC,IAAI,EAAGD,KAAKE,IAAI,IAAKH,EAAS,IAAJA,IAEnC,MAAMI,EAASC,GACIJ,KAAKK,MAAMD,GAAOE,SAAS,IAC5BC,SAAS,EAAG,KAG9B,MAAO,IAAIJ,EAAMP,KAAKO,EAAML,KAAKK,EAAMJ,IACzC,CAEO,SAASS,IACd,MAAOC,EAAUC,IAAgBC,EAAAA,EAAAA,WAAS,IACnCC,EAAUC,IAAeF,EAAAA,EAAAA,WAAS,IAClCG,EAAWC,IAAgBJ,EAAAA,EAAAA,WAAS,GAmB3C,OAjBAK,EAAAA,EAAAA,WAAU,KACR,MAAMC,EAAeA,KACnB,MAAMC,EAAQC,OAAOC,WAErBV,EAAaQ,EAAQ,KACrBL,EAAYK,EAAQ,KAAOA,GAAS,KACpCH,EAAaG,EAAQ,MAMvB,OAHAD,IACAE,OAAOE,iBAAiB,SAAUJ,GAE3B,KACLE,OAAOG,oBAAoB,SAAUL,KAEtC,IAEI,CAAER,WAAUG,WAAUE,YAC/B,C,+IChHO,SAASS,EAAYC,GACxB,OAAIA,EAC0B,QAA3BA,EAASC,cAAgC,IACd,QAA3BD,EAASC,cAAgC,IACd,QAA3BD,EAASC,cAAgC,IACd,QAA3BD,EAASC,eACkB,QAA3BD,EAASC,cADgC,KAErC,GANc,EAOzB,CAEO,SAASC,EAAWF,GACvB,OAAIA,EAC0B,QAA3BA,EAASC,eACkB,QAA3BD,EAASC,cADgC,KAEd,QAA3BD,EAASC,cAAgC,KACd,QAA3BD,EAASC,cAAgC,KACd,QAA3BD,EAASC,cAAgC,KACd,QAA3BD,EAASC,cAAgC,KACd,QAA3BD,EAASC,cAAgC,KACrC,KARc,EASzB,CAEO,SAASE,EAAWC,GACvB,IAAIA,EAAS,MAAO,GACpB,MACMC,EAAO,IAAIC,KAAKF,EAAQG,QAAQ,KAAK,MAE3C,MAHc,CAAC,UAAU,WAAW,QAAQ,QAAQ,MAAM,OAAO,OAAO,SAAS,YAAY,UAAU,WAAW,YAGrGF,EAAKG,YAAc,IAAMH,EAAKI,UAAY,IAAMJ,EAAKK,aACtE,CAEO,SAASC,EAASC,GACrB,OAAGA,EAAKC,YAAoBD,EAAKC,YAC9BD,EAAKE,MAAcF,EAAKE,MACpB,GACX,CAEO,SAASC,EAAiBC,GAC7B,OAAOA,EAAElC,WAAWyB,QAAQ,wBAAyB,IACzD,CAEO,SAASU,EAAaC,GAC3B,MAAMC,EAAcC,WAAWF,GAC/B,OACMH,EADEI,EAAc,GAAM,EACLA,EAAYE,QAAQ,GACpBF,EAAYE,QAAQ,GAC7C,CAEO,SAASC,EAAatB,EAAUc,GACnC,OAAId,GAAac,EAEdS,MAAMT,GAAe,GAErBM,WAAWN,IAAU,EACU,QAA3Bd,EAASC,cACDgB,EAAaH,GAAOU,eAAe,SAAW,OACpB,QAA3BxB,EAASC,cACRgB,EAAaH,GAAOU,eAAe,SAAW,MACpB,QAA3BxB,EAASC,cACRgB,EAAaH,GAAOU,eAAe,SAAW,MACpB,QAA3BxB,EAASC,cACRgB,EAAaH,GAAOU,eAAe,SAAW,MACpB,QAA3BxB,EAASC,cACRgB,EAAaH,GAAOU,eAAe,SAAW,KACrB,QAA3BxB,EAASC,cACPgB,EAAaH,GAAOU,eAAe,SAAW,MACrB,QAA3BxB,EAASC,cACP,KAAOgB,EAAaH,GAAOU,eAAe,SAChB,QAA3BxB,EAASC,cACRgB,EAAaH,GAAOU,eAAe,SAAW,KACpB,QAA3BxB,EAASC,cACRgB,EAAaH,GAAOU,eAAe,SAAW,MACpB,QAA3BxB,EAASC,cACRgB,EAAaH,GAAOU,eAAe,SAAW,KACpB,QAA3BxB,EAASC,cACRgB,EAAaH,GAAOU,eAAe,SAAW,IAElDzB,EAAYC,GAAYiB,EAAaH,GAAOU,eAAe,SAG/D,IAAMzB,EAAYC,KAAoC,EAAvBiB,EAAaH,IAAaU,eAAe,SA/BhD,EAgCnC,CAEO,SAASC,EAAQC,EAAKC,GAEzBD,EAAM,IAAIpB,KAAKoB,GAEf,IAAIE,IADJD,EAAM,IAAIrB,KAAKqB,IACAE,UAAYH,EAAIG,WAAa,IAE5C,OADAD,GAAQ,GACDpD,KAAKsD,IAAItD,KAAKK,MAAM+C,GAC/B,CAEO,SAASG,EAAe3B,GAC3B,IAAIA,EAAS,MAAO,GACpB,MACMC,EAAO,IAAIC,KAAKF,EAAQG,QAAQ,KAAK,MAE3C,MAHc,CAAC,UAAU,WAAW,QAAQ,QAAQ,MAAM,OAAO,OAAO,SAAS,YAAY,UAAU,WAAW,YAGrGF,EAAKG,YAAc,IAAMH,EAAKI,UAAY,IAAMJ,EAAKK,cAAgB,IAAML,EAAK2B,WAAa,IAAM3B,EAAK4B,YACzH,CAEO,SAASC,EAAUC,GAAU,IAAT,KAACC,GAAKD,EAC3BE,EAAa,GACbC,EAAW,GAgBf,MAf8B,WAA1BF,EAAKG,mBACPF,EAAaf,EAAac,EAAKpC,SAAUoB,WAAWgB,EAAKtB,MAAQ,KAAKO,QAAQ,IAC9EiB,GAAWE,EAAAA,EAAAA,MAAA,OAAKC,MAAM,eAAcC,SAAA,CAAC,UAAQpB,EAAac,EAAKpC,SAAUoC,EAAKtB,OAAO,YAGzD,YAA1BsB,EAAKG,mBACPF,EAAaf,EAAac,EAAKpC,SAAUoB,WAAWgB,EAAKtB,MAAQ,IAAIO,QAAQ,IAC7EiB,GAAWE,EAAAA,EAAAA,MAAA,OAAKC,MAAM,eAAcC,SAAA,CAAC,UAAQpB,EAAac,EAAKpC,SAAUoC,EAAKtB,OAAO,aAGnFsB,EAAKvB,cACPwB,EAAaf,EAAac,EAAKpC,SAAUoB,WAAWgB,EAAKvB,YAAcuB,EAAKO,YAAYtB,QAAQ,IAChGiB,GAAWE,EAAAA,EAAAA,MAAA,OAAKC,MAAM,eAAcC,SAAA,CAAC,UAAQpB,EAAac,EAAKpC,SAAUoC,EAAKvB,kBAI9E2B,EAAAA,EAAAA,MAAAI,EAAAA,SAAA,CAAAF,SAAA,EACEF,EAAAA,EAAAA,MAAA,KAAGK,UAAW,qCAA+D,MAA1BC,EAAAA,EAAAA,IAAU,YAAsB,OAAS,IAAKJ,SAAA,CAC9FL,EAAW,KAACU,EAAAA,EAAAA,KAAA,QAAMF,UAAU,UAASH,SAAC,gBAExCJ,IAGP,CAEO,SAASU,EAAcC,GAAU,IAAT,KAACb,GAAKa,EACnC,MAA2B,QAAvBH,EAAAA,EAAAA,IAAU,SACLZ,EAAW,CAACE,SAEjBA,EAAKvB,aACCkC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wCAAuCH,SAAEpB,EAAac,EAAKpC,SAAUoC,EAAKvB,gBAG/F2B,EAAAA,EAAAA,MAAA,KAAGK,UAAU,wCAAuCH,SAAA,CACjDpB,EAAac,EAAKpC,SAAUoC,EAAKtB,QAClC0B,EAAAA,EAAAA,MAAA,QAAMK,UAAU,UAASH,SAAA,CAAC,IACK,YAA1BN,EAAKG,iBAAiC,QAAU,YAI3D,CAqBO,SAASW,IACd,MAAMC,EAAkBxD,OAAOyD,SAASC,KAExC,OAAIF,EAAgBtF,QAAQ,YAAc,EAC/B,WACAsF,EAAgBtF,QAAQ,QAAU,EAClC,OAGJ,EACT,C,yGCmEA,QA1OA,SAAoBsE,GAiBhB,IAjBiB,KACjBvB,EAAI,kBACJ0C,EAAiB,aACjBhC,EAAY,aACZiC,EAAY,IACZC,EAAG,oBACHC,EAAmB,QACnBC,EAAO,UACPC,EAAS,cACTC,EAAa,WACbC,EAAU,UACVC,EAAS,WACTC,EAAU,SACV9E,EAAQ,WACR+E,EAAU,UACVC,EAAS,UACTC,GACH/B,EAEC,MAAOgC,EAASC,IAAcjF,EAAAA,EAAAA,UAAS,eAYrC,OAVFK,EAAAA,EAAAA,WAAU,KACR,MACM6E,EADe,IAAIC,gBAAgB3E,OAAOyD,SAASmB,QACvBC,IAAI,WAClCH,EACFD,EAAWC,IACHvB,EAAAA,EAAAA,IAAU,YAChBsB,GAAWtB,EAAAA,EAAAA,IAAU,aAExB,KAEMC,EAAAA,EAAAA,KAAAH,EAAAA,SAAA,CAAAF,UACHK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,8CAA6CH,UACxDK,EAAAA,EAAAA,KAAA,OAAKF,UAAW,0BAA0BH,UACtCF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,SAAQH,SAAA,EACnBK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,iBAAgBH,UAC3BF,EAAAA,EAAAA,MAAA,OAAKK,UAAW,OAAOH,SAAA,EACnBK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,gBACfL,EAAAA,EAAAA,MAAA,OAAKK,UAAU,sBAAqBH,SAAA,EAChCK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,8EAA6EH,SAAC,kBAG5FF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,0BAAyBH,SAAA,CAAC,gCAA8ByB,SAE3EpB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,qBAGvBL,EAAAA,EAAAA,MAAA,OAAKK,UAAW,8DAA8DH,SAAA,EAC1EK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,mEAAkEH,UAC7EF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,4DAA2DH,SAAA,EACtEK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,qGAAoGH,SAAC,qBACnHF,EAAAA,EAAAA,MAAA,MAAIK,UAAU,wDAAuDH,SAAA,EACrEK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,WAAUH,SAAC,YACzBK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,WAAUH,SAAC,cAGzBF,EAAAA,EAAAA,MAAA,MAAIK,UAAU,WAAUH,SAAA,CAAC,QAErBK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,sEAAqEH,SAAC,YAIzFK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,WAAUH,SAAC,WACzBK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,WAAUH,SAAC,eACzBK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,WAAUH,SAAC,eAEzBK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,+DAA8DH,SAAC,sBAC7EF,EAAAA,EAAAA,MAAA,MAAIK,UAAU,sDAAqDH,SAAA,EACnEK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,WAAUH,SAAC,cACzBK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,WAAUH,SAAC,aAEzBK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,iDAAgDH,SAAC,cAC/DF,EAAAA,EAAAA,MAAA,MAAIK,UAAU,4CAA2CH,SAAA,EACzDK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,WAAUH,SAAC,oBACzBK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,WAAUH,SAAC,oBACzBK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,WAAUH,SAAC,0BAKjCK,EAAAA,EAAAA,KAAA,OAAKF,UAAW,yBAAyBH,UACrCF,EAAAA,EAAAA,MAAA,OAAKK,UAAW,iEAAiEH,SAAA,CAC5E9B,aAAI,EAAJA,EAAM6D,IAAI,CAACrC,EAAMsC,IAClBpB,EAAkBlB,IACdW,EAAAA,EAAAA,KAAA,OAAiBF,UAAW,sEAAsEH,UAClGK,EAAAA,EAAAA,KAAA,OAAKF,UAAW,WAAsB,IAAV6B,GAAyB,IAAVA,EAAe,eAAiB,sDAAuDhC,UAClIF,EAAAA,EAAAA,MAAA,OAAKK,UAAW,QAAyB,WAAjBU,EAA4B,UAAW,gCAAgCb,SAAA,EAC3FK,EAAAA,EAAAA,KAAA,MAAIF,UAAW,wDAAwDH,SAAEN,EAAKuC,QACtD,QAAvB7B,EAAAA,EAAAA,IAAU,UAAqBC,EAAAA,EAAAA,KAACb,EAAAA,GAAU,CAACE,KAAMA,KAC7CqB,GAAiD,WAA1BrB,EAAKG,kBAA0C,OAARiB,GAA4B,OAAZE,EAM3EtB,EAAKvB,aAELkC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,oCAAmCH,SAC3CpB,EAAac,EAAKpC,SAAUoC,EAAKvB,gBAItC2B,EAAAA,EAAAA,MAAA,KAAGK,UAAW,sDAAyGH,SAAA,CACrG,OAAZgB,GAAgC,OAAZA,GAA+C,WAA1BtB,EAAKG,iBAE9CjB,EAAac,EAAKpC,SAAUoC,EAAKtB,OADjCQ,EAAac,EAAKpC,SAAUoB,WAAWgB,EAAKtB,MAAQ,IAAIO,QAAQ,KAElEmB,EAAAA,EAAAA,MAAA,QAAMK,UAAU,wBAAuBH,SAAA,CAAC,IACzB,OAAZgB,GAAgC,OAAZA,GAA+C,WAA1BtB,EAAKG,iBAEjB,YAA1BH,EAAKG,iBACL,SACA,QAHA,gBAjBVC,EAAAA,EAAAA,MAAA,KAAGK,UAAW,sCAA6C,OAARW,GAA4B,OAAZE,EAAmB,OAAS,IAAKhB,SAAA,CACnGpB,EAAac,EAAKpC,SAAUoB,WAAWgB,EAAKtB,MAAQ,IAAIO,QAAQ,KACjE0B,EAAAA,EAAAA,KAAA,QAAMF,UAAU,wBAAuBH,SAAC,gBAwBhDK,EAAAA,EAAAA,KAAC6B,EAAAA,EAAOC,OAAM,CACVhC,UAAW,uPACXiC,MAAO,CAACC,iBAAiBjH,EAAAA,EAAAA,IAAQ6F,IACjCqB,WAAY,CAAEC,MAAO,KAAMF,iBAAiB5G,EAAAA,EAAAA,IAAYwF,IACxDuB,SAAU,CAAED,MAAO,IACnBE,QAASA,IAAMpB,EAAW3B,EAAKgD,SAAS1C,SAEvC,sBAES,OAAZgB,GAAgC,OAAZA,IAA+C,WAA1BtB,EAAKG,kBAAwD,QAAvBO,EAAAA,EAAAA,IAAU,WACvFN,EAAAA,EAAAA,MAAA,QAAMK,UAAW,yGAAyGH,SAAA,CAAC,MACvHpB,EAAac,EAAKpC,SAAUoC,EAAKtB,OAAO,cAI3C2C,GAAiD,WAA1BrB,EAAKG,mBAAkC,CAAC,KAAM,KAAM,MAAM8C,SAAS3B,IAAmC,QAAvBZ,EAAAA,EAAAA,IAAU,WAC/G,CAAC,KAAM,KAAM,MAAMuC,SAAS3B,IAAoB,KAARF,GAAqC,QAAvBV,EAAAA,EAAAA,IAAU,YAElEC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,eAAcH,SAAC,qBAElCK,EAAAA,EAAAA,KAAA,OAAKF,UAAW,QAAqB,OAAZa,GAAgC,OAAZA,EAAoB,OAAS,0BAAyC,SAAfM,EAAwB,YAAc,qBAAqBR,IAAMd,UAEjKF,EAAAA,EAAAA,MAAA,MAAIK,UAAU,mCAAkCH,SAAA,CAC5B,UAAnBN,EAAKkD,YACF9C,EAAAA,EAAAA,MAAAI,EAAAA,SAAA,CAAAF,SAAA,EACAK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,iEACdL,EAAAA,EAAAA,MAAA,MAAIK,UAAU,8DAA6DH,SAAA,CAAEzD,GAAY,YAAWuD,EAAAA,EAAAA,MAAA,QAAMK,UAAU,2BAA0BH,SAAA,CAAC,KAACK,EAAAA,EAAAA,KAAA,OAAKwC,IAAKtB,EAAWuB,IAAI,WAAW3C,UAAU,4BAC9LL,EAAAA,EAAAA,MAAA,MAAIK,UAAU,8DAA6DH,SAAA,CAAEzD,GAAY,aAAYuD,EAAAA,EAAAA,MAAA,QAAMK,UAAU,2BAA0BH,SAAA,CAAC,KAACK,EAAAA,EAAAA,KAAA,OAAKwC,IAAKtB,EAAWuB,IAAI,WAAW3C,UAAU,4BAC7L5D,IACEuD,EAAAA,EAAAA,MAAAI,EAAAA,SAAA,CAAAF,SAAA,EACAK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,8DAA6DH,SAAC,OAC5EK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,8DAA6DH,SAAC,OAC5EK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,8DAA6DH,SAAC,UAGhFF,EAAAA,EAAAA,MAAA,MAAIK,UAAU,8DAA6DH,SAAA,CAAEzD,GAAY,WAAW,aAAW,aAC/G8D,EAAAA,EAAAA,KAAA,MAAIF,UAAU,iEACdL,EAAAA,EAAAA,MAAA,MAAIK,UAAU,8DAA6DH,SAAA,CAAEzD,GAAY,aAAa,YAAU,aAChHuD,EAAAA,EAAAA,MAAA,MAAIK,UAAU,8DAA6DH,SAAA,CAAEzD,GAAY,SAAS,YAAU,aAC5G8D,EAAAA,EAAAA,KAAA,MAAIF,UAAU,iEACdL,EAAAA,EAAAA,MAAA,MAAIK,UAAU,8DAA6DH,SAAA,CAAEzD,GAAY,mBAAmB,eAC5GuD,EAAAA,EAAAA,MAAA,MAAIK,UAAU,8DAA6DH,SAAA,CAAEzD,GAAY,mBAAmB,iBAAe,aAC3HuD,EAAAA,EAAAA,MAAA,MAAIK,UAAU,8DAA6DH,SAAA,CAAEzD,GAAY,iBAAkB,GAAGmD,aAAI,EAAJA,EAAMuC,gBAAevC,aAAI,EAAJA,EAAMqD,kBAAkBrD,aAAI,EAAJA,EAAMtB,qBAGhJ,QAAnBsB,EAAKkD,YACH9C,EAAAA,EAAAA,MAAAI,EAAAA,SAAA,CAAAF,SAAA,EACAK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,iEACdL,EAAAA,EAAAA,MAAA,MAAIK,UAAU,8DAA6DH,SAAA,CAAEzD,GAAY,YAAWuD,EAAAA,EAAAA,MAAA,QAAMK,UAAU,2BAA0BH,SAAA,CAAC,KAACK,EAAAA,EAAAA,KAAA,OAAKwC,IAAKtB,EAAWuB,IAAI,WAAW3C,UAAU,4BAC9LL,EAAAA,EAAAA,MAAA,MAAIK,UAAU,8DAA6DH,SAAA,CAAEzD,GAAY,aAAYuD,EAAAA,EAAAA,MAAA,QAAMK,UAAU,2BAA0BH,SAAA,CAAC,KAACK,EAAAA,EAAAA,KAAA,OAAKwC,IAAKtB,EAAWuB,IAAI,WAAW3C,UAAU,4BAC/LL,EAAAA,EAAAA,MAAA,MAAIK,UAAU,8DAA6DH,SAAA,CAAEzD,GAAY,SAAQuD,EAAAA,EAAAA,MAAA,QAAMK,UAAU,2BAA0BH,SAAA,CAAC,KAACK,EAAAA,EAAAA,KAAA,OAAKwC,IAAKtB,EAAWuB,IAAI,WAAW3C,UAAU,4BACzL5D,IACEuD,EAAAA,EAAAA,MAAAI,EAAAA,SAAA,CAAAF,SAAA,EACAK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,8DAA6DH,SAAC,OAC5EK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,8DAA6DH,SAAC,UAGhFF,EAAAA,EAAAA,MAAA,MAAIK,UAAU,8DAA6DH,SAAA,CAAEzD,GAAY,WAAW,aAAW,aAC/G8D,EAAAA,EAAAA,KAAA,MAAIF,UAAU,iEACdL,EAAAA,EAAAA,MAAA,MAAIK,UAAU,8DAA6DH,SAAA,CAAEzD,GAAY,aAAa,YAAU,aAChHuD,EAAAA,EAAAA,MAAA,MAAIK,UAAU,8DAA6DH,SAAA,CAAEzD,GAAY,SAAS,YAAU,aAC5G8D,EAAAA,EAAAA,KAAA,MAAIF,UAAU,iEACdL,EAAAA,EAAAA,MAAA,MAAIK,UAAU,8DAA6DH,SAAA,CAAEzD,GAAY,mBAAmB,WAC5GuD,EAAAA,EAAAA,MAAA,MAAIK,UAAU,8DAA6DH,SAAA,CAAEzD,GAAY,mBAAmB,mBAAiB,aAC7HuD,EAAAA,EAAAA,MAAA,MAAIK,UAAU,8DAA6DH,SAAA,CAAEzD,GAAY,iBAAkB,GAAGmD,aAAI,EAAJA,EAAMuC,gBAAevC,aAAI,EAAJA,EAAMqD,kBAAkBrD,aAAI,EAAJA,EAAMtB,qBAGjJ,WAAnBsB,EAAKkD,YACF9C,EAAAA,EAAAA,MAAAI,EAAAA,SAAA,CAAAF,SAAA,EACAK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,iEACdL,EAAAA,EAAAA,MAAA,MAAIK,UAAU,8DAA6DH,SAAA,CAAEzD,GAAY,YAAWuD,EAAAA,EAAAA,MAAA,QAAMK,UAAU,2BAA0BH,SAAA,CAAC,KAACK,EAAAA,EAAAA,KAAA,OAAKwC,IAAKtB,EAAWuB,IAAI,WAAW3C,UAAU,4BAC9LL,EAAAA,EAAAA,MAAA,MAAIK,UAAU,8DAA6DH,SAAA,CAAEzD,GAAY,aAAYuD,EAAAA,EAAAA,MAAA,QAAMK,UAAU,2BAA0BH,SAAA,CAAC,KAACK,EAAAA,EAAAA,KAAA,OAAKwC,IAAKtB,EAAWuB,IAAI,WAAW3C,UAAU,4BAC/LL,EAAAA,EAAAA,MAAA,MAAIK,UAAU,8DAA6DH,SAAA,CAAEzD,GAAY,UAASuD,EAAAA,EAAAA,MAAA,QAAMK,UAAU,2BAA0BH,SAAA,CAAC,KAACK,EAAAA,EAAAA,KAAA,OAAKwC,IAAKtB,EAAWuB,IAAI,WAAW3C,UAAU,4BAC5LL,EAAAA,EAAAA,MAAA,MAAIK,UAAU,8DAA6DH,SAAA,CAAEzD,GAAY,WAAUuD,EAAAA,EAAAA,MAAA,QAAMK,UAAU,2BAA0BH,SAAA,CAAC,KAACK,EAAAA,EAAAA,KAAA,OAAKwC,IAAKtB,EAAWuB,IAAI,WAAW3C,UAAU,4BAC7LL,EAAAA,EAAAA,MAAA,MAAIK,UAAU,8DAA6DH,SAAA,CAAEzD,GAAY,eAAcuD,EAAAA,EAAAA,MAAA,QAAMK,UAAU,2BAA0BH,SAAA,CAAC,KAACK,EAAAA,EAAAA,KAAA,OAAKwC,IAAKtB,EAAWuB,IAAI,WAAW3C,UAAU,4BACjML,EAAAA,EAAAA,MAAA,MAAIK,UAAU,8DAA6DH,SAAA,CAAEzD,GAAY,YAAW8D,EAAAA,EAAAA,KAAA,QAAMF,UAAU,2BAA0BH,SAAC,kBAC/IK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,iEACdL,EAAAA,EAAAA,MAAA,MAAIK,UAAU,8DAA6DH,SAAA,CAAEzD,GAAY,cAAa8D,EAAAA,EAAAA,KAAA,QAAMF,UAAU,2BAA0BH,SAAC,kBACjJF,EAAAA,EAAAA,MAAA,MAAIK,UAAU,8DAA6DH,SAAA,CAAEzD,GAAY,SAAS,aAAW,aAC7G8D,EAAAA,EAAAA,KAAA,MAAIF,UAAU,iEACdL,EAAAA,EAAAA,MAAA,MAAIK,UAAU,8DAA6DH,SAAA,CAAEzD,GAAY,mBAAmB,eAC5GuD,EAAAA,EAAAA,MAAA,MAAIK,UAAU,8DAA6DH,SAAA,CAAEzD,GAAY,oBAAmB8D,EAAAA,EAAAA,KAAA,QAAMF,UAAU,2BAA0BH,SAAC,kBACvJF,EAAAA,EAAAA,MAAA,MAAIK,UAAU,8DAA6DH,SAAA,CAAEzD,GAAY,kBAAiB8D,EAAAA,EAAAA,KAAA,QAAMF,UAAU,2BAA0BH,SAAE,GAAGN,aAAI,EAAJA,EAAMuC,gBAAevC,aAAI,EAAJA,EAAMqD,kBAAkBrD,aAAI,EAAJA,EAAMtB,kCAnH9M4D,GA4HV,IAGa,OAAfb,GAA+B,OAARL,GAAiC,WAAjBD,GAAkD,QAArB3C,EAAK,GAAGZ,UAC9E+C,EAAAA,EAAAA,KAAAH,EAAAA,SAAA,CAAAF,SACCkB,MAEC,aAKE,OAAdE,GACEf,EAAAA,EAAAA,KAAAH,EAAAA,SAAA,CAAAF,UACIK,EAAAA,EAAAA,KAAC2C,EAAAA,SAAQ,CAACC,SAAU,KAAKjD,UACrBK,EAAAA,EAAAA,KAACmB,EAAS,QAGpB,aAKtB,C,wNCnOA,MAAMA,EAAY0B,EAAAA,KAAW,IAAM,6EA+rBnC,QA7rBA,SAAgBC,GACd,MAAM,IAAQC,EAAAA,EAAAA,MACRlF,EAAOiF,EAAMjF,KAAOiF,EAAMjF,KAAO,KACjCmD,EAAa8B,EAAM9B,WAAa8B,EAAM9B,WAAa,OACzD,IAAIP,GAAMV,EAAAA,EAAAA,IAAU,QAASA,EAAAA,EAAAA,IAAU,OAA6CiD,KAChFpC,GAAYb,EAAAA,EAAAA,IAAU,cAAeA,EAAAA,EAAAA,IAAU,aAAe,SAElE,MACMkD,KADqB,CAAC,KAAK,KAAK,KAAK,KAAK,MAAM,MAAM,OACrBX,SAAS7B,KAAQqC,EAAMG,aAAcH,EAAMG,WAC5EC,GAAa,CAAC,KAAK,KAAK,MAAMZ,SAAS7B,GACvCM,GAAYhB,EAAAA,EAAAA,IAAU,eAAgBA,EAAAA,EAAAA,IAAU,cAAgB,GAChEe,GAAaf,EAAAA,EAAAA,IAAU,eAAgBA,EAAAA,EAAAA,IAAU,cAAgB,MACjEY,GAAUZ,EAAAA,EAAAA,IAAU,aAAcA,EAAAA,EAAAA,IAAU,YAAc,IACxDkB,IAAe7E,EAAAA,EAAAA,WAAS2D,EAAAA,EAAAA,IAAU,gBACnC7D,EAAUiH,IAAe/G,EAAAA,EAAAA,WAAS,IACnC,SACJC,EAAQ,WACR+G,EAAU,WACVC,EAAU,WACVC,EAAU,WACVC,EAAU,WACVC,EAAU,YACVC,EAAW,aACXC,EAAY,aACZC,IACE1H,EAAAA,EAAAA,MACE2H,EAAiB/F,IAAUA,EAAK,GAAG+B,YAAc/B,EAAK,GAAGC,aAAgB,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,MAAMwE,SAAS7B,KACjHoD,EAAkBC,IAAuB1H,EAAAA,EAAAA,WAAS,GAGnD2H,GAAgBhE,EAAAA,EAAAA,IAAU,YAAc,MAE9CtD,EAAAA,EAAAA,WAAU,KACR,MAAMuH,EAAWtJ,SAASsJ,SACpBC,EAAeD,EAAW,IAAIE,IAAIF,GAAUG,SAAW,IAOzB,UAAhCpE,EAAAA,EAAAA,IAAU,mBALS,CACrB,+BACA,wBAImBuC,SAAS2B,KAE5BH,GAAoB,IAErB,IAEH,IAAIpD,GAAsB,GAE1BjE,EAAAA,EAAAA,WAAU,KACR,MAAM2H,EAAkBA,KACtBjB,EAAYvG,OAAOC,YAAc,MAOnC,OAHAuH,IACAxH,OAAOE,iBAAiB,SAAUsH,GAE3B,IAAMxH,OAAOG,oBAAoB,SAAUqH,IACjD,IAEO,OAAN3D,IACFC,GAAsB,GAGT,OAAXC,GAA+B,OAAZA,IACrBD,GAAsB,GAGxB,MAAQF,EAAc6D,IAAoBjI,EAAAA,EAAAA,UAASsE,EAAsB,SAAW,WAU9E4D,EAAiBjF,GACK,UAAnBA,EAAKkD,WAAyBsB,IAAqBD,EAGtDrD,EAAoB,SAASlB,GACjC,OAAIiF,EAAcjF,MACd4D,GACD5D,EAAKG,iBAAiBtC,gBAAkBsD,EAE7C,EAeMK,EAAgB,WACpB,OAEIb,EAAAA,EAAAA,KAAA,OAAKF,UAAU,mEAAkEH,UAC/EK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,gDAA+CH,UAC5DF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,oCAAmCH,SAAA,EAChDK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,0BAAyBH,SAAE4E,EAAE,8CAC3CvE,EAAAA,EAAAA,KAAA,KAAGF,UAAW,sCAAsD,WAAjBU,GAAqC,OAARC,EAAgB,GAAI,QAASd,SAAE4E,EAAE,6CAC9F,WAAjB/D,GAAqC,OAARC,GAAuC,QAAvBV,EAAAA,EAAAA,IAAU,UAAoBC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,eAAcH,SAAC,oBAAwB,IACnIK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,OAAMH,UACnBK,EAAAA,EAAAA,KAAC6B,EAAAA,EAAOC,OAAM,CACZhC,UAAU,4CACViC,MAAO,CAACC,iBAAiBjH,EAAAA,EAAAA,IAAQ6F,IACjCqB,WAAY,CAAEC,MAAO,KAAMF,iBAAiB5G,EAAAA,EAAAA,IAAYwF,IACxDuB,SAAU,CAAED,MAAO,IACnBE,QAASA,IAAMpB,EAAW,IAAIrB,SAE7B4E,EAAE,8CAGPvE,EAAAA,EAAAA,KAAA,OAAKF,UAAW,4BAA2C,SAAfmB,EAAwB,YAAc,qBAAqBR,IAAMd,UAC3GF,EAAAA,EAAAA,MAAA,MAAIK,UAAU,wBAAuBH,SAAA,EACnCK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,iBAAgBH,UAACK,EAAAA,EAAAA,KAAA,OAAAL,SAAM4E,EAAE,gDACvCvE,EAAAA,EAAAA,KAAA,MAAIF,UAAU,GAAEH,UAACK,EAAAA,EAAAA,KAAA,OAAAL,SAAM4E,EAAE,0DAQzC,EA0BMC,EAtEyBC,MAC7B,IAAK5G,GAAQA,EAAK5C,OAAS,EAAG,OAAO,EAErC,MAAMyJ,EAAM7G,EAAK5C,OAAS,EAC1B,OAAuB,IAAhB4C,EAAK5C,SAAkC,IAAhB4C,EAAK5C,QAAwB,IAARyJ,IAAcJ,EAAczG,EAAK,KAkEjE4G,GACfE,EAAe9G,IAAyB,IAAhBA,EAAK5C,QAAiB4C,EAAK5C,OAAS,GAAM,GAAQ4C,EAAK5C,OAAS,GAAM,GAAMqJ,EAAczG,EAAK,KACvH+G,IAhEC/G,GAEEA,EAAK5C,QAAU,IAAMqJ,EAAczG,EAAK,IAkE3CgH,EAA0BL,IAFC,QAAxB3G,EADUiH,EAGuC,GAFvCvC,WAAuB1E,EAAKiH,GAAKvC,UAAUrF,cAAc6H,WAAW,QADpED,MAInB,MAAME,EAA8C,OAAlBjB,IAEhC,CAAC,MAAMzB,SAASyB,IACfX,IAAe,CAAC,MAAMd,SAASyB,KAC9BP,IAAeJ,GAAc,CAAC,MAAMd,SAASyB,KAC7CV,GAAcC,IAAe,CAAC,MAAMhB,SAASyB,IAC9CV,GAAc,CAAC,MAAMf,SAASyB,IAG3BkB,EAAuBpB,EACvBqB,GAA6BD,GAAwBN,EACrDQ,EAA2B,CAAC,KAAK,MAAM7C,SAASyB,KAAmBH,IAAmBY,IAAiBtI,IAAawH,IAAiBD,IAAgBE,EAG3J,IAAIyB,EAAQ,GACZ,GAAGhC,EACD,OAAQW,GACN,IAAK,KAAMqB,EAAQ,GAAI,MACvB,IAAK,KACL,IAAK,KACHA,EAAQ,GACR,MACF,IAAK,KAAMA,EAAQ,GAAI,MACvB,IAAK,KACL,IAAK,KACHA,EAAQ,GACR,MACF,IAAK,KAAMA,EAAQ,QAGhB,GAAG5B,EACR,OAAQO,GACN,IAAK,KAAMqB,EAAQ,GAAI,MACvB,IAAK,KACL,IAAK,KACHA,EAAQ,GAAI,MACd,IAAK,KAAMA,EAAQ,GAAI,MACvB,IAAK,KAAMA,EAAQ,QAGhB,GAAG7B,EACR,OAAQQ,GACN,IAAK,KAAMqB,EAAQ,GAAI,MACvB,IAAK,KACL,IAAK,KACHA,EAAQ,GAAI,MACd,IAAK,KAAMA,EAAQ,GAAI,MACvB,IAAK,KAAMA,EAAQ,QAGhB,GAAG/B,EACR,OAAQU,GACN,IAAK,KAAMqB,EAAQ,GAAI,MACvB,IAAK,KACL,IAAK,KACHA,EAAQ,GAAI,MACd,IAAK,KACL,IAAK,KACL,IAAK,KACHA,EAAQ,QAGP,GAAG9B,EACR,OAAQS,GACN,IAAK,KAAMqB,EAAQ,GAAI,MACvB,IAAK,KAAMA,EAAQ,GAAI,MACvB,IAAK,KACL,IAAK,KACHA,EAAQ,GAAI,MACd,IAAK,KACL,IAAK,KACHA,EAAQ,GAAI,MACd,IAAK,KAAMA,EAAQ,GAKvB,IAAIC,EAAoB,YACxB,OAAQtB,GACN,IAAK,KASL,IAAK,KACHsB,EAAoB,GAAGA,+BACvB,MARF,IAAK,KACHA,EAAoB,GAAGA,uFACvB,MACF,IAAK,KACHA,EAAoB,GAAGA,oDACvB,MAIF,IAAK,KACL,IAAK,KACL,IAAK,KACL,IAAK,KACHA,EAAoB,GAAGA,+BAa3B,OACErF,EAAAA,EAAAA,KAAA,OAAKF,UAAU,8CAA6CH,UAC1DK,EAAAA,EAAAA,KAAA,OAAKF,UAAW,2BAA2B8D,GAAkBY,EAAe,YAAc,OAAOZ,IAAkBY,GAAkBtI,EAAyB,GAAd,cAAmByD,UACjKF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,SAAQH,SAAA,EAErBF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,iBAAgBH,SAAA,EAC7BK,EAAAA,EAAAA,KAAA,OAAKF,UAAW,wBAAuB8D,GAAkBY,IAAkBvB,GAAc2B,EAA2B,OAAS,IAC3H7C,OAAQ6B,IAAkBY,GAAkBtI,EAA0C,CAAC,EAAhC,CAAEoJ,YAAa,GAAGF,MAAgBzF,UACzFF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,gBAAeH,SAAA,EAChCK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,8EAA6EH,SACxF4E,EAAE,0CAEDvE,EAAAA,EAAAA,KAAA,OAAKF,UAAW,6BAA4BmF,IAAwBC,GAA+BhJ,EAAmC,GAAxB,uBAA6ByD,SApBlI4F,MACrB,IAAGxF,EAAAA,EAAAA,IAAU,WAAY,CAEvB,OADawE,EAAE,mCACH/G,QAAQ,eAAeuC,EAAAA,EAAAA,IAAU,WAC/C,CACA,OAA0BwE,EAAnBV,EAAqB,qCAA0C,oCAemF0B,UAGhJtC,IACCxD,EAAAA,EAAAA,MAAA,OAAKK,UAAU,MAAKH,SAAA,EAClBK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,qDAEfE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,+CAA8CH,UAC3DF,EAAAA,EAAAA,MAAA,SAAO+F,QAAQ,UAAU1F,UAAU,mCAAkCH,SAAA,EACnEK,EAAAA,EAAAA,KAAA,OAAKF,WAA+B,YAAjBU,EAA6B,0BAA4B,iBAA5D,kBAA6Fb,SAC5G4E,EAAE,sCAEH9E,EAAAA,EAAAA,MAAA,OAAKK,UAAU,WAAUH,SAAA,EACvBK,EAAAA,EAAAA,KAAA,SACEyF,KAAK,WACLC,GAAG,UACH5F,UAAU,iBACV6F,SAzOC,WAEnBtB,EADkB,YAAjB7D,EACe,SAEA,UAEpB,EAoOsBoF,eAAgBlF,KAElBV,EAAAA,EAAAA,KAAA,OAAKF,UAAU,8EACfE,EAAAA,EAAAA,KAAA,OACEF,UAAW,6FAA6G,WAAjBU,EAA4B,gBAAkB,gBAIzJR,EAAAA,EAAAA,KAAA,OAAKF,WAA+B,WAAjBU,EAA4B,0BAA4B,iBAA3D,kBAA4Fb,SACzG4E,EAAE,gDAOf9E,EAAAA,EAAAA,MAAA,OAAKK,UAAW,gEAAgEmF,GAAwBN,IAAiBlB,GAAgByB,EAA4B,aAAe,IAAKvF,SAAA,EACvLK,EAAAA,EAAAA,KAAA,OAAKF,UAAW,+DAA+DmF,GAAwBC,EAA4B,yBAA2B,UAAUvF,UACtKF,EAAAA,EAAAA,MAAA,OAAKK,WAAc8D,GAAkBY,EAAe,YAAc,aAAlD,wEAAqI7E,SAAA,CACjJiE,GAAmBY,GAIVxE,EAAAA,EAAAA,KAAA,OAAKF,UAAW,gCAAgC8D,GAAmBY,IAAiBK,EAA4B,GAAK,oBAH9HpF,EAAAA,EAAAA,MAAAI,EAAAA,SAAA,CAAAF,SAAA,EACEK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,kBAAiBH,SAAE4E,EAAE,2CACnC9E,EAAAA,EAAAA,MAAA,KAAGK,UAAU,8CAA6CH,SAAA,CAAE4E,EAAE,2CAA0CvE,EAAAA,EAAAA,KAAA,SAAMuE,EAAE,iDAIpHvE,EAAAA,EAAAA,KAAA,MAAIF,UAAW,+BAA+B8D,GAAkBY,EAAe,6DAA+Da,KAAsB1F,SAAE4E,EAAE,yCACxK9E,EAAAA,EAAAA,MAAA,MAAIK,UAAU,wDAAuDH,SAAA,EACnEK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,WAAUH,SAAC,YACzBK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,WAAUH,SAAC,cAGzBF,EAAAA,EAAAA,MAAA,MAAIK,UAAU,WAAUH,SAAA,CAAC,QAEvBK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,sEAAqEH,SACjF4E,EAAE,0CAGP9E,EAAAA,EAAAA,MAAA,MAAIK,UAAU,WAAUH,SAAA,CAAC,SAEvBK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,sEAAqEH,SACjF4E,EAAE,0CAGPvE,EAAAA,EAAAA,KAAA,MAAIF,UAAU,WAAUH,SAAC,eACzBK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,WAAUH,SAAC,eAE3BK,EAAAA,EAAAA,KAAA,MAAIF,UAAW,uDAAsDqF,EAA2B,WAAa,YAAaxF,SAAE4E,EAAE,0CAC9H9E,EAAAA,EAAAA,MAAA,MAAIK,UAAU,sDAAqDH,SAAA,EACjEK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,WAAUH,SAAC,cACzBK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,WAAUH,SAAC,aAE3BK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,8DAA6DH,SAAE4E,EAAE,0CAC/EvE,EAAAA,EAAAA,KAAA,MAAIF,UAAU,sDAAqDH,UACjEK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,WAAUH,SAAC,eAE3BK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,iDAAgDH,SAAE4E,EAAE,mCAClE9E,EAAAA,EAAAA,MAAA,MAAIK,UAAU,4CAA2CH,SAAA,EACvDK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,WAAUH,SAAE4E,EAAE,2CAC3BX,IAAkB5D,EAAAA,EAAAA,KAAA,MAAIF,UAAU,WAAUH,SAAE4E,EAAE,kDAKrDvE,EAAAA,EAAAA,KAAA,OAAKF,UAAW,2BAA0B8D,GAAkBY,EAAe,wBAA0B,cAAe7E,SAChHuD,GACAzD,EAAAA,EAAAA,MAAA,OAAKK,UAAW,iEAAiEH,SAAA,CAG9E9B,aAAI,EAAJA,EAAM6D,IAAI,CAACrC,EAAMsC,IAChBpB,EAAkBlB,IAChBI,EAAAA,EAAAA,MAAA,OAAiBK,UAAW,sEAAsEH,SAAA,EAChGK,EAAAA,EAAAA,KAAA,OAAKF,UAAW,WAAsB,IAAV6B,GAAyB,IAAVA,EAAe,eAAiB,sDAAuDhC,UAClIF,EAAAA,EAAAA,MAAA,OAAKK,UAAW,QAAyB,WAAjBU,EAA4B,UAAW,gCAAgCb,SAAA,EAC3FK,EAAAA,EAAAA,KAAA,MAAIF,UAAW,yCAAwC8D,GAAkBY,EAAe,GAAK,qBAAsB7E,SAAoB,OAAlBoE,GAA0BH,EAAiBW,EAAE,oCAAsClF,EAAKuC,QACrL,QAAvB7B,EAAAA,EAAAA,IAAU,UAAqBC,EAAAA,EAAAA,KAACb,EAAAA,GAAU,CAACE,KAAMA,KAC/CqB,GAAiD,WAA1BrB,EAAKG,kBAA0C,OAARiB,GAA4B,OAAZE,EAMzEtB,EAAKvB,aAELkC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,oCAAmCH,UAC7CpB,EAAAA,EAAAA,IAAac,EAAKpC,SAAUoC,EAAKvB,gBAIpC2B,EAAAA,EAAAA,MAAA,KAAGK,UAAW,sDAAyGH,SAAA,CACvG,OAAZgB,GAAgC,OAAZA,GAA+C,WAA1BtB,EAAKG,kBAE5CjB,EAAAA,EAAAA,IAAac,EAAKpC,SAAUoC,EAAKtB,QADjCQ,EAAAA,EAAAA,IAAac,EAAKpC,SAAUoB,WAAWgB,EAAKtB,MAAQ,IAAIO,QAAQ,KAEpEmB,EAAAA,EAAAA,MAAA,QAAMK,UAAU,wBAAuBH,SAAA,CAAC,IACvB,OAAZgB,GAAgC,OAAZA,GAA+C,WAA1BtB,EAAKG,iBAEnB,YAA1BH,EAAKG,iBACH,IAAI+E,EAAE,mCACN,IAAIA,EAAE,kCAHR,IAAIA,EAAE,0CAjBhB9E,EAAAA,EAAAA,MAAA,KAAGK,UAAW,sCAA6C,OAARW,GAA4B,OAAZE,EAAmB,OAAS,IAAKhB,SAAA,EACjGpB,EAAAA,EAAAA,IAAac,EAAKpC,SAAUoB,WAAWgB,EAAKtB,MAAQ,IAAIO,QAAQ,KACjE0B,EAAAA,EAAAA,KAAA,QAAMF,UAAU,wBAAuBH,SAAC,gBAwBhDK,EAAAA,EAAAA,KAAC6B,EAAAA,EAAOC,OAAM,CACZhC,UAAW,yPAAwP8D,GAAkBY,EAAe,YAAc,IAClTzC,MAAO,CAACC,iBAAiBjH,EAAAA,EAAAA,IAAQ6F,IACjCqB,WAAY,CAAEC,MAAO,KAAMF,iBAAiB5G,EAAAA,EAAAA,IAAYwF,IACxDuB,SAAU,CAAED,MAAO,IACnBE,QAASA,IAAMpB,EAAW3B,EAAKgD,SAAS1C,SAEvCtD,IAAauH,EAAiB,mBAAqBW,EAAE,6CAE1C,OAAZ5D,GAAgC,OAAZA,IAA+C,WAA1BtB,EAAKG,kBAAwD,QAAvBO,EAAAA,EAAAA,IAAU,WACzFN,EAAAA,EAAAA,MAAA,QAAMK,UAAW,8FAAyG,OAAZa,EAAmB,eAAiB,iBAAkBhB,SAAA,CAAC,OAC/JpB,EAAAA,EAAAA,IAAac,EAAKpC,SAAUoC,EAAKtB,OAAO,cAI7C2C,GAAiD,WAA1BrB,EAAKG,mBAAkC,CAAC,KAAM,KAAM,MAAM8C,SAAS3B,IAAmC,QAAvBZ,EAAAA,EAAAA,IAAU,WAC/G,CAAC,KAAM,KAAM,MAAMuC,SAAS3B,IAAoB,KAARF,GAAqC,QAAvBV,EAAAA,EAAAA,IAAU,YAEhEC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,eAAcH,SAAC,qBAElCK,EAAAA,EAAAA,KAAA,OAAKF,UAAW,QAAqB,OAAZa,GAAgC,OAAZA,EAAoB,OAAS,0BAAyC,SAAfM,EAAwB,YAAc,qBAAqBR,IAAMd,UAEnKF,EAAAA,EAAAA,MAAA,MAAIK,UAAU,mCAAkCH,SAAA,CAC1B,UAAnBN,EAAKkD,YACJ9C,EAAAA,EAAAA,MAAAI,EAAAA,SAAA,CAAAF,SAAA,EACEK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,iEACdL,EAAAA,EAAAA,MAAA,MAAIK,UAAU,8DAA6DH,SAAA,CAAEzD,GAAY,YAAWuD,EAAAA,EAAAA,MAAA,QAAMK,UAAU,2BAA0BH,SAAA,CAAC,KAACK,EAAAA,EAAAA,KAAA,OAAKwC,IAAKtB,EAAAA,EAAWuB,IAAI,WAAW3C,UAAU,4BAC9LL,EAAAA,EAAAA,MAAA,MAAIK,UAAU,8DAA6DH,SAAA,CAAEzD,GAAY,aAAYuD,EAAAA,EAAAA,MAAA,QAAMK,UAAU,2BAA0BH,SAAA,CAAC,KAACK,EAAAA,EAAAA,KAAA,OAAKwC,IAAKtB,EAAAA,EAAWuB,IAAI,WAAW3C,UAAU,4BAC7L5D,IACAuD,EAAAA,EAAAA,MAAAI,EAAAA,SAAA,CAAAF,SAAA,EACAK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,8DAA6DH,SAAC,OAC5EK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,8DAA6DH,SAAC,OAC5EK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,8DAA6DH,SAAC,UAG9EF,EAAAA,EAAAA,MAAA,MAAIK,UAAU,8DAA6DH,SAAA,CACxEzD,GAAY,WACM,OAAlB6H,GAA0BH,EAAiBW,EAAE,qCAAuC,aAClE,OAAlBR,GAA2B1H,IAAYH,GAAc0H,IAAkB7D,EAAAA,EAAAA,IAAU,SAAgC,SAAV,OAAsB,SAChIC,EAAAA,EAAAA,KAAA,MAAIF,UAAW,uDAAsDqF,EAA2B,WAAa,eAC7G1F,EAAAA,EAAAA,MAAA,MAAIK,UAAU,8DAA6DH,SAAA,CACxEzD,GAAY,aACM,OAAlB6H,GAA0BH,EAAiBW,EAAE,qCAAuC,YAClE,OAAlBR,GAA2B1H,IAAYH,GAAc0H,IAAkB7D,EAAAA,EAAAA,IAAU,SAAgC,SAAV,OAAsB,SAEhIN,EAAAA,EAAAA,MAAA,MAAIK,UAAU,8DAA6DH,SAAA,CACxEzD,GAAY,SACM,OAAlB6H,GAA0BH,EAAiBW,EAAE,mCAAqC,YAChE,OAAlBR,GAA2B1H,IAAYH,GAAc0H,IAAkB7D,EAAAA,EAAAA,IAAU,SAAgC,SAAV,OAAsB,SAEhIC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,iEACZ5D,IACAuD,EAAAA,EAAAA,MAAAI,EAAAA,SAAA,CAAAF,SAAA,EACEK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,8DAA6DH,SAAC,OAC5EK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,oEAGlBL,EAAAA,EAAAA,MAAA,MAAIK,UAAW,uDAAsD5D,IAAaG,EAAW,WAAa,YAAasD,SAAA,CACpHzD,GAAY,GAAGqI,EAAE,6CACC,OAAlBR,GAA0BH,EAAiBW,EAAE,4CAA8C,kBAC1FlI,IAAYH,GAAc0H,EAA+E,OAAlBG,EAAyB,SAAW,IAA/EhE,EAAAA,EAAAA,IAAU,SAAW,SAAW,UAE/E6D,IAAkBnE,EAAAA,EAAAA,MAAA,MAAIK,UAAW,uDAAuD5D,IAAaG,GAAa2I,EAA4B,WAAa,YAAarF,SAAA,CACtKzD,GAAY,GAAGqI,EAAE,2CACC,OAAlBR,GAA0BH,EACR,OAAlBG,GAA4C,OAAlBA,GAA4C,OAAlBA,GAA4C,OAAlBA,GAA4C,OAAlBA,GAA6C,OAAlBA,EAClIQ,EAAE,2CAEiB,OAAlBR,EACCQ,EAAE,2CAA6C,IAAGlF,aAAI,EAAJA,EAAMqD,kBAAgBrD,aAAI,EAAJA,EAAMtB,SAAUwG,EAAE,2CAErE,OAAlBR,EACU,OAARtD,EACC8D,EAAE,2CAA6C,IAAGlF,aAAI,EAAJA,EAAMtB,QAAMsB,aAAI,EAAJA,EAAMqD,mBAAoB6B,EAAE,2CAE1FA,EAAE,2CAA6C,IAAGlF,aAAI,EAAJA,EAAMqD,kBAAgBrD,aAAI,EAAJA,EAAMtB,SAAUwG,EAAE,2CAG5F,GAAGlF,aAAI,EAAJA,EAAMuC,gBAAerD,EAAAA,EAAAA,IAAac,EAAKpC,SAAUoC,EAAKtB,eAE7D,GAAGsB,aAAI,EAAJA,EAAMuC,gBAAerD,EAAAA,EAAAA,IAAac,EAAKpC,SAAUoC,EAAKtB,qBAI9C,QAAnBsB,EAAKkD,YACL9C,EAAAA,EAAAA,MAAAI,EAAAA,SAAA,CAAAF,SAAA,EACEK,EAAAA,EAAAA,KAAA,MAAIF,UAAW,iEACfL,EAAAA,EAAAA,MAAA,MAAIK,UAAU,8DAA6DH,SAAA,CAAEzD,GAAY,YAAWuD,EAAAA,EAAAA,MAAA,QAAMK,UAAU,2BAA0BH,SAAA,CAAC,KAACK,EAAAA,EAAAA,KAAA,OAAKwC,IAAKtB,EAAAA,EAAWuB,IAAI,WAAW3C,UAAU,4BAC9LL,EAAAA,EAAAA,MAAA,MAAIK,UAAU,8DAA6DH,SAAA,CAAEzD,GAAY,cAAauD,EAAAA,EAAAA,MAAA,QAAMK,UAAU,2BAA0BH,SAAA,CAAC,KAACK,EAAAA,EAAAA,KAAA,OAAKwC,IAAKtB,EAAAA,EAAWuB,IAAI,WAAW3C,UAAU,4BAChML,EAAAA,EAAAA,MAAA,MAAIK,UAAU,8DAA6DH,SAAA,CAAEzD,GAAY,UAASuD,EAAAA,EAAAA,MAAA,QAAMK,UAAU,2BAA0BH,SAAA,CAAC,KAACK,EAAAA,EAAAA,KAAA,OAAKwC,IAAKtB,EAAAA,EAAWuB,IAAI,WAAW3C,UAAU,4BAC5LL,EAAAA,EAAAA,MAAA,MAAIK,UAAU,8DAA6DH,SAAA,CAAEzD,GAAY,WAAUuD,EAAAA,EAAAA,MAAA,QAAMK,UAAU,2BAA0BH,SAAA,CAAC,KAACK,EAAAA,EAAAA,KAAA,OAAKwC,IAAKtB,EAAAA,EAAWuB,IAAI,WAAW3C,UAAU,4BAC3L5D,IACA8D,EAAAA,EAAAA,KAAAH,EAAAA,SAAA,CAAAF,UACAK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,8DAA6DH,SAAC,SAG9EF,EAAAA,EAAAA,MAAA,MAAIK,UAAU,8DAA6DH,SAAA,CACxEzD,GAAY,WACM,OAAlB6H,GAA0BH,EAAiBW,EAAE,qCAAuC,aAClE,OAAlBR,GAA2B1H,IAAYH,GAAc0H,IAAkB7D,EAAAA,EAAAA,IAAU,SAAgC,SAAV,OAAsB,SAEhIC,EAAAA,EAAAA,KAAA,MAAIF,UAAW,uDAAsDqF,EAA2B,WAAa,eAC7G1F,EAAAA,EAAAA,MAAA,MAAIK,UAAU,8DAA6DH,SAAA,CACxEzD,GAAY,aACM,OAAlB6H,GAA0BH,EAAiBW,EAAE,qCAAuC,YAClE,OAAlBR,GAA2B1H,IAAYH,GAAc0H,IAAkB7D,EAAAA,EAAAA,IAAU,SAAgC,SAAV,OAAsB,SAEhIN,EAAAA,EAAAA,MAAA,MAAIK,UAAU,8DAA6DH,SAAA,CACxEzD,GAAY,SACM,OAAlB6H,GAA0BH,EAAiBW,EAAE,mCAAqC,YAChE,OAAlBR,GAA2B1H,IAAYH,GAAc0H,IAAkB7D,EAAAA,EAAAA,IAAU,SAAgC,SAAV,OAAsB,SAEhIC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,iEACdL,EAAAA,EAAAA,MAAA,MAAIK,UAAU,8DAA6DH,SAAA,CACxEzD,GAAY,YACM,OAAlB6H,GAA0BH,EAAiBW,EAAE,sCAAwC,WACnE,OAAlBR,GAA2B1H,IAAYH,GAAc0H,IAAkB7D,EAAAA,EAAAA,IAAU,SAAgC,SAAV,OAAsB,SAEhIC,EAAAA,EAAAA,KAAA,MAAIF,UAAW,uDAAsDmF,IAAyBT,EAAe,WAAa,eAC1H/E,EAAAA,EAAAA,MAAA,MAAIK,UAAW,uDAAsD5D,IAAaG,EAAW,WAAa,YAAasD,SAAA,CACpHzD,GAAY,GAAGqI,EAAE,6CACC,OAAlBR,GAA0BH,EAAiBW,EAAE,4CAA8C,oBAC1FlI,IAAYH,GAAc0H,EAA+E,OAAlBG,EAAyB,SAAW,IAA/EhE,EAAAA,EAAAA,IAAU,SAAW,SAAW,UAE/E6D,IAAkBnE,EAAAA,EAAAA,MAAA,MAAIK,UAAW,uDAAuD5D,IAAaG,GAAa2I,EAA4B,WAAa,YAAarF,SAAA,CACtKzD,GAAY,GAAGqI,EAAE,2CACC,OAAlBR,GAA0BH,EACR,OAAlBG,GAA4C,OAAlBA,GAA4C,OAAlBA,GAA4C,OAAlBA,GAA4C,OAAlBA,GAA6C,OAAlBA,EAClIQ,EAAE,2CAEiB,OAAlBR,EACCQ,EAAE,2CAA6C,IAAGlF,aAAI,EAAJA,EAAMqD,kBAAgBrD,aAAI,EAAJA,EAAMtB,SAAUwG,EAAE,2CAErE,OAAlBR,EACU,OAARtD,EACC8D,EAAE,2CAA6C,IAAGlF,aAAI,EAAJA,EAAMtB,QAAMsB,aAAI,EAAJA,EAAMqD,mBAAoB6B,EAAE,2CAE1FA,EAAE,2CAA6C,IAAGlF,aAAI,EAAJA,EAAMqD,kBAAgBrD,aAAI,EAAJA,EAAMtB,SAAUwG,EAAE,2CAG5F,GAAGlF,aAAI,EAAJA,EAAMuC,gBAAerD,EAAAA,EAAAA,IAAac,EAAKpC,SAAUoC,EAAKtB,eAE7D,GAAGsB,aAAI,EAAJA,EAAMuC,gBAAerD,EAAAA,EAAAA,IAAac,EAAKpC,SAAUoC,EAAKtB,qBAI9C,aAAnBsB,EAAKkD,YACL9C,EAAAA,EAAAA,MAAAI,EAAAA,SAAA,CAAAF,SAAA,EACEK,EAAAA,EAAAA,KAAA,MAAIF,UAAW,iEACb5D,IAAY8D,EAAAA,EAAAA,KAAA,MAAIF,UAAU,8DAA6DH,SAAC,OAC1FF,EAAAA,EAAAA,MAAA,MAAIK,UAAU,8DAA6DH,SAAA,CAAEzD,GAAY,cAAauD,EAAAA,EAAAA,MAAA,QAAMK,UAAU,2BAA0BH,SAAA,CAAC,KAACK,EAAAA,EAAAA,KAAA,OAAKwC,IAAKtB,EAAAA,EAAWuB,IAAI,WAAW3C,UAAU,4BAC9L5D,IAAY8D,EAAAA,EAAAA,KAAA,MAAIF,UAAU,8DAA6DH,SAAC,OAC1FF,EAAAA,EAAAA,MAAA,MAAIK,UAAU,8DAA6DH,SAAA,CAAEzD,GAAY,WAAUuD,EAAAA,EAAAA,MAAA,QAAMK,UAAU,2BAA0BH,SAAA,CAAC,KAACK,EAAAA,EAAAA,KAAA,OAAKwC,IAAKtB,EAAAA,EAAWuB,IAAI,WAAW3C,UAAU,4BAC3L5D,IAAY8D,EAAAA,EAAAA,KAAA,MAAIF,UAAU,8DAA6DH,SAAC,OAC1FF,EAAAA,EAAAA,MAAA,MAAIK,UAAU,8DAA6DH,SAAA,CACxEzD,GAAY,WACM,OAAlB6H,GAA0BH,EAAiBW,EAAE,qCAAuC,aAClE,OAAlBR,GAA2B1H,IAAYH,GAAc0H,IAAkB7D,EAAAA,EAAAA,IAAU,SAAgC,SAAV,OAAsB,SAEhIC,EAAAA,EAAAA,KAAA,MAAIF,UAAW,uDAAsDqF,EAA2B,WAAa,eAC7G1F,EAAAA,EAAAA,MAAA,MAAIK,UAAU,8DAA6DH,SAAA,CACxEzD,GAAY,aACM,OAAlB6H,GAA0BH,EAAiBW,EAAE,wCAA0C,YACrE,OAAlBR,GAA2B1H,IAAYH,GAAc0H,IAAkB7D,EAAAA,EAAAA,IAAU,SAAgC,SAAV,OAAsB,SAEhIN,EAAAA,EAAAA,MAAA,MAAIK,UAAU,8DAA6DH,SAAA,CACxEzD,GAAY,SACM,OAAlB6H,GAA0BH,EAAiBW,EAAE,sCAAwC,YACnE,OAAlBR,GAA2B1H,IAAYH,GAAc0H,IAAkB7D,EAAAA,EAAAA,IAAU,SAAgC,SAAV,OAAsB,SAEhIC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,iEACdL,EAAAA,EAAAA,MAAA,MAAIK,UAAU,8DAA6DH,SAAA,CACxEzD,GAAY,YACM,OAAlB6H,GAA0BH,EAAiBW,EAAE,yCAA2C,WACtE,OAAlBR,GAA2B1H,IAAYH,GAAc0H,IAAkB7D,EAAAA,EAAAA,IAAU,SAAgC,SAAV,OAAsB,SAEhIC,EAAAA,EAAAA,KAAA,MAAIF,UAAW,uDAAsDmF,IAAyBT,EAAe,WAAa,eAC1H/E,EAAAA,EAAAA,MAAA,MAAIK,UAAW,uDAAsD5D,IAAaG,EAAW,WAAa,YAAasD,SAAA,CACpHzD,GAAY,GAAGqI,EAAE,6CACC,OAAlBR,GAA0BH,EAAiBW,EAAE,+CAAiD,mBAC9FlI,IAAauH,GAAkB7D,EAAAA,EAAAA,IAAU,SAAW,SAAW,OAA6B,OAAlBgE,EAAyB,SAAW,MAEhHH,IAAkBnE,EAAAA,EAAAA,MAAA,MAAIK,UAAW,uDAAuD5D,IAAaG,GAAa2I,EAA4B,WAAa,YAAarF,SAAA,CACtKzD,GAAY,GAAGqI,EAAE,2CACC,OAAlBR,GAA0BH,EACR,OAAlBG,GAA4C,OAAlBA,GAA4C,OAAlBA,GAA4C,OAAlBA,GAA4C,OAAlBA,GAA6C,OAAlBA,EAClIQ,EAAE,2CAEiB,OAAlBR,EACCQ,EAAE,2CAA6C,IAAGlF,aAAI,EAAJA,EAAMqD,kBAAgBrD,aAAI,EAAJA,EAAMtB,SAAUwG,EAAE,2CAErE,OAAlBR,EACU,OAARtD,EACC8D,EAAE,2CAA6C,IAAGlF,aAAI,EAAJA,EAAMtB,QAAMsB,aAAI,EAAJA,EAAMqD,mBAAoB6B,EAAE,2CAE1FA,EAAE,2CAA6C,IAAGlF,aAAI,EAAJA,EAAMqD,kBAAgBrD,aAAI,EAAJA,EAAMtB,SAAUwG,EAAE,2CAG5F,GAAGlF,aAAI,EAAJA,EAAMuC,gBAAerD,EAAAA,EAAAA,IAAac,EAAKpC,SAAUoC,EAAKtB,eAE7D,GAAGsB,aAAI,EAAJA,EAAMuC,gBAAerD,EAAAA,EAAAA,IAAac,EAAKpC,SAAUoC,EAAKtB,qBAI/C,WAAnBsB,EAAKkD,YACJ9C,EAAAA,EAAAA,MAAAI,EAAAA,SAAA,CAAAF,SAAA,EACEK,EAAAA,EAAAA,KAAA,MAAIF,UAAW,iEACfL,EAAAA,EAAAA,MAAA,MAAIK,UAAU,8DAA6DH,SAAA,CAAEzD,GAAY,YAAWuD,EAAAA,EAAAA,MAAA,QAAMK,UAAU,2BAA0BH,SAAA,CAAC,KAACK,EAAAA,EAAAA,KAAA,OAAKwC,IAAKtB,EAAAA,EAAWuB,IAAI,WAAW3C,UAAU,4BAC9LL,EAAAA,EAAAA,MAAA,MAAIK,UAAU,8DAA6DH,SAAA,CAAEzD,GAAY,cAAauD,EAAAA,EAAAA,MAAA,QAAMK,UAAU,2BAA0BH,SAAA,CAAC,KAACK,EAAAA,EAAAA,KAAA,OAAKwC,IAAKtB,EAAAA,EAAWuB,IAAI,WAAW3C,UAAU,4BAChML,EAAAA,EAAAA,MAAA,MAAIK,UAAU,8DAA6DH,SAAA,CAAEzD,GAAY,UAASuD,EAAAA,EAAAA,MAAA,QAAMK,UAAU,2BAA0BH,SAAA,CAAC,KAACK,EAAAA,EAAAA,KAAA,OAAKwC,IAAKtB,EAAAA,EAAWuB,IAAI,WAAW3C,UAAU,4BAC5LL,EAAAA,EAAAA,MAAA,MAAIK,UAAU,8DAA6DH,SAAA,CAAEzD,GAAY,WAAUuD,EAAAA,EAAAA,MAAA,QAAMK,UAAU,2BAA0BH,SAAA,CAAC,KAACK,EAAAA,EAAAA,KAAA,OAAKwC,IAAKtB,EAAAA,EAAWuB,IAAI,WAAW3C,UAAU,4BAC7LL,EAAAA,EAAAA,MAAA,MAAIK,UAAU,8DAA6DH,SAAA,CAAEzD,GAAY,eAAcuD,EAAAA,EAAAA,MAAA,QAAMK,UAAU,2BAA0BH,SAAA,CAAC,KAACK,EAAAA,EAAAA,KAAA,OAAKwC,IAAKtB,EAAAA,EAAWuB,IAAI,WAAW3C,UAAU,4BACjML,EAAAA,EAAAA,MAAA,MAAIK,UAAU,8DAA6DH,SAAA,CAAEzD,GAAY,YAAW8D,EAAAA,EAAAA,KAAA,QAAMF,UAAU,2BAA0BH,SAAE4E,EAAE,2CAClJvE,EAAAA,EAAAA,KAAA,MAAIF,UAAW,uDAAsDqF,EAA2B,WAAa,eAC7G1F,EAAAA,EAAAA,MAAA,MAAIK,UAAU,8DAA6DH,SAAA,CAAEzD,GAAY,cAAa8D,EAAAA,EAAAA,KAAA,QAAMF,UAAU,2BAA0BH,SAAE4E,EAAE,2CACpJ9E,EAAAA,EAAAA,MAAA,MAAIK,UAAU,8DAA6DH,SAAA,CAAEzD,GAAY,SAAS,aAAWG,IAAauH,EAAiB,OAAS,aACpJ5D,EAAAA,EAAAA,KAAA,MAAIF,UAAU,iEACdL,EAAAA,EAAAA,MAAA,MAAIK,UAAU,8DAA6DH,SAAA,CAAEzD,GAAY,YAAY,YAAUG,IAAauH,EAAiB,OAAS,aACtJ5D,EAAAA,EAAAA,KAAA,MAAIF,UAAW,uDAAuDmF,IAAyBT,GAAiBU,EAA4B,WAAa,eACzJzF,EAAAA,EAAAA,MAAA,MAAIK,UAAW,uDAAsD5D,IAAaG,EAAW,WAAa,YAAasD,SAAA,CAAEzD,GAAY,GAAGqI,EAAE,8CAA6CvE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,2BAA0BH,SAAC,kBACjOiE,IAAkBnE,EAAAA,EAAAA,MAAA,MAAIK,UAAW,uDAAuD5D,IAAaG,GAAa2I,EAA4B,WAAa,YAAarF,SAAA,CAAEzD,GAAY,GAAGqI,EAAE,4CAA2CvE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,2BAA0BH,SAAE,GAAGN,aAAI,EAAJA,EAAMuC,gBAAerD,EAAAA,EAAAA,IAAac,EAAKpC,SAAUoC,EAAKtB,iCASnU,QAAnBsB,EAAKkD,WAAwBqB,IAAmB7D,EAAAA,EAAAA,IAAU,SACnD,MAD8DN,EAAAA,EAAAA,MAAA,OAAKK,UAAW,kDAAmD5D,GAAc6H,GAAmC,OAAlBA,EAA0D,8BAAhC,gJAAgLpE,SAAA,EAACK,EAAAA,EAAAA,KAAC6F,EAAAA,IAAc,CAAC/F,UAAU,2BAA2B,IAAEyE,EAAE,wCAAuCvE,EAAAA,EAAAA,KAAC6F,EAAAA,IAAc,CAAC/F,UAAU,gCAtQte6B,GAyQR,IAEW,OAAfb,GAA+B,OAARL,GAAiC,WAAjBD,GAAkD,QAArB3C,EAAK,GAAGZ,UAC5E+C,EAAAA,EAAAA,KAAAH,EAAAA,SAAA,CAAAF,SACCkB,MAED,SAGJpB,EAAAA,EAAAA,MAAA,OAAKK,UAAU,2CAA0CH,SAAA,CACtD9B,aAAI,EAAJA,EAAM6D,IAAI,CAACrC,EAAMsC,KAChBlC,EAAAA,EAAAA,MAAA,OAAiBK,UAAW,oFAAoFH,SAAA,EAC9GK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,8BAA6BH,UAC1CF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,mCAAkCH,SAAA,EAC/CK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,0BAAyBH,SAAEN,EAAKuC,SAC5C5B,EAAAA,EAAAA,KAACC,EAAAA,GAAc,CAACZ,KAAMA,KACxBI,EAAAA,EAAAA,MAAA,OAAKK,UAAW,4BAA2C,SAAfmB,EAAwB,YAAc,qBAAqBR,IAAMd,SAAA,EAC3GK,EAAAA,EAAAA,KAAC6B,EAAAA,EAAOC,OAAM,CACZhC,UAAU,8HACViC,MAAO,CAACC,iBAAiBjH,EAAAA,EAAAA,IAAQ6F,IACjCqB,WAAY,CAAEC,MAAO,KAAMF,iBAAiB5G,EAAAA,EAAAA,IAAYwF,IACxDuB,SAAU,CAAED,MAAO,IACnBE,QAASA,IAAMpB,EAAW3B,EAAKgD,SAAS1C,SAEd,eAA3BN,EAAKuC,MAAM1E,cAA+B,gBAAkB,sBAE7D8C,EAAAA,EAAAA,KAAA,MAAIF,UAAU,wBAAuBH,SACjCN,EAAKyG,cAAe9F,EAAAA,EAAAA,KAAA,MAAIF,UAAU,QAAc,eAKrC,QAAnBT,EAAKkD,WAAsB9C,EAAAA,EAAAA,MAAA,OAAKK,UAAU,+KAA8KH,SAAA,EAACK,EAAAA,EAAAA,KAAC6F,EAAAA,IAAc,CAAC/F,UAAU,2BAA2B,IAAEyE,EAAE,uCAAuC,KAACvE,EAAAA,EAAAA,KAAC6F,EAAAA,IAAc,CAAC/F,UAAU,8BAC3U,OAtBH6B,IAyBK,OAAfb,GAA+B,OAARL,GAAqC,QAArB5C,EAAK,GAAGZ,UAC/C+C,EAAAA,EAAAA,KAAAH,EAAAA,SAAA,CAAAF,SACCkB,MAED,aAMM,OAAdE,GACAf,EAAAA,EAAAA,KAAAH,EAAAA,SAAA,CAAAF,UACAK,EAAAA,EAAAA,KAAC2C,EAAAA,SAAQ,CAACC,SAAU,KAAKjD,UACvBK,EAAAA,EAAAA,KAACmB,EAAS,QAGZ,WAKZ,EC7rBM4E,GAAWC,EAAAA,EAAAA,MAAK,IAAM,iCACtBC,GAAWD,EAAAA,EAAAA,MAAK,IAAM,6EACtBE,GAAWF,EAAAA,EAAAA,MAAK,IAAM,uFACtBG,GAAWH,EAAAA,EAAAA,MAAK,IAAM,6EACtBI,GAAWJ,EAAAA,EAAAA,MAAK,IAAM,iCAEtBK,GAAoBL,EAAAA,EAAAA,MAAK,IAC7B,iCAGF,IAAI3G,EAAO,KACP4D,GAAa,EACbqD,GAAY,EAChBC,eAAeC,IAAU,IAADpH,EAAAqH,EAClBhG,EAA2D,QAAxDrB,EAAmB,QAAnBqH,GAAG1G,EAAAA,EAAAA,IAAU,cAAM,IAAA0G,EAAAA,EAAIzD,YAAiC,IAAA5D,EAAAA,EAAI,KACnE,GAAIC,EAAM,OAAOA,EACjB,MAKMqH,SALiBC,EAAAA,EAAMC,KAC3B,wCACA,CAAEnG,OACF,CAAEoG,QAAS,CAAE,eAAgB,wCAEPhJ,KACxB,OAAI6I,EAAOI,SACTzH,EAAOqH,EAAO7I,KAAKkJ,OAAQlL,GACH,KAAlBP,SAASmF,IACcuC,OAAlBnH,EAAMwG,SAIjBiE,EAAYU,EAAAA,GAAAA,KAAO3H,EAAM,SAAU4H,GACjC,MAA4C,WAArCA,EAAEzH,iBAAiBtC,aAC5B,GACA+F,EACE+D,EAAAA,GAAAA,KAAO3H,EAAM,SAAU4H,GACrB,MAA4C,YAArCA,EAAEzH,iBAAiBtC,aAC5B,IAAMoJ,EACDjH,GAEA,EAEX,CAgEA,QA9DA,WACE,MAAM,KAAExB,IAASqJ,EAAAA,EAAAA,UAAS,QAASV,GAMnC,IAJA/J,EAAAA,EAAAA,WAAU,OAEP,SAEU0K,IAATtJ,EAAoB,OACxB,MAAMuJ,GAAKrH,EAAAA,EAAAA,IAAU,UAafsH,GAAetH,EAAAA,EAAAA,IAAU,WAAYA,EAAAA,EAAAA,IAAU,UAAY,GAC3DuH,GAAUvH,EAAAA,EAAAA,IAAU,YAAaA,EAAAA,EAAAA,IAAU,WAAa,GACxDwH,EAAc,CAAEtE,aAAYqD,YAAWzI,OAAMmD,WAbhC,SAAU0E,EAAI8B,IAC/BC,EAAAA,EAAAA,IAAU,UAAW/B,EAAI,CAAEgC,KAAM,MACjC,MAAMC,EAAgB,YAAaxH,EAAAA,EAAAA,MAAsB,uBACrDvD,OAAOgL,MAAQhL,OAAOiL,KACxBjL,OAAOgL,IAAIvH,SAASC,KAAOqH,EAE3BG,WAAW,KACTlL,OAAOyD,SAASC,KAAOqH,GACtB,IAEP,GAIMlH,GAAMV,EAAAA,EAAAA,IAAU,OAEtB,OACEN,EAAAA,EAAAA,MAAAI,EAAAA,SAAA,CAAAF,SAAA,EACEF,EAAAA,EAAAA,MAACsI,EAAAA,EAAM,CAACC,KAAMZ,EAAGzH,SAAA,EACfK,EAAAA,EAAAA,KAAA,SAAAL,SAAO,gCACPK,EAAAA,EAAAA,KAAA,QACEiI,KAAK,cACLC,QAAQ,qJAIZlI,EAAAA,EAAAA,KAAC2C,EAAAA,SAAQ,CAACC,SAAU,KAAKjD,SAEL,OAAZ2H,GACKtH,EAAAA,EAAAA,KAACoG,EAAQ,IAAKmB,IACJ,QAAR9G,GACFT,EAAAA,EAAAA,KAAC+F,EAAQ,IAAKwB,IACJ,QAAR9G,GACFT,EAAAA,EAAAA,KAACqG,EAAiB,IAAKkB,IAEZ,KAAjBF,GAA+B,OAAR5G,GACP,eAAjB4G,GAEOrH,EAAAA,EAAAA,KAACiG,EAAQ,IAAKsB,IACK,eAAjBF,GACFrH,EAAAA,EAAAA,KAACkG,EAAQ,IAAKqB,IACK,eAAjBF,GACFrH,EAAAA,EAAAA,KAACmG,EAAQ,IAAKoB,KAEdvH,EAAAA,EAAAA,KAACmI,EAAM,IAAKZ,QAM/B,C", "sources": ["assets/images/check-icon.svg", "core/utils/helper.jsx", "core/utils/main.jsx", "pricing/components/vprice_trial.jsx", "pricing/components/vprice.jsx", "pricing/index.jsx"], "sourcesContent": ["var _path, _defs;\nfunction _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\nimport * as React from \"react\";\nfunction SvgCheckIcon(_ref, svgRef) {\n  let {\n    title,\n    titleId,\n    ...props\n  } = _ref;\n  return /*#__PURE__*/React.createElement(\"svg\", _extends({\n    width: 15,\n    height: 15,\n    viewBox: \"0 0 15 15\",\n    fill: \"none\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, _path || (_path = /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M13.4738 4.89075C13.491 4.76025 13.5 4.62975 13.5 4.5C13.5 2.71575 11.8928 1.284 10.1093 1.52625C9.5895 0.6015 8.5995 0 7.5 0C6.4005 0 5.4105 0.6015 4.89075 1.52625C3.1035 1.284 1.5 2.71575 1.5 4.5C1.5 4.62975 1.509 4.76025 1.52625 4.89075C0.6015 5.41125 0 6.40125 0 7.5C0 8.59875 0.6015 9.58875 1.52625 10.1093C1.50895 10.2388 1.50018 10.3693 1.5 10.5C1.5 12.2842 3.1035 13.7123 4.89075 13.4738C5.4105 14.3985 6.4005 15 7.5 15C8.5995 15 9.5895 14.3985 10.1093 13.4738C11.8928 13.7123 13.5 12.2842 13.5 10.5C13.5 10.3703 13.491 10.2398 13.4738 10.1093C14.3985 9.58875 15 8.59875 15 7.5C15 6.40125 14.3985 5.41125 13.4738 4.89075ZM6.71625 10.812L3.966 8.0265L5.034 6.9735L6.72675 8.688L9.972 5.4675L11.028 6.5325L6.71625 10.812Z\",\n    fill: \"url(#paint0_linear_6732_19103)\"\n  })), _defs || (_defs = /*#__PURE__*/React.createElement(\"defs\", null, /*#__PURE__*/React.createElement(\"linearGradient\", {\n    id: \"paint0_linear_6732_19103\",\n    x1: 0,\n    y1: 7.5,\n    x2: 15,\n    y2: 7.5,\n    gradientUnits: \"userSpaceOnUse\"\n  }, /*#__PURE__*/React.createElement(\"stop\", {\n    stopColor: \"#3D57BC\"\n  }), /*#__PURE__*/React.createElement(\"stop\", {\n    offset: 1,\n    stopColor: \"#2590EE\"\n  })))));\n}\nconst ForwardRef = /*#__PURE__*/React.forwardRef(SvgCheckIcon);\nexport default __webpack_public_path__ + \"static/media/check-icon.6596926d038ac7c23abf36ecd142c51f.svg\";\nexport { ForwardRef as ReactComponent };", "import { useState, useEffect } from 'react';\r\n//check if browser supports WEBP\r\nexport function isWebpSupported() {\r\n  const elem = document.createElement('canvas');\r\n  if (!!(elem.getContext && elem.getContext('2d'))) {\r\n    return elem.toDataURL('image/webp').indexOf('data:image/webp') === 0;\r\n  }\r\n  return false;\r\n}\r\n// Observe sections/divs for lazy loading\r\nexport function observeSections(callback) {\r\n  const observer = new IntersectionObserver(entries => {\r\n    entries.forEach(entry => {\r\n      if (entry.isIntersecting) {\r\n        callback(entry.target.id);\r\n        // observer.unobserve(entry.target); // Stop observing once the section is intersecting\r\n      }\r\n    });\r\n  });\r\n  document.querySelectorAll('.lazy-section').forEach(section => {\r\n    observer.observe(section);\r\n  });\r\n  return () => {\r\n    observer.disconnect();\r\n  };\r\n}\r\n\r\n// Observe videos for lazy loading\r\nexport function observeVideos(callback) {\r\n  const observer = new IntersectionObserver(entries => {\r\n    entries.forEach(entry => {\r\n      if (entry.isIntersecting) {\r\n        const video = entry.target.querySelector('video');\r\n        if (video) {\r\n          const source = video.querySelector('source');\r\n          if (source && source.getAttribute('data-src')) {\r\n            source.setAttribute('src', source.getAttribute('data-src'));\r\n            video.load();\r\n            callback(entry.target.id);\r\n          }\r\n        }\r\n        observer.unobserve(entry.target);\r\n      }\r\n    });\r\n  });\r\n\r\n  document.querySelectorAll('.lazy-video').forEach(video => {\r\n    observer.observe(video);\r\n  });\r\n\r\n  return () => {\r\n    observer.disconnect();\r\n  };\r\n}\r\n\r\n// Hash pp_ctaclr\r\nexport function hexHash(hex) {\r\n  const hexRegex = /^[0-9a-f]*$/i;\r\n  if (hex == null || hex.length < 6 || !hexRegex.test(hex)) {\r\n    return `#1559ED`;\r\n  }\r\n\r\n  hex = hex.slice(0, 6);\r\n\r\n  return `#${hex}`;\r\n}\r\n\r\n// Darken color when hovering\r\nexport function hoverDarken(hex) {\r\n  const hexRegex = /^[0-9a-f]*$/i;\r\n  if (hex == null || hex.length < 6 || !hexRegex.test(hex)) {\r\n    hex = '1559ED';\r\n  }\r\n\r\n  let r = parseInt(hex.slice(0, 2), 16);\r\n  let g = parseInt(hex.slice(2, 4), 16);\r\n  let b = parseInt(hex.slice(4, 6), 16);\r\n\r\n  r = Math.max(0, Math.min(255, r - (r * 0.15)));\r\n  g = Math.max(0, Math.min(255, g - (g * 0.15)));\r\n  b = Math.max(0, Math.min(255, b - (b * 0.15)));\r\n\r\n  const toHex = (value) => {\r\n    const hexValue = Math.round(value).toString(16);\r\n    return hexValue.padStart(2, '0');\r\n  };\r\n  \r\n  return `#${toHex(r)}${toHex(g)}${toHex(b)}`;\r\n}\r\n\r\nexport function useDeviceSize() {\r\n  const [isMobile, setIsMobile_] = useState(false);\r\n  const [isTablet, setIsTablet] = useState(false);\r\n  const [isDesktop, setIsDesktop] = useState(false);\r\n\r\n  useEffect(() => {\r\n    const handleResize = () => {\r\n      const width = window.innerWidth;\r\n\r\n      setIsMobile_(width > 430);\r\n      setIsTablet(width > 729 && width <= 828); \r\n      setIsDesktop(width > 901); \r\n    };\r\n\r\n    handleResize(); \r\n    window.addEventListener('resize', handleResize);\r\n\r\n    return () => {\r\n      window.removeEventListener('resize', handleResize); \r\n    };\r\n  }, []);\r\n\r\n  return { isMobile, isTablet, isDesktop };\r\n}\r\n\r\nexport function renderContent(isTablet, isMobile, isDesktop, Infinite) {\r\n  if (isTablet) {\r\n    return <img src={Infinite} alt=\"Infinite\" className=\"mx-auto w-[22px]\" />;\r\n  } else {\r\n    return <span className=\"text-[#3C57BB] font-bold\">Unlimited</span>;\r\n  }\r\n}", "import { GetCookie } from '../../core/utils/cookies';\r\nexport function getCurrency(currency) {\r\n    if(!currency) return \"\";\r\n    if(currency.toLowerCase() === 'usd') return \"$\";\r\n    if(currency.toLowerCase() === 'eur') return \"€\";\r\n    if(currency.toLowerCase() === 'gbp') return \"£\";\r\n    if(currency.toLowerCase() === 'brl') return \"R$\";\r\n    if(currency.toLowerCase() === 'sar') return \"R$\";\r\n    return \"\";\r\n}\r\n\r\nexport function getCountry(currency) {\r\n    if(!currency) return \"\";\r\n    if(currency.toLowerCase() === 'usd') return \"US\";\r\n    if(currency.toLowerCase() === 'eur') return \"US\";\r\n    if(currency.toLowerCase() === 'gbp') return \"GB\";\r\n    if(currency.toLowerCase() === 'brl') return \"US\";\r\n    if(currency.toLowerCase() === 'sar') return \"AE\";\r\n    if(currency.toLowerCase() === 'chf') return \"CH\";\r\n    if(currency.toLowerCase() === 'sek') return \"SE\";\r\n    return \"US\";\r\n}\r\n\r\nexport function formatDate(dateStr) {\r\n    if(!dateStr) return \"\";\r\n    const month = [\"January\",\"February\",\"March\",\"April\",\"May\",\"June\",\"July\",\"August\",\"September\",\"October\",\"November\",\"December\"];\r\n    const date = new Date(dateStr.replace(/ /g,\"T\"));\r\n\r\n    return month[date.getMonth()] + \" \" + date.getDate() + \" \" + date.getFullYear();\r\n}\r\n\r\nexport function getPrice(data) {\r\n    if(data.trial_price) return data.trial_price;\r\n    if(data.price) return data.price;\r\n    return \"0\";\r\n}\r\n\r\nexport function numberWithCommas(x) {\r\n    return x.toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, \",\");\r\n}\r\n\r\nexport function formatNumber(number) {\r\n  const floatNumber = parseFloat(number);\r\n  return (floatNumber % 1 === 0)\r\n      ? numberWithCommas(floatNumber.toFixed(0))\r\n      : numberWithCommas(floatNumber.toFixed(2)); \r\n}\r\n\r\nexport function getPricePlan(currency, price) {\r\n    if(!currency || !price) return \"\";\r\n    // Check if price is a number\r\n    if(isNaN(price)) return \"\";\r\n    // Check if price is positive number\r\n    if(parseFloat(price) >= 0) {\r\n        if(currency.toLowerCase() === 'sar') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"ر.س.\";\r\n        } else if(currency.toLowerCase() === 'aed') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"AED\";\r\n        } else if(currency.toLowerCase() === 'chf') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"CHF\";\r\n        } else if(currency.toLowerCase() === 'sek') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"SEK\";\r\n        } else if(currency.toLowerCase() === 'pln') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"zł\";\r\n        }else if(currency.toLowerCase() === 'ron') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"LEI\";\r\n        }else if(currency.toLowerCase() === 'czk') {\r\n            return \"Kč\" + formatNumber(price).toLocaleString(\"en-US\");\r\n        } else if(currency.toLowerCase() === 'huf') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"Ft\";\r\n        } else if(currency.toLowerCase() === 'dkk') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"kr.\";\r\n        } else if(currency.toLowerCase() === 'bgn') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"лв\";\r\n        } else if(currency.toLowerCase() === 'try') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"₺\";\r\n        }\r\n        return getCurrency(currency) + formatNumber(price).toLocaleString(\"en-US\");\r\n    }\r\n    // Negative number\r\n    return \"-\" + getCurrency(currency) + (formatNumber(price) * -1).toLocaleString(\"en-US\");\r\n}\r\n\r\nexport function diffMin(dt1, dt2)\r\n{\r\n    dt1 = new Date(dt1);\r\n    dt2 = new Date(dt2);\r\n    var diff =(dt2.getTime() - dt1.getTime()) / 1000;\r\n    diff /= 60;\r\n    return Math.abs(Math.round(diff));\r\n}\r\n\r\nexport function formatDateTime(dateStr) {\r\n    if(!dateStr) return \"\";\r\n    const month = [\"January\",\"February\",\"March\",\"April\",\"May\",\"June\",\"July\",\"August\",\"September\",\"October\",\"November\",\"December\"];\r\n    const date = new Date(dateStr.replace(/ /g,\"T\"));\r\n\r\n    return month[date.getMonth()] + \" \" + date.getDate() + \" \" + date.getFullYear() + \" \" + date.getHours() + \":\" + date.getMinutes();\r\n}\r\n\r\nexport function DailyPrice({plan}) {\r\n  let dailyPrice = '';\r\n  let interval = '';\r\n  if (plan.payment_interval === 'Yearly') {\r\n    dailyPrice = getPricePlan(plan.currency, parseFloat(plan.price / 365).toFixed(2));\r\n    interval = <div class=\"text-xs mb-4\">billed {getPricePlan(plan.currency, plan.price)}/year</div>;\r\n  }\r\n\r\n  if (plan.payment_interval === 'Monthly') {\r\n    dailyPrice = getPricePlan(plan.currency, parseFloat(plan.price / 30).toFixed(2));\r\n    interval = <div class=\"text-xs mb-4\">billed {getPricePlan(plan.currency, plan.price)}/Month</div>;\r\n  }\r\n\r\n  if (plan.trial_price) {\r\n    dailyPrice = getPricePlan(plan.currency, parseFloat(plan.trial_price / plan.trial_days).toFixed(2));\r\n    interval = <div class=\"text-xs mb-4\">billed {getPricePlan(plan.currency, plan.trial_price)}</div>;\r\n  }\r\n\r\n  return (\r\n    <>\r\n      <p className={`text-4xl font-bold text-gray-800 ${(GetCookie(\"p_toggle\") === '') ? 'mb-4' : ''}`}>\r\n        {dailyPrice} <span className=\"text-sm\"> per Day</span>\r\n      </p>\r\n      {interval}\r\n    </>\r\n  );\r\n}\r\n\r\nexport function PriceFormatted({plan}) {\r\n  if (GetCookie(\"daily\") === 'on') {\r\n    return DailyPrice({plan});\r\n  } \r\n  if (plan.trial_price) {\r\n    return (<p className=\"text-4xl font-bold text-gray-800 mb-4\">{getPricePlan(plan.currency, plan.trial_price)}</p>)\r\n  } \r\n  return (\r\n    <p className=\"text-4xl font-bold text-gray-800 mb-4\">\r\n      {getPricePlan(plan.currency, plan.price)}\r\n      <span className=\"text-sm\"> \r\n        /{ plan.payment_interval === \"Monthly\" ? \"month\" : \"year\" }\r\n      </span>\r\n    </p>\r\n  )\r\n}\r\n\r\nexport function displayTextFormatted(plan) {\r\n  let payment_interval = plan.payment_interval === 'Yearly' ?  365 : 30;\r\n  let trialPricePer = `\r\n    then only ${getPricePlan(plan.currency, plan.price)} per month\r\n  `;\r\n\r\n  if (GetCookie(\"daily\") === 'on') {\r\n    trialPricePer = `\r\n      then only ${getPricePlan(plan.currency, parseFloat(plan.price / payment_interval).toFixed(2))} per day<br>\r\n      (billed ${getPricePlan(plan.currency, plan.price)} ${plan.payment_interval})\r\n    `;\r\n  }\r\n\r\n  return plan.display_txt2\r\n        .replace(\"{trialDays}\", plan.trial_days)\r\n        .replace(\"{trialPricePer}\", trialPricePer);\r\n  \r\n}\r\n\r\nexport function getPrefixLocation() {\r\n  const currentLocation = window.location.href;\r\n\r\n  if (currentLocation.indexOf(\"staging\") > -1) {\r\n      return \"staging.\";\r\n  } else if (currentLocation.indexOf(\"dev\") > -1) {\r\n      return \"dev.\";\r\n  }\r\n\r\n  return \"\"\r\n}", "import { Suspense } from \"react\";\r\nimport { GetCookie } from \"../../core/utils/cookies\";\r\nimport { motion } from \"framer-motion\";\r\nimport { DailyPrice } from \"../../core/utils/main\";\r\nimport { hexHash, hoverDarken } from \"../../core/utils/helper\";\r\nimport { useEffect, useState } from \"react\";\r\n\r\nfunction VPriceTrial({\r\n    data,\r\n    checkPlanInterval,\r\n    getPricePlan,\r\n    planInterval,\r\n    ppg,\r\n    billedAnnualDisplay,\r\n    ptoggle,\r\n    pp_ctaclr,\r\n    enterpriseTab,\r\n    enterprise,\r\n    tpreviews,\r\n    setPricing,\r\n    isMobile,\r\n    desc_align,\r\n    CheckIcon,\r\n    TpReviews\r\n}) {\r\n\r\n  const [appName, setAppName] = useState(\"Chatbot Pro\");\r\n\r\n  useEffect(() => {\r\n    const searchParams = new URLSearchParams(window.location.search);\r\n    const appNameParam = searchParams.get(\"appName\");\r\n    if (appNameParam) {\r\n      setAppName(appNameParam);\r\n    } else if(GetCookie('appName')) {\r\n        setAppName(GetCookie('appName'));\r\n    }\r\n  }, []);\r\n    \r\n    return <>\r\n        <div className=\"v-pricing pricing bg-white md:min-h-[90vh] \">\r\n            <div className={`pricing_columns mx-auto`}>\r\n                <div className=\"w-full\">   \r\n                    <div className='pricing_header'>\r\n                        <div className={`flex`}>\r\n                            <div className='flex w-1/3'></div>\r\n                            <div className='flex flex-col w-1/3'>\r\n                                <h1 className=\"text-4xl text-[#336CEB] lg:text-4xl font-bold text-center mb-4 min-h-[40px]\">\r\n                                    Pricing Plan\r\n                                </h1>\r\n                                <div className='text-center text-[18px]'>Unlock the full potential of {appName}</div>   \r\n                            </div>\r\n                            <div className='flex w-1/3'></div>\r\n                        </div>\r\n                    </div> \r\n                    <div className={`flex flex-col max-w-768:flex-row justify-center items-start`}>\r\n                        <div className=\"hidden max-w-768:block pricing_details lg:w-[20%] space-y-8 pl-6\">\r\n                            <div className='pt-[45px] pb-[50px] md:pb-[0px] lg:pb-[12px] xl:pb-[50px]'>\r\n                                <h3 className=\"font-bold mb-4 sm:text-sm sm:mt-[58px] max-w-768:mt-[68px] md:mt-[70px] lg:mt-[73px] xl:mt-[112px]\">Language Models</h3>\r\n                                <ul className=\"space-y-4 max-w-768:text-sm md:text-base lg:mb-[13px]\">\r\n                                <li className='h-[24px]'>GPT-4o</li>\r\n                                <li className='h-[24px]'>\r\n                                    DeepSeek\r\n                                </li>\r\n                                <li className='h-[24px]'>\r\n                                    Grok\r\n                                    <div className=\"inline px-2 ml-[2px] text-white bg-[#4285F4] rounded-xl text-[12px]\">\r\n                                    New\r\n                                    </div>\r\n                                </li>\r\n                                <li className='h-[24px]'>GPT-4</li>\r\n                                <li className='h-[24px]'>OpenAI o1</li>\r\n                                <li className='h-[24px]'>Claude</li>\r\n                                </ul>\r\n                                <h3 className=\"font-bold mb-4 mt-2 h-[24px] max-w-768:text-sm md:text-base \">Image Generation</h3>\r\n                                <ul className=\"space-y-4 mb-[17px] max-w-768:text-sm md:text-base \">\r\n                                <li className='h-[24px]'>DALL-E 3</li>\r\n                                <li className='h-[24px]'>Flux</li>\r\n                                </ul>\r\n                                <h3 className=\"font-bold mb-6 max-w-768:text-sm md:text-base \">Features</h3>\r\n                                <ul className=\"space-y-4 max-w-768:text-sm md:text-base \">\r\n                                <li className='h-[24px]'>Context Memory</li>\r\n                                <li className='h-[24px]'>Dialogue Limit</li>\r\n                                <li className='h-[24px]'>Trial Period</li>\r\n                                </ul>\r\n                            </div>\r\n                        </div>\r\n\r\n                        <div className={`pricing_columns w-full`}>\r\n                            <div className={`pricing-toggle flex flex-col max-w-768:flex-row justify-center`}>\r\n                                {data?.map((plan, index) => (\r\n                                checkPlanInterval(plan) ? (\r\n                                    <div key={index} className={` w-full lg:w-[340px] xl:w-[410px] text-center sm:px-1 mb-8 relative`}>\r\n                                    <div className={`rounded ${(index === 1 || index === 4) ? 'bg-[#F5F5F5]' : 'bg-gradient-to-r from-gray-50 to-[#FBFBFB]' } h-full`}>\r\n                                    <div className={`px-2 ${planInterval === 'yearly' ? 'sm:px-2' :'sm:px-4'} py-10 price-content`}>\r\n                                        <h3 className={`text-2xl font-bold mb-4 sm:text-[1.07rem] lg:text-2xl`}>{plan.label}</h3>\r\n                                        {GetCookie(\"daily\") === 'on' ? (<DailyPrice plan={plan}/>) :\r\n                                            (billedAnnualDisplay && plan.payment_interval === 'Yearly' && (ppg === '48' || ptoggle === '02') ?\r\n                                            (\r\n                                                <p className={`text-4xl font-bold text-[#4285F4] ${ppg === '48' && ptoggle === '01' ? 'mb-4' : ''}`}>\r\n                                                {getPricePlan(plan.currency, parseFloat(plan.price / 12).toFixed(2))}\r\n                                                <span className=\"text-lg text-gray-400\"> /month</span>\r\n                                                </p>\r\n                                            ) : plan.trial_price ?\r\n                                                (\r\n                                                <p className=\"text-4xl font-bold text-[#4285F4]\">\r\n                                                    {getPricePlan(plan.currency, plan.trial_price)}\r\n                                                </p>\r\n                                                ) :\r\n                                                (\r\n                                                <p className={`text-2xl lg:text-4xl font-bold text-[#4285F4] mb-4 ${(ptoggle === '02' || ptoggle === '03') ? '' : ''}`}>\r\n                                                    {(ptoggle === '02' || ptoggle === '03') && plan.payment_interval === \"Yearly\"\r\n                                                    ? getPricePlan(plan.currency, parseFloat(plan.price / 12).toFixed(2))\r\n                                                    : getPricePlan(plan.currency, plan.price)}\r\n                                                    <span className=\"text-lg text-gray-400\">\r\n                                                    /{(ptoggle === '02' || ptoggle === '03') && plan.payment_interval === \"Yearly\"\r\n                                                        ? \" month\"\r\n                                                        : plan.payment_interval === \"Monthly\"\r\n                                                        ? \" month\"\r\n                                                        : \" year\"}\r\n                                                    </span>\r\n                                                </p>\r\n                                                )\r\n                                            )\r\n                                        }\r\n                                        <motion.button\r\n                                            className={`block text-white font-bold py-3 mb-4 rounded-2xl text-[18px] sm:text-[16px] lg:text-[18px] px-3 lg:px-6 max-w-768:rounded-full bg-gradient-to-r from-[#3D56BA] to-[#268FED] w-full mb-[50px] max-w-768:mb-[24px] sm-[26px] md:mb-[30px] lg:mb-[40px]`}\r\n                                            style={{backgroundColor: hexHash(pp_ctaclr)}}\r\n                                            whileHover={{ scale: 1.05, backgroundColor: hoverDarken(pp_ctaclr) }}\r\n                                            whileTap={{ scale: 0.9 }}\r\n                                            onClick={() => setPricing(plan.plan_id)}\r\n                                        >\r\n                                            {'Choose This Plan'}\r\n                                        </motion.button>\r\n                                        {(ptoggle === \"02\" || ptoggle === \"03\") && plan.payment_interval === 'Yearly' && GetCookie(\"daily\") !== 'on' && (\r\n                                            <span className={`absolute text-lg text-gray-400 text-gray-600 mb-4 absolute left-[0] right-[0] mt-[-20px] lg:mt-[-30px]`}>\r\n                                            or {getPricePlan(plan.currency, plan.price)} annual\r\n                                            </span>\r\n                                        )}\r\n                                        {(\r\n                                            (billedAnnualDisplay && plan.payment_interval === 'Yearly' && !['01', '02', '03'].includes(ptoggle) && GetCookie(\"daily\") !== 'on') ||\r\n                                            (!['01', '02', '03'].includes(ptoggle) && ppg === 48 && GetCookie(\"daily\") !== 'on')\r\n                                        ) && (\r\n                                            <div className='text-xs mb-4'>(billed yearly)</div>\r\n                                            )}\r\n                                        <div className={`mb-6 ${(ptoggle === '02' || ptoggle === '03') ? 'mt-4' : ''} pricing-description ${desc_align === 'left' ? 'text-left' : 'text-center'} ppg-${ppg}`}>\r\n\r\n                                            <ul className=\"text-sm text-gray-600 space-y-4 \">\r\n                                            {plan.plan_type === 'Basic' && (\r\n                                                <>\r\n                                                <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\"></li>\r\n                                                <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\">{isMobile && 'GPT-4o: '}<span className='text-[#3C57BB] font-bold'> <img src={CheckIcon} alt=\"Infinite\" className=\"mx-auto w-[22px]\"/></span></li>\r\n                                                <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\">{isMobile && 'DeepSeek:'}<span className='text-[#3C57BB] font-bold'> <img src={CheckIcon} alt=\"Infinite\" className=\"mx-auto w-[22px]\"/></span></li>\r\n                                                {!isMobile && (\r\n                                                    <>\r\n                                                    <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\">-</li>\r\n                                                    <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\">-</li>\r\n                                                    <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\">-</li>\r\n                                                    </>\r\n                                                )}\r\n                                                <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\">{isMobile && 'Claude: '}25k tokens{'/month'}</li>\r\n                                                <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\"></li>\r\n                                                <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\">{isMobile && 'DALL-E 3: '}20 images{'/month'}</li>\r\n                                                <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\">{isMobile && 'Flux: '}15 images{'/month'}</li>\r\n                                                <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\"></li>\r\n                                                <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\">{isMobile && 'Context Memory: '}Standard</li>\r\n                                                <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\">{isMobile && 'Dialogue Limit: '}500,000 tokens{'/month'}</li>\r\n                                                <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\">{isMobile && 'Trial Period: '}{`${plan?.label}, then ${plan?.currency_symbol + plan?.price}/month`}</li>\r\n                                                </>\r\n                                            )}\r\n                                            {(plan.plan_type === 'Pro') && (\r\n                                                <>\r\n                                                <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\"></li>\r\n                                                <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\">{isMobile && 'GPT-4o: '}<span className='text-[#3C57BB] font-bold'> <img src={CheckIcon} alt=\"Infinite\" className=\"mx-auto w-[22px]\"/></span></li>\r\n                                                <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\">{isMobile && 'DeepSeek:'}<span className='text-[#3C57BB] font-bold'> <img src={CheckIcon} alt=\"Infinite\" className=\"mx-auto w-[22px]\"/></span></li>\r\n                                                <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\">{isMobile && 'Grok:'}<span className='text-[#3C57BB] font-bold'> <img src={CheckIcon} alt=\"Infinite\" className=\"mx-auto w-[22px]\"/></span></li>\r\n                                                {!isMobile && (\r\n                                                    <>\r\n                                                    <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\">-</li>\r\n                                                    <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\">-</li>\r\n                                                    </>\r\n                                                )}\r\n                                                <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\">{isMobile && 'Claude: '}50k tokens{'/month'}</li>\r\n                                                <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\"></li>\r\n                                                <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\">{isMobile && 'DALL-E 3: '}50 images{'/month'}</li>\r\n                                                <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\">{isMobile && 'Flux: '}30 images{'/month'}</li>\r\n                                                <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\"></li>\r\n                                                <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\">{isMobile && 'Context Memory: '}Full</li>\r\n                                                <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\">{isMobile && 'Dialogue Limit: '}1,000,000 tokens{'/month'}</li>\r\n                                                <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\">{isMobile && 'Trial Period: '}{`${plan?.label}, then ${plan?.currency_symbol + plan?.price}/month`}</li>\r\n                                                </>\r\n                                            )}\r\n                                            {plan.plan_type === 'ProMax' && (\r\n                                                <>\r\n                                                <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\"></li>\r\n                                                <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\">{isMobile && 'GPT-4o: '}<span className='text-[#3C57BB] font-bold'> <img src={CheckIcon} alt=\"Infinite\" className=\"mx-auto w-[22px]\"/></span></li>\r\n                                                <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\">{isMobile && 'DeepSeek:'}<span className='text-[#3C57BB] font-bold'> <img src={CheckIcon} alt=\"Infinite\" className=\"mx-auto w-[22px]\"/></span></li>\r\n                                                <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\">{isMobile && 'Grok: '}<span className='text-[#3C57BB] font-bold'> <img src={CheckIcon} alt=\"Infinite\" className=\"mx-auto w-[22px]\"/></span></li>\r\n                                                <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\">{isMobile && 'GPT-4: '}<span className='text-[#3C57BB] font-bold'> <img src={CheckIcon} alt=\"Infinite\" className=\"mx-auto w-[22px]\"/></span></li>\r\n                                                <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\">{isMobile && 'OpenAI o1: '}<span className='text-[#3C57BB] font-bold'> <img src={CheckIcon} alt=\"Infinite\" className=\"mx-auto w-[22px]\"/></span></li>\r\n                                                <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\">{isMobile && 'Claude: '}<span className='text-[#3C57BB] font-bold'>Unlimited</span></li>\r\n                                                <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\"></li>\r\n                                                <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\">{isMobile && 'DALL-E 3: '}<span className='text-[#3C57BB] font-bold'>Unlimited</span></li>\r\n                                                <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\">{isMobile && 'Flux: '}160 images{'/month'}</li>\r\n                                                <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\"></li>\r\n                                                <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\">{isMobile && 'Context Memory: '}Advanced</li>\r\n                                                <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\">{isMobile && 'Dialogue Limit: '}<span className='text-[#3C57BB] font-bold'>Unlimited</span></li>\r\n                                                <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\">{isMobile && 'Trial Period: '}<span className='text-[#3C57BB] font-bold'>{`${plan?.label}, then ${plan?.currency_symbol + plan?.price}/month`}</span></li>\r\n                                                </>\r\n                                            )}\r\n                                            </ul>\r\n                                        </div>\r\n\r\n                                        </div>\r\n                                    </div>\r\n                                    </div>\r\n                                ) : \"\"\r\n                                ))}\r\n\r\n                                {(enterprise === 'on' && ppg !== '46' && planInterval === 'yearly' && data[0].currency === 'USD') ?\r\n                                <>\r\n                                {enterpriseTab()}\r\n                                </>\r\n                                : null }\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n\r\n                    { tpreviews === 'on' ?\r\n                        <>\r\n                            <Suspense fallback={null}>\r\n                                <TpReviews/>\r\n                            </Suspense>\r\n                        </>\r\n                    : null }\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </>\r\n}\r\n    \r\n\r\nexport default VPriceTrial;", "import React, { Suspense, useState, useEffect } from 'react';\r\nimport { motion } from \"framer-motion\";\r\nimport { getPricePlan, DailyPrice, PriceFormatted } from '../../core/utils/main';\r\nimport { GetCookie } from '../../core/utils/cookies';\r\nimport { hexHash, hoverDarken, useDeviceSize } from '../../core/utils/helper';\r\nimport '../css/style.css';\r\nimport { useTranslation } from 'react-i18next';\r\nimport CheckIcon from '../../assets/images/check-icon.svg';\r\nimport { HiMiniSparkles } from \"react-icons/hi2\";\r\nimport VPriceTrial from './vprice_trial';\r\n\r\nconst TpReviews = React.lazy(() => import('../../features/tpreviews'));\r\n\r\nfunction VPrice(props) {\r\n  const { t } = useTranslation();\r\n  const data = props.data ? props.data : null;\r\n  const setPricing = props.setPricing ? props.setPricing : ()=>{};\r\n  var ppg = GetCookie(\"ppg\") ? GetCookie(\"ppg\") : process.env.REACT_APP_DEFAULT_PPG ? process.env.REACT_APP_DEFAULT_PPG : \"14\";\r\n  var pp_ctaclr = GetCookie(\"pp_ctaclr\") ? GetCookie(\"pp_ctaclr\") : \"1559ED\";\r\n\r\n  const ppgArrayWithToggle = ['40','48','52','97','101','109','110'];\r\n  const showToggle = (ppgArrayWithToggle.includes(ppg) && props.showToggle) ? props.showToggle : false;\r\n  const isShowPPg = !['40','48','52'].includes(ppg); //Can add condition weather to show or not the ppg but we are currently displaying all active\r\n  const tpreviews = GetCookie(\"tp_reviews\") ? GetCookie(\"tp_reviews\") : \"\";\r\n  const enterprise = GetCookie(\"enterprise\") ? GetCookie(\"enterprise\") : \"off\";\r\n  const ptoggle = GetCookie(\"p_toggle\") ? GetCookie(\"p_toggle\") : \"\";\r\n  const [ desc_align ] = useState(GetCookie(\"desc_align\"));\r\n  const [isMobile, setIsMobile] = useState(false);\r\n  const { \r\n    isTablet,  \r\n    is785to810, \r\n    is850to885, \r\n    is885to900, \r\n    is910to925,\r\n    is940to965,\r\n    is970to1005,\r\n    is1080to1090,\r\n    is1125to1130\r\n  } = useDeviceSize();\r\n  const allowTrialPlan = data && ((data[0].trial_days && data[0].trial_price) || [\"03\",\"05\",\"11\",\"60\",\"62\",\"72\"].includes(ppg));\r\n  const [isChatPDFContext, setIsChatPDFContext] = useState(false);\r\n  // const [isMobile768, setIVsMobile768] = useState(false);\r\n\r\n  const localesCookie = GetCookie(\"locales\") || \"en\";\r\n\r\n  useEffect(() => {\r\n    const referrer = document.referrer;\r\n    const parentDomain = referrer ? new URL(referrer).hostname : \"\";\r\n\r\n    const allowedDomains = [\r\n      \"staging.chatpdfv2.ai-pro.org\",\r\n      \"chatpdfv2.ai-pro.org\"\r\n    ];\r\n    if (\r\n        GetCookie(\"chatpdfv2modal\") === \"true\" ||\r\n          allowedDomains.includes(parentDomain)\r\n        ) {\r\n      setIsChatPDFContext(true);\r\n    }\r\n  }, []);\r\n\r\n  var billedAnnualDisplay = false;\r\n\r\n  useEffect(() => {\r\n    const checkScreenSize = () => {\r\n      setIsMobile(window.innerWidth <= 729);\r\n      // setIsMobile768(window.innerWidth <= 768);\r\n    };\r\n\r\n    checkScreenSize();\r\n    window.addEventListener('resize', checkScreenSize);\r\n\r\n    return () => window.removeEventListener('resize', checkScreenSize);\r\n  }, []);\r\n\r\n  if (ppg==='48'){\r\n    billedAnnualDisplay = true;\r\n  }\r\n\r\n  if (ptoggle ==='01' || ptoggle === '03'){\r\n    billedAnnualDisplay = true;\r\n  }\r\n\r\n  const [ planInterval, setPlanInterval ] = useState(billedAnnualDisplay ? \"yearly\" : \"monthly\");\r\n\r\n  const intervalChange = function() {\r\n    if(planInterval === \"monthly\") {\r\n      setPlanInterval(\"yearly\");\r\n    } else {\r\n      setPlanInterval(\"monthly\");\r\n    }\r\n  };\r\n\r\n  const basicIsHidden = (plan) => {\r\n    return plan.plan_type === 'Basic' && isChatPDFContext && !allowTrialPlan;\r\n  }\r\n\r\n  const checkPlanInterval = function(plan) {\r\n    if (basicIsHidden(plan)) return false;\r\n    if(!showToggle) return true;\r\n    if(plan.payment_interval.toLowerCase() === planInterval) return true;\r\n    return false;\r\n  }\r\n\r\n  const isSinglePlanWillRemain = () => {\r\n    if (!data || data.length > 2) return false;\r\n\r\n    const rem = data.length % 4; //Means 2 monthly and 2 yearly\r\n    return data.length === 1 || ((data.length === 2 || rem === 0) && basicIsHidden(data[0]));\r\n  }\r\n\r\n  const shouldDisplayAllPlans = () => {\r\n    if (!data) return false;\r\n\r\n    return data.length >= 2 && !basicIsHidden(data[0]);      \r\n  }\r\n\r\n  const enterpriseTab = function() {\r\n    return (\r\n\r\n        <div className=\" w-full lg:w-[330px] xl:w-[380px] text-center px-4 mb-8 relative\">\r\n          <div className=\"bg-white rounded-lg shadow-lg overflow-hidden\">\r\n            <div className=\"px-6 py-20 md:py-10 price-content\">\r\n              <h3 className=\"text-2xl font-bold mb-4\">{t('echo.pricing.vprice_02.enterprise.title')}</h3>\r\n              <p className={`text-4xl font-bold text-[#4285F4] ${planInterval === 'yearly' && ppg === '48'  ? \"\" :\"mb-4\"}`}>{t('echo.pricing.vprice_02.enterprise.price')}</p>\r\n              { planInterval === 'yearly' && ppg === '48' && GetCookie(\"daily\") !== 'on' ? <div className='text-xs mb-4'>(billed yearly)</div> : '' }\r\n              <div className='py-4'>\r\n                <motion.button\r\n                  className=\"text-white font-bold py-3 px-3 rounded-lg\"\r\n                  style={{backgroundColor: hexHash(pp_ctaclr)}}\r\n                  whileHover={{ scale: 1.05, backgroundColor: hoverDarken(pp_ctaclr) }}\r\n                  whileTap={{ scale: 0.9 }}\r\n                  onClick={() => setPricing(62)}\r\n                >\r\n                  {t('echo.pricing.vprice_02.enterprise.cta')}\r\n                </motion.button>\r\n              </div>\r\n              <div className={`mb-6 pricing-description ${desc_align === 'left' ? 'text-left' : 'text-center'} ppg-${ppg}`}>\r\n                <ul className=\"text-sm text-gray-600\">\r\n                  <li className=\"mb-2 font-bold\"><div>{t('echo.pricing.vprice_02.enterprise.desc1')}</div></li>\r\n                  <li className=\"\"><div>{t('echo.pricing.vprice_02.enterprise.desc2')}</div></li>\r\n                </ul>\r\n              </div>\r\n\r\n            </div>\r\n          </div>\r\n        </div>\r\n    );\r\n  };\r\n\r\n  const trialPlanUseCustomComponent = false; //Set to true if want to use the separate component \r\n  if (allowTrialPlan && trialPlanUseCustomComponent) {\r\n    return (\r\n      <VPriceTrial\r\n        data={data}\r\n        checkPlanInterval={checkPlanInterval}\r\n        getPricePlan={getPricePlan}\r\n        planInterval={planInterval}\r\n        ppg={ppg}\r\n        billedAnnualDisplay={billedAnnualDisplay}\r\n        ptoggle={ptoggle}\r\n        pp_ctaclr={pp_ctaclr}\r\n        enterpriseTab={enterpriseTab}\r\n        enterprise={enterprise}\r\n        tpreviews={tpreviews}\r\n        setPricing={setPricing}\r\n        isMobile={isMobile}\r\n        desc_align={desc_align}\r\n        CheckIcon={CheckIcon}\r\n        TpReviews={TpReviews}\r\n      />\r\n    )\r\n  }\r\n\r\n  const isSinglePlan = isSinglePlanWillRemain();\r\n  const isDoublePlan = data && (data.length === 2 || (data.length % 4 === 0) || ((data.length % 3 === 0) && basicIsHidden(data[0]))); //4 Means 2 monthly and 2 yearly\r\n  const isShouldDisplayAllPlans = shouldDisplayAllPlans();\r\n  const isPlanPro = (ndx) => {\r\n    return data[ndx].plan_type === 'Pro' || data[ndx].plan_type.toLowerCase().startsWith('pro');\r\n  };\r\n  const isRemainedSinglePlanPro = isSinglePlan && isPlanPro(0);\r\n  const isExtendedTrialPlanDetail = localesCookie !== 'en' && \r\n  (\r\n    ['pl'].includes(localesCookie) || \r\n    (is785to810 && !['it'].includes(localesCookie)) ||\r\n    (!is940to965 && !is785to810 && ['pt'].includes(localesCookie)) ||\r\n    ((is850to885 || is885to900) && ['de'].includes(localesCookie)) ||\r\n    (is850to885 && ['fr'].includes(localesCookie))\r\n  );\r\n  //const noBasicPlan = data.filter(pl => pl.plan_type.toLowerCase() === 'basic');\r\n  const isChatPDFContextFull = isChatPDFContext;// && noBasicPlan.length <= 0;\r\n  const isUseChatPdfContextDesign = !isChatPDFContextFull && isDoublePlan;\r\n  const dallETitleHeightExtended = ['es','it'].includes(localesCookie) && !allowTrialPlan && !isSinglePlan && !isMobile && !is1080to1090 && !is970to1005 && !is1125to1130;\r\n\r\n  //Below left padding only for trial or single plan display (will only work for allowTrialPlan and isSinglePlan)\r\n  let plval = 20;\r\n  if(is785to810) {\r\n    switch (localesCookie) {\r\n      case 'de': plval = 18; break;\r\n      case 'es': \r\n      case 'it': \r\n        plval = 26; \r\n        break;\r\n      case 'fr': plval = 23; break;\r\n      case 'pl': \r\n      case 'pt': \r\n        plval = 25; \r\n        break;\r\n      case 'tr': plval = 22; break;\r\n      default:\r\n    }\r\n  } else if(is940to965) {\r\n    switch (localesCookie) {\r\n      case 'de': plval = 15; break;\r\n      case 'es':\r\n      case 'it': \r\n        plval = 23; break;\r\n      case 'tr': plval = 18; break;\r\n      case 'en': plval = 17; break;\r\n      default:\r\n    }  \r\n  } else if(is910to925) {\r\n    switch (localesCookie) {\r\n      case 'de': plval = 15; break;\r\n      case 'es':\r\n      case 'it': \r\n        plval = 23; break;\r\n      case 'tr': plval = 18; break;\r\n      case 'en': plval = 17; break;\r\n      default:\r\n    }\r\n  } else if(is850to885) {\r\n    switch (localesCookie) {\r\n      case 'de': plval = 16; break;\r\n      case 'es':\r\n      case 'it': \r\n        plval = 24; break;\r\n      case 'fr': \r\n      case 'pl':\r\n      case 'pt': \r\n        plval = 22; break;\r\n      default:\r\n    }  \r\n  } else if(is885to900) {\r\n    switch (localesCookie) {\r\n      case 'en': plval = 18; break;\r\n      case 'de': plval = 16; break;\r\n      case 'es':\r\n      case 'it': \r\n        plval = 24; break;\r\n      case 'pl':\r\n      case 'pt': \r\n        plval = 22; break;\r\n      case 'tr': plval = 19; break;\r\n      default:\r\n    }\r\n  }\r\n\r\n  let langModelsTextPos = 'mt-[59px]';\r\n  switch (localesCookie) {\r\n    case 'en':\r\n      langModelsTextPos = `${langModelsTextPos} min-970-max-1005:mt-[66px]`;\r\n      break;\r\n    case 'de':\r\n      langModelsTextPos = `${langModelsTextPos} min-1080-max-1090:mt-[75px] min-970-max-1005:mt-[81px] min-1125-max-1130:mt-[77px]`;\r\n      break;  \r\n    case 'es':\r\n      langModelsTextPos = `${langModelsTextPos} min-w-1300:mt-[35px] min-970-max-1005:mt-[81px]`;\r\n      break;\r\n    case 'fr':\r\n      langModelsTextPos = `${langModelsTextPos} min-970-max-1005:mt-[66px]`;\r\n      break;\r\n    case 'it':\r\n    case 'pl':\r\n    case 'pt':\r\n    case 'tr':\r\n      langModelsTextPos = `${langModelsTextPos} min-970-max-1005:mt-[64px]`;\r\n      break;\r\n    default:  \r\n  }\r\n\r\n  const getPricingText = () => {\r\n    if(GetCookie('appName')) {\r\n      const text = t('echo.pricing.vprice.pricingText');\r\n      return text.replace(\"ChatBot Pro\", GetCookie('appName'));\r\n    }\r\n    return isChatPDFContext ? t('echo.pricing.vprice.pricingTextPDF') : t('echo.pricing.vprice.pricingText');\r\n  }\r\n  \r\n  return (\r\n    <div className=\"v-pricing pricing bg-white md:min-h-[90vh] \">\r\n      <div className={`pricing_columns mx-auto ${allowTrialPlan || isSinglePlan ? \"mt-[10px]\" : \"\"} ${(allowTrialPlan || isSinglePlan) && !isMobile ? 'pl-[20px]' : ''}`}>\r\n        <div className=\"w-full\">\r\n\r\n          <div className='pricing_header'>\r\n            <div className={`flex justify-center ${allowTrialPlan || isSinglePlan || (!showToggle && isShouldDisplayAllPlans) ? \"mb-5\" : \"\"}`} \r\n              style={(allowTrialPlan || isSinglePlan) && !isMobile ? { paddingLeft: `${plval}%` } : {}}>\r\n              <div className='flex flex-col'>\r\n            <h1 className=\"text-4xl text-[#336CEB] lg:text-4xl font-bold text-center mb-4 min-h-[40px]\">\r\n              {t('echo.pricing.vprice.pricingPlanText')}\r\n            </h1>\r\n                <div className={`text-center text-[18px] ${(isChatPDFContextFull || isUseChatPdfContextDesign) && !isMobile ? 'max-w-500:w-[235px]' : ''}`}>{getPricingText()}</div>\r\n              </div>\r\n            </div>\r\n            {showToggle && (\r\n              <div className=\"p-4\">\r\n                <div className=\"text-1xl lg:text-1xl font-bold text-center mb-4\">\r\n                </div>\r\n                <div className=\"flex items-center justify-center w-full mb-8\">\r\n                  <label htmlFor=\"toggleB\" className=\"flex items-center cursor-pointer\">\r\n                    <div className={`${planInterval === 'monthly' ? \"text-gray-800 font-bold\" : \"text-gray-700\"} mr-3 uppercase`}>\r\n                    {t('echo.pricing.vprice.monthlyText')}\r\n                    </div>\r\n                    <div className=\"relative\">\r\n                      <input\r\n                        type=\"checkbox\"\r\n                        id=\"toggleB\"\r\n                        className=\"sr-only toggle\"\r\n                        onChange={intervalChange}\r\n                        defaultChecked={billedAnnualDisplay}\r\n                      />\r\n                      <div className=\"block bg-gradient-to-r from-[#268FED] to-[#3D56BA] w-12 h-6 rounded-full\"></div>\r\n                      <div\r\n                        className={`dot absolute top-1 bg-white w-4 h-4 rounded-full transition-all duration-300 ease-in-out ${planInterval === 'yearly' ? 'left-[0.3rem]' : 'left-1'\r\n                          }`}\r\n                      ></div>\r\n                    </div>\r\n                    <div className={`${planInterval === 'yearly' ? \"text-gray-800 font-bold\" : \"text-gray-700\"} ml-3 uppercase`}>\r\n                      {t('echo.pricing.vprice.yearlyText')}\r\n                    </div>\r\n                  </label>\r\n                </div>\r\n              </div>\r\n            )}\r\n          </div>\r\n          <div className={`flex flex-col max-w-768:flex-row justify-center items-start ${(isChatPDFContextFull && isDoublePlan && !is970to1005) || isUseChatPdfContextDesign ? 'lg:ml-[5%]' : ''}`}>\r\n            <div className={`hidden max-w-768:block pricing_details lg:w-[20%] space-y-8 ${isChatPDFContextFull || isUseChatPdfContextDesign ? 'min-w-1370:ml-[0.5rem]' : ''} pl-6`}>\r\n              <div className={`${allowTrialPlan || isSinglePlan ? 'pt-[37px]' : 'pt-[45px]'} pb-[50px] md:pb-[0px] lg:pb-[12px] xl:pb-[50px] min-w-1164:w-[175px]`}>\r\n                {!allowTrialPlan && !isSinglePlan ? (\r\n                  <>\r\n                    <h3 className=\"font-bold mb-4 \">{t('echo.pricing.vprice.comparePlansText')}</h3>\r\n                    <p className=\"text-sm sm:text-xs lg:text-sm text-gray-600\">{t('echo.pricing.vprice.aiDescriptionText1')}<br/>{t('echo.pricing.vprice.aiDescriptionText2')}</p>\r\n                  </>) : ( <div className={`md:mb-[180px] sm:mb-[150px] ${(allowTrialPlan || (isSinglePlan && !isRemainedSinglePlanPro)) ? \"\" : \"lg:mb-[195px]\"}`}></div> )\r\n                }  \r\n                {/* <h3 className=\"font-bold mb-4 sm:text-sm sm:mt-[58px] max-w-768:mt-[68px] md:mt-[70px] lg:mt-[73px] xl:mt-[112px]\">Language Models</h3> */}\r\n                <h3 className={`font-bold mb-4 sm:text-sm   ${allowTrialPlan || isSinglePlan ? 'sm:mt-[58px] md:mt-[70px] lg:mt-[73px] max-w-768:mt-[68px]' : langModelsTextPos } `}>{t('echo.pricing.vprice.languageModels')}</h3>\r\n                <ul className=\"space-y-4 max-w-768:text-sm md:text-base lg:mb-[13px]\">\r\n                  <li className='h-[24px]'>GPT-4o</li>\r\n                  <li className='h-[24px]'>\r\n                    DeepSeek\r\n                  </li>\r\n                  <li className='h-[24px]'>\r\n                    Grok\r\n                    <div className=\"inline px-2 ml-[2px] text-white bg-[#4285F4] rounded-xl text-[12px]\">\r\n                      {t('echo.pricing.vprice.newBadgeText')}\r\n                    </div>\r\n                  </li>\r\n                  <li className='h-[24px]'>\r\n                    GPT-5\r\n                    <div className=\"inline px-2 ml-[2px] text-white bg-[#4285F4] rounded-xl text-[12px]\">\r\n                      {t('echo.pricing.vprice.newBadgeText')}\r\n                    </div>\r\n                  </li>\r\n                  <li className='h-[24px]'>OpenAI o1</li>\r\n                  <li className='h-[24px]'>Claude</li>\r\n                </ul>\r\n                <h3 className={`font-bold mb-4 mt-2 max-w-768:text-sm md:text-base ${dallETitleHeightExtended ? 'h-[40px]' : 'h-[24px]'}`}>{t('echo.pricing.vprice.imageGeneration')}</h3>\r\n                <ul className=\"space-y-4 mb-[17px] max-w-768:text-sm md:text-base \">\r\n                  <li className='h-[24px]'>DALL-E 3</li>\r\n                  <li className='h-[24px]'>Flux</li>\r\n                </ul>\r\n                <h3 className=\"font-bold mb-4 mt-2 max-w-768:text-sm md:text-base h-[24px]\">{t('echo.pricing.vprice.videoGeneration')}</h3>\r\n                <ul className=\"space-y-4 mb-[17px] max-w-768:text-sm md:text-base \">\r\n                  <li className='h-[24px]'>KlingAI</li>\r\n                </ul>\r\n                <h3 className=\"font-bold mb-6 max-w-768:text-sm md:text-base \">{t('echo.pricing.vprice.features')}</h3>\r\n                <ul className=\"space-y-4 max-w-768:text-sm md:text-base \">\r\n                  <li className='h-[24px]'>{t('echo.pricing.vprice.dialogueLimitText')}</li>\r\n                  {allowTrialPlan && <li className='h-[24px]'>{t('echo.pricing.vprice.trialPeriodText')}</li>}\r\n                </ul>\r\n              </div>\r\n            </div>\r\n\r\n            <div className={`pricing_columns w-full ${allowTrialPlan || isSinglePlan ? \"md:w-[60%] sm:px-[5%]\" : \"lg:w-[78%]\"}`}>\r\n              { isShowPPg ? (\r\n                <div className={`pricing-toggle flex flex-col max-w-768:flex-row justify-center`}>\r\n\r\n\r\n                  {data?.map((plan, index) => (\r\n                    checkPlanInterval(plan) ? (\r\n                      <div key={index} className={` w-full lg:w-[340px] xl:w-[410px] text-center sm:px-1 mb-8 relative`}>\r\n                        <div className={`rounded ${(index === 1 || index === 4) ? 'bg-[#F5F5F5]' : 'bg-gradient-to-r from-gray-50 to-[#FBFBFB]' } h-full`}>\r\n                        <div className={`px-2 ${planInterval === 'yearly' ? 'sm:px-2' :'sm:px-4'} py-10 price-content`}>\r\n                            <h3 className={`text-2xl font-bold mb-4 lg:text-2xl ${!allowTrialPlan || isSinglePlan ? '' : 'sm:text-[1.07rem]'}`}>{localesCookie !== 'en' && allowTrialPlan ? t('echo.pricing.vprice.localesLabel') : plan.label}</h3>\r\n                            {GetCookie(\"daily\") === 'on' ? (<DailyPrice plan={plan}/>) :\r\n                              (billedAnnualDisplay && plan.payment_interval === 'Yearly' && (ppg === '48' || ptoggle === '02') ?\r\n                                (\r\n                                  <p className={`text-4xl font-bold text-[#4285F4] ${ppg === '48' && ptoggle === '01' ? 'mb-4' : ''}`}>\r\n                                    {getPricePlan(plan.currency, parseFloat(plan.price / 12).toFixed(2))}\r\n                                    <span className=\"text-lg text-gray-400\"> /month</span>\r\n                                  </p>\r\n                                ) : plan.trial_price ?\r\n                                  (\r\n                                    <p className=\"text-4xl font-bold text-[#4285F4]\">\r\n                                      {getPricePlan(plan.currency, plan.trial_price)}\r\n                                    </p>\r\n                                  ) :\r\n                                  (\r\n                                    <p className={`text-2xl lg:text-4xl font-bold text-[#4285F4] mb-4 ${(ptoggle === '02' || ptoggle === '03') ? '' : ''}`}>\r\n                                      {(ptoggle === '02' || ptoggle === '03') && plan.payment_interval === \"Yearly\"\r\n                                        ? getPricePlan(plan.currency, parseFloat(plan.price / 12).toFixed(2))\r\n                                        : getPricePlan(plan.currency, plan.price)}\r\n                                      <span className=\"text-lg text-gray-400\">\r\n                                        /{(ptoggle === '02' || ptoggle === '03') && plan.payment_interval === \"Yearly\"\r\n                                          ? ` ${t('echo.pricing.vprice.monthText')}`\r\n                                          : plan.payment_interval === \"Monthly\"\r\n                                            ? ` ${t('echo.pricing.vprice.monthText')}`\r\n                                            : ` ${t('echo.pricing.vprice.yearText')}`}\r\n                                      </span>\r\n                                    </p>\r\n                                  )\r\n                              )\r\n                            }\r\n                            <motion.button\r\n                              className={`block text-white font-bold py-3 mb-4 rounded-2xl text-[18px] sm:text-[16px] lg:text-[18px] px-3 lg:px-6 max-w-768:rounded-full bg-gradient-to-r from-[#3D56BA] to-[#268FED] w-full mb-[50px] max-w-768:mb-[24px] sm-[26px] md:mb-[30px] lg:mb-[40px] ${allowTrialPlan || isSinglePlan ? \"mt-[10px]\" : \"\"}`}\r\n                              style={{backgroundColor: hexHash(pp_ctaclr)}}\r\n                              whileHover={{ scale: 1.05, backgroundColor: hoverDarken(pp_ctaclr) }}\r\n                              whileTap={{ scale: 0.9 }}\r\n                              onClick={() => setPricing(plan.plan_id)}\r\n                            >\r\n                              {isTablet && !allowTrialPlan ? 'Choose This Plan' : t('echo.pricing.vprice.chooseThisPlanText')}\r\n                            </motion.button>\r\n                            {(ptoggle === \"02\" || ptoggle === \"03\") && plan.payment_interval === 'Yearly' && GetCookie(\"daily\") !== 'on' && (\r\n                              <span className={`absolute text-lg text-gray-400 text-gray-600 mb-4 absolute left-[0] right-[0] mt-[-20px]  ${ptoggle === \"03\" ? \"lg:mt-[-8px]\" : \"lg:mt-[-30px]\"}`}>\r\n                                or {getPricePlan(plan.currency, plan.price)} annual\r\n                              </span>\r\n                            )}\r\n                            {(\r\n                              (billedAnnualDisplay && plan.payment_interval === 'Yearly' && !['01', '02', '03'].includes(ptoggle) && GetCookie(\"daily\") !== 'on') ||\r\n                              (!['01', '02', '03'].includes(ptoggle) && ppg === 48 && GetCookie(\"daily\") !== 'on')\r\n                            ) && (\r\n                                <div className='text-xs mb-4'>(billed yearly)</div>\r\n                              )}\r\n                            <div className={`mb-6 ${(ptoggle === '02' || ptoggle === '03') ? 'mt-4' : ''} pricing-description ${desc_align === 'left' ? 'text-left' : 'text-center'} ppg-${ppg}`}>\r\n\r\n                              <ul className=\"text-sm text-gray-600 space-y-4 \">\r\n                                {plan.plan_type === 'Basic' && (\r\n                                  <>\r\n                                    <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\"></li>\r\n                                    <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\">{isMobile && 'GPT-4o: '}<span className='text-[#3C57BB] font-bold'> <img src={CheckIcon} alt=\"Infinite\" className=\"mx-auto w-[22px]\"/></span></li>\r\n                                    <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\">{isMobile && 'DeepSeek:'}<span className='text-[#3C57BB] font-bold'> <img src={CheckIcon} alt=\"Infinite\" className=\"mx-auto w-[22px]\"/></span></li>\r\n                                    {!isMobile && (\r\n                                      <>\r\n                                      <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\">-</li>\r\n                                      <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\">-</li>\r\n                                      <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\">-</li>\r\n                                      </>\r\n                                    )}\r\n                                    <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\">\r\n                                      {isMobile && 'Claude: '}\r\n                                      {localesCookie !== 'en' && allowTrialPlan ? t('echo.pricing.vprice.claudeAmtText') : '25k tokens'} \r\n                                      {localesCookie === 'en' ? ((isTablet || isMobile) && !allowTrialPlan ? (GetCookie('promo') ? '/month' : '/mo.') : '/month') : null}</li>\r\n                                    <li className={`flex items-center gap-2 justify-center text-[16px] ${dallETitleHeightExtended ? 'h-[40px]' : 'h-[24px]'}`}></li>\r\n                                    <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\">\r\n                                      {isMobile && 'DALL-E 3: '}\r\n                                      {localesCookie !== 'en' && allowTrialPlan ? t('echo.pricing.vprice.dalle3AmtText') : '20 images'} \r\n                                      {localesCookie === 'en' ? ((isTablet || isMobile) && !allowTrialPlan ? (GetCookie('promo') ? '/month' : '/mo.') : '/month') : null}\r\n                                    </li>\r\n                                    <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\">\r\n                                      {isMobile && 'Flux: '}\r\n                                      {localesCookie !== 'en' && allowTrialPlan ? t('echo.pricing.vprice.fluxAmtText') : '15 images'} \r\n                                      {localesCookie === 'en' ? ((isTablet || isMobile) && !allowTrialPlan ? (GetCookie('promo') ? '/month' : '/mo.') : '/month') : null}\r\n                                    </li>\r\n                                    <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\"></li>\r\n                                    {!isMobile && (\r\n                                      <>\r\n                                        <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\">-</li>\r\n                                        <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\"></li>\r\n                                      </>\r\n                                    )}\r\n                                    <li className={`flex items-center gap-2 justify-center text-[16px] ${isMobile && !isTablet ? 'h-[40px]' : 'h-[24px]'}`}>\r\n                                      {isMobile && `${t('echo.pricing.vprice.dialogueLimitText')}: `}\r\n                                      {localesCookie !== 'en' && allowTrialPlan ? t('echo.pricing.vprice.dialogueLimitAmtText') : '500,000 tokens'}\r\n                                      {(isTablet || isMobile) && !allowTrialPlan ? (GetCookie('promo') ? '/month' : '/mo.') : (localesCookie === 'en' ? '/month' : '')}\r\n                                    </li>\r\n                                    {allowTrialPlan && <li className={`flex items-center gap-2 justify-center text-[16px] ${(isMobile && !isTablet) || isExtendedTrialPlanDetail ? 'h-[40px]' : 'h-[24px]'}`}>\r\n                                      {isMobile && `${t('echo.pricing.vprice.trialPeriodText')}: `}\r\n                                      {localesCookie !== 'en' && allowTrialPlan ? \r\n                                      (localesCookie === 'it' || localesCookie === 'pt' || localesCookie === 'en' || localesCookie === 'fr' || localesCookie === 'tr'  || localesCookie === 'de'  ? \r\n                                        t('echo.pricing.vprice.trialPeriodAmtText1')\r\n                                        :\r\n                                        (localesCookie === 'es' ? \r\n                                          t('echo.pricing.vprice.trialPeriodAmtText1') + `${plan?.currency_symbol+plan?.price}` + t('echo.pricing.vprice.trialPeriodAmtText2') \r\n                                            : \r\n                                            (localesCookie === 'pl' ?\r\n                                              (ppg === \"72\" ?\r\n                                                t('echo.pricing.vprice.trialPeriodAmtText1') + `${plan?.price+plan?.currency_symbol}` + t('echo.pricing.vprice.trialPeriodAmtText2')\r\n                                                :\r\n                                                t('echo.pricing.vprice.trialPeriodAmtText1') + `${plan?.currency_symbol+plan?.price}` + t('echo.pricing.vprice.trialPeriodAmtText2')\r\n                                              )\r\n                                              :\r\n                                              `${plan?.label}, then ${getPricePlan(plan.currency, plan.price)}/month`\r\n                                            ))\r\n                                      ) : `${plan?.label}, then ${getPricePlan(plan.currency, plan.price)}/month`}\r\n                                    </li>}\r\n                                  </>\r\n                                )}\r\n                                {(plan.plan_type === 'Pro') && (\r\n                                  <>\r\n                                    <li className={`flex items-center gap-2 justify-center text-[16px] ${false ? 'min-w-1315:mb-[-7px] min-w-1315:h-0 min-w-1204:h-[12px] min-w-995:h-[32px] h-[22px]' : 'h-[24px]'}`}></li>\r\n                                    <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\">{isMobile && 'GPT-4o: '}<span className='text-[#3C57BB] font-bold'> <img src={CheckIcon} alt=\"Infinite\" className=\"mx-auto w-[22px]\"/></span></li>\r\n                                    <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\">{isMobile && 'DeepSeek: '}<span className='text-[#3C57BB] font-bold'> <img src={CheckIcon} alt=\"Infinite\" className=\"mx-auto w-[22px]\"/></span></li>\r\n                                    <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\">{isMobile && 'Grok: '}<span className='text-[#3C57BB] font-bold'> <img src={CheckIcon} alt=\"Infinite\" className=\"mx-auto w-[22px]\"/></span></li>\r\n                                    <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\">{isMobile && 'GPT-5: '}<span className='text-[#3C57BB] font-bold'> <img src={CheckIcon} alt=\"Infinite\" className=\"mx-auto w-[22px]\"/></span></li>\r\n                                    {!isMobile && (\r\n                                      <>\r\n                                      <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\">-</li>\r\n                                      </>\r\n                                    )}\r\n                                    <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\">\r\n                                      {isMobile && 'Claude: '}\r\n                                      {localesCookie !== 'en' && allowTrialPlan ? t('echo.pricing.vprice.claudeAmtText') : '50k tokens'} \r\n                                      {localesCookie === 'en' ? ((isTablet || isMobile) && !allowTrialPlan ? (GetCookie('promo') ? '/month' : '/mo.') : '/month') : null}\r\n                                    </li>\r\n                                    <li className={`flex items-center gap-2 justify-center text-[16px] ${dallETitleHeightExtended ? 'h-[40px]' : 'h-[24px]'}`}></li>\r\n                                    <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\">\r\n                                      {isMobile && 'DALL-E 3: '}\r\n                                      {localesCookie !== 'en' && allowTrialPlan ? t('echo.pricing.vprice.dalle3AmtText') : '50 images'} \r\n                                      {localesCookie === 'en' ? ((isTablet || isMobile) && !allowTrialPlan ? (GetCookie('promo') ? '/month' : '/mo.') : '/month') : null}\r\n                                    </li>\r\n                                    <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\">\r\n                                      {isMobile && 'Flux: '}\r\n                                      {localesCookie !== 'en' && allowTrialPlan ? t('echo.pricing.vprice.fluxAmtText') : '30 images'} \r\n                                      {localesCookie === 'en' ? ((isTablet || isMobile) && !allowTrialPlan ? (GetCookie('promo') ? '/month' : '/mo.') : '/month') : null}\r\n                                    </li>\r\n                                    <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\"></li>\r\n                                    <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\">\r\n                                      {isMobile && 'KlingAI: '}\r\n                                      {localesCookie !== 'en' && allowTrialPlan ? t('echo.pricing.vprice.klingAIAmtText') : '3 videos'} \r\n                                      {localesCookie === 'en' ? ((isTablet || isMobile) && !allowTrialPlan ? (GetCookie('promo') ? '/month' : '/mo.') : '/month') : null}\r\n                                    </li>\r\n                                    <li className={`flex items-center gap-2 justify-center text-[16px] ${isChatPDFContextFull && !isSinglePlan ? 'h-[28px]' : 'h-[24px]'}`}></li>\r\n                                    <li className={`flex items-center gap-2 justify-center text-[16px] ${isMobile && !isTablet ? 'h-[40px]' : 'h-[24px]'}`}>\r\n                                      {isMobile && `${t('echo.pricing.vprice.dialogueLimitText')}: `}\r\n                                      {localesCookie !== 'en' && allowTrialPlan ? t('echo.pricing.vprice.dialogueLimitAmtText') : '1,000,000 tokens'} \r\n                                      {(isTablet || isMobile) && !allowTrialPlan ? (GetCookie('promo') ? '/month' : '/mo.') : (localesCookie === 'en' ? '/month' : '')}\r\n                                    </li>\r\n                                    {allowTrialPlan && <li className={`flex items-center gap-2 justify-center text-[16px] ${(isMobile && !isTablet) || isExtendedTrialPlanDetail ? 'h-[40px]' : 'h-[24px]'}`}>\r\n                                      {isMobile && `${t('echo.pricing.vprice.trialPeriodText')}: `}\r\n                                      {localesCookie !== 'en' && allowTrialPlan ? \r\n                                      (localesCookie === 'it' || localesCookie === 'pt' || localesCookie === 'en' || localesCookie === 'fr' || localesCookie === 'tr'  || localesCookie === 'de'  ? \r\n                                        t('echo.pricing.vprice.trialPeriodAmtText1')\r\n                                        :\r\n                                        (localesCookie === 'es' ? \r\n                                          t('echo.pricing.vprice.trialPeriodAmtText1') + `${plan?.currency_symbol+plan?.price}` + t('echo.pricing.vprice.trialPeriodAmtText2')\r\n                                            : \r\n                                            (localesCookie === 'pl' ?\r\n                                              (ppg === \"72\" ?\r\n                                                t('echo.pricing.vprice.trialPeriodAmtText1') + `${plan?.price+plan?.currency_symbol}` + t('echo.pricing.vprice.trialPeriodAmtText2')\r\n                                                :\r\n                                                t('echo.pricing.vprice.trialPeriodAmtText1') + `${plan?.currency_symbol+plan?.price}` + t('echo.pricing.vprice.trialPeriodAmtText2')\r\n                                              )\r\n                                              :\r\n                                              `${plan?.label}, then ${getPricePlan(plan.currency, plan.price)}/month`\r\n                                            ))\r\n                                      ) : `${plan?.label}, then ${getPricePlan(plan.currency, plan.price)}/month`}\r\n                                    </li>}\r\n                                  </>\r\n                                )}\r\n                                {(plan.plan_type === 'Advanced') && (\r\n                                  <>\r\n                                    <li className={`flex items-center gap-2 justify-center text-[16px] ${false ? 'min-w-1315:mb-[-7px] min-w-1315:h-0 min-w-1204:h-[12px] min-w-995:h-[32px] h-[22px]' : 'h-[24px]'}`}></li>\r\n                                    {!isMobile && <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\">-</li>}\r\n                                    <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\">{isMobile && 'DeepSeek: '}<span className='text-[#3C57BB] font-bold'> <img src={CheckIcon} alt=\"Infinite\" className=\"mx-auto w-[22px]\"/></span></li>\r\n                                    {!isMobile && <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\">-</li>}\r\n                                    <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\">{isMobile && 'GPT-5: '}<span className='text-[#3C57BB] font-bold'> <img src={CheckIcon} alt=\"Infinite\" className=\"mx-auto w-[22px]\"/></span></li>\r\n                                    {!isMobile && <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\">-</li>}\r\n                                    <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\">\r\n                                      {isMobile && 'Claude: '}\r\n                                      {localesCookie !== 'en' && allowTrialPlan ? t('echo.pricing.vprice.claudeAmtText') : '50k tokens'} \r\n                                      {localesCookie === 'en' ? ((isTablet || isMobile) && !allowTrialPlan ? (GetCookie('promo') ? '/month' : '/mo.') : '/month') : null}\r\n                                    </li>\r\n                                    <li className={`flex items-center gap-2 justify-center text-[16px] ${dallETitleHeightExtended ? 'h-[40px]' : 'h-[24px]'}`}></li>\r\n                                    <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\">\r\n                                      {isMobile && 'DALL-E 3: '}\r\n                                      {localesCookie !== 'en' && allowTrialPlan ? t('echo.pricing.vprice.dalle3AmtTextAdv') : '80 images'} \r\n                                      {localesCookie === 'en' ? ((isTablet || isMobile) && !allowTrialPlan ? (GetCookie('promo') ? '/month' : '/mo.') : '/month') : null}\r\n                                    </li>\r\n                                    <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\">\r\n                                      {isMobile && 'Flux: '}\r\n                                      {localesCookie !== 'en' && allowTrialPlan ? t('echo.pricing.vprice.fluxAmtTextAdv') : '80 images'} \r\n                                      {localesCookie === 'en' ? ((isTablet || isMobile) && !allowTrialPlan ? (GetCookie('promo') ? '/month' : '/mo.') : '/month') : null}\r\n                                    </li>\r\n                                    <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\"></li>\r\n                                    <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\">\r\n                                      {isMobile && 'KlingAI: '}\r\n                                      {localesCookie !== 'en' && allowTrialPlan ? t('echo.pricing.vprice.klingAIAmtTextAdv') : '7 videos'} \r\n                                      {localesCookie === 'en' ? ((isTablet || isMobile) && !allowTrialPlan ? (GetCookie('promo') ? '/month' : '/mo.') : '/month') : null}\r\n                                    </li>\r\n                                    <li className={`flex items-center gap-2 justify-center text-[16px] ${isChatPDFContextFull && !isSinglePlan ? 'h-[28px]' : 'h-[24px]'}`}></li>\r\n                                    <li className={`flex items-center gap-2 justify-center text-[16px] ${isMobile && !isTablet ? 'h-[40px]' : 'h-[24px]'}`}>\r\n                                      {isMobile && `${t('echo.pricing.vprice.dialogueLimitText')}: `}\r\n                                      {localesCookie !== 'en' && allowTrialPlan ? t('echo.pricing.vprice.dialogueLimitAmtTextAdv') : '2,500,000 tokens'} \r\n                                      {isTablet && !allowTrialPlan ? (GetCookie('promo') ? '/month' : '/mo.') : (localesCookie === 'en' ? '/month' : '')}\r\n                                    </li>\r\n                                    {allowTrialPlan && <li className={`flex items-center gap-2 justify-center text-[16px] ${(isMobile && !isTablet) || isExtendedTrialPlanDetail ? 'h-[40px]' : 'h-[24px]'}`}>\r\n                                      {isMobile && `${t('echo.pricing.vprice.trialPeriodText')}: `}\r\n                                      {localesCookie !== 'en' && allowTrialPlan ? \r\n                                      (localesCookie === 'it' || localesCookie === 'pt' || localesCookie === 'en' || localesCookie === 'fr' || localesCookie === 'tr'  || localesCookie === 'de'  ? \r\n                                        t('echo.pricing.vprice.trialPeriodAmtText1')\r\n                                        :\r\n                                        (localesCookie === 'es' ? \r\n                                          t('echo.pricing.vprice.trialPeriodAmtText1') + `${plan?.currency_symbol+plan?.price}` + t('echo.pricing.vprice.trialPeriodAmtText2')\r\n                                            : \r\n                                            (localesCookie === 'pl' ?\r\n                                              (ppg === \"72\" ?\r\n                                                t('echo.pricing.vprice.trialPeriodAmtText1') + `${plan?.price+plan?.currency_symbol}` + t('echo.pricing.vprice.trialPeriodAmtText2')\r\n                                                :\r\n                                                t('echo.pricing.vprice.trialPeriodAmtText1') + `${plan?.currency_symbol+plan?.price}` + t('echo.pricing.vprice.trialPeriodAmtText2')\r\n                                              )\r\n                                              :\r\n                                              `${plan?.label}, then ${getPricePlan(plan.currency, plan.price)}/month`\r\n                                            ))\r\n                                      ) : `${plan?.label}, then ${getPricePlan(plan.currency, plan.price)}/month`}\r\n                                    </li>}\r\n                                  </>\r\n                                )}\r\n                                {plan.plan_type === 'ProMax' && (\r\n                                  <>\r\n                                    <li className={`flex items-center gap-2 justify-center text-[16px] ${false ? 'min-w-1315:mb-[-7px] min-w-1315:h-0 min-w-1204:h-[12px] min-w-995:h-[32px] h-[22px]' : 'h-[24px]'}`}></li>\r\n                                    <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\">{isMobile && 'GPT-4o: '}<span className='text-[#3C57BB] font-bold'> <img src={CheckIcon} alt=\"Infinite\" className=\"mx-auto w-[22px]\"/></span></li>\r\n                                    <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\">{isMobile && 'DeepSeek: '}<span className='text-[#3C57BB] font-bold'> <img src={CheckIcon} alt=\"Infinite\" className=\"mx-auto w-[22px]\"/></span></li>\r\n                                    <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\">{isMobile && 'Grok: '}<span className='text-[#3C57BB] font-bold'> <img src={CheckIcon} alt=\"Infinite\" className=\"mx-auto w-[22px]\"/></span></li>\r\n                                    <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\">{isMobile && 'GPT-5: '}<span className='text-[#3C57BB] font-bold'> <img src={CheckIcon} alt=\"Infinite\" className=\"mx-auto w-[22px]\"/></span></li>\r\n                                    <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\">{isMobile && 'OpenAI o1: '}<span className='text-[#3C57BB] font-bold'> <img src={CheckIcon} alt=\"Infinite\" className=\"mx-auto w-[22px]\"/></span></li>\r\n                                    <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\">{isMobile && 'Claude: '}<span className='text-[#3C57BB] font-bold'>{t('echo.pricing.vprice.unlimitedText')}</span></li>\r\n                                    <li className={`flex items-center gap-2 justify-center text-[16px] ${dallETitleHeightExtended ? 'h-[40px]' : 'h-[24px]'}`}></li>\r\n                                    <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\">{isMobile && 'DALL-E 3: '}<span className='text-[#3C57BB] font-bold'>{t('echo.pricing.vprice.unlimitedText')}</span></li>\r\n                                    <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\">{isMobile && 'Flux: '}160 images{isTablet && !allowTrialPlan ? '/mo.' : '/month'}</li>\r\n                                    <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\"></li>\r\n                                    <li className=\"flex items-center gap-2 justify-center text-[16px] h-[24px]\">{isMobile && 'KlingAI: '}15 videos{isTablet && !allowTrialPlan ? '/mo.' : '/month'}</li>\r\n                                    <li className={`flex items-center gap-2 justify-center text-[16px] ${(isChatPDFContextFull && !isSinglePlan) || isUseChatPdfContextDesign ? 'h-[28px]' : 'h-[24px]'}`}></li>\r\n                                    <li className={`flex items-center gap-2 justify-center text-[16px] ${isMobile && !isTablet ? 'h-[40px]' : 'h-[24px]'}`}>{isMobile && `${t('echo.pricing.vprice.dialogueLimitText')}: `}<span className='text-[#3C57BB] font-bold'> Unlimited</span></li>\r\n                                    {allowTrialPlan && <li className={`flex items-center gap-2 justify-center text-[16px] ${(isMobile && !isTablet) || isExtendedTrialPlanDetail ? 'h-[40px]' : 'h-[24px]'}`}>{isMobile && `${t('echo.pricing.vprice.trialPeriodText')}: `}<span className='text-[#3C57BB] font-bold'>{`${plan?.label}, then ${getPricePlan(plan.currency, plan.price)}/month`}</span></li>}\r\n                                  </>\r\n                                )}\r\n                              </ul>\r\n                            </div>\r\n\r\n                          </div>\r\n                        </div>\r\n                        {/* Class max-w-768:min-w-[142px] was originally in the classlist, removed from op 36193 */}\r\n                        { plan.plan_type === 'Pro' && !allowTrialPlan && !GetCookie(\"promo\") ? <div className={`absolute top-0 left-1/2 transform rounded-full ${!isMobile && (!localesCookie || localesCookie === 'en') ? 'min-w-[130px] max-w-[155px]' : 'min-w-[155px] max-w-[180px]'} -translate-x-1/2 -translate-y-1/2 bg-blue-600 text-white font-bold text-[16px] py-1 px-2 text-xs span-highlight`}><HiMiniSparkles className='inline text-yellow-400' /> {t('echo.pricing.vprice.mostPopularText')}<HiMiniSparkles className='inline text-yellow-400'/>\r\n                        </div> : null }\r\n                      </div>\r\n                    ) : \"\"\r\n                  ))}\r\n                  {(enterprise === 'on' && ppg !== '46' && planInterval === 'yearly' && data[0].currency === 'USD') ?\r\n                    <>\r\n                    {enterpriseTab()}\r\n                    </>\r\n                  : null }\r\n                </div>\r\n              ) : (\r\n                <div className=\"flex flex-col md:flex-row justify-center\">\r\n                  {data?.map((plan, index) => (\r\n                    <div key={index} className={` w-full lg:w-[330px] xl:w-[400px] min-h-[474px] text-center sm:px-2 mb-8 relative`}>\r\n                      <div className=\"rounded bg-[#F5F5F5] h-full\">\r\n                        <div className=\"px-2 sm:px-4 py-10 price-content\">\r\n                          <h3 className=\"text-2xl font-bold mb-4\">{plan.label}</h3>\r\n                            <PriceFormatted plan={plan}/>\r\n                          <div className={`mb-6 pricing-description ${desc_align === 'left' ? 'text-left' : 'text-center'} ppg-${ppg}`}>\r\n                            <motion.button\r\n                              className=\"text-white font-bold py-3 mb-4 rounded-2xl px-3 lg:px-6 md:rounded-full bg-gradient-to-r from-[#3D56BA] to-[#268FED] w-full\"\r\n                              style={{backgroundColor: hexHash(pp_ctaclr)}}\r\n                              whileHover={{ scale: 1.05, backgroundColor: hoverDarken(pp_ctaclr) }}\r\n                              whileTap={{ scale: 0.9 }}\r\n                              onClick={() => setPricing(plan.plan_id)}\r\n                            >\r\n                            {plan.label.toLowerCase()===\"enterprise\" ? \"Build My Plan\" : \"Choose This Plan\"}\r\n                            </motion.button>\r\n                            <ul className=\"text-sm text-gray-600\">\r\n                              { plan.display_txt2 ? <li className=\"asd\"></li> : null }\r\n                            </ul>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                      { plan.plan_type === 'Pro' ? <div className=\"absolute top-0 left-1/2 transform rounded-full min-w-[180px] -translate-x-1/2 -translate-y-1/2 bg-blue-600 text-white font-bold text-[16px] py-1 px-4 text-xs span-highlight\"><HiMiniSparkles className='inline text-yellow-400' /> {t('echo.pricing.vprice.mostPopularText')} <HiMiniSparkles className='inline text-yellow-400'/>\r\n                        </div> : null }\r\n                    </div>\r\n                  ))}\r\n                  {(enterprise === 'on' && ppg !== '46' && data[0].currency === 'USD') ?\r\n                    <>\r\n                    {enterpriseTab()}\r\n                    </>\r\n                  : null }\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n\r\n          { tpreviews === 'on' ?\r\n            <>\r\n            <Suspense fallback={null}>\r\n              <TpReviews/>\r\n            </Suspense>\r\n            </>\r\n          : null }\r\n        </div>\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default VPrice;", "import React, { Suspense, lazy, useEffect } from \"react\";\r\nimport \"./components/style.css\";\r\n// import Header from \"../header/headerlogo\";\r\nimport { useQuery } from \"react-query\";\r\nimport axios from \"axios\";\r\nimport { GetCookie, SetCookie } from \"../core/utils/cookies\";\r\nimport { Helmet } from \"react-helmet\";\r\nimport _ from \"underscore\";\r\nimport { getPrefixLocation } from \"../core/utils/main.jsx\";\r\n\r\nimport VPrice from \"./components/vprice.jsx\";\r\nconst VPriceDL = lazy(() => import(\"./components/vprice_dl.jsx\"));\r\nconst VPrice01 = lazy(() => import(\"./components/vprice_01.jsx\"));\r\nconst VPrice02 = lazy(() => import(\"./components/vprice_02.jsx\"));\r\nconst VPrice03 = lazy(() => import(\"./components/vprice_03.jsx\"));\r\nconst VPrice04 = lazy(() => import(\"./components/vprice_04.jsx\"));\r\n\r\nconst VPriceClusterPlan = lazy(() =>\r\n  import(\"./components/vprice_cluster_plan.jsx\")\r\n);\r\n\r\nvar plan = null;\r\nvar showToggle = false;\r\nvar hasAnnual = false;\r\nasync function getPPG() {\r\n  var ppg = GetCookie(\"ppg\") ?? process.env.REACT_APP_DEFAULT_PPG ?? \"14\";\r\n  if (plan) return plan;\r\n  const response = await axios.post(\r\n    `${process.env.REACT_APP_API_URL}/get-pricing`,\r\n    { ppg },\r\n    { headers: { \"content-type\": \"application/x-www-form-urlencoded\" } }\r\n  );\r\n  const output = response.data;\r\n  if (output.success) {\r\n    plan = output.data.filter((value) => {\r\n      if (parseInt(ppg) === 59)\r\n        return value.plan_id !== process.env.REACT_APP_ENTERPRISE_ID;\r\n\r\n      return true;\r\n    });\r\n    hasAnnual = _.some(plan, function (o) {\r\n      return o.payment_interval.toLowerCase() === \"yearly\";\r\n    });\r\n    showToggle =\r\n      _.some(plan, function (o) {\r\n        return o.payment_interval.toLowerCase() === \"monthly\";\r\n      }) && hasAnnual;\r\n    return plan;\r\n  } else {\r\n    return [];\r\n  }\r\n}\r\n\r\nfunction Pricing() {\r\n  const { data } = useQuery(\"users\", getPPG);\r\n\r\n  useEffect(() => {\r\n    // window.close();\r\n  }, []);\r\n\r\n  if (data === undefined) return;\r\n  const tk = GetCookie(\"access\");\r\n\r\n  const setPricing = function (id, ent) {\r\n    SetCookie(\"pricing\", id, { path: \"/\" });\r\n    const redirectToPay = \"https://\" + getPrefixLocation() + \"start.ai-pro.org/pay\";\r\n    if (window.top !== window.self) {\r\n      window.top.location.href = redirectToPay;\r\n    } else {\r\n      setTimeout(() => {\r\n        window.location.href = redirectToPay;\r\n      }, 300);\r\n    }\r\n  };\r\n  const vPrice_token = GetCookie(\"vprice\") ? GetCookie(\"vprice\") : \"\";\r\n  const pp_echo = GetCookie(\"pp_echo\") ? GetCookie(\"pp_echo\") : \"\";\r\n  const commonProps = { showToggle, hasAnnual, data, setPricing };\r\n  const ppg = GetCookie(\"ppg\");\r\n\r\n  return (\r\n    <>\r\n      <Helmet auth={tk} >\r\n        <title>AI Pro | Plans and Pricing</title>\r\n        <meta\r\n          name=\"description\"\r\n          content=\"Discover our flexible AI pricing plans designed to suit your needs. Explore our features and find the perfect plan to elevate your projects.\"\r\n        />\r\n      </Helmet>\r\n      {/* <Header auth={tk} /> */}\r\n      <Suspense fallback={null}>\r\n        {(() => {\r\n          if (pp_echo === '02') {\r\n            return <VPrice04 {...commonProps} />;\r\n          } else if (ppg === \"108\") {\r\n            return <VPriceDL {...commonProps} />;\r\n          } else if (ppg === \"118\") {\r\n            return <VPriceClusterPlan {...commonProps} />;\r\n          } else if (\r\n            (vPrice_token === \"\" && ppg === \"46\") ||\r\n            vPrice_token === \"vP1zx12mXk\"\r\n          ) {\r\n            return <VPrice01 {...commonProps} />;\r\n          } else if (vPrice_token === \"vP2xyYxjjj\") {\r\n            return <VPrice02 {...commonProps} />;\r\n          } else if (vPrice_token === \"waYmXFAgLs\") {\r\n            return <VPrice03 {...commonProps} />;\r\n          } else {\r\n            return <VPrice {...commonProps} />;\r\n          }\r\n        })()}\r\n      </Suspense>\r\n    </>\r\n  );\r\n}\r\n\r\nexport default Pricing;\r\n"], "names": ["isWebpSupported", "elem", "document", "createElement", "getContext", "toDataURL", "indexOf", "hexHash", "hex", "length", "test", "slice", "hoverDarken", "r", "parseInt", "g", "b", "Math", "max", "min", "toHex", "value", "round", "toString", "padStart", "useDeviceSize", "isMobile", "setIsMobile_", "useState", "isTablet", "setIsTablet", "isDesktop", "setIsDesktop", "useEffect", "handleResize", "width", "window", "innerWidth", "addEventListener", "removeEventListener", "getCurrency", "currency", "toLowerCase", "getCountry", "formatDate", "dateStr", "date", "Date", "replace", "getMonth", "getDate", "getFullYear", "getPrice", "data", "trial_price", "price", "numberWithCommas", "x", "formatNumber", "number", "floatNumber", "parseFloat", "toFixed", "getPricePlan", "isNaN", "toLocaleString", "diffMin", "dt1", "dt2", "diff", "getTime", "abs", "formatDateTime", "getHours", "getMinutes", "DailyPrice", "_ref", "plan", "dailyPrice", "interval", "payment_interval", "_jsxs", "class", "children", "trial_days", "_Fragment", "className", "Get<PERSON><PERSON><PERSON>", "_jsx", "PriceFormatted", "_ref2", "getPrefixLocation", "currentLocation", "location", "href", "checkPlanInterval", "planInterval", "ppg", "billedAnnualDisplay", "ptoggle", "pp_ctaclr", "enterpriseTab", "enterprise", "tpreviews", "setPricing", "desc_align", "CheckIcon", "TpReviews", "appName", "setAppName", "appNameParam", "URLSearchParams", "search", "get", "map", "index", "label", "motion", "button", "style", "backgroundColor", "whileHover", "scale", "whileTap", "onClick", "plan_id", "includes", "plan_type", "src", "alt", "currency_symbol", "Suspense", "fallback", "React", "props", "useTranslation", "process", "showToggle", "isShowPPg", "setIsMobile", "is785to810", "is850to885", "is885to900", "is910to925", "is940to965", "is970to1005", "is1080to1090", "is1125to1130", "allowTrialPlan", "isChatPDFContext", "setIsChatPDFContext", "localesCookie", "referrer", "parentDomain", "URL", "hostname", "checkScreenSize", "setPlanInterval", "basicIsHidden", "t", "isSinglePlan", "isSinglePlanWillRemain", "rem", "isDoublePlan", "isShouldDisplayAllPlans", "isRemainedSinglePlanPro", "ndx", "startsWith", "isExtendedTrialPlanDetail", "isChatPDFContextFull", "isUseChatPdfContextDesign", "dallETitleHeightExtended", "plval", "langModelsTextPos", "paddingLeft", "getPricingText", "htmlFor", "type", "id", "onChange", "defaultChecked", "HiMiniSparkles", "display_txt2", "VPriceDL", "lazy", "VPrice01", "VPrice02", "VPrice03", "VPrice04", "VPriceClusterPlan", "has<PERSON>nnual", "async", "getPPG", "_<PERSON><PERSON><PERSON><PERSON>", "output", "axios", "post", "headers", "success", "filter", "_", "o", "useQuery", "undefined", "tk", "vPrice_token", "pp_echo", "commonProps", "ent", "<PERSON><PERSON><PERSON><PERSON>", "path", "redirectToPay", "top", "self", "setTimeout", "<PERSON><PERSON><PERSON>", "auth", "name", "content", "VPrice"], "sourceRoot": ""}