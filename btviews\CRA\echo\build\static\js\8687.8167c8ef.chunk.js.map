{"version": 3, "file": "static/js/8687.8167c8ef.chunk.js", "mappings": "2KAGA,MAoCA,EApCwBA,IAA0B,IAAzB,gBAAEC,GAAiBD,EAC1C,MAAOE,EAAWC,IAAgBC,EAAAA,EAAAA,WAAS,IAE3CC,EAAAA,EAAAA,WAAU,KACR,MAAMC,EAAeA,KACnBH,EAAaI,OAAOC,YAAc,MAAQP,IAGtCQ,EAA+BA,KACnCN,GAAcF,IAMhB,OAHAM,OAAOG,iBAAiB,SAAUJ,GAClCC,OAAOG,iBAAiB,SAAUD,GAE3B,KACLF,OAAOI,oBAAoB,SAAUL,GACrCC,OAAOI,oBAAoB,SAAUF,KAEtC,CAACR,IAUJ,OACEW,EAAAA,EAAAA,KAAA,OAAKC,UAAW,gBAAeX,EAAY,UAAY,IAAKY,UAC1DF,EAAAA,EAAAA,KAACG,EAAAA,IAAe,CAACC,QATDC,KAClBV,OAAOW,SAAS,CACdC,IAAK,EACLC,SAAU,gB,0ECFhB,QAxBA,WAGE,MAAMC,EAAWC,yBAWjB,OAVAjB,EAAAA,EAAAA,WAAU,KACR,MAAMkB,EAASC,SAASC,cAAc,UACtCF,EAAOG,IAAML,EAAW,iDACxBE,EAAOI,OAAQ,EACfJ,EAAOK,OAAS,OAIhBJ,SAASK,KAAKC,YAAYP,IACzB,CAACF,KAEFT,EAAAA,EAAAA,KAAAmB,EAAAA,SAAA,CAAAjB,UACEF,EAAAA,EAAAA,KAAA,UAAQC,UAAW,iHAMzB,C,sHC4CA,QA9DA,SAAmBb,GAAkC,IAAjC,YAAEgC,EAAW,KAAEC,GAAOC,EAAAA,EAAAA,OAAQlC,EAChD,MAAMmC,EAAW5B,OAAO6B,SAASD,SAC3BE,EAAc,yBAAyBC,KAAKH,GAC5CI,EAAgB,sBAAsBD,KAAKH,GAC3CK,EAAc,yBAAyBF,KAAKH,GAC5CM,EAAiBN,EAASO,SAAS,kBACnCC,GAAYX,EACZY,EAAwBtB,CAAAA,SAAAA,aAAAA,WAAAA,eAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,cAAAA,EAAAA,kBAAAA,4BAAAA,mBAAAA,yBAAAA,oCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,kCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,2BAAAA,iBAAAA,4BAAAA,cAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,oBAAAA,uBAAAA,yBAAAA,8BAAAA,yBAAAA,gFAAAA,uBAAAA,6CAAAA,sBAAAA,KAAAA,uBAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,wBAAAA,KAAAA,mBAAAA,aAAAA,qCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,mCAAAA,KAAAA,8BAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,4BAAAA,4BAAAA,gCAAAA,oBAAAA,gCAAAA,oBAAAA,gCAAAA,oBAAAA,gCAAAA,oBAAAA,kCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,gCAAAA,KAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,iCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,6BAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,yBAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,iCAAAA,8GAAAA,iCAAAA,8GAAAA,oBAAAA,8jBAAYuB,iCAAmC,IAE7ExC,EAAAA,EAAAA,WAAU,KACR,MAAMyC,EAAUC,EAAkBC,EAAAA,EAAW,SACvCC,EAAWF,EAAkBG,EAAe,SAMlD,OALA1B,SAAS2B,KAAKC,OAAON,EAASG,GAEzBR,GACH,yDAEK,KACLK,EAAQO,SACRJ,EAASI,WAEV,CAACZ,IAGJ,MAAMM,EAAoBA,CAACO,EAAMC,KAC/B,MAAMC,EAAOhC,SAASC,cAAc,QAIpC,OAHA+B,EAAKC,IAAM,UACXD,EAAKF,KAAOA,EACZE,EAAKD,GAAKA,EACHC,GAGT,OACEE,EAAAA,EAAAA,MAAA3B,EAAAA,SAAA,CAAAjB,SAAA,CACG8B,IACChC,EAAAA,EAAAA,KAAA,OAAK+C,GAAG,wBAAwB9C,UAAU,mCAAkCC,SAAC,sFAI/EF,EAAAA,EAAAA,KAAA,UAAQ+C,GAAG,SAAS9C,UAAW,+FAA8F+B,EAAwB,aAAe,IAAK9B,UACvK4C,EAAAA,EAAAA,MAAA,OAAK7C,UAAU,2DAA0DC,SAAA,EACvE4C,EAAAA,EAAAA,MAAA,WAAS7C,UAAU,YAAWC,SAAA,EAC5BF,EAAAA,EAAAA,KAAA,UAAQgD,KAAK,aAAaC,OAAQX,EAAeY,MAAM,MAAMC,OAAO,KAAKlD,UAAU,eACnFD,EAAAA,EAAAA,KAAA,OAAKc,IAAKsB,EAAAA,EAAWgB,IAAI,cAAcnD,UAAU,kBAEjDwB,GAAeE,GAAiBC,IAAgBG,IAChD/B,EAAAA,EAAAA,KAAA,OAAKC,UAAU,uCAAuC8C,GAAG,OAAM7C,UAC7DF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,2BAA0BC,UACtCF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,uBAAsBC,UAClCF,EAAAA,EAAAA,KAAA,KAAG0C,KAAMrB,EAAO,cAAgB,SAAUpB,UAAU,YAAY,aAAYoB,EAAO,aAAe,QAAQnB,SACvGmB,EAAO,UAAY,wBAUxC,C,mMCgFA,QApIA,WACE,MAAMA,GAAOC,EAAAA,EAAAA,OACN+B,EAASC,IAAc9D,EAAAA,EAAAA,UAAS,QACjC+D,GAAYC,EAAAA,EAAAA,QAAO,OAClBnE,EAAiBoE,IAAsBjE,EAAAA,EAAAA,WAAS,GACjDkE,MAAYC,EAAAA,EAAAA,IAAU,aAAyC,QAA1BA,EAAAA,EAAAA,IAAU,cAErDlE,EAAAA,EAAAA,WAAU,KAER,MAAMmE,EAAUhD,SAASiD,OAAOC,MAAM,KACtC,IAAK,IAAIC,EAAI,EAAGA,EAAIH,EAAQI,OAAQD,IAAK,CACvC,MAAMF,EAASD,EAAQG,GAAGE,OAC1B,GAAIJ,EAAOK,WAAW,YAAa,CACjCZ,EAAWO,EAAOM,UAAU,IAC5B,KACF,CACF,CAGA,MAAMzE,EAAeA,KACnB,MAAM0E,EAAeb,EAAUc,QAAQC,wBAAwBC,OAC/Dd,EAAmB9D,OAAOC,YAAcwE,IAK1C,OAFAzE,OAAOG,iBAAiB,SAAUJ,GAE3B,KACLC,OAAOI,oBAAoB,SAAUL,KAItC,CAAC2B,IAEJ,MAAMmD,EAAY9D,CAAAA,SAAAA,aAAAA,WAAAA,eAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,cAAAA,EAAAA,kBAAAA,4BAAAA,mBAAAA,yBAAAA,oCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,kCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,2BAAAA,iBAAAA,4BAAAA,cAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,oBAAAA,uBAAAA,yBAAAA,8BAAAA,yBAAAA,gFAAAA,uBAAAA,6CAAAA,sBAAAA,KAAAA,uBAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,wBAAAA,KAAAA,mBAAAA,aAAAA,qCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,mCAAAA,KAAAA,8BAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,4BAAAA,4BAAAA,gCAAAA,oBAAAA,gCAAAA,oBAAAA,gCAAAA,oBAAAA,gCAAAA,oBAAAA,kCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,gCAAAA,KAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,iCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,6BAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,yBAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,iCAAAA,8GAAAA,iCAAAA,8GAAAA,oBAAAA,8jBAAY+D,kBAY9B,QAAYC,IAATrD,EACH,OACEyB,EAAAA,EAAAA,MAAA3B,EAAAA,SAAA,CAAAjB,SAAA,EACE4C,EAAAA,EAAAA,MAAC6B,EAAAA,EAAM,CAAAzE,SAAA,EACLF,EAAAA,EAAAA,KAAA,QAAM4E,KAAK,SAASC,QAAQ,uBAC5B7E,EAAAA,EAAAA,KAAA,SAAAE,SAAO,gDACPF,EAAAA,EAAAA,KAAA,QAAM4E,KAAK,cAAcC,QAAQ,iJAEtB,SAAZxB,GACCrD,EAAAA,EAAAA,KAAC8E,EAAAA,QAAM,CAAC1D,YAAasC,KAErB1D,EAAAA,EAAAA,KAAC+E,EAAAA,QAAU,CAAC3D,YAAasC,KAEzB1D,EAAAA,EAAAA,KAAA,OAAKgF,IAAKzB,KACVvD,EAAAA,EAAAA,KAACiF,EAAAA,QAAe,CAAC5F,gBAAiBA,KAClCyD,EAAAA,EAAAA,MAAA,OAAK7C,UAAU,4BAA2BC,SAAA,EACxCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,0CAAyCC,UACtD4C,EAAAA,EAAAA,MAAA,OAAK7C,UAAU,uCAAsCC,SAAA,EACnD4C,EAAAA,EAAAA,MAAA,OAAK7C,UAAU,iIAAgIC,SAAA,EAC7IF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,+EAA8EC,SAAC,mBAC7F4C,EAAAA,EAAAA,MAAA,KAAG7C,UAAU,mBAAkBC,SAAA,CAAC,wDACsBF,EAAAA,EAAAA,KAAA,SAAK,4CAAwCA,EAAAA,EAAAA,KAAA,SAAK,8CAA0CA,EAAAA,EAAAA,KAAA,SAAK,0DAGzJA,EAAAA,EAAAA,KAAA,OAAKC,UAAU,uCAAsCC,UACnDF,EAAAA,EAAAA,KAAA,UAAQ+C,GAAG,gBAAgBmC,MAAM,kBAAkBpE,IAAK0D,EAAWW,YAAY,cAMrFrC,EAAAA,EAAAA,MAAA,OAAK7C,UAAU,gCAA+BC,SAAA,EAC5CF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gCAA+BC,UAC5C4C,EAAAA,EAAAA,MAAA,OAAK7C,UAAU,+CAA8CC,SAAA,EAC3DF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,+BAA8BC,UAC3C4C,EAAAA,EAAAA,MAAA,MAAI7C,UAAU,+CAA8CC,SAAA,CAAC,cAAUF,EAAAA,EAAAA,KAAA,SAAK,oBAE9E8C,EAAAA,EAAAA,MAAA,OAAK7C,UAAU,+BAA8BC,SAAA,EAC3CF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,yBAAwBC,SAAC,2BACvC4C,EAAAA,EAAAA,MAAA,KAAA5C,SAAA,CAAG,8DAA0DF,EAAAA,EAAAA,KAAA,KAAG0C,KAAK,iBAAiBzC,UAAU,YAAWC,SAAC,iBAAgB,WAE9H4C,EAAAA,EAAAA,MAAA,OAAK7C,UAAU,+BAA8BC,SAAA,EAC3CF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,yBAAwBC,SAAC,wBACvCF,EAAAA,EAAAA,KAAA,KAAAE,SAAG,2HAEL4C,EAAAA,EAAAA,MAAA,OAAK7C,UAAU,+BAA8BC,SAAA,EAC3CF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,yBAAwBC,SAAC,2BACvCF,EAAAA,EAAAA,KAAA,KAAAE,SAAG,4LAITF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,4BAA2BC,UACxCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oBAAmBC,UAChCF,EAAAA,EAAAA,KAAA,OAAKc,IAAKsE,EAAMhC,IAAI,MAAMnD,UAAU,0CAK5C6C,EAAAA,EAAAA,MAAA,OAAK7C,UAAU,gCAA+BC,SAAA,EAC5CF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oDAAmDC,UAChE4C,EAAAA,EAAAA,MAAA,OAAK7C,UAAU,mEAAkEC,SAAA,EAC/EF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,+DAA8DC,SAAC,iCAG7EF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,kDAAiDC,SAAC,+VAG/DF,EAAAA,EAAAA,KAACqF,EAAAA,EAAOC,OAAM,CACZlF,QA9EYmF,KACnBlE,EAEMA,GAAwB,WAAhBA,EAAKmE,OACtB7F,OAAO6B,SAASkB,KAAO,cAEvB/C,OAAO6B,SAASkB,KAAO,WAJvB/C,OAAO6B,SAASkB,KAAO,kBA6EbzC,UAAU,sHACVwF,WAAY,CAAEC,MAAO,KACrBC,SAAU,CAAED,MAAO,IAAMxF,SAC1B,0BAKLF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,sEAAqEC,UAClFF,EAAAA,EAAAA,KAAA,OAAKc,IAAK8E,EAAQxC,IAAI,iBAAiBnD,UAAU,qCAIvDD,EAAAA,EAAAA,KAAC6F,EAAAA,QAAM,CAACxE,KAAMA,EAAMD,YAAasC,MAGvC,C,gDC/IA,SAAiB,C", "sources": ["footer/backtotop.jsx", "footer/index.jsx", "header/headerlogo.jsx", "lp/text-to-image.jsx", "webpack://v1/./node_modules/react-responsive-carousel/lib/styles/carousel.css?c782"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { FaArrowCircleUp } from 'react-icons/fa';\r\n\r\nconst BackToTopButton = ({ isHeaderVisible }) => {\r\n  const [isVisible, setIsVisible] = useState(false);\r\n\r\n  useEffect(() => {\r\n    const handleScroll = () => {\r\n      setIsVisible(window.pageYOffset > 300 && !isHeaderVisible);\r\n    };\r\n\r\n    const handleHeaderVisibilityChange = () => {\r\n      setIsVisible(!isHeaderVisible);\r\n    };\r\n\r\n    window.addEventListener('scroll', handleScroll);\r\n    window.addEventListener('resize', handleHeaderVisibilityChange);\r\n\r\n    return () => {\r\n      window.removeEventListener('scroll', handleScroll);\r\n      window.removeEventListener('resize', handleHeaderVisibilityChange);\r\n    };\r\n  }, [isHeaderVisible]);\r\n\r\n\r\n  const scrollToTop = () => {\r\n    window.scrollTo({\r\n      top: 0,\r\n      behavior: 'smooth',\r\n    });\r\n  };\r\n\r\n  return (\r\n    <div className={`back-to-top ${isVisible ? 'visible' : ''}`}>\r\n      <FaArrowCircleUp onClick={scrollToTop} />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default BackToTopButton;\r\n", "import React, { useEffect } from 'react';\r\n\r\nfunction Powered() {\r\n  // const [isHidden, setIsHidden] = useState(false);\r\n\r\n  const base_url = process.env.REACT_APP_BASE_URL;\r\n  useEffect(() => {\r\n    const script = document.createElement('script');\r\n    script.src = base_url + 'snippets/com.global.vuzo/js/com.global.vuzo.js';\r\n    script.async = true;\r\n    script.onload = () => {\r\n      // window.displayGooglePlay();\r\n      // window.displayQR();\r\n    };\r\n    document.body.appendChild(script);\r\n  }, [base_url]);\r\n  return (\r\n    <>\r\n      <footer className={`relative hidden md:block md:absolute bottom-[10px] right-[5px] w-full mt-3 py-12 md:py-2 z-[8888] md:hidden`}>\r\n      {/* <footer className=\"relative hidden md:block md:absolute bottom-[10px] right-[5px] w-full mt-3 py-12 md:py-2 z-[8888]\"> */}\r\n\r\n      </footer>\r\n    </>\r\n  );\r\n}\r\n\r\nexport default Powered;\r\n", "import React, { useEffect } from 'react';\r\nimport aiproLogo from '../assets/images/AIPRO.svg';\r\nimport aiproLogoWebP from '../assets/images/AIPRO.webp';\r\nimport { Auth } from '../core/utils/auth';\r\nimport './style.css';\r\n\r\nfunction HeaderLogo({ hideNavLink, auth = Auth() }) {\r\n  const pathname = window.location.pathname;\r\n  const isChatGptGo = /\\/start-chatgpt-go\\/?$/.test(pathname);\r\n  const isTexttoImage = /\\/text-to-image\\/?$/.test(pathname);\r\n  const isChatGptV2 = /\\/start-chatgpt-v2\\/?$/.test(pathname);\r\n  const isRegisterPage = pathname.includes('/register-auth');\r\n  const showLink = !hideNavLink;\r\n  const showMaintenanceBanner = process.env.REACT_APP_ShowMaintenanceBanner || '';\r\n\r\n  useEffect(() => {\r\n    const linkPNG = createPreloadLink(aiproLogo, 'image');\r\n    const linkWebP = createPreloadLink(aiproLogoWebP, 'image');\r\n    document.head.append(linkPNG, linkWebP);\r\n\r\n    if (!isRegisterPage) {\r\n      import('@fortawesome/fontawesome-free/css/all.css');\r\n    }\r\n    return () => {\r\n      linkPNG.remove();\r\n      linkWebP.remove();\r\n    };\r\n  }, [isRegisterPage]);\r\n\r\n\r\n  const createPreloadLink = (href, as) => {\r\n    const link = document.createElement('link');\r\n    link.rel = 'preload';\r\n    link.href = href;\r\n    link.as = as;\r\n    return link;\r\n  };\r\n\r\n  return (\r\n    <>\r\n      {showMaintenanceBanner && (\r\n        <div id=\"maintenance-container\" className=\"p-[5px] bg-[#f7a73f] text-center\">\r\n          We are currently undergoing maintenance. We're expecting to be back at 4 AM EST.\r\n        </div>\r\n      )}\r\n      <header id=\"header\" className={`headernav border-gray-500 bg-white text-black w-3/4 mx-auto rounded-3xl mt-3 py-2 z-[8888] ${showMaintenanceBanner ? 'top-[60px]' : ''}`}>\r\n        <div className=\"container mx-auto flex justify-between items-center px-4\">\r\n          <picture className=\"aiprologo\">\r\n            <source type=\"image/webp\" srcSet={aiproLogoWebP} width=\"150\" height=\"52\" className=\"aiprologo\" />\r\n            <img src={aiproLogo} alt=\"AI-Pro Logo\" className=\"aiprologo\"/>\r\n          </picture>\r\n          {(isChatGptGo || isTexttoImage || isChatGptV2) && showLink && (\r\n            <nav className=\"text-xs lg:text-sm block inline-flex\" id=\"menu\">\r\n              <ul className=\"headnav flex inline-flex\">\r\n                <li className=\"mr-1 md:mr-2 lg:mr-6\">\r\n                  <a href={auth ? '/my-account' : '/login'} className=\"font-bold\" aria-label={auth ? 'my-account' : 'login'}>\r\n                    {auth ? 'My Apps' : 'LOG IN'}\r\n                  </a>\r\n                </li>\r\n              </ul>\r\n            </nav>\r\n          )}\r\n        </div>\r\n      </header>\r\n    </>\r\n  );\r\n}\r\n\r\nexport default HeaderLogo;\r\n", "import React, { useState, useEffect, useRef } from 'react';\r\nimport './text-to-image.css';\r\nimport Header from '../header';\r\nimport Headerlogo from '../header/headerlogo';\r\nimport Footer from '../footer';\r\nimport { Auth } from '../core/utils/auth';\r\nimport img1 from '../assets/images/ai-art-robot.png';\r\nimport 'react-responsive-carousel/lib/styles/carousel.css';\r\nimport { Helmet } from 'react-helmet';\r\nimport { motion } from \"framer-motion\";\r\nimport lpPic1 from '../assets/images/wizard-1.png';\r\nimport BackToTopButton from '../footer/backtotop';\r\nimport { GetCookie } from '../core/utils/cookies';\r\n\r\nfunction Test2Image() {\r\n  const auth = Auth();\r\n  const [navmenu, setNavmenu] = useState('hide');\r\n  const headerRef = useRef(null);\r\n  const [isHeaderVisible, setIsHeaderVisible] = useState(true);\r\n  const hideLinks = GetCookie('qW1eMlya') && GetCookie('qW1eMlya') === 'on' ? true : false;\r\n\r\n  useEffect(() => {\r\n    // checkflag\r\n    const cookies = document.cookie.split(';');\r\n    for (let i = 0; i < cookies.length; i++) {\r\n      const cookie = cookies[i].trim();\r\n      if (cookie.startsWith('navmenu=')) {\r\n        setNavmenu(cookie.substring('navmenu='.length));\r\n        break;\r\n      }\r\n    }\r\n    // end checkflag\r\n\r\n    const handleScroll = () => {\r\n      const headerBottom = headerRef.current.getBoundingClientRect().bottom;\r\n      setIsHeaderVisible(window.pageYOffset < headerBottom);\r\n    };\r\n\r\n    window.addEventListener('scroll', handleScroll);\r\n\r\n    return () => {\r\n      window.removeEventListener('scroll', handleScroll);\r\n    };\r\n\r\n\r\n  }, [auth]);\r\n  \r\n  const iframeSrc = process.env.REACT_APP_TXT2IMG;\r\n\r\n  const checkSubscription = () => {\r\n    if(!(auth)){\r\n      window.location.href = '/register-auth';\r\n    } else if (auth && auth.status === 'active') {\r\n      window.location.href = '/my-account';\r\n    } else {\r\n      window.location.href = '/pricing';\r\n    }\r\n  };\r\n\r\n  if(auth === undefined) return;\r\n  return (\r\n    <>\r\n      <Helmet>\r\n        <meta name=\"robots\" content=\"noindex, nofollow\" />\r\n        <title>AI Pro | Create Art | Visualize Your Words</title>\r\n        <meta name=\"description\" content=\"Introducing our powerful DreamPhoto app. Bring your text to life with beautiful pictures. Explore our tool for creative expression today!\" />\r\n      </Helmet>\r\n      {navmenu === 'show' ? (\r\n        <Header hideNavLink={hideLinks} />\r\n      ) : (\r\n        <Headerlogo hideNavLink={hideLinks} />\r\n      )}\r\n        <div ref={headerRef}></div>\r\n        <BackToTopButton isHeaderVisible={isHeaderVisible} />\r\n        <div className=\"text-to-image bg-gray-100\">\r\n          <div className=\"intro mx-auto block pt-0 pb-10 md:pt-20\">\r\n            <div className=\"mainbanner block md:flex md:flex-row\">\r\n              <div className=\"w-full md:w-2/5 bg-gray-900 text-white text-center mt-20 sm:mt-20 md:mt-0 py-20 sm:py-[140px] md:pt-[165px] lg:pt-[185px] px-8\">\r\n                <h1 className=\"text-3xl md:text-3xl lg:text-4xl mb-4 sm:pt-4 pb-8 font-bold drop-shadow-2xl\">Text-to-Image</h1>\r\n                <p className=\"text-[16px] mb-4\">\r\n                  Generate any kind of image, just by typing out text!<br/> Through AI-PRO's intelligent AI engine,<br/> you can now create stunning, captivating,<br/> and elaborate pictures, in a matter of seconds!\r\n                </p>\r\n              </div>\r\n              <div className=\"w-full h-[600px] md:w-3/5 banner_img\">\r\n                <iframe id=\"createartdemo\" title=\"Create Art Demo\" src={iframeSrc} frameBorder=\"0\">\r\n                </iframe>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"howto container mx-auto block\">\r\n            <div className=\"lg:justify-center text-center\">\r\n              <div className=\"flex flex-wrap text-center md:text-left my-4\">\r\n                <div className=\"w-full lg:w-2/5 xl:w-2/5 p-4\">\r\n                  <h2 className=\"text-3xl md:text-6xl text-blue-700 font-bold\">How to Use<br/> Create Art</h2>\r\n                </div>\r\n                <div className=\"w-full lg:w-1/5 xl:w-1/5 p-4\">\r\n                  <h4 className=\"font-bold text-xl mb-4\">Login to your account</h4>\r\n                  <p>Make sure that you are logged in to your account. You may <a href=\"/register-auth\" className=\"underline\">sign in here</a>.</p>\r\n                </div>\r\n                <div className=\"w-full lg:w-1/5 xl:w-1/5 p-4\">\r\n                  <h4 className=\"font-bold text-xl mb-4\">Describe the Image</h4>\r\n                  <p>Once logged in, you may generate your AI art by describing the image that you want inside the Positive Prompt box.</p>\r\n                </div>\r\n                <div className=\"w-full lg:w-1/5 xl:w-1/5 p-4\">\r\n                  <h4 className=\"font-bold text-xl mb-4\">Experiment & download</h4>\r\n                  <p>Experiment with different prompts. Use the Negative Prompt box to describe the things you don’t want to appear in your image. Click Download to save the image in your device.</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div className=\"lg:flex lg:justify-center\">\r\n              <div className=\"w-full banner_img\">\r\n                <img src={img1} alt=\"bot\" className=\"object-cover w-full shadow\"/>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div className=\"useai mx-auto pt-10 z-1 my-12\">\r\n          <div className=\"flex flex-col items-center p-10 lg:py-16 lg:pb-28\">\r\n            <div className=\"w-full lg:w-1/2 bg-white rounded-xl p-6 text-center mx-4 lg:mx-0\">\r\n              <h2 className=\"text-blue-800 text-xl md:text-5xl font-bold text-center pb-6\">\r\n                Use Artificial Intelligence\r\n              </h2>\r\n              <p className=\"text-md text-black text-center md:p-6 leading-8\">\r\n                At AI-PRO, we believe that everyone should have access to the resources and guidance they need to succeed in the world of AI. That’s why we offer a variety of membership options to suit your needs and budget. Whether you’re an individual looking to learn about AI or a business looking to adopt AI solutions, we have a plan that’s right for you.\r\n              </p>\r\n              <motion.button\r\n                onClick={checkSubscription}\r\n                className=\"cta bg-blue-800 hover:bg-blue-700 mb-1 text-white mx-auto text-center font-bold py-3 px-6 my-3 rounded-2xl md:w-1/2\"\r\n                whileHover={{ scale: 1.1 }}\r\n                whileTap={{ scale: 0.9 }}\r\n              >\r\n                Discover AI Now\r\n              </motion.button>\r\n            </div>\r\n          </div>\r\n          <div className=\"flex flex-col items-center w-full text-center bg-gray-200 h-[400px]\">\r\n            <img src={lpPic1} alt=\"ChatGPT-Now AI\" className=\"mx-auto mt-2 md:mt-[-30px]\" />\r\n          </div>\r\n\r\n        </div>\r\n      <Footer auth={auth} hideNavLink={hideLinks}/>\r\n    </>\r\n  );\r\n}\r\n\r\nexport default Test2Image;\r\n", "// extracted by mini-css-extract-plugin\nexport default {};"], "names": ["_ref", "isHeaderVisible", "isVisible", "setIsVisible", "useState", "useEffect", "handleScroll", "window", "pageYOffset", "handleHeaderVisibilityChange", "addEventListener", "removeEventListener", "_jsx", "className", "children", "FaArrowCircleUp", "onClick", "scrollToTop", "scrollTo", "top", "behavior", "base_url", "process", "script", "document", "createElement", "src", "async", "onload", "body", "append<PERSON><PERSON><PERSON>", "_Fragment", "hideNavLink", "auth", "<PERSON><PERSON>", "pathname", "location", "isChatGptGo", "test", "isTexttoImage", "isChatGptV2", "isRegisterPage", "includes", "showLink", "showMaintenanceBanner", "REACT_APP_ShowMaintenanceBanner", "linkPNG", "createPreloadLink", "aiproLogo", "linkWebP", "aiproLogoWebP", "head", "append", "remove", "href", "as", "link", "rel", "_jsxs", "id", "type", "srcSet", "width", "height", "alt", "navmenu", "setNavmenu", "headerRef", "useRef", "setIsHeaderVisible", "hideLinks", "Get<PERSON><PERSON><PERSON>", "cookies", "cookie", "split", "i", "length", "trim", "startsWith", "substring", "headerBottom", "current", "getBoundingClientRect", "bottom", "iframeSrc", "REACT_APP_TXT2IMG", "undefined", "<PERSON><PERSON><PERSON>", "name", "content", "Header", "Headerlogo", "ref", "BackToTopButton", "title", "frameBorder", "img1", "motion", "button", "checkSubscription", "status", "whileHover", "scale", "whileTap", "lpPic1", "Footer"], "sourceRoot": ""}