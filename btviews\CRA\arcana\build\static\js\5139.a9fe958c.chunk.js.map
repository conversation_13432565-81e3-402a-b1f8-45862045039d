{"version": 3, "file": "static/js/5139.a9fe958c.chunk.js", "mappings": "kGACO,SAASA,IACd,MAAMC,EAAOC,SAASC,cAAc,UACpC,SAAOF,EAAKG,aAAcH,EAAKG,WAAW,QAC2B,IAA5DH,EAAKI,UAAU,cAAcC,QAAQ,kBAGhD,CAgDO,SAASC,EAAQC,GAEtB,OAAW,MAAPA,GAAeA,EAAIC,OAAS,IADf,eAC8BC,KAAKF,GAC5C,WAGRA,EAAMA,EAAIG,MAAM,EAAG,GAEb,IAANC,OAAWJ,GACb,CAGO,SAASK,EAAYL,IAEf,MAAPA,GAAeA,EAAIC,OAAS,IADf,eAC8BC,KAAKF,MAClDA,EAAM,UAGR,IAAIM,EAAIC,SAASP,EAAIG,MAAM,EAAG,GAAI,IAC9BK,EAAID,SAASP,EAAIG,MAAM,EAAG,GAAI,IAC9BM,EAAIF,SAASP,EAAIG,MAAM,EAAG,GAAI,IAElCG,EAAII,KAAKC,IAAI,EAAGD,KAAKE,IAAI,IAAKN,EAAS,IAAJA,IACnCE,EAAIE,KAAKC,IAAI,EAAGD,KAAKE,IAAI,IAAKJ,EAAS,IAAJA,IACnCC,EAAIC,KAAKC,IAAI,EAAGD,KAAKE,IAAI,IAAKH,EAAS,IAAJA,IAEnC,MAAMI,EAASC,GACIJ,KAAKK,MAAMD,GAAOE,SAAS,IAC5BC,SAAS,EAAG,KAG9B,MAAM,IAANb,OAAWS,EAAMP,IAAEF,OAAGS,EAAML,IAAEJ,OAAGS,EAAMJ,GACzC,CAEO,SAASS,IACd,MAAMC,EAAkBC,OAAOC,SAASC,KAExC,OAAIH,EAAgBrB,QAAQ,YAAc,EAC/B,WACAqB,EAAgBrB,QAAQ,QAAU,EAClC,OAGJ,EACT,C,uMC1FIyB,EAAmB,IACnBC,EAAU,GAEP,SAASC,EAAkBC,GAA0H,IAAxH,kBAACC,EAAiB,qBAAEC,EAAoB,mBAAEC,EAAkB,8BAAEC,EAA6B,wBAAEC,GAAwBL,EACvJ,MAAOM,EAAcC,IAAmBC,EAAAA,EAAAA,UAAS,IAC1CC,EAAaC,IAAkBF,EAAAA,EAAAA,UAAS,IACxCG,EAAiBC,IAAsBJ,EAAAA,EAAAA,UAAS,IAEjDK,GAAOC,EAAAA,EAAAA,OAEbC,EAAAA,EAAAA,YAAU,UACKC,IAATH,GAAkC,eAAZA,EAAKI,OAC7BpB,EAAmBgB,EAAKhB,iBAEU,YAA9BgB,EAAKK,SAASC,cAChBP,EAAmB,SAEnBA,EAAmB,WAItB,CAACC,IAGJ,MAOMO,EAA2BA,KAC/B,IAAIC,EAAQrD,SAASsD,eAAe,uBACxB,OAARD,IACFA,EAAME,MAAMC,QAAU,OACtBtB,GAAqB,MAczBa,EAAAA,EAAAA,YAAU,KACRU,IAAAA,QAAiB,CACfC,cAAe,sBAEhB,KAEHX,EAAAA,EAAAA,YAAU,KAERL,EADaJ,EAAeT,KAE3B,CAACS,KAEJS,EAAAA,EAAAA,YAAU,KACRR,EAAgB,GAGhBG,EADaJ,EAAeT,KAG3B,CAACI,IAoBJ,YAPwBe,IAApBf,IAAuD,IAAtBA,GAvDL0B,MAC9B,IAAIN,EAAQrD,SAASsD,eAAe,uBACxB,OAARD,IACFA,EAAME,MAAMC,QAAU,UAqDxBG,QAEsBX,IAApBf,IAAuD,IAAtBA,GACnCmB,KAIAQ,EAAAA,EAAAA,KAAAC,EAAAA,SAAA,CAAAC,UACFF,EAAAA,EAAAA,KAAA,OAAKG,GAAG,sBAAsBC,UAAU,iBAAgBF,UACvDF,EAAAA,EAAAA,KAAA,OAAKK,MAAM,yDAAwDH,UAC9DI,EAAAA,EAAAA,MAAA,OAAAJ,SAAA,EACEI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,0BAAyBF,SAAA,EACtCF,EAAAA,EAAAA,KAAA,OAAKI,UAAU,uDAAsDF,SAAC,sBAGtEF,EAAAA,EAAAA,KAAA,OAAKI,UAAU,wCAAuCF,UACpDF,EAAAA,EAAAA,KAAA,QAAMK,MAAM,QAAQE,QAASA,IAAKf,IAA2BU,SAAC,YAGlEI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gDAA+CF,SAAA,EAC5DF,EAAAA,EAAAA,KAAA,OAAAE,SAAK,gEACLF,EAAAA,EAAAA,KAAA,OAAAE,SAAK,kDACLI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mBAAkBF,SAAA,EAC/BF,EAAAA,EAAAA,KAAA,QAAMI,UAAU,uDAAsDF,SAAC,SACvEI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,kCAAiCF,SAAA,EAC9CF,EAAAA,EAAAA,KAAA,SAAOI,UAAU,8DAA8DI,KAAK,SAAShD,MAAM,IAAI+C,QAASA,KA7DxH7B,EAAa,GACfC,EAAgBD,EAAa,OA6DnBsB,EAAAA,EAAAA,KAAA,QAAMI,UAAU,sCAAqCF,SAAExB,KACvDsB,EAAAA,EAAAA,KAAA,SAAOI,UAAU,8DAA8DI,KAAK,SAAShD,MAAM,IAAI+C,QAASA,KAnE5H5B,EAAgBD,EAAa,UAqEnBsB,EAAAA,EAAAA,KAAA,QAAMI,UAAU,uDAAsDF,SAAC,iBAEzEI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oBAAmBF,SAAA,CAAC,kBACjBrB,MAElByB,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oBAAmBF,SAAA,CAAC,OAC5BnB,MAEPiB,EAAAA,EAAAA,KAAA,OAAAE,UACEF,EAAAA,EAAAA,KAACS,EAAAA,EAAOC,OAAM,CACdN,UAAU,oFACVO,WAAY,CAAEC,gBAAiB,WAC/BC,SAAU,CAAEC,MAAO,IACnBP,QAASA,KArDnBzC,OAAOC,SAASC,KAAO,gBAAgBU,GAqDKwB,SACjC,6BAUf,CAEO,SAASa,EAAsBC,GAAiG,IAA/F,gBAACC,EAAe,2BAAEC,EAA0B,wBAAEzC,EAAuB,qBAAE0C,GAAqBH,EAElI,MAAM/B,GAAOC,EAAAA,EAAAA,OAEbC,EAAAA,EAAAA,YAAU,UACKC,IAATH,GAAkC,eAAZA,EAAKI,OAC7BnB,EAAUe,EAAKf,WAEhB,CAACe,IAEJ,MAOMmC,EAAaA,KACjB,IAAI3B,EAAQrD,SAASsD,eAAe,iBACxB,OAARD,IACFA,EAAME,MAAMC,QAAU,OACtBnB,GAAwB,KA8C5B,YAP2BW,IAAvB+B,IAA6D,IAAzBA,GAlDtBE,MAChB,IAAI5B,EAAQrD,SAASsD,eAAe,iBACxB,OAARD,IACFA,EAAME,MAAMC,QAAU,UAgDxByB,QAEyBjC,IAAvB+B,IAA6D,IAAzBA,GACtCC,KAIApB,EAAAA,EAAAA,KAAAC,EAAAA,SAAA,CAAAC,UACAF,EAAAA,EAAAA,KAAA,OAAKG,GAAG,gBAAgBC,UAAU,iBAAgBF,UAChDI,EAAAA,EAAAA,MAAA,OAAKD,MAAM,4EAA2EH,SAAA,EACpFF,EAAAA,EAAAA,KAAA,QAAMK,MAAM,QAAQE,QAASA,IAAKa,IAAalB,SAAC,OAChDF,EAAAA,EAAAA,KAAA,OAAKI,UAAU,sCAAqCF,UAACF,EAAAA,EAAAA,KAAA,OAAKsB,IAAKC,EAAAA,EAAWC,IAAI,cAAcpB,UAAU,yBACtGE,EAAAA,EAAAA,MAAA,MAAIF,UAAU,qEAAoEF,SAAA,CAAC,mBAAeF,EAAAA,EAAAA,KAAA,SAAK,2BACvGA,EAAAA,EAAAA,KAAA,OAAKI,UAAU,yCAAwCF,SAAC,kEACxDF,EAAAA,EAAAA,KAAA,OAAKI,UAAU,yCAAwCF,SAAC,sFACxDI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMF,SAAA,EACnBI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wBAAuBF,SAAA,CAAC,mBAAiBe,MACxDX,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wBAAuBF,SAAA,CAAC,wBAAsBgB,SAE/DZ,EAAAA,EAAAA,MAAA,OAAKF,UAAU,+DAA8DF,SAAA,EAC3EF,EAAAA,EAAAA,KAAA,OAAKI,UAAU,YAAWF,SAAC,sBAC3BF,EAAAA,EAAAA,KAAA,OAAKI,UAAU,4EAA4EG,QAASA,KAAKkB,OAnD3GC,EAAUT,EACVU,EAAeT,EACfU,EAAG,GAAA9E,OAAM+E,4BAA6B,mCAE1CT,IACAhF,SAAS0F,cAAc,qBAAqBC,UAAUC,IAAI,eAE1DC,EAAAA,EAAMC,KAAKN,EAAK,CACdF,UACAC,gBACC,CAAEQ,QAAS,CAAE,eAAgB,uCAAyCC,MAAK,SAASC,GACrF,IAAIC,EAASD,EAAIE,KAEjBnG,SAAS0F,cAAc,qBAAqBC,UAAUS,OAAO,UAE1DF,EAAOG,QACR5C,IAAAA,QAAe,iBAAiByC,EAAOC,MAEvC1C,IAAAA,MAAa,gBAEjB,IAAG6C,OAAM,SAAUC,GACjBvG,SAAS0F,cAAc,qBAAqBC,UAAUS,OAAO,UACzDG,EAAMC,UAAoC,MAAxBD,EAAMC,SAASC,SACnCzG,SAAS0F,cAAc,qBAAqBC,UAAUS,OAAO,UAC7D3C,IAAAA,MAAa,wDAEjB,IA3BmB4B,IACfC,EACAC,EACAC,GAiD0H1B,SAAC,oBACzHI,EAAAA,EAAAA,MAAA,OAAAJ,SAAA,EAAKF,EAAAA,EAAAA,KAAA,QAAMI,UAAU,YAAWF,SAAC,iBAAmB,uCACpDI,EAAAA,EAAAA,MAAA,OAAAJ,SAAA,EAAKF,EAAAA,EAAAA,KAAA,QAAMI,UAAU,YAAWF,SAAC,WAAa,gBAC9CI,EAAAA,EAAAA,MAAA,OAAAJ,SAAA,EAAKF,EAAAA,EAAAA,KAAA,QAAMI,UAAU,YAAWF,SAAC,eAAiB,uBAClDI,EAAAA,EAAAA,MAAA,OAAAJ,SAAA,EAAKF,EAAAA,EAAAA,KAAA,QAAMI,UAAU,YAAWF,SAAC,oBAAsB,iBACvDI,EAAAA,EAAAA,MAAA,OAAAJ,SAAA,EAAKF,EAAAA,EAAAA,KAAA,QAAMI,UAAU,YAAWF,SAAC,yCAA2C,iBAC5EI,EAAAA,EAAAA,MAAA,OAAAJ,SAAA,EAAKF,EAAAA,EAAAA,KAAA,QAAMI,UAAU,YAAWF,SAAC,oBAAsB,sBACvDI,EAAAA,EAAAA,MAAA,OAAAJ,SAAA,EAAKF,EAAAA,EAAAA,KAAA,QAAMI,UAAU,YAAWF,SAAC,qBAAuB,IAAEhC,MAC1DoC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,kDAAiDF,SAAA,EAACF,EAAAA,EAAAA,KAAC8C,EAAAA,IAAY,CAAC1C,UAAU,wBAAuB,0HAGlHJ,EAAAA,EAAAA,KAAA,OAAKI,UAAU,8CAA6CF,SAAC,8IAG7DI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCF,SAAA,CAAC,iBACzCF,EAAAA,EAAAA,KAAA,KAAAE,SAAG,qBAAoB,kDAEtCF,EAAAA,EAAAA,KAAA,OAAKI,UAAU,cAAaF,UAC1BF,EAAAA,EAAAA,KAACS,EAAAA,EAAOC,OAAM,CACZN,UAAU,sEACVO,WAAY,CAAEC,gBAAiB,WAC/BC,SAAU,CAAEC,MAAO,IACnBP,QA/EuBwC,KAC/BjF,OAAOC,SAASC,KAAO,sBA8EmBkC,SACnC,sCAQX,C,4hJC5PA,MAuCA,EAvCgB9B,IAAyB,IAAxB,eAAE4E,GAAgB5E,EACjC,MAuBQ6E,KAAMC,EAAW,QAAEC,EAAO,UAAEC,GAvBjBC,MACjB,OAAOL,GACL,IAAK,WACH,MAAO,CACLC,KAAMK,EACNH,SAAS7C,EAAAA,EAAAA,MAAAL,EAAAA,SAAA,CAAAC,SAAA,EAAEF,EAAAA,EAAAA,KAAA,QAAMI,UAAU,iBAAgBF,SAAC,aAAe,cAC3DkD,UAAW,yHAEf,IAAK,OACH,MAAO,CACLH,KAAMM,EACNJ,SAAS7C,EAAAA,EAAAA,MAAAL,EAAAA,SAAA,CAAAC,SAAA,EAAEF,EAAAA,EAAAA,KAAA,QAAMI,UAAU,iBAAgBF,SAAC,aAAe,cAC3DkD,UAAW,2HAEf,QACE,MAAO,CACLH,KAAMA,EACNE,SAAS7C,EAAAA,EAAAA,MAAAL,EAAAA,SAAA,CAAAC,SAAA,CAAE,eAAWF,EAAAA,EAAAA,KAAA,QAAMI,UAAU,iBAAgBF,SAAC,YACvDkD,UAAW,6CAK+BC,GAElD,OACI/C,EAAAA,EAAAA,MAAA,OAAKF,UAAS,gEAAAtD,OAAqF,QAAnBkG,EAA2B,QAAU,IAAK9C,SAAA,EACxHF,EAAAA,EAAAA,KAAA,OAAKI,UAAS,gDAAAtD,OAAqE,aAAnBkG,GAAoD,SAAnBA,EAA4B,mCAAqC,kCAAmC9C,UACpMF,EAAAA,EAAAA,KAAA,OAAKsB,IAAK4B,EAAa1B,IAAI,mBAE5BxB,EAAAA,EAAAA,KAAA,MAAII,UAAU,sGAAqGF,SAChHiD,KAEHnD,EAAAA,EAAAA,KAAA,KAAGI,UAAS,2DAAAtD,OAAgF,aAAnBkG,GAAoD,SAAnBA,EAA4B,gBAAkB,IAAK9C,SAAEkD,Q,eCpCvK,MAmFA,EAnFkBhF,IAAyE,IAAxE,YAAEoF,EAAW,eAAEC,EAAc,eAAET,EAAc,kBAAEU,GAAmBtF,EACnF,MAAMuF,GAAWC,EAAAA,EAAAA,QAAO,MAGlBC,EAAgBA,IACb,mBAAmBjH,KAAKkH,UAAUC,WA4C3C,OACEzD,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gCAA+BF,SAAA,EAC5CF,EAAAA,EAAAA,KAAA,OAAKI,UAAU,sEAAqEF,SACjFsD,GACCxD,EAAAA,EAAAA,KAAA,UAAQO,QAASA,IAAMkD,EAAe,IAAKrD,UAAU,oEAAmEF,UACtHF,EAAAA,EAAAA,KAACgE,EAAAA,IAAc,CAACC,KAAM,GAAI7D,UAAU,yCAGtCE,EAAAA,EAAAA,MAAA,OAAK4D,MAAM,KAAKC,OAAO,KAAKC,QAAQ,YAAYC,KAAK,OAAOC,MAAM,6BAA4BpE,SAAA,EAC5FF,EAAAA,EAAAA,KAAA,QAAMuE,EAAE,UAAUL,MAAM,KAAKC,OAAO,KAAKK,GAAG,KAAKH,KAAK,kCACtDrE,EAAAA,EAAAA,KAAA,QAAMyE,SAAS,UAAUC,SAAS,UAAUC,EAAE,sjBAAsjBN,KAAK,WACzmBrE,EAAAA,EAAAA,KAAA,QAAAE,UACEI,EAAAA,EAAAA,MAAA,kBAAgBH,GAAG,yBAAyByE,GAAG,UAAUC,GAAG,KAAKC,GAAG,QAAQC,GAAG,KAAKC,cAAc,iBAAgB9E,SAAA,EAChHF,EAAAA,EAAAA,KAAA,QAAMiF,UAAU,aAChBjF,EAAAA,EAAAA,KAAA,QAAMkF,OAAO,IAAID,UAAU,uBAMrCjF,EAAAA,EAAAA,KAAA,SACEmF,IAAKxB,EACLnD,KAAK,OACL4E,YAAY,iBACZ5H,MAAOgG,EACP6B,SAlEsBC,IAC1B,MAAM9H,EAAQ8H,EAAEC,OAAO/H,MACvBiG,EAAejG,GAIXA,GAA4B,YAAnBwF,IAAiCa,MAC5CH,EAAkB,OAElB8B,YAAW,KACT,MAAMC,EAAcrJ,SAAS0F,cAAc,gBACvC2D,GACFA,EAAYC,UAEb,KAqDDC,UAjDiBL,IACP,UAAVA,EAAEM,KAAmBpC,IAEnBK,IACqB,YAAnBb,GACFU,EAAkB,OAQtBD,EAAeD,KAqCbjD,QAjCoB+E,IAExBA,EAAEO,iBACFP,EAAEQ,mBA+BE1F,UAAU,4KCnBlB,EAtDmBhC,IAAqF,IAApF,WAAE2H,EAAU,eAAE/C,EAAc,kBAAEU,EAAiB,YAAEF,EAAW,eAAEC,GAAgBrF,EAChG,MAOM4H,EAAoBD,GAPD,CACvB,CAAE5F,GAAI,MAAO8F,KAAM,WAAYC,KAAMjD,GACrC,CAAE9C,GAAI,UAAW8F,KAAM,eAAgBC,K,0iEACvC,CAAE/F,GAAI,WAAY8F,KAAM,cAAeC,KAAM5C,GAC7C,CAAEnD,GAAI,OAAQ8F,KAAM,UAAWC,KAAM3C,IAYvC,OACEjD,EAAAA,EAAAA,MAAA,OAAKF,UAAS,kDAAAtD,OAAqD0G,EAAmB,QAAL,GAAY,KAAA1G,OAAuB,YAAnBkG,EAA+B,GAAK,QAAO,qJAAoJ9C,SAAA,CAC7R8F,EAAkBG,KAAKC,IACtB9F,EAAAA,EAAAA,MAAA,UAEEC,QAASA,KAAM8F,MAXF,SADQC,EAYcF,EAASjG,KAVhDsD,EAAe,SAEjBC,EAAkB4C,GAJSA,OAarBlG,UAAS,6FAAAtD,OACHkG,IAAmBoD,EAASjG,GACxB,wEACA,6DACHD,SAAA,EAEPF,EAAAA,EAAAA,KAAA,OACEsB,IAAK8E,EAASF,KACd1E,IAAK4E,EAASH,KACd7F,UAAS,+BAAAtD,OACPkG,IAAmBoD,EAASjG,GAC1B,0BACA,MAGLiG,EAASH,OAjBLG,EAASjG,OAqBlBH,EAAAA,EAAAA,KAAA,OAAKI,UAAU,GAAEF,UACfF,EAAAA,EAAAA,KAACuG,EAAS,CACR/C,YAAaA,EACbC,eAAgBA,EAChBT,eAAgBA,EAChBU,kBAAmBA,U,eCpD7B,MAIA,EAJiBtF,IAAe,IAAd,KAAEoC,GAAMpC,EACtB,OAAO4B,EAAAA,EAAAA,KAAA,OAAKsB,IAAKd,EAAMgB,IAAI,YAAYpB,UAAU,0CCwErD,EAtEiBhC,IAA0E,IAAzE,KAAEoI,EAAI,UAAEC,EAAS,eAAEzD,EAAc,cAAE0D,EAAa,gBAAEC,GAAiBvI,EACnF,GAAIqI,EACF,OACEzG,EAAAA,EAAAA,KAAA,OAAKI,UAAU,gHAWnB,OACEJ,EAAAA,EAAAA,KAACS,EAAAA,EAAOmG,EAAC,CAEPxG,UAAU,iYACVyG,QAAS,CAAEC,QAAS,GACpBC,QAAS,CAAED,QAAS,GACpBE,KAAM,CAAEF,QAAS,GACjBG,WAAY,CAAEC,SAAU,GAAKC,KAAM,aACnCxG,WAAY,CAAEG,MAAO,MACrBD,SAAU,CAAEC,MAAO,KACnB,aAAY0F,EAAKP,KACjBjI,KAAM2I,EAAgBH,EAAKY,MAC3B7B,OAAQoB,EAAgBH,EAAKY,QAAUZ,EAAKY,KAAO,SAAW,GAC9D7G,QAnBgB8G,KACdb,EAAKY,MACPV,EAAcF,EAAKY,OAiBElH,UAErBI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,6DAA4DF,SAAA,EACzEF,EAAAA,EAAAA,KAAA,OAAKI,UAAU,gBAAeF,UAC5BF,EAAAA,EAAAA,KAACsH,EAAQ,CAAC9G,KAAMgG,EAAKN,UAEvB5F,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iGAAgGF,SAAA,EAC7GI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oBAAmBF,SAAA,EAChCF,EAAAA,EAAAA,KAAA,MAAII,UAAU,iHAAgHF,SAAEsG,EAAKP,OACpIO,EAAKe,YACJjH,EAAAA,EAAAA,MAAA,OAAKF,UAAU,4RAA2RF,SAAA,EACxSF,EAAAA,EAAAA,KAAA,OAAKI,UAAU,cAAaF,UAC1BF,EAAAA,EAAAA,KAAA,OAAKkE,MAAM,KAAKC,OAAO,KAAKC,QAAQ,YAAYC,KAAK,OAAOC,MAAM,6BAA4BpE,UAC5FF,EAAAA,EAAAA,KAAA,QAAM2E,EAAE,iJAAiJN,KAAK,eAGlKrE,EAAAA,EAAAA,KAAA,QAAAE,SAAM,kBAGNF,EAAAA,EAAAA,KAAA,OAAKI,UAAU,gDAGlBoG,EAAKgB,QACJlH,EAAAA,EAAAA,MAAA,OAAKF,UAAU,4TAA2TF,SAAA,EACxUF,EAAAA,EAAAA,KAAA,OAAKI,UAAU,cAAaF,UAC1BF,EAAAA,EAAAA,KAAA,OAAKkE,MAAM,KAAKC,OAAO,KAAKC,QAAQ,YAAYC,KAAK,OAAOC,MAAM,6BAA4BpE,UAC5FF,EAAAA,EAAAA,KAAA,QAAM,YAAU,UAAU,YAAU,UAAU2E,EAAE,06GAA06GN,KAAK,eAGj+GrE,EAAAA,EAAAA,KAAA,QAAAE,SAAM,SAGNF,EAAAA,EAAAA,KAAA,OAAKI,UAAU,oDAIrBJ,EAAAA,EAAAA,KAAA,KAAGI,UAAU,wCAAuCF,SAAEsG,EAAKiB,qBA/C5DzE,I,eCbX,MAsFM0E,EAAc1G,IAAmD,IAAlD,KAAER,EAAI,MAAEmH,EAAK,eAAE3E,EAAc,YAAEQ,GAAaxC,EAC/D,OACEwC,EACE,MAEAxD,EAAAA,EAAAA,KAACS,EAAAA,EAAOmH,IAAG,CACTxH,UAAU,yBAAwBF,SAExB,SAATM,GAAkBR,EAAAA,EAAAA,KAAA,OAAKsB,IAAKgC,EAAQ9B,IAAI,iBAA6B,QAAThB,GAAiBR,EAAAA,EAAAA,KAAA,OAAKsB,IAAKiC,EAAO/B,IAAI,aAAgB,QAM3H,EApGqBpD,IAAsF,IAArF,MAAEyJ,EAAK,KAAE3B,EAAI,UAAE4B,EAAS,MAAEC,EAAK,YAAEvE,EAAW,UAAEiD,EAAS,eAAEzD,EAAc,KAAE/D,GAAMb,EAEnG,MAAMsI,EAAiB9E,KACrBoG,EAAAA,EAAAA,IAAY/I,EAAM2C,IAGd+E,EAAmB/E,IAChBqG,EAAAA,EAAAA,IAAchJ,EAAM2C,GAGvBsG,EAAgBH,EAAMI,QAAQ3B,GAClCA,EAAKP,KAAK1G,cAAc6I,SAAS5E,EAAYjE,gBAC7CiH,EAAKiB,YAAYlI,cAAc6I,SAAS5E,EAAYjE,iBAIhD8I,EAAoB,CACxBC,OAAQ,CAAExB,QAAS,GACnByB,QAAS,CACPzB,QAAS,IAIP0B,EAAe,CACnBF,OAAQ,CAAExB,QAAS,GACnByB,QAAS,CACPzB,QAAS,EACTG,WAAY,CAAEC,SAAU,MAItBuB,IAAmBjF,GAAwC,QAAnBR,EAE9C,OACE1C,EAAAA,EAAAA,MAACG,EAAAA,EAAOmH,IAAG,CAETxH,UAAU,OACVyG,QAAQ,SACRE,QAAQ,UACR2B,SAAUL,EAAkBnI,SAAA,CAE3BuI,IACCnI,EAAAA,EAAAA,MAACG,EAAAA,EAAOmH,IAAG,CACTxH,UAAS,qBAAAtD,OAAwB0G,EAAiC,OAAnB,iBAAyB,yBACxEkF,SAAUF,EAAatI,SAAA,EAEvBF,EAAAA,EAAAA,KAAC0H,EAAW,CAAClH,KAAM0F,EAAMyB,MAAOG,EAAW9E,eAAgBA,EAAgBQ,YAAaA,KACxFxD,EAAAA,EAAAA,KAAA,MAAII,UAAS,iCAAAtD,OAAmC0G,EAAc,cAAgB,eAAgBtD,SAC3FsD,GACCxD,EAAAA,EAAAA,KAAAC,EAAAA,SAAA,CAAAC,UACEF,EAAAA,EAAAA,KAAA,QAAMI,UAAU,iBAAgBF,SAAE2H,MAGpCA,OAMPK,EAAcvL,OAAS,GACtBqD,EAAAA,EAAAA,KAACS,EAAAA,EAAOmH,IAAG,CAETxH,UAAU,+FACVsI,SAAUL,EAAkBnI,SAE3BgI,EAAc/B,KAAKK,IAClBxG,EAAAA,EAAAA,KAACS,EAAAA,EAAOmH,IAAG,CAETc,SAAUF,EAAatI,UAEvBF,EAAAA,EAAAA,KAAC2I,EAAQ,CAACnC,KAAMA,EAAMC,UAAWA,EAAWzD,eAAgBA,EAAgB0D,cAAeA,EAAeC,gBAAiBA,KAHtHH,EAAKrG,IAAMqG,EAAKP,SANpBjD,IAcP1C,EAAAA,EAAAA,MAACG,EAAAA,EAAOmI,EAAC,CACPF,SAAUF,EACVpI,UAAU,4BAA2BF,SAAA,CACtC,yBACwBsD,EAAY,SA5ClCR,I,uDC3BX,EAbgB6F,KAEZvI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mBAAkBF,SAAA,EAC/BF,EAAAA,EAAAA,KAAA,OAAKI,UAAU,mDAAkDF,UAChEF,EAAAA,EAAAA,KAAA,OAAKsB,IAAKwH,EAAStH,IAAI,yBAExBxB,EAAAA,EAAAA,KAAA,KAAGI,UAAU,kEAAiEF,SAAC,gF,+zPCerF,MAAM6I,EAAY,CACdC,QAAQ,WAADlM,QAAac,EAAAA,EAAAA,MAAmB,2BACvCqL,QAAQ,WAADnM,QAAac,EAAAA,EAAAA,MAAmB,uBACvCsL,UAAU,WAADpM,QAAac,EAAAA,EAAAA,MAAmB,yBACzCuL,gBAAgB,WAADrM,QAAac,EAAAA,EAAAA,MAAmB,4CAC/CwL,WAAW,WAADtM,QAAac,EAAAA,EAAAA,MAAmB,uBAC1CyL,SAAS,WAADvM,QAAac,EAAAA,EAAAA,MAAmB,oCACxC0L,aAAa,WAADxM,QAAac,EAAAA,EAAAA,MAAmB,oCAC5C2L,cAAc,WAADzM,QAAac,EAAAA,EAAAA,MAAmB,+CAC7C4L,SAAS,WAAD1M,QAAac,EAAAA,EAAAA,MAAmB,sBACxC6L,SAAS,WAAD3M,QAAac,EAAAA,EAAAA,MAAmB,wBACxC8L,QAAQ,WAAD5M,QAAac,EAAAA,EAAAA,MAAmB,uBACvC+L,SAAS,WAAD7M,QAAac,EAAAA,EAAAA,MAAmB,sBACxCgM,SAAS,WAAD9M,QAAac,EAAAA,EAAAA,MAAmB,qBACxCiM,QAAQ,WAAD/M,QAAac,EAAAA,EAAAA,MAAmB,8BACvCkM,YAAY,WAADhN,QAAac,EAAAA,EAAAA,MAAmB,2BAC3CmM,aAAa,WAADjN,QAAac,EAAAA,EAAAA,MAAmB,6BAC5CoM,SAAS,WAADlN,QAAac,EAAAA,EAAAA,MAAmB,uBACxCqM,YAAY,WAADnN,QAAac,EAAAA,EAAAA,MAAmB,2BAC3CsM,UAAU,WAADpN,QAAac,EAAAA,EAAAA,MAAmB,yBACzCuM,KAAK,WAADrN,QAAac,EAAAA,EAAAA,MAAmB,qBAGlCwM,EAAmB,CACrB,CAAEjK,GAAI,cAAe8F,KAAM,cAAewB,YAAa,4BAA6BvB,KAAMmE,EAASzE,IAAK,UAAWQ,SAAU,WAAYmB,WAAW,EAAMC,OAAO,GACjK,CAAErH,GAAI,YAAa8F,KAAM,aAAcwB,YAAa,0CAA2CvB,KAAMoE,EAAe1E,IAAK,YAAaQ,SAAU,WAAYmB,WAAW,EAAOC,OAAO,GACrL,CAAErH,GAAI,UAAW8F,KAAM,UAAWwB,YAAa,0CAA2CvB,KAAMqE,EAAa3E,IAAK,UAAWQ,SAAU,WAAYmB,WAAW,EAAOC,OAAO,GAC5K,CAAErH,GAAI,aAAc8F,KAAM,aAAcwB,YAAa,kCAAmCvB,KAAMsE,EAAa5E,IAAK,kBAAmBQ,SAAU,WAAYmB,WAAW,EAAOC,OAAO,GAClL,CAAErH,GAAI,aAAc8F,KAAM,aAAcwB,YAAa,mCAAoCvB,KAAMuE,EAAa7E,IAAK,aAAcQ,SAAU,WAAYmB,WAAW,EAAOC,OAAO,GAC9K,CAAErH,GAAI,WAAY8F,KAAM,WAAYwB,YAAa,yCAA0CvB,KAAMwE,EAAW9E,IAAK,WAAYQ,SAAU,WAAYmB,WAAW,EAAOC,OAAO,GAC5K,CAAErH,GAAI,eAAgB8F,KAAM,eAAgBwB,YAAa,0CAA2CvB,KAAMyE,EAAY/E,IAAK,eAAgBQ,SAAU,WAAYmB,WAAW,EAAOC,OAAO,GAC1L,CAAErH,GAAI,gBAAiB8F,KAAM,gBAAiBwB,YAAa,0CAA2CvB,KAAM0E,EAAehF,IAAK,gBAAiBQ,SAAU,WAAYmB,WAAW,EAAOC,OAAO,GAChM,CAAErH,GAAI,YAAa8F,KAAM,YAAawB,YAAa,yCAA0CvB,KAAM2E,EAAYjF,IAAK,WAAYQ,SAAU,WAAYmB,WAAW,EAAOC,OAAO,GAC/K,CAAErH,GAAI,aAAc8F,KAAM,aAAcwB,YAAa,kCAAmCvB,KAAM4E,EAAelF,IAAK,WAAYQ,SAAU,WAAYmB,WAAW,EAAOC,OAAO,GAC7K,CAAErH,GAAI,UAAW8F,KAAM,UAAWwB,YAAa,qCAAsCvB,KAAM6E,EAAanF,IAAK,UAAWQ,SAAU,WAAYmB,WAAW,EAAOC,OAAO,GACvK,CAAErH,GAAI,YAAa8F,KAAM,YAAawB,YAAa,uBAAwBvB,KAAM8E,EAAYpF,IAAK,WAAYQ,SAAU,WAAYmB,WAAW,EAAOC,OAAO,GAC7J,CAAErH,GAAI,gBAAiB8F,KAAM,gBAAiBwB,YAAa,0BAA2BvB,KAAM+E,EAAcrF,IAAK,WAAYQ,SAAU,WAAYmB,WAAW,EAAOC,OAAO,GAC1K,CAAErH,GAAI,cAAe8F,KAAM,cAAewB,YAAa,kCAAmCvB,KAAMgF,EAAgBtF,IAAK,UAAWQ,SAAU,OAAQmB,WAAW,EAAOC,OAAO,GAC3K,CAAErH,GAAI,cAAe8F,KAAM,cAAewB,YAAa,8BAA+BvB,KAAMiF,EAAcvF,IAAK,cAAeQ,SAAU,OAAQmB,WAAW,EAAOC,OAAO,GACzK,CAAErH,GAAI,gBAAiB8F,KAAM,gBAAiBwB,YAAa,mCAAoCvB,KAAMkF,EAAkBxF,IAAK,eAAgBQ,SAAU,OAAQmB,WAAW,EAAOC,OAAO,GACvL,CAAErH,GAAI,oBAAqB8F,KAAM,oBAAqBwB,YAAa,mCAAoCvB,KAAMmF,EAAsBzF,IAAK,WAAYQ,SAAU,OAAQmB,WAAW,EAAOC,OAAO,GAC/L,CAAErH,GAAI,eAAgB8F,KAAM,eAAgBwB,YAAa,4BAA6BvB,KAAMoF,EAAiB1F,IAAK,cAAeQ,SAAU,OAAQmB,WAAW,EAAOC,OAAO,GAC5K,CAAErH,GAAI,aAAc8F,KAAM,aAAcwB,YAAa,qCAAsCvB,KAAMqF,EAAe3F,IAAK,YAAaQ,SAAU,OAAQmB,WAAW,EAAOC,OAAO,GAC7K,CAAErH,GAAI,gBAAiB8F,KAAM,gBAAiBwB,YAAa,+BAAgCvB,KAAMsF,EAAkB5F,IAAK,OAAQQ,SAAU,OAAQmB,WAAW,EAAOC,OAAO,I,kiBCxC/K,MACA,GAAe,IAA0B,qECDzC,MACA,GAAe,IAA0B,sECDzC,MACA,GAAe,IAA0B,uE,yDCiJzC,GAtJoBpJ,IAAgE,IAA/D,MAAE2J,EAAK,cAAErB,EAAa,gBAAEC,EAAe,eAAE3D,GAAgB5E,EAE1E,MAAMqN,EAAW,CACb,CACIxF,KAAM,SACNwB,YAAa,mBACbiE,QAASC,EACT1I,KAAM2I,IAEV,CACI3F,KAAM,cACNwB,YAAa,4BACbiE,QAASG,EACT5I,KAAM6I,IAEV,CACI7F,KAAM,WACNwB,YAAa,uBACbiE,QAASK,EACT9I,KAAM+I,KAIR3D,EAAoB,CACtBC,OAAQ,CAAExB,QAAS,GACnByB,QAAS,CAAEzB,QAAS,IAGlB0B,EAAe,CACjBF,OAAQ,CAAExB,QAAS,GACnByB,QAAS,CAAEzB,QAAS,IAKlBmF,EAAWlE,EAAMmE,QAAO,CAACC,EAAKC,KAChCD,EAAIC,EAAIjM,IAAMiM,EACPD,IACR,CAAC,GAEEE,EAAc,CAChB,CAAElM,GAAI,cAAe8C,K,6yJAAe3B,IAAKgL,EAAWrG,KAAM,cAAesG,KAAM,6BAC/E,CAAEpM,GAAI,cAAe8C,K,y2PAAkB3B,IAAKkL,EAAcvG,KAAM,cAAesG,KAAM,gCACrF,CAAEpM,GAAI,UAAW8C,K,6oPAAe3B,IAAKmL,EAAWxG,KAAM,UAAWsG,KAAM,kCACvE,CAAEpM,GAAI,gBAAiB8C,KAAMkH,EAAM7I,IAAKoL,EAAQzG,KAAM,gBAAiBsG,KAAM,gCAC7E,CAAEpM,GAAI,aAAc8C,K,6qUAAiB3B,IAAKqL,GAAa1G,KAAM,aAAcsG,KAAM,oCACjF,CAAEpM,GAAI,eAAgB8C,KAAM2J,GAAYtL,IAAKuL,EAAe5G,KAAM,eAAgBsG,KAAM,+BAItFO,EAjBe,CAAC,cAAe,cAAe,UAAW,gBAAiB,aAAc,gBAiBjE3G,KAAIhG,IAAE,IAAA4M,EAAA,OAAAC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAC5BX,EAAYY,MAAKb,GAAOA,EAAIjM,KAAOA,KAAG,IACzCiH,MAAkB,QAAZ2F,EAAAd,EAAS9L,UAAG,IAAA4M,OAAA,EAAZA,EAAc3F,OAAQ,SAIhC,OACI9G,EAAAA,EAAAA,MAACG,EAAAA,EAAOmH,IAAG,CACPxH,UAAS,2CAAAtD,OAAgE,YAAnBkG,EAA+B,QAAU,IAC/F6D,QAAQ,SACRE,QAAQ,UACR2B,SAAUL,EAAkBnI,SAAA,EAE5BF,EAAAA,EAAAA,KAACS,EAAAA,EAAOmH,IAAG,CACXxH,UAAU,yJACVsI,SAAUL,EACVxB,QAAQ,SACRE,QAAQ,UAAS7G,SAEZuL,EAAStF,KAAI,CAAC+G,EAAOC,KAAW,IAADC,EAC5B,MAAMhG,GAA8B,QAAvBgG,EAAAnB,EAAS,sBAAc,IAAAmB,OAAA,EAAvBA,EAAyBhG,OAAQ,IAC9C,OACApH,EAAAA,EAAAA,KAACS,EAAAA,EAAOmG,EAAC,CAEL5I,KAAM2I,EAAgBS,GACtB7B,OAAQoB,EAAgBS,KAAUA,EAAO,SAAW,GACpD7G,QAASA,IAAM6G,GAAQV,EAAcU,GACrCiG,IAAI,sBACJjN,UAAS,6LAAAtD,OAAyM,IAAVqQ,EAAc,yDAAqE,IAAVA,EAAc,iBAAmB,GAAE,KAAArQ,OAAc,IAAVqQ,EAAc,kBAAoB,IAC1VzE,SAAUF,EAAatI,UAE3BI,EAAAA,EAAAA,MAAA,OAAAJ,SAAA,EACIF,EAAAA,EAAAA,KAAA,OAAKI,UAAU,yBAAwBF,UACnCF,EAAAA,EAAAA,KAAA,OAAKsB,IAAK4L,EAAMxB,QAASlK,IAAG,GAAA1E,OAAKoQ,EAAMjH,KAAI,eAAe7F,UAAU,YAExEE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCF,SAAA,EAC9CI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BF,SAAA,EACxCF,EAAAA,EAAAA,KAAA,OAAKsB,IAAK4L,EAAMjK,KAAMzB,IAAG,GAAA1E,OAAKoQ,EAAMjH,KAAI,SAAS7F,UAAU,iBAC3DE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,uBAAsBF,SAAA,EACjCF,EAAAA,EAAAA,KAAA,MAAII,UAAU,qCAAoCF,SAAEgN,EAAMjH,QAC1DjG,EAAAA,EAAAA,KAAA,KAAGI,UAAU,yCAAwCF,SAAEgN,EAAMzF,qBAGrEzH,EAAAA,EAAAA,KAAA,UAAQI,UAAU,4MAA2MF,SAAC,mBApB7NiN,SA8BjBnN,EAAAA,EAAAA,KAACS,EAAAA,EAAO6M,GAAE,CACNlN,UAAU,gFACVsI,SAAUF,EAAatI,SAC1B,sBAKDF,EAAAA,EAAAA,KAACS,EAAAA,EAAOmH,IAAG,CACPxH,UAAU,+HACVsI,SAAUL,EAAkBnI,SAE3B4M,EAAQ3G,KAAI,CAACiG,EAAKe,KACf,MAAM/F,EAAOgF,EAAIhF,MAAQ,IACzB,OACApH,EAAAA,EAAAA,KAACS,EAAAA,EAAOmG,EAAC,CAEL5I,KAAM2I,EAAgBS,GACtB7B,OAAQoB,EAAgBS,KAAUA,EAAO,SAAW,GACpD7G,QAASA,IAAM6G,GAAQV,EAAcU,GACrCiG,IAAI,sBACJjN,UAAU,8BACVmN,SAAU,EAAErN,UAEZI,EAAAA,EAAAA,MAACG,EAAAA,EAAOmH,IAAG,CACPxH,UAAU,sKACVsI,SAAUF,EAAatI,SAAA,EAEvBF,EAAAA,EAAAA,KAAA,OAAKsB,IAAK8K,EAAI9K,IAAKE,IAAK4K,EAAInG,KAAM7F,UAAU,yBAC5CJ,EAAAA,EAAAA,KAAA,OAAKI,UAAU,+BACfE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yBAAwBF,SAAA,EACnCF,EAAAA,EAAAA,KAAA,OAAKI,UAAS,8BAAAtD,OAA2C,kBAAXsP,EAAIjM,GAAyB,eAAiB,IAAKD,UAC7FF,EAAAA,EAAAA,KAAA,OAAKsB,IAAK8K,EAAInJ,KAAMzB,IAAG,GAAA1E,OAAKsP,EAAInG,KAAI,SAAS7F,UAAU,qCAE3DE,EAAAA,EAAAA,MAAA,OAAAJ,SAAA,EACIF,EAAAA,EAAAA,KAAA,MAAII,UAAU,yCAAwCF,SAAEkM,EAAInG,QAC5DjG,EAAAA,EAAAA,KAAA,KAAGI,UAAU,wBAAuBF,SAAEkM,EAAIG,iBApBjDY,Y,w6NC3H7B,MAAMK,GAAU3L,4BAehB,MAsjBM4L,GAAyBrP,IAA0C,IAAzC,cAAEsP,EAAa,iBAAEC,GAAkBvP,EAKjE,OAAKsP,GAKH1N,EAAAA,EAAAA,KAAA,OAAKI,UAAU,OAAMF,UACnBI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,0LAAyLF,SAAA,EACtMF,EAAAA,EAAAA,KAAA,OAAKI,UAAU,4BAA2BF,UACxCF,EAAAA,EAAAA,KAAA,OAAKI,UAAU,kEAAiEF,UAC9EF,EAAAA,EAAAA,KAAA,OAAK4N,wBAAyB,CAAEC,OAAQH,UAG5C1N,EAAAA,EAAAA,KAAA,UAAQI,UAAU,sFAAsFG,QAhB1FuN,KAClBH,GAAiB,IAegHzN,UAC3HF,EAAAA,EAAAA,KAAA,QAAMI,UAAU,UAASF,SAAC,aAZzB,MAkBX,GA9kBmB6N,KACjB,MAAM9O,GAAOC,EAAAA,GAAAA,MACP8O,EAAO/O,SAAAA,EAAMgP,SAAQ,SAAAnR,OAAYmC,EAAKgP,UAAa,GACnDC,ELkCqBjP,IAElBmL,EAAiBjE,KAAIK,IACxB,MACM2H,EADUpF,EAAUvC,EAAKZ,MAAQ,IAGvC,OAAAoH,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACOxG,GAAI,IACPY,KAAM+G,EACN5I,QAAQ0C,EAAAA,EAAAA,IAAchJ,EAAMkP,KAAaA,EAAU,SAAW,GAC9D5N,QAAU+E,IACNA,EAAEO,kBACEoC,EAAAA,EAAAA,IAAchJ,EAAMkP,KAAaA,GACjCnG,EAAAA,EAAAA,IAAY/I,EAAMkP,GAElBrQ,OAAOsQ,KAAKD,EAAS,gBKjDrBE,CAAapP,IACxBqP,EAAOC,IAAY3P,EAAAA,EAAAA,UAAS,CAAC,IAE7B8O,EAAeC,IAAoB/O,EAAAA,EAAAA,WAAS,IAE5CP,EAAmBC,IAAwBM,EAAAA,EAAAA,WAAS,IACpDuC,EAAsB1C,IAA2BG,EAAAA,EAAAA,WAAS,IAC1DqC,EAAiB1C,IAAsBK,EAAAA,EAAAA,UAAS,MAChDsC,EAA4B1C,IAAiCI,EAAAA,EAAAA,UAAS,MACtE4E,EAAaC,IAAkB7E,EAAAA,EAAAA,UAAS,KACxCoE,EAAgBU,IAAqB9E,EAAAA,EAAAA,UAAS,YAC9C6H,EAAW+H,IAAgB5P,EAAAA,EAAAA,WAAS,IACpC6P,EAAYC,IAAiB9P,EAAAA,EAAAA,WAAS,IACtC+P,EAAwBC,IAA6BhQ,EAAAA,EAAAA,WAASiQ,IAAW,KACzEC,EAAsBC,IAA2BnQ,EAAAA,EAAAA,WAASiQ,IAAW,KACrEG,EAAaC,IAAkBrQ,EAAAA,EAAAA,WAAS,IACxCsQ,EAAuBC,IAA4BvQ,EAAAA,EAAAA,WAAS,IAC5DwQ,EAAiBC,IAAsBzQ,EAAAA,EAAAA,UAAS,KAChD0Q,EAAcC,IAAmB3Q,EAAAA,EAAAA,WAAS,IAC1C4Q,EAAQC,IAAa7Q,EAAAA,EAAAA,WAAS,IAcrCO,EAAAA,EAAAA,YAAU,KACR,MAAMuQ,EAAStT,SAASC,cAAc,UACtCqT,EAAOpO,IAAMO,CAAAA,SAAAA,aAAAA,WAAAA,iBAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,cAAAA,EAAAA,gCAAAA,QAAAA,kBAAAA,4BAAAA,mBAAAA,yBAAAA,oCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,kCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,2BAAAA,iBAAAA,4BAAAA,cAAAA,+BAAAA,iBAAAA,+BAAAA,UAAAA,+BAAAA,iBAAAA,+BAAAA,UAAAA,+BAAAA,UAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,+BAAAA,UAAAA,+BAAAA,UAAAA,+BAAAA,UAAAA,+BAAAA,iBAAAA,+BAAAA,UAAAA,yBAAAA,kCAAAA,yBAAAA,oFAAAA,sBAAAA,KAAAA,uBAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,wBAAAA,MAAAA,mBAAAA,aAAAA,qCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,mCAAAA,iBAAAA,8BAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,UAAAA,kCAAAA,iCAAAA,kCAAAA,UAAAA,kCAAAA,UAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,UAAAA,kCAAAA,UAAAA,kCAAAA,UAAAA,kCAAAA,iCAAAA,kCAAAA,UAAAA,4BAAAA,QAAAA,gCAAAA,QAAAA,gCAAAA,QAAAA,gCAAAA,QAAAA,gCAAAA,QAAAA,gCAAAA,QAAAA,kCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,gCAAAA,KAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,iCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,+BAAAA,MAAAA,+BAAAA,MAAAA,+BAAAA,MAAAA,+BAAAA,MAAAA,+BAAAA,MAAAA,+BAAAA,MAAAA,+BAAAA,MAAAA,6BAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,yBAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,MAAAA,6BAAAA,WAAAA,6BAAAA,MAAAA,6BAAAA,MAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,MAAAA,6BAAAA,MAAAA,6BAAAA,MAAAA,6BAAAA,WAAAA,6BAAAA,MAAAA,gCAAAA,GAAAA,iCAAAA,8GAAAA,iCAAAA,8GAAAA,oBAAAA,8jBAAY8N,uBACzBD,EAAOE,iBAAiB,QAAQ,IAAIH,GAAU,KAC9CrT,SAASyT,KAAKC,YAAYJ,KACzB,KAEHvQ,EAAAA,EAAAA,YAAU,KACJqQ,GAnBiBO,MACrB,GAAIjS,OAAOkS,qBAAsB,CAC/B,MAAMC,EAAa,CACjBC,MAAOrO,CAAAA,SAAAA,aAAAA,WAAAA,iBAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,cAAAA,EAAAA,gCAAAA,QAAAA,kBAAAA,4BAAAA,mBAAAA,yBAAAA,oCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,kCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,2BAAAA,iBAAAA,4BAAAA,cAAAA,+BAAAA,iBAAAA,+BAAAA,UAAAA,+BAAAA,iBAAAA,+BAAAA,UAAAA,+BAAAA,UAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,+BAAAA,UAAAA,+BAAAA,UAAAA,+BAAAA,UAAAA,+BAAAA,iBAAAA,+BAAAA,UAAAA,yBAAAA,kCAAAA,yBAAAA,oFAAAA,sBAAAA,KAAAA,uBAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,wBAAAA,MAAAA,mBAAAA,aAAAA,qCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,mCAAAA,iBAAAA,8BAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,UAAAA,kCAAAA,iCAAAA,kCAAAA,UAAAA,kCAAAA,UAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,UAAAA,kCAAAA,UAAAA,kCAAAA,UAAAA,kCAAAA,iCAAAA,kCAAAA,UAAAA,4BAAAA,QAAAA,gCAAAA,QAAAA,gCAAAA,QAAAA,gCAAAA,QAAAA,gCAAAA,QAAAA,gCAAAA,QAAAA,kCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,gCAAAA,KAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,iCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,+BAAAA,MAAAA,+BAAAA,MAAAA,+BAAAA,MAAAA,+BAAAA,MAAAA,+BAAAA,MAAAA,+BAAAA,MAAAA,+BAAAA,MAAAA,6BAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,yBAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,MAAAA,6BAAAA,WAAAA,6BAAAA,MAAAA,6BAAAA,MAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,MAAAA,6BAAAA,MAAAA,6BAAAA,MAAAA,6BAAAA,WAAAA,6BAAAA,MAAAA,gCAAAA,GAAAA,iCAAAA,8GAAAA,iCAAAA,8GAAAA,oBAAAA,8jBAAYsO,qBAErBrS,OAAOkS,qBAAqBC,EAC9B,GAcAF,KACC,CAACP,KAUJrQ,EAAAA,EAAAA,YAAU,KACJF,IACmB,QAAjBA,EAAKmR,SACPnB,GAAe,GACG,eAAdhQ,EAAKI,OACP8P,GAAyB,GACzBE,EAAmBpQ,EAAKoR,sBAEH,eAAdpR,EAAKI,MACdkQ,GAAgB,MAGnB,CAACtQ,KAIJE,EAAAA,EAAAA,YAAU,KACR,MAAMmR,EAAQ9K,YAAW,IAAMgJ,GAAa,IAAQ,KACpD,MAAO,IAAM+B,aAAaD,KACzB,KAEHnR,EAAAA,EAAAA,YAAU,MACRqR,iBACE,MAAMC,QA5FZD,iBACE,MAGMlO,SAHiBL,GAAAA,EAAMC,KAAK,GAADpF,OAAI0Q,GAAO,eAAe,CAC3D,EAAG,CAAErL,QAAS,CAAE,eAAgB,wCAERI,KACxB,GAAID,EAAOG,QAET,OADoBH,EAAOC,KAAK,GAAGmO,eAGnC,MAAO,EAEX,CAiFkCC,QACNvR,IAAlBqR,GACF9C,EAAiB8C,EAErB,CACAG,KACC,KAEHzR,EAAAA,EAAAA,YAAU,UAEGC,IAATH,IACS,IAATA,GAEqB,YAAjBA,aAAI,EAAJA,EAAM4D,SACR6L,GAAc,KAGjB,CAACzP,KAEJE,EAAAA,EAAAA,YAAU,KACR,IAAI0R,EAAYvC,EAAMwC,WAClBC,EAAczC,EAAMyC,YACpBC,EAAoC,GAAZH,EAE5BjC,GAA0B,GAC1BG,GAAwB,GAEpB8B,GAAaE,EACfhC,GAAwB,GACfiC,GAAwBD,GACjCnC,GAA0B,KAE3B,CAACN,KAEJnP,EAAAA,EAAAA,YAAU,MACRqR,iBACE,MAAMjO,QAAa0O,EAAAA,GAAAA,MACnB1C,EAAShM,EACX,CACA2O,KACC,KAGH/R,EAAAA,EAAAA,YAAU,KACRqG,YAAW,KACT,MAAM2L,GAAQC,EAAAA,GAAAA,IAAU,aAClBC,GAAcD,EAAAA,GAAAA,IAAU,eACxBE,GAASF,EAAAA,GAAAA,IAAU,UAErBE,GAAWD,GAAeC,EAAOlJ,SAAS,WAAuB,QAAV+I,GACzDlP,GAAAA,EAAMC,KAAK,GAADpF,OAAI0Q,GAAO,eAAe,CAClC+D,OAAQF,GACP,CAAElP,QAAS,CAAE,eAAgB,uCAAyCC,MAAMC,IAC7E,MAAME,EAAOF,EAAIE,KACjB,GAAIA,EAAKE,QAAS,CAChB,MAAMb,EAAMW,EAAK6E,KACL,KAARxF,GACF4P,MAAM5P,GACHQ,MAAKQ,GAAYA,EAAS6O,SAC1BrP,MAAKqP,IACJ,MAAMC,EAAUC,IAAIC,gBAAgBH,GAC9BrK,EAAOhL,SAASC,cAAc,KACpC+K,EAAKpJ,KAAO0T,EACZtK,EAAKyK,aAAa,WAAY,oBAC9BzV,SAAS0V,KAAKhC,YAAY1I,GAC1BA,EAAK2K,QACL3V,SAAS0V,KAAKE,YAAY5K,KAGlC,EACA6K,EAAAA,GAAAA,IAAa,cAAe,CAAEC,OAAQ,cAAeC,KAAM,WAG9D,OACF,IAEH,MAAMC,EAAoBlE,EAAU/H,KAAIK,IAAIwG,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACvCxG,GAAI,IACPY,KAAMZ,EAAKY,KAAO4G,MAGd9F,EAAgBkK,EAAkBjK,QAAO3B,IAC7C,MAAM6L,EAAgB7L,EAAKP,KAAK1G,cAAc6I,SAAS5E,EAAYjE,gBACjEiH,EAAKiB,YAAYlI,cAAc6I,SAAS5E,EAAYjE,eAChD+S,EAAqC,QAAnBtP,GACF,YAAnBA,GAAgCwD,EAAKe,WACtCf,EAAKJ,WAAapD,EACpB,OAAOqP,GAAiBC,KAGpBC,EAAerK,EAAcC,QAAO3B,GAA0B,aAAlBA,EAAKJ,WACjDoM,EAAWtK,EAAcC,QAAO3B,GAA0B,SAAlBA,EAAKJ,WA4D7CqM,EAAgBA,KACTnS,EAAAA,EAAAA,MAAA,OAAK4D,MAAM,KAAKC,OAAO,KAAKC,QAAQ,YAAYC,KAAK,OAAOC,MAAM,6BAA6BlE,UAAU,gBAAeF,SAAA,CAAC,UAAMF,EAAAA,EAAAA,KAAA,QAAMuE,EAAE,KAAKmO,EAAE,IAAIxO,MAAM,IAAIC,OAAO,KAAKE,KAAK,UAAU,UAAMrE,EAAAA,EAAAA,KAAA,QAAM2E,EAAE,0yBAA0yBN,KAAK,YAAY,UAG7gC,OACE/D,EAAAA,EAAAA,MAAA,OAAKX,MAAO,CAAEC,QAAS,OAAQ+S,cAAe,SAAUC,UAAW,SAAU1S,SAAA,EAC3EI,EAAAA,EAAAA,MAACuS,GAAAA,EAAM,CAAA3S,SAAA,EACLF,EAAAA,EAAAA,KAAA,SAAAE,SAAO,4BACPF,EAAAA,EAAAA,KAAA,QAAMiG,KAAK,cAAc6M,QAAQ,sJAChCjR,CAAAA,SAAAA,aAAAA,WAAAA,iBAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,cAAAA,EAAAA,gCAAAA,QAAAA,kBAAAA,4BAAAA,mBAAAA,yBAAAA,oCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,kCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,2BAAAA,iBAAAA,4BAAAA,cAAAA,+BAAAA,iBAAAA,+BAAAA,UAAAA,+BAAAA,iBAAAA,+BAAAA,UAAAA,+BAAAA,UAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,+BAAAA,UAAAA,+BAAAA,UAAAA,+BAAAA,UAAAA,+BAAAA,iBAAAA,+BAAAA,UAAAA,yBAAAA,kCAAAA,yBAAAA,oFAAAA,sBAAAA,KAAAA,uBAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,wBAAAA,MAAAA,mBAAAA,aAAAA,qCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,mCAAAA,iBAAAA,8BAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,UAAAA,kCAAAA,iCAAAA,kCAAAA,UAAAA,kCAAAA,UAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,UAAAA,kCAAAA,UAAAA,kCAAAA,UAAAA,kCAAAA,iCAAAA,kCAAAA,UAAAA,4BAAAA,QAAAA,gCAAAA,QAAAA,gCAAAA,QAAAA,gCAAAA,QAAAA,gCAAAA,QAAAA,gCAAAA,QAAAA,kCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,gCAAAA,KAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,iCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,+BAAAA,MAAAA,+BAAAA,MAAAA,+BAAAA,MAAAA,+BAAAA,MAAAA,+BAAAA,MAAAA,+BAAAA,MAAAA,+BAAAA,MAAAA,6BAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,yBAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,MAAAA,6BAAAA,WAAAA,6BAAAA,MAAAA,6BAAAA,MAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,MAAAA,6BAAAA,MAAAA,6BAAAA,MAAAA,6BAAAA,WAAAA,6BAAAA,MAAAA,gCAAAA,GAAAA,iCAAAA,8GAAAA,iCAAAA,8GAAAA,oBAAAA,8jBAAYkR,8BAA+B9T,aAAI,EAAJA,EAAM+T,SAAUnR,CAAAA,SAAAA,aAAAA,WAAAA,iBAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,cAAAA,EAAAA,gCAAAA,QAAAA,kBAAAA,4BAAAA,mBAAAA,yBAAAA,oCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,kCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,2BAAAA,iBAAAA,4BAAAA,cAAAA,+BAAAA,iBAAAA,+BAAAA,UAAAA,+BAAAA,iBAAAA,+BAAAA,UAAAA,+BAAAA,UAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,+BAAAA,UAAAA,+BAAAA,UAAAA,+BAAAA,UAAAA,+BAAAA,iBAAAA,+BAAAA,UAAAA,yBAAAA,kCAAAA,yBAAAA,oFAAAA,sBAAAA,KAAAA,uBAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,wBAAAA,MAAAA,mBAAAA,aAAAA,qCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,mCAAAA,iBAAAA,8BAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,UAAAA,kCAAAA,iCAAAA,kCAAAA,UAAAA,kCAAAA,UAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,UAAAA,kCAAAA,UAAAA,kCAAAA,UAAAA,kCAAAA,iCAAAA,kCAAAA,UAAAA,4BAAAA,QAAAA,gCAAAA,QAAAA,gCAAAA,QAAAA,gCAAAA,QAAAA,gCAAAA,QAAAA,gCAAAA,QAAAA,kCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,gCAAAA,KAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,iCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,+BAAAA,MAAAA,+BAAAA,MAAAA,+BAAAA,MAAAA,+BAAAA,MAAAA,+BAAAA,MAAAA,+BAAAA,MAAAA,+BAAAA,MAAAA,6BAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,yBAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,MAAAA,6BAAAA,WAAAA,6BAAAA,MAAAA,6BAAAA,MAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,MAAAA,6BAAAA,MAAAA,6BAAAA,MAAAA,6BAAAA,WAAAA,6BAAAA,MAAAA,gCAAAA,GAAAA,iCAAAA,8GAAAA,iCAAAA,8GAAAA,oBAAAA,8jBAAYkR,4BAA4B3K,SAASnJ,EAAK+T,SAChHhT,EAAAA,EAAAA,KAAA,UAAQQ,KAAK,kBAAkBc,IAAI,kEAAkEkP,OAAK,QAG9GxQ,EAAAA,EAAAA,KAACiT,EAAAA,QAAM,CAAChU,KAAMA,EAAMX,qBAAsBA,KAC1CgC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,uBAAsBF,SAAA,EACnB,IAAfuO,GACCzO,EAAAA,EAAAA,KAAA,OAAKI,UAAU,OAAMF,UACnBI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,0LAAyLF,SAAA,EACtMF,EAAAA,EAAAA,KAAA,OAAKI,UAAU,4BAA2BF,UACxCI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wEAAuEF,SAAA,EACpFF,EAAAA,EAAAA,KAAA,OAAKI,UAAU,gDAA+CF,SAC3DuS,OAEHnS,EAAAA,EAAAA,MAAA,OAAKF,UAAU,SAAQF,SAAA,EACrBF,EAAAA,EAAAA,KAAA,OAAKI,UAAU,0BAAyBF,SAAC,8BACzCI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wBAAuBF,SAAA,CAAC,+DAA6DjB,aAAI,EAAJA,EAAMiU,WAAW,sDAEvHlT,EAAAA,EAAAA,KAAA,UAAQI,UAAU,oFAAoFG,QArF5F4S,KACxBrV,OAAOC,SAASC,KAAO,WAoFwHkC,SAAC,iBAKtIF,EAAAA,EAAAA,KAAA,UAAQI,UAAU,sFAAsFG,QAASA,IAAMmO,GAAc,GAAOxO,UAC1IF,EAAAA,EAAAA,KAAA,QAAMI,UAAU,UAASF,SAAC,aAI9B,IACF8O,GAAgBE,GAA0BT,GAsBvB,IAAhBO,IAAkD,IAA1BE,IAA+C,IAAbT,GAA0C,KAApBW,GACjFpP,EAAAA,EAAAA,KAAA,OAAKI,UAAU,OAAMF,UACnBI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,0LAAyLF,SAAA,EACtMF,EAAAA,EAAAA,KAAA,OAAKI,UAAU,4BAA2BF,UACxCI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wEAAuEF,SAAA,EACpFF,EAAAA,EAAAA,KAAA,OAAKI,UAAU,gDAA+CF,SAC3DuS,OAEHnS,EAAAA,EAAAA,MAAA,OAAKF,UAAU,SAAQF,SAAA,EACrBF,EAAAA,EAAAA,KAAA,OAAKI,UAAU,0BAAyBF,SAAC,+BACzCI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wBAAuBF,SAAA,CAAC,8GAA0GF,EAAAA,EAAAA,KAAA,KAAGhC,KAAK,iCAAiCqP,IAAI,aAAa9H,OAAO,SAASnF,UAAU,0BAAyBF,UAACF,EAAAA,EAAAA,KAAA,QAAMI,UAAU,UAASF,SAAC,mBAAuB,gBAItTF,EAAAA,EAAAA,KAAA,UAAQI,UAAU,sFAAsFG,QAASA,KAAQ4O,GAAyB,GAAQF,GAAe,IAAU/O,UACjLF,EAAAA,EAAAA,KAAA,QAAMI,UAAU,UAASF,SAAC,cAIZ,IAAhB8O,IAAkD,IAA1BE,GAAsD,KAApBE,GAC5D9O,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMF,SAAA,EACnBF,EAAAA,EAAAA,KAAA,OAAKI,UAAU,0LAAyLF,UACtMF,EAAAA,EAAAA,KAAA,OAAKI,UAAU,4BAA2BF,UACxCI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wEAAuEF,SAAA,EACpFF,EAAAA,EAAAA,KAAA,OAAKI,UAAU,gDAA+CF,SAC3DuS,OAEHzS,EAAAA,EAAAA,KAAA,OAAKI,UAAU,SAAQF,UACrBF,EAAAA,EAAAA,KAAA,QAAAE,SAAM,oKAOdF,EAAAA,EAAAA,KAAA,UAAQI,UAAU,sFAAsFG,QAASA,KAAQ4O,GAAyB,GAAQF,GAAe,IAAU/O,UACjLF,EAAAA,EAAAA,KAAA,QAAMI,UAAU,UAASF,SAAC,YAG3BjB,GAAwB,WAAhBA,EAAK4D,QA5GtB5D,GAAsB,WAAdA,EAAKI,MAAsC,OAAjBJ,EAAKmR,SAAoC,WAAhBnR,EAAK4D,QAKhE5D,GAAsB,eAAdA,EAAKI,MAA2D,gBAApB,QAAd+T,GAAAnU,EAAKoU,iBAAS,IAAAD,QAAA,EAAdA,GAAgB7T,gBAAmD,OAAjBN,EAAKmR,SAAoC,WAAhBnR,EAAK4D,OAwM5G,MAhGF7C,EAAAA,EAAAA,KAAAC,EAAAA,SAAA,CAAAC,SACGoP,GACCtP,EAAAA,EAAAA,KAAAC,EAAAA,SAAA,CAAAC,SACG4O,GACC9O,EAAAA,EAAAA,KAAA,OAAKI,UAAU,OAAMF,UACnBI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,0LAAyLF,SAAA,EACtMF,EAAAA,EAAAA,KAAA,OAAKI,UAAU,4BAA2BF,UACxCI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wEAAuEF,SAAA,EACpFF,EAAAA,EAAAA,KAAA,OAAKI,UAAU,gDAA+CF,SAC3DuS,OAEHnS,EAAAA,EAAAA,MAAA,OAAKF,UAAU,SAAQF,SAAA,EACrBF,EAAAA,EAAAA,KAAA,OAAKI,UAAU,0BAAyBF,SAAC,2BACzCI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wBAAuBF,SAAA,CAAC,sDACrCF,EAAAA,EAAAA,KAAA,KAAGhC,KAAK,iCAAiCoC,UAAU,2BAA0BF,UAACF,EAAAA,EAAAA,KAAA,UAAAE,SAAQ,iBAAuB,qFAIrHF,EAAAA,EAAAA,KAAA,UAAQI,UAAU,sFAAsFG,QAASA,IAAMwO,GAAwB,GAAO7O,UACpJF,EAAAA,EAAAA,KAAA,QAAMI,UAAU,UAASF,SAAC,aAI9ByO,GACF3O,EAAAA,EAAAA,KAAA,OAAKI,UAAU,OAAMF,UACnBI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,0LAAyLF,SAAA,EACtMF,EAAAA,EAAAA,KAAA,OAAKI,UAAU,4BAA2BF,UACxCI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wEAAuEF,SAAA,EACpFF,EAAAA,EAAAA,KAAA,OAAKI,UAAU,gDAA+CF,SAC3DuS,OAEHnS,EAAAA,EAAAA,MAAA,OAAKF,UAAU,SAAQF,SAAA,EACrBF,EAAAA,EAAAA,KAAA,OAAKI,UAAU,0BAAyBF,SAAC,0BACzCI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wBAAuBF,SAAA,CAAC,0EACrCF,EAAAA,EAAAA,KAAA,KAAGhC,KAAK,iCAAiCoC,UAAU,2BAA0BF,UAACF,EAAAA,EAAAA,KAAA,UAAAE,SAAQ,iBAAuB,qFAIrHF,EAAAA,EAAAA,KAAA,UAAQI,UAAU,sFAAsFG,QAASA,IAAMqO,GAA0B,GAAO1O,UACtJF,EAAAA,EAAAA,KAAA,QAAMI,UAAU,UAASF,SAAC,cAKhCF,EAAAA,EAAAA,KAACyN,GAAsB,CAACC,cAAeA,EAAeC,iBAAkBA,OAI5E3N,EAAAA,EAAAA,KAAAC,EAAAA,SAAA,CAAAC,SACG4O,GACC9O,EAAAA,EAAAA,KAAA,OAAKI,UAAU,OAAMF,UACnBI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,0LAAyLF,SAAA,EACtMF,EAAAA,EAAAA,KAAA,OAAKI,UAAU,4BAA2BF,UACxCI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wEAAuEF,SAAA,EACpFF,EAAAA,EAAAA,KAAA,OAAKI,UAAU,gDAA+CF,SAC3DuS,OAEHnS,EAAAA,EAAAA,MAAA,OAAKF,UAAU,SAAQF,SAAA,EACrBF,EAAAA,EAAAA,KAAA,OAAKI,UAAU,0BAAyBF,SAAC,2BACzCI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wBAAuBF,SAAA,CAAC,sDACrCF,EAAAA,EAAAA,KAAA,KAAGhC,KAAK,iBAAiBoC,UAAU,2BAA0BF,UAACF,EAAAA,EAAAA,KAAA,UAAAE,SAAQ,kBAAwB,qFAKtGF,EAAAA,EAAAA,KAAA,UAAQI,UAAU,sFAAsFG,QAASA,IAAMwO,GAAwB,GAAO7O,UACpJF,EAAAA,EAAAA,KAAA,QAAMI,UAAU,UAASF,SAAC,aAI9ByO,GACF3O,EAAAA,EAAAA,KAAA,OAAKI,UAAU,OAAMF,UACnBI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,0LAAyLF,SAAA,EACtMF,EAAAA,EAAAA,KAAA,OAAKI,UAAU,4BAA2BF,UACxCI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wEAAuEF,SAAA,EACpFF,EAAAA,EAAAA,KAAA,OAAKI,UAAU,gDAA+CF,SAC3DuS,OAEHnS,EAAAA,EAAAA,MAAA,OAAKF,UAAU,SAAQF,SAAA,EACrBF,EAAAA,EAAAA,KAAA,OAAKI,UAAU,0BAAyBF,SAAC,0BACzCI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wBAAuBF,SAAA,CAAC,0EACrCF,EAAAA,EAAAA,KAAA,KAAGhC,KAAK,iBAAiBoC,UAAU,2BAA0BF,UAACF,EAAAA,EAAAA,KAAA,UAAAE,SAAQ,kBAAwB,qFAItGF,EAAAA,EAAAA,KAAA,UAAQI,UAAU,sFAAsFG,QAASA,IAAMqO,GAA0B,GAAO1O,UACtJF,EAAAA,EAAAA,KAAA,QAAMI,UAAU,UAASF,SAAC,cAKhCF,EAAAA,EAAAA,KAACyN,GAAsB,CAACC,cAAeA,EAAeC,iBAAkBA,SAxJtF3N,EAAAA,EAAAA,KAAA,OAAKI,UAAU,OAAMF,UACnBI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,0LAAyLF,SAAA,EACtMF,EAAAA,EAAAA,KAAA,OAAKI,UAAU,4BAA2BF,UACxCI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wEAAuEF,SAAA,EACpFF,EAAAA,EAAAA,KAAA,OAAKI,UAAU,gDAA+CF,SAC3DuS,OAEHnS,EAAAA,EAAAA,MAAA,OAAKF,UAAU,SAAQF,SAAA,EACrBF,EAAAA,EAAAA,KAAA,OAAKI,UAAU,0BAAyBF,SAAC,6BACzCF,EAAAA,EAAAA,KAAA,OAAKI,UAAU,wBAAuBF,SAAC,uFAEzCF,EAAAA,EAAAA,KAAA,UAAQI,UAAU,oFAAoFG,QAxG3F+S,KACzB,MACMC,EAAWtU,EAAKsU,SAAShU,eAE/BiU,EAAAA,GAAAA,IAAU,yBAA0B,2BACpCA,EAAAA,GAAAA,IAAU,yBAA0B,yBAA0B,CAAErB,KAAM,OACtEqB,EAAAA,GAAAA,IAAU,yBAA0B,yBAA0B,CAAErB,KAAM,gBACtEqB,EAAAA,GAAAA,IAAU,yBAA0B,yBAA0B,CAAErB,KAAM,gBAEtE,MAAMsB,EAAU5R,CAAAA,SAAAA,aAAAA,WAAAA,iBAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,cAAAA,EAAAA,gCAAAA,QAAAA,kBAAAA,4BAAAA,mBAAAA,yBAAAA,oCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,kCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,2BAAAA,iBAAAA,4BAAAA,cAAAA,+BAAAA,iBAAAA,+BAAAA,UAAAA,+BAAAA,iBAAAA,+BAAAA,UAAAA,+BAAAA,UAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,+BAAAA,UAAAA,+BAAAA,UAAAA,+BAAAA,UAAAA,+BAAAA,iBAAAA,+BAAAA,UAAAA,yBAAAA,kCAAAA,yBAAAA,oFAAAA,sBAAAA,KAAAA,uBAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,wBAAAA,MAAAA,mBAAAA,aAAAA,qCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,mCAAAA,iBAAAA,8BAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,UAAAA,kCAAAA,iCAAAA,kCAAAA,UAAAA,kCAAAA,UAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,UAAAA,kCAAAA,UAAAA,kCAAAA,UAAAA,kCAAAA,iCAAAA,kCAAAA,UAAAA,4BAAAA,QAAAA,gCAAAA,QAAAA,gCAAAA,QAAAA,gCAAAA,QAAAA,gCAAAA,QAAAA,gCAAAA,QAAAA,kCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,gCAAAA,KAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,iCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,+BAAAA,MAAAA,+BAAAA,MAAAA,+BAAAA,MAAAA,+BAAAA,MAAAA,+BAAAA,MAAAA,+BAAAA,MAAAA,+BAAAA,MAAAA,6BAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,yBAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,MAAAA,6BAAAA,WAAAA,6BAAAA,MAAAA,6BAAAA,MAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,MAAAA,6BAAAA,MAAAA,6BAAAA,MAAAA,6BAAAA,WAAAA,6BAAAA,MAAAA,gCAAAA,GAAAA,iCAAAA,8GAAAA,iCAAAA,8GAAAA,oBAAAA,8jBAAY6R,mBAAqB,gBACjDF,EAAAA,GAAAA,IAAU,UAAWC,IACrBD,EAAAA,GAAAA,IAAU,UAAWC,EAAS,CAAEtB,KAAM,OACtCqB,EAAAA,GAAAA,IAAU,UAAWC,EAAS,CAAEtB,KAAM,gBACtCqB,EAAAA,GAAAA,IAAU,UAAWC,EAAS,CAAEtB,KAAM,iBACtCF,EAAAA,GAAAA,IAAa,SAGXnU,OAAOC,SAASC,KADD,QAAbuV,EACqB,0BACD,QAAbA,EACc,0BACD,QAAbA,EACc,0BACD,QAAbA,EACc,0BACD,QAAbA,EACc,0BACD,QAAbA,EACc,0BACD,QAAbA,EACc,0BACD,QAAbA,EACc,0BACD,QAAbA,EACc,0BACD,QAAbA,EACc,0BACD,QAAbA,EACc,0BAEA,oBAiEuHrT,SAAC,qBAKvIF,EAAAA,EAAAA,KAAA,UAAQI,UAAU,sFAAsFG,QAASA,IAAM0O,GAAe,GAAO/O,UAC3IF,EAAAA,EAAAA,KAAA,QAAMI,UAAU,UAASF,SAAC,cAgK5BF,EAAAA,EAAAA,KAAAC,EAAAA,SAAA,KACND,EAAAA,EAAAA,KAAA,OAAKI,UAAU,0DAAyDF,SAClD,YAAnB8C,GACC1C,EAAAA,EAAAA,MAAAL,EAAAA,SAAA,CAAAC,SAAA,EACEI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,WAAUF,SAAA,EACvBF,EAAAA,EAAAA,KAAA,OACEsB,IAAKqS,GACLnS,IAAI,aACJpB,UAAU,gDACVT,MAAO,CAAEwE,OAAQ,OAAQyO,UAAW,YAEtC5S,EAAAA,EAAAA,KAAC4T,EAAa,KACd5T,EAAAA,EAAAA,KAAC6T,EAAU,CACT7Q,eAAgBA,EAChBU,kBAAmBA,EACnBF,YAAaA,EACbC,eAAgBA,QAGpBzD,EAAAA,EAAAA,KAAC8T,GAAW,CAAC/L,MAAOqK,EAAmB1L,cAja5B9E,KACrBoG,EAAAA,EAAAA,IAAY/I,EAAM2C,IAga6D+E,gBA7ZxD/E,IAChBqG,EAAAA,EAAAA,IAAchJ,EAAM2C,GA4ZsFoB,eAAgBA,QAGzH1C,EAAAA,EAAAA,MAAAL,EAAAA,SAAA,CAAAC,SAAA,EACEI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,WAAUF,SAAA,EACvBF,EAAAA,EAAAA,KAAC+T,EAAO,CAAC/Q,eAAgBA,KACzBhD,EAAAA,EAAAA,KAAC6T,EAAU,CACT7Q,eAAgBA,EAChBU,kBAAmBA,EACnBF,YAAaA,EACbC,eAAgBA,KAElBzD,EAAAA,EAAAA,KAAA,OACEsB,IAAKqS,GACLnS,IAAI,aACJpB,UAAU,gDACVT,MAAO,CAAEwE,OAAQ,OAAQyO,UAAW,cAGpB,QAAnB5P,GACChD,EAAAA,EAAAA,KAAAC,EAAAA,SAAA,CAAAC,SACGsD,GACCxD,EAAAA,EAAAA,KAACgU,EAAY,CACXnM,OAAOvH,EAAAA,EAAAA,MAAA,QAAMX,MAAO,CAAEgI,MAAO,WAAYzH,SAAA,CAAC,gBAAYI,EAAAA,EAAAA,MAAA,QAAMX,MAAO,CAAEgI,MAAO,WAAYzH,SAAA,CAAC,IAAEsD,EAAY,UACvG0C,KAAK,MACL4B,UAAU,OACVC,MAAOG,EACP1E,YAAaA,EACbiD,UAAWA,EACXzD,eAAgBA,EAChB/D,KAAMA,KAGRqB,EAAAA,EAAAA,MAAAL,EAAAA,SAAA,CAAAC,SAAA,EACEF,EAAAA,EAAAA,KAACgU,EAAY,CACXnM,MAAM,cACN3B,KAAK,OACL4B,UAAU,OACVC,MAAOwK,EACP/O,YAAaA,EACbiD,UAAWA,EACXzD,eAAgBA,EAChB/D,KAAMA,KAERe,EAAAA,EAAAA,KAACgU,EAAY,CACXnM,MAAM,UACN3B,KAAK,MACL4B,UAAU,SACVC,MAAOyK,EACPhP,YAAaA,EACbiD,UAAWA,EACXzD,eAAgBA,EAChB/D,KAAMA,UAMdqB,EAAAA,EAAAA,MAAAL,EAAAA,SAAA,CAAAC,SAAA,CACsB,aAAnB8C,IACChD,EAAAA,EAAAA,KAAAC,EAAAA,SAAA,CAAAC,UACEF,EAAAA,EAAAA,KAACgU,EAAY,CACXnM,OAAOvH,EAAAA,EAAAA,MAAA,QAAMX,MAAO,CAAEgI,MAAO,WAAYzH,SAAA,CAAC,gBAAYI,EAAAA,EAAAA,MAAA,QAAMX,MAAO,CAAEgI,MAAO,WAAYzH,SAAA,CAAC,IAAEsD,EAAY,UACvG0C,KAAK,OACL4B,UAAU,OACVC,MAAOG,EAAcC,QAAO3B,GAA0B,aAAlBA,EAAKJ,WACzC5C,YAAaA,EACbiD,UAAWA,EACXzD,eAAgBA,EAChB/D,KAAMA,MAKQ,SAAnB+D,IACChD,EAAAA,EAAAA,KAAAC,EAAAA,SAAA,CAAAC,UACEF,EAAAA,EAAAA,KAACgU,EAAY,CACXnM,OAAOvH,EAAAA,EAAAA,MAAA,QAAMX,MAAO,CAAEgI,MAAO,WAAYzH,SAAA,CAAC,gBAAYI,EAAAA,EAAAA,MAAA,QAAMX,MAAO,CAAEgI,MAAO,WAAYzH,SAAA,CAAC,IAAEsD,EAAY,UACvG0C,KAAK,MACL4B,UAAU,SACVC,MAAOG,EAAcC,QAAO3B,GAA0B,SAAlBA,EAAKJ,WACzC5C,YAAaA,EACbiD,UAAWA,EACXzD,eAAgBA,EAChB/D,KAAMA,kBAUxBe,EAAAA,EAAAA,KAAC7B,GAAAA,EAAkB,CAACE,kBAAmBA,EAAmBC,qBAAsBA,EAAsBC,mBAAoBA,EAAoBC,8BAA+BA,EAA+BC,wBAAyBA,KACrOuB,EAAAA,EAAAA,KAACe,GAAAA,EAAsB,CAACE,gBAAiBA,EAAiBC,2BAA4BA,EAA4BzC,wBAAyBA,EAAyB0C,qBAAsBA,KAC1LnB,EAAAA,EAAAA,KAACiU,EAAAA,QAAM,OAhVgBC,IAAMd,G", "sources": ["core/utils/helper.jsx", "modal/enterprise.jsx", "my-account-b/components/Header-a.jsx", "my-account-b/components/SearchBar.jsx", "my-account-b/components/Navigation.jsx", "my-account-b/components/ToolIcon.jsx", "my-account-b/components/ToolCard.jsx", "my-account-b/components/ToolsSection.jsx", "my-account-b/components/Header-b.jsx", "my-account-b/data/toolsData.js", "my-account-b/components/icon/claude-logo-only.svg", "my-account-b/components/icon/chatgpt-logo-only.svg", "my-account-b/components/icon/deepseek-logo-only.svg", "my-account-b/components/MostPopular.jsx", "my-account-b/index.jsx"], "sourcesContent": ["//check if browser supports WEBP\r\nexport function isWebpSupported() {\r\n  const elem = document.createElement('canvas');\r\n  if (!!(elem.getContext && elem.getContext('2d'))) {\r\n    return elem.toDataURL('image/webp').indexOf('data:image/webp') === 0;\r\n  }\r\n  return false;\r\n}\r\n// Observe sections/divs for lazy loading\r\nexport function observeSections(callback) {\r\n  const observer = new IntersectionObserver(entries => {\r\n    entries.forEach(entry => {\r\n      if (entry.isIntersecting) {\r\n        callback(entry.target.id);\r\n        // observer.unobserve(entry.target); // Stop observing once the section is intersecting\r\n      }\r\n    });\r\n  });\r\n  document.querySelectorAll('.lazy-section').forEach(section => {\r\n    observer.observe(section);\r\n  });\r\n  return () => {\r\n    observer.disconnect();\r\n  };\r\n}\r\n\r\n// Observe videos for lazy loading\r\nexport function observeVideos(callback) {\r\n  const observer = new IntersectionObserver(entries => {\r\n    entries.forEach(entry => {\r\n      if (entry.isIntersecting) {\r\n        const video = entry.target.querySelector('video');\r\n        if (video) {\r\n          const source = video.querySelector('source');\r\n          if (source && source.getAttribute('data-src')) {\r\n            source.setAttribute('src', source.getAttribute('data-src'));\r\n            video.load();\r\n            callback(entry.target.id);\r\n          }\r\n        }\r\n        observer.unobserve(entry.target);\r\n      }\r\n    });\r\n  });\r\n\r\n  document.querySelectorAll('.lazy-video').forEach(video => {\r\n    observer.observe(video);\r\n  });\r\n\r\n  return () => {\r\n    observer.disconnect();\r\n  };\r\n}\r\n\r\n// Hash pp_ctaclr\r\nexport function hexHash(hex) {\r\n  const hexRegex = /^[0-9a-f]*$/i;\r\n  if (hex == null || hex.length < 6 || !hexRegex.test(hex)) {\r\n    return `#1559ED`;\r\n  }\r\n\r\n  hex = hex.slice(0, 6);\r\n\r\n  return `#${hex}`;\r\n}\r\n\r\n// Darken color when hovering\r\nexport function hoverDarken(hex) {\r\n  const hexRegex = /^[0-9a-f]*$/i;\r\n  if (hex == null || hex.length < 6 || !hexRegex.test(hex)) {\r\n    hex = '1559ED';\r\n  }\r\n\r\n  let r = parseInt(hex.slice(0, 2), 16);\r\n  let g = parseInt(hex.slice(2, 4), 16);\r\n  let b = parseInt(hex.slice(4, 6), 16);\r\n\r\n  r = Math.max(0, Math.min(255, r - (r * 0.15)));\r\n  g = Math.max(0, Math.min(255, g - (g * 0.15)));\r\n  b = Math.max(0, Math.min(255, b - (b * 0.15)));\r\n\r\n  const toHex = (value) => {\r\n    const hexValue = Math.round(value).toString(16);\r\n    return hexValue.padStart(2, '0');\r\n  };\r\n  \r\n  return `#${toHex(r)}${toHex(g)}${toHex(b)}`;\r\n}\r\n\r\nexport function getPrefixLocation() {\r\n  const currentLocation = window.location.href;\r\n\r\n  if (currentLocation.indexOf(\"staging\") > -1) {\r\n      return \"staging.\";\r\n  } else if (currentLocation.indexOf(\"dev\") > -1) {\r\n      return \"dev.\";\r\n  }\r\n\r\n  return \"\"\r\n}", "import { useEffect, useState } from 'react';\r\nimport { motion } from \"framer-motion\";\r\nimport axios from 'axios';\r\nimport aiproLogo from '../assets/images/AIPRO.svg';\r\nimport { FaInfoCircle } from 'react-icons/fa';\r\nimport { Auth } from '../core/utils/auth';\r\nimport toastr from 'toastr';\r\nimport 'toastr/build/toastr.min.css';\r\n\r\nvar price_per_member = '0';\r\nvar user_id = '';\r\n\r\nexport function AddMoreMemberModal ({showAddMoreMember, setshowAddMoreMember, setMoreToAddMember, setMoreToAddMemberTotalAmount, setShowCompletePurchase}) {\r\n  const [membersToAdd, setMembersToAdd] = useState(1);\r\n  const [totalAmount, setTotalAmount] = useState(0);\r\n  const [paymentInterval, setpaymentInterval] = useState(\"\");\r\n\r\n  const auth = Auth();\r\n\r\n  useEffect(() => {\r\n    if (auth !== undefined && auth.plan==='enterprise'){\r\n      price_per_member = auth.price_per_member;\r\n\r\n      if (auth.interval.toLowerCase()===\"monthly\"){\r\n        setpaymentInterval(\"MONTH\");        \r\n      }else{\r\n        setpaymentInterval(\"YEAR\");       \r\n      }\r\n\r\n    }\r\n  }, [auth]);\r\n\r\n\r\n  const modalAddMoreMembersOpen = () => {\r\n    let modal = document.getElementById(\"modalAddMoreMembers\");\t\t\r\n    if (modal!==null){\r\n      modal.style.display = \"block\";\r\n    }\r\n  }\r\n\r\n  const modalAddMoreMembersClose = () => {\r\n    let modal = document.getElementById(\"modalAddMoreMembers\");\t\t\r\n    if (modal!==null){\r\n      modal.style.display = \"none\";\t\t \r\n      setshowAddMoreMember(false);\r\n    }\r\n  }\r\n\r\n  const handlePlus = () => {\r\n    setMembersToAdd(membersToAdd+1);\r\n  }\r\n\r\n  const handleMinus = () => {\r\n    if (membersToAdd>1){\r\n      setMembersToAdd(membersToAdd-1);\r\n    }\r\n  }\r\n  \r\n  useEffect(() => {\r\n    toastr.options = {\r\n      positionClass: 'toast-top-center'\r\n    };\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    let amount = membersToAdd * price_per_member;\r\n    setTotalAmount(amount);\r\n  }, [membersToAdd]);\r\n\r\n  useEffect(() => {\r\n    setMembersToAdd(1);\r\n\r\n    let amount = membersToAdd * price_per_member;\r\n    setTotalAmount(amount);\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps    \r\n  }, [showAddMoreMember]);\r\n\r\n  const handleUupgradeNow = () => {\r\n    window.location.href = '/upgrade-ent/'+membersToAdd;\r\n    return;\r\n\r\n    // setMoreToAddMember(membersToAdd);\r\n    // setMoreToAddMemberTotalAmount(totalAmount);\r\n\r\n    // setshowAddMoreMember(false);\r\n    // setShowCompletePurchase(true)\r\n  }\r\n\r\n  if (showAddMoreMember!==undefined && showAddMoreMember === true){\r\n    modalAddMoreMembersOpen();\r\n  }\r\n  if (showAddMoreMember!==undefined && showAddMoreMember === false){\r\n    modalAddMoreMembersClose();\r\n  }\r\n\r\n  return (\r\n    <>\r\n\t\t<div id=\"modalAddMoreMembers\" className=\"modal z-[9999]\">\r\n\t\t\t<div class=\"modal-content w-full md:w-[60%] max-w-[90%] p-3 md:p-4\">\r\n        <div>\r\n          <div className=\"mb-4 flex border-b pb-2\">\r\n            <div className=\"text-blue-500 w-full md:w-2/3 mr-2 md:mr-5 font-bold\">\r\n            Add More Members\r\n            </div>\r\n            <div className=\"float-right mt-[-5px] w-full md:w-1/3\">\r\n              <span class=\"close\" onClick={()=> modalAddMoreMembersClose()}>&times;</span>\r\n            </div>\r\n          </div>\r\n          <div className=\"p-2 md:p-4 border rounded text-sm text-center\">\r\n            <div>Your enterprise account has hit its maximum user capacity.</div>\r\n            <div>Add more members to your Enterprise Account.</div>\r\n            <div className=\"py-4 text-center\">\r\n              <span className=\"font-bold text-[12px] sm:text-sm inline px-1 sm:px-2\">Add</span>\r\n              <div className=\"border rounded px-2 py-4 inline\">\r\n                <input className=\"bg-blue-500 rounded px-3 py-2 m-2 text-white cursor-pointer\" type=\"button\" value=\"-\" onClick={()=>handleMinus()}/>\r\n                <span className=\"text-blue-500 p-2 mx-auto font-bold\">{membersToAdd}</span>\r\n                <input className=\"bg-blue-500 rounded px-3 py-2 m-2 text-white cursor-pointer\" type=\"button\" value=\"+\" onClick={()=>handlePlus()} />\r\n              </div>\r\n              <span className=\"font-bold text-[12px] sm:text-sm inline px-1 sm:px-2\">Member/s</span>\r\n            </div>\r\n            <div className=\"font-bold text-sm\">\r\n              Total Amount: ${totalAmount}\r\n            </div>\r\n            <div className=\"font-bold text-sm\">\r\n              PER {paymentInterval}\r\n            </div>\r\n            <div>\r\n              <motion.button\r\n              className=\"bg-sky-600 w-full md:w-70 text-white font-bold my-4 py-2 px-6 rounded proceed-pmt\"\r\n              whileHover={{ backgroundColor: \"#49b1df\" }}\r\n              whileTap={{ scale: 0.9 }}\r\n              onClick={()=> handleUupgradeNow()}\r\n              >\r\n              UPGRADE NOW\r\n              </motion.button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n    </>\r\n  );\r\n}\r\n\r\nexport function MemberCompletePurchase ({moreToAddMember, moreToAddMemberTotalAmount, setShowCompletePurchase, showCompletePurchase}) {\r\n\r\n  const auth = Auth();\r\n\r\n  useEffect(() => {\r\n    if (auth !== undefined && auth.plan==='enterprise'){\r\n      user_id = auth.user_id;\r\n    }\r\n  }, [auth]);\r\n\r\n  const modalOpen = () => {\r\n    let modal = document.getElementById(\"modalComplete\");\t\t\r\n    if (modal!==null){\r\n      modal.style.display = \"block\";\t\t\r\n    }\r\n  }\r\n\r\n  const modalClose = () => {\r\n    let modal = document.getElementById(\"modalComplete\");\t\t\r\n    if (modal!==null){\r\n      modal.style.display = \"none\";\t\t \r\n      setShowCompletePurchase(false);\r\n    }\r\n  }\r\n\r\n  const submitPaymentInformation = () => {\r\n    window.location.href = '/payment-reference';\r\n    return;\r\n  }\r\n\r\n  const sendViaEmail = () => {\r\n    var members = moreToAddMember;\r\n    var total_amount = moreToAddMemberTotalAmount;\r\n    var url = `${process.env.REACT_APP_API_URL}/t/send-enterprise-payment-info`;\r\n\r\n    modalClose();    \r\n    document.querySelector(\".loader-container\").classList.add('active');\r\n\r\n    axios.post(url, {\r\n      members,\r\n      total_amount\r\n    }, { headers: { 'content-type': 'application/x-www-form-urlencoded' } }).then(function(res) {\r\n      let output = res.data;\r\n\r\n      document.querySelector(\".loader-container\").classList.remove('active');\r\n\r\n      if(output.success) {\r\n        toastr.success(\"Email sent to \"+output.data);\r\n      }else{\r\n        toastr.error(\"Email Failed.\");\r\n      }\r\n    }).catch(function (error) {\r\n      document.querySelector(\".loader-container\").classList.remove('active');\r\n      if (error.response && error.response.status===429) {\r\n        document.querySelector(\".loader-container\").classList.remove('active');\r\n        toastr.error(\"Sorry, too many requests. Please try again in a bit!\");\r\n      }\r\n    });\r\n  }\r\n\r\n  if (showCompletePurchase!==undefined && showCompletePurchase === true){\r\n    modalOpen();\r\n  }\r\n  if (showCompletePurchase!==undefined && showCompletePurchase === false){\r\n    modalClose();\r\n  }\r\n\r\n  return (\r\n    <>\r\n    <div id=\"modalComplete\" className=\"modal z-[9999]\">\r\n      <div class=\"w-full md:w-[600px] border-[#888] md:mt-[15px] mx-[auto] bg-[#fefefe] p-6\">\r\n        <span class=\"close\" onClick={()=> modalClose()}>&times;</span>\r\n        <div className=\"border-b pb-[10px] border-[#d5d5d5]\"><img src={aiproLogo} alt=\"AI-Pro Logo\" className=\"aiprologo mx-auto\"/></div>\r\n        <h1 className=\"font-bold text-center p-2 text-gray-700 text-[20px] md:text-[24px]\">Payment Details<br/>for Enterprise Order</h1>\r\n        <div className=\"text-center text-[12px] md:text-[14px]\">Adding more than 10 Enterprise users requires prior payment.</div>\r\n        <div className=\"text-center text-[12px] md:text-[14px]\">Please use the provided payment details below to settle your Enterprise account.</div>\r\n        <div className=\"py-2\">\r\n          <div className=\"font-bold text-[11px]\">No. of Members: {moreToAddMember}</div>\r\n          <div className=\"font-bold text-[11px]\">Enterprise - Total: ${moreToAddMemberTotalAmount}</div>\r\n        </div>\r\n        <div className=\"border rounded p-2 text-[12px] md:text-[14px] leading-7 my-2\">\r\n          <div className=\"font-bold\">Bank Information</div>\r\n          <div className=\"float-right text-blue-400 font-bold cursor-pointer mt-[-28px] text-[12px]\" onClick={()=> sendViaEmail()}>Send via Email</div>\r\n          <div><span className=\"font-bold\">Beneficiary:</span> TELECOM BUSINESS SOLUTIONS INC.</div>\r\n          <div><span className=\"font-bold\">SWIFT:</span> BOFAUS3N</div>\r\n          <div><span className=\"font-bold\">Bank Name:</span> Bank of America</div>\r\n          <div><span className=\"font-bold\">Routing (Wire):</span> *********</div>\r\n          <div><span className=\"font-bold\">Routing Number (Paper & Electronic):</span> *********</div>\r\n          <div><span className=\"font-bold\">Account Number:</span> 3810-6766-2647</div>\r\n          <div><span className=\"font-bold\">Customer Number:</span> {user_id}</div>\r\n          <div className=\"bg-[#dddddd] px-4 py-2 rounded text-center mt-4\"><FaInfoCircle className=\"inline text-lg mr-2\"/>Customer Number must be included in the bank transfer description field for your funds to transfer successfully.</div>\r\n        </div>\r\n\r\n        <div className=\"text-center text-[12px] md:text-[14px] mt-4\">\r\n          Once the payment is received, our dedicated account manager will contact you to assist in the seamless setup of your Enterprise account.\r\n        </div>\r\n        <div className=\"text-center text-[12px] md:text-[14px]\">\r\n          Please allow <b>2-3 banking days</b> for the payment to reflect in the account.\r\n        </div>\r\n        <div className=\"text-center\">\r\n          <motion.button\r\n            className=\"bg-blue-500 text-white font-bold py-3 px-4 rounded my-4 proceed-pmt\"\r\n            whileHover={{ backgroundColor: \"#5997fd\" }}\r\n            whileTap={{ scale: 0.9 }}\r\n            onClick={submitPaymentInformation}\r\n          >\r\n            Send Payment Confirmation\r\n          </motion.button>\r\n        </div>\r\n      </div>\r\n    </div>    \r\n    </>\r\n  )\r\n}\r\n", "import React from 'react';\r\nimport logo from '../assets/ai-pro-logo.png'\r\nimport aiChat from '../assets/ai-chat-i.png';\r\nimport aiArt from '../assets/ai-art-i.png';\r\n\r\nconst HeaderA = ({ activeCategory }) => {\r\n  const getContent = () => {\r\n    switch(activeCategory) {\r\n      case 'chatbots':\r\n        return {\r\n          logo: aiChat,\r\n          heading: <><span className=\"text-[#3073D5]\">Powerful</span> AI Apps</>,\r\n          paragraph: \"OpenAI GPT technology generates essays, reports, website content, contracts, poems, or ask questions and get answers.\"\r\n        };\r\n      case 'arts':\r\n        return {\r\n          logo: aiArt,\r\n          heading: <><span className=\"text-[#3073D5]\">Powerful</span> AI Apps</>,\r\n          paragraph: \"Access the latest AI Art generation software. Describe what you want to create, and within seconds you have your image.\"\r\n        };\r\n      default:\r\n        return {\r\n          logo: logo,\r\n          heading: <>All AI-Pro <span className=\"text-[#3073D5]\">Apps</span></>,\r\n          paragraph: \"Your headquarters for powerful AI apps.\"\r\n        };\r\n    }\r\n  };\r\n\r\n  const { logo: currentLogo, heading, paragraph } = getContent();\r\n\r\n  return (\r\n      <div className={`relative z-10 flex flex-col items-center justify-center pt-8 ${activeCategory === 'all' ? '!pt-9' : ''}`}>\r\n        <div className={`flex items-center justify-center mb-2 filter ${activeCategory === 'chatbots' || activeCategory === 'arts' ? 'brightness-100 w-[53px] h-[53px]' : 'brightness-0 w-[73px] h-[73px]'}`}>\r\n         <img src={currentLogo} alt='AI-Pro logo' />\r\n        </div>\r\n        <h1 className=\"text-[34px] xs:text-[42px] md:text-[44px] font-bold text-[#373737] text-center xs:whitespace-nowrap\">\r\n          {heading}\r\n        </h1>\r\n        <p className={`text-[#000000BF] text-[14px] xs:text-[16px] text-center ${activeCategory === 'chatbots' || activeCategory === 'arts' ? 'max-w-[540px]' : ''}`}>{paragraph}</p>\r\n      </div>\r\n  );\r\n};\r\n\r\nexport default HeaderA;\r\n", "import React, { useRef } from 'react';\r\nimport { IoCloseOutline } from \"react-icons/io5\";\r\n\r\nconst SearchBar = ({ searchQuery, setSearchQuery, activeCategory, setActiveCategory }) => {\r\n  const inputRef = useRef(null);\r\n  \r\n  // Apple mobile device detection (iOS only)\r\n  const isAppleMobile = () => {\r\n    return /iPad|iPhone|iPod/.test(navigator.userAgent);\r\n  };\r\n\r\n  const handleSearchChange = (e) => {\r\n    const value = e.target.value;\r\n    setSearchQuery(value);\r\n    \r\n    // Keep focus on input while changing category\r\n    // Only change category automatically for non-Apple mobile devices\r\n    if (value && activeCategory === 'popular' && !isAppleMobile()) {\r\n      setActiveCategory('all');\r\n      // Find and refocus the searchinput after category change\r\n      setTimeout(() => {\r\n        const searchInput = document.querySelector('.searchinput');\r\n        if (searchInput) {\r\n          searchInput.focus();\r\n        }\r\n      }, 0);\r\n    }\r\n  };\r\n\r\n  const handleKeyDown = (e) => {\r\n    if (e.key === 'Enter' && searchQuery) {\r\n      // Only trigger category change on Enter for Apple mobile devices (iOS)\r\n      if (isAppleMobile()) {\r\n        if (activeCategory === 'popular') {\r\n          setActiveCategory('all');\r\n        }\r\n      } else {\r\n        // For Android and desktop, keep the current behavior\r\n        if (activeCategory === 'popular') {\r\n          setActiveCategory('all');\r\n        }\r\n      }\r\n      setSearchQuery(searchQuery);\r\n    }\r\n  };\r\n\r\n  const handleInputClick = (e) => {\r\n    // Prevent any unwanted navigation or link opening\r\n    e.preventDefault();\r\n    e.stopPropagation();\r\n  };\r\n\r\n  return (\r\n    <div className=\"relative w-full max-w-[13rem]\">\r\n      <div className=\"absolute inset-y-0 right-0 flex items-center pr-1.5  cursor-pointer\">\r\n        {searchQuery ? (\r\n          <button onClick={() => setSearchQuery('')} className=\"focus:outline-none border-[1.5px] border-[#00000080] rounded-full\">\r\n            <IoCloseOutline size={20} className=\"text-gray-500 hover:text-gray-700\" />\r\n          </button>\r\n        ) : (\r\n          <svg width=\"30\" height=\"30\" viewBox=\"0 0 31 30\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n            <rect x=\"0.23999\" width=\"30\" height=\"30\" rx=\"15\" fill=\"url(#paint0_linear_679_1643)\" />\r\n            <path fillRule=\"evenodd\" clipRule=\"evenodd\" d=\"M9.23999 14C9.23999 11.2386 11.4786 9 14.24 9C17.0014 9 19.24 11.2386 19.24 14C19.24 16.7614 17.0014 19 14.24 19C11.4786 19 9.23999 16.7614 9.23999 14ZM14.24 7C10.374 7 7.23999 10.134 7.23999 14C7.23999 17.866 10.374 21 14.24 21C15.8159 21 17.2702 20.4792 18.4401 19.6004C18.4681 19.6376 18.499 19.6733 18.5329 19.7071L21.5329 22.7071C21.9234 23.0976 22.5566 23.0976 22.9471 22.7071C23.3376 22.3166 23.3376 21.6834 22.9471 21.2929L19.9471 18.2929C19.9132 18.259 19.8776 18.2281 19.8404 18.2001C20.7192 17.0302 21.24 15.5759 21.24 14C21.24 10.134 18.106 7 14.24 7Z\" fill=\"white\" />\r\n            <defs>\r\n              <linearGradient id=\"paint0_linear_679_1643\" x1=\"0.23999\" y1=\"15\" x2=\"30.24\" y2=\"15\" gradientUnits=\"userSpaceOnUse\">\r\n                <stop stopColor=\"#2569E8\" />\r\n                <stop offset=\"1\" stopColor=\"#1951B8\" />\r\n              </linearGradient>\r\n            </defs>\r\n          </svg>\r\n        )}\r\n      </div>\r\n      <input\r\n        ref={inputRef}\r\n        type=\"text\"\r\n        placeholder=\"Search apps...\"\r\n        value={searchQuery}\r\n        onChange={handleSearchChange}\r\n        onKeyDown={handleKeyDown}\r\n        onClick={handleInputClick}\r\n        className=\"searchinput pl-4 pr-10 py-2.5 lg:py-2 bg-white rounded-full text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:bg-white w-full border lg:border-none\"\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SearchBar;", "import React from 'react';\r\nimport SearchBar from './SearchBar';\r\nimport logo from '../assets/ai-pro-logo.png';\r\nimport aiArt from '../assets/ai-art-i.png';\r\nimport aiChat from '../assets/ai-chat-i.png';\r\nimport aiPopular from '../assets/ai-popular-i.png';\r\n\r\nconst Navigation = ({ categories, activeCategory, setActiveCategory, searchQuery, setSearchQuery }) => {\r\n  const mockupCategories = [\r\n    { id: 'all', name: 'All Apps', icon: logo },\r\n    { id: 'popular', name: 'Most Popular', icon: aiPopular },\r\n    { id: 'chatbots', name: 'AI Chatbots', icon: aiChat },\r\n    { id: 'arts', name: 'AI Arts', icon: aiArt },\r\n  ];\r\n\r\n  const displayCategories = categories || mockupCategories;\r\n\r\n  const handleCategoryClick = (categoryId) => {\r\n    if (categoryId !== 'all') {\r\n      setSearchQuery('');\r\n    }\r\n    setActiveCategory(categoryId);\r\n  };\r\n\r\n  return (\r\n    <div className={`lg:bg-[#F5F5F5] rounded-full py-2 lg:px-2 my-8 ${!searchQuery ? '' : 'mb-12'} ${activeCategory === 'popular' ? '' : 'mb-16'} flex flex-wrap items-center justify-center gap-x-[0.13rem] gap-y-[0.5rem] lg:gap-4 w-fit justify-self-center max-w-[800px] relative z-10 mx-auto`}>\r\n      {displayCategories.map((category) => (\r\n        <button\r\n          key={category.id}\r\n          onClick={() => handleCategoryClick(category.id)}\r\n          className={`px-3 py-2 rounded-full text-sm font-medium inline-flex items-center border lg:border-none ${\r\n                activeCategory === category.id\r\n                  ? 'bg-gradient-to-r from-[#3E53B8] via-[#3073D5] to-[#2393F1] text-white'\r\n                  : 'bg-white text-[#3073D5] hover:ring-1 hover:ring-[#2393F1]'\r\n              }`}\r\n        >\r\n          <img\r\n            src={category.icon}\r\n            alt={category.name}\r\n            className={`mr-2 w-5 h-5 object-contain ${\r\n              activeCategory === category.id\r\n              ? 'filter brightness-[100]'\r\n              : ''\r\n            }`}\r\n          />\r\n          {category.name}\r\n        </button>\r\n      ))}\r\n\r\n      <div className=\"\">\r\n        <SearchBar \r\n          searchQuery={searchQuery} \r\n          setSearchQuery={setSearchQuery} \r\n          activeCategory={activeCategory}\r\n          setActiveCategory={setActiveCategory}\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Navigation;\r\n", "import React from 'react';\r\n\r\nconst ToolIcon = ({ type }) => {\r\n    return <img src={type} alt=\"Tool Icon\" className=\"w-[62px] h-[62px] rounded-lg mx-auto\" />;\r\n};\r\n\r\nexport default ToolIcon;", "import React from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport ToolIcon from './ToolIcon';\r\nimport '../assets/styles.css';\r\n\r\nconst ToolCard = ({ tool, isLoading, activeCategory, fnRedirectApp, fnReturnAppHref }) => {\r\n  if (isLoading) {\r\n    return (\r\n      <div className=\"bg-gray-200 relative rounded-[14px] shadow-sm px-10 md:px-28 xl:px-32 border animate-pulse h-[120px] w-full\">\r\n      </div>\r\n    );\r\n  }\r\n\r\n  const handleClick = () => {\r\n    if (tool.link) {\r\n      fnRedirectApp(tool.link);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <motion.a \r\n      key={activeCategory}\r\n      className=\"bg-white relative rounded-[14px] shadow-sm p-4 hover:shadow-[0px_4px_10.1px_0px_rgba(11,107,190,0.28)] hover:border hover:border-blue-500 hover:cursor-pointer transition-shadow duration-200 border h-[150px] md:h-[120px] xl:h-[100px] w-full flex flex-col justify-center focus-visible:outline focus-visible:outline-2 focus-visible:outline-blue-500 focus-visible:outline-offset-2 group\"\r\n      initial={{ opacity: 0 }}\r\n      animate={{ opacity: 1 }}\r\n      exit={{ opacity: 0 }}\r\n      transition={{ duration: 0.1, ease: \"easeInOut\" }}\r\n      whileHover={{ scale: 1.02 }}\r\n      whileTap={{ scale: 0.98 }}\r\n      aria-label={tool.name}\r\n      href={fnReturnAppHref(tool.link)}\r\n      target={fnReturnAppHref(tool.link) === tool.link ? \"_blank\" : \"\"}\r\n      onClick={handleClick}\r\n    >\r\n      <div className=\"md:flex items-start justify-items-center md:justify-normal\">\r\n        <div className=\"flex-shrink-0\">\r\n          <ToolIcon type={tool.icon} />\r\n        </div>\r\n        <div className=\"place-items-center md:place-items-start md:ml-3 flex flex-col w-full text-center md:text-start\">\r\n          <div className=\"flex items-center\">\r\n            <h3 className=\"font-bold sm:font-semibold text-gray-800 text-xs sm:text-sm md:max-w-[180px] lg:max-w-[120px] xl:max-w-[180px]\">{tool.name}</h3>\r\n            {tool.isPopular && (\r\n              <div className=\"badge absolute top-[-12px] left-0 w-fit !mx-auto md:mx-0 md:left-auto inline-flex items-center bg-[#39BD5C] text-white text-xs px-2 py-1 rounded-full group-focus-visible:outline group-focus-visible:outline-2 group-focus-visible:outline-blue-500 group-focus-visible:outline-offset-2\">\r\n                <div className='mb-0.5 mr-1'>\r\n                  <svg width=\"11\" height=\"11\" viewBox=\"0 0 11 11\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                    <path d=\"M5.5 0.5L6.73483 4.30041H10.7308L7.49799 6.64919L8.73282 10.4496L5.5 8.10081L2.26718 10.4496L3.50201 6.64919L0.269189 4.30041H4.26517L5.5 0.5Z\" fill=\"white\" />\r\n                  </svg>\r\n                </div>\r\n                <span>\r\n                  Most Popular\r\n                </span>\r\n                <div className=\"shine hidden md:block focus:outline-none\"></div>\r\n              </div>\r\n            )}\r\n            {tool.isNew && (\r\n              <div className=\"badge absolute top-[-12px] left-0 w-fit !mx-auto md:mx-0 md:left-auto inline-flex items-center bg-gradient-to-r from-[#2467E4] to-[#1C56C0] text-white text-xs px-2 py-1 rounded-full group-focus-visible:outline group-focus-visible:outline-2 group-focus-visible:outline-blue-500 group-focus-visible:outline-offset-2\">\r\n                <div className='mb-0.5 mr-1'>\r\n                  <svg width=\"12\" height=\"12\" viewBox=\"0 0 12 12\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                    <path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M4.49997 2.25C4.58146 2.25002 4.66071 2.27657 4.72576 2.32565C4.7908 2.37473 4.83809 2.44365 4.86047 2.522L5.26697 3.945C5.3545 4.25146 5.5187 4.53055 5.74406 4.75591C5.96942 4.98128 6.24851 5.14547 6.55497 5.233L7.97797 5.6395C8.05627 5.66193 8.12514 5.70924 8.17416 5.77428C8.22319 5.83932 8.2497 5.91855 8.2497 6C8.2497 6.08145 8.22319 6.16068 8.17416 6.22572C8.12514 6.29076 8.05627 6.33807 7.97797 6.3605L6.55497 6.767C6.24851 6.85453 5.96942 7.01872 5.74406 7.24409C5.5187 7.46945 5.3545 7.74854 5.26697 8.055L4.86047 9.478C4.83804 9.5563 4.79073 9.62516 4.72569 9.67419C4.66065 9.72321 4.58142 9.74973 4.49997 9.74973C4.41853 9.74973 4.33929 9.72321 4.27425 9.67419C4.20921 9.62516 4.1619 9.5563 4.13947 9.478L3.73297 8.055C3.64545 7.74854 3.48125 7.46945 3.25589 7.24409C3.03052 7.01872 2.75143 6.85453 2.44497 6.767L1.02197 6.3605C0.943676 6.33807 0.87481 6.29076 0.825785 6.22572C0.776761 6.16068 0.750244 6.08145 0.750244 6C0.750244 5.91855 0.776761 5.83932 0.825785 5.77428C0.87481 5.70924 0.943676 5.66193 1.02197 5.6395L2.44497 5.233C2.75143 5.14547 3.03052 4.98128 3.25589 4.75591C3.48125 4.53055 3.64545 4.25146 3.73297 3.945L4.13947 2.522C4.16185 2.44365 4.20914 2.37473 4.27419 2.32565C4.33923 2.27657 4.41849 2.25002 4.49997 2.25ZM8.99997 0.75C9.08363 0.749953 9.1649 0.777882 9.23086 0.829343C9.29682 0.880804 9.34367 0.952843 9.36397 1.034L9.49297 1.552C9.61097 2.022 9.97797 2.389 10.448 2.507L10.966 2.636C11.0473 2.65614 11.1195 2.70293 11.1711 2.7689C11.2228 2.83487 11.2508 2.91623 11.2508 3C11.2508 3.08377 11.2228 3.16513 11.1711 3.2311C11.1195 3.29707 11.0473 3.34386 10.966 3.364L10.448 3.493C9.97797 3.611 9.61097 3.978 9.49297 4.448L9.36397 4.966C9.34383 5.04731 9.29705 5.11954 9.23107 5.17117C9.1651 5.22279 9.08374 5.25084 8.99997 5.25084C8.9162 5.25084 8.83485 5.22279 8.76887 5.17117C8.7029 5.11954 8.65611 5.04731 8.63597 4.966L8.50697 4.448C8.44928 4.21722 8.32995 4.00645 8.16174 3.83824C7.99352 3.67003 7.78276 3.55069 7.55197 3.493L7.03397 3.364C6.95266 3.34386 6.88043 3.29707 6.82881 3.2311C6.77718 3.16513 6.74913 3.08377 6.74913 3C6.74913 2.91623 6.77718 2.83487 6.82881 2.7689C6.88043 2.70293 6.95266 2.65614 7.03397 2.636L7.55197 2.507C7.78276 2.44931 7.99352 2.32997 8.16174 2.16176C8.32995 1.99355 8.44928 1.78279 8.50697 1.552L8.63597 1.034C8.65627 0.952843 8.70313 0.880804 8.76909 0.829343C8.83504 0.777882 8.91632 0.749953 8.99997 0.75ZM8.24997 7.5C8.32873 7.49995 8.40551 7.52471 8.4694 7.57075C8.5333 7.61679 8.58109 7.68178 8.60597 7.7565L8.80297 8.348C8.87797 8.5715 9.05297 8.7475 9.27697 8.822L9.86847 9.0195C9.94296 9.04451 10.0077 9.09227 10.0536 9.15606C10.0994 9.21985 10.1241 9.29643 10.1241 9.375C10.1241 9.45357 10.0994 9.53015 10.0536 9.59394C10.0077 9.65773 9.94296 9.70549 9.86847 9.7305L9.27697 9.928C9.05347 10.003 8.87747 10.178 8.80297 10.402L8.60547 10.9935C8.58047 11.068 8.5327 11.1327 8.46891 11.1786C8.40513 11.2245 8.32854 11.2491 8.24997 11.2491C8.17141 11.2491 8.09482 11.2245 8.03103 11.1786C7.96725 11.1327 7.91948 11.068 7.89447 10.9935L7.69697 10.402C7.66014 10.2916 7.59814 10.1914 7.51588 10.1091C7.43361 10.0268 7.33333 9.96483 7.22297 9.928L6.63147 9.7305C6.55699 9.70549 6.49224 9.65773 6.44637 9.59394C6.4005 9.53015 6.37582 9.45357 6.37582 9.375C6.37582 9.29643 6.4005 9.21985 6.44637 9.15606C6.49224 9.09227 6.55699 9.04451 6.63147 9.0195L7.22297 8.822C7.44647 8.747 7.62247 8.572 7.69697 8.348L7.89447 7.7565C7.91933 7.68186 7.96704 7.61693 8.03084 7.57089C8.09464 7.52486 8.1713 7.50006 8.24997 7.5Z\" fill=\"white\" />\r\n                    </svg>\r\n                  </div>\r\n                  <span>\r\n                    New\r\n                  </span>\r\n                  <div className=\"shine hidden md:block focus:outline-none\"></div>\r\n                </div>\r\n              )}\r\n            </div>\r\n            <p className=\"text-xs sm:text-sm text-gray-600 mt-1\">{tool.description}</p>\r\n          </div>\r\n        </div>\r\n      </motion.a>\r\n  );\r\n};\r\n\r\nexport default ToolCard;\r\n", "import React from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport ToolCard from './ToolCard';\r\nimport aiChat from '../assets/ai-chat-i.png';\r\nimport aiArt from '../assets/ai-art-i.png';\r\nimport { returnAppHref, redirectApp } from '../../core/utils/app';\r\n\r\n\r\nconst ToolsSection = ({ title, icon, iconColor, tools, searchQuery, isLoading, activeCategory, auth }) => {\r\n \r\n  const fnRedirectApp = (url) => {\r\n    redirectApp(auth, url);\r\n  };\r\n\r\n  const fnReturnAppHref = (url) => {\r\n    return returnAppHref(auth, url);\r\n  };\r\n\r\n  const filteredTools = tools.filter((tool) =>\r\n    tool.name.toLowerCase().includes(searchQuery.toLowerCase()) ||\r\n    tool.description.toLowerCase().includes(searchQuery.toLowerCase())\r\n  );\r\n\r\n  // Animation variants\r\n  const containerVariants = {\r\n    hidden: { opacity: 0 },\r\n    visible: {\r\n      opacity: 1,\r\n    }\r\n  };\r\n\r\n  const itemVariants = {\r\n    hidden: { opacity: 0 },\r\n    visible: {\r\n      opacity: 1,\r\n      transition: { duration: 0.5 }\r\n    }\r\n  };\r\n\r\n  const shouldShowHeader = searchQuery ? true : activeCategory === 'all';\r\n\r\n  return (\r\n    <motion.div\r\n      key={activeCategory}\r\n      className=\"mb-8\"\r\n      initial=\"hidden\"\r\n      animate=\"visible\"\r\n      variants={containerVariants}\r\n    >\r\n      {shouldShowHeader && (\r\n        <motion.div\r\n          className={`flex items-center ${!searchQuery ? 'my-14 md:my-10' : 'my-8'} place-content-center`}\r\n          variants={itemVariants}\r\n        >\r\n          <SectionIcon type={icon} color={iconColor} activeCategory={activeCategory} searchQuery={searchQuery} />\r\n          <h2 className={`font-bold text-[#3073D5] z-10 ${searchQuery ? 'text-[24px]' : 'text-[36px]'}`}>\r\n            {searchQuery ? (\r\n              <>\r\n                <span className='text-[#3073D5]'>{title}</span>\r\n              </>\r\n            ) : (\r\n              title\r\n            )}\r\n          </h2>\r\n        </motion.div>\r\n      )}\r\n\r\n      {filteredTools.length > 0 ? (\r\n        <motion.div\r\n          key={activeCategory}\r\n          className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 md:gap-8 max-w-[1228px] md:px-0 mx-auto\"\r\n          variants={containerVariants}\r\n        >\r\n          {filteredTools.map((tool) => (\r\n            <motion.div\r\n              key={tool.id || tool.name}\r\n              variants={itemVariants}\r\n            >\r\n              <ToolCard tool={tool} isLoading={isLoading} activeCategory={activeCategory} fnRedirectApp={fnRedirectApp} fnReturnAppHref={fnReturnAppHref} />\r\n            </motion.div>\r\n          ))}\r\n        </motion.div>\r\n      ) : (\r\n        <motion.p\r\n          variants={itemVariants}\r\n          className=\"text-center text-gray-500\"\r\n        >\r\n          No results found for \"{searchQuery}\"\r\n        </motion.p>\r\n      )}\r\n    </motion.div>\r\n  );\r\n};\r\n\r\nconst SectionIcon = ({ type, color, activeCategory, searchQuery }) => {\r\n  return (\r\n    searchQuery ? (\r\n      null\r\n    ) : (\r\n      <motion.div\r\n        className=\"w-[36px] h-[34px] mr-2\"\r\n      >\r\n        {type === 'chat' ? <img src={aiChat} alt='Ai chatbots ' /> : type === 'art' ? <img src={aiArt} alt='Ai arts ' /> : null}\r\n      </motion.div>\r\n    )\r\n  );\r\n};\r\n\r\nexport default ToolsSection;\r\n", "import React from 'react';\r\nimport headerB from '../assets/header-b.png'\r\n\r\nconst HeaderB = () => {\r\n  return (\r\n    <div className=\"text-center px-4\">\r\n      <div className=\"relative inline-block max-w-[43rem] md:h-[132px]\">\r\n       <img src={headerB} alt=\"header background\"></img>\r\n      </div>\r\n      <p className=\"text-[16px] text-gray-600 md:max-w-[25rem] mx-auto md:mt-[20px]\">\r\n        AI you can count on to generate essays, reports, contracts, and content.\r\n      </p>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default HeaderB;", "import chatbot from '../assets/chatbot.png';\r\nimport chatpdfIcon from '../assets/chatpdf.png';\r\nimport chatpdfv2Icon from '../assets/chatpdfv2.png';\r\nimport grammarIcon from '../assets/grammar-ai.png';\r\nimport teacherIcon from '../assets/teacher-ai.png';\r\nimport tripsIcon from '../assets/trips-ai.png';\r\nimport recipeIcon from '../assets/recipe-maker.png';\r\nimport translateIcon from '../assets/translate-now.png';\r\nimport searchIcon from '../assets/search-ai.png';\r\nimport multiChatIcon from '../assets/multi-chat.png';\r\nimport sitebotIcon from '../assets/sitebot.png';\r\nimport codingIcon from '../assets/coding-ai.png';\r\nimport homeworkIcon from '../assets/homework-help.png';\r\nimport dreamPhotoIcon from '../assets/dream-photo.png';\r\nimport interiorIcon from '../assets/interior-ai.png';\r\nimport restorePhotoIcon from '../assets/restore-photo.png';\r\nimport removeBackgroundIcon from '../assets/remove-background.png';\r\nimport avatarMakerIcon from '../assets/avatar-maker-g.png';\r\nimport storyBookIcon from '../assets/story-book.png';\r\nimport fluxImageGenIcon from '../assets/flux-imagegen.png';\r\n// import moreAppsIcon from '../assets/more-apps.png';\r\nimport { getPrefixLocation } from '../../core/utils/helper';\r\nimport { redirectApp, returnAppHref } from '../../core/utils/app';\r\n\r\nconst toolLinks = {\r\n    chatpro: `https://${getPrefixLocation()}chatpro.ai-pro.org/chat`,\r\n    chatpdf: `https://${getPrefixLocation()}chatpdf.ai-pro.org/`,\r\n    chatpdfv2: `https://${getPrefixLocation()}chatpdfv2.ai-pro.org/`,\r\n    convert2english: `https://${getPrefixLocation()}app.ai-pro.org/convert-to-proper-english`,\r\n    teacher_ai: `https://${getPrefixLocation()}teacher.ai-pro.org/`,\r\n    trips_ai: `https://${getPrefixLocation()}chatlibrary.ai-pro.org/ai/travel`,\r\n    recipe_maker: `https://${getPrefixLocation()}chatlibrary.ai-pro.org/ai/recipe`,\r\n    translate_now: `https://${getPrefixLocation()}chatlibrary.ai-pro.org/ai/writing/translate`,\r\n    searchai: `https://${getPrefixLocation()}search.ai-pro.org/`,\r\n    multillm: `https://${getPrefixLocation()}multillm.ai-pro.org/`,\r\n    sitebot: `https://${getPrefixLocation()}sitebot.ai-pro.org/`,\r\n    codingai: `https://${getPrefixLocation()}coding.ai-pro.org/`,\r\n    homework: `https://${getPrefixLocation()}tutor.ai-pro.org/`,\r\n    txt2img: `https://${getPrefixLocation()}app.ai-pro.org/dream-photo`,\r\n    interiorgpt: `https://${getPrefixLocation()}interiorgpt.ai-pro.org/`,\r\n    restorephoto: `https://${getPrefixLocation()}restorephotos.ai-pro.org/`,\r\n    removebg: `https://${getPrefixLocation()}clearbg.ai-pro.org/`,\r\n    avatar_make: `https://${getPrefixLocation()}avatarmaker.ai-pro.org/`,\r\n    storybook: `https://${getPrefixLocation()}storybook.ai-pro.org/`,\r\n    flux: `https://${getPrefixLocation()}flux.ai-pro.org/`,\r\n};\r\n\r\nconst toolDataTemplate = [\r\n    { id: 'chatbot-pro', name: 'Chatbot Pro', description: 'Advanced features of GPTs', icon: chatbot, key: 'chatpro', category: 'chatbots', isPopular: true, isNew: false },\r\n    { id: 'chatpdfv2', name: 'ChatPDF V2', description: 'Upload PDF, Ask questions, Get insights', icon: chatpdfv2Icon, key: 'chatpdfv2', category: 'chatbots', isPopular: false, isNew: true },\r\n    { id: 'chatpdf', name: 'ChatPDF', description: 'Upload PDF, Ask questions, Get insights', icon: chatpdfIcon, key: 'chatpdf', category: 'chatbots', isPopular: false, isNew: false },\r\n    { id: 'grammar-ai', name: 'Grammar AI', description: 'Convert texts to proper English', icon: grammarIcon, key: 'convert2english', category: 'chatbots', isPopular: false, isNew: false },\r\n    { id: 'teacher-ai', name: 'Teacher AI', description: 'Seamless Learning: Text & Speech', icon: teacherIcon, key: 'teacher_ai', category: 'chatbots', isPopular: false, isNew: false },\r\n    { id: 'trips-ai', name: 'Trips AI', description: 'AI-Generated Itineraries for Your Trip', icon: tripsIcon, key: 'trips_ai', category: 'chatbots', isPopular: false, isNew: false },\r\n    { id: 'recipe-maker', name: 'Recipe Maker', description: 'Transform Ingredients into Masterpieces', icon: recipeIcon, key: 'recipe_maker', category: 'chatbots', isPopular: false, isNew: false },\r\n    { id: 'translate-now', name: 'Translate Now', description: 'Fast Text Translation in 100+ Languages', icon: translateIcon, key: 'translate_now', category: 'chatbots', isPopular: false, isNew: false },\r\n    { id: 'search-ai', name: 'Search AI', description: 'Stay Ahead with the latest information', icon: searchIcon, key: 'searchai', category: 'chatbots', isPopular: false, isNew: false },\r\n    { id: 'multi-chat', name: 'Multi-Chat', description: 'Explore Answers with Multi-Chat', icon: multiChatIcon, key: 'multillm', category: 'chatbots', isPopular: false, isNew: false },\r\n    { id: 'sitebot', name: 'Sitebot', description: 'Analyze Documents & Build Chatbots', icon: sitebotIcon, key: 'sitebot', category: 'chatbots', isPopular: false, isNew: false },\r\n    { id: 'coding-ai', name: 'Coding AI', description: 'Speed Up Development', icon: codingIcon, key: 'codingai', category: 'chatbots', isPopular: false, isNew: false },\r\n    { id: 'homework-help', name: 'Homework Help', description: 'Your Homework Assistant', icon: homeworkIcon, key: 'homework', category: 'chatbots', isPopular: false, isNew: false },\r\n    { id: 'dream-photo', name: 'Dream Photo', description: 'Generate stunning AI art easily', icon: dreamPhotoIcon, key: 'txt2img', category: 'arts', isPopular: false, isNew: false },\r\n    { id: 'interior-ai', name: 'Interior AI', description: 'Remodel your room instantly', icon: interiorIcon, key: 'interiorgpt', category: 'arts', isPopular: false, isNew: false },\r\n    { id: 'restore-photo', name: 'Restore Photo', description: 'Restores and enhance your photos', icon: restorePhotoIcon, key: 'restorephoto', category: 'arts', isPopular: false, isNew: false },\r\n    { id: 'remove-background', name: 'Remove Background', description: 'Removes backgrounds effortlessly', icon: removeBackgroundIcon, key: 'removebg', category: 'arts', isPopular: false, isNew: false },\r\n    { id: 'avatar-maker', name: 'Avatar Maker', description: 'Turn your images into art', icon: avatarMakerIcon, key: 'avatar_make', category: 'arts', isPopular: false, isNew: false },\r\n    { id: 'story-book', name: 'Story Book', description: 'Create stunning book illustrations', icon: storyBookIcon, key: 'storybook', category: 'arts', isPopular: false, isNew: false },\r\n    { id: 'flux-imagegen', name: 'Flux ImageGen', description: 'Generate high-quality images', icon: fluxImageGenIcon, key: 'flux', category: 'arts', isPopular: false, isNew: false },\r\n    // { id: 'more-apps', name: 'More Applications', description: 'Coming soon...', icon: moreAppsIcon, key: '#', category: 'arts', isPopular: false, isNew: false },\r\n];\r\n\r\nexport const getToolsData = (auth) => {\r\n\r\n    return toolDataTemplate.map(tool => {\r\n        const baseUrl = toolLinks[tool.key] || '#';\r\n        const fullUrl = baseUrl;\r\n        \r\n        return {\r\n            ...tool,\r\n            link: fullUrl,\r\n            target: returnAppHref(auth, fullUrl) === fullUrl ? '_blank' : '',\r\n            onClick: (e) => {\r\n                e.preventDefault();\r\n                if (returnAppHref(auth, fullUrl) !== fullUrl) {\r\n                    redirectApp(auth, fullUrl);\r\n                } else {\r\n                    window.open(fullUrl, '_blank');\r\n                }\r\n            }\r\n        };\r\n    });\r\n};\r\n", "var _path;\nconst _excluded = [\"title\", \"titleId\"];\nfunction _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\nfunction _objectWithoutProperties(e, t) { if (null == e) return {}; var o, r, i = _objectWithoutPropertiesLoose(e, t); if (Object.getOwnPropertySymbols) { var n = Object.getOwnPropertySymbols(e); for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]); } return i; }\nfunction _objectWithoutPropertiesLoose(r, e) { if (null == r) return {}; var t = {}; for (var n in r) if ({}.hasOwnProperty.call(r, n)) { if (-1 !== e.indexOf(n)) continue; t[n] = r[n]; } return t; }\nimport * as React from \"react\";\nfunction SvgClaudeLogoOnly(_ref, svgRef) {\n  let {\n      title,\n      titleId\n    } = _ref,\n    props = _objectWithoutProperties(_ref, _excluded);\n  return /*#__PURE__*/React.createElement(\"svg\", _extends({\n    width: 34,\n    height: 33,\n    viewBox: \"0 0 34 33\",\n    fill: \"none\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, _path || (_path = /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M7.12551 21.9381L13.6155 18.2985L13.7255 17.9823L13.6155 17.8062H13.3006L12.2144 17.7402L8.50463 17.6399L5.28851 17.5065L2.17276 17.3387L1.38763 17.1724L0.650635 16.203L0.72626 15.719L1.38626 15.2776L2.32951 15.3601L4.41951 15.5017L7.55176 15.719L9.82326 15.8524L13.1906 16.203H13.7255L13.8011 15.9871L13.6169 15.8524L13.4753 15.719L10.233 13.5245L6.72401 11.2035L4.88701 9.867L3.89151 9.19187L3.39101 8.55663L3.17376 7.17063L4.07576 6.17788L5.28713 6.26038L5.59651 6.34425L6.82438 7.2875L9.44788 9.317L12.873 11.8374L13.3749 12.2554L13.5743 12.1138L13.6004 12.0134L13.3749 11.6366L11.5118 8.27338L9.52351 4.84963L8.63801 3.43062L8.40426 2.5795C8.3152 2.2526 8.26716 1.91589 8.26126 1.57712L9.28976 0.18425L9.85763 0L11.2271 0.18425L11.8046 0.68475L12.6571 2.629L14.0349 5.69387L16.173 9.86012L16.8 11.0949L17.1341 12.2389L17.2593 12.5895H17.4765V12.3888L17.6525 10.043L17.9784 7.16237L18.2946 3.45675L18.4046 2.41175L18.9216 1.1605L19.9488 0.484L20.7518 0.869L21.4118 1.81088L21.3196 2.42138L20.9264 4.9665L20.1578 8.95813L19.6573 11.6284H19.9488L20.2829 11.2956L21.6373 9.49988L23.9088 6.66187L24.9125 5.53438L26.0813 4.29138L26.8334 3.69875H28.2538L29.2988 5.25112L28.8313 6.85438L27.3683 8.7065L26.1569 10.2767L24.4189 12.6143L23.3326 14.4843L23.433 14.6355L23.6915 14.608L27.6185 13.7747L29.7401 13.3897L32.2715 12.9566L33.4169 13.4901L33.542 14.0333L33.091 15.1429L30.3836 15.8111L27.2088 16.4464L22.4801 17.5643L22.4224 17.6055L22.4898 17.6894L24.6196 17.8901L25.5299 17.9396H27.7601L31.9126 18.249L32.9989 18.9667L33.6506 19.844L33.542 20.5109L31.8714 21.3634L29.6164 20.8285L24.3515 19.5772L22.5475 19.1249H22.2973V19.2761L23.8001 20.7446L26.5584 23.2334L30.0083 26.4371L30.1829 27.2319L29.7401 27.8575L29.2726 27.7901L26.2408 25.5118L25.0706 24.4846L22.4224 22.2571H22.2464V22.4909L22.8569 23.3833L26.0813 28.2246L26.249 29.7096L26.0153 30.195L25.1793 30.4879L24.2608 30.3201L22.3715 27.6732L20.4259 24.6936L18.8543 22.022L18.6618 22.132L17.735 32.1063L17.3005 32.615L16.2981 33L15.4635 32.3661L15.0208 31.339L15.4635 29.3095L15.9984 26.664L16.4315 24.5602L16.8248 21.9478L17.0585 21.0788L17.042 21.021L16.8495 21.0457L14.8778 23.7504L11.8803 27.7998L9.50701 30.3366L8.93776 30.5621L7.95188 30.0534L8.04401 29.1431L8.59538 28.3333L11.8789 24.1587L13.8589 21.571L15.1376 20.0778L15.1294 19.8605H15.0538L6.33213 25.52L4.77838 25.7207L4.10876 25.0938L4.19263 24.068L4.51026 23.7339L7.13376 21.9299L7.12551 21.9381Z\",\n    fill: \"#D97757\"\n  })));\n}\nconst ForwardRef = /*#__PURE__*/React.forwardRef(SvgClaudeLogoOnly);\nexport default __webpack_public_path__ + \"static/media/claude-logo-only.ec4112bf2e8500f31ca055fcd0ee88f4.svg\";\nexport { ForwardRef as ReactComponent };", "var _path;\nconst _excluded = [\"title\", \"titleId\"];\nfunction _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\nfunction _objectWithoutProperties(e, t) { if (null == e) return {}; var o, r, i = _objectWithoutPropertiesLoose(e, t); if (Object.getOwnPropertySymbols) { var n = Object.getOwnPropertySymbols(e); for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]); } return i; }\nfunction _objectWithoutPropertiesLoose(r, e) { if (null == r) return {}; var t = {}; for (var n in r) if ({}.hasOwnProperty.call(r, n)) { if (-1 !== e.indexOf(n)) continue; t[n] = r[n]; } return t; }\nimport * as React from \"react\";\nfunction SvgChatgptLogoOnly(_ref, svgRef) {\n  let {\n      title,\n      titleId\n    } = _ref,\n    props = _objectWithoutProperties(_ref, _excluded);\n  return /*#__PURE__*/React.createElement(\"svg\", _extends({\n    width: 46,\n    height: 45,\n    viewBox: \"0 0 46 45\",\n    fill: \"none\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, _path || (_path = /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M45.1836 23.17C44.6335 21.1597 43.5332 19.3909 42.024 17.9986C43.257 15.164 43.0711 11.9103 41.4945 9.23739C39.9821 6.67616 37.5412 4.84351 34.6208 4.07769C32.5648 3.53949 30.4467 3.58629 28.457 4.17129C26.5619 1.70578 23.6111 0.23584 20.4417 0.23584C15.1075 0.23584 10.6334 3.86922 9.44281 8.73536C9.43194 8.73642 9.42216 8.73217 9.41128 8.73323C6.25602 9.11614 3.53566 10.8807 1.94716 13.5717C0.43476 16.1329 0.0335568 19.1175 0.816393 21.9744C1.36764 23.9858 2.46796 25.7567 3.97927 27.149C2.7463 29.9847 2.92461 33.2266 4.50551 35.907C6.01791 38.4682 8.45883 40.3009 11.3792 41.0667C12.3534 41.322 13.3439 41.4486 14.3268 41.4486C15.4174 41.4486 16.496 41.2773 17.5419 40.9699C19.4359 43.4397 22.3748 44.9086 25.5584 44.9086C30.898 44.9086 35.3743 41.2688 36.5616 36.3963C39.6929 36.024 42.4665 34.2626 44.054 31.5727C45.5653 29.0115 45.9665 26.0269 45.1836 23.17ZM34.0576 6.13158C36.418 6.74955 38.3893 8.23119 39.6103 10.3C40.7508 12.2326 40.9618 14.5545 40.2289 16.6467C40.1213 16.5807 40.0191 16.5073 39.9093 16.4446L29.9401 10.8137C29.6008 10.6233 29.1812 10.6254 28.8419 10.8201L17.7539 17.2455L17.6974 12.3453L27.1164 7.02609C29.2334 5.83057 31.6982 5.51467 34.0576 6.13158ZM28.1504 19.5887L28.2178 25.443L23.0685 28.4265L17.8507 25.5557L17.7833 19.7014L22.9326 16.7179L28.1504 19.5887ZM11.2955 11.3104C11.2955 6.37621 15.3989 2.36311 20.4417 2.36311C22.7347 2.36311 24.8908 3.3374 26.376 5.00944C26.2618 5.069 26.1433 5.11899 26.0302 5.18281L16.06 10.8137C15.7207 11.0062 15.512 11.3636 15.5163 11.7465L15.6609 24.3517L11.2955 21.95V11.3104ZM2.91592 21.4245C2.28421 19.1164 2.60931 16.7041 3.83031 14.6353C4.98282 12.6825 6.89968 11.3657 9.14055 10.9403C9.1362 11.0637 9.12098 11.1849 9.12098 11.3104V22.5722C9.12098 22.9562 9.333 23.3104 9.67549 23.4997L20.9059 29.6794L16.596 32.1768L7.17802 26.8565C5.06219 25.6621 3.54871 23.7337 2.91592 21.4245ZM11.9425 39.0128C9.58198 38.3949 7.61076 36.9132 6.38975 34.8444C5.24485 32.9044 5.03175 30.5888 5.76674 28.4945C5.87656 28.5615 5.97985 28.636 6.09184 28.6998L16.061 34.3307C16.2285 34.4254 16.4166 34.4732 16.6047 34.4732C16.7971 34.4732 16.9885 34.4232 17.1592 34.3243L28.2472 27.8989L28.3037 32.7991L18.8847 38.1183C16.7689 39.3149 14.3029 39.6329 11.9425 39.0128ZM34.7045 33.834C34.7045 38.7682 30.6022 42.7813 25.5584 42.7813C23.2544 42.7813 21.1071 41.8038 19.6229 40.1286C19.7371 40.069 19.8578 40.0243 19.9709 39.9605L29.9412 34.3296C30.2804 34.1371 30.4892 33.7797 30.4848 33.3968L30.3391 20.7927L34.7045 23.1944V33.834ZM42.1697 30.5091C41.0216 32.4555 39.0819 33.7914 36.8595 34.2137C36.8638 34.0861 36.8791 33.9616 36.8791 33.834V22.5722C36.8791 22.1882 36.667 21.834 36.3245 21.6447L25.0941 15.465L29.4041 12.9676L38.822 18.2879C40.9378 19.4823 42.4513 21.4118 43.0841 23.7199C43.7158 26.028 43.3907 28.4403 42.1697 30.5091Z\",\n    fill: \"black\"\n  })));\n}\nconst ForwardRef = /*#__PURE__*/React.forwardRef(SvgChatgptLogoOnly);\nexport default __webpack_public_path__ + \"static/media/chatgpt-logo-only.ac6992898e7a3878e7adc89701239a07.svg\";\nexport { ForwardRef as ReactComponent };", "var _path;\nconst _excluded = [\"title\", \"titleId\"];\nfunction _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\nfunction _objectWithoutProperties(e, t) { if (null == e) return {}; var o, r, i = _objectWithoutPropertiesLoose(e, t); if (Object.getOwnPropertySymbols) { var n = Object.getOwnPropertySymbols(e); for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]); } return i; }\nfunction _objectWithoutPropertiesLoose(r, e) { if (null == r) return {}; var t = {}; for (var n in r) if ({}.hasOwnProperty.call(r, n)) { if (-1 !== e.indexOf(n)) continue; t[n] = r[n]; } return t; }\nimport * as React from \"react\";\nfunction SvgDeepseekLogoOnly(_ref, svgRef) {\n  let {\n      title,\n      titleId\n    } = _ref,\n    props = _objectWithoutProperties(_ref, _excluded);\n  return /*#__PURE__*/React.createElement(\"svg\", _extends({\n    width: 39,\n    height: 27,\n    viewBox: \"0 0 39 27\",\n    fill: \"none\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, _path || (_path = /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M37.9495 2.6815C37.5474 2.49894 37.3732 2.84786 37.1389 3.026C37.0581 3.08342 36.9901 3.1585 36.922 3.22623C36.333 3.8107 35.6458 4.19349 34.7481 4.14785C33.4355 4.08012 32.3146 4.4629 31.3234 5.3963C31.1128 4.24501 30.413 3.55895 29.349 3.11728C28.7917 2.88761 28.2281 2.65941 27.837 2.16032C27.5647 1.80552 27.4902 1.40948 27.3541 1.02081C27.267 0.785255 27.1799 0.54528 26.8902 0.50553C26.5735 0.45989 26.45 0.705754 26.3265 0.911867C25.8309 1.75399 25.6394 2.6815 25.6584 3.62078C25.7011 5.73492 26.6606 7.41916 28.5685 8.61609C28.7854 8.753 28.8408 8.8914 28.7727 9.09162C28.6429 9.50385 28.4877 9.90429 28.3516 10.318C28.2645 10.5815 28.1347 10.6375 27.8307 10.5241C26.8035 10.1136 25.8705 9.52392 25.082 8.78687C23.7252 7.56785 22.4997 6.22223 20.9702 5.16811C20.6158 4.92462 20.252 4.69333 19.8793 4.47468C18.3198 3.06575 20.0851 1.90857 20.4936 1.77165C20.9211 1.62738 20.6409 1.13565 19.2602 1.14154C17.8796 1.14743 16.6161 1.57585 15.0059 2.14855C14.7666 2.23381 14.5204 2.30125 14.2697 2.35024C12.7649 2.08662 11.226 2.03599 9.70502 2.20008C6.72049 2.50925 4.33762 3.82248 2.58491 6.06175C0.479116 8.75301 -0.0164571 11.8123 0.589947 15.0012C1.22802 18.3638 3.07415 21.1478 5.90984 23.3237C8.85162 25.5807 12.2383 26.6863 16.1031 26.4743C18.4496 26.3492 21.0636 26.0562 24.0101 23.736C24.7543 24.0805 25.5333 24.2174 26.8284 24.3204C27.8259 24.4073 28.7854 24.2763 29.528 24.132C30.6917 23.9023 30.6109 22.8997 30.1914 22.7172C26.7793 21.239 27.5282 21.8412 26.8458 21.3539C28.5811 19.4459 31.1936 17.4642 32.2164 11.0438C32.2956 10.5329 32.2275 10.212 32.2164 9.79977C32.2101 9.54949 32.2718 9.45084 32.5806 9.42287C33.4372 9.34024 34.2682 9.10268 35.0268 8.72356C37.2371 7.60024 38.13 5.757 38.3406 3.5457C38.3723 3.20709 38.3343 2.85964 37.9495 2.6815ZM18.6855 22.5832C15.378 20.1658 13.7741 19.3693 13.1123 19.4032C12.4916 19.4385 12.604 20.0966 12.7402 20.5265C12.8827 20.9505 13.0679 21.242 13.3276 21.6145C13.5081 21.8603 13.6316 22.2269 13.1487 22.5022C12.0831 23.1147 10.2323 22.2961 10.1452 22.2564C7.9903 21.0756 6.18692 19.518 4.91869 17.3877C3.69322 15.3369 2.98073 13.1373 2.86357 10.7891C2.8319 10.2208 3.01082 10.0206 3.6188 9.91755C4.41696 9.77552 5.23476 9.75612 6.03967 9.86013C9.41527 10.3195 12.2874 11.7225 14.6972 13.9441C16.0715 15.2102 17.1117 16.7222 18.1836 18.2004C19.3236 19.7698 20.5491 21.2656 22.1102 22.4905C22.6612 22.9203 23.0998 23.2472 23.5209 23.4872C22.2511 23.6197 20.1326 23.6491 18.6855 22.5832ZM20.2688 13.102C20.2685 13.0289 20.2874 12.9568 20.3238 12.8919C20.3602 12.8271 20.413 12.7715 20.4777 12.7299C20.5424 12.6883 20.617 12.662 20.6951 12.6532C20.7732 12.6444 20.8524 12.6534 20.9259 12.6795C21.0195 12.7107 21.1004 12.7684 21.1572 12.8444C21.2139 12.9204 21.2438 13.0109 21.2425 13.1035C21.2428 13.1633 21.2302 13.2225 21.2055 13.2777C21.1809 13.3329 21.1446 13.383 21.099 13.4251C21.0533 13.4671 20.9991 13.5003 20.9394 13.5227C20.8798 13.5451 20.816 13.5562 20.7517 13.5554C20.6879 13.5556 20.6247 13.544 20.5658 13.5212C20.5069 13.4984 20.4534 13.4649 20.4086 13.4227C20.3638 13.3805 20.3285 13.3304 20.3048 13.2753C20.281 13.2202 20.2678 13.1613 20.2688 13.102ZM25.1929 15.4517C24.8762 15.5709 24.5611 15.674 24.2587 15.6872C23.8038 15.702 23.3573 15.5699 22.9953 15.3133C22.5614 14.9747 22.2511 14.7862 22.1213 14.1973C22.0765 13.9096 22.0851 13.6168 22.1466 13.3317C22.2574 12.8502 22.1339 12.5411 21.7682 12.2613C21.4721 12.0317 21.0937 11.9684 20.6789 11.9684C20.5368 11.9607 20.3989 11.9213 20.2767 11.8535C20.1026 11.774 19.9601 11.5738 20.0962 11.3265C20.1406 11.247 20.3496 11.0526 20.4002 11.0173C20.9639 10.7199 21.6146 10.8171 22.2147 11.0409C22.772 11.2529 23.1932 11.6415 23.7996 12.1922C24.4186 12.8561 24.5311 13.0402 24.8841 13.5378C25.1628 13.9279 25.4161 14.3284 25.5887 14.7862C25.6948 15.0733 25.5586 15.3074 25.1929 15.4517Z\",\n    fill: \"#4D6BFE\"\n  })));\n}\nconst ForwardRef = /*#__PURE__*/React.forwardRef(SvgDeepseekLogoOnly);\nexport default __webpack_public_path__ + \"static/media/deepseek-logo-only.d07836bc5c81f7403526679481032eb2.svg\";\nexport { ForwardRef as ReactComponent };", "import React from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport claudeBG from '../assets/claude-bg.png'\r\nimport chatgptBG from '../assets/chatgpt-bg.png'\r\nimport deepseekBG from '../assets/deepseek-bg.png'\r\nimport chatbot from '../assets/chatbot-black.png'\r\nimport dreamphoto from '../assets/dreamphoto-bw.png'\r\nimport chatpdf from '../assets/chatpdf-bw.png'\r\nimport flux from '../assets/flux-imagegen.png'\r\nimport teacherai from '../assets/teacherai-bw.png'\r\nimport avatarmakerBG from '../assets/avatarbg.png'\r\nimport chatbotBG from '../assets/chatbot-bg.png'\r\nimport dreamphotoBG from '../assets/dreamphoto-bg.png'\r\nimport chatpdfBG from '../assets/chatpdf-bg.png'\r\nimport fluxBG from '../assets/flux-bg.png'\r\nimport teacheraiBG from '../assets/teacherai-bg.png'\r\nimport claudeLOGO from './icon/claude-logo-only.svg'\r\nimport chatgptLOGO from './icon/chatgpt-logo-only.svg'\r\nimport deepseekLOGO from './icon/deepseek-logo-only.svg'\r\nimport avatarLOGO from '../assets/avatar-bw.png'\r\n\r\n\r\n\r\nconst MostPopular = ({ tools, fnRedirectApp, fnReturnAppHref, activeCategory }) => {\r\n    // console.log({tools})\r\n    const aiModels = [\r\n        {\r\n            name: \"Claude\",\r\n            description: \"Smarter AI chats\",\r\n            bgImage: claudeBG,\r\n            logo: claudeLOGO,\r\n        },\r\n        {\r\n            name: \"OpenAI GPTs\",\r\n            description: \"Advanced features of GPTs\",\r\n            bgImage: chatgptBG,\r\n            logo: chatgptLOGO,\r\n        },\r\n        {\r\n            name: \"DeepSeek\",\r\n            description: \"Powerful performance\",\r\n            bgImage: deepseekBG,\r\n            logo: deepseekLOGO,\r\n        }\r\n    ];\r\n    \r\n    const containerVariants = {\r\n        hidden: { opacity: 0 },\r\n        visible: { opacity: 1 },\r\n    };\r\n    \r\n    const itemVariants = {\r\n        hidden: { opacity: 0},\r\n        visible: { opacity: 1 },\r\n    };\r\n\r\n    const selectedApps = [\"chatbot-pro\", \"dream-photo\", \"chatpdf\", \"flux-imagegen\", \"teacher-ai\", \"avatar-maker\"];\r\n\r\n    const toolsMap = tools.reduce((acc, app) => {\r\n        acc[app.id] = app;\r\n        return acc;\r\n    }, {});\r\n    \r\n    const appMetadata = [\r\n        { id: \"chatbot-pro\", logo: chatbot, src: chatbotBG, name: \"Chatbot Pro\", desc: \"Advanced features of GPTs\" },\r\n        { id: \"dream-photo\", logo: dreamphoto, src: dreamphotoBG, name: \"Dream Photo\", desc: \"Generate stunning art easily\" },\r\n        { id: \"chatpdf\", logo: chatpdf, src: chatpdfBG, name: \"ChatPDF\", desc: \"Upload PDF, Ask & Get Insights\" },\r\n        { id: \"flux-imagegen\", logo: flux, src: fluxBG, name: \"Flux ImageGen\", desc: \"Generate high-quality images\" },\r\n        { id: \"teacher-ai\", logo: teacherai, src: teacheraiBG, name: \"Teacher AI\", desc: \"Seamless Learning: Text & Speech\" },\r\n        { id: \"avatar-maker\", logo: avatarLOGO, src: avatarmakerBG, name: \"Avatar Maker\", desc: \"Turns your images into art\" }\r\n    ];\r\n    \r\n    // Generate appData dynamically based on selectedApps\r\n    const appData = selectedApps.map(id => ({\r\n        ...appMetadata.find(app => app.id === id),\r\n        link: toolsMap[id]?.link || \"#\" \r\n    }));\r\n    \r\n\r\n    return (\r\n        <motion.div\r\n            className={`max-w-6xl mx-auto md:pb-8 py-2 md:mb-12 ${activeCategory === 'popular' ? 'px-10' : ''}`}\r\n            initial=\"hidden\"\r\n            animate=\"visible\"\r\n            variants={containerVariants}\r\n        >\r\n            <motion.div\r\n            className=\"grid grid-cols-1 max-w-[425px] lg:max-w-fit place-items-center lg:grid-cols-3 gap-10 lg:w-fit mx-auto my-20 !mt-0 lg:!mt-20 mb-14 lg:mb-32 xl:gap-x-24\"\r\n            variants={containerVariants}\r\n            initial=\"hidden\"\r\n            animate=\"visible\"\r\n        >\r\n                {aiModels.map((model, index) => {\r\n                    const link = toolsMap[\"chatbot-pro\"]?.link || \"#\";\r\n                    return (\r\n                    <motion.a\r\n                        key={index}\r\n                        href={fnReturnAppHref(link)}\r\n                        target={fnReturnAppHref(link) === link ? \"_blank\" : \"\"}\r\n                        onClick={() => link && fnRedirectApp(link)}\r\n                        rel=\"noopener noreferrer\"\r\n                        className={`bg-white hover:cursor-pointer xl:w-[340px] xl:h-[265px] shadow-[0px_4px_10.1px_0px_rgba(11,107,190,0.28)] rounded-[22px] p-4 flex flex-col justify-between transform transition-transform ${index === 1 ? 'lg:scale-110 hover:scale-[1.15] order-first lg:order-0' : index === 0 ? 'lg:order-first' : ''} ${index !== 1 ? 'hover:scale-105' : ''}`}\r\n                        variants={itemVariants}\r\n                    >\r\n                    <div>\r\n                        <div className=\"flex items-center mb-2\">\r\n                            <img src={model.bgImage} alt={`${model.name} background`} className=\"mr-2\" />\r\n                        </div>\r\n                        <div className=\"flex justify-between items-center\">\r\n                            <div className=\"flex items-center space-x-4\">\r\n                                <img src={model.logo} alt={`${model.name} logo`} className=\"w-6 md:w-10\" />\r\n                                <div className=\"flex flex-col pt-1.5\">\r\n                                    <h3 className=\"text-sm md:text-base font-semibold\">{model.name}</h3>\r\n                                    <p className=\"text-xs text-[#00000080] xl:max-w-full\">{model.description}</p>\r\n                                </div>\r\n                            </div>\r\n                            <button className=\"bg-gradient-to-r font-bold text-sm from-[#2468E6] to-[#1B54BD] hover:bg-blue-600 text-white whitespace-nowrap md:whitespace-normal py-[12px] px-[8px] md:px-3 mx-1 md:mx-0 rounded-full transition-colors\">\r\n                                Try Now\r\n                            </button>\r\n                        </div>\r\n                    </div>\r\n                </motion.a>\r\n                )})}\r\n        </motion.div>\r\n\r\n            {/* Top Applications Header */}\r\n            <motion.h2 \r\n                className=\"text-[29px] xs:text-[30px] sm:text-[40px] font-bold mb-14 lg:mb-8 text-center\"\r\n                variants={itemVariants}\r\n            >\r\n                Top Applications\r\n            </motion.h2>\r\n\r\n            {/* Applications Grid */}\r\n            <motion.div\r\n                className=\"grid grid-cols-1 max-w-[425px] lg:max-w-fit place-items-center lg:grid-cols-3 gap-10 lg:w-fit mx-auto mb-12 lg:!gap-y-[54px]\"\r\n                variants={containerVariants}\r\n            >\r\n                {appData.map((app, index) => {\r\n                    const link = app.link || \"#\";\r\n                    return (\r\n                    <motion.a\r\n                        key={index}\r\n                        href={fnReturnAppHref(link)}\r\n                        target={fnReturnAppHref(link) === link ? \"_blank\" : \"\"}\r\n                        onClick={() => link && fnRedirectApp(link)}\r\n                        rel=\"noopener noreferrer\"\r\n                        className=\"w-full focus:rounded-[10px]\"\r\n                        tabIndex={0}\r\n                    >\r\n                        <motion.div\r\n                            className=\"flex flex-col transform transition-all hover:scale-105 hover:shadow-[0px_4px_10.1px_0px_rgba(11,107,190,0.28)] cursor-pointer border rounded-[10px] p-2 ease-in-out\"\r\n                            variants={itemVariants}\r\n                        >\r\n                            <img src={app.src} alt={app.name} className=\"h-full object-cover\" />\r\n                            <div className=\"absolute bottom-2 right-2\"></div>\r\n                            <div className=\"flex items-center mt-2\">\r\n                                <div className={`p-1 mr-2 w-[62px] h-[62px] ${app.id === \"flux-imagegen\" ? 'brightness-0' : ''}`}>\r\n                                    <img src={app.logo} alt={`${app.name} icon`} className=\"filter grayscale contrast-100\" />\r\n                                </div>\r\n                                <div>\r\n                                    <h4 className=\"font-semibold text-sm leading-[1.7rem]\">{app.name}</h4>\r\n                                    <p className=\"text-xs text-gray-600\">{app.desc}</p>\r\n                                </div>\r\n                            </div>\r\n                        </motion.div>\r\n                    </motion.a>\r\n                )})}\r\n            </motion.div>\r\n        </motion.div>\r\n    );\r\n};\r\n\r\nexport default MostPopular;", "import React, { useState, useEffect } from 'react';\r\nimport HeaderA from './components/Header-a';\r\nimport Navigation from './components/Navigation';\r\nimport ToolsSection from './components/ToolsSection';\r\n// import EmptyState from './components/EmptyState';\r\nimport PopularModels from './components/Header-b';\r\nimport { getToolsData } from './data/toolsData';\r\nimport Header from '../header';\r\nimport Footer from '../footer';\r\nimport MostPopular from './components/MostPopular';\r\nimport bg from './assets/bg_a.png';\r\nimport { Auth, checkUsage } from '../core/utils/auth';\r\nimport { Helmet } from 'react-helmet';\r\nimport { GetCookie, SetCookie, RemoveCookie } from '../core/utils/cookies';\r\nimport axios from 'axios';\r\nimport { AddMoreMemberModal } from '../modal/enterprise';\r\nimport { MemberCompletePurchase } from '../modal/enterprise';\r\nimport { returnAppHref, redirectApp } from '../core/utils/app';\r\n\r\nconst api_url = process.env.REACT_APP_API_URL || \"https://start.ai-pro.org/api\";\r\n\r\nasync function checkBanner() {\r\n  const response = await axios.post(`${api_url}/get-banner`, {\r\n  }, { headers: { 'content-type': 'application/x-www-form-urlencoded' } })\r\n\r\n  const output = response.data;\r\n  if (output.success) {\r\n    const banner_data = output.data[0].banner_content;\r\n    return banner_data;\r\n  } else {\r\n    return '';\r\n  }\r\n}\r\n\r\nconst MyAccountB = () => {\r\n  const auth = Auth();\r\n  const upid = auth?.user_pid ? `?upid=${auth.user_pid}` : '';\r\n  const toolsData = getToolsData(auth);\r\n  const [usage, setUsage] = useState({});\r\n\r\n  const [downAppBanner, setDownAppBanner] = useState(false);\r\n\r\n  const [showAddMoreMember, setshowAddMoreMember] = useState(false);\r\n  const [showCompletePurchase, setShowCompletePurchase] = useState(false);\r\n  const [moreToAddMember, setMoreToAddMember] = useState(\"1\");\r\n  const [moreToAddMemberTotalAmount, setMoreToAddMemberTotalAmount] = useState(\"0\");\r\n  const [searchQuery, setSearchQuery] = useState('');\r\n  const [activeCategory, setActiveCategory] = useState('popular');\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [showPaused, setShowPaused] = useState(false);\r\n  const [showTokenMaxoutWarning, setshowTokenMaxoutWarning] = useState(current => false);\r\n  const [showTokenMaxoutFinal, setshowTokenMaxoutFinal] = useState(current => false);\r\n  const [ShowUpgrade, setShowUpgrade] = useState(false);\r\n  const [ShowExpiredEnterprise, setShowExpiredEnterprise] = useState(false);\r\n  const [entParentUserID, setentParentUserID] = useState('');\r\n  const [isEnterprise, setIsEnterprise] = useState(false);\r\n  const [loaded, setLoaded] = useState(false);\r\n\r\n\r\n  const initializeChat = () => {\r\n    if (window.initializeChatWidget) {\r\n      const chatConfig = {\r\n        token: process.env.REACT_APP_BOT_TOKEN // Assuming bot_token is available as an environment variable\r\n      };\r\n      window.initializeChatWidget(chatConfig);\r\n    } else {\r\n      console.error('initializeChatWidget is not defined.');\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    const script = document.createElement('script');\r\n    script.src = process.env.REACT_APP_CHATHEAD_URL;\r\n    script.addEventListener('load', ()=>setLoaded(true));\r\n    document.head.appendChild(script);\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    if(!loaded) return;\r\n    initializeChat();\r\n  }, [loaded]);\r\n\r\n  const fnRedirectApp = (url) => {\r\n    redirectApp(auth, url);\r\n  };\r\n\r\n  const fnReturnAppHref = (url) => {\r\n    return returnAppHref(auth, url);\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (auth) {\r\n      if (auth.expired === 'yes') {\r\n        setShowUpgrade(true);\r\n        if (auth.plan === 'enterprise') {\r\n          setShowExpiredEnterprise(true);\r\n          setentParentUserID(auth.ent_parent_user_id);\r\n        }\r\n      } else if (auth.plan === 'enterprise') {\r\n        setIsEnterprise(true);\r\n      }\r\n    }\r\n  }, [auth]);\r\n\r\n  \r\n\r\n  useEffect(() => {\r\n    const timer = setTimeout(() => setIsLoading(false), 500);\r\n    return () => clearTimeout(timer);\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    async function fetchBanner() {\r\n      const fetchedBanner = await checkBanner();\r\n      if (fetchedBanner !== undefined) {\r\n        setDownAppBanner(fetchedBanner);\r\n      }\r\n    }\r\n    fetchBanner();\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    if (\r\n      auth !== undefined &&\r\n      auth !== false\r\n    ) {\r\n      if (auth?.status === 'paused') {\r\n        setShowPaused(true);\r\n      }\r\n    }\r\n  }, [auth]);\r\n\r\n  useEffect(() => {\r\n    let max_token = usage.max_tokens;\r\n    let total_usage = usage.total_usage;\r\n    let eightyPercentOfToken = (max_token * 0.8);\r\n    //\r\n    setshowTokenMaxoutWarning(false);\r\n    setshowTokenMaxoutFinal(false);\r\n\r\n    if (max_token <= total_usage) {\r\n      setshowTokenMaxoutFinal(true);\r\n    } else if (eightyPercentOfToken <= total_usage) {\r\n      setshowTokenMaxoutWarning(true);\r\n    }\r\n  }, [usage]);\r\n\r\n  useEffect(() => {\r\n    async function getUserUsage() {\r\n      const data = await checkUsage();\r\n      setUsage(data);\r\n    }\r\n    getUserUsage();\r\n  }, []);\r\n  \r\n\r\n  useEffect(() => {\r\n    setTimeout(() => {\r\n      const isPro = GetCookie('user_plan');\r\n      const avatarMaker = GetCookie('avatarmaker');\r\n      const appUrl = GetCookie('appurl');\r\n\r\n      if (appUrl && (avatarMaker && appUrl.includes('avatar') && isPro === 'pro')) {\r\n        axios.post(`${api_url}/get-avatar`, {\r\n          folder: avatarMaker\r\n        }, { headers: { 'content-type': 'application/x-www-form-urlencoded' } }).then((res) => {\r\n          const data = res.data;\r\n          if (data.success) {\r\n            const url = data.link;\r\n            if (url !== '') {\r\n              fetch(url)\r\n                .then(response => response.blob())\r\n                .then(blob => {\r\n                  const blobUrl = URL.createObjectURL(blob);\r\n                  const link = document.createElement('a');\r\n                  link.href = blobUrl;\r\n                  link.setAttribute('download', 'highresimage.png');\r\n                  document.body.appendChild(link);\r\n                  link.click();\r\n                  document.body.removeChild(link);\r\n                });\r\n            }\r\n          }\r\n          RemoveCookie('avatarmaker', { domain: '.ai-pro.org', path: '/' });\r\n        });\r\n      }\r\n    }, 500);\r\n  }, [])\r\n\r\n  const modifiedToolsData = toolsData.map(tool => ({\r\n    ...tool,\r\n    link: tool.link + upid,\r\n  }));\r\n\r\n  const filteredTools = modifiedToolsData.filter(tool => {\r\n    const matchesSearch = tool.name.toLowerCase().includes(searchQuery.toLowerCase()) ||\r\n      tool.description.toLowerCase().includes(searchQuery.toLowerCase());\r\n    const matchesCategory = activeCategory === 'all' ||\r\n      (activeCategory === 'popular' && tool.isPopular) ||\r\n      tool.category === activeCategory;\r\n    return matchesSearch && matchesCategory;\r\n  });\r\n\r\n  const chatbotTools = filteredTools.filter(tool => tool.category === 'chatbots');\r\n  const artTools = filteredTools.filter(tool => tool.category === 'arts');\r\n\r\n  const handleResumeClick = () => {\r\n    window.location.href = '/resume';\r\n  }\r\n\r\n  const handleUpgradeClick = () => {\r\n    const DEFAULT_PPG = process.env.REACT_APP_DEFAULT_PPG ? process.env.REACT_APP_DEFAULT_PPG : \"14\";\r\n    const currency = auth.currency.toLowerCase();\r\n\r\n    SetCookie('reactivateSubscription', \"reactivateSubscription\");\r\n    SetCookie('reactivateSubscription', \"reactivateSubscription\", { path: '/' });\r\n    SetCookie('reactivateSubscription', \"reactivateSubscription\", { path: 'ai-pro.org' });\r\n    SetCookie('reactivateSubscription', \"reactivateSubscription\", { path: '.ai-pro.org' });\r\n\r\n    const emailid = process.env.REACT_APP_EMAILID || \"comeback5sys\";\r\n    SetCookie('emailid', emailid);\r\n    SetCookie('emailid', emailid, { path: '/' });\r\n    SetCookie('emailid', emailid, { path: 'ai-pro.org' });\r\n    SetCookie('emailid', emailid, { path: '.ai-pro.org' });\r\n    RemoveCookie(\"daily\");\r\n\r\n    if (currency === 'gbp') {\r\n      window.location.href = '/pricing/?ppg=24&pmt=st';\r\n    } else if (currency === 'eur') {\r\n      window.location.href = '/pricing/?ppg=26&pmt=st';\r\n    } else if (currency === 'brl') {\r\n      window.location.href = '/pricing/?ppg=28&pmt=st';\r\n    } else if (currency === 'sar') {\r\n      window.location.href = '/pricing/?ppg=30&pmt=st';\r\n    } else if (currency === 'aed') {\r\n      window.location.href = '/pricing/?ppg=32&pmt=st';\r\n    } else if (currency === 'pln') {\r\n      window.location.href = '/pricing/?ppg=73&pmt=st';\r\n    } else if (currency === 'ron') {\r\n      window.location.href = '/pricing/?ppg=76&pmt=st';\r\n    } else if (currency === 'czk') {\r\n      window.location.href = '/pricing/?ppg=79&pmt=st';\r\n    } else if (currency === 'huf') {\r\n      window.location.href = '/pricing/?ppg=82&pmt=st';\r\n    } else if (currency === 'dkk') {\r\n      window.location.href = '/pricing/?ppg=85&pmt=st';\r\n    } else if (currency === 'bgn') {\r\n      window.location.href = '/pricing/?ppg=88&pmt=st';\r\n    } else {\r\n      window.location.href = '/pricing/?ppg=' + DEFAULT_PPG;\r\n    }\r\n    return;\r\n  };\r\n\r\n  const isProMax = () => {\r\n    if (auth && auth.plan === 'promax' && auth.expired === 'no' && auth.status === 'active') return true;\r\n    return false;\r\n  }\r\n\r\n  const isEnterpriseCluter = () => {\r\n    if (auth && auth.plan === 'enterprise' && auth.plan_name?.toLowerCase() !== 'enterprise' && auth.expired === 'no' && auth.status === 'active') return true;\r\n    return false;\r\n  }\r\n\r\n  const warningBanner = () => {\r\n        return <svg width=\"25\" height=\"22\" viewBox=\"0 0 25 22\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\" className=\"flex-shrink-0\">      <rect x=\"10\" y=\"6\" width=\"5\" height=\"15\" fill=\"black\" />      <path d=\"M3.43665 22C1.90318 22 0.940189 20.3453 1.69774 19.012L10.7611 3.06049C11.5277 1.71118 13.4723 1.71118 14.2389 3.06049L23.3023 19.012C24.0598 20.3453 23.0968 22 21.5633 22H3.43665ZM12.5 19.4696C12.9855 19.4696 13.4003 19.2914 13.7445 18.9349C14.0948 18.5721 14.2699 18.1392 14.2699 17.6363C14.2699 17.1334 14.0948 16.7037 13.7445 16.3472C13.4003 15.9907 12.9855 15.8125 12.5 15.8125C12.0145 15.8125 11.5966 15.9907 11.2463 16.3472C10.9022 16.7037 10.7301 17.1334 10.7301 17.6363C10.7301 18.1392 10.9022 18.5721 11.2463 18.9349C11.5966 19.2914 12.0145 19.4696 12.5 19.4696ZM11.1799 12.4789C11.1963 13.1962 11.7825 13.7691 12.5 13.7691C13.2175 13.7691 13.8037 13.1962 13.8201 12.4789L13.9051 8.77101C13.9232 7.98239 13.2888 7.33333 12.5 7.33333C11.7112 7.33333 11.0768 7.98239 11.0949 8.77101L11.1799 12.4789Z\" fill=\"#FFB02E\" />    </svg>;\r\n  }\r\n\r\n  return (\r\n    <div style={{ display: 'flex', flexDirection: 'column', minHeight: '100vh' }}>\r\n      <Helmet>\r\n        <title>AI Pro | Member's Area</title>\r\n        <meta name=\"description\" content=\"Welcome to your Dashboard, your central hub to access key information and manage all your activities in one place. Stay organized and in control.\" />\r\n        {process.env.REACT_APP_CONSORTIUM_EMAILS && auth?.email && !process.env.REACT_APP_CONSORTIUM_EMAILS.includes(auth.email) && (\r\n          <script type=\"text/javascript\" src=\"//widget.trustpilot.com/bootstrap/v5/tp.widget.bootstrap.min.js\" async></script>\r\n        )}\r\n      </Helmet>\r\n      <Header auth={auth} setshowAddMoreMember={setshowAddMoreMember} />\r\n      <div className=\"relative z-10 w-full\">\r\n        {showPaused === true ?\r\n          <div className='px-4'>\r\n            <div className=\"sticky border border-[#FFA500] rounded-[10px] p-6 flex flex-col md:flex-row items-center justify-between bg-[#FFF4E5] mt-[125px] mx-auto w-full md:w-fit z-50 mb-[-50px] max-w-[1230px]\">\r\n              <div className='relative w-full md:w-auto'>\r\n                <div className=\"flex flex-col md:flex-row items-center text-center md:text-left gap-4\">\r\n                  <div className=\"min-w-[20px] flex items-center justify-center\">\r\n                    {warningBanner()}\r\n                  </div>\r\n                  <div className='flex-1'>\r\n                    <div className=\"font-bold text-gray-800\">Resume Your Subscription</div>\r\n                    <div className=\"text-gray-600 text-sm\">Account Update: Your subscription is currently Paused until {auth?.resumed_at}. Resume Now and enjoy uninterrupted access!</div>\r\n                  </div>\r\n                  <button className=\"bg-blue-500 text-white px-4 py-1 rounded-md hover:bg-blue-600 cursor-pointer mt-1\" onClick={handleResumeClick}>\r\n                    Resume\r\n                  </button>\r\n                </div>\r\n              </div>\r\n              <button className=\"text-gray-500/50 hover:text-gray-700 absolute top-2 right-2 md:top-0 md:right-[5px]\" onClick={() => setShowPaused(false)}>\r\n                <span className=\"text-xl\">&times;</span>\r\n              </button>\r\n            </div>\r\n          </div>\r\n          : \"\"}\r\n        {(ShowUpgrade && !ShowExpiredEnterprise && !showPaused) ?\r\n          <div className='px-4'>\r\n            <div className=\"sticky border border-[#FFA500] rounded-[10px] p-6 flex flex-col md:flex-row items-center justify-between bg-[#FFF4E5] mt-[125px] mx-auto w-full md:w-fit z-50 mb-[-50px] max-w-[1230px]\">\r\n              <div className='relative w-full md:w-auto'>\r\n                <div className=\"flex flex-col md:flex-row items-center text-center md:text-left gap-4\">\r\n                  <div className=\"min-w-[20px] flex items-center justify-center\">\r\n                    {warningBanner()}\r\n                  </div>\r\n                  <div className='flex-1'>\r\n                    <div className=\"font-bold text-gray-800\">Renew Your Subscription</div>\r\n                    <div className=\"text-gray-600 text-sm\">Your subscription has ended. Renew now to restore full access to your account.</div>\r\n                  </div>\r\n                  <button className=\"bg-blue-500 text-white px-4 py-1 rounded-md hover:bg-blue-600 cursor-pointer mt-1\" onClick={handleUpgradeClick}>\r\n                    Reactivate\r\n                  </button>\r\n                </div>\r\n              </div>\r\n              <button className=\"text-gray-500/50 hover:text-gray-700 absolute top-2 right-2 md:top-0 md:right-[5px]\" onClick={() => setShowUpgrade(false)}>\r\n                <span className=\"text-xl\">&times;</span>\r\n              </button>\r\n            </div>\r\n          </div>\r\n          : (ShowUpgrade === true && ShowExpiredEnterprise === true && showPaused===false && entParentUserID !== '') ?\r\n            <div className='px-4'>\r\n              <div className=\"sticky border border-[#FFA500] rounded-[10px] p-6 flex flex-col md:flex-row items-center justify-between bg-[#FFF4E5] mt-[125px] mx-auto w-full md:w-fit z-50 mb-[-50px] max-w-[1230px]\">\r\n                <div className='relative w-full md:w-auto'>\r\n                  <div className=\"flex flex-col md:flex-row items-center text-center md:text-left gap-4\">\r\n                    <div className=\"min-w-[20px] flex items-center justify-center\">\r\n                      {warningBanner()}\r\n                    </div>\r\n                    <div className='flex-1'>\r\n                      <div className=\"font-bold text-gray-800\">Important Account Update:</div>\r\n                      <div className=\"text-gray-600 text-sm\">Your subscription is either expired or currently inactive. For continuous access, kindly reach out to our <a href=\"https://ai-pro.org/contact-us/\" rel=\"noreferrer\" target='_blank' className='underline text-blue-500'><span className=\"pointer\">support team</span></a>.</div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                <button className=\"text-gray-500/50 hover:text-gray-700 absolute top-2 right-2 md:top-0 md:right-[5px]\" onClick={() => { setShowExpiredEnterprise(false); setShowUpgrade(false); }}>\r\n                  <span className=\"text-xl\">&times;</span>\r\n                </button>\r\n              </div>\r\n            </div>\r\n             : (ShowUpgrade === true && ShowExpiredEnterprise === true && entParentUserID === '') ?\r\n              <div className='px-4'>\r\n                <div className=\"sticky border border-[#FFA500] rounded-[10px] p-6 flex flex-col md:flex-row items-center justify-between bg-[#FFF4E5] mt-[125px] mx-auto w-full md:w-fit z-50 mb-[-50px] max-w-[1230px]\">\r\n                  <div className='relative w-full md:w-auto'>\r\n                    <div className=\"flex flex-col md:flex-row items-center text-center md:text-left gap-4\">\r\n                      <div className=\"min-w-[20px] flex items-center justify-center\">\r\n                        {warningBanner()}\r\n                      </div>\r\n                      <div className='flex-1'>\r\n                        <span>\r\n                          Important Account Update: Your subscription Has Expired or Is Currently Inactive. Please contact your account administrator for uninterrupted access.\r\n                        </span>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                <button className=\"text-gray-500/50 hover:text-gray-700 absolute top-2 right-2 md:top-0 md:right-[5px]\" onClick={() => { setShowExpiredEnterprise(false); setShowUpgrade(false); }}>\r\n                  <span className=\"text-xl\">&times;</span>\r\n                </button>\r\n              </div>\r\n              : (auth && auth.status === 'active' && !isProMax() && !isEnterpriseCluter()) ? (\r\n                <>\r\n                  {isEnterprise ? (\r\n                    <>\r\n                      {showTokenMaxoutFinal ? (\r\n                        <div className='px-4'>\r\n                          <div className=\"sticky border border-[#FFA500] rounded-[10px] p-6 flex flex-col md:flex-row items-center justify-between bg-[#FFF4E5] mt-[125px] mx-auto w-full md:w-fit z-50 mb-[-50px] max-w-[1230px]\">\r\n                            <div className='relative w-full md:w-auto'>\r\n                              <div className=\"flex flex-col md:flex-row items-center text-center md:text-left gap-4\">\r\n                                <div className=\"min-w-[20px] flex items-center justify-center\">\r\n                                  {warningBanner()}\r\n                                </div>\r\n                                <div className='flex-1'>\r\n                                  <div className=\"font-bold text-gray-800\">Token Limit Exceeded:</div>\r\n                                  <div className=\"text-gray-600 text-sm\">You have reached your token limit for this month.&nbsp;\r\n                                    <a href=\"https://ai-pro.org/contact-us/\" className=\"text-[#3b82f6] underline\"><strong>CONTACT US</strong></a> to continue accessing and enjoying our services without interruption.</div>\r\n                                </div>\r\n                              </div>\r\n                            </div>\r\n                            <button className=\"text-gray-500/50 hover:text-gray-700 absolute top-2 right-2 md:top-0 md:right-[5px]\" onClick={() => setshowTokenMaxoutFinal(false)}>\r\n                              <span className=\"text-xl\">&times;</span>\r\n                            </button>\r\n                          </div>\r\n                        </div>\r\n                      ) : showTokenMaxoutWarning ? (\r\n                        <div className='px-4'>\r\n                          <div className=\"sticky border border-[#FFA500] rounded-[10px] p-6 flex flex-col md:flex-row items-center justify-between bg-[#FFF4E5] mt-[125px] mx-auto w-full md:w-fit z-50 mb-[-50px] max-w-[1230px]\">\r\n                            <div className='relative w-full md:w-auto'>\r\n                              <div className=\"flex flex-col md:flex-row items-center text-center md:text-left gap-4\">\r\n                                <div className=\"min-w-[20px] flex items-center justify-center\">\r\n                                  {warningBanner()}\r\n                                </div>\r\n                                <div className='flex-1'>\r\n                                  <div className=\"font-bold text-gray-800\">Token Limit Warning:</div>\r\n                                  <div className=\"text-gray-600 text-sm\">You're just a few tokens away from reaching you limit for this month.&nbsp;\r\n                                    <a href=\"https://ai-pro.org/contact-us/\" className=\"text-[#3b82f6] underline\"><strong>CONTACT US</strong></a> to continue accessing and enjoying our services without interruption.</div>\r\n                                </div>\r\n                              </div>\r\n                            </div>\r\n                            <button className=\"text-gray-500/50 hover:text-gray-700 absolute top-2 right-2 md:top-0 md:right-[5px]\" onClick={() => setshowTokenMaxoutWarning(false)}>\r\n                              <span className=\"text-xl\">&times;</span>\r\n                            </button>\r\n                          </div>\r\n                        </div>\r\n                      ) : (\r\n                        <DownAppBannerContainer downAppBanner={downAppBanner} setDownAppBanner={setDownAppBanner} />\r\n                      )}\r\n                    </>\r\n                  ) : (\r\n                    <>\r\n                      {showTokenMaxoutFinal ? (\r\n                        <div className='px-4'>\r\n                          <div className=\"sticky border border-[#FFA500] rounded-[10px] p-6 flex flex-col md:flex-row items-center justify-between bg-[#FFF4E5] mt-[125px] mx-auto w-full md:w-fit z-50 mb-[-50px] max-w-[1230px]\">\r\n                            <div className='relative w-full md:w-auto'>\r\n                              <div className=\"flex flex-col md:flex-row items-center text-center md:text-left gap-4\">\r\n                                <div className=\"min-w-[20px] flex items-center justify-center\">\r\n                                  {warningBanner()}\r\n                                </div>\r\n                                <div className='flex-1'>\r\n                                  <div className=\"font-bold text-gray-800\">Token Limit Exceeded:</div>\r\n                                  <div className=\"text-gray-600 text-sm\">You have reached your token limit for this month.&nbsp;\r\n                                    <a href=\"/upgrade/?mx=1\" className=\"text-[#3b82f6] underline\"><strong>UPGRADE NOW</strong></a> to continue accessing and enjoying our services without interruption.\r\n                                  </div>\r\n                                </div>\r\n                              </div>\r\n                            </div>\r\n                            <button className=\"text-gray-500/50 hover:text-gray-700 absolute top-2 right-2 md:top-0 md:right-[5px]\" onClick={() => setshowTokenMaxoutFinal(false)}>\r\n                              <span className=\"text-xl\">&times;</span>\r\n                            </button>\r\n                          </div>\r\n                        </div>\r\n                      ) : showTokenMaxoutWarning ? (\r\n                        <div className='px-4'>\r\n                          <div className=\"sticky border border-[#FFA500] rounded-[10px] p-6 flex flex-col md:flex-row items-center justify-between bg-[#FFF4E5] mt-[125px] mx-auto w-full md:w-fit z-50 mb-[-50px] max-w-[1230px]\">\r\n                            <div className='relative w-full md:w-auto'>\r\n                              <div className=\"flex flex-col md:flex-row items-center text-center md:text-left gap-4\">\r\n                                <div className=\"min-w-[20px] flex items-center justify-center\">\r\n                                  {warningBanner()}\r\n                                </div>\r\n                                <div className='flex-1'>\r\n                                  <div className=\"font-bold text-gray-800\">Token Limit Warning:</div>\r\n                                  <div className=\"text-gray-600 text-sm\">You're just a few tokens away from reaching you limit for this month.&nbsp;\r\n                                    <a href=\"/upgrade/?mx=1\" className=\"text-[#3b82f6] underline\"><strong>UPGRADE NOW</strong></a> to continue accessing and enjoying our services without interruption.</div>\r\n                                </div>\r\n                              </div>\r\n                            </div>\r\n                            <button className=\"text-gray-500/50 hover:text-gray-700 absolute top-2 right-2 md:top-0 md:right-[5px]\" onClick={() => setshowTokenMaxoutWarning(false)}>\r\n                              <span className=\"text-xl\">&times;</span>\r\n                            </button>\r\n                          </div>\r\n                        </div>\r\n                      ) : (\r\n                        <DownAppBannerContainer downAppBanner={downAppBanner} setDownAppBanner={setDownAppBanner} />\r\n                      )}\r\n                    </>\r\n                  )}\r\n                </>\r\n              ) : null}\r\n        {false && auth.plan && auth.status === 'active' ? <>\r\n          {process.env.REACT_APP_CONSORTIUM_EMAILS && auth.email && !process.env.REACT_APP_CONSORTIUM_EMAILS.includes(auth.email) && (\r\n            <div id=\"tp-container\" className=\"flex flex-row text-center md:text-left w-[205px] mx-auto md:mx-0 md:mt-[10px]\">\r\n              <div className='flex flex-col justify-start'>\r\n                {/* <!-- TrustBox widget - Mini -->  */}\r\n                <div id=\"tp-star-container\">\r\n                  <div class=\"trustpilot-widget\" data-locale=\"en-US\" data-template-id=\"53aa8807dec7e10d38f59f32\" data-businessunit-id=\"63f8938353044ed29109ad33\" data-style-height=\"150px\" data-style-width=\"100%\" data-theme=\"light\">\r\n                    <a href=\"https://www.trustpilot.com/review/ai-pro.org\" target=\"_blank\" rel=\"noreferrer\">Trustpilot</a>\r\n                  </div>\r\n                </div>\r\n                {/* <!-- End TrustBox widget --> */}\r\n              </div>\r\n              <div className='flex flex-col justify-start'>\r\n                <div className=\"trustpilot-widget ml-[-10px] sm:ml-[-15px] mt-[27px] h-[40px]\" data-locale=\"en-US\" data-template-id=\"56278e9abfbbba0bdcd568bc\" data-businessunit-id=\"63f8938353044ed29109ad33\" data-style-height=\"52px\" data-style-width=\"225px\" data-font-family=\"Poppins\" data-border-color=\"#00b67a\">\r\n                  <a href=\"https://www.trustpilot.com/review/ai-pro.org\" target=\"_blank\" rel=\"noreferrer\">Trustpilot</a>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )}\r\n        </> : <></>}\r\n        <div className=\"px-4 justify-self-center py-8 pt-[6rem] md:min-h-[98vh]\">\r\n          {activeCategory === 'popular' ? (\r\n            <>\r\n              <div className='relative'>\r\n                <img\r\n                  src={bg}\r\n                  alt=\"Background\"\r\n                  className=\"absolute top-0 left-0 z-0 w-full object-cover\"\r\n                  style={{ height: '100%', minHeight: '300px' }}\r\n                />\r\n                <PopularModels />\r\n                <Navigation\r\n                  activeCategory={activeCategory}\r\n                  setActiveCategory={setActiveCategory}\r\n                  searchQuery={searchQuery}\r\n                  setSearchQuery={setSearchQuery}\r\n                />\r\n              </div>\r\n              <MostPopular tools={modifiedToolsData} fnRedirectApp={fnRedirectApp} fnReturnAppHref={fnReturnAppHref} activeCategory={activeCategory} />\r\n            </>\r\n          ) : (\r\n            <>\r\n              <div className='relative'>\r\n                <HeaderA activeCategory={activeCategory} />\r\n                <Navigation\r\n                  activeCategory={activeCategory}\r\n                  setActiveCategory={setActiveCategory}\r\n                  searchQuery={searchQuery}\r\n                  setSearchQuery={setSearchQuery}\r\n                />\r\n                <img\r\n                  src={bg}\r\n                  alt=\"Background\"\r\n                  className=\"absolute top-0 left-0 z-0 w-full object-cover\"\r\n                  style={{ height: '100%', minHeight: '300px' }}\r\n                />\r\n              </div>\r\n              {activeCategory === 'all' ? (\r\n                <>\r\n                  {searchQuery ? (\r\n                    <ToolsSection\r\n                      title={<span style={{ color: '#373737' }}>Results for <span style={{ color: '#3073D5' }}>\"{searchQuery}\"</span></span>}\r\n                      icon=\"all\"\r\n                      iconColor=\"blue\"\r\n                      tools={filteredTools}\r\n                      searchQuery={searchQuery}\r\n                      isLoading={isLoading}\r\n                      activeCategory={activeCategory}\r\n                      auth={auth}\r\n                    />\r\n                  ) : (\r\n                    <>\r\n                      <ToolsSection\r\n                        title=\"AI Chatbots\"\r\n                        icon=\"chat\"\r\n                        iconColor=\"blue\"\r\n                        tools={chatbotTools}\r\n                        searchQuery={searchQuery}\r\n                        isLoading={isLoading}\r\n                        activeCategory={activeCategory}\r\n                        auth={auth}\r\n                      />\r\n                      <ToolsSection\r\n                        title=\"AI Arts\"\r\n                        icon=\"art\"\r\n                        iconColor=\"purple\"\r\n                        tools={artTools}\r\n                        searchQuery={searchQuery}\r\n                        isLoading={isLoading}\r\n                        activeCategory={activeCategory}\r\n                        auth={auth}\r\n                      />\r\n                    </>\r\n                  )}\r\n                </>\r\n              ) : (\r\n                <>\r\n                  {activeCategory === 'chatbots' && (\r\n                    <>\r\n                      <ToolsSection\r\n                        title={<span style={{ color: '#373737' }}>Results for <span style={{ color: '#3073D5' }}>\"{searchQuery}\"</span></span>}\r\n                        icon=\"chat\"\r\n                        iconColor=\"blue\"\r\n                        tools={filteredTools.filter(tool => tool.category === 'chatbots')}\r\n                        searchQuery={searchQuery}\r\n                        isLoading={isLoading}\r\n                        activeCategory={activeCategory}\r\n                        auth={auth}\r\n                      />\r\n                    </>\r\n                  )}\r\n\r\n                  {activeCategory === 'arts' && (\r\n                    <>\r\n                      <ToolsSection\r\n                        title={<span style={{ color: '#373737' }}>Results for <span style={{ color: '#3073D5' }}>\"{searchQuery}\"</span></span>}\r\n                        icon=\"art\"\r\n                        iconColor=\"purple\"\r\n                        tools={filteredTools.filter(tool => tool.category === 'arts')}\r\n                        searchQuery={searchQuery}\r\n                        isLoading={isLoading}\r\n                        activeCategory={activeCategory}\r\n                        auth={auth}\r\n                      />\r\n                    </>\r\n                  )}\r\n                </>\r\n              )}\r\n            </>\r\n          )}\r\n        </div>\r\n      </div>\r\n      <AddMoreMemberModal showAddMoreMember={showAddMoreMember} setshowAddMoreMember={setshowAddMoreMember} setMoreToAddMember={setMoreToAddMember} setMoreToAddMemberTotalAmount={setMoreToAddMemberTotalAmount} setShowCompletePurchase={setShowCompletePurchase} />\r\n      <MemberCompletePurchase moreToAddMember={moreToAddMember} moreToAddMemberTotalAmount={moreToAddMemberTotalAmount} setShowCompletePurchase={setShowCompletePurchase} showCompletePurchase={showCompletePurchase} />\r\n      <Footer />\r\n    </div>\r\n  );\r\n};\r\n\r\nconst DownAppBannerContainer = ({ downAppBanner, setDownAppBanner }) => {\r\n  const handleClose = () => {\r\n    setDownAppBanner(false);\r\n  };\r\n\r\n  if (!downAppBanner) {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <div className='px-4'>\r\n      <div className=\"sticky border border-[#FFA500] rounded-[10px] p-6 flex flex-col md:flex-row items-center justify-between bg-[#FFF4E5] mt-[125px] mx-auto w-full md:w-fit z-50 mb-[-50px] max-w-[1230px]\">\r\n        <div className='relative w-full md:w-auto'>\r\n          <div className=\"flex flex-col md:flex-row items-center text-center md:text-left\">\r\n            <div dangerouslySetInnerHTML={{ __html: downAppBanner }} />\r\n          </div>\r\n        </div>\r\n        <button className=\"text-gray-500/50 hover:text-gray-700 absolute top-2 right-2 md:top-0 md:right-[5px]\" onClick={handleClose}>\r\n          <span className=\"text-xl\">&times;</span>\r\n        </button>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\nexport default MyAccountB;\r\n\r\n"], "names": ["isWebpSupported", "elem", "document", "createElement", "getContext", "toDataURL", "indexOf", "hexHash", "hex", "length", "test", "slice", "concat", "hoverDarken", "r", "parseInt", "g", "b", "Math", "max", "min", "toHex", "value", "round", "toString", "padStart", "getPrefixLocation", "currentLocation", "window", "location", "href", "price_per_member", "user_id", "AddMoreMemberModal", "_ref", "showAddMoreMember", "setshowAddMoreMember", "setMoreToAddMember", "setMoreToAddMemberTotalAmount", "setShowCompletePurchase", "membersToAdd", "setMembersToAdd", "useState", "totalAmount", "setTotalAmount", "paymentInterval", "setpaymentInterval", "auth", "<PERSON><PERSON>", "useEffect", "undefined", "plan", "interval", "toLowerCase", "modalAddMoreMembersClose", "modal", "getElementById", "style", "display", "toastr", "positionClass", "modalAddMoreMembersOpen", "_jsx", "_Fragment", "children", "id", "className", "class", "_jsxs", "onClick", "type", "motion", "button", "whileHover", "backgroundColor", "whileTap", "scale", "MemberCompletePurchase", "_ref2", "moreToAddMember", "moreToAddMemberTotalAmount", "showCompletePurchase", "modalClose", "modalOpen", "src", "aiproLogo", "alt", "sendViaEmail", "members", "total_amount", "url", "process", "querySelector", "classList", "add", "axios", "post", "headers", "then", "res", "output", "data", "remove", "success", "catch", "error", "response", "status", "FaInfoCircle", "submitPaymentInformation", "activeCategory", "logo", "currentLogo", "heading", "paragraph", "get<PERSON>ontent", "aiChat", "aiArt", "searchQuery", "setSearch<PERSON>uery", "setActiveCategory", "inputRef", "useRef", "isAppleMobile", "navigator", "userAgent", "IoCloseOutline", "size", "width", "height", "viewBox", "fill", "xmlns", "x", "rx", "fillRule", "clipRule", "d", "x1", "y1", "x2", "y2", "gradientUnits", "stopColor", "offset", "ref", "placeholder", "onChange", "e", "target", "setTimeout", "searchInput", "focus", "onKeyDown", "key", "preventDefault", "stopPropagation", "categories", "displayCategories", "name", "icon", "map", "category", "handleCategoryClick", "categoryId", "SearchBar", "tool", "isLoading", "fnRedirectApp", "fnReturnAppHref", "a", "initial", "opacity", "animate", "exit", "transition", "duration", "ease", "link", "handleClick", "ToolIcon", "isPopular", "isNew", "description", "SectionIcon", "color", "div", "title", "iconColor", "tools", "redirectApp", "returnAppHref", "filteredTools", "filter", "includes", "containerVariants", "hidden", "visible", "itemVariants", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "variants", "ToolCard", "p", "HeaderB", "headerB", "toolLinks", "chatpro", "chatpdf", "chatpdfv2", "convert2english", "teacher_ai", "trips_ai", "recipe_maker", "translate_now", "searchai", "multillm", "sitebot", "codingai", "homework", "txt2img", "interiorgpt", "restorephoto", "removebg", "avatar_make", "storybook", "flux", "toolDataTemplate", "chatbot", "chatpdfv2Icon", "chatpdfIcon", "grammarIcon", "teacherIcon", "tripsIcon", "recipeIcon", "translateIcon", "searchIcon", "multiChatIcon", "sitebotIcon", "codingIcon", "homeworkIcon", "dreamPhotoIcon", "interiorIcon", "restorePhotoIcon", "removeBackgroundIcon", "avatarMakerIcon", "storyBookIcon", "fluxImageGenIcon", "aiModels", "bgImage", "claudeB<PERSON>", "claudeLOGO", "chatgptBG", "chatgptLOGO", "deepseekBG", "deepseekLOGO", "toolsMap", "reduce", "acc", "app", "appMetadata", "chatbotBG", "desc", "dreamphotoBG", "chatpdfBG", "fluxBG", "teacheraiBG", "avatarLOGO", "avatarmakerBG", "appData", "_toolsMap$id", "_objectSpread", "find", "model", "index", "_toolsMap$chatbotPro", "rel", "h2", "tabIndex", "api_url", "DownAppBannerContainer", "downAppBanner", "setDownAppBanner", "dangerouslySetInnerHTML", "__html", "handleClose", "MyAccountB", "upid", "user_pid", "toolsData", "fullUrl", "open", "getToolsData", "usage", "setUsage", "setIsLoading", "showPaused", "setShowPaused", "showTokenMaxoutWarning", "setshowTokenMaxoutWarning", "current", "showTokenMaxoutFinal", "setshowTokenMaxoutFinal", "ShowUpgrade", "setShowUpgrade", "ShowExpiredEnterprise", "setShowExpiredEnterprise", "entParentUserID", "setentParentUserID", "isEnterprise", "setIsEnterprise", "loaded", "setLoaded", "script", "REACT_APP_CHATHEAD_URL", "addEventListener", "head", "append<PERSON><PERSON><PERSON>", "initializeChat", "initializeChatWidget", "chatConfig", "token", "REACT_APP_BOT_TOKEN", "expired", "ent_parent_user_id", "timer", "clearTimeout", "async", "fetchedBanner", "banner_content", "checkBanner", "fetchBanner", "max_token", "max_tokens", "total_usage", "eightyPercentOfToken", "checkUsage", "getUserUsage", "isPro", "Get<PERSON><PERSON><PERSON>", "avatar<PERSON><PERSON>", "appUrl", "folder", "fetch", "blob", "blobUrl", "URL", "createObjectURL", "setAttribute", "body", "click", "<PERSON><PERSON><PERSON><PERSON>", "RemoveCookie", "domain", "path", "modifiedToolsData", "matchesSearch", "matchesCategory", "chatbotTools", "artTools", "warningBanner", "y", "flexDirection", "minHeight", "<PERSON><PERSON><PERSON>", "content", "REACT_APP_CONSORTIUM_EMAILS", "email", "Header", "resumed_at", "handleResumeClick", "_auth$plan_name", "plan_name", "handleUpgradeClick", "currency", "<PERSON><PERSON><PERSON><PERSON>", "emailid", "REACT_APP_EMAILID", "bg", "PopularModels", "Navigation", "MostPopular", "HeaderA", "ToolsSection", "Footer", "isEnterpriseCluter"], "sourceRoot": ""}