"use strict";(self.webpackChunkv1=self.webpackChunkv1||[]).push([[3513,722,9044],{34492:(e,t,r)=>{r.d(t,{aS:()=>o,ar:()=>g,mD:()=>h,mW:()=>p,o0:()=>x,p6:()=>i,rZ:()=>n,tN:()=>u,x6:()=>d,yt:()=>m});var s=r(74335),a=r(80184);function l(e){return e?"usd"===e.toLowerCase()?"$":"eur"===e.toLowerCase()?"€":"gbp"===e.toLowerCase()?"£":"brl"===e.toLowerCase()||"sar"===e.toLowerCase()?"R$":"czk"===e.toLowerCase()?"Kč":"":""}function n(e){return e?"usd"===e.toLowerCase()||"eur"===e.toLowerCase()?"US":"gbp"===e.toLowerCase()?"GB":"brl"===e.toLowerCase()?"US":"sar"===e.toLowerCase()?"AE":"chf"===e.toLowerCase()?"CH":"sek"===e.toLowerCase()?"SE":"US":""}function i(e){if(!e)return"";const t=new Date(e.replace(/ /g,"T"));return["January","February","March","April","May","June","July","August","September","October","November","December"][t.getMonth()]+" "+t.getDate()+" "+t.getFullYear()}function o(e){return e.trial_price?e.trial_price:e.price?e.price:"0"}function d(e){return e.toString().replace(/\B(?=(\d{3})+(?!\d))/g,",")}function c(e){const t=parseFloat(e);return d(t%1==0?t.toFixed(0):t.toFixed(2))}function m(e,t){return e&&t?isNaN(t)?"":parseFloat(t)>=0?"sar"===e.toLowerCase()?c(t).toLocaleString("en-US")+"ر.س.":"aed"===e.toLowerCase()?c(t).toLocaleString("en-US")+"AED":"chf"===e.toLowerCase()?c(t).toLocaleString("en-US")+"CHF":"sek"===e.toLowerCase()?c(t).toLocaleString("en-US")+"SEK":"pln"===e.toLowerCase()?c(t).toLocaleString("en-US")+"zł":"ron"===e.toLowerCase()?c(t).toLocaleString("en-US")+"LEI":"huf"===e.toLowerCase()?c(t).toLocaleString("en-US")+"Ft":"dkk"===e.toLowerCase()?c(t).toLocaleString("en-US")+"kr.":"bgn"===e.toLowerCase()?c(t).toLocaleString("en-US")+"лв":"try"===e.toLowerCase()?c(t).toLocaleString("en-US")+"₺":l(e)+c(t).toLocaleString("en-US"):"-"+l(e)+(-1*c(t)).toLocaleString("en-US"):""}function u(e,t){e=new Date(e);var r=((t=new Date(t)).getTime()-e.getTime())/1e3;return r/=60,Math.abs(Math.round(r))}function x(e){if(!e)return"";const t=new Date(e.replace(/ /g,"T"));return["January","February","March","April","May","June","July","August","September","October","November","December"][t.getMonth()]+" "+t.getDate()+" "+t.getFullYear()+" "+t.getHours()+":"+t.getMinutes()}function p(e){let{plan:t}=e,r="",l="";return"Yearly"===t.payment_interval&&(r=m(t.currency,parseFloat(t.price/365).toFixed(2)),l=(0,a.jsxs)("div",{class:"text-xs mb-4",children:["billed ",m(t.currency,t.price),"/year"]})),"Monthly"===t.payment_interval&&(r=m(t.currency,parseFloat(t.price/30).toFixed(2)),l=(0,a.jsxs)("div",{class:"text-xs mb-4",children:["billed ",m(t.currency,t.price),"/Month"]})),t.trial_price&&(r=m(t.currency,parseFloat(t.trial_price/t.trial_days).toFixed(2)),l=(0,a.jsxs)("div",{class:"text-xs mb-4",children:["billed ",m(t.currency,t.trial_price)]})),(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("p",{className:"text-4xl font-bold text-gray-800 ".concat(""===(0,s.bG)("p_toggle")?"mb-4":""),children:[r," ",(0,a.jsx)("span",{className:"text-sm",children:" per Day"})]}),l]})}function h(e){let{plan:t}=e;return"on"===(0,s.bG)("daily")?p({plan:t}):t.trial_price?(0,a.jsx)("p",{className:"text-4xl font-bold text-gray-800 mb-4",children:m(t.currency,t.trial_price)}):(0,a.jsxs)("p",{className:"text-4xl font-bold text-gray-800 mb-4",children:[m(t.currency,t.price),(0,a.jsxs)("span",{className:"text-sm",children:["/","Monthly"===t.payment_interval?"month":"year"]})]})}function g(e){var t,r;const a=(null!==(t=(0,s.bG)("locales"))&&void 0!==t?t:"en").toLowerCase(),l=(null!==(r=(0,s.bG)("daily"))&&void 0!==r?r:"off").toLowerCase(),{trial_days:n,payment_interval:i,trial_price:o,currency:d,currency_symbol:c}=e;let{display_txt2:u,price:x}=e;if(n>0&&o>0&&"en"===a){let e=x,t="month";"on"===l&&(e=parseFloat(x/("Yearly"===i?365:30)).toFixed(2),t="day<br>(billed ".concat(c+x," ").concat(i,")")),u+="<div>".concat(n,"-Day Trial, then only ").concat(m(d,e)," per ").concat(t,"</div>")}return u}},78227:(e,t,r)=>{r.r(t),r.d(t,{default:()=>X});var s=r(72791),a=r(19886),l=r(10728),n=r(18168),i=r(28891),o=r(74335),d=r(78820),c=r(56355),m=r(31243),u=r(34492),x=r(82801),p=r(2777),h=r(84947),g=r(54270),b=r(85187),y=r(95828),f=r.n(y),v=(r(92831),r(29540)),j=r(80184);function w(e){let{showPausedAccountModal:t,setShowPausedAccountModal:r,userSubscriptionID:a,userMerchant:l,userAccountID:n}=e;const[i,d]=(0,s.useState)(1),[c,u]=(0,s.useState)(!1);(0,s.useEffect)((()=>{f().options={positionClass:"toast-top-center"}}),[]),(0,s.useEffect)((()=>{u(i>=5)}),[i]);const h=()=>{r(!1)};return void 0!==t&&!0===t&&(()=>{let e=document.getElementById("modalPausedAccount");null!==e&&(e.style.display="block",r(!0))})(),void 0!==t&&!1===t&&h(),(0,j.jsx)(j.Fragment,{children:(0,j.jsx)(x.u,{appear:!0,show:t,as:s.Fragment,children:(0,j.jsxs)(p.V,{as:"div",className:"relative z-10",onClose:()=>h(),children:[(0,j.jsx)(x.u.Child,{as:s.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:(0,j.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-25"})}),(0,j.jsx)("div",{className:"fixed inset-0 overflow-y-auto",children:(0,j.jsx)("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:(0,j.jsx)(x.u.Child,{as:s.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:(0,j.jsxs)(p.V.Panel,{className:"w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all",children:[(0,j.jsx)(p.V.Title,{as:"h3",className:"text-lg font-medium leading-6 text-gray-900",children:"Pause Subscription"}),(0,j.jsxs)("div",{className:"mt-2",children:[(0,j.jsx)("div",{className:"text-sm text-gray-500",children:"You're about to pause your subscription. Please select how many billing cycles you'd like it to be paused."}),(0,j.jsxs)("div",{className:"text-center",children:[(0,j.jsx)("input",{className:"bg-gray-400 rounded px-3 py-2 m-2 text-white cursor-pointer",type:"button",value:"-",onClick:()=>{i>1&&d(i-1)}}),(0,j.jsx)("span",{className:"text-black p-2 mx-auto font-bold",children:i}),(0,j.jsx)("input",{className:"bg-gray-400 rounded px-3 py-2 m-2 text-white cursor-pointer",type:"button",value:"+",onClick:()=>{d(i+1)},disabled:c})]})]}),(0,j.jsxs)("div",{className:"mt-4 text-right",children:[(0,j.jsx)("button",{type:"button",className:"inline-flex justify-center rounded-md border border-transparent bg-red-500 px-4 py-2 mr-2 text-sm font-medium text-white hover:bg-[#2563eb]",onClick:()=>{h(),document.querySelector(".loader-container").classList.add("active"),m.Z.post("".concat("http://localhost:9002/api","/pause-subscription"),{tk:(0,o.bG)("access"),subscription_id:a,merchant:l,billing_cycle:i,account_pid:n},{headers:{"content-type":"application/x-www-form-urlencoded"}}).then((function(e){let t=e.data;t.success?(document.querySelector(".loader-container").classList.remove("active"),f().success("Success!<br> Your subscription is now paused."),setTimeout((function(){window.location.reload()}),1e3)):(document.querySelector(".loader-container").classList.remove("active"),f().error(t.data.msg))}))},children:"Confirm"}),(0,j.jsx)("button",{type:"button",className:"inline-flex justify-center rounded-md border border-transparent bg-gray-100 px-4 py-2 text-sm font-medium text-black hover:bg-gray-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-gray-500 focus-visible:ring-offset-2",onClick:()=>h(),children:"Close"})]})]})})})})]})})})}var N=r(72608),k=r(91933);const _=e=>{let{currentPlan:t,currentPlanName:r,totalTokenUsage:a,maxTokens:l,resetDate:n,tokenUsages:i,entParentUserID:d}=e;const[c,m]=(0,s.useState)(""),[u,x]=(0,s.useState)([]),[p,h]=(0,s.useState)([]);t=t?t.toLowerCase():"",r=r?r.toLowerCase():"";const g=""===t,b="basic"===t,y="pro"===t,f="promax"===t,v="enterprise"===t,w="advanced"===t,N=v&&r!==t,k=window.location.href.includes("staging"),_="v1"===(0,o.bG)("auth_version");(0,s.useEffect)((()=>{k&&m("staging.")}),[k]),(0,s.useEffect)((()=>{const{perModel:e,perApp:t}=i;let r=Object.fromEntries(Object.entries(e).filter((e=>{const[t]=e;switch(t){case"DALL·E Image Generated":case"Claude 3.5":return!v;case"GPT Image 1 Image Generated":case"Grok 4":return!b;case"Kling 1.6":return!b&&!_;case"Claude 3.7":case"Grok 3":return!b&&!v;case"Deepseek R1":return y||f;case"o1 Prompts":return N;default:return!0}})));x(r),h(t)}),[b,y,f,v,N,i,t,r,_]);const S=e=>parseInt(e).toLocaleString("en-US"),C=e=>{let t=!0;switch(e){case"GPT-4o":b||(t=!1);break;case"GPT Image 1 Image Generated":case"Kling 1.6":case"Flux Prompts":t=!0;break;case"Claude 3.5":(w||f)&&(t=!1);break;default:f&&(t=!1)}return t},M=()=>!g&&!f&&parseInt(a)>=parseInt(l),L=(e,t,r)=>!!C(e)&&(!g&&parseInt(t)>=parseInt(r)),T=(e,t,r)=>{let s=S(t);return C(e)&&(s+=" out of ".concat(S(r))),s.trim()};return(0,j.jsxs)("div",{className:"w-full",children:[(0,j.jsxs)("div",{className:"mb-6",children:[(0,j.jsx)("div",{className:"text-xl mb-2",children:(0,j.jsx)("strong",{className:"font-medium ".concat(M()?"text-[#db7e00]":""),children:(()=>{let e=S(a);return f||N||(e+=" out of ".concat(S(l))),"TOTAL TOKEN USAGE: ".concat(e).trim()})()})}),!g&&(0,j.jsxs)("div",{className:"text-sm text-gray-600 mb-2",children:["Token count will reset on: ",n]}),M()&&(0,j.jsx)("div",{className:"flex gap-4 items-start flex-col ".concat(v?"lg:items-center lg:flex-row":"sm:items-center sm:flex-row"),children:""===d&&(v?(0,j.jsxs)(j.Fragment,{children:[(0,j.jsx)("div",{className:"text-[#db7e00]",children:"For continuous access, kindly reach out to our support team"}),(0,j.jsx)("a",{href:"https://".concat(c,"ai-pro.org/contact-us"),class:"font-bold text-white py-2 px-4 rounded-md bg-[#db7e00] hover:bg-[#f99d1f]",children:"Contact Support"})]}):(0,j.jsxs)(j.Fragment,{children:[(0,j.jsx)("div",{className:"text-[#db7e00]",children:"UPGRADE is required to continue"}),(0,j.jsx)("a",{href:"/upgrade/?mx=1",class:"font-bold text-white py-2 px-4 rounded-md bg-[#db7e00] hover:bg-[#f99d1f]",children:"Upgrade"})]}))})]}),(0,j.jsxs)("div",{className:"flex gap-6 flex-col md:flex-row md:items-start",children:[(0,j.jsxs)("div",{className:"flex-1 bg-gray-50 rounded-lg shadow-md",children:[(0,j.jsx)("div",{className:"px-4 py-4 border-b border-gray-200",children:(0,j.jsx)("strong",{className:"text-gray-900 font-medium uppercase",children:"Token Usage by Model"})}),(0,j.jsxs)("table",{className:"w-full",children:[(0,j.jsx)("thead",{className:"border-b border-gray-200",children:(0,j.jsxs)("tr",{children:[(0,j.jsx)("th",{className:"px-4 py-3 text-left font-medium text-gray-900 w-1/2",children:"Model"}),(0,j.jsx)("th",{className:"px-4 py-3 text-center font-medium text-gray-900 w-1/2",children:"Token Usage"})]})}),(0,j.jsx)("tbody",{className:"divide-y divide-gray-200",children:u&&Object.keys(u).length>0?(0,j.jsx)(j.Fragment,{children:Object.entries(u).map(((e,t)=>{let[r,{usage:s,max_usage:a}]=e;return(0,j.jsxs)("tr",{children:[(0,j.jsx)("td",{className:"px-4 py-3 text-sm text-left text-gray-900",children:r}),(0,j.jsx)("td",{className:"px-4 py-3 text-sm text-center ".concat(L(r,s,a)?"text-[#db7e00]":"text-gray-900"),children:T(r,s,a)})]},t)}))}):(0,j.jsx)("tr",{children:(0,j.jsx)("td",{colspan:"2",className:"px-4 py-3 text-sm text-center font-medium text-gray-900",children:"No data available."})})})]})]}),(0,j.jsxs)("div",{className:"flex-1 bg-gray-50 rounded-lg shadow-md",children:[(0,j.jsx)("div",{className:"px-4 py-4 border-b border-gray-200",children:(0,j.jsx)("strong",{className:"text-gray-900 font-medium uppercase",children:"Token Usage by App"})}),(0,j.jsxs)("table",{className:"w-full",children:[(0,j.jsx)("thead",{className:"border-b border-gray-200",children:(0,j.jsxs)("tr",{children:[(0,j.jsx)("th",{className:"px-4 py-3 text-left font-medium text-gray-900 w-1/2",children:"App"}),(0,j.jsx)("th",{className:"px-4 py-3 text-center font-medium text-gray-900 w-1/2",children:"Token Usage"})]})}),(0,j.jsx)("tbody",{className:"divide-y divide-gray-200",children:p&&Object.keys(p).length>0?(0,j.jsx)(j.Fragment,{children:Object.entries(p).map(((e,t)=>{let[r,s]=e;return(0,j.jsxs)("tr",{children:[(0,j.jsx)("td",{className:"px-4 py-3 text-sm text-left text-gray-900",children:r}),(0,j.jsx)("td",{className:"px-4 py-3 text-sm text-center text-gray-900",children:S(s)})]},t)}))}):(0,j.jsx)("tr",{children:(0,j.jsx)("td",{colspan:"2",className:"px-4 py-3 text-sm text-center font-medium text-gray-900",children:"No data available."})})})]})]})]})]})};var S=0,C=null;async function M(){if(C)return C;const e=(await m.Z.post("".concat("http://localhost:9002/api","/get-subscription"),{tk:(0,o.bG)("access")},{headers:{"content-type":"application/x-www-form-urlencoded"}})).data;return e.success?(C=e.data,e.data):[]}const L=window.view_data,T=L.offer?L.offer.plan_id:"",E=L.offer?L.offer.price:"",F=L.offer?L.offer.currency:"",A=L.offer?L.offer.cancel:"",I=L.offer?L.offer.percentage:"",P=e=>{let{show:t,onClose:r,onAccept:a,onDecline:l}=e;const{data:n}=(0,k.useQuery)("users",M),[i,o]=(0,s.useState)("MONTH");return(0,s.useEffect)((()=>{if((null==n?void 0:n.length)>0){const e=n.find((e=>"active"===(null==e?void 0:e.status)));null!=e&&e.payment_interval&&o("YEARLY"===e.payment_interval.toUpperCase()?"YEAR":"MONTH")}}),[n]),t?(0,j.jsxs)(j.Fragment,{children:[(0,j.jsx)("div",{className:"popup-overlay fixed top-0 left-0 w-full h-full bg-black opacity-50 z-[8889] block",onClick:r}),(0,j.jsxs)("div",{className:"popup-modal fixed top-0 bottom-0 left-0 right-0 mx-auto my-auto w-[90%] max-w-[432px] h-fit bg-white p-4 sm:p-6 md:p-8 rounded-md shadow-lg overflow-auto block z-[9999]",children:[(0,j.jsx)("button",{className:"absolute top-2 right-4 text-gray-300 hover:text-gray-700 text-2xl font-bold focus:outline-none z-[10000]",onClick:r,"aria-label":"Close",children:"×"}),(0,j.jsxs)("div",{className:"space-y-4 sm:space-y-7",children:[(0,j.jsx)("img",{src:N.Z,alt:"AI-Pro Logo",className:"aiprologo mx-auto w-[150px] sm:w-[180px] md:w-[200px]"}),(0,j.jsxs)("div",{children:[(0,j.jsxs)("div",{className:"text-center text-2xl sm:text-3xl md:text-4xl font-bold mb-0.5",children:[I,"% off forever"]}),(0,j.jsxs)("div",{className:"text-center text-sm sm:text-base",children:["Keep your account for ",I,"% off forever"]})]}),(0,j.jsx)("div",{className:"w-[calc(100%+2rem)] sm:w-[calc(100%+3rem)] md:w-[calc(100%+4rem)] bg-gradient-to-r max-h-[79px] from-[#3E53B8] to-[#2393F1] -mx-4 sm:-mx-6 md:-mx-8 overflow-hidden",children:(0,j.jsxs)("div",{className:"text-center text-white text-xl sm:text-2xl font-bold py-6 uppercase flex justify-center items-center gap-2 max-h-[79px]",children:[F," ",(0,j.jsx)("span",{className:"text-[40px] sm:text-[45px] md:text-[50px]",children:E}),(0,j.jsxs)("span",{className:"flex flex-col text-left text-[12px] leading-[16px] font-semibold",children:[(0,j.jsx)("span",{children:"PER"}),(0,j.jsx)("span",{children:i})]})]})}),(0,j.jsxs)("div",{className:"flex flex-col gap-3 sm:gap-4",children:[(0,j.jsxs)("button",{className:"relative overflow-hidden text-center text-xs sm:text-[14px] font-semibold bg-black text-white rounded-md py-3 sm:py-4 px-12 max-w-[200px] sm:w-[232px] sm:max-w-[232px] max-h-[42px] sm:max-h-[46px] mx-auto hover:scale-105 transition-all duration-300 ease-in-out group",onClick:a,children:[(0,j.jsx)("span",{className:"text-[12px] sm:text-[14px]",children:"Accept this Offer"}),(0,j.jsx)("span",{className:"shine-effect"})]}),(0,j.jsx)("button",{className:"text-center text-[10px] sm:text-xs text-black hover:underline hover:text-[#9F1313]",onClick:l,children:"Decline Offer"})]})]})]})]}):null};const D=e=>{let{tabName:t,activeTab:r,onClick:s,children:a}=e;const l=r===t;return(0,j.jsx)("li",{className:" pb-2 cursor-pointer py-3 px-6 rounded-t-lg ".concat(l?"border-b-2 border-white bg-white":""),onClick:()=>s(t),children:a})},U=e=>{const[t,r]=(0,s.useState)(""),[a]=(0,s.useState)(e.auth.email),[l,n]=(0,s.useState)(""),[i,d]=(0,s.useState)(""),[u,x]=(0,s.useState)(""),[p]=(0,s.useState)(e.auth.user_pid),[h,g]=(0,s.useState)(!1);(0,s.useEffect)((()=>{void 0!==e.auth&&(null!==e.auth.is_social&&""!==e.auth.is_social?g(!0):g(!1))}),[e.auth]);return(0,j.jsxs)("div",{className:"w-96",children:[(0,j.jsx)("p",{className:"text-sm py-4",children:"You may edit your account details below:"}),(0,j.jsxs)("div",{className:"flex items-center",children:[(0,j.jsxs)("label",{className:"text-sm mr-2 font-bold font-s",children:["Your Account ID: ",p]}),(0,j.jsx)("button",{class:"bg-white hover:bg-gray-100 text-gray-800 font-semibold py-2 ml-1",onClick:()=>{navigator.clipboard.writeText(p),f().success("Successfully copied")},children:(0,j.jsx)(c.Dup,{})})]}),(0,j.jsxs)("div",{className:"relative block mt-3",children:[(0,j.jsxs)("div",{className:"border-solid border-2 border-gray-100 rounded-sm",children:[(0,j.jsx)("div",{className:"p-3 bg-gray-50 border-b-2 border-gray-100 sm:text-sm",children:"Change Email Address"}),(0,j.jsxs)("div",{className:"p-2 pt-1 pb-1",children:[(0,j.jsx)("input",{className:"placeholder:italic placeholder-text-slate-400 block bg-gray-100 w-full border border-slate-300 rounded-md my-3 py-2 pl-3 pr-3 shadow-sm focus:outline-none focus:border-sky-500 focus:ring-sky-500 focus:ring-1 sm:text-sm text-gray-400",placeholder:"<EMAIL>",type:"email",name:"email",readOnly:!0,value:e.auth.email}),(0,j.jsx)("input",{className:"placeholder:italic placeholder-text-slate-400 block w-full border border-slate-300 rounded-md my-3 py-2 pl-3 pr-3 shadow-sm focus:outline-none focus:border-sky-500 focus:ring-sky-500 focus:ring-1 sm:text-sm",placeholder:"New Email",type:"email",name:"new_email",autocomplete:"new-email",onKeyUp:e=>{r(e.target.value)},onChange:e=>r(e.target.value),disabled:h})]})]}),(0,j.jsxs)("div",{className:"border-solid border-2 border-gray-100 rounded-sm mt-2",children:[(0,j.jsx)("div",{className:"p-3 bg-gray-50 border-b-2 border-gray-100 sm:text-sm",children:"Change Password"}),(0,j.jsxs)("div",{className:"p-2 pt-1 pb-1",children:[(0,j.jsx)("input",{className:"placeholder:italic placeholder:text-slate-400 block bg-white w-full border border-slate-300 rounded-md my-3 py-2 pl-3 pr-3 shadow-sm focus:outline-none focus:border-sky-500 focus:ring-sky-500 focus:ring-1 sm:text-sm",placeholder:"New Password",type:"password",name:"new_password",autocomplete:"new-password",onKeyUp:e=>{d(e.target.value)},disabled:h}),(0,j.jsx)("input",{className:"placeholder:italic placeholder:text-slate-400 block bg-white w-full border border-slate-300 rounded-md my-3 py-2 pl-3 pr-3 shadow-sm focus:outline-none focus:border-sky-500 focus:ring-sky-500 focus:ring-1 sm:text-sm",placeholder:"Confirm New Password",type:"password",name:"conf_password",onKeyUp:e=>{x(e.target.value)},disabled:h})]})]})]}),(0,j.jsx)(B,{updateUserDetails:()=>{let e=(()=>{let e="";return l?i.length&&(i!==u?e="New Password do not match.":i.length<6&&(e="New Password should be at least 6 characters.")):e="Current Password is required.",e})();e?f().error(e):(e=(()=>{let e="";return t.length&&(/\S+@\S+\.\S+/.test(t)||(e="Invalid email format")),e})(),e?f().error(e):(document.querySelector(".loader-container").classList.add("active"),m.Z.post("".concat("http://localhost:9002/api","/update-account"),{tk:(0,o.bG)("access"),newemail:t,password:l,newpassword:i,confpassword:u},{headers:{"content-type":"application/x-www-form-urlencoded"}}).then((function(e){let t=e.data;if(t.success)return f().success("Success!<br> Please re-login your account."),void setTimeout((function(){window.location.reload()}),1e3);document.querySelector(".loader-container").classList.remove("active"),t.data&&f().error(t.data.msg)}))))},setOldPassword:n}),(0,j.jsx)("div",{className:"text-sm text-slate-400 py-4 mt-2 ml-2",children:(0,j.jsx)(V,{email:a})})]})},B=e=>{const t=e.setOldPassword,[r,l]=(0,s.useState)(!1),n=()=>{l(!1)},i=()=>{e.updateUserDetails()};return(0,j.jsxs)(j.Fragment,{children:[(0,j.jsx)(a.E.button,{className:"bg-blue-500 mb-1 text-white font-bold py-3 px-6 my-3 rounded-lg change-password hover:bg-[#2563eb]",whileTap:{scale:.9},onClick:()=>{l(!0)},children:"Save Changes"}),(0,j.jsx)(x.u,{appear:!0,show:r,as:s.Fragment,children:(0,j.jsxs)(p.V,{as:"div",className:"relative z-10",onClose:n,children:[(0,j.jsx)(x.u.Child,{as:s.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:(0,j.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-25"})}),(0,j.jsx)("div",{className:"fixed inset-0 overflow-y-auto",children:(0,j.jsx)("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:(0,j.jsx)(x.u.Child,{as:s.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:(0,j.jsxs)(p.V.Panel,{className:"w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all",children:[(0,j.jsx)(p.V.Title,{as:"h3",className:"text-lg font-medium leading-6 text-gray-900",children:"Update Account"}),(0,j.jsxs)("div",{className:"mt-2",children:[(0,j.jsx)("div",{className:"text-sm text-gray-500",children:"Please enter your password to proceed."}),(0,j.jsx)("div",{children:(0,j.jsx)("input",{className:"placeholder:italic placeholder:text-slate-400 block bg-white w-full border border-slate-300 rounded-md my-3 py-2 pl-3 pr-3 shadow-sm focus:outline-none focus:border-sky-500 focus:ring-sky-500 focus:ring-1 sm:text-sm",placeholder:"Current Password *",type:"password",name:"current_password",onKeyUp:e=>{t(e.target.value),13===e.keyCode&&i()}})})]}),(0,j.jsxs)("div",{className:"mt-4",children:[(0,j.jsx)("button",{type:"button",className:"inline-flex justify-center rounded-md border border-transparent bg-blue-500 px-4 py-2 mr-2 text-sm font-medium text-white hover:bg-[#2563eb]",onClick:i,children:"Proceed"}),(0,j.jsx)("button",{type:"button",className:"inline-flex justify-center rounded-md border border-transparent bg-gray-100 px-4 py-2 text-sm font-medium text-black hover:bg-gray-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-gray-500 focus-visible:ring-offset-2",onClick:n,children:"Close"})]})]})})})})]})})]})};async function O(){const e=(await m.Z.post("".concat("http://localhost:9002/api","/get-enterprise-members"),{tk:(0,o.bG)("access")},{headers:{"content-type":"application/x-www-form-urlencoded"}})).data;return e.success?e.data:[]}const q=e=>{let{setshowAddMoreMember:t,setShowPausedAccountModal:r,setUserSubscriptionID:a,setUserMerchant:l,setUserAccountID:n,date_now:i,user_subscription:d,userPpg:m,showOffer:p}=e;const g=(0,o.bG)("access");if(null==d)return;const b=e=>{let t=e.target.getAttribute("pid");t&&(document.querySelector(".loader-container").classList.add("active"),window.location.href="/change-card?pid="+t)},y=()=>{window.location.href="/downgrade"},f=e=>e.merchant?e.merchant.toLowerCase():"",v=e=>e.currency?e.currency.toLowerCase():"",w=e=>e.plan_type?e.plan_type.toLowerCase():"",N=e=>e.payment_interval?e.payment_interval.toLowerCase():"";return(0,j.jsx)(j.Fragment,{children:(0,j.jsx)("div",{className:"overflow-x-auto overflow-y-visible container-full-width cm-scrollbar",children:d&&d.length?(0,j.jsxs)("table",{className:"subs-table min-w-full divide-y bg-gray-50 divide-gray-200 min-h-[50px]",children:[(0,j.jsx)("thead",{children:(0,j.jsxs)("tr",{className:"sub_tbl h-3",children:[(0,j.jsx)("td",{className:"px-6 py-2 bg-gray-50 text-center text-[12px] font-medium bg-gray-200 text-gray-500 uppercase tracking-wider border",children:"Plan Details"}),(0,j.jsx)("td",{className:"px-6 py-2 bg-gray-50 text-center text-[12px] font-medium bg-gray-200 text-gray-500 uppercase tracking-wider border",children:"Amount"}),(0,j.jsx)("td",{className:"px-6 py-2 bg-gray-50 text-center text-[12px] font-medium bg-gray-200 text-gray-500 uppercase tracking-wider border",children:"Trial Period"}),(0,j.jsx)("td",{className:"px-6 py-2 bg-gray-50 text-center text-[12px] font-medium bg-gray-200 text-gray-500 uppercase tracking-wider border",children:"Starts On"}),(d&&d.length>0&&d.some((e=>"inactive"===e.status))||d.some((e=>"paused"===e.status)))&&(0,j.jsx)("td",{className:"px-6 py-2 bg-gray-50 text-center text-[12px] font-medium bg-gray-200 text-gray-500 uppercase tracking-wider border",children:"Expires On"}),(0,j.jsx)("td",{className:"px-6 py-2 bg-gray-50 text-center text-[12px] font-medium bg-gray-200 text-gray-500 uppercase tracking-wider border",children:"Status"}),(0,j.jsx)("td",{className:"px-6 py-2 bg-gray-50 text-center text-[12px] font-medium bg-gray-200 text-gray-500 uppercase tracking-wider border",children:"Actions"})]})}),(0,j.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:null==d?void 0:d.map(((e,o)=>{return(0,j.jsxs)("tr",{className:"sub_tbl h-3",children:[(0,j.jsx)("td",{className:"px-6 py-2 bg-gray-50 text-center text-[12px] text-gray-500 uppercase tracking-wider border font-medium",children:(c=e.plan_type,c&&"enterprise"!==c.toLowerCase()?e.plan_type.replace("ProMax"," pro max"):e.plan_name)}),(0,j.jsx)("td",{className:"px-6 py-2 bg-gray-50 text-center text-[12px] text-gray-500 uppercase tracking-wider border font-medium",children:e.price_label}),(0,j.jsx)("td",{className:"px-6 py-2 bg-gray-50 text-center text-[12px] text-gray-500 uppercase tracking-wider border font-medium",children:e.trial_days?"Yes":"No"}),(0,j.jsx)("td",{className:"px-6 py-2 bg-gray-50 text-center text-[12px] text-gray-500 uppercase tracking-wider border font-medium",children:(0,u.p6)(e.start_date)}),d&&d.length>0&&d.some((e=>"inactive"===e.status))?"active"===e.status?(0,j.jsx)("td",{className:"inactive px-6 py-2 bg-gray-50 text-center text-[12px] text-gray-500 uppercase tracking-wider border font-medium"}):(0,j.jsx)("td",{className:"inactive px-6 py-2 bg-gray-50 text-center text-[12px] text-gray-500 uppercase tracking-wider border font-medium",children:(0,u.p6)(e.end_date)}):"active"===e.status?"":(e.status,(0,j.jsx)("td",{className:"active px-6 py-2 bg-gray-50 text-center text-[12px] text-gray-500 uppercase tracking-wider border font-medium",children:(0,u.p6)(e.end_date)})),(0,j.jsx)("td",{className:"px-6 py-2 bg-gray-50 text-center text-[12px] text-gray-500 uppercase tracking-wider border font-medium",children:e.status}),(0,j.jsxs)("td",{className:"px-6 py-2 bg-gray-50 text-center text-[12px] text-gray-500 uppercase tracking-wider border font-medium",children:["paused"===e.status?(0,j.jsx)("div",{className:"text-[12px]",children:(0,j.jsxs)(h.v,{as:"div",className:"relative inline-block text-center w-full text-[12px]",children:[(0,j.jsx)("div",{className:"w-full",children:(0,j.jsx)(h.v.Button,{className:"inline-flex w-full justify-center rounded-md bg-sky-500/100 px-4 py-2 font-medium text-white hover:bg-[#49b1df]",children:"Options"})}),(0,j.jsx)(x.u,{as:s.Fragment,enter:"transition ease-out duration-100",enterFrom:"transform opacity-0 scale-95",enterTo:"transform opacity-100 scale-100",leave:"transition ease-in duration-75",leaveFrom:"transform opacity-100 scale-100",leaveTo:"transform opacity-0 scale-95",children:(0,j.jsx)(h.v.Items,{className:"".concat(o<3?"dp-top":"dp-bot"," absolute w-50 origin-top-right divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none text-center min-w-full text-center"),children:(0,j.jsxs)("div",{className:"text-center min-w-full",children:["recurly"===f(e)||"stripe"===f(e)?(0,j.jsx)(h.v.Item,{children:t=>{let{active:r}=t;return(0,j.jsx)("button",{className:"".concat(r?"bg-sky-500/100 text-white":"text-gray-900"," group min-w-full items-center px-4 py-2 text-left border-b border-b-slate-100"),pid:e.pid,onClick:b,children:"Change Card"})}}):"","recurly"!==f(e)&&"stripe"!==f(e)&&"fastspring"!==f(e)&&"cardtransaction"!==f(e)||"paused"!==e.status||"monthly"!==N(e)?"":(0,j.jsx)(h.v.Item,{children:e=>{let{active:t}=e;return(0,j.jsx)("button",{className:"".concat(t?"bg-sky-500/100 text-white":"text-gray-900"," group min-w-full items-center px-4 py-2 text-left border-b border-b-slate-100"),onClick:e=>{window.location.href="/resume"},children:"Resume"})}}),(0,j.jsx)(h.v.Item,{children:t=>{let{active:r}=t;return(0,j.jsx)(z,{pid:e.pid,tk:g,showOffer:p})}})]})})})]})}):"","active"===e.status?(0,j.jsx)("div",{className:"text-[12px]",children:(0,j.jsxs)(h.v,{as:"div",className:"relative inline-block text-center w-full text-[12px]",children:[(0,j.jsx)("div",{className:"w-full",children:(0,j.jsx)(h.v.Button,{className:"inline-flex w-full justify-center rounded-md bg-sky-500/100 px-4 py-2 font-medium text-white hover:bg-[#49b1df]",children:"Options"})}),(0,j.jsx)(x.u,{as:s.Fragment,enter:"transition ease-out duration-100",enterFrom:"transform opacity-0 scale-95",enterTo:"transform opacity-100 scale-100",leave:"transition ease-in duration-75",leaveFrom:"transform opacity-100 scale-100",leaveTo:"transform opacity-0 scale-95",children:(0,j.jsx)(h.v.Items,{className:"".concat(o<3?"dp-top":"dp-bot"," absolute w-50 origin-top-right divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none text-center min-w-full text-center"),children:(0,j.jsxs)("div",{className:"text-center min-w-full",children:["recurly"===f(e)||"stripe"===f(e)?(0,j.jsx)(h.v.Item,{children:t=>{let{active:r}=t;return(0,j.jsx)("button",{className:"".concat(r?"bg-sky-500/100 text-white":"text-gray-900"," group min-w-full items-center px-4 py-2 text-left border-b border-b-slate-100"),pid:e.pid,onClick:b,children:"Change Card"})}}):"",("usd"===v(e)||"paddle"===f(e)||"stripe"===f(e)||"paypal"===f(e)||"fastspring"===f(e)||"cardtransaction"===f(e))&&(0,u.tN)((0,u.o0)(i),(0,u.o0)(e.start_date))>=0?(0,j.jsxs)(j.Fragment,{children:["enterprise"!==w(e)&&"yearly"!==N(e)&&"promax"!==w(e)&&"97"!==m||"97"===m&&"yearly"!==N(e)&&"promax"===w(e)||"basic"===w(e)||"pro"===w(e)?(0,j.jsx)(h.v.Item,{children:r=>{let{active:s}=r;return(0,j.jsx)("button",{className:"".concat(s?"bg-sky-500/100 text-white":"text-gray-900"," group min-w-full items-center px-4 py-2 text-left border-b border-b-slate-100"),onClick:r=>(e=>{"enterprise"===e?t(!0):window.location.href="/upgrade"})(w(e)),children:"Upgrade"})}}):"","basic"===w(e)&&"monthly"===N(e)||"enterprise"===w(e)?"":(0,j.jsx)(h.v.Item,{children:e=>{let{active:t}=e;return(0,j.jsx)("button",{className:"".concat(t?"bg-sky-500/100 text-white":"text-gray-900"," group min-w-full items-center px-4 py-2 text-left border-b border-b-slate-100"),onClick:y,children:"Downgrade"})}}),"recurly"!==f(e)&&"stripe"!==f(e)&&"fastspring"!==f(e)&&"cardtransaction"!==f(e)||"active"!==e.status||"monthly"!==N(e)||"yes"!==e.is_trial_end||"Enterprise"===e.plan_type?"":(0,j.jsx)(h.v.Item,{children:t=>{let{active:s}=t;return(0,j.jsx)("button",{className:"".concat(s?"bg-sky-500/100 text-white":"text-gray-900"," group min-w-full items-center px-4 py-2 text-left border-b border-b-slate-100"),onClick:t=>(e=>{a(e.merchant_subscription_id),l(e.merchant),n(e.pid),r(!0)})(e),children:"Pause"})}})]}):(0,j.jsx)(j.Fragment,{}),(0,j.jsx)(h.v.Item,{children:t=>{let{active:r}=t;return(0,j.jsx)(z,{pid:e.pid,tk:g,showOffer:p})}})]})})})]})}):""]})]},o);var c}))})]}):(0,j.jsx)("div",{children:(0,j.jsxs)("span",{className:"text-[12px] text-gray-600",children:[(0,j.jsx)(c.DAO,{className:"inline text-lg mr-1"})," No active subscription. Look for available ",(0,j.jsx)("a",{href:"/pricing",className:"text-blue-400 font-bold",children:"SUBSCRIPTIONS"})]})})})})},G=e=>{let{user_order:t}=e;if(null!=t)return(0,j.jsx)("div",{className:"overflow-x-auto custom-scrollbar container-full-width bg-gray-50",children:t&&t.length?(0,j.jsxs)("table",{className:"min-w-full divide-y divide-gray-200 min-h-[50px]",children:[(0,j.jsx)("thead",{children:(0,j.jsxs)("tr",{className:"sub_tbl h-3",children:[(0,j.jsx)("th",{className:"px-6 py-3 bg-gray-50 text-center text-[12px] font-medium bg-gray-200 text-gray-500 uppercase tracking-wider",children:"Invoice"}),(0,j.jsx)("th",{className:"px-6 py-3 bg-gray-50 text-center text-[12px] font-medium bg-gray-200 text-gray-500 uppercase tracking-wider",children:"Membership"}),(0,j.jsx)("th",{className:"px-6 py-3 bg-gray-50 text-center text-[12px] font-medium bg-gray-200 text-gray-500 uppercase tracking-wider",children:"Amount"}),(0,j.jsx)("th",{className:"px-6 py-3 bg-gray-50 text-center text-[12px] font-medium bg-gray-200 text-gray-500 uppercase tracking-wider",children:"Date"}),(0,j.jsx)("th",{className:"px-6 py-3 bg-gray-50 text-center text-[12px] font-medium bg-gray-200 text-gray-500 uppercase tracking-wider",children:"Status"})]})}),(0,j.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:null==t?void 0:t.map(((e,t)=>(0,j.jsxs)("tr",{className:"sub_tbl h-3",children:[(0,j.jsx)("td",{className:"px-6 py-3 bg-gray-50 text-center text-[12px] text-gray-500 uppercase tracking-wider border font-medium",children:e.charge_id}),(0,j.jsx)("td",{className:"px-6 py-3 bg-gray-50 text-center text-[12px] text-gray-500 uppercase tracking-wider border font-medium",children:e.label}),(0,j.jsx)("td",{className:"px-6 py-3 bg-gray-50 text-center text-[12px] text-gray-500 uppercase tracking-wider border font-medium",children:e.amount?(0,u.yt)(e.currency,e.amount):""}),(0,j.jsx)("td",{className:"px-6 py-3 bg-gray-50 text-center text-[12px] text-gray-500 uppercase tracking-wider border font-medium",children:(0,u.p6)(e.created_at)}),(0,j.jsx)("td",{className:"px-6 py-3 bg-gray-50 text-center text-[12px] text-gray-500 uppercase tracking-wider border font-medium",children:"0000-00-00 00:00:00"!==e.refund_at?"Refunded":"Completed"})]},t)))})]}):(0,j.jsxs)("span",{className:"text-[12px] text-gray-600",children:[(0,j.jsx)(c.DAO,{className:"inline text-lg mr-1"})," No orders have been made yet. Look for available ",(0,j.jsx)("a",{href:"/pricing",className:"text-blue-400 font-bold",children:"SUBSCRIPTIONS"})]})})},H=()=>(0,j.jsxs)("div",{className:"w-full",children:[(0,j.jsxs)("p",{className:"text-sm py-4",children:["If you have any comments about our website, AI tools. and articles, or if you have questions about your account access, please don't hesitate to get in touch with us. Leave your messages through the ",(0,j.jsx)("a",{href:"https://ai-pro.org/contact-us/",className:"text-blue-400 font-bold",children:"Contact us"})," page."]}),(0,j.jsx)("p",{className:"font-bold",children:"Quick Links:"}),(0,j.jsxs)("ul",{children:[(0,j.jsx)("li",{children:(0,j.jsx)("a",{href:"/my-account",className:"text-blue-400 font-bold px-2",children:" My Apps"})}),(0,j.jsx)("li",{children:(0,j.jsx)("a",{href:"/my-account",className:"text-blue-400 font-bold px-2",children:" AI Tools"})}),(0,j.jsx)("li",{children:(0,j.jsx)("a",{href:"https://ai-pro.org/articles/",className:"text-blue-400 font-bold px-2",children:" Articles"})}),(0,j.jsx)("li",{children:(0,j.jsx)("a",{href:"https://ai-pro.org/tutorials/",className:"text-blue-400 font-bold px-2",children:" Tutorials"})})]})]}),R=()=>(0,j.jsx)("div",{children:(0,j.jsx)(a.E.button,{className:"bg-blue-500 mb-1 text-white font-bold py-3 px-6 my-3 rounded-lg",whileTap:{scale:.9},onClick:function(){(0,o.Sz)("access"),(0,o.Sz)("ci_session"),m.Z.get("".concat("http://localhost:9002/api","/logout")).then((function(){window.location.href="/login"}))},children:"Logout"})}),V=e=>{const[t]=(0,s.useState)(e.email),[r,a]=(0,s.useState)(!1),l=()=>{a(!1)};return(0,j.jsxs)(j.Fragment,{children:[(0,j.jsx)("span",{className:"cursor-pointer hover:underline hover:decoration-solid",onClick:()=>{a(!0)},children:"Delete Account"}),(0,j.jsx)(x.u,{appear:!0,show:r,as:s.Fragment,children:(0,j.jsxs)(p.V,{as:"div",className:"relative z-10",onClose:l,children:[(0,j.jsx)(x.u.Child,{as:s.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:(0,j.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-25"})}),(0,j.jsx)("div",{className:"fixed inset-0 overflow-y-auto",children:(0,j.jsx)("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:(0,j.jsx)(x.u.Child,{as:s.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:(0,j.jsxs)(p.V.Panel,{className:"w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all",children:[(0,j.jsx)(p.V.Title,{as:"h3",className:"text-lg font-medium leading-6 text-gray-900",children:"Delete Account"}),(0,j.jsxs)("div",{className:"mt-2",children:[(0,j.jsx)("p",{className:"text-sm text-gray-500",children:"Deleting your account will permanently remove all of your data and information associated with it. This action is irreversible, and you won't be able to recover your account or any of its contents."}),(0,j.jsx)("p",{className:"text-sm text-gray-500",children:"Are you sure you want to delete your account?"})]}),(0,j.jsxs)("div",{className:"mt-4",children:[(0,j.jsx)("button",{type:"button",className:"inline-flex justify-center rounded-md border border-transparent bg-red-500 px-4 py-2 mr-2 text-sm font-medium text-white hover:bg-[#d30101]",onClick:()=>{document.querySelector(".loader-container").classList.add("active"),m.Z.post("".concat("http://localhost:9002/api","/delete-account"),{},{headers:{"content-type":"application/x-www-form-urlencoded"}}).then((function(e){if(e.data.success)return window.mixpanel.people.set_once({$email:t}),window.mixpanel.identify(t),window.mixpanel.track("delete_account",{}),f().success("Success"),void window.location.reload();document.querySelector(".loader-container").classList.remove("active"),f().error("Fail")}))},children:"Yes! Delete My Account"}),(0,j.jsx)("button",{type:"button",className:"inline-flex justify-center rounded-md border border-transparent bg-gray-100 px-4 py-2 text-sm font-medium text-black hover:bg-gray-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-gray-500 focus-visible:ring-offset-2",onClick:l,children:"Cancel"})]})]})})})})]})})]})},z=e=>{let{pid:t,tk:r,showOffer:a}=e;const[l,n]=(0,s.useState)(!1),o=(0,i.gx)(),d=()=>{n(!1)};return(0,j.jsxs)(j.Fragment,{children:[(0,j.jsx)("button",{className:"hover:bg-sky-500/100 hover:text-white text-gray-900 group min-w-full items-center px-4 py-2 text-left",pid:t,onClick:()=>{n(!0)},children:"Cancel"}),(0,j.jsx)(x.u,{appear:!0,show:l,as:s.Fragment,children:(0,j.jsxs)(p.V,{as:"div",className:"relative z-10",onClose:d,children:[(0,j.jsx)(x.u.Child,{as:s.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:(0,j.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-25"})}),(0,j.jsx)("div",{className:"fixed inset-0 overflow-y-auto",children:(0,j.jsx)("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:(0,j.jsx)(x.u.Child,{as:s.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:(0,j.jsxs)(p.V.Panel,{className:"w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all",children:[(0,j.jsx)(p.V.Title,{as:"h3",className:"text-lg font-medium leading-6 text-gray-900",children:"Cancel Subscription"}),(0,j.jsx)("div",{className:"mt-2",children:(0,j.jsx)("p",{className:"text-sm text-gray-500",children:"Are you sure you want to cancel your subscription?"})}),(0,j.jsxs)("div",{className:"mt-4 text-right",children:[(0,j.jsx)("button",{type:"button",className:"inline-flex justify-center rounded-md border border-transparent bg-red-500 px-4 py-2 mr-2 text-sm font-medium text-white hover:bg-[#d30101]",onClick:e=>{t&&(d(),""!==o.surveydata&&null!==o.surveydata||"yes"===A?window.location.href="/survey":T&&""!==T?a({onDecline:()=>{window.location.href="/survey"}}):window.location.href="/survey")},children:"Yes"}),(0,j.jsx)("button",{type:"button",className:"inline-flex justify-center rounded-md border border-transparent bg-gray-100 px-4 py-2 text-sm font-medium text-black hover:bg-gray-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-gray-500 focus-visible:ring-offset-2",onClick:d,children:"Close"})]})]})})})})]})})]})};function Y(e,t){return e.slice(0,t)}function Z(e,t){if(e.length<t)return 1;let r=e.length,s=Math.floor(r/t);return r%t>0&&(s+=1),s}const K=e=>{let{handleShowAddMember:t,enterpriseMembers:r,handleReloadMembers:l,handleShowEditMemberModal:n,currentPlan:i,currentPlanName:o}=e;const[d,m]=(0,s.useState)(""),[p,g]=(0,s.useState)([]),[b,y]=(0,s.useState)([]),[f,v]=(0,s.useState)(5),[w,N]=(0,s.useState)(0),[k,_]=(0,s.useState)(1);(0,s.useEffect)((()=>{let e=r;g(e),e=Y(e,5),y(e);let t=Z(r,5);N(t)}),[r]);const S=()=>{let e=document.getElementById("ent_search").value,t=r;t=r.filter((function(t){let r=t.first_name+" "+t.last_name;return t.first_name.indexOf(e)>-1||t.last_name.indexOf(e)>-1||t.email.indexOf(e)>-1||r.indexOf(e)>-1}));let s=Z(t,f);N(s),g(t),t=Y(t,f),y(t)};(0,s.useEffect)((()=>{S()}),[f]);const C=e=>{_(e);let t=p,r=(e-1)*f;t=t.slice(r,t.length),t=Y(t,f),y(t)};return(0,j.jsx)(j.Fragment,{children:(0,j.jsxs)("div",{className:"w-full",children:[(0,j.jsxs)("div",{className:"block md:grid md:grid-cols-3 md:gap-4 text-[12px]",children:[(0,j.jsx)("div",{className:"text-left md:col-span-1",children:(0,j.jsx)("input",{className:"w-full px-3 py-2 mb-2 border border-gray-300 rounded fs-exclude",type:"text",id:"ent_search",name:"search",placeholder:"Search by Name or Email",value:d,onChange:e=>{S(),m(e.target.value)}})}),(0,j.jsx)("div",{className:"text-left md:col-span-1",children:(0,j.jsx)(a.E.button,{className:"bg-sky-500 w-full md:w-48 text-white font-bold py-2 px-6 rounded-md proceed-pmt",whileHover:{backgroundColor:"#49b1df"},whileTap:{scale:.9},onClick:()=>t(),children:"+ Add Member"})}),r.length>0?(0,j.jsxs)("div",{className:"text-center md:text-right md:col-span-1 my-2 text-[11px]",children:["Show ",(0,j.jsxs)("select",{className:"border rounded-md",onChange:e=>(v(e.target.value),void _(1)),children:[(0,j.jsx)("option",{value:"5",children:"5"}),(0,j.jsx)("option",{value:"10",children:"10"}),(0,j.jsx)("option",{value:"15",children:"15"}),(0,j.jsx)("option",{value:"20",children:"20"})]})," entries"]}):""]}),(0,j.jsx)("div",{className:"overflow-x-scroll lg:overflow-x-visible overflow-y-visible container-full-width cm-scrollbar pb-1 min-h-[400px]",children:b&&b.length?(0,j.jsxs)("table",{className:"subs-table min-w-full divide-y bg-gray-50 divide-gray-200 min-h-[50px]",children:[(0,j.jsx)("thead",{children:(0,j.jsxs)("tr",{className:"sub_tbl h-3",children:[(0,j.jsx)("td",{className:"px-6 py-2 bg-gray-50 text-center text-[12px] font-medium bg-gray-200 text-gray-500 uppercase tracking-wider border",children:"Fullname"}),(0,j.jsx)("td",{className:"px-6 py-2 bg-gray-50 text-center text-[12px] font-medium bg-gray-200 text-gray-500 uppercase tracking-wider border",children:"Email"}),(0,j.jsx)("td",{className:"px-6 py-2 bg-gray-50 text-center text-[12px] font-medium bg-gray-200 text-gray-500 uppercase tracking-wider border",children:"Status"}),(0,j.jsx)("td",{className:"px-6 py-2 bg-gray-50 text-center text-[12px] font-medium bg-gray-200 text-gray-500 uppercase tracking-wider border",children:"Date Added"}),"enterprise"===i.toLowerCase()&&"enterprise"!==o.toLowerCase()?(0,j.jsx)("td",{className:"px-6 py-2 bg-gray-50 text-center text-[12px] font-medium bg-gray-200 text-gray-500 uppercase tracking-wider border",children:"Total Usage"}):(0,j.jsx)("td",{className:"px-6 py-2 bg-gray-50 text-center text-[12px] font-medium bg-gray-200 text-gray-500 uppercase tracking-wider border",children:"Total Token Used"}),(0,j.jsx)("td",{className:"px-6 py-2 bg-gray-50 text-center text-[12px] font-medium bg-gray-200 text-gray-500 uppercase tracking-wider border",children:"Actions"})]})}),(0,j.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:null==b?void 0:b.map(((e,t)=>(0,j.jsxs)("tr",{className:"sub_tbl h-3",children:[(0,j.jsxs)("td",{className:"px-6 py-2 bg-gray-50 text-center text-[12px] text-gray-500 uppercase tracking-wider border font-medium",children:[e.first_name," ",e.last_name]}),(0,j.jsx)("td",{className:"px-6 py-2 bg-gray-50 text-center text-[12px] text-gray-500 uppercase tracking-wider border font-medium",children:e.email}),(0,j.jsx)("td",{className:"px-6 py-2 bg-gray-50 text-center text-[12px] text-gray-500 uppercase tracking-wider border font-medium",children:e.status}),(0,j.jsx)("td",{className:"px-6 py-2 bg-gray-50 text-center text-[12px] text-gray-500 uppercase tracking-wider border font-medium",children:(0,u.p6)(e.created_at)}),(0,j.jsx)("td",{className:"px-6 py-2 bg-gray-50 text-center text-[12px] text-gray-500 tracking-wider border font-medium",children:"enterprise"===i.toLowerCase()&&"enterprise"!==o.toLowerCase()?(0,j.jsxs)(j.Fragment,{children:[(0,j.jsxs)("p",{children:["Tokens: ",e.total_token]}),(0,j.jsxs)("p",{children:["Flux: ",e.total_flux_prompt]}),(0,j.jsxs)("p",{children:["o1: ",e.total_o1_prompt]})]}):(0,j.jsx)("p",{children:e.total_token})}),(0,j.jsx)("td",{className:"px-6 py-2 bg-gray-50 text-center text-[12px] text-gray-500 uppercase tracking-wider border font-medium",children:"active"===e.status?(0,j.jsx)("div",{className:"text-[12px]",children:(0,j.jsxs)(h.v,{as:"div",className:"relative inline-block text-center w-full text-[12px]",children:[(0,j.jsx)("div",{className:"w-full",children:(0,j.jsx)(h.v.Button,{className:"inline-flex w-full justify-center rounded-md bg-sky-500/100 px-4 py-2 font-medium text-white hover:bg-[#49b1df]",children:"Options"})}),(0,j.jsx)(x.u,{as:s.Fragment,enter:"transition ease-out duration-100",enterFrom:"transform opacity-0 scale-95",enterTo:"transform opacity-100 scale-100",leave:"transition ease-in duration-75",leaveFrom:"transform opacity-100 scale-100",leaveTo:"transform opacity-0 scale-95",children:(0,j.jsx)(h.v.Items,{className:"".concat(t<3?"dp-top":"dp-bot"," z-[9999] absolute w-50 origin-top-right divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none text-center min-w-full text-center"),children:(0,j.jsxs)("div",{className:"text-center min-w-full",children:[(0,j.jsx)(h.v.Item,{children:(0,j.jsx)("button",{className:"group min-w-full items-center px-4 py-2 text-left border-b border-b-slate-100 text-gray-900",onClick:(t,r,s,a)=>((e,t,r,s)=>{let a={};a.user_id=e,a.first_name=t,a.last_name=r,a.email=s,n(!0,a)})(e.user_id,e.first_name,e.last_name,e.email),children:"Edit Info"})}),(0,j.jsx)(h.v.Item,{children:(0,j.jsx)(Q,{member_user_id:e.user_id,email:e.email})}),(0,j.jsx)(h.v.Item,{children:(0,j.jsx)($,{member_user_id:e.user_id,email:e.email,handleReloadMembers:l})})]})})})]})}):""})]},t)))})]}):(0,j.jsx)("div",{children:(0,j.jsxs)("span",{className:"text-[12px] text-gray-600",children:[(0,j.jsx)(c.DAO,{className:"inline text-lg mr-1"}),"No members found."]})})}),(0,j.jsxs)("div",{className:"py-2 text-[11px]",children:[(0,j.jsx)("span",{className:"block md:flex float-left w-full md:w-auto text-center md:text-right py-2 justify-center md:justify-normal items-center md:items-start",children:(()=>{let e=p.length,t=k*f,r=t-(f-1);return t>e&&(t=e),"Showing "+r+" to "+t+" of "+e+" entries"})()}),(0,j.jsx)("ul",{className:"w-full md:w-auto flex float-none md:float-right justify-center md:justify-normal items-center md:items-end",children:(()=>{let e=[];if(w<=1||0===r.length)return e;k>1?e.push((0,j.jsx)("li",{className:"p-2 cursor-pointer",onClick:e=>C(k-1),children:"Previous"},"prev_page")):e.push((0,j.jsx)("li",{className:"p-2 cursor-default",children:"Previous"},"prev_page"));for(let t=0;t<w;t++){const r="p-2 rounded cursor-pointer w-[30px] text-center ".concat(k===t+1?"border":"");e.push((0,j.jsx)("li",{className:r,onClick:e=>C(t+1),children:t+1},t+1))}return k<w?e.push((0,j.jsx)("li",{className:"p-2 cursor-pointer",onClick:e=>C(k+1),children:"Next"},"prev_page")):e.push((0,j.jsx)("li",{className:"p-2 cursor-default",children:"Next"},"prev_page")),e})()})]})]})})},J=e=>{let{showEditMember:t,setShowEditEnterprise:r,editMemberDetails:a,handleReloadMembers:l}=e;const[n,i]=(0,s.useState)(""),[o,d]=(0,s.useState)(""),[c,u]=(0,s.useState)(""),[x,p]=(0,s.useState)(""),[h,g]=(0,s.useState)(""),b=()=>{let e=document.getElementById("modalEditMembers");if(null!==e){let t=document.getElementById("ent-edit-email");t.title="",t.classList.remove("ent-field-error");let s=document.getElementById("ent-edit-fullname");s.title="",s.classList.remove("ent-field-error"),e.style.display="none",r(!1)}};(0,s.useEffect)((()=>{let e=a.first_name?a.first_name:"",t=a.last_name?a.last_name:"",r=a.email?a.email:"",s=a.user_id?a.user_id:"";i(e+" "+t),d(e+" "+t),u(r),p(r),g(s)}),[a]);void 0!==t&&!0===t&&(()=>{let e=document.getElementById("modalEditMembers");null!==e&&(e.style.display="block")})(),void 0!==t&&!1===t&&b();return(0,j.jsx)(j.Fragment,{children:(0,j.jsx)("div",{id:"modalEditMembers",className:"modal z-[9999]",children:(0,j.jsx)("div",{class:"modal-content w-full md:w-[50%]",children:(0,j.jsxs)("div",{children:[(0,j.jsxs)("div",{className:"mb-4 flex border-b pb-2",children:[(0,j.jsx)("div",{className:"text-blue-500 w-full md:w-2/3 mr-2 md:mr-5 font-bold",children:"Edit Members"}),(0,j.jsx)("div",{className:"float-right mt-[-5px] w-full md:w-1/3",children:(0,j.jsx)("span",{class:"close",onClick:()=>b(),children:"×"})})]}),(0,j.jsxs)("div",{className:"border-b border-[#dddddd] mx-auto my-4 pb-4",children:[(0,j.jsxs)("div",{className:"w-full text-[12px] md:text-[12px] font-bold pb-2",children:[(0,j.jsx)("p",{children:"Fulname *"}),(0,j.jsx)("input",{type:"text",id:"ent-edit-fullname",className:"w-full px-3 py-2 border border-gray-300 rounded",placeholder:"Fullname",value:n,onChange:e=>{e.preventDefault(),i(e.target.value),(e=>{let t=e.target.value.trim(),r=document.getElementById("ent-edit-fullname-error");""===t?(r.innerHTML="Full Name is required",r.style.display="block",e.target.classList.add("ent-field-error")):(r.innerHTML="",r.style.display="none",e.target.classList.remove("ent-field-error"))})(e)},title:""}),(0,j.jsx)("div",{class:"member-error",id:"ent-edit-fullname-error"})]}),(0,j.jsxs)("div",{className:"w-full text-[12px] md:text-[12px] font-bold pb-2",children:[(0,j.jsx)("p",{children:"Email *"}),(0,j.jsx)("input",{type:"text",id:"ent-edit-email",className:"w-full px-3 py-2 border border-gray-300 rounded",placeholder:"Email",value:c,onChange:e=>{e.preventDefault(),u(e.target.value),(e=>{let t=e.target.value,r=document.getElementById("ent-edit-email-error");""===t.trim()?(r.innerHTML="Email is required",r.style.display="block",e.target.classList.add("ent-field-error")):/\S+@\S+\.\S+/.test(t)?(r.innerHTML="",r.style.display="none",e.target.classList.remove("ent-field-error")):(r.innerHTML="Invalid email format",r.style.display="block",e.target.classList.add("ent-field-error"))})(e)},title:""}),(0,j.jsx)("div",{class:"member-error",id:"ent-edit-email-error"})]})]}),(0,j.jsx)("div",{className:"",children:(0,j.jsxs)("div",{className:"text-right",children:[(0,j.jsx)("input",{type:"button",value:"Update Member",className:"border rounded font-bold bg-blue-500 text-white py-2 px-4 cursor-pointer text-[12px] md:text-[14px] mr-2",onClick:()=>(async()=>{let e=document.getElementById("ent-edit-fullname"),t=document.getElementById("ent-edit-fullname-error"),r=document.getElementById("ent-edit-email"),s=document.getElementById("ent-edit-email-error");if(""===r.value.trim())return s.innerHTML="Email is required",s.style.display="block",void r.classList.add("ent-field-error");if(!/\S+@\S+\.\S+/.test(c))return s.innerHTML="Invalid email format",s.style.display="block",void r.classList.add("ent-field-error");if(""===e.value.trim())return t.innerHTML="Full Name is required",t.style.display="block",void e.classList.add("ent-field-error");if(document.querySelector(".loader-container").classList.add("active"),o===e.value&&x===r.value)return b(),void document.querySelector(".loader-container").classList.remove("active");let a="".concat("http://localhost:9002/api","/edit-enterprise-member");m.Z.post(a,{member_user_id:h,member_email:r.value,member_fullname:e.value,member_old_fullname:o,member_old_email:x},{headers:{"content-type":"application/x-www-form-urlencoded"}}).then((function(e){let t=e.data;if(document.querySelector(".loader-container").classList.remove("active"),t.success)if(Array.isArray(t.data)&&t.data.length>0)for(var r=0;r<t.data.length;r++){let e=document.getElementById("ent-edit-email");e.title=t.data[r].error,e.classList.add("ent-field-error"),f().error(t.data[r].error)}else b(),l(),f().success("Member Updated");else b(),l(),f().error("Update Member Failed")})).catch((function(e){b(),document.querySelector(".loader-container").classList.remove("active"),f().error("Something went wrong. Please try again in a bit!")}))})()}),(0,j.jsx)("input",{type:"button",value:"Close",className:"border rounded font-bold bg-white text-black py-2 px-4 cursor-pointer text-[12px] md:text-[14px]",onClick:()=>b()})]})})]})})})})},W=e=>{let{showAddMember:t,setshowAddMember:r,handleReloadMembers:a,entMaxMembers:l,enterpriseMembers:n,isMax:i,isVisbleBtn:o,setIsVisibleBtn:d}=e;const[u,x]=(0,s.useState)([{name:"",email:"",error:""}]),[p,h]=(0,s.useState)(!0),[g,b]=(0,s.useState)(!0),y=()=>{x((e=>[...e,{name:"",email:"",error:""}]));let e=u.length+n.length;d(!0),b(!0),i&&e+1>=l&&h(!1)},v=async()=>{await x((e=>e.filter(((e,t)=>t<=-1)))),y(),h(!0),b(!0)},w=e=>{x((t=>t.filter(((t,r)=>r!==e))));let t=u.length+n.length-1;i&&t<l&&(d(!0),h(!0)),i&&1===u.length&&b(!1)},N=()=>{let e=document.getElementById("modalAddMembers");null!==e&&(e.style.display="none",r(!1))},k=async()=>{let e=0,t=!1,r="",s="",a="",l="",n="",i="",o=!1;for(;e<u.length;){r=document.getElementById("name-"+e),s=document.getElementById("email-"+e),l=document.getElementById("error1-"+e),n=document.getElementById("error2-"+e);let d=u[e].email.trim();a=u[e].name.trim(),i=u[e].error.trim(),l.style.display="none",n.style.display="none",r.title="",r.classList.remove("ent-field-error"),s.title="",s.classList.remove("ent-field-error"),""===d?(s.title="Email is required",s.classList.add("ent-field-error"),n.style.display="block",n.innerHTML="Email is required",t=!0):/\S+@\S+\.\S+/.test(d)||(s.title="Invalid email format",s.classList.add("ent-field-error"),n.style.display="block",n.innerHTML="Invalid email format",t=!0),""!==i&&(s.title=i,s.classList.add("ent-field-error"),t=!0),""===a&&(r.title="Full Name is required",r.classList.add("ent-field-error"),l.innerHTML="Full Name is required",l.style.display="block",t=!0),t||(o=u.filter((function(e){return e.email.trim()===d})),o.length>1&&(s.title="Duplicate email",s.classList.add("ent-field-error"),n.innerHTML="Duplicate email",n.style.display="block",t=!0)),e++}return t};return void 0!==t&&!0===t&&(()=>{let e=document.getElementById("modalAddMembers");null!==e&&(e.style.display="block")})(),void 0!==t&&!1===t&&N(),(0,j.jsx)(j.Fragment,{children:(0,j.jsx)("div",{id:"modalAddMembers",className:"modal z-[9999]",children:(0,j.jsx)("div",{class:"modal-content w-full md:w-[60%]",children:(0,j.jsxs)("div",{children:[(0,j.jsxs)("div",{className:"mb-4 flex border-b pb-2",children:[(0,j.jsx)("div",{className:"text-blue-500 w-full md:w-2/3 mr-2 md:mr-5 font-bold",children:"Add Members"}),(0,j.jsx)("div",{className:"float-right mt-[-5px] w-full md:w-1/3",children:(0,j.jsx)("span",{class:"close",onClick:()=>N(),children:"×"})})]}),(0,j.jsxs)("div",{className:"text-[11px] pb-2",children:[(0,j.jsx)("b",{children:"Note:"})," The password for each member will be sent to their email address"]}),(0,j.jsxs)("div",{className:"",children:[(0,j.jsxs)("div",{className:"border rounded-tr rounded-tl flex p-2",children:[(0,j.jsx)("div",{className:"w-1/2 text-[12px] md:text-[12px] font-bold",children:"Full Name"}),(0,j.jsx)("div",{className:"w-1/2 ml-[-4px] text-[12px] md:text-[12px] font-bold",children:"Email"})]}),null==u?void 0:u.map(((e,t)=>(0,j.jsxs)("div",{className:"flex py-2 pl-2 bg-gray-100",children:[(0,j.jsxs)("div",{className:"w-1/2 mr-2 text-[11px] md:text-[14px]",children:[(0,j.jsx)("input",{type:"text",id:"name-"+t,index:t,className:"w-full px-3 py-2 border border-gray-300 rounded fs-exclude",placeholder:"Enter Full Name",value:e.name,onChange:e=>{x((r=>{const s=r.slice();return s[t].name=e.target.value,((e,t)=>{let r=e.target.value.trim(),s=document.getElementById("email-"+t).value,a=document.getElementById("error1-"+t);""===r&&""!==s?(e.target.title="Full Name is required",e.target.classList.add("ent-field-error"),a.style.display="block",a.innerHTML="Full Name is required"):(e.target.title="",e.target.classList.remove("ent-field-error"),a.style.display="none",a.innerHTML="")})(e,t),s}))},title:""}),(0,j.jsx)("div",{class:"member-error",id:"error1-"+t})]}),(0,j.jsxs)("div",{className:"w-1/2 mr-2 text-[11px] md:text-[14px]",children:[(0,j.jsx)("input",{type:"text",id:"email-"+t,index:t,className:"w-full px-3 py-2 border border-gray-300 rounded fs-exclude",placeholder:"Enter Email",value:e.email,onChange:e=>{x((r=>{const s=r.slice();return s[t].email=e.target.value,((e,t)=>{let r=e.target.value,s=!0,a=document.getElementById("name-"+t),l=a.value.trim(),n=document.getElementById("error1-"+t),i=document.getElementById("error2-"+t);""===r.trim()?(e.target.title="Email is required",e.target.classList.add("ent-field-error"),i.style.display="block",i.innerHTML="Email is required",s=!1):/\S+@\S+\.\S+/.test(r)||(e.target.title="Invalid email format",e.target.classList.add("ent-field-error"),i.style.display="block",i.innerHTML="Invalid email format",s=!1),s&&(e.target.title="",e.target.classList.remove("ent-field-error"),n.style.display="none",n.innerHTML="",i.style.display="none",i.innerHTML=""),s&&""===l&&(a.title="Full Name is required",a.classList.add("ent-field-error"),n.style.display="block",n.innerHTML="Full Name is required")})(e,t),s}))},title:""}),(0,j.jsx)("div",{class:"member-error",id:"error2-"+t})]}),(0,j.jsx)("div",{className:"w-1/20 text-right pt-2",children:(0,j.jsx)("button",{onClick:()=>w(t),children:(0,j.jsx)("span",{children:(0,j.jsx)(c.Xm5,{className:"text-lg mr-2 text-red-500"})})})})]})))]}),(0,j.jsxs)("div",{className:"".concat(i&&!p?"justify-end":o?"justify-between":"justify-end"," flex border-b border-[#dddddd] mx-auto my-4 pb-4"),children:[p&&o&&(0,j.jsx)("input",{type:"button",value:"Add More",className:"border rounded font-bold bg-white py-2 px-4 cursor-pointer text-[12px] md:text-[14px]",onClick:()=>y()}),g&&(0,j.jsx)("input",{type:"button",value:"Remove Last",className:"border rounded font-bold bg-white float-right py-2 px-4 cursor-pointer text-[12px] md:text-[14px]",onClick:()=>(()=>{if(u.length>0){let e=u.length-1;w(e)}})()})]}),g&&(0,j.jsx)("div",{className:"text-right",children:(0,j.jsx)("input",{type:"button",value:"Add Member",className:"border rounded font-bold bg-blue-500 text-white py-2 px-4 cursor-pointer text-[12px] md:text-[14px]",onClick:()=>(async()=>{if(u.length+n.length>l)f().error("Members exceeded. Max. members should only "+l+" members.");else if(!await k()){var e="".concat("http://localhost:9002/api","/add-enterprise-member");u.length<=0?N():(document.querySelector(".loader-container").classList.add("active"),m.Z.post(e,{members:JSON.stringify(u)},{headers:{"content-type":"application/x-www-form-urlencoded"}}).then((function(e){let t=e.data;if(document.querySelector(".loader-container").classList.remove("active"),t.success)if(Array.isArray(t.data)&&t.data.length>0)for(var r=0;r<t.data.length;r++){let e=t.data[r].index,s=document.getElementById("email-"+e);s.title=t.data[r].error,s.classList.add("ent-field-error");let a=document.getElementById("error2-"+e);a.style.display="block",a.innerHTML=t.data[r].error}else f().success("Members Added"),N(),a(),v();else f().error("Adding Members Failed"),N(),a(),v()})).catch((function(e){N(),document.querySelector(".loader-container").classList.remove("active"),f().error("Something went wrong. Please try again in a bit!")})))}})()})})]})})})})},$=e=>{let{member_user_id:t,email:r,handleReloadMembers:a}=e;const[l,n]=(0,s.useState)(!1),i=()=>{n(!1)};return(0,j.jsxs)(j.Fragment,{children:[(0,j.jsx)("button",{className:"hover:bg-sky-500/100 hover:text-white text-gray-900 group min-w-full items-center px-4 py-2 text-left",onClick:()=>{n(!0)},children:"Delete"}),(0,j.jsx)(x.u,{appear:!0,show:l,as:s.Fragment,children:(0,j.jsxs)(p.V,{as:"div",className:"relative z-10",onClose:i,children:[(0,j.jsx)(x.u.Child,{as:s.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:(0,j.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-25"})}),(0,j.jsx)("div",{className:"fixed inset-0 overflow-y-auto",children:(0,j.jsx)("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:(0,j.jsx)(x.u.Child,{as:s.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:(0,j.jsxs)(p.V.Panel,{className:"w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all",children:[(0,j.jsx)(p.V.Title,{as:"h3",className:"text-red-800 w-full md:w-2/3 mr-2 md:mr-5 font-bold",children:"Delete Member"}),(0,j.jsx)("div",{className:"mt-2",children:(0,j.jsxs)("p",{className:"text-sm text-gray-500 break-words border-t border-b border-[#dddddd] py-2",children:[(0,j.jsx)(d.CSE,{className:"inline text-sm mr-1 text-red-800"}),"Are you sure you want to delete ",(0,j.jsx)("strong",{children:r})," account?"]})}),(0,j.jsxs)("div",{className:"mt-4 text-right",children:[(0,j.jsx)("button",{type:"button",className:"inline-flex justify-center rounded-md border border-transparent bg-red-500 px-4 py-2 mr-2 text-sm font-medium text-white hover:bg-[#d30101]",onClick:e=>{document.querySelector(".loader-container").classList.add("active"),i(),m.Z.post("".concat("http://localhost:9002/api","/delete-enterprise-member"),{member_user_id:t,member_email:r},{headers:{"content-type":"application/x-www-form-urlencoded"}}).then((function(e){let t=e.data;if(t.success)return document.querySelector(".loader-container").classList.remove("active"),f().success("Delete success."),void a();document.querySelector(".loader-container").classList.remove("active"),t.data&&f().error(t.data.msg)}))},children:"Delete Member"}),(0,j.jsx)("button",{type:"button",className:"inline-flex justify-center rounded-md border border-transparent bg-gray-100 px-4 py-2 text-sm font-medium text-black hover:bg-gray-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-gray-500 focus-visible:ring-offset-2",onClick:i,children:"Close"})]})]})})})})]})})]})},Q=e=>{let{member_user_id:t,email:r}=e;const[a,l]=(0,s.useState)(!1),n=()=>{l(!1)};return(0,j.jsxs)(j.Fragment,{children:[(0,j.jsx)("button",{className:"hover:bg-sky-500/100 hover:text-white text-gray-900 group min-w-full items-center px-4 py-2 text-left",onClick:()=>{l(!0)},children:"Resend Password"}),(0,j.jsx)(x.u,{appear:!0,show:a,as:s.Fragment,children:(0,j.jsxs)(p.V,{as:"div",className:"relative z-10",onClose:n,children:[(0,j.jsx)(x.u.Child,{as:s.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:(0,j.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-25"})}),(0,j.jsx)("div",{className:"fixed inset-0 overflow-y-auto",children:(0,j.jsx)("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:(0,j.jsx)(x.u.Child,{as:s.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:(0,j.jsxs)(p.V.Panel,{className:"w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all",children:[(0,j.jsx)(p.V.Title,{as:"h3",className:"text-lg font-medium leading-6 text-gray-900",children:"Resend Password"}),(0,j.jsx)("div",{className:"mt-2",children:(0,j.jsxs)("p",{className:"text-sm text-gray-500 break-words",children:["Do you want to proceed with sending the new password to ",(0,j.jsx)("strong",{children:r}),"?"]})}),(0,j.jsxs)("div",{className:"mt-4 text-right",children:[(0,j.jsx)("button",{type:"button",className:"inline-flex justify-center rounded-md border border-transparent bg-red-500 px-4 py-2 mr-2 text-sm font-medium text-white hover:bg-[#d30101]",onClick:e=>{document.querySelector(".loader-container").classList.add("active"),n(),m.Z.post("".concat("http://localhost:9002/api","/resend-pass-enterprise-member"),{member_user_id:t,member_email:r},{headers:{"content-type":"application/x-www-form-urlencoded"}}).then((function(e){let t=e.data;if(t.success)return f().success("New Password sent."),void document.querySelector(".loader-container").classList.remove("active");document.querySelector(".loader-container").classList.remove("active"),t.data&&f().error(t.data.msg)}))},children:"Resend Password"}),(0,j.jsx)("button",{type:"button",className:"inline-flex justify-center rounded-md border border-transparent bg-gray-100 px-4 py-2 text-sm font-medium text-black hover:bg-gray-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-gray-500 focus-visible:ring-offset-2",onClick:n,children:"Close"})]})]})})})})]})})]})},X=function(){const[e,t]=(0,s.useState)([]),[r,a]=(0,s.useState)("subscription"),[d,c]=(0,s.useState)(!1),[u,x]=(0,s.useState)(!1),[p,h]=(0,s.useState)([]),[y,N]=(0,s.useState)(!1),[k,C]=(0,s.useState)(!1),[M,L]=(0,s.useState)(!1),[E,F]=(0,s.useState)(""),[A,I]=(0,s.useState)(""),[B,V]=(0,s.useState)(""),[z,Y]=(0,s.useState)(""),[Z,$]=(0,s.useState)(""),[Q,X]=(0,s.useState)(""),[ee,te]=(0,s.useState)(!1),[re,se]=(0,s.useState)(!1),[ae,le]=(0,s.useState)(!1),[ne,ie]=(0,s.useState)(!1),[oe,de]=(0,s.useState)(!1),[ce,me]=(0,s.useState)(!0),[ue,xe]=(0,s.useState)(null),[pe,he]=(0,s.useState)(""),[ge,be]=(0,s.useState)(null),[ye,fe]=(0,s.useState)({}),[ve,je]=(0,s.useState)(!1),[we,Ne]=(0,s.useState)(null),[ke,_e]=(0,s.useState)(0),[Se,Ce]=(0,s.useState)(0),[Me,Le]=(0,s.useState)(),[Te,Ee]=(0,s.useState)({perModel:{},perApp:[]}),Fe=(0,i.gx)();if((0,s.useEffect)((()=>{f().options={positionClass:"toast-top-center"},async function(){const e=await(0,i.Qk)();fe(e)}()}),[]),(0,s.useEffect)((()=>{let e=(0,o.bG)("threed_error");void 0!==e&&""!==e&&setTimeout((function(){(0,o.I1)("threed_error",""),f().error(e)}),3e3)}),[]),(0,s.useEffect)((()=>{if(void 0!==Fe){let e=null===Fe.ent_parent_user_id?"":Fe.ent_parent_user_id;V(e),Y(Fe.plan),$(window.view_data.plan_name),X(Fe.user_ppg),"mem"!==window.view_data.active_tab&&a("account")}else"mem"===window.view_data.active_tab&&a("members")}),[Fe]),(0,s.useEffect)((()=>{void 0!==ye&&(Ce(ye.max_tokens),Le(ye.max_end))}),[ye]),(0,s.useEffect)((()=>{void 0!==Fe&&"enterprise"===Fe.plan&&void 0!==ye&&(S=ye.max_members,c(!0))}),[Fe,ye]),(0,s.useEffect)((()=>{d&&(async()=>{let e=await O();t(e)})()}),[d]),(0,s.useEffect)((()=>{const e=async()=>{let e=await async function(){const e=await m.Z.post("".concat("http://localhost:9002/api","/get-token-usage"),{tk:(0,o.bG)("access")},{headers:{"content-type":"application/x-www-form-urlencoded"}}),t=e.data?e.data:[];return t.success?t:[]}();void 0!==e.data&&(_e(e.total),Ee({perModel:{"GPT-4o":{usage:e.gpt_4o_total_token_usage,max_usage:e.gpt_4o_max_tokens},"DALL·E Image Generated":{usage:e.dalle_total_image_generated,max_usage:e.dalle_max_images},"GPT Image 1 Image Generated":{usage:e.gpt_image_1_total_image_generated,max_usage:e.gpt_image_1_max_images},"Claude 3.5":{usage:e.claude_35_total_token_usage,max_usage:e.claude_35_max_tokens},"Claude 3.7":{usage:e.claude_37_total_token_usage,max_usage:e.claude_37_max_tokens},"Deepseek R1":{usage:e.deepseek_r1_total_token_usage,max_usage:e.deepseek_r1_max_tokens},"Grok 3":{usage:e.grok_v3_total_token_usage,max_usage:e.grok_v3_max_tokens},"Grok 4":{usage:e.grok_v4_total_token_usage,max_usage:e.grok_v4_max_tokens},"Flux Prompts":{usage:e.flux_total_prompts,max_usage:e.flux_max_prompts},"o1 Prompts":{usage:e.o1_total_prompts,max_usage:e.o1_max_prompts},"Kling 1.6":{usage:e.videogen_credit,max_usage:e.videogen_total_credit}},perApp:e.data}))};z&&e();"token-usage"===r?e():"order"===r&&(async()=>{const e=await async function(){const e=await m.Z.post("".concat("http://localhost:9002/api","/get-payment"),{tk:(0,o.bG)("access")},{headers:{"content-type":"application/x-www-form-urlencoded"}}),t=e.data;return t.success?t.data:[]}();be(e)})()}),[r,z]),(0,s.useEffect)((()=>{if(null!==ge){const e=["enterprisemax","officemax","teammax"];(()=>{const t=ge.find((t=>e.includes(t.label.toLowerCase().replace(/\s+/g,""))));de(Boolean(t))})()}}),[ge]),void 0===Fe)return;if(!1===Fe)return void(window.location.href="/login");const Ae=e=>{a(e)},Ie=async()=>{let e=await O();t(e)};return(0,b.N9)(),(0,j.jsxs)(j.Fragment,{children:[(0,j.jsxs)(g.q,{children:[(0,j.jsx)("title",{children:"AI Pro | Manage Account"}),(0,j.jsx)("meta",{name:"description",content:"Take control of your account with ease through our My Account page. Review billing details, update contact information, and tailor your preferences."})]}),(0,j.jsx)(l.default,{auth:Fe,setSubscription:xe,setGetDateNow:he,setshowAddMoreMember:C}),(0,j.jsx)("div",{className:"Manage bg-gray-100 min-h-[500px] flex items-center",children:(0,j.jsx)("div",{className:"container mx-auto py-10 px-4 sm:px-0",children:(0,j.jsxs)("div",{className:"max-w-6xl mx-auto pt-8 pb-8 sm:p-8",children:[(0,j.jsx)("h1",{className:"text-xl font-bold text-blue-600 my-6 lg:my-8",children:"Manage Your Account"}),(0,j.jsx)("div",{className:"",children:(0,j.jsxs)("ul",{className:"flex flex-wrap text-xs md:text-sm",children:[""===B?(0,j.jsx)(D,{tabName:"subscription",activeTab:r,onClick:Ae,children:"Subscription"}):"",d&&""===B?(0,j.jsx)(D,{tabName:"members",activeTab:r,onClick:Ae,children:"Members"}):"",""===B?(0,j.jsx)(D,{tabName:"order",activeTab:r,onClick:Ae,children:"Invoice"}):"",(0,j.jsx)(D,{tabName:"token-usage",activeTab:r,onClick:Ae,children:"Token Usage"}),(0,j.jsx)(D,{tabName:"account",activeTab:r,onClick:Ae,children:"Account"}),(0,j.jsx)(D,{tabName:"help",activeTab:r,onClick:Ae,children:"Help"}),(0,j.jsx)(D,{tabName:"logout",activeTab:r,onClick:Ae,children:"Logout"})]})}),(0,j.jsxs)("div",{className:"bg-white drop-shadow-sm p-6 rounded-tr-md rounded-br-md rounded-bl-md min-h-[400px] flex",children:["subscription"===r&&(0,j.jsx)(q,{setshowAddMoreMember:C,setShowPausedAccountModal:te,setUserSubscriptionID:se,setUserMerchant:le,setUserAccountID:ie,date_now:pe,user_subscription:ue,userPpg:Q,showOffer:e=>{Ne(e),je(!0)}}),"members"===r&&(0,j.jsx)(K,{handleShowAddMember:()=>{const t=S-e.length;e.length>=S?e.length===Number(S)&&oe?f().error("Member limit reached."):(me(!1),C(!0)):e.length>0&&oe?(N(!0),e.length>S?me(!1):me(1!==t)):N(!0)},enterpriseMembers:e,handleReloadMembers:Ie,handleShowEditMemberModal:(e,t)=>{x(!0),h(t)},currentPlan:z,currentPlanName:Z}),"order"===r&&(0,j.jsx)(G,{user_order:ge}),"token-usage"===r&&(0,j.jsx)(_,{currentPlan:z,currentPlanName:Z,totalTokenUsage:ke,maxTokens:Se,resetDate:Me,tokenUsages:Te,entParentUserID:B}),"account"===r&&(0,j.jsx)(U,{auth:Fe}),"help"===r&&(0,j.jsx)(H,{}),"logout"===r&&(0,j.jsx)(R,{})]})]})})}),(0,j.jsx)(J,{showEditMember:u,setShowEditEnterprise:x,editMemberDetails:p,handleReloadMembers:Ie}),(0,j.jsx)(W,{showAddMember:y,setshowAddMember:N,handleReloadMembers:Ie,entMaxMembers:S,enterpriseMembers:e,isMax:oe,isVisbleBtn:ce,setIsVisibleBtn:me}),(0,j.jsx)(v.W,{showAddMoreMember:k,setshowAddMoreMember:C,setMoreToAddMember:F,setMoreToAddMemberTotalAmount:I,setShowCompletePurchase:L}),(0,j.jsx)(v.U,{moreToAddMember:E,moreToAddMemberTotalAmount:A,setShowCompletePurchase:L,showCompletePurchase:M}),(0,j.jsx)(w,{showPausedAccountModal:ee,setShowPausedAccountModal:te,userSubscriptionID:re,userMerchant:ae,userAccountID:ne}),(0,j.jsx)(P,{show:ve,onClose:()=>{je(!1)},onAccept:()=>{window.location.href="/downgrade/"+T},onDecline:()=>{je(!1),we&&we.onDecline&&we.onDecline()}}),(0,j.jsx)(n.default,{auth:Fe})]})}},29540:(e,t,r)=>{r.d(t,{U:()=>h,W:()=>p});var s=r(72791),a=r(19886),l=r(31243),n=r(72608),i=r(56355),o=r(28891),d=r(95828),c=r.n(d),m=(r(92831),r(80184)),u="0",x="";function p(e){let{showAddMoreMember:t,setshowAddMoreMember:r,setMoreToAddMember:l,setMoreToAddMemberTotalAmount:n,setShowCompletePurchase:i}=e;const[d,x]=(0,s.useState)(1),[p,h]=(0,s.useState)(0),[g,b]=(0,s.useState)(""),y=(0,o.gx)();(0,s.useEffect)((()=>{void 0!==y&&"enterprise"===y.plan&&(u=y.price_per_member,"monthly"===y.interval.toLowerCase()?b("MONTH"):b("YEAR"))}),[y]);const f=()=>{let e=document.getElementById("modalAddMoreMembers");null!==e&&(e.style.display="none",r(!1))};(0,s.useEffect)((()=>{c().options={positionClass:"toast-top-center"}}),[]),(0,s.useEffect)((()=>{h(d*u)}),[d]),(0,s.useEffect)((()=>{x(1),h(d*u)}),[t]);return void 0!==t&&!0===t&&(()=>{let e=document.getElementById("modalAddMoreMembers");null!==e&&(e.style.display="block")})(),void 0!==t&&!1===t&&f(),(0,m.jsx)(m.Fragment,{children:(0,m.jsx)("div",{id:"modalAddMoreMembers",className:"modal z-[9999]",children:(0,m.jsx)("div",{class:"modal-content w-full md:w-[60%] max-w-[90%] p-3 md:p-4",children:(0,m.jsxs)("div",{children:[(0,m.jsxs)("div",{className:"mb-4 flex border-b pb-2",children:[(0,m.jsx)("div",{className:"text-blue-500 w-full md:w-2/3 mr-2 md:mr-5 font-bold",children:"Add More Members"}),(0,m.jsx)("div",{className:"float-right mt-[-5px] w-full md:w-1/3",children:(0,m.jsx)("span",{class:"close",onClick:()=>f(),children:"×"})})]}),(0,m.jsxs)("div",{className:"p-2 md:p-4 border rounded text-sm text-center",children:[(0,m.jsx)("div",{children:"Your enterprise account has hit its maximum user capacity."}),(0,m.jsx)("div",{children:"Add more members to your Enterprise Account."}),(0,m.jsxs)("div",{className:"py-4 text-center",children:[(0,m.jsx)("span",{className:"font-bold text-[12px] sm:text-sm inline px-1 sm:px-2",children:"Add"}),(0,m.jsxs)("div",{className:"border rounded px-2 py-4 inline",children:[(0,m.jsx)("input",{className:"bg-blue-500 rounded px-3 py-2 m-2 text-white cursor-pointer",type:"button",value:"-",onClick:()=>{d>1&&x(d-1)}}),(0,m.jsx)("span",{className:"text-blue-500 p-2 mx-auto font-bold",children:d}),(0,m.jsx)("input",{className:"bg-blue-500 rounded px-3 py-2 m-2 text-white cursor-pointer",type:"button",value:"+",onClick:()=>{x(d+1)}})]}),(0,m.jsx)("span",{className:"font-bold text-[12px] sm:text-sm inline px-1 sm:px-2",children:"Member/s"})]}),(0,m.jsxs)("div",{className:"font-bold text-sm",children:["Total Amount: $",p]}),(0,m.jsxs)("div",{className:"font-bold text-sm",children:["PER ",g]}),(0,m.jsx)("div",{children:(0,m.jsx)(a.E.button,{className:"bg-sky-600 w-full md:w-70 text-white font-bold my-4 py-2 px-6 rounded proceed-pmt",whileHover:{backgroundColor:"#49b1df"},whileTap:{scale:.9},onClick:()=>{window.location.href="/upgrade-ent/"+d},children:"UPGRADE NOW"})})]})]})})})})}function h(e){let{moreToAddMember:t,moreToAddMemberTotalAmount:r,setShowCompletePurchase:d,showCompletePurchase:u}=e;const p=(0,o.gx)();(0,s.useEffect)((()=>{void 0!==p&&"enterprise"===p.plan&&(x=p.user_id)}),[p]);const h=()=>{let e=document.getElementById("modalComplete");null!==e&&(e.style.display="none",d(!1))};return void 0!==u&&!0===u&&(()=>{let e=document.getElementById("modalComplete");null!==e&&(e.style.display="block")})(),void 0!==u&&!1===u&&h(),(0,m.jsx)(m.Fragment,{children:(0,m.jsx)("div",{id:"modalComplete",className:"modal z-[9999]",children:(0,m.jsxs)("div",{class:"w-full md:w-[600px] border-[#888] md:mt-[15px] mx-[auto] bg-[#fefefe] p-6",children:[(0,m.jsx)("span",{class:"close",onClick:()=>h(),children:"×"}),(0,m.jsx)("div",{className:"border-b pb-[10px] border-[#d5d5d5]",children:(0,m.jsx)("img",{src:n.Z,alt:"AI-Pro Logo",className:"aiprologo mx-auto"})}),(0,m.jsxs)("h1",{className:"font-bold text-center p-2 text-gray-700 text-[20px] md:text-[24px]",children:["Payment Details",(0,m.jsx)("br",{}),"for Enterprise Order"]}),(0,m.jsx)("div",{className:"text-center text-[12px] md:text-[14px]",children:"Adding more than 10 Enterprise users requires prior payment."}),(0,m.jsx)("div",{className:"text-center text-[12px] md:text-[14px]",children:"Please use the provided payment details below to settle your Enterprise account."}),(0,m.jsxs)("div",{className:"py-2",children:[(0,m.jsxs)("div",{className:"font-bold text-[11px]",children:["No. of Members: ",t]}),(0,m.jsxs)("div",{className:"font-bold text-[11px]",children:["Enterprise - Total: $",r]})]}),(0,m.jsxs)("div",{className:"border rounded p-2 text-[12px] md:text-[14px] leading-7 my-2",children:[(0,m.jsx)("div",{className:"font-bold",children:"Bank Information"}),(0,m.jsx)("div",{className:"float-right text-blue-400 font-bold cursor-pointer mt-[-28px] text-[12px]",onClick:()=>{return e=t,s=r,a="".concat("http://localhost:9002/api","/t/send-enterprise-payment-info"),h(),document.querySelector(".loader-container").classList.add("active"),void l.Z.post(a,{members:e,total_amount:s},{headers:{"content-type":"application/x-www-form-urlencoded"}}).then((function(e){let t=e.data;document.querySelector(".loader-container").classList.remove("active"),t.success?c().success("Email sent to "+t.data):c().error("Email Failed.")})).catch((function(e){document.querySelector(".loader-container").classList.remove("active"),e.response&&429===e.response.status&&(document.querySelector(".loader-container").classList.remove("active"),c().error("Sorry, too many requests. Please try again in a bit!"))}));var e,s,a},children:"Send via Email"}),(0,m.jsxs)("div",{children:[(0,m.jsx)("span",{className:"font-bold",children:"Beneficiary:"})," TELECOM BUSINESS SOLUTIONS INC."]}),(0,m.jsxs)("div",{children:[(0,m.jsx)("span",{className:"font-bold",children:"SWIFT:"})," BOFAUS3N"]}),(0,m.jsxs)("div",{children:[(0,m.jsx)("span",{className:"font-bold",children:"Bank Name:"})," Bank of America"]}),(0,m.jsxs)("div",{children:[(0,m.jsx)("span",{className:"font-bold",children:"Routing (Wire):"})," *********"]}),(0,m.jsxs)("div",{children:[(0,m.jsx)("span",{className:"font-bold",children:"Routing Number (Paper & Electronic):"})," *********"]}),(0,m.jsxs)("div",{children:[(0,m.jsx)("span",{className:"font-bold",children:"Account Number:"})," 3810-6766-2647"]}),(0,m.jsxs)("div",{children:[(0,m.jsx)("span",{className:"font-bold",children:"Customer Number:"})," ",x]}),(0,m.jsxs)("div",{className:"bg-[#dddddd] px-4 py-2 rounded text-center mt-4",children:[(0,m.jsx)(i.DAO,{className:"inline text-lg mr-2"}),"Customer Number must be included in the bank transfer description field for your funds to transfer successfully."]})]}),(0,m.jsx)("div",{className:"text-center text-[12px] md:text-[14px] mt-4",children:"Once the payment is received, our dedicated account manager will contact you to assist in the seamless setup of your Enterprise account."}),(0,m.jsxs)("div",{className:"text-center text-[12px] md:text-[14px]",children:["Please allow ",(0,m.jsx)("b",{children:"2-3 banking days"})," for the payment to reflect in the account."]}),(0,m.jsx)("div",{className:"text-center",children:(0,m.jsx)(a.E.button,{className:"bg-blue-500 text-white font-bold py-3 px-4 rounded my-4 proceed-pmt",whileHover:{backgroundColor:"#5997fd"},whileTap:{scale:.9},onClick:()=>{window.location.href="/payment-reference"},children:"Send Payment Confirmation"})})]})})})}}}]);
//# sourceMappingURL=3513.904c9560.chunk.js.map