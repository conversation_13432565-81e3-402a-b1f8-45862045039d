{"version": 3, "file": "static/css/1200.0109c422.chunk.css", "mappings": "AAAA,iBACE,qBAAsB,CAEtB,YACF,CACA,4BAGI,iBAAkB,CADlB,iBAEJ,CACA,iCACI,cACJ,CACA,4BAKE,aAAc,CAFd,eAAgB,CAFhB,gCAA0D,CAA1D,wDAA0D,CAC1D,eAAiC,CAAjC,+BAAiC,CAIjC,SAAU,CAFV,gBAGF,CACA,sCACE,kBACF,CAEA,gCACE,WAAY,CACZ,iBACF,CAEA,+CACE,kBACF,CAEA,iCACE,qBAAuB,CAGvB,YAAa,CAFb,2BAA6B,CAC7B,uBAEF,CAEA,mCAEE,eAAgB,CADhB,eAEF,CAEA,QACE,mBACF,CAEA,0BACE,mBACE,WACF,CACF,CAEA,0BACE,QACE,cAAe,CACf,eAAgB,CAChB,gBACF,CACA,mBACE,WACF,CACF,CAEA,yBACE,4BACI,UACJ,CACF,CAEA,qBAIE,qBAAsB,CAHtB,wBAAyB,CAMzB,iBAAkB,CAJlB,WAAY,CASZ,gCAAiC,CAPjC,UAAW,CAKX,cAAe,CACf,gBAAiB,CAFjB,eAAgB,CAHhB,WAAY,CAJZ,iBAAkB,CAMlB,UAKF,CAEA,uDAEE,4BAAmC,CACnC,6BAAoC,CAGpC,YAAa,CACb,UAAW,CACX,aAAc,CACd,QAAS,CAJT,qBAAsB,CADtB,iBAAkB,CAMlB,yBACF,CAEA,4BACE,gCAAiC,CAEjC,YAAa,CADb,SAEF,CAEA,2BACE,6BAA8B,CAE9B,WAAY,CADZ,SAEF,CAEA,qBACE,YACF,CAEA,cAEE,cAAe,CADf,iBAEF,CAEA,yCACE,aACF,CC5HA,WACE,gBAAiB,CAEjB,KAAM,CADN,UAEF,CACA,WAAY,WAAa,CACzB,OAGE,QAAS,CAFT,cAAe,CACf,QAAS,CAGT,8BAAgC,CADhC,UAEF,CACA,UACE,SACF,CACA,WAEE,eAAmB,CADpB,gCAED,CACA,cAEC,4BAA8B,CAC9B,eAAiB,CAFjB,0BAGD,CAEA,yBACE,uBACE,cACF,CACF,CACA,yBACE,wBACE,kBACF,CACF,CAEA,yBACE,WACE,mBACF,CACF,CAEA,yBACE,WACE,mBACF,CACF,CAEA,qDAEE,WAAY,CACZ,oBAAqB,CAErB,gBAAiB,CACjB,SAAU,CACV,kCAAoC,CAHpC,SAIF,CAEA,iEAEE,SACF,CAEA,wBACE,GAEE,SAAU,CADV,2BAEF,CACA,GAEE,SAAU,CADV,uBAEF,CACF,CAEA,kBACE,iBACF,CAEA,UASE,qBAAuB,CACvB,iBAAkB,CAElB,gCAAkC,CAPlC,YAAa,CAGb,MAAO,CAJP,eAAgB,CADhB,iBAAkB,CAFlB,2BAA4B,CAU5B,cAAe,CALf,iBAAkB,CAJlB,oBAAqB,CAKrB,QAMF,CAEA,aACE,QACF,CAEA,YAEE,aAAe,CACf,WACF,CAEA,8CALE,aAOF,CAEA,gBACE,8BAA+B,CAC/B,eACF,CAQA,yBACE,WACE,YACF,CACF,CAEA,yBACE,aACE,yBACF,CACF,CAEA,oBAEI,eAAgB,CAKhB,kBAAmB,CACnB,+BAAiC,CAHjC,cAAe,CAIf,aAAc,CACd,eAAgB,CAChB,iBAAkB,CAPlB,YAAa,CAHb,OAAQ,CAER,eAAgB,CAGhB,QAMJ,CAEA,6CACE,SACE,YACF,CACA,cAAe,kBAAqB,CACpC,gBACE,iBAAkB,CAClB,UACF,CACA,mBAIE,qBAAsB,CACtB,YACF,CACA,mCAJE,kBAAmB,CAFnB,YAAa,CACb,qBAgBF,CAXA,gBAQE,eAAgB,CAChB,WAAY,CAFZ,cAAe,CADf,WAAY,CAHZ,sBAAuB,CAOvB,SAAU,CALV,UAMF,CACA,gBAIE,+BAAiC,CAFjC,WAAY,CACZ,oBAAsB,CAEtB,iBAAkB,CAJlB,UAKF,CACF,CACA,yBACE,WAAY,WAAa,CACzB,cAAe,eAAkB,CACjC,yFACI,QACJ,CACA,gBACE,iBAAkB,CAClB,UACF,CACF", "sources": ["upgrade/style.css", "header/style.css"], "sourcesContent": [".pricing-upgrade {\r\n  background-color: #fff;\r\n  /* border: 1px solid #ccc; */\r\n  padding: 20px;\r\n}\r\n.pricing-upgrade .price_col {\r\n/*    width: 400px;*/\r\n    text-align: center;\r\n    position: relative;\r\n}\r\n.price_col i.fas, .price_col i.fa {\r\n    font-size: 1rem;\r\n}\r\n.price_col ul li > div::before {\r\n  font-family: var(--fa-style-family, \"Font Awesome 6 Free\");\r\n  font-weight: var(--fa-style, 900);\r\n  content: \"\\f00c\";\r\n  margin-right: 5px;\r\n  color: #3b82f6;  \r\n  left: 30px;\r\n}\r\n.pricing-upgrade .price_col ul li > div {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.pricing-upgrade .price_col > div {\r\n  height: 100%;\r\n  position: relative;\r\n}\r\n\r\n.pricing-upgrade .price_col > div .price-content {\r\n  margin-bottom: 30px;\r\n}\r\n\r\n.pricing-upgrade .span-highlight {\r\n  height: auto !important;\r\n  position: absolute !important;\r\n  width: calc(100% - 2rem);\r\n  padding: 10px;\r\n}\r\n\r\n.pricing-upgrade .plan-description {\r\n  text-align: left;\r\n  margin-left: 5px;\r\n}\r\n\r\n.indent {\r\n  margin-left: 1.25rem;\r\n}\r\n\r\n@media (max-width: 1500px) {\r\n  .plan-4 .price_col {\r\n    width: 350px;\r\n  }\r\n}\r\n\r\n@media (max-width: 1380px) {\r\n  .plan-4 {\r\n    flex-wrap: wrap;\r\n    padding-left: 6%;\r\n    padding-right: 6%;\r\n  }\r\n  .plan-4 .price_col {\r\n    width: 380px;\r\n  }\r\n}\r\n\r\n@media (max-width: 500px) {\r\n  .pricing-upgrade .price_col {\r\n      width: 100%;\r\n  }\r\n}\r\n\r\n.description-tooltip {\r\n  border: 3px solid #2872fa;\r\n  position: absolute;\r\n  bottom: 30px;\r\n  background-color: #fff;\r\n  color: #333;\r\n  padding: 5px;\r\n  border-radius: 5px;\r\n  width: auto;\r\n  max-width: 320px;\r\n  font-size: 11px;\r\n  margin-top: -35px;\r\n  box-shadow: 0 3px 15px -13px #000;\r\n}\r\n\r\n.description-tooltip:after,\r\n.description-tooltip:before {\r\n  border-left: 10px solid transparent;\r\n  border-right: 10px solid transparent;\r\n  position: absolute;\r\n  left: calc(50% - 10px);\r\n  border-top: 0;\r\n  content: \"\";\r\n  display: block;\r\n  height: 0;\r\n  transform: rotate(-180deg);\r\n}\r\n\r\n.description-tooltip:before {\r\n  border-bottom: 10px solid #2872fa;\r\n  z-index: 1;\r\n  bottom: -10px;\r\n}\r\n\r\n.description-tooltip:after {\r\n  border-bottom: 10px solid #fff;\r\n  z-index: 2;\r\n  bottom: -6px;\r\n}\r\n\r\n.description-tooltip {\r\n  display: none;\r\n}\r\n\r\n.hover-target {\r\n  position: relative;\r\n  cursor: pointer;\r\n}\r\n\r\n.hover-target:hover .description-tooltip {\r\n  display: block;\r\n}\r\n", ".headerRef {\r\n  display: absolute;\r\n  width: 100%;\r\n  top: 0;\r\n}\r\n.aiprologo {width: 150px;}\r\nheader {\r\n  position: fixed;\r\n  top: 25px;\r\n  left: 50%;\r\n  width: 100%;\r\n  transform: translate(-50%, -50%);\r\n}\r\n.svg-icon {\r\n  fill: white;\r\n}\r\n.headernav {\r\n\tbox-shadow: 0 3px 19px -14px black;\r\n  background: #ffffff;\r\n}\r\n.headerctabtn {\r\n\tpadding: 5px 10px !important;\r\n\tborder-radius: 15px !important;\r\n\tfont-size: 0.9rem;\r\n}\r\n\r\n@media (max-width: 680px) {\r\n  #maintenance-container {\r\n    font-size: 14px;\r\n  }\r\n}\r\n@media (max-width: 590px) {\r\n  .headernav.top-\\[60px\\] {\r\n    top: 70px !important;\r\n  }\r\n}\r\n\r\n@media (max-width: 991px) {\r\n  .headernav {\r\n    width: 98% !important;\r\n  }\r\n}\r\n\r\n@media (max-width: 500px) {\r\n  .headernav {\r\n    width: 90% !important;\r\n  }\r\n}\r\n\r\n.headernav nav#menu ul li a:before,\r\n.menuArrow:before {\r\n  content: \">\";\r\n  display: inline-block;\r\n  width: 7px;\r\n  margin-right: 2px;\r\n  opacity: 0;\r\n  transition: opacity 0.2s ease-in-out;\r\n}\r\n\r\n.headernav nav#menu ul li a:hover:before,\r\n.menuArrow:hover:before {\r\n  opacity: 1;\r\n}\r\n\r\n@keyframes slideInRight {\r\n  from {\r\n    transform: translateX(-100%);\r\n    opacity: 0;\r\n  }\r\n  to {\r\n    transform: translateX(0);\r\n    opacity: 1;\r\n  }\r\n}\r\n\r\n.dropdown-wrapper {\r\n  position: relative;\r\n}\r\n\r\n.dropdown {\r\n  overscroll-behavior: contain;\r\n  scrollbar-width: none;\r\n  overflow-y: scroll;\r\n  max-height: 75vh;\r\n  display: none;\r\n  position: absolute;\r\n  top: 100%;\r\n  left: 0;\r\n  background-color: white;\r\n  border-radius: 5px;\r\n  padding: 1rem 0;\r\n  box-shadow: 0 7px 17px -10px black;\r\n}\r\n\r\n.dropdown li {\r\n  margin: 0;\r\n}\r\n\r\n.dropdown a {\r\n  display: block;\r\n  padding: 0.5rem;\r\n  width: 250px;\r\n}\r\n\r\n.dropdown-wrapper:hover .dropdown {\r\n  display: block;\r\n}\r\n\r\n.menu-container {\r\n  max-height: calc(90vh - 3.5rem);\r\n  overflow-y: auto;\r\n}\r\n\r\n\r\n/* .dropdown-wrapper:focus-within .dropdown {\r\n  display: block;\r\n} */\r\n/*WILL FIX SHIFT+TAB ACCESSIBILITY IN ANOTHER TICKET*/\r\n\r\n@media (min-width: 768px) {\r\n  .mobilenav {\r\n    display: none;\r\n  }\r\n}\r\n\r\n@media (max-width: 991px){\r\n  .headerStyle {\r\n    max-width: 980px !important;\r\n  }\r\n}\r\n\r\n.mobilenav .headnav{\r\n    right: 0;\r\n    background: #fff;\r\n    text-align: left;\r\n    padding: 15px;\r\n    font-size: 14px;\r\n    top: 72px;\r\n    border-radius: 15px;\r\n    box-shadow: 0 2px 13px -9px black;\r\n    line-height: 2;\r\n    max-height: 83vh;\r\n    overflow-y: scroll;\r\n}\r\n\r\n@media (min-width: 0px) and (max-width: 991px) {\r\n  .headnav {\r\n    display: none;\r\n  }\r\n  .ctaStartHere {margin: 0 !important;}\r\n  .hamburger-menu {\r\n    position: absolute;\r\n    right: 25px;\r\n  }\r\n  .headnav.show-menu {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    background-color: #fff;\r\n    padding: 10px;\r\n  }\r\n  .hamburger-menu {\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: center;\r\n    align-items: center;\r\n    width: 30px;\r\n    height: 30px;\r\n    cursor: pointer;\r\n    background: none;\r\n    border: none;\r\n    padding: 0;\r\n  }\r\n  .hamburger-line {\r\n    width: 30px;\r\n    height: 100%;\r\n    height: 3px !important;\r\n    background-color: #000 !important;\r\n    margin-bottom: 4px;\r\n  }\r\n}\r\n@media (max-width: 639px) {\r\n  .aiprologo {width: 110px;}\r\n  .headerctabtn {font-size: 0.8rem;}\r\n  .headerctabtn.gradient-hover-effect.text-white.rounded-3xl.block.sm\\:hidden.ml-auto.mr-3 {\r\n      margin: 0;\r\n  }\r\n  .hamburger-menu {\r\n    position: absolute;\r\n    right: 25px;\r\n  }\r\n}"], "names": [], "sourceRoot": ""}