"use strict";(self.webpackChunkv1=self.webpackChunkv1||[]).push([[1875],{71875:(e,t,s)=>{s.r(t),s.d(t,{default:()=>k});var a=s(72791),i=s(31243),o=s(95828),n=s.n(o),r=s(74335),l=s(70572),c=s(39230),d=s(40642),p=s(3633),x=s(80184);function m(e){let{handleRedirect:t}=e;const{t:s}=(0,c.$G)(),o=(0,a.useRef)(null),[m,h]=(0,a.useState)(""),[u,w]=(0,a.useState)(""),[g]=(0,a.useState)(!1),[f,b]=(0,a.useState)(""),[v,j]=(0,a.useState)(""),[y,N]=(0,a.useState)(!1),k=(0,r.bG)("lp"),A=(0,r.bG)("members")||"",[D,E]=(0,a.useState)(""),[B,I]=(0,a.useState)(""),F=window.screen.width,S=`width=600,height=700,top=${(window.screen.height-700)/2},left=${(F-600)/2}`,C=()=>{const e=(0,r.bG)("flow"),s=(0,r.bG)("chatpdf"),a=(0,r.bG)("reg_pop");if("chatpdf"===e)"01"===s?"aihub"===k?window.top.location.href=`https://${window.location.hostname}/chat.ai-pro.org`:"1"===a?window.self.location.href="/pay":window.location.href="/pay":"aihub"===k?window.top.location.href=`https://${window.location.hostname}/chat.ai-pro.org`:"1"===a?window.self.location.href=`https://${window.location.hostname}/chatpdf.ai-pro.org`:window.location.href=`https://${window.location.hostname}/chatpdf.ai-pro.org`;else if("on"===A)"aihub"===k?window.top.location.href="/my-account":"1"===a?window.self.location.href="/my-account":t("/my-account");else{const e=(0,r.bG)("pricing"),t=(0,r.bG)("iSplanEnt");"62"===e||"1"===t?"aihub"===k?window.top.location.href=`https://${window.location.hostname}/chat.ai-pro.org`:"1"===a?window.self.location.href="/pay/mcWiDilmgQ":window.location.href="/pay/mcWiDilmgQ":"aihub"===k?window.top.location.href=`https://${window.location.hostname}/chat.ai-pro.org`:"1"===a?window.self.location.href="/pay":window.location.href="/pay"}};return(0,a.useEffect)(()=>{let e=!0;return(async()=>{try{const t=await i.Z.post("http://localhost:9002/api/register-google");e&&t.data.success&&t.data.url&&E(t.data.url)}catch(e){console.error("Error fetching Google register URL:",e)}})(),(async()=>{try{const e=await i.Z.post("http://localhost:9002/api/register-apple",{reg_num:"0"},{headers:{"content-type":"application/x-www-form-urlencoded"}});e.data.success&&e.data.url&&I(e.data.url)}catch(e){console.error("Error fetching Apple register URL:",e)}})(),()=>{e=!1}},[]),(0,x.jsxs)("section",{children:[(0,x.jsxs)("div",{className:"border-b-2 border-gray-200 pb-4 mb-4",children:[(0,x.jsx)("p",{className:"text-[36px] text-[#3B5ABF] font-bold",children:"Sign Up"}),(0,x.jsx)("p",{className:"text-[#000] text-[18px] font-bold mt-[20px]",children:"Get Started!"})]}),(0,x.jsx)("input",{ref:o,type:"text",name:"user_phone",className:"hidden absolute left-[-9999px]",autoComplete:"off"}),(0,x.jsxs)("div",{className:"mb-[20px]",children:[(0,x.jsx)("label",{className:"font-semibold text-[14px] text-[#333]",children:"Email Address"}),(0,x.jsx)("input",{type:"email",name:"email",value:m,onChange:e=>{h(e.target.value),b("")},placeholder:"Enter your email address",className:"w-full h-[48px] mt-[8px] px-4 py-2 bg-[#f7f7f7] text-[14px] rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"}),f&&(0,x.jsx)("p",{className:"text-red-500 text-sm mt-1",children:f})]}),(0,x.jsxs)("div",{className:"mb-[20px]",children:[(0,x.jsx)("label",{className:"font-semibold text-[14px] text-[#333]",children:"Password"}),(0,x.jsxs)("div",{className:"relative",children:[(0,x.jsx)("input",{type:y?"text":"password",name:"password",value:u,onChange:e=>{w(e.target.value),j("")},placeholder:"Enter your password",className:"w-full h-[48px] mt-[8px] px-4 py-2 bg-[#f7f7f7] rounded-md text-[14px] focus:outline-none focus:ring-2 focus:ring-blue-500"}),(0,x.jsx)("div",{className:"absolute inset-y-0 right-3 flex items-center cursor-pointer text-gray-500",onClick:()=>N(e=>!e),children:y?(0,x.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"mt-[6px]",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"#708090",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,x.jsx)("path",{d:"M1 12s4-7 11-7 11 7 11 7-4 7-11 7-11-7-11-7z"}),(0,x.jsx)("path",{d:"M3 3l18 18"}),(0,x.jsx)("circle",{cx:"12",cy:"12",r:"3"})]}):(0,x.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"mt-[6px]",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"#708090",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,x.jsx)("path",{d:"M1 12s4-7 11-7 11 7 11 7-4 7-11 7-11-7-11-7z"}),(0,x.jsx)("circle",{cx:"12",cy:"12",r:"3"})]})})]}),v&&(0,x.jsx)("p",{className:"text-red-500 text-sm mt-1",children:v})]}),(0,x.jsx)("button",{onClick:()=>{var e,t;null!==(e=o.current)&&void 0!==e&&e.value&&((0,r.I1)("ishoneypot","yes",{path:"/"}),(0,r.I1)("ishoneypot","yes",{domain:".ai-pro.org",path:"/"}));const a=(()=>{let e=!1;return m?/\S+@\S+\.\S+/.test(m)?(b(""),e=!0):b(s("echo.register.form.emailInvalidText")||"Invalid email address."):b(s("echo.register.form.emailReqText")||"Email is required."),e})(),c=(()=>{let e=(0,l.X)(u);return e?(j(e),!1):(j(""),!0)})();a&&c&&(null===(t=document.querySelector(".loader-container"))||void 0===t||t.classList.add("active"),i.Z.post("http://localhost:9002/api/t/register",{email:m,password:u,pass_con:u,emailOptIn:g},{headers:{"content-type":"application/x-www-form-urlencoded"}}).then(e=>{var t;const s=e.data;var a;(null===(t=document.querySelector(".loader-container"))||void 0===t||t.classList.remove("active"),s.success)?(n().success("Success"),(0,r.I1)("access",s.data.login_token),(0,r.I1)("user_email",s.data.email,{domain:".ai-pro.org",path:"/"}),C()):n().error((null===(a=s.data)||void 0===a?void 0:a.msg)||"Registration failed")}).catch(e=>{var t,s;null===(t=document.querySelector(".loader-container"))||void 0===t||t.classList.remove("active"),429===(null===(s=e.response)||void 0===s?void 0:s.status)&&n().error("Too many requests. Please try again later.")}))},style:{background:"linear-gradient(to right, #3D57BB 0%, #268EEC 100%)",boxShadow:"0px 4px 5px 0px rgba(0, 0, 0, 0.13)"},className:"w-full h-[48px] text-white font-semibold text-[20px] rounded-md hover:bg-[#3B5ABF] focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:cursor-not-allowed",children:"Sign Up"}),(0,x.jsxs)("div",{className:"flex items-center mb-4 mt-[24px] mx-auto custom-lg:w-[60%] w-[283px]",children:[(0,x.jsx)("div",{className:"flex-grow h-px bg-[#E6E6E6]"}),(0,x.jsx)("span",{className:"px-3 text-sm text-gray-600 whitespace-nowrap",children:"or continue with"}),(0,x.jsx)("div",{className:"flex-grow h-px bg-[#E6E6E6]"})]}),(0,x.jsxs)("div",{className:"flex gap-[16px] justify-center items-center mb-[20px]",children:[(0,x.jsxs)("button",{onClick:()=>(()=>{try{"aihub"===k?window.top.open(D,"GoogleRegister",S)||(console.log("Error opening new GoogleRegister"),window.top.location.href=D):window.open(D,"GoogleRegister",S)||(console.log("Error opening new GoogleRegister"),window.location.href=D)}catch{console.log("Error opening new window"),"aihub"===k?window.top.location.href=D:window.location.href=D}})(),className:"flex items-center justify-center border w-[122px] h-[38px] border-gray-300 py-[7.3px] px-[22px] rounded-full gap-[6px]",children:[(0,x.jsx)("div",{children:(0,x.jsx)("img",{src:p,alt:"",className:"w-[19px]"})}),(0,x.jsx)("p",{className:"text-[14px] text-[#333]",children:"Google"})]}),(0,x.jsxs)("button",{onClick:()=>(()=>{try{"aihub"===k?window.top.open(B,"AppleRegister",S)||(window.top.location.href=B):window.open(B,"AppleRegister",S)||(window.location.href=B)}catch(e){"aihub"===k?window.top.location.href=B:window.location.href=B}})(),className:"flex items-center justify-center w-[122px] h-[38px] border border-gray-300 py-[7.3px] px-[22px] rounded-full gap-[6px]",children:[(0,x.jsx)("div",{className:"",children:(0,x.jsx)("img",{src:d,alt:"",className:"w-[15px]"})}),(0,x.jsx)("p",{className:"text-[14px] text-[#333]",children:"Apple"})]})]}),(0,x.jsx)("div",{className:"mx-auto mb-[20px] w-fit",children:(0,x.jsxs)("span",{className:"text-center text-[14px] text-[#333]",children:["Already have an account?"," ",(0,x.jsx)("span",{onClick:()=>window.top.location.href="/login",className:"text-[#3B5ABF] cursor-pointer font-bold hover:underline",children:"Log In"})]})})]})}const h=[{name:"Fiona Edmonds",desc:"I use AI as a tool to float questions, in order for me to write a narrative…",init:"FE"},{name:"Dan Huffman",desc:"I feel like I've stumbled into a creative playground of the mind…",init:"DH"},{name:"Info TapTech",desc:"It meets expectations and gets better every time I use it. A real-time saver…",init:"IT"},{name:"Thomas Rudge",desc:"Simple and easy to use. I created a resume after 8 years in less than 2mins.",init:"TR"}],u={FE:"bg-[#6A5ACD]",DH:"bg-[#944D31]",IT:"bg-[#20B2AA]",TR:"bg-[#DF4B3D]"},w=["bg-[#6A5ACD]","bg-[#3CB371]","bg-[#FFB347]","bg-[#20B2AA]","bg-[#6495ED]","bg-[#FF69B4]","bg-[#944D31]","bg-[#DBBDC8]","bg-[#AF9D91]","bg-[#DF4B3D]"],g=()=>(0,x.jsx)("div",{className:"overflow-hidden whitespace-nowrap w-[94%] relative flex-nowrap inline-flex mb-2",children:(0,x.jsx)("div",{className:"animate-marquee flex items-center justify-center",children:[...h,...h].map((e,t)=>{const s=u[e.init]||w[t%w.length];return(0,x.jsx)("div",{className:"bg-white rounded-3xl p-[16px] w-[418px] max-w-[418px] border-2 border-[#E7E7E7] inline-block mr-2",children:(0,x.jsxs)("div",{className:"flex gap-[20px] items-center",children:[(0,x.jsx)("div",{className:`rounded-full text-[22px] w-[50px] h-[50px] ${s} text-white flex justify-center items-center`,children:(0,x.jsx)("p",{children:e.init})}),(0,x.jsxs)("div",{className:"flex-1 leading-[20px] text-[#616161]",children:[(0,x.jsx)("p",{className:"text-[14px] leading-[16px] break-words whitespace-normal mb-[8px]",children:e.desc}),(0,x.jsx)("p",{className:"text-[12px] break-words whitespace-normal font-medium",children:e.name})]})]})},t)})})}),f=[{name:"Aaron Hamer",desc:"This AI platform has improved my research skills, serving as a tool that helps me discover better ways to access information.",init:"AH"},{name:"Henry Parada",desc:"I used it to write my ideas for my artwork…it has been excellent because I have been able to express my ideas clearly.",init:"HP"},{name:"Jim Parnell",desc:"…What I appreciate most about this AI program is its versatility. It truly is only limited by your own imagination.",init:"JP"},{name:"D Green",desc:"…when I am curious about something, I use this chat. It never judges me for my thousands of questions!!",init:"DG"}],b={AH:"bg-[#6A5ACD]",HP:"bg-[#3CB371]",JP:"bg-[#FFB347]",DG:"bg-[#944D31]"},v=["bg-[#6A5ACD]","bg-[#3CB371]","bg-[#FFB347]","bg-[#20B2AA]","bg-[#6495ED]","bg-[#FF69B4]","bg-[#944D31]","bg-[#DBBDC8]","bg-[#AF9D91]","bg-[#DF4B3D]"],j=()=>(0,x.jsx)("div",{className:"overflow-hidden whitespace-nowrap w-[94%] relative flex-nowrap inline-flex",children:(0,x.jsx)("div",{className:"animate-marqueeReverse flex items-center justify-center md:justify-start",children:[...f,...f].map((e,t)=>{const s=b[e.init]||v[t%v.length];return(0,x.jsx)("div",{className:"bg-white rounded-3xl p-[16px] w-[418px] max-w-[418px] border-2 border-[#E7E7E7] inline-block mr-2",children:(0,x.jsxs)("div",{className:"flex gap-[20px] items-center",children:[(0,x.jsx)("div",{className:`rounded-full text-[22px] w-[50px] h-[50px] ${s} text-white flex justify-center items-center`,children:(0,x.jsx)("p",{children:e.init})}),(0,x.jsxs)("div",{className:"flex-1 leading-[20px] text-[#616161]",children:[(0,x.jsx)("p",{className:"text-[14px] leading-[16px] break-words whitespace-normal mb-[8px]",children:e.desc}),(0,x.jsx)("p",{className:"text-[12px] break-words whitespace-normal font-medium",children:e.name})]})]})},t)})})});var y=s(2656),N=s(82940);function k(e){let{handleRedirect:t}=e;return(0,x.jsx)("div",{className:"relative flex-col w-full text-gray-600 bg-transparent text-sm",children:(0,x.jsxs)("div",{className:"flex items-center",style:{zIndex:"1"},children:[(0,x.jsxs)("div",{className:"flex-1 w-[20%] hidden xl:block lg:block md:block sm:block",children:[(0,x.jsxs)("div",{className:"flex flex-col items-center justify-center",children:[(0,x.jsx)("img",{src:y,alt:"",className:"w-[308px] h-[193px]"}),(0,x.jsx)("img",{src:N,alt:"",className:" h-[59px] mb-[1rem]"})]}),(0,x.jsx)(g,{}),(0,x.jsx)(j,{}),(0,x.jsx)("div",{className:"absolute top-[100px] left-[-93px] w-full h-[100px] z-[-1]",children:(0,x.jsx)("img",{src:"/assets/bg-grid.png",alt:""})})]}),(0,x.jsx)("div",{className:"flex-1 px-[20px] xl:px-[47px] lg:px-[47px] md:px-[47px] sm:px-[47px] pt-[40px] xl:pt-0 lg:pt-0 md:pt-0 sm:pt-0",children:(0,x.jsx)(m,{handleRedirect:t})})]})})}},40642:(e,t,s)=>{e.exports=s.p+"static/media/apple_ico.af13ada0dd55a0d30879.webp"},2656:(e,t,s)=>{e.exports=s.p+"static/media/ai-chatbot-user.03770d4057f917c92c9b.png"},82940:(e,t,s)=>{e.exports=s.p+"static/media/available-on-browsers.da6c95b6de6eaaea01ad.png"},3633:(e,t,s)=>{e.exports=s.p+"static/media/google_icon.aeb309a8678785836262.png"}}]);
//# sourceMappingURL=1875.6877a199.chunk.js.map