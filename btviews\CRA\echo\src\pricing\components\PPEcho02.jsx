import { useTranslation } from 'react-i18next'
import { useMemo } from 'react'

const PPEcho02 = ({ planType }) => {
    const { t } = useTranslation()

    const perMonthOptions = ['tokens_per_month', 'images_per_month', 'videos_per_month']

    const pricingDescription = useMemo(() => {
        const getBasicLanguageModels = () => {
            return [
                'GPT-4o',
                'DeepSeek',
                {
                    model: 'Claude',
                    tokens_per_month: '25k',
                },
            ]
        }

        const getProLanguageModels = () => {
            return [
                'GPT-4o',
                'DeepSeek',
                {
                    model: 'Grok',
                    new_badge: true,
                },
                {
                    model: 'GPT-5',
                    new_badge: true,
                },
                {
                    model: 'Claude',
                    tokens_per_month: '50k',
                },
            ]
        }

        const getProMaxLanguageModels = () => {
            return [
                'GPT-4o',
                'DeepSeek',
                {
                    model: 'Grok',
                    new_badge: true,
                },
                {
                    model: 'GPT-5',
                    new_badge: true,
                },
                'OpenAI o1',
                {
                    model: '<PERSON>',
                    unlimited: true,
                },
            ]
        }

        const getImageModels = () => {
            const dalle_limit = {
                Basic: 20,
                Pro: 50,
            }

            const flux_limit = {
                Basic: 15,
                Pro: 30,
                ProMax: 160,
            }

            const dalle_model = 'DALL-E 3'
            const current_dalle_limit = dalle_limit[planType] ?? null
            const current_flux_limit = flux_limit[planType] ?? null
            let models = []

            if (planType !== 'ProMax') {
                if (current_dalle_limit !== null) {
                    models.push({
                        model: dalle_model,
                        images_per_month: current_dalle_limit,
                    })
                }
            } else {
                models.push({
                    model: dalle_model,
                    unlimited: true,
                })
            }

            if (current_flux_limit !== null) {
                models.push({
                    model: 'Flux',
                    images_per_month: current_flux_limit,
                })
            }

            return models
        }

        const getVideoModels = () => {
            let models = []

            const klingai_limit = {
                Pro: 3,
                ProMax: 15,
            }

            const current_klingai_limit = klingai_limit[planType] ?? null

            if (current_klingai_limit !== null) {
                models.push({
                    model: 'KlingAI',
                    videos_per_month: current_klingai_limit,
                })
            }

            return models
        }

        const getFeatures = () => {
            const dialouge_limit = {
                Basic: 500000,
                Pro: 1000000,
            }

            const translation = {
                dialougeLimit: t('echo.pricing.vprice.dialogueLimitText'),
            }

            const current_dialouge_limit = dialouge_limit[planType] ?? null
            let features = []

            if (planType !== 'ProMax') {
                if (current_dialouge_limit !== null) {
                    features.push({
                        text: translation.dialougeLimit,
                        tokens_per_month: current_dialouge_limit,
                    })
                }
            } else {
                features.push({
                    text: translation.dialougeLimit,
                    unlimited: true,
                })
            }

            return features
        }

        const models = {
            Basic: getBasicLanguageModels(),
            Pro: getProLanguageModels(),
            ProMax: getProMaxLanguageModels(),
        }

        let pricingDescription = {
            languageModels: models[planType] ?? [],
            imageGeneration: getImageModels(),
            videoGeneration: getVideoModels(),
            features: getFeatures(),
        }

        if (planType === 'Basic') {
            delete pricingDescription.videoGeneration
        } else if (['Enterprise', 'Advanced'].includes(planType)) {
            pricingDescription = []
        }

        const translation = {
            new_badge: t('echo.pricing.vprice.newBadgeText'),
            unlimited: t('echo.pricing.vprice.unlimitedText'),
            tokens: t('echo.pricing.vprice.tokensText'),
            images: t('echo.pricing.vprice.imagesText'),
            videos: t('echo.pricing.vprice.videosText'),
            perMonth: t('echo.pricing.vprice.perMonthText'),
        }

        const booleanOptions = ['new_badge', 'unlimited']

        return Object.fromEntries(
            Object.entries(pricingDescription).map(([section, sectionValues]) => {
                const translatedSection = t(`echo.pricing.vprice.${section}`)

                sectionValues = sectionValues.map((option) => {
                    if (typeof option === 'object') {
                        const hasBooleanOption = booleanOptions.some((currentOption) => option.hasOwnProperty(currentOption))
                        const hasPerMonthOption = perMonthOptions.some((currentOption) => option.hasOwnProperty(currentOption))

                        if (hasBooleanOption) {
                            const currentBooleanOption = booleanOptions.find((currentOption) => option.hasOwnProperty(currentOption))

                            if (option[currentBooleanOption]) {
                                option[currentBooleanOption] = translation[currentBooleanOption]
                            }
                        } else if (hasPerMonthOption) {
                            const currentPerMonthOption = perMonthOptions.find((currentOption) => option.hasOwnProperty(currentOption))

                            if (option[currentPerMonthOption]) {
                                option[currentPerMonthOption] = `${option[currentPerMonthOption]} ${translation[currentPerMonthOption]}`
                            }
                        }

                        if (!option.hasOwnProperty('text') && option.hasOwnProperty('model')) {
                            option['text'] = option.model
                        }
                    }

                    return option
                })

                return [translatedSection, sectionValues]
            })
        )
    }, [t, planType, perMonthOptions])

    return (
        <>
            {Object.entries(pricingDescription).map(([translatedSection, sectionValues], index) => (
                <li key={index} className="w-full">
                    <h3 className="font-semibold text-gray-800 mb-3">{translatedSection}</h3>

                    {sectionValues.length > 0 && (
                        <ul>
                            {sectionValues.map((value, index) => (
                                <li key={index} className="flex items-center">
                                    <span className="text-blue-600 mr-2">✓</span>
                                    {typeof value !== 'object' ? (
                                        <span className="text-gray-700">{value}</span>
                                    ) : (
                                        <>
                                            {'text' in value && (
                                                <>
                                                    <span className="text-gray-700">
                                                        {value.text}
                                                        {value.unlimited && (
                                                            <>
                                                                {': '}
                                                                <span className="text-blue-600 font-medium">{value.unlimited}</span>
                                                            </>
                                                        )}
                                                    </span>
                                                    {value.new_badge && <span className="ml-2 bg-blue-100 text-blue-600 text-xs px-2 py-0.5 rounded">{value.new_badge}</span>}
                                                </>
                                            )}
                                        </>
                                    )}
                                </li>
                            ))}
                        </ul>
                    )}
                </li>
            ))}
        </>
    )
}

export default PPEcho02
