import { GetCookie } from '../../core/utils/cookies';
export function getCurrency(currency) {
    if(!currency) return "";
    if(currency.toLowerCase() === 'usd') return "$";
    if(currency.toLowerCase() === 'eur') return "€";
    if(currency.toLowerCase() === 'gbp') return "£";
    if(currency.toLowerCase() === 'brl') return "R$";
    if(currency.toLowerCase() === 'sar') return "R$";
    if(currency.toLowerCase() === 'czk') return "Kč";
    return "";
}

export function getCountry(currency) {
    if(!currency) return "";
    if(currency.toLowerCase() === 'usd') return "US";
    if(currency.toLowerCase() === 'eur') return "US";
    if(currency.toLowerCase() === 'gbp') return "GB";
    if(currency.toLowerCase() === 'brl') return "US";
    if(currency.toLowerCase() === 'sar') return "AE";
    if(currency.toLowerCase() === 'chf') return "CH";
    if(currency.toLowerCase() === 'sek') return "SE";
    return "US";
}

export function formatDate(dateStr) {
    if(!dateStr) return "";
    const month = ["January","February","March","April","May","June","July","August","September","October","November","December"];
    const date = new Date(dateStr.replace(/ /g,"T"));

    return month[date.getMonth()] + " " + date.getDate() + " " + date.getFullYear();
}

export function getPrice(data) {
    if(data.trial_price) return data.trial_price;
    if(data.price) return data.price;
    return "0";
}

export function numberWithCommas(x) {
    return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}

export function formatNumber(number) {
  const floatNumber = parseFloat(number);
  return (floatNumber % 1 === 0)
      ? numberWithCommas(floatNumber.toFixed(0))
      : numberWithCommas(floatNumber.toFixed(2)); 
}

export function getPricePlan(currency, price) {
    if(!currency || !price) return "";
    // Check if price is a number
    if(isNaN(price)) return "";
    // Check if price is positive number
    if(parseFloat(price) >= 0) {
        if(currency.toLowerCase() === 'sar') {
            return formatNumber(price).toLocaleString("en-US") + "ر.س.";
        } else if(currency.toLowerCase() === 'aed') {
            return formatNumber(price).toLocaleString("en-US") + "AED";
        } else if(currency.toLowerCase() === 'chf') {
            return formatNumber(price).toLocaleString("en-US") + "CHF";
        } else if(currency.toLowerCase() === 'sek') {
            return formatNumber(price).toLocaleString("en-US") + "SEK";
        } else if(currency.toLowerCase() === 'pln') {
            return formatNumber(price).toLocaleString("en-US") + "zł";
        }else if(currency.toLowerCase() === 'ron') {
            return formatNumber(price).toLocaleString("en-US") + "LEI";
        } else if(currency.toLowerCase() === 'huf') {
            return formatNumber(price).toLocaleString("en-US") + "Ft";
        } else if(currency.toLowerCase() === 'dkk') {
            return formatNumber(price).toLocaleString("en-US") + "kr.";
        } else if(currency.toLowerCase() === 'bgn') {
            return formatNumber(price).toLocaleString("en-US") + "лв";
        } else if(currency.toLowerCase() === 'try') {
            return formatNumber(price).toLocaleString("en-US") + "₺";
        }
        return getCurrency(currency) + formatNumber(price).toLocaleString("en-US");
    }
    // Negative number
    return "-" + getCurrency(currency) + (formatNumber(price) * -1).toLocaleString("en-US");
}

export function diffMin(dt1, dt2)
{
    dt1 = new Date(dt1);
    dt2 = new Date(dt2);
    var diff =(dt2.getTime() - dt1.getTime()) / 1000;
    diff /= 60;
    return Math.abs(Math.round(diff));
}

export function formatDateTime(dateStr) {
    if(!dateStr) return "";
    const month = ["January","February","March","April","May","June","July","August","September","October","November","December"];
    const date = new Date(dateStr.replace(/ /g,"T"));

    return month[date.getMonth()] + " " + date.getDate() + " " + date.getFullYear() + " " + date.getHours() + ":" + date.getMinutes();
}

export function DailyPrice({plan}) {
  let dailyPrice = '';
  let interval = '';
  if (plan.payment_interval === 'Yearly') {
    dailyPrice = getPricePlan(plan.currency, parseFloat(plan.price / 365).toFixed(2));
    interval = <div class="text-xs mb-4">billed {getPricePlan(plan.currency, plan.price)}/year</div>;
  }

  if (plan.payment_interval === 'Monthly') {
    dailyPrice = getPricePlan(plan.currency, parseFloat(plan.price / 30).toFixed(2));
    interval = <div class="text-xs mb-4">billed {getPricePlan(plan.currency, plan.price)}/Month</div>;
  }

  if (plan.trial_price) {
    dailyPrice = getPricePlan(plan.currency, parseFloat(plan.trial_price / plan.trial_days).toFixed(2));
    interval = <div class="text-xs mb-4">billed {getPricePlan(plan.currency, plan.trial_price)}</div>;
  }

  return (
    <>
      <p className={`text-4xl font-bold text-gray-800 ${(GetCookie("p_toggle") === '') ? 'mb-4' : ''}`}>
        {dailyPrice} <span className="text-sm"> per Day</span>
      </p>
      {interval}
    </>
  );
}

export function PriceFormatted({plan}) {
  if (GetCookie("daily") === 'on') {
    return DailyPrice({plan});
  } 
  if (plan.trial_price) {
    return (<p className="text-4xl font-bold text-gray-800 mb-4">{getPricePlan(plan.currency, plan.trial_price)}</p>)
  } 
  return (
    <p className="text-4xl font-bold text-gray-800 mb-4">
      {getPricePlan(plan.currency, plan.price)}
      <span className="text-sm"> 
        /{ plan.payment_interval === "Monthly" ? "month" : "year" }
      </span>
    </p>
  )
}

export function displayTextFormatted(plan) {
  const locales = (GetCookie('locales') ?? 'en').toLowerCase();
  const daily = (GetCookie('daily') ?? 'off').toLowerCase();
  const { trial_days, payment_interval, trial_price, currency, currency_symbol } = plan;
  let { display_txt2: pricing_description, price } = plan;

  if (trial_days > 0 && trial_price > 0 && locales === 'en') {
    const per_day = payment_interval === 'Yearly' ? 365 : 30;
    let amount = price;
    let price_text = 'month';

    if (daily === 'on') {
      amount = parseFloat(price / per_day).toFixed(2);
      price_text = `day<br>(billed ${currency_symbol + price} ${payment_interval})`;
    }

    pricing_description += `<div>${trial_days}-Day Trial, then only ${getPricePlan(currency, amount)} per ${price_text}</div>`;
  }

  return pricing_description;
}