{"version": 3, "file": "static/js/7138.c6e77b23.chunk.js", "mappings": "oIAEA,MAQA,EARkBA,IAA6B,IAA5B,IAAEC,EAAG,IAAEC,KAAQC,GAAOH,EACvC,OACEI,EAAAA,EAAAA,KAACC,EAAAA,SAAQ,CAACC,UAAUF,EAAAA,EAAAA,KAAA,OAAAG,SAAK,eAAiBA,UACxCH,EAAAA,EAAAA,KAAA,OAAKH,IAAKA,EAAKC,IAAKA,KAASC,M,uFCH5B,SAASK,IACd,MAAMC,EAAOC,SAASC,cAAc,UACpC,SAAOF,EAAKG,aAAcH,EAAKG,WAAW,QAC2B,IAA5DH,EAAKI,UAAU,cAAcC,QAAQ,kBAGhD,CAgDO,SAASC,EAAQC,GAEtB,OAAW,MAAPA,GAAeA,EAAIC,OAAS,IADf,eAC8BC,KAAKF,GAC3C,UAKF,IAFPA,EAAMA,EAAIG,MAAM,EAAG,IAGrB,CAGO,SAASC,EAAYJ,IAEf,MAAPA,GAAeA,EAAIC,OAAS,IADf,eAC8BC,KAAKF,MAClDA,EAAM,UAGR,IAAIK,EAAIC,SAASN,EAAIG,MAAM,EAAG,GAAI,IAC9BI,EAAID,SAASN,EAAIG,MAAM,EAAG,GAAI,IAC9BK,EAAIF,SAASN,EAAIG,MAAM,EAAG,GAAI,IAElCE,EAAII,KAAKC,IAAI,EAAGD,KAAKE,IAAI,IAAKN,EAAS,IAAJA,IACnCE,EAAIE,KAAKC,IAAI,EAAGD,KAAKE,IAAI,IAAKJ,EAAS,IAAJA,IACnCC,EAAIC,KAAKC,IAAI,EAAGD,KAAKE,IAAI,IAAKH,EAAS,IAAJA,IAEnC,MAAMI,EAASC,GACIJ,KAAKK,MAAMD,GAAOE,SAAS,IAC5BC,SAAS,EAAG,KAG9B,MAAO,IAAIJ,EAAMP,KAAKO,EAAML,KAAKK,EAAMJ,IACzC,CAEO,SAASS,IACd,MAAOC,EAAUC,IAAgBC,EAAAA,EAAAA,WAAS,IACnCC,EAAUC,IAAeF,EAAAA,EAAAA,WAAS,IAClCG,EAAWC,IAAgBJ,EAAAA,EAAAA,WAAS,GAmB3C,OAjBAK,EAAAA,EAAAA,WAAU,KACR,MAAMC,EAAeA,KACnB,MAAMC,EAAQC,OAAOC,WAErBV,EAAaQ,EAAQ,KACrBL,EAAYK,EAAQ,KAAOA,GAAS,KACpCH,EAAaG,EAAQ,MAMvB,OAHAD,IACAE,OAAOE,iBAAiB,SAAUJ,GAE3B,KACLE,OAAOG,oBAAoB,SAAUL,KAEtC,IAEI,CAAER,WAAUG,WAAUE,YAC/B,C,iMCpGA,MAgbA,EAhbuBvC,IAOhB,IAPiB,eACtBgD,EAAc,cACdC,EAAa,eACbC,EAAc,WACdC,EAAU,UACVC,EAAS,QACTC,GAAU,GACXrD,EACC,MAAM,EAAEsD,IAAMC,EAAAA,EAAAA,MACRC,EAAW,MACf,IAEE,GAAIZ,OAAOa,MAAQb,OAAOc,KAAM,CAE9B,MACMC,EADgBf,OAAOa,IAAI/C,SAASkD,OACZC,MAAM,cACpC,OAAOF,EAAUA,EAAQ,GAAK,IAChC,CACA,OAAOG,EAAAA,EAAAA,IAAU,KACnB,CAAE,MAAOC,GAEP,OAAOD,EAAAA,EAAAA,IAAU,KACnB,CACD,EAdgB,GAeXE,GAAUxD,EAAAA,EAAAA,MAAoByD,EAAWC,EACzCC,GAAU3D,EAAAA,EAAAA,MAAoB4D,EAAWC,EACzCC,GAAc9D,EAAAA,EAAAA,MAAoB+D,EAAeC,GAChDC,EAAWC,IAAgBtC,EAAAA,EAAAA,UAAS,KACpCuC,EAAUC,IAAexC,EAAAA,EAAAA,UAAS,KAClCyC,EAAYC,IAAiB1C,EAAAA,EAAAA,UAAS,IAEvC2C,EAAcnC,OAAOoC,OAAOrC,MAS5BsC,EAAiB,6BARFrC,OAAOoC,OAAOE,OAGd,KAGuB,WAD9BH,EAHM,KAGuB,IAK3C,SAASI,IACP,IACE,GAAiB,UAAb3B,EAAsB,CAENZ,OAAOa,IAAI2B,KAAKT,EAAU,gBAAiBM,KAE3DrC,OAAOa,IAAI4B,SAASC,KAAOX,EAE/B,KAAO,CAEa/B,OAAOwC,KAAKT,EAAU,gBAAiBM,KAEvDrC,OAAOyC,SAASC,KAAOX,EAE3B,CACF,CAAE,MAAOZ,GACU,UAAbP,EACFZ,OAAOa,IAAI4B,SAASC,KAAOX,EAE3B/B,OAAOyC,SAASC,KAAOX,CAE3B,CACF,CACA,SAASY,IACP,IACE,GAAiB,UAAb/B,EAAsB,CAENZ,OAAOa,IAAI2B,KAC3BP,EACA,gBACAI,KAGArC,OAAOa,IAAI4B,SAASC,KAAOT,EAE/B,KAAO,CAEajC,OAAOwC,KACvBP,EACA,gBACAI,KAGArC,OAAOyC,SAASC,KAAOT,EAE3B,CACF,CAAE,MAAOd,GACU,UAAbP,EACFZ,OAAOa,IAAI4B,SAASC,KAAOT,EAE3BjC,OAAOyC,SAASC,KAAOT,CAE3B,CACF,CAEA,SAASW,IACP,IACE,GAAiB,UAAbhC,EAAsB,CAENZ,OAAOa,IAAI2B,KAC3BX,EACA,iBACAQ,KAGAQ,QAAQC,IAAI,oCACZ9C,OAAOa,IAAI4B,SAASC,KAAOb,EAE/B,KAAO,CAEa7B,OAAOwC,KACvBX,EACA,iBACAQ,KAGAQ,QAAQC,IAAI,oCACZ9C,OAAOyC,SAASC,KAAOb,EAE3B,CACF,CAAE,MACAgB,QAAQC,IAAI,4BACK,UAAblC,EACFZ,OAAOa,IAAI4B,SAASC,KAAOb,EAE3B7B,OAAOyC,SAASC,KAAOb,CAE3B,CACF,CA6DA,OA3DAhC,EAAAA,EAAAA,WAAU,KACR,IAAIkD,GAAY,EAqDhB,MAnDqBC,WACnB,IACE,MAAMC,QAAiBC,EAAAA,EAAMC,KAC3B,6CAEEJ,GAAaE,EAASG,KAAKC,SAAWJ,EAASG,KAAKE,KACtDxB,EAAamB,EAASG,KAAKE,IAE/B,CAAE,MAAOnC,GACP0B,QAAQ1B,MAAM,sCAAuCA,EACvD,GAqCFoC,GAjBoBP,WAClB,IACE,MAAMC,QAAiBC,EAAAA,EAAMC,KAC3B,2CACA,CACEK,QAAS,IAEX,CAAEC,QAAS,CAAE,eAAgB,uCAE3BR,EAASG,KAAKC,SAAWJ,EAASG,KAAKE,KACzCtB,EAAYiB,EAASG,KAAKE,IAE9B,CAAE,MAAOnC,GACP0B,QAAQ1B,MAAM,qCAAsCA,EACtD,GAIFuC,GAnCsBV,WACpB,IACE,MAAMC,QAAiBC,EAAAA,EAAMC,KAC3B,2CACA,CACEK,QAAS,KAEX,CAAEC,QAAS,CAAE,eAAgB,uCAE3BR,EAASG,KAAKC,SAAWJ,EAASG,KAAKE,KACzCpB,EAAce,EAASG,KAAKE,IAEhC,CAAE,MAAOnC,GACP0B,QAAQ1B,MAAM,qCAAsCA,EACtD,GAsBFwC,GAEO,KACLZ,GAAY,IAEb,KAGDvF,EAAAA,EAAAA,KAAAoG,EAAAA,SAAA,CAAAjG,SACgB,UAAbiD,GAECiD,EAAAA,EAAAA,MAAAD,EAAAA,SAAA,CAAAjG,SAAA,EACEkG,EAAAA,EAAAA,MAAA,OAAKC,UAAU,0CAAyCnG,SAAA,CACvC,OAAd6C,IACChD,EAAAA,EAAAA,KAAA,OAAKsG,UAAU,qBAAoBnG,UACjCkG,EAAAA,EAAAA,MAAA,UACEE,KAAK,SACLD,UAAU,wDACVE,UAAWjC,EACXkC,SAAU,EACVC,QAASA,KACPjC,GAAcU,KAEhBwB,UAAYC,IACI,UAAVA,EAAEC,KAAmBpC,GACvBU,KAGJ,aAAW,iBAAgBhF,SAAA,EAE3BH,EAAAA,EAAAA,KAAA,OAAKsG,UAAU,oCAAmCnG,UAChDH,EAAAA,EAAAA,KAAC8G,EAAAA,EAAS,CACRjH,IAAKqE,EACLpE,IAAI,aACJwG,UAAU,6BACV/D,MAAM,KACNuC,OAAO,UAGX9E,EAAAA,EAAAA,KAAA,OAAKsG,UAAU,4BAA2BnG,SAAC,6BAQjC,OAAf4C,IACC/C,EAAAA,EAAAA,KAAA,OAAKsG,UAAU,qBAAoBnG,UACjCkG,EAAAA,EAAAA,MAAA,UACEE,KAAK,SACLD,UAAU,wDACVE,UAAWnC,EACXoC,SAAU,EACVC,QAASA,IAAMrC,GAAae,IAC5BuB,UAAYC,IACI,UAAVA,EAAEC,KAAmBxC,GACvBe,KAGJ,aAAW,kBAAiBjF,SAAA,EAE5BH,EAAAA,EAAAA,KAAA,OAAKsG,UAAU,oCAAmCnG,UAChDH,EAAAA,EAAAA,KAAC8G,EAAAA,EAAS,CACRjH,IAAKkE,EACLjE,IAAI,cACJwG,UAAU,6BACV/D,MAAM,KACNuC,OAAO,UAGX9E,EAAAA,EAAAA,KAAA,OAAKsG,UAAU,4BAA2BnG,SAAC,kCAQnC,OAAd6C,GAAqC,OAAfD,KACtBsD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,sBAAqBnG,SAAA,EAClCkG,EAAAA,EAAAA,MAAA,QAAMC,UAAU,0BAAyBnG,SAAA,CAAC,0BAChB,QAE1BH,EAAAA,EAAAA,KAAA,UACEuG,KAAK,SACLD,UAAU,2BACVI,QAASA,IAAM9D,EAAe,SAC9B,aAAW,cAAazC,UAExBH,EAAAA,EAAAA,KAAA,OAAKsG,UAAU,uCAAsCnG,SAAC,mBAa9DkG,EAAAA,EAAAA,MAAAD,EAAAA,SAAA,CAAAjG,SAAA,EACEkG,EAAAA,EAAAA,MAAA,OAAKC,UAAU,0CAAyCnG,SAAA,CACtC,OAAf4C,IACC/C,EAAAA,EAAAA,KAAA,OAAKsG,UAAU,qBAAoBnG,UACjCkG,EAAAA,EAAAA,MAAA,UACEE,KAAK,SACLD,UAAW,kCAAiCrD,EAAU,4EAA8E,6BACpIuD,UAAWnC,EACXqC,QAASA,IAAMrC,GAAae,IAC5BuB,UAAYC,IACI,UAAVA,EAAEC,KAAmBxC,GACvBe,KAGJ,aAAW,kBAAiBjF,SAAA,EAE5BH,EAAAA,EAAAA,KAAA,OAAKsG,UAAU,oCAAmCnG,UAChDH,EAAAA,EAAAA,KAAC8G,EAAAA,EAAS,CACRjH,IAAK+D,EACL9D,IAAI,cACJwG,UAAU,6BACV/D,MAAM,KACNuC,OAAO,UAGX9E,EAAAA,EAAAA,KAAA,OAAKsG,UAAU,cAAanG,SACzB+C,EAAE,mDAMVJ,IACC9C,EAAAA,EAAAA,KAAA,OAAKsG,UAAU,qBAAoBnG,UACjCkG,EAAAA,EAAAA,MAAA,UACEE,KAAK,SACLD,UAAW,kCAAiCrD,EAAU,4EAA8E,gCACpIuD,UAAWnC,EACXqC,QAASA,IAAMrC,GAAae,IAC5BuB,UAAYC,IACI,UAAVA,EAAEC,KAAmBxC,GACvBe,KAGJ,aAAW,kBAAiBjF,SAAA,EAE5BH,EAAAA,EAAAA,KAAA,OAAKsG,UAAU,oCAAmCnG,UAChDH,EAAAA,EAAAA,KAAC8G,EAAAA,EAAS,CACRjH,IAAKkE,EACLjE,IAAI,cACJwG,UAAU,6BACV/D,MAAM,KACNuC,OAAO,UAGX9E,EAAAA,EAAAA,KAAA,OAAKsG,UAAU,4BAA2BnG,SAAC,8BAOlC,OAAd6C,IACChD,EAAAA,EAAAA,KAAA,OAAKsG,UAAU,qBAAoBnG,UACjCkG,EAAAA,EAAAA,MAAA,UACEE,KAAK,SACLD,UAAW,kCAAiCrD,EAAU,4EAA8E,6BACpIuD,UAAWjC,EACXmC,QAASA,IAAMnC,GAAYQ,IAC3B4B,UAAYC,IACI,UAAVA,EAAEC,KAAmBtC,GACvBQ,KAGJ,aAAW,iBAAgB5E,SAAA,EAE3BH,EAAAA,EAAAA,KAAA,OAAKsG,UAAU,oCAAmCnG,UAChDH,EAAAA,EAAAA,KAAC8G,EAAAA,EAAS,CACRjH,IAAKqE,EACLpE,IAAI,aACJwG,UAAU,6BACV/D,MAAM,KACNuC,OAAO,UAGX9E,EAAAA,EAAAA,KAAA,OAAKsG,UAAU,cAAanG,SACzB+C,EAAE,kDAMVL,IACC7C,EAAAA,EAAAA,KAAA,OAAKsG,UAAU,qBAAoBnG,UACjCkG,EAAAA,EAAAA,MAAA,UACEE,KAAK,SACLD,UAAW,kCAAiCrD,EAAU,4EAA8E,gCACpIuD,UAAWjC,EACXmC,QAASA,KACPjC,GAAcU,KAEhBwB,UAAYC,IACI,UAAVA,EAAEC,KAAmBpC,GACvBU,KAGJ,aAAW,iBAAgBhF,SAAA,EAE3BH,EAAAA,EAAAA,KAAA,OAAKsG,UAAU,oCAAmCnG,UAChDH,EAAAA,EAAAA,KAAC8G,EAAAA,EAAS,CACRjH,IAAKqE,EACLpE,IAAI,aACJwG,UAAU,6BACV/D,MAAM,KACNuC,OAAO,UAGX9E,EAAAA,EAAAA,KAAA,OAAKsG,UAAU,4BAA2BnG,SAAC,iCAOnC,OAAd6C,GAAqC,OAAfD,KACtBsD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,sBAAqBnG,SAAA,EAClCkG,EAAAA,EAAAA,MAAA,QAAMC,UAAW,cAAarD,EAAU,gBAAkB,iBAAkB9C,SAAA,CAAC,0BACnD,QAE1BH,EAAAA,EAAAA,KAAA,UACEuG,KAAK,SACLD,UAAU,2BACVI,QAASA,IAAM9D,EAAe,SAC9B,aAAW,cAAazC,UAExBH,EAAAA,EAAAA,KAAA,OAAKsG,UAAW,8BAA6BrD,EAAU,aAAe,IAAK9C,SAAC,oB", "sources": ["LazyImage.jsx", "core/utils/helper.jsx", "register/Buttons.jsx"], "sourcesContent": ["import React, { Suspense } from 'react';\r\n\r\nconst LazyImage = ({ src, alt, ...props }) => {\r\n  return (\r\n    <Suspense fallback={<div>Loading...</div>}>\r\n      <img src={src} alt={alt} {...props} />\r\n    </Suspense>\r\n  );\r\n};\r\n\r\nexport default LazyImage;\r\n", "import { useState, useEffect } from 'react';\r\n//check if browser supports WEBP\r\nexport function isWebpSupported() {\r\n  const elem = document.createElement('canvas');\r\n  if (!!(elem.getContext && elem.getContext('2d'))) {\r\n    return elem.toDataURL('image/webp').indexOf('data:image/webp') === 0;\r\n  }\r\n  return false;\r\n}\r\n// Observe sections/divs for lazy loading\r\nexport function observeSections(callback) {\r\n  const observer = new IntersectionObserver(entries => {\r\n    entries.forEach(entry => {\r\n      if (entry.isIntersecting) {\r\n        callback(entry.target.id);\r\n        // observer.unobserve(entry.target); // Stop observing once the section is intersecting\r\n      }\r\n    });\r\n  });\r\n  document.querySelectorAll('.lazy-section').forEach(section => {\r\n    observer.observe(section);\r\n  });\r\n  return () => {\r\n    observer.disconnect();\r\n  };\r\n}\r\n\r\n// Observe videos for lazy loading\r\nexport function observeVideos(callback) {\r\n  const observer = new IntersectionObserver(entries => {\r\n    entries.forEach(entry => {\r\n      if (entry.isIntersecting) {\r\n        const video = entry.target.querySelector('video');\r\n        if (video) {\r\n          const source = video.querySelector('source');\r\n          if (source && source.getAttribute('data-src')) {\r\n            source.setAttribute('src', source.getAttribute('data-src'));\r\n            video.load();\r\n            callback(entry.target.id);\r\n          }\r\n        }\r\n        observer.unobserve(entry.target);\r\n      }\r\n    });\r\n  });\r\n\r\n  document.querySelectorAll('.lazy-video').forEach(video => {\r\n    observer.observe(video);\r\n  });\r\n\r\n  return () => {\r\n    observer.disconnect();\r\n  };\r\n}\r\n\r\n// Hash pp_ctaclr\r\nexport function hexHash(hex) {\r\n  const hexRegex = /^[0-9a-f]*$/i;\r\n  if (hex == null || hex.length < 6 || !hexRegex.test(hex)) {\r\n    return `#1559ED`;\r\n  }\r\n\r\n  hex = hex.slice(0, 6);\r\n\r\n  return `#${hex}`;\r\n}\r\n\r\n// Darken color when hovering\r\nexport function hoverDarken(hex) {\r\n  const hexRegex = /^[0-9a-f]*$/i;\r\n  if (hex == null || hex.length < 6 || !hexRegex.test(hex)) {\r\n    hex = '1559ED';\r\n  }\r\n\r\n  let r = parseInt(hex.slice(0, 2), 16);\r\n  let g = parseInt(hex.slice(2, 4), 16);\r\n  let b = parseInt(hex.slice(4, 6), 16);\r\n\r\n  r = Math.max(0, Math.min(255, r - (r * 0.15)));\r\n  g = Math.max(0, Math.min(255, g - (g * 0.15)));\r\n  b = Math.max(0, Math.min(255, b - (b * 0.15)));\r\n\r\n  const toHex = (value) => {\r\n    const hexValue = Math.round(value).toString(16);\r\n    return hexValue.padStart(2, '0');\r\n  };\r\n  \r\n  return `#${toHex(r)}${toHex(g)}${toHex(b)}`;\r\n}\r\n\r\nexport function useDeviceSize() {\r\n  const [isMobile, setIsMobile_] = useState(false);\r\n  const [isTablet, setIsTablet] = useState(false);\r\n  const [isDesktop, setIsDesktop] = useState(false);\r\n\r\n  useEffect(() => {\r\n    const handleResize = () => {\r\n      const width = window.innerWidth;\r\n\r\n      setIsMobile_(width > 430);\r\n      setIsTablet(width > 729 && width <= 828); \r\n      setIsDesktop(width > 901); \r\n    };\r\n\r\n    handleResize(); \r\n    window.addEventListener('resize', handleResize);\r\n\r\n    return () => {\r\n      window.removeEventListener('resize', handleResize); \r\n    };\r\n  }, []);\r\n\r\n  return { isMobile, isTablet, isDesktop };\r\n}\r\n\r\nexport function renderContent(isTablet, isMobile, isDesktop, Infinite) {\r\n  if (isTablet) {\r\n    return <img src={Infinite} alt=\"Infinite\" className=\"mx-auto w-[22px]\" />;\r\n  } else {\r\n    return <span className=\"text-[#3C57BB] font-bold\">Unlimited</span>;\r\n  }\r\n}", "import React, { useState, useEffect } from \"react\";\r\nimport LazyImage from \"../LazyImage\";\r\nimport { isWebpSupported } from \"../core/utils/helper\";\r\nimport { GetCookie } from \"../core/utils/cookies\";\r\nimport img1 from \"../assets/images/google_icon.png\";\r\nimport img2 from \"../assets/images/google_icon_black.png\";\r\nimport img1webp from \"../assets/images/google_icon.webp\";\r\nimport img2webp from \"../assets/images/google_icon_black.webp\";\r\nimport imgApple from \"../assets/images/apple_ico.png\";\r\nimport imgApplewebp from \"../assets/images/apple_ico.webp\";\r\nimport axios from \"axios\";\r\nimport { useTranslation } from \"react-i18next\";\r\n\r\nconst RegisterButton = ({\r\n  handleRedirect,\r\n  regAppleShown,\r\n  regGoogleShown,\r\n  reg_google,\r\n  reg_apple,\r\n  isAiHub = false\r\n}) => {\r\n  const { t } = useTranslation();\r\n  const lpCookie = (() => {\r\n    try {\r\n      // Try to get cookie from parent window if in iframe\r\n      if (window.top !== window.self) {\r\n        // Direct access to parent window's cookies\r\n        const parentCookies = window.top.document.cookie;\r\n        const lpMatch = parentCookies.match(/lp=([^;]+)/);\r\n        return lpMatch ? lpMatch[1] : null;\r\n      }\r\n      return GetCookie(\"lp\");\r\n    } catch (error) {\r\n      // Fallback to current window if cross-origin\r\n      return GetCookie(\"lp\");\r\n    }\r\n  })();\r\n  const img1Src = isWebpSupported() ? img1webp : img1;\r\n  const img2Src = isWebpSupported() ? img2webp : img2;\r\n  const imgAppleSrc = isWebpSupported() ? imgApplewebp : imgApple;\r\n  const [googleUrl, setGoogleUrl] = useState(\"\");\r\n  const [appleUrl, setAppleUrl] = useState(\"\");\r\n  const [appleUrl_0, setAppleUrl_0] = useState(\"\");\r\n\r\n  const screenWidth = window.screen.width;\r\n  const screenHeight = window.screen.height;\r\n\r\n  const windowWidth = 600;\r\n  const windowHeight = 700;\r\n\r\n  const left = (screenWidth - windowWidth) / 2;\r\n  const top = (screenHeight - windowHeight) / 2;\r\n\r\n  const windowFeatures = `width=${windowWidth},height=${windowHeight},top=${top},left=${left}`;\r\n\r\n  function registerApple() {\r\n    try {\r\n      if (lpCookie === \"aihub\") {\r\n        // Break out of iframe for AI Hub\r\n        const newWindow = window.top.open(appleUrl, \"AppleRegister\", windowFeatures);\r\n        if (!newWindow) {\r\n          window.top.location.href = appleUrl;\r\n        }\r\n      } else {\r\n        // Regular behavior for non-AI Hub\r\n        const newWindow = window.open(appleUrl, \"AppleRegister\", windowFeatures);\r\n        if (!newWindow) {\r\n          window.location.href = appleUrl;\r\n        }\r\n      }\r\n    } catch (error) {\r\n      if (lpCookie === \"aihub\") {\r\n        window.top.location.href = appleUrl;\r\n      } else {\r\n        window.location.href = appleUrl;\r\n      }\r\n    }\r\n  }\r\n  function registerApple_0() {\r\n    try {\r\n      if (lpCookie === \"aihub\") {\r\n        // Break out of iframe for AI Hub\r\n        const newWindow = window.top.open(\r\n          appleUrl_0,\r\n          \"AppleRegister\",\r\n          windowFeatures\r\n        );\r\n        if (!newWindow) {\r\n          window.top.location.href = appleUrl_0;\r\n        }\r\n      } else {\r\n        // Regular behavior for non-AI Hub\r\n        const newWindow = window.open(\r\n          appleUrl_0,\r\n          \"AppleRegister\",\r\n          windowFeatures\r\n        );\r\n        if (!newWindow) {\r\n          window.location.href = appleUrl_0;\r\n        }\r\n      }\r\n    } catch (error) {\r\n      if (lpCookie === \"aihub\") {\r\n        window.top.location.href = appleUrl_0;\r\n      } else {\r\n        window.location.href = appleUrl_0;\r\n      }\r\n    }\r\n  }\r\n\r\n  function registerGoogle() {\r\n    try {\r\n      if (lpCookie === \"aihub\") {\r\n        // Break out of iframe for AI Hub\r\n        const newWindow = window.top.open(\r\n          googleUrl,\r\n          \"GoogleRegister\",\r\n          windowFeatures\r\n        );\r\n        if (!newWindow) {\r\n          console.log(\"Error opening new GoogleRegister\");\r\n          window.top.location.href = googleUrl;\r\n        }\r\n      } else {\r\n        // Regular behavior for non-AI Hub\r\n        const newWindow = window.open(\r\n          googleUrl,\r\n          \"GoogleRegister\",\r\n          windowFeatures\r\n        );\r\n        if (!newWindow) {\r\n          console.log(\"Error opening new GoogleRegister\");\r\n          window.location.href = googleUrl;\r\n        }\r\n      }\r\n    } catch {\r\n      console.log(\"Error opening new window\");\r\n      if (lpCookie === \"aihub\") {\r\n        window.top.location.href = googleUrl;\r\n      } else {\r\n        window.location.href = googleUrl;\r\n      }\r\n    }\r\n  }\r\n\r\n  useEffect(() => {\r\n    let isMounted = true;\r\n\r\n    const getGoogleUrl = async () => {\r\n      try {\r\n        const response = await axios.post(\r\n          `${process.env.REACT_APP_API_URL}/register-google`\r\n        );\r\n        if (isMounted && response.data.success && response.data.url) {\r\n          setGoogleUrl(response.data.url);\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error fetching Google register URL:\", error);\r\n      }\r\n    };\r\n\r\n    const getAppleUrl_0 = async () => {\r\n      try {\r\n        const response = await axios.post(\r\n          `${process.env.REACT_APP_API_URL}/register-apple`,\r\n          {\r\n            reg_num: \"0\",\r\n          },\r\n          { headers: { \"content-type\": \"application/x-www-form-urlencoded\" } }\r\n        );\r\n        if (response.data.success && response.data.url) {\r\n          setAppleUrl_0(response.data.url);\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error fetching Apple register URL:\", error);\r\n      }\r\n    };\r\n\r\n    const getAppleUrl = async () => {\r\n      try {\r\n        const response = await axios.post(\r\n          `${process.env.REACT_APP_API_URL}/register-apple`,\r\n          {\r\n            reg_num: \"\",\r\n          },\r\n          { headers: { \"content-type\": \"application/x-www-form-urlencoded\" } }\r\n        );\r\n        if (response.data.success && response.data.url) {\r\n          setAppleUrl(response.data.url);\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error fetching Apple register URL:\", error);\r\n      }\r\n    };\r\n\r\n    getGoogleUrl();\r\n    getAppleUrl();\r\n    getAppleUrl_0();\r\n\r\n    return () => {\r\n      isMounted = false;\r\n    };\r\n  }, []);\r\n\r\n  return (\r\n    <>\r\n      {lpCookie === \"aihub\" ? (\r\n        // AI Hub specific design\r\n        <>\r\n          <div className=\"flex flex-col items-center w-full gap-2\">\r\n            {reg_apple === \"on\" && (\r\n              <div className=\"inline-flex w-full\">\r\n                <button\r\n                  type=\"button\"\r\n                  className=\"w-full py-3 border rounded-lg bg-[#EDEDED] text-black\"\r\n                  disabled={!appleUrl}\r\n                  tabIndex={0}\r\n                  onClick={() => {\r\n                    appleUrl_0 && registerApple_0();\r\n                  }}\r\n                  onKeyDown={(e) => {\r\n                    if (e.key === 'Enter' && appleUrl_0) {\r\n                      registerApple_0();\r\n                    }\r\n                  }}\r\n                  aria-label=\"apple register\"\r\n                >\r\n                  <div className=\"inline-flex align-middle mr-[5px]\">\r\n                    <LazyImage\r\n                      src={imgAppleSrc}\r\n                      alt=\"AppleLogin\"\r\n                      className=\"w-[16px] max-h-[20px] mb-1\"\r\n                      width=\"20\"\r\n                      height=\"20\"\r\n                    />\r\n                  </div>\r\n                  <div className=\"inline-flex font-semibold\">\r\n                    Continue with Apple\r\n                  </div>\r\n                </button>\r\n              </div>\r\n            )}\r\n\r\n\r\n            {reg_google === \"on\" && (\r\n              <div className=\"inline-flex w-full\">\r\n                <button\r\n                  type=\"button\"\r\n                  className=\"w-full py-3 border rounded-lg bg-[#EDEDED] text-black\"\r\n                  disabled={!googleUrl}\r\n                  tabIndex={0}\r\n                  onClick={() => googleUrl && registerGoogle()}\r\n                  onKeyDown={(e) => {\r\n                    if (e.key === 'Enter' && googleUrl) {\r\n                      registerGoogle();\r\n                    }\r\n                  }}\r\n                  aria-label=\"google register\"\r\n                >\r\n                  <div className=\"inline-flex align-middle mr-[5px]\">\r\n                    <LazyImage\r\n                      src={img2Src}\r\n                      alt=\"GoogleLogin\"\r\n                      className=\"w-[20px] max-h-[20px] mb-1\"\r\n                      width=\"20\"\r\n                      height=\"20\"\r\n                    />\r\n                  </div>\r\n                  <div className=\"inline-flex font-semibold\">\r\n                    Continue with Google\r\n                  </div>\r\n                </button>\r\n              </div>\r\n            )}\r\n\r\n          </div>\r\n          {(reg_apple === \"02\" || reg_google === \"02\") && (\r\n            <div className=\"mb-[10px] mt-[10px]\">\r\n              <span className=\"text-base text-white/70\">\r\n                Do you have an account?{\" \"}\r\n              </span>\r\n              <button\r\n                type=\"button\"\r\n                className=\"font-bold cursor-pointer\"\r\n                onClick={() => handleRedirect(\"login\")}\r\n                aria-label=\"apple login\"\r\n              >\r\n                <div className=\"inline-flex font-semibold text-white\">Login</div>\r\n              </button>\r\n            </div>\r\n          )}\r\n          {/* <div class=\"flex items-center justify-center space-x-4 mb-4\">\r\n            <span class=\"text-sm\">\r\n              Already have an account? <span class=\"text-[#3073D5] hover:text-blue-600 cursor-pointer\" tabIndex={0} onClick={() => window.location.href = '/login'}>Log in!</span>\r\n            </span>\r\n          </div> */}\r\n\r\n        </>\r\n      ) : (\r\n        // Original design\r\n        <>\r\n          <div className=\"flex flex-col items-center w-full gap-2\">\r\n            {reg_google === \"on\" && (\r\n              <div className=\"inline-flex w-full\">\r\n                <button\r\n                  type=\"button\"\r\n                  className={`w-full py-3 border rounded-lg ${isAiHub ? 'bg-white/10 text-white border-white/30 hover:bg-white/20 backdrop-blur-sm' : 'bg-white border-slate-300'}`}\r\n                  disabled={!googleUrl}\r\n                  onClick={() => googleUrl && registerGoogle()}\r\n                  onKeyDown={(e) => {\r\n                    if (e.key === 'Enter' && googleUrl) {\r\n                      registerGoogle();\r\n                    }\r\n                  }}\r\n                  aria-label=\"google register\"\r\n                >\r\n                  <div className=\"inline-flex align-middle mr-[5px]\">\r\n                    <LazyImage\r\n                      src={img1Src}\r\n                      alt=\"GoogleLogin\"\r\n                      className=\"w-[17px] max-h-[17px] mb-1\"\r\n                      width=\"20\"\r\n                      height=\"20\"\r\n                    />\r\n                  </div>\r\n                  <div className=\"inline-flex\">\r\n                    {t(\"echo.register.buttons.registerGoogleText\")}\r\n                  </div>\r\n                </button>\r\n              </div>\r\n            )}\r\n\r\n            {regGoogleShown && (\r\n              <div className=\"inline-flex w-full\">\r\n                <button\r\n                  type=\"button\"\r\n                  className={`w-full py-3 border rounded-lg ${isAiHub ? 'bg-white/10 text-white border-white/30 hover:bg-white/20 backdrop-blur-sm' : 'bg-gray-300 border-slate-300'}`}\r\n                  disabled={!googleUrl}\r\n                  onClick={() => googleUrl && registerGoogle()}\r\n                  onKeyDown={(e) => {\r\n                    if (e.key === 'Enter' && googleUrl) {\r\n                      registerGoogle();\r\n                    }\r\n                  }}\r\n                  aria-label=\"google register\"\r\n                >\r\n                  <div className=\"inline-flex align-middle mr-[5px]\">\r\n                    <LazyImage\r\n                      src={img2Src}\r\n                      alt=\"GoogleLogin\"\r\n                      className=\"w-[20px] max-h-[20px] mb-1\"\r\n                      width=\"20\"\r\n                      height=\"20\"\r\n                    />\r\n                  </div>\r\n                  <div className=\"inline-flex font-semibold\">\r\n                    Continue with Google\r\n                  </div>\r\n                </button>\r\n              </div>\r\n            )}\r\n\r\n            {reg_apple === \"on\" && (\r\n              <div className=\"inline-flex w-full\">\r\n                <button\r\n                  type=\"button\"\r\n                  className={`w-full py-3 border rounded-lg ${isAiHub ? 'bg-white/10 text-white border-white/30 hover:bg-white/20 backdrop-blur-sm' : 'bg-white border-slate-300'}`}\r\n                  disabled={!appleUrl}\r\n                  onClick={() => appleUrl && registerApple()}\r\n                  onKeyDown={(e) => {\r\n                    if (e.key === 'Enter' && appleUrl) {\r\n                      registerApple();\r\n                    }\r\n                  }}\r\n                  aria-label=\"apple register\"\r\n                >\r\n                  <div className=\"inline-flex align-middle mr-[5px]\">\r\n                    <LazyImage\r\n                      src={imgAppleSrc}\r\n                      alt=\"AppleLogin\"\r\n                      className=\"w-[16px] max-h-[20px] mb-1\"\r\n                      width=\"20\"\r\n                      height=\"20\"\r\n                    />\r\n                  </div>\r\n                  <div className=\"inline-flex\">\r\n                    {t(\"echo.register.buttons.registerAppleText\")}\r\n                  </div>\r\n                </button>\r\n              </div>\r\n            )}\r\n\r\n            {regAppleShown && (\r\n              <div className=\"inline-flex w-full\">\r\n                <button\r\n                  type=\"button\"\r\n                  className={`w-full py-3 border rounded-lg ${isAiHub ? 'bg-white/10 text-white border-white/30 hover:bg-white/20 backdrop-blur-sm' : 'bg-gray-300 border-slate-300'}`}\r\n                  disabled={!appleUrl}\r\n                  onClick={() => {\r\n                    appleUrl_0 && registerApple_0();\r\n                  }}\r\n                  onKeyDown={(e) => {\r\n                    if (e.key === 'Enter' && appleUrl_0) {\r\n                      registerApple_0();\r\n                    }\r\n                  }}\r\n                  aria-label=\"apple register\"\r\n                >\r\n                  <div className=\"inline-flex align-middle mr-[5px]\">\r\n                    <LazyImage\r\n                      src={imgAppleSrc}\r\n                      alt=\"AppleLogin\"\r\n                      className=\"w-[16px] max-h-[20px] mb-1\"\r\n                      width=\"20\"\r\n                      height=\"20\"\r\n                    />\r\n                  </div>\r\n                  <div className=\"inline-flex font-semibold\">\r\n                    Continue with Apple\r\n                  </div>\r\n                </button>\r\n              </div>\r\n            )}\r\n          </div>\r\n          {(reg_apple === \"02\" || reg_google === \"02\") && (\r\n            <div className=\"mb-[10px] mt-[10px]\">\r\n              <span className={`text-base ${isAiHub ? 'text-white/70' : 'text-gray-500'}`}>\r\n                Do you have an account?{\" \"}\r\n              </span>\r\n              <button\r\n                type=\"button\"\r\n                className=\"font-bold cursor-pointer\"\r\n                onClick={() => handleRedirect(\"login\")}\r\n                aria-label=\"apple login\"\r\n              >\r\n                <div className={`inline-flex font-semibold ${isAiHub ? 'text-white' : ''}`}>Login</div>\r\n              </button>\r\n            </div>\r\n          )}\r\n        </>\r\n      )}\r\n    </>\r\n  );\r\n};\r\n\r\nexport default RegisterButton;\r\n"], "names": ["_ref", "src", "alt", "props", "_jsx", "Suspense", "fallback", "children", "isWebpSupported", "elem", "document", "createElement", "getContext", "toDataURL", "indexOf", "hexHash", "hex", "length", "test", "slice", "hoverDarken", "r", "parseInt", "g", "b", "Math", "max", "min", "toHex", "value", "round", "toString", "padStart", "useDeviceSize", "isMobile", "setIsMobile_", "useState", "isTablet", "setIsTablet", "isDesktop", "setIsDesktop", "useEffect", "handleResize", "width", "window", "innerWidth", "addEventListener", "removeEventListener", "handleRedirect", "regAppleShown", "regGoogleShown", "reg_google", "reg_apple", "isAiHub", "t", "useTranslation", "lpC<PERSON>ie", "top", "self", "lpMatch", "cookie", "match", "Get<PERSON><PERSON><PERSON>", "error", "img1Src", "img1webp", "img1", "img2Src", "img2webp", "img2", "imgAppleSrc", "imgApplewebp", "imgApple", "googleUrl", "setGoogleUrl", "appleUrl", "setAppleUrl", "appleUrl_0", "setAppleUrl_0", "screenWidth", "screen", "windowFeatures", "height", "registerApple", "open", "location", "href", "registerApple_0", "registerGoogle", "console", "log", "isMounted", "async", "response", "axios", "post", "data", "success", "url", "getGoogleUrl", "reg_num", "headers", "getAppleUrl", "getAppleUrl_0", "_Fragment", "_jsxs", "className", "type", "disabled", "tabIndex", "onClick", "onKeyDown", "e", "key", "LazyImage"], "sourceRoot": ""}