"use strict";(self.webpackChunkv1=self.webpackChunkv1||[]).push([[4711,9044],{50742:(e,t,s)=>{s.r(t),s.d(t,{default:()=>i});var n=s(72791),a=(s(46904),s(96347)),c=s(10728),l=s(80184);const i=function(){const[e]=(0,n.useState)(window.user_data),[t,s]=(0,n.useState)("");return(0,n.useEffect)(()=>{e.email||(window.location.href="/login")},[e]),(0,n.useEffect)(()=>{s("/my-account")},[]),(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(c.default,{auth:e}),(0,l.jsx)("div",{className:"Thankyou bg-gray-100 mt-[100px] md:mt-[50px] md:min-h-[85vh] flex justify-center items-center",children:(0,l.jsx)("div",{className:"container mx-auto py-10 px-4 sm:px-0",children:(0,l.jsxs)("div",{className:"reg_col text-center mb-8",children:[(0,l.jsxs)("h1",{className:"text-2xl lg:text-3xl font-bold text-center mb-6 lg:mb-8",children:[(0,l.jsx)("span",{className:"text-gradient font-black",children:"Thank you"})," for subscribing."]}),(0,l.jsx)("div",{className:"overflow-hidden",children:(0,l.jsx)("div",{className:"px-6 py-10",children:(0,l.jsxs)("div",{className:"relative block",children:["You now have access to our entire library of content, dedicated to AI learning.",(0,l.jsx)("br",{}),t?"Click below to continue to your main account page.":"Click below to continue."]})})}),(0,l.jsx)(a.E.a,{className:"bg-blue-500 text-white font-bold py-3 px-6 rounded-lg",whileHover:{scale:1.1,backgroundColor:"#5997fd"},whileTap:{scale:.9},href:t,children:"Continue"})]})})})]})}},46904:()=>{}}]);
//# sourceMappingURL=4711.0ed5c2ef.chunk.js.map