{"version": 3, "file": "static/js/3513.904c9560.chunk.js", "mappings": "+NACO,SAASA,EAAYC,GACxB,OAAIA,EAC0B,QAA3BA,EAASC,cAAgC,IACd,QAA3BD,EAASC,cAAgC,IACd,QAA3BD,EAASC,cAAgC,IACd,QAA3BD,EAASC,eACkB,QAA3BD,EAASC,cADgC,KAEd,QAA3BD,EAASC,cAAgC,KACrC,GAPc,EAQzB,CAEO,SAASC,EAAWF,GACvB,OAAIA,EAC0B,QAA3BA,EAASC,eACkB,QAA3BD,EAASC,cADgC,KAEd,QAA3BD,EAASC,cAAgC,KACd,QAA3BD,EAASC,cAAgC,KACd,QAA3BD,EAASC,cAAgC,KACd,QAA3BD,EAASC,cAAgC,KACd,QAA3BD,EAASC,cAAgC,KACrC,KARc,EASzB,CAEO,SAASE,EAAWC,GACvB,IAAIA,EAAS,MAAO,GACpB,MACMC,EAAO,IAAIC,KAAKF,EAAQG,QAAQ,KAAK,MAE3C,MAHc,CAAC,UAAU,WAAW,QAAQ,QAAQ,MAAM,OAAO,OAAO,SAAS,YAAY,UAAU,WAAW,YAGrGF,EAAKG,YAAc,IAAMH,EAAKI,UAAY,IAAMJ,EAAKK,aACtE,CAEO,SAASC,EAASC,GACrB,OAAGA,EAAKC,YAAoBD,EAAKC,YAC9BD,EAAKE,MAAcF,EAAKE,MACpB,GACX,CAEO,SAASC,EAAiBC,GAC7B,OAAOA,EAAEC,WAAWV,QAAQ,wBAAyB,IACzD,CAEO,SAASW,EAAaC,GAC3B,MAAMC,EAAcC,WAAWF,GAC/B,OACMJ,EADEK,EAAc,GAAM,EACLA,EAAYE,QAAQ,GACpBF,EAAYE,QAAQ,GAC7C,CAEO,SAASC,EAAavB,EAAUc,GACnC,OAAId,GAAac,EAEdU,MAAMV,GAAe,GAErBO,WAAWP,IAAU,EACU,QAA3Bd,EAASC,cACDiB,EAAaJ,GAAOW,eAAe,SAAW,OACpB,QAA3BzB,EAASC,cACRiB,EAAaJ,GAAOW,eAAe,SAAW,MACpB,QAA3BzB,EAASC,cACRiB,EAAaJ,GAAOW,eAAe,SAAW,MACpB,QAA3BzB,EAASC,cACRiB,EAAaJ,GAAOW,eAAe,SAAW,MACpB,QAA3BzB,EAASC,cACRiB,EAAaJ,GAAOW,eAAe,SAAW,KACrB,QAA3BzB,EAASC,cACPiB,EAAaJ,GAAOW,eAAe,SAAW,MACpB,QAA3BzB,EAASC,cACRiB,EAAaJ,GAAOW,eAAe,SAAW,KACpB,QAA3BzB,EAASC,cACRiB,EAAaJ,GAAOW,eAAe,SAAW,MACpB,QAA3BzB,EAASC,cACRiB,EAAaJ,GAAOW,eAAe,SAAW,KACpB,QAA3BzB,EAASC,cACRiB,EAAaJ,GAAOW,eAAe,SAAW,IAElD1B,EAAYC,GAAYkB,EAAaJ,GAAOW,eAAe,SAG/D,IAAM1B,EAAYC,KAAoC,EAAvBkB,EAAaJ,IAAaW,eAAe,SA7BhD,EA8BnC,CAEO,SAASC,EAAQC,EAAKC,GAEzBD,EAAM,IAAIrB,KAAKqB,GAEf,IAAIE,IADJD,EAAM,IAAItB,KAAKsB,IACAE,UAAYH,EAAIG,WAAa,IAE5C,OADAD,GAAQ,GACDE,KAAKC,IAAID,KAAKE,MAAMJ,GAC/B,CAEO,SAASK,EAAe9B,GAC3B,IAAIA,EAAS,MAAO,GACpB,MACMC,EAAO,IAAIC,KAAKF,EAAQG,QAAQ,KAAK,MAE3C,MAHc,CAAC,UAAU,WAAW,QAAQ,QAAQ,MAAM,OAAO,OAAO,SAAS,YAAY,UAAU,WAAW,YAGrGF,EAAKG,YAAc,IAAMH,EAAKI,UAAY,IAAMJ,EAAKK,cAAgB,IAAML,EAAK8B,WAAa,IAAM9B,EAAK+B,YACzH,CAEO,SAASC,EAAUC,GAAU,IAAT,KAACC,GAAKD,EAC3BE,EAAa,GACbC,EAAW,GAgBf,MAf8B,WAA1BF,EAAKG,mBACPF,EAAajB,EAAagB,EAAKvC,SAAUqB,WAAWkB,EAAKzB,MAAQ,KAAKQ,QAAQ,IAC9EmB,GAAWE,EAAAA,EAAAA,MAAA,OAAKC,MAAM,eAAcC,SAAA,CAAC,UAAQtB,EAAagB,EAAKvC,SAAUuC,EAAKzB,OAAO,YAGzD,YAA1ByB,EAAKG,mBACPF,EAAajB,EAAagB,EAAKvC,SAAUqB,WAAWkB,EAAKzB,MAAQ,IAAIQ,QAAQ,IAC7EmB,GAAWE,EAAAA,EAAAA,MAAA,OAAKC,MAAM,eAAcC,SAAA,CAAC,UAAQtB,EAAagB,EAAKvC,SAAUuC,EAAKzB,OAAO,aAGnFyB,EAAK1B,cACP2B,EAAajB,EAAagB,EAAKvC,SAAUqB,WAAWkB,EAAK1B,YAAc0B,EAAKO,YAAYxB,QAAQ,IAChGmB,GAAWE,EAAAA,EAAAA,MAAA,OAAKC,MAAM,eAAcC,SAAA,CAAC,UAAQtB,EAAagB,EAAKvC,SAAUuC,EAAK1B,kBAI9E8B,EAAAA,EAAAA,MAAAI,EAAAA,SAAA,CAAAF,SAAA,EACEF,EAAAA,EAAAA,MAAA,KAAGK,UAAS,oCAAAC,OAAiE,MAA1BC,EAAAA,EAAAA,IAAU,YAAsB,OAAS,IAAKL,SAAA,CAC9FL,EAAW,KAACW,EAAAA,EAAAA,KAAA,QAAMH,UAAU,UAASH,SAAC,gBAExCJ,IAGP,CAEO,SAASW,EAAcC,GAAU,IAAT,KAACd,GAAKc,EACnC,MAA2B,QAAvBH,EAAAA,EAAAA,IAAU,SACLb,EAAW,CAACE,SAEjBA,EAAK1B,aACCsC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,wCAAuCH,SAAEtB,EAAagB,EAAKvC,SAAUuC,EAAK1B,gBAG/F8B,EAAAA,EAAAA,MAAA,KAAGK,UAAU,wCAAuCH,SAAA,CACjDtB,EAAagB,EAAKvC,SAAUuC,EAAKzB,QAClC6B,EAAAA,EAAAA,MAAA,QAAMK,UAAU,UAASH,SAAA,CAAC,IACK,YAA1BN,EAAKG,iBAAiC,QAAU,YAI3D,CAEO,SAASY,EAAqBf,GAAO,IAADgB,EAAAC,EACzC,MAAMC,GAA+B,QAArBF,GAACL,EAAAA,EAAAA,IAAU,kBAAU,IAAAK,EAAAA,EAAI,MAAMtD,cACzCyD,GAA2B,QAAnBF,GAACN,EAAAA,EAAAA,IAAU,gBAAQ,IAAAM,EAAAA,EAAI,OAAOvD,eACtC,WAAE6C,EAAU,iBAAEJ,EAAgB,YAAE7B,EAAW,SAAEb,EAAQ,gBAAE2D,GAAoBpB,EACjF,IAAMqB,aAAcC,EAAmB,MAAE/C,GAAUyB,EAEnD,GAAIO,EAAa,GAAKjC,EAAc,GAAiB,OAAZ4C,EAAkB,CAEzD,IAAIK,EAAShD,EACTiD,EAAa,QAEH,OAAVL,IACFI,EAASzC,WAAWP,GALe,WAArB4B,EAAgC,IAAM,KAKfpB,QAAQ,GAC7CyC,EAAU,kBAAAd,OAAqBU,EAAkB7C,EAAK,KAAAmC,OAAIP,EAAgB,MAG5EmB,GAAmB,QAAAZ,OAAYH,EAAU,0BAAAG,OAAyB1B,EAAavB,EAAU8D,GAAO,SAAAb,OAAQc,EAAU,SACpH,CAEA,OAAOF,CACT,C,6QC7JO,SAASG,EAAkB1B,GAAyG,IAAvG,uBAAC2B,EAAsB,0BAAEC,EAAyB,mBAAEC,EAAkB,aAAEC,EAAY,cAAEC,GAAc/B,EACtI,MAAOgC,EAAcC,IAAmBC,EAAAA,EAAAA,UAAS,IAC1CC,EAAeC,IAAoBF,EAAAA,EAAAA,WAAS,IAEnDG,EAAAA,EAAAA,YAAU,KACRC,IAAAA,QAAiB,CACfC,cAAe,sBAEhB,KAEHF,EAAAA,EAAAA,YAAU,KAEND,EADEJ,GAAc,KAKjB,CAACA,IAGJ,MAQMQ,EAA0BA,KAM9BZ,GAA0B,IA8C5B,YAP6Ba,IAAzBd,IAAiE,IAA3BA,GArDXe,MAC7B,IAAIC,EAAQC,SAASC,eAAe,sBACxB,OAARF,IACFA,EAAMG,MAAMC,QAAU,QACtBnB,GAA0B,KAkD5Bc,QAE2BD,IAAzBd,IAAiE,IAA3BA,GACxCa,KAIA3B,EAAAA,EAAAA,KAAAJ,EAAAA,SAAA,CAAAF,UAEJM,EAAAA,EAAAA,KAACmC,EAAAA,EAAU,CAACC,QAAM,EAACC,KAAMvB,EAAwBwB,GAAIC,EAAAA,SAAS7C,UACtDF,EAAAA,EAAAA,MAACgD,EAAAA,EAAM,CAACF,GAAG,MAAMzC,UAAU,gBAAgB4C,QAASA,IAAKd,IAA0BjC,SAAA,EACjFM,EAAAA,EAAAA,KAACmC,EAAAA,EAAWO,MAAK,CACfJ,GAAIC,EAAAA,SACJI,MAAM,wBACNC,UAAU,YACVC,QAAQ,cACRC,MAAM,uBACNC,UAAU,cACVC,QAAQ,YAAWtD,UAEnBM,EAAAA,EAAAA,KAAA,OAAKH,UAAU,4CAGjBG,EAAAA,EAAAA,KAAA,OAAKH,UAAU,gCAA+BH,UAC5CM,EAAAA,EAAAA,KAAA,OAAKH,UAAU,8DAA6DH,UAC1EM,EAAAA,EAAAA,KAACmC,EAAAA,EAAWO,MAAK,CACfJ,GAAIC,EAAAA,SACJI,MAAM,wBACNC,UAAU,qBACVC,QAAQ,wBACRC,MAAM,uBACNC,UAAU,wBACVC,QAAQ,qBAAoBtD,UAE5BF,EAAAA,EAAAA,MAACgD,EAAAA,EAAOS,MAAK,CAACpD,UAAU,qHAAoHH,SAAA,EAC1IM,EAAAA,EAAAA,KAACwC,EAAAA,EAAOU,MAAK,CACXZ,GAAG,KACHzC,UAAU,8CAA6CH,SACxD,wBAGDF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,OAAMH,SAAA,EACnBM,EAAAA,EAAAA,KAAA,OAAKH,UAAU,wBAAuBH,SAAC,gHAGvCF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,cAAaH,SAAA,EAC1BM,EAAAA,EAAAA,KAAA,SAAOH,UAAU,8DAA8DsD,KAAK,SAASC,MAAM,IAAIC,QAASA,KA9E9HlC,EAAa,GACfC,EAAgBD,EAAa,OA8EbnB,EAAAA,EAAAA,KAAA,QAAMH,UAAU,mCAAkCH,SAAEyB,KACpDnB,EAAAA,EAAAA,KAAA,SAAOH,UAAU,8DAA8DsD,KAAK,SAASC,MAAM,IAAIC,QAASA,KApFlIjC,EAAgBD,EAAa,IAoFuHmC,SAAUhC,WAIhJ9B,EAAAA,EAAAA,MAAA,OAAKK,UAAU,kBAAiBH,SAAA,EAC9BM,EAAAA,EAAAA,KAAA,UACEmD,KAAK,SACLtD,UAAU,8IACVwD,QAlFAE,KAClB5B,IACAI,SAASyB,cAAc,qBAAqBC,UAAUC,IAAI,UAE1DC,EAAAA,EAAMC,KAAK,GAAD9D,OAAI+D,4BAA6B,uBAAuB,CAChE,IAAM9D,EAAAA,EAAAA,IAAU,UAChB,gBAAmBiB,EACnB,SAAYC,EACZ,cAAkBE,EAClB,YAAgBD,GACf,CAAE4C,QAAS,CAAE,eAAgB,uCAAyCC,MAAK,SAASC,GACrF,IAAIC,EAASD,EAAIvG,KACdwG,EAAOC,SACRnC,SAASyB,cAAc,qBAAqBC,UAAUU,OAAO,UAC7D1C,IAAAA,QAAe,iDACf2C,YAAW,WACTC,OAAOC,SAASC,QAClB,GAAG,OAEHxC,SAASyB,cAAc,qBAAqBC,UAAUU,OAAO,UAC7D1C,IAAAA,MAAawC,EAAOxG,KAAK+G,KAE7B,KA4DuC9E,SACtB,aAGDM,EAAAA,EAAAA,KAAA,UACEmD,KAAK,SACLtD,UAAU,yOACVwD,QAASA,IAAK1B,IAA0BjC,SACzC,4BAYrB,C,0BCzJA,MA8TA,EA9T0BP,IAQnB,IARoB,YACvBsF,EAAW,gBACXC,EAAe,gBACfC,EAAe,UACfC,EAAS,UACTC,EAAS,YACTC,EAAW,gBACXC,GACH5F,EACG,MAAO6F,EAAWC,IAAgB5D,EAAAA,EAAAA,UAAS,KACpC6D,EAAqBC,IAA0B9D,EAAAA,EAAAA,UAAS,KACxD+D,EAAmBC,IAAwBhE,EAAAA,EAAAA,UAAS,IAE3DoD,EAAcA,EAAcA,EAAY3H,cAAgB,GACxD4H,EAAkBA,EAAkBA,EAAgB5H,cAAgB,GAEpE,MAAMwI,EAA2B,KAAhBb,EACXc,EAA0B,UAAhBd,EACVe,EAAwB,QAAhBf,EACRgB,EAA2B,WAAhBhB,EACXiB,EAA+B,eAAhBjB,EACfkB,EAA6B,aAAhBlB,EACbmB,EAAgBF,GAAgBhB,IAAoBD,EACpDoB,EAAYxB,OAAOC,SAASwB,KAAKC,SAAS,WAC1CC,EAA6C,QAA9BjG,EAAAA,EAAAA,IAAU,iBAE/ByB,EAAAA,EAAAA,YAAU,KACFqE,GACAZ,EAAa,cAElB,CAACY,KAEJrE,EAAAA,EAAAA,YAAU,KACN,MAAM,SAAEyE,EAAQ,OAAEC,GAAWpB,EAE7B,IAAIqB,EAAWC,OAAOC,YAClBD,OAAOE,QAAQL,GAAUM,QAAQC,IAC7B,MAAOC,GAASD,EAEhB,OAAQC,GACJ,IAAK,yBACL,IAAK,aACD,OAAQf,EACZ,IAAK,8BAOL,IAAK,SACD,OAAQH,EANZ,IAAK,YACD,OAAQA,IAAYS,EACxB,IAAK,aACL,IAAK,SACD,OAAQT,IAAYG,EAGxB,IAAK,cACD,OAAOF,GAASC,EACpB,IAAK,aACD,OAAOG,EACX,QACI,OAAO,OAKvBT,EAAuBgB,GACvBd,EAAqBa,KACtB,CACCX,EACAC,EACAC,EACAC,EACAE,EACAd,EACAL,EACAC,EACAsB,IAGJ,MAAMU,EAAyBC,GACpBC,SAASD,GAAOrI,eAAe,SAapCuI,EAAeJ,IACjB,IAAII,GAAc,EAElB,OAAQJ,GACJ,IAAK,SACIlB,IACDsB,GAAc,GAElB,MACJ,IAAK,8BACL,IAAK,YACL,IAAK,eACDA,GAAc,EACd,MACJ,IAAK,cACGlB,GAAcF,KACdoB,GAAc,GAElB,MACJ,QACQpB,IACAoB,GAAc,GAK1B,OAAOA,GAGLC,EAAoBA,KAEjBxB,IACAG,GACDmB,SAASjC,IAAoBiC,SAAShC,GAIxCmC,EAAeA,CAACN,EAAOO,EAAOC,MACdJ,EAAYJ,MAGlBnB,GAAYsB,SAASI,IAAUJ,SAASK,IAMlDC,EAAiBA,CAACT,EAAOO,EAAOC,KAClC,IAAIE,EAAOT,EAAsBM,GAOjC,OANkBH,EAAYJ,KAG1BU,GAAI,WAAArH,OAAe4G,EAAsBO,KAGtCE,EAAKC,QAGhB,OACI5H,EAAAA,EAAAA,MAAA,OAAKK,UAAU,SAAQH,SAAA,EACnBF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,OAAMH,SAAA,EACjBM,EAAAA,EAAAA,KAAA,OAAKH,UAAU,eAAcH,UACzBM,EAAAA,EAAAA,KAAA,UACIH,UAAS,eAAAC,OACLgH,IAAsB,iBAAmB,IAC1CpH,SA3EK2H,MACxB,IAAIF,EAAOT,EAAsB/B,GAMjC,OAJKc,GAAaG,IACduB,GAAI,WAAArH,OAAe4G,EAAsB9B,KAGtC,sBAAA9E,OAAsBqH,GAAOC,QAqEnBC,QAIP/B,IACE9F,EAAAA,EAAAA,MAAA,OAAKK,UAAU,6BAA4BH,SAAA,CAAC,8BACZmF,KAInCiC,MACG9G,EAAAA,EAAAA,KAAA,OAAKH,UAAS,mCAAAC,OAAsC4F,EAA+C,8BAAhC,+BAAgEhG,SAC1G,KAApBqF,IACKW,GAaElG,EAAAA,EAAAA,MAAAI,EAAAA,SAAA,CAAAF,SAAA,EACIM,EAAAA,EAAAA,KAAA,OAAKH,UAAU,iBAAgBH,SAAC,iEAKhCM,EAAAA,EAAAA,KAAA,KACI8F,KAAI,WAAAhG,OAAakF,EAAS,yBAC1BvF,MAAM,4EAA2EC,SAAC,wBApB1FF,EAAAA,EAAAA,MAAAI,EAAAA,SAAA,CAAAF,SAAA,EACIM,EAAAA,EAAAA,KAAA,OAAKH,UAAU,iBAAgBH,SAAC,qCAIhCM,EAAAA,EAAAA,KAAA,KACI8F,KAAK,iBACLrG,MAAM,4EAA2EC,SAAC,sBAsB9GF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,iDAAgDH,SAAA,EAC3DF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,yCAAwCH,SAAA,EACnDM,EAAAA,EAAAA,KAAA,OAAKH,UAAU,qCAAoCH,UAC/CM,EAAAA,EAAAA,KAAA,UAAQH,UAAU,sCAAqCH,SAAC,4BAK5DF,EAAAA,EAAAA,MAAA,SAAOK,UAAU,SAAQH,SAAA,EACrBM,EAAAA,EAAAA,KAAA,SAAOH,UAAU,2BAA0BH,UACvCF,EAAAA,EAAAA,MAAA,MAAAE,SAAA,EACIM,EAAAA,EAAAA,KAAA,MAAIH,UAAU,sDAAqDH,SAAC,WAGpEM,EAAAA,EAAAA,KAAA,MAAIH,UAAU,wDAAuDH,SAAC,sBAK9EM,EAAAA,EAAAA,KAAA,SAAOH,UAAU,2BAA0BH,SACtCwF,GACDkB,OAAOkB,KAAKpC,GAAqBqC,OAAS,GACtCvH,EAAAA,EAAAA,KAAAJ,EAAAA,SAAA,CAAAF,SACK0G,OAAOE,QAAQpB,GAAqBsC,KACjC,CAAAtH,EAEIuH,KAAK,IADJhB,GAAO,MAAEO,EAAK,UAAEC,IAAY/G,EAAA,OAG7BV,EAAAA,EAAAA,MAAA,MAAAE,SAAA,EACIM,EAAAA,EAAAA,KAAA,MAAIH,UAAU,4CAA2CH,SACpD+G,KAELzG,EAAAA,EAAAA,KAAA,MACIH,UAAS,iCAAAC,OACLiH,EACIN,EACAO,EACAC,GAEE,iBACA,iBACPvH,SACFwH,EACGT,EACAO,EACAC,OAjBHQ,SAyBrBzH,EAAAA,EAAAA,KAAA,MAAAN,UACIM,EAAAA,EAAAA,KAAA,MACI0H,QAAQ,IACR7H,UAAU,0DAAyDH,SAAC,kCAS5FF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,yCAAwCH,SAAA,EACnDM,EAAAA,EAAAA,KAAA,OAAKH,UAAU,qCAAoCH,UAC/CM,EAAAA,EAAAA,KAAA,UAAQH,UAAU,sCAAqCH,SAAC,0BAK5DF,EAAAA,EAAAA,MAAA,SAAOK,UAAU,SAAQH,SAAA,EACrBM,EAAAA,EAAAA,KAAA,SAAOH,UAAU,2BAA0BH,UACvCF,EAAAA,EAAAA,MAAA,MAAAE,SAAA,EACIM,EAAAA,EAAAA,KAAA,MAAIH,UAAU,sDAAqDH,SAAC,SAGpEM,EAAAA,EAAAA,KAAA,MAAIH,UAAU,wDAAuDH,SAAC,sBAK9EM,EAAAA,EAAAA,KAAA,SAAOH,UAAU,2BAA0BH,SACtC0F,GACDgB,OAAOkB,KAAKlC,GAAmBmC,OAAS,GACpCvH,EAAAA,EAAAA,KAAAJ,EAAAA,SAAA,CAAAF,SACK0G,OAAOE,QAAQlB,GAAmBoC,KAC/B,CAAAG,EAAqBF,KAAK,IAAxBG,EAAKC,GAAYF,EAAA,OACfnI,EAAAA,EAAAA,MAAA,MAAAE,SAAA,EACIM,EAAAA,EAAAA,KAAA,MAAIH,UAAU,4CAA2CH,SACpDkI,KAEL5H,EAAAA,EAAAA,KAAA,MAAIH,UAAU,8CAA6CH,SACtDgH,EACGmB,OANHJ,SAcrBzH,EAAAA,EAAAA,KAAA,MAAAN,UACIM,EAAAA,EAAAA,KAAA,MACI0H,QAAQ,IACR7H,UAAU,0DAAyDH,SAAC,wCC7R5G,IAAIoI,EAAkB,EAClB1I,EAAO,KAEX2I,eAAeC,IACb,GAAG5I,EAAM,OAAOA,EAChB,MAKM6E,SALiBN,EAAAA,EAAMC,KAAK,GAAD9D,OAC5B+D,4BAA6B,qBAChC,CAAEoE,IAAIlI,EAAAA,EAAAA,IAAU,WAChB,CAAE+D,QAAS,CAAE,eAAgB,wCAEPrG,KACxB,OAAGwG,EAAOC,SACR9E,EAAO6E,EAAOxG,KACPwG,EAAOxG,MAEP,EAEX,CAEA,MAAMyK,EAAY7D,OAAO6D,UACnBC,EAAcD,EAAUE,MAAQF,EAAUE,MAAMC,QAAU,GAC1DC,EAAYJ,EAAUE,MAAQF,EAAUE,MAAMzK,MAAQ,GACtD4K,EAAeL,EAAUE,MAAQF,EAAUE,MAAMvL,SAAW,GAC5D2L,EAAaN,EAAUE,MAAQF,EAAUE,MAAMK,OAAS,GAExDC,EAAiBR,EAAUE,MAAQF,EAAUE,MAAMO,WAAa,GAEhEC,EAAazJ,IAA6C,IAA5C,KAAEkD,EAAI,QAAEI,EAAO,SAAEoG,EAAQ,UAAEC,GAAW3J,EACxD,MAAM,KAAE1B,IAASsL,EAAAA,EAAAA,UAAS,QAASf,IAC5BgB,EAAiBC,IAAsB5H,EAAAA,EAAAA,UAAS,SAWvD,OATAG,EAAAA,EAAAA,YAAU,KACR,IAAI/D,aAAI,EAAJA,EAAM8J,QAAS,EAAG,CACpB,MAAM2B,EAAYzL,EAAK0L,MAAKC,GAAuB,YAAhBA,aAAG,EAAHA,EAAKC,UACpCH,SAAAA,EAAW3J,kBACb0J,EAAgE,WAA7CC,EAAU3J,iBAAiB+J,cAA6B,OAAS,QAExF,IACC,CAAC7L,IAEC4E,GAGH7C,EAAAA,EAAAA,MAAAI,EAAAA,SAAA,CAAAF,SAAA,EACEM,EAAAA,EAAAA,KAAA,OACEH,UAAU,oFACVwD,QAASZ,KAEXjD,EAAAA,EAAAA,MAAA,OAAKK,UAAU,2KAA0KH,SAAA,EAEvLM,EAAAA,EAAAA,KAAA,UACEH,UAAU,2GACVwD,QAASZ,EACT,aAAW,QAAO/C,SACnB,OAGDF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,yBAAwBH,SAAA,EACrCM,EAAAA,EAAAA,KAAA,OACEuJ,IAAKC,EAAAA,EACLC,IAAI,cACJ5J,UAAU,2DAEZL,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,gEAA+DH,SAAA,CAC3EgJ,EAAe,oBAElBlJ,EAAAA,EAAAA,MAAA,OAAKK,UAAU,mCAAkCH,SAAA,CAAC,yBACzBgJ,EAAe,uBAI1C1I,EAAAA,EAAAA,KAAA,OAAKH,UAAU,sKAAqKH,UAClLF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,0HAAyHH,SAAA,CACrI6I,EAAa,KAACvI,EAAAA,EAAAA,KAAA,QAAMH,UAAU,4CAA2CH,SAAE4I,KAC5E9I,EAAAA,EAAAA,MAAA,QAAMK,UAAU,mEAAkEH,SAAA,EAChFM,EAAAA,EAAAA,KAAA,QAAAN,SAAM,SACNM,EAAAA,EAAAA,KAAA,QAAAN,SAAOsJ,aAKbxJ,EAAAA,EAAAA,MAAA,OAAKK,UAAU,+BAA8BH,SAAA,EAC3CF,EAAAA,EAAAA,MAAA,UACEK,UAAU,6QACVwD,QAASwF,EAASnJ,SAAA,EAElBM,EAAAA,EAAAA,KAAA,QAAMH,UAAU,6BAA4BH,SAAC,uBAC7CM,EAAAA,EAAAA,KAAA,QAAMH,UAAU,qBAElBG,EAAAA,EAAAA,KAAA,UACEH,UAAU,qFACVwD,QAASyF,EAAUpJ,SACpB,8BArDO,MAkdpB,MAAMgK,EAAUxJ,IAAgD,IAA/C,QAAEyJ,EAAO,UAAEC,EAAS,QAAEvG,EAAO,SAAE3D,GAAUQ,EACxD,MAAM2J,EAAWD,IAAcD,EAE/B,OACE3J,EAAAA,EAAAA,KAAA,MACEH,UAAS,+CAAAC,OACP+J,EAAW,mCAAqC,IAElDxG,QAASA,IAAMA,EAAQsG,GAASjK,SAE/BA,KAKDoK,EAAqBC,IACzB,MAAOC,EAAUC,IAAe5I,EAAAA,EAAAA,UAAS,KAClC6I,IAAS7I,EAAAA,EAAAA,UAAS0I,EAAMI,KAAKD,QAC7BE,EAAaC,IAAkBhJ,EAAAA,EAAAA,UAAS,KACxCiJ,EAAaC,IAAkBlJ,EAAAA,EAAAA,UAAS,KACxCmJ,EAAcC,IAAmBpJ,EAAAA,EAAAA,UAAS,KAC1CqJ,IAAWrJ,EAAAA,EAAAA,UAAS0I,EAAMI,KAAKQ,WAC/BC,EAAeC,IAAoBxJ,EAAAA,EAAAA,WAAS,IAQnDG,EAAAA,EAAAA,YAAU,UACWI,IAAfmI,EAAMI,OAEoB,OAAxBJ,EAAMI,KAAKW,WAA4C,KAAvBf,EAAMI,KAAKW,UAC7CD,GAAiB,GAEjBA,GAAiB,MAGpB,CAACd,EAAMI,OA0DV,OACE3K,EAAAA,EAAAA,MAAA,OAAKK,UAAU,OAAMH,SAAA,EACnBM,EAAAA,EAAAA,KAAA,KAAGH,UAAU,eAAcH,SAAC,8CAC5BF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,oBAAmBH,SAAA,EAChCF,EAAAA,EAAAA,MAAA,SAAOK,UAAU,gCAA+BH,SAAA,CAAC,oBAAkBgL,MACnE1K,EAAAA,EAAAA,KAAA,UAAQP,MAAM,mEAAmE4D,QA9EpE0H,KAEjBC,UAAUC,UAAUC,UAAUR,GAC9BjJ,IAAAA,QAAe,wBA2E0F/B,UAACM,EAAAA,EAAAA,KAACmL,EAAAA,IAAS,UAElH3L,EAAAA,EAAAA,MAAA,OAAKK,UAAU,sBAAqBH,SAAA,EAClCF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,mDAAkDH,SAAA,EAC/DM,EAAAA,EAAAA,KAAA,OAAKH,UAAU,uDAAsDH,SAAC,0BAGtEF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,gBAAeH,SAAA,EAC5BM,EAAAA,EAAAA,KAAA,SAAOH,UAAU,2OACjBuL,YAAY,gBACZjI,KAAK,QACLkI,KAAK,QACLC,UAAQ,EACRlI,MAAO2G,EAAMI,KAAKD,SAElBlK,EAAAA,EAAAA,KAAA,SAAOH,UAAU,iNACjBuL,YAAY,YACZjI,KAAK,QACLkI,KAAK,YACLE,aAAa,YACbC,QAAUC,IACRxB,EAAYwB,EAAMC,OAAOtI,QAE3BuI,SAAWC,GAAM3B,EAAY2B,EAAEF,OAAOtI,OACtCE,SAAUsH,WAIdpL,EAAAA,EAAAA,MAAA,OAAKK,UAAU,wDAAuDH,SAAA,EACpEM,EAAAA,EAAAA,KAAA,OAAKH,UAAU,uDAAsDH,SAAC,qBAGtEF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,gBAAeH,SAAA,EAC5BM,EAAAA,EAAAA,KAAA,SAAOH,UAAU,0NACjBuL,YAAY,eACZjI,KAAK,WACLkI,KAAK,eACLE,aAAa,eACbC,QAAUC,IACRlB,EAAekB,EAAMC,OAAOtI,QAE9BE,SAAUsH,KAEV5K,EAAAA,EAAAA,KAAA,SAAOH,UAAU,0NACjBuL,YAAY,uBACZjI,KAAK,WACLkI,KAAK,gBACLG,QAAUC,IACRhB,EAAgBgB,EAAMC,OAAOtI,QAE/BE,SAAUsH,cAKhB5K,EAAAA,EAAAA,KAAC6L,EAAa,CAACC,kBA5FOA,KACxB,IAAIC,EAzBmBC,MACvB,IAAID,EAAY,GAUhB,OATI3B,EAEOE,EAAY/C,SAClB+C,IAAgBE,EACjBuB,EAAY,6BACHzB,EAAY/C,OAAS,IAC9BwE,EAAY,kDALdA,EAAY,gCAQPA,GAcSC,GACbD,EACDtK,IAAAA,MAAasK,IAGfA,EAhBoBE,MACpB,IAAIF,EAAY,GAMhB,OALI/B,EAASzC,SACN,eAAe2E,KAAKlC,KACvB+B,EAAY,yBAGTA,GASKE,GACTF,EACDtK,IAAAA,MAAasK,IAGfhK,SAASyB,cAAc,qBAAqBC,UAAUC,IAAI,UAC1DC,EAAAA,EAAMC,KAAK,GAAD9D,OAAI+D,4BAA6B,mBAAmB,CAC5DoE,IAAIlI,EAAAA,EAAAA,IAAU,UACdoM,SAAUnC,EACVoC,SAAUhC,EACViC,YAAa/B,EACbgC,aAAc9B,GACb,CAAE1G,QAAS,CAAE,eAAgB,uCAAyCC,MAAK,SAASC,GACrF,IAAIC,EAASD,EAAIvG,KACjB,GAAGwG,EAAOC,QAKR,OAJAzC,IAAAA,QAAe,mDACf2C,YAAW,WACTC,OAAOC,SAASC,QAClB,GAAG,KAGLxC,SAASyB,cAAc,qBAAqBC,UAAUU,OAAO,UAC1DF,EAAOxG,MAAMgE,IAAAA,MAAawC,EAAOxG,KAAK+G,IAC3C,OA+DuD6F,eAAgBA,KACrErK,EAAAA,EAAAA,KAAA,OAAKH,UAAU,wCAAuCH,UACpDM,EAAAA,EAAAA,KAACuM,EAAa,CAACrC,MAAOA,UAMxB2B,EAAiB9B,IACrB,MAAMM,EAAiBN,EAAMM,gBACtBmC,EAAaC,IAAkBpL,EAAAA,EAAAA,WAAS,GAKzCqL,EAAaA,KACjBD,GAAe,IAGXE,EAAuBA,KAC3B5C,EAAM+B,qBAER,OACEtM,EAAAA,EAAAA,MAAAI,EAAAA,SAAA,CAAAF,SAAA,EACEM,EAAAA,EAAAA,KAAC4M,EAAAA,EAAOC,OAAM,CACZhN,UAAU,qGACViN,SAAU,CAAEC,MAAO,IACnB1J,QAfyB2J,KAC7BP,GAAe,IAcqB/M,SACjC,kBAGDM,EAAAA,EAAAA,KAACmC,EAAAA,EAAU,CAACC,QAAM,EAACC,KAAMmK,EAAalK,GAAIC,EAAAA,SAAS7C,UACjDF,EAAAA,EAAAA,MAACgD,EAAAA,EAAM,CAACF,GAAG,MAAMzC,UAAU,gBAAgB4C,QAASiK,EAAWhN,SAAA,EAC7DM,EAAAA,EAAAA,KAACmC,EAAAA,EAAWO,MAAK,CACfJ,GAAIC,EAAAA,SACJI,MAAM,wBACNC,UAAU,YACVC,QAAQ,cACRC,MAAM,uBACNC,UAAU,cACVC,QAAQ,YAAWtD,UAEnBM,EAAAA,EAAAA,KAAA,OAAKH,UAAU,4CAGjBG,EAAAA,EAAAA,KAAA,OAAKH,UAAU,gCAA+BH,UAC5CM,EAAAA,EAAAA,KAAA,OAAKH,UAAU,8DAA6DH,UAC1EM,EAAAA,EAAAA,KAACmC,EAAAA,EAAWO,MAAK,CACfJ,GAAIC,EAAAA,SACJI,MAAM,wBACNC,UAAU,qBACVC,QAAQ,wBACRC,MAAM,uBACNC,UAAU,wBACVC,QAAQ,qBAAoBtD,UAE5BF,EAAAA,EAAAA,MAACgD,EAAAA,EAAOS,MAAK,CAACpD,UAAU,qHAAoHH,SAAA,EAC1IM,EAAAA,EAAAA,KAACwC,EAAAA,EAAOU,MAAK,CACXZ,GAAG,KACHzC,UAAU,8CAA6CH,SACxD,oBAGDF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,OAAMH,SAAA,EACnBM,EAAAA,EAAAA,KAAA,OAAKH,UAAU,wBAAuBH,SAAC,4CAGvCM,EAAAA,EAAAA,KAAA,OAAAN,UACEM,EAAAA,EAAAA,KAAA,SAAOH,UAAU,0NACjBuL,YAAY,qBACZjI,KAAK,WACLkI,KAAK,mBACLG,QAAUC,IACRpB,EAAeoB,EAAMC,OAAOtI,OACP,KAAlBqI,EAAMwB,SAAgBN,aAM/BnN,EAAAA,EAAAA,MAAA,OAAKK,UAAU,OAAMH,SAAA,EAC3BM,EAAAA,EAAAA,KAAA,UACUmD,KAAK,SACLtD,UAAU,+IACVwD,QAASsJ,EAAqBjN,SAC/B,aAGDM,EAAAA,EAAAA,KAAA,UACEmD,KAAK,SACLtD,UAAU,yOACVwD,QAASqJ,EAAWhN,SACrB,+BAcrBqI,eAAemF,IACb,MAGMjJ,SAHiBN,EAAAA,EAAMC,KAAK,GAAD9D,OAAI+D,4BAA6B,2BAA2B,CAC3F,IAAM9D,EAAAA,EAAAA,IAAU,WACf,CAAE+D,QAAS,CAAE,eAAgB,wCACRrG,KACxB,OAAGwG,EAAOC,QACDD,EAAOxG,KAEP,EAEX,CAEA,MAAM0P,EAAyBxF,IAAmK,IAAlK,qBAACyF,EAAoB,0BAAErM,EAAyB,sBAAEsM,EAAqB,gBAAEC,EAAe,iBAAEC,EAAgB,SAAEC,EAAQ,kBAAEC,EAAiB,QAAEC,EAAO,UAAEC,GAAUhG,EAC1L,MAAMM,GAAKlI,EAAAA,EAAAA,IAAU,UAErB,GAAG0N,QAA+D,OAClE,MAAMG,EAAcnC,IAClB,IAAIoC,EAAOpC,EAAMC,OAAOoC,aAAa,OACjCD,IACJ9L,SAASyB,cAAc,qBAAqBC,UAAUC,IAAI,UAC1DW,OAAOC,SAASwB,KAAO,oBAAsB+H,IAYzCE,EAAeA,KACnB1J,OAAOC,SAASwB,KAAO,cAmBnBkI,EAAe5E,GACZA,EAAI6E,SAAW7E,EAAI6E,SAASnR,cAAgB,GAG/CF,EAAewM,GACZA,EAAIvM,SAAWuM,EAAIvM,SAASC,cAAgB,GAG/CoR,EAAe9E,GACZA,EAAI+E,UAAY/E,EAAI+E,UAAUrR,cAAgB,GAGjDsR,EAAsBhF,GACnBA,EAAI7J,iBAAmB6J,EAAI7J,iBAAiBzC,cAAgB,GAGrE,OACEkD,EAAAA,EAAAA,KAAAJ,EAAAA,SAAA,CAAAF,UACAM,EAAAA,EAAAA,KAAA,OAAKH,UAAU,uEAAsEH,SAClF+N,GAAqBA,EAAkBlG,QACxC/H,EAAAA,EAAAA,MAAA,SAAOK,UAAU,yEAAwEH,SAAA,EACvFM,EAAAA,EAAAA,KAAA,SAAAN,UACEF,EAAAA,EAAAA,MAAA,MAAIK,UAAU,cAAaH,SAAA,EACzBM,EAAAA,EAAAA,KAAA,MAAIH,UAAU,qHAAoHH,SAAC,kBACnIM,EAAAA,EAAAA,KAAA,MAAIH,UAAU,qHAAoHH,SAAC,YACnIM,EAAAA,EAAAA,KAAA,MAAIH,UAAU,qHAAoHH,SAAC,kBACnIM,EAAAA,EAAAA,KAAA,MAAIH,UAAU,qHAAoHH,SAAC,eAChI+N,GAAqBA,EAAkBlG,OAAS,GAAKkG,EAAkBY,MAAKjF,GAAsB,aAAfA,EAAIC,UAA2BoE,EAAkBY,MAAKjF,GAAsB,WAAfA,EAAIC,YACrJrJ,EAAAA,EAAAA,KAAA,MAAIH,UAAU,qHAAoHH,SAAC,gBAErIM,EAAAA,EAAAA,KAAA,MAAIH,UAAU,qHAAoHH,SAAC,YAEnIM,EAAAA,EAAAA,KAAA,MAAIH,UAAU,qHAAoHH,SAAC,kBAGvIM,EAAAA,EAAAA,KAAA,SAAOH,UAAU,oCAAmCH,SACjD+N,aAAiB,EAAjBA,EAAmBjG,KAAI,CAAC4B,EAAK3B,KAC5BjI,SAAAA,EAAAA,MAAA,MAAgBK,UAAU,cAAaH,SAAA,EACvCM,EAAAA,EAAAA,KAAA,MAAIH,UAAU,yGAAwGH,UA/C1GyO,EA+CyH/E,EAAI+E,UA9C7IA,GAA8C,eAA5BA,EAAUrR,cA8C8HsM,EAAI+E,UAAU/Q,QAAQ,SAAS,YAAcgM,EAAIkF,cACvMtO,EAAAA,EAAAA,KAAA,MAAIH,UAAU,yGAAwGH,SAAE0J,EAAImF,eAC5HvO,EAAAA,EAAAA,KAAA,MAAIH,UAAU,yGAAwGH,SAAE0J,EAAIzJ,WAAa,MAAQ,QACjJK,EAAAA,EAAAA,KAAA,MAAIH,UAAU,yGAAwGH,UAAE1C,EAAAA,EAAAA,IAAWoM,EAAIoF,cAErIf,GAAqBA,EAAkBlG,OAAS,GAAKkG,EAAkBY,MAAKjF,GAAsB,aAAfA,EAAIC,SACtE,WAAfD,EAAIC,QACFrJ,EAAAA,EAAAA,KAAA,MAAIH,UAAU,qHAGdG,EAAAA,EAAAA,KAAA,MAAIH,UAAU,kHAAiHH,UAC5H1C,EAAAA,EAAAA,IAAWoM,EAAIqF,YAIH,WAAfrF,EAAIC,OACN,IACED,EAAIC,QACNrJ,EAAAA,EAAAA,KAAA,MAAIH,UAAU,gHAA+GH,UAC1H1C,EAAAA,EAAAA,IAAWoM,EAAIqF,cAQtBzO,EAAAA,EAAAA,KAAA,MAAIH,UAAU,yGAAwGH,SAAE0J,EAAIC,UAC5H7J,EAAAA,EAAAA,MAAA,MAAIK,UAAU,yGAAwGH,SAAA,CACnG,WAAf0J,EAAIC,QACNrJ,EAAAA,EAAAA,KAAA,OAAKH,UAAU,cAAaH,UAC1BF,EAAAA,EAAAA,MAACkP,EAAAA,EAAI,CAACpM,GAAG,MAAMzC,UAAU,uDAAsDH,SAAA,EAC7EM,EAAAA,EAAAA,KAAA,OAAKH,UAAU,SAAQH,UACrBM,EAAAA,EAAAA,KAAC0O,EAAAA,EAAKC,OAAM,CAAC9O,UAAU,kHAAiHH,SAAC,eAI3IM,EAAAA,EAAAA,KAACmC,EAAAA,EAAU,CACTG,GAAIC,EAAAA,SACJI,MAAM,mCACNC,UAAU,+BACVC,QAAQ,kCACRC,MAAM,iCACNC,UAAU,kCACVC,QAAQ,+BAA8BtD,UAGtCM,EAAAA,EAAAA,KAAC0O,EAAAA,EAAKE,MAAK,CAAC/O,UAAS,GAAAC,OACjB2H,EAAQ,EAAI,SAAW,SAAQ,iLAC+I/H,UAChLF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,yBAAwBH,SAAA,CAChB,YAArBsO,EAAY5E,IAA2C,WAArB4E,EAAY5E,IAC9CpJ,EAAAA,EAAAA,KAAC0O,EAAAA,EAAKG,KAAI,CAAAnP,SACPoP,IAAA,IAAC,OAAEC,GAAQD,EAAA,OACV9O,EAAAA,EAAAA,KAAA,UACEH,UAAS,GAAAC,OACPiP,EAAS,4BAA8B,gBAAe,kFAExDC,IAAK5F,EAAI4F,IACT3L,QAASuK,EAAWlO,SACrB,mBAKD,GACoB,YAArBsO,EAAY5E,IAA2C,WAArB4E,EAAY5E,IAA0C,eAArB4E,EAAY5E,IAA8C,oBAArB4E,EAAY5E,IAA8C,WAAfA,EAAIC,QAAmD,YAA5B+E,EAAmBhF,GAahM,IAZJpJ,EAAAA,EAAAA,KAAC0O,EAAAA,EAAKG,KAAI,CAAAnP,SACTuP,IAAA,IAAC,OAAEF,GAAQE,EAAA,OACVjP,EAAAA,EAAAA,KAAA,UACEH,UAAS,GAAAC,OACPiP,EAAS,4BAA8B,gBAAe,kFAExD1L,QAAUjE,IAlHlCiF,OAAOC,SAASwB,KAAO,WAkHmCpG,SACjC,eAMLM,EAAAA,EAAAA,KAAC0O,EAAAA,EAAKG,KAAI,CAAAnP,SACPwP,IAAA,IAAC,OAAEH,GAAQG,EAAA,OACVlP,EAAAA,EAAAA,KAACmP,EAAkB,CAACH,IAAK5F,EAAI4F,IAAK/G,GAAIA,EAAI0F,UAAWA,mBAO1D,GAEQ,WAAfvE,EAAIC,QACNrJ,EAAAA,EAAAA,KAAA,OAAKH,UAAU,cAAaH,UAC1BF,EAAAA,EAAAA,MAACkP,EAAAA,EAAI,CAACpM,GAAG,MAAMzC,UAAU,uDAAsDH,SAAA,EAC7EM,EAAAA,EAAAA,KAAA,OAAKH,UAAU,SAAQH,UACrBM,EAAAA,EAAAA,KAAC0O,EAAAA,EAAKC,OAAM,CAAC9O,UAAU,kHAAiHH,SAAC,eAI3IM,EAAAA,EAAAA,KAACmC,EAAAA,EAAU,CACTG,GAAIC,EAAAA,SACJI,MAAM,mCACNC,UAAU,+BACVC,QAAQ,kCACRC,MAAM,iCACNC,UAAU,kCACVC,QAAQ,+BAA8BtD,UAEtCM,EAAAA,EAAAA,KAAC0O,EAAAA,EAAKE,MAAK,CAAC/O,UAAS,GAAAC,OACjB2H,EAAQ,EAAI,SAAW,SAAQ,iLAC+I/H,UAChLF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,yBAAwBH,SAAA,CACd,YAArBsO,EAAY5E,IAA2C,WAArB4E,EAAY5E,IAChDpJ,EAAAA,EAAAA,KAAC0O,EAAAA,EAAKG,KAAI,CAAAnP,SACP0P,IAAA,IAAC,OAAEL,GAAQK,EAAA,OACVpP,EAAAA,EAAAA,KAAA,UACEH,UAAS,GAAAC,OACPiP,EAAS,4BAA8B,gBAAe,kFAExDC,IAAK5F,EAAI4F,IACT3L,QAASuK,EAAWlO,SACrB,mBAKD,IACoB,QAArB9C,EAAYwM,IAAuC,WAArB4E,EAAY5E,IAA0C,WAArB4E,EAAY5E,IAA0C,WAArB4E,EAAY5E,IAA0C,eAArB4E,EAAY5E,IAA8C,oBAArB4E,EAAY5E,MAClL7K,EAAAA,EAAAA,KAAQQ,EAAAA,EAAAA,IAAeyO,IAAWzO,EAAAA,EAAAA,IAAeqK,EAAIoF,cAAgB,GACxEhP,EAAAA,EAAAA,MAAAI,EAAAA,SAAA,CAAAF,SAAA,CACyB,eAArBwO,EAAY9E,IAAqD,WAA5BgF,EAAmBhF,IAA0C,WAArB8E,EAAY9E,IAAiC,OAAZsE,GACnG,OAAZA,GAAiD,WAA5BU,EAAmBhF,IAA0C,WAArB8E,EAAY9E,IACpD,UAArB8E,EAAY9E,IAA2C,QAArB8E,EAAY9E,IAC/CpJ,EAAAA,EAAAA,KAAC0O,EAAAA,EAAKG,KAAI,CAAAnP,SACP2P,IAAA,IAAC,OAAEN,GAAQM,EAAA,OACVrP,EAAAA,EAAAA,KAAA,UACEH,UAAS,GAAAC,OACPiP,EAAS,4BAA8B,gBAAe,kFAExD1L,QAAUjE,GA9MpBA,KAEP,eAAPA,EACFgO,GAAqB,GAErB/I,OAAOC,SAASwB,KAAO,YAyMsBwJ,CAAWpB,EAAY9E,IAAM1J,SACjD,eAKD,GAEsB,UAArBwO,EAAY9E,IAAgD,YAA5BgF,EAAmBhF,IAA4C,eAArB8E,EAAY9E,GAavF,IAZJpJ,EAAAA,EAAAA,KAAC0O,EAAAA,EAAKG,KAAI,CAAAnP,SACP6P,IAAA,IAAC,OAAER,GAAQQ,EAAA,OACVvP,EAAAA,EAAAA,KAAA,UACEH,UAAS,GAAAC,OACPiP,EAAS,4BAA8B,gBAAe,kFAExD1L,QAAS0K,EAAarO,SACvB,iBAOmB,YAArBsO,EAAY5E,IAA2C,WAArB4E,EAAY5E,IAA0C,eAArB4E,EAAY5E,IAA8C,oBAArB4E,EAAY5E,IAA8C,WAAfA,EAAIC,QAAmD,YAA5B+E,EAAmBhF,IAA2C,QAArBA,EAAIoG,cAA4C,eAAlBpG,EAAI+E,UAaxP,IAZJnO,EAAAA,EAAAA,KAAC0O,EAAAA,EAAKG,KAAI,CAAAnP,SACP+P,IAAA,IAAC,OAAEV,GAAQU,EAAA,OACVzP,EAAAA,EAAAA,KAAA,UACEH,UAAS,GAAAC,OACPiP,EAAS,4BAA8B,gBAAe,kFAExD1L,QAAUjE,GA/NtBA,KAChBiO,EAAsBjO,EAAKsQ,0BAC3BpC,EAAgBlO,EAAK6O,UACrBV,EAAiBnO,EAAK4P,KACtBjO,GAA0B,IA2NqB4O,CAASvG,GAAK1J,SAClC,iBAODM,EAAAA,EAAAA,KAAAJ,EAAAA,SAAA,KACNI,EAAAA,EAAAA,KAAC0O,EAAAA,EAAKG,KAAI,CAAAnP,SACPkQ,IAAA,IAAC,OAAEb,GAAQa,EAAA,OACV5P,EAAAA,EAAAA,KAACmP,EAAkB,CAACH,IAAK5F,EAAI4F,IAAK/G,GAAIA,EAAI0F,UAAWA,mBAO1D,QA5LFlG,GA9CG0G,eAkPhBnO,EAAAA,EAAAA,KAAA,OAAAN,UACAF,EAAAA,EAAAA,MAAA,QAAMK,UAAU,4BAA2BH,SAAA,EAACM,EAAAA,EAAAA,KAAC6P,EAAAA,IAAY,CAAChQ,UAAU,wBAAuB,gDAA4CG,EAAAA,EAAAA,KAAA,KAAG8F,KAAK,WAAWjG,UAAU,0BAAyBH,SAAC,4BAShMoQ,EAAkBC,IAAmB,IAAlB,WAACC,GAAWD,EAEnC,GAAGC,QACH,OACEhQ,EAAAA,EAAAA,KAAA,OAAKH,UAAU,mEAAkEH,SAC9EsQ,GAAcA,EAAWzI,QAC1B/H,EAAAA,EAAAA,MAAA,SAAOK,UAAU,mDAAkDH,SAAA,EACjEM,EAAAA,EAAAA,KAAA,SAAAN,UACEF,EAAAA,EAAAA,MAAA,MAAIK,UAAU,cAAaH,SAAA,EACzBM,EAAAA,EAAAA,KAAA,MAAIH,UAAU,8GAA6GH,SAAC,aAC5HM,EAAAA,EAAAA,KAAA,MAAIH,UAAU,8GAA6GH,SAAC,gBAC5HM,EAAAA,EAAAA,KAAA,MAAIH,UAAU,8GAA6GH,SAAC,YAC5HM,EAAAA,EAAAA,KAAA,MAAIH,UAAU,8GAA6GH,SAAC,UAC5HM,EAAAA,EAAAA,KAAA,MAAIH,UAAU,8GAA6GH,SAAC,iBAGhIM,EAAAA,EAAAA,KAAA,SAAOH,UAAU,oCAAmCH,SACjDsQ,aAAU,EAAVA,EAAYxI,KAAI,CAAC4B,EAAK3B,KACrBjI,EAAAA,EAAAA,MAAA,MAAgBK,UAAU,cAAaH,SAAA,EACvCM,EAAAA,EAAAA,KAAA,MAAIH,UAAU,yGAAwGH,SAAE0J,EAAI6G,aAC5HjQ,EAAAA,EAAAA,KAAA,MAAIH,UAAU,yGAAwGH,SAAE0J,EAAI8G,SAC5HlQ,EAAAA,EAAAA,KAAA,MAAIH,UAAU,yGAAwGH,SAAE0J,EAAIzI,QAASvC,EAAAA,EAAAA,IAAagL,EAAIvM,SAAUuM,EAAIzI,QAAU,MAC9KX,EAAAA,EAAAA,KAAA,MAAIH,UAAU,yGAAwGH,UAAE1C,EAAAA,EAAAA,IAAWoM,EAAI+G,eACvInQ,EAAAA,EAAAA,KAAA,MAAIH,UAAU,yGAAwGH,SAAoB,wBAAlB0J,EAAIgH,UAAsC,WAAa,gBALtK3I,WAUbjI,EAAAA,EAAAA,MAAA,QAAMK,UAAU,4BAA2BH,SAAA,EAACM,EAAAA,EAAAA,KAAC6P,EAAAA,IAAY,CAAChQ,UAAU,wBAAuB,sDAAkDG,EAAAA,EAAAA,KAAA,KAAG8F,KAAK,WAAWjG,UAAU,0BAAyBH,SAAC,wBAKtM2Q,EAAiBA,KAEnB7Q,EAAAA,EAAAA,MAAA,OAAKK,UAAU,SAAQH,SAAA,EACrBF,EAAAA,EAAAA,MAAA,KAAGK,UAAU,eAAcH,SAAA,CAAC,2MAAuMM,EAAAA,EAAAA,KAAA,KAAG8F,KAAK,iCAAiCjG,UAAU,0BAAyBH,SAAC,eAAc,aAC9TM,EAAAA,EAAAA,KAAA,KAAGH,UAAU,YAAWH,SAAC,kBACzBF,EAAAA,EAAAA,MAAA,MAAAE,SAAA,EACEM,EAAAA,EAAAA,KAAA,MAAAN,UAAIM,EAAAA,EAAAA,KAAA,KAAG8F,KAAK,cAAcjG,UAAU,+BAA8BH,SAAC,gBACnEM,EAAAA,EAAAA,KAAA,MAAAN,UAAIM,EAAAA,EAAAA,KAAA,KAAG8F,KAAK,cAAcjG,UAAU,+BAA8BH,SAAC,iBACnEM,EAAAA,EAAAA,KAAA,MAAAN,UAAIM,EAAAA,EAAAA,KAAA,KAAG8F,KAAK,+BAA+BjG,UAAU,+BAA8BH,SAAC,iBACpFM,EAAAA,EAAAA,KAAA,MAAAN,UAAIM,EAAAA,EAAAA,KAAA,KAAG8F,KAAK,gCAAgCjG,UAAU,+BAA8BH,SAAC,uBAMvF4Q,EAAgBA,KAYlBtQ,EAAAA,EAAAA,KAAA,OAAAN,UACEM,EAAAA,EAAAA,KAAC4M,EAAAA,EAAOC,OAAM,CACZhN,UAAU,kEACViN,SAAU,CAAEC,MAAO,IACnB1J,QAdN,YACEkN,EAAAA,EAAAA,IAAa,WACbA,EAAAA,EAAAA,IAAa,cAEb5M,EAAAA,EAAM6M,IAAI,GAAD1Q,OAAI+D,4BAA6B,YAAWE,MAAK,WACxDM,OAAOC,SAASwB,KAAO,QACzB,GACF,EAOsBpG,SACjB,aAOD6M,EAAiBxC,IACrB,MAAOG,IAAS7I,EAAAA,EAAAA,UAAS0I,EAAMG,QACxBsC,EAAaC,IAAkBpL,EAAAA,EAAAA,WAAS,GAKzCqL,EAAaA,KACjBD,GAAe,IAqBjB,OACEjN,EAAAA,EAAAA,MAAAI,EAAAA,SAAA,CAAAF,SAAA,EACEM,EAAAA,EAAAA,KAAA,QAAMH,UAAU,wDAAwDwD,QA3BpDoN,KACtBhE,GAAe,IA0BoF/M,SAAC,oBAClGM,EAAAA,EAAAA,KAACmC,EAAAA,EAAU,CAACC,QAAM,EAACC,KAAMmK,EAAalK,GAAIC,EAAAA,SAAS7C,UACjDF,EAAAA,EAAAA,MAACgD,EAAAA,EAAM,CAACF,GAAG,MAAMzC,UAAU,gBAAgB4C,QAASiK,EAAWhN,SAAA,EAC7DM,EAAAA,EAAAA,KAACmC,EAAAA,EAAWO,MAAK,CACfJ,GAAIC,EAAAA,SACJI,MAAM,wBACNC,UAAU,YACVC,QAAQ,cACRC,MAAM,uBACNC,UAAU,cACVC,QAAQ,YAAWtD,UAEnBM,EAAAA,EAAAA,KAAA,OAAKH,UAAU,4CAGjBG,EAAAA,EAAAA,KAAA,OAAKH,UAAU,gCAA+BH,UAC5CM,EAAAA,EAAAA,KAAA,OAAKH,UAAU,8DAA6DH,UAC1EM,EAAAA,EAAAA,KAACmC,EAAAA,EAAWO,MAAK,CACfJ,GAAIC,EAAAA,SACJI,MAAM,wBACNC,UAAU,qBACVC,QAAQ,wBACRC,MAAM,uBACNC,UAAU,wBACVC,QAAQ,qBAAoBtD,UAE5BF,EAAAA,EAAAA,MAACgD,EAAAA,EAAOS,MAAK,CAACpD,UAAU,qHAAoHH,SAAA,EAC1IM,EAAAA,EAAAA,KAACwC,EAAAA,EAAOU,MAAK,CACXZ,GAAG,KACHzC,UAAU,8CAA6CH,SACxD,oBAGDF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,OAAMH,SAAA,EACnBM,EAAAA,EAAAA,KAAA,KAAGH,UAAU,wBAAuBH,SAAC,2MAGrCM,EAAAA,EAAAA,KAAA,KAAGH,UAAU,wBAAuBH,SAAC,sDAKvCF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,OAAMH,SAAA,EACnBM,EAAAA,EAAAA,KAAA,UACEmD,KAAK,SACLtD,UAAU,8IACVwD,QAlESqN,KAC3B3O,SAASyB,cAAc,qBAAqBC,UAAUC,IAAI,UAC1DC,EAAAA,EAAMC,KAAK,GAAD9D,OAAI+D,4BAA6B,mBAAmB,CAC9D,EAAG,CAAEC,QAAS,CAAE,eAAgB,uCAAyCC,MAAK,SAASC,GAErF,GADaA,EAAIvG,KACPyG,QAOR,OANAG,OAAOsM,SAASC,OAAOC,SAAS,CAAE,OAAU3G,IAC5C7F,OAAOsM,SAASG,SAAS5G,GACzB7F,OAAOsM,SAASI,MAAM,iBAAkB,CAAC,GAEzCtP,IAAAA,QAAe,gBACf4C,OAAOC,SAASC,SAGlBxC,SAASyB,cAAc,qBAAqBC,UAAUU,OAAO,UAC7D1C,IAAAA,MAAa,OACf,KAkDgD/B,SAC/B,4BAGDM,EAAAA,EAAAA,KAAA,UACEmD,KAAK,SACLtD,UAAU,yOACVwD,QAASqJ,EAAWhN,SACrB,gCAcfyP,EAAqB6B,IAA6B,IAA5B,IAAEhC,EAAG,GAAE/G,EAAE,UAAE0F,GAAWqD,EAChD,MAAOxE,EAAaC,IAAkBpL,EAAAA,EAAAA,WAAS,GACzC8I,GAAO8G,EAAAA,EAAAA,MAMPvE,EAAaA,KACjBD,GAAe,IA2CjB,OACEjN,EAAAA,EAAAA,MAAAI,EAAAA,SAAA,CAAAF,SAAA,EACEM,EAAAA,EAAAA,KAAA,UACAH,UAAS,wGACTmP,IAAKA,EACL3L,QArDoB6N,KACtBzE,GAAe,IAoDY/M,SACxB,YAGDM,EAAAA,EAAAA,KAACmC,EAAAA,EAAU,CAACC,QAAM,EAACC,KAAMmK,EAAalK,GAAIC,EAAAA,SAAS7C,UACjDF,EAAAA,EAAAA,MAACgD,EAAAA,EAAM,CAACF,GAAG,MAAMzC,UAAU,gBAAgB4C,QAASiK,EAAWhN,SAAA,EAC7DM,EAAAA,EAAAA,KAACmC,EAAAA,EAAWO,MAAK,CACfJ,GAAIC,EAAAA,SACJI,MAAM,wBACNC,UAAU,YACVC,QAAQ,cACRC,MAAM,uBACNC,UAAU,cACVC,QAAQ,YAAWtD,UAEnBM,EAAAA,EAAAA,KAAA,OAAKH,UAAU,4CAGjBG,EAAAA,EAAAA,KAAA,OAAKH,UAAU,gCAA+BH,UAC5CM,EAAAA,EAAAA,KAAA,OAAKH,UAAU,8DAA6DH,UAC1EM,EAAAA,EAAAA,KAACmC,EAAAA,EAAWO,MAAK,CACfJ,GAAIC,EAAAA,SACJI,MAAM,wBACNC,UAAU,qBACVC,QAAQ,wBACRC,MAAM,uBACNC,UAAU,wBACVC,QAAQ,qBAAoBtD,UAE5BF,EAAAA,EAAAA,MAACgD,EAAAA,EAAOS,MAAK,CAACpD,UAAU,qHAAoHH,SAAA,EAC1IM,EAAAA,EAAAA,KAACwC,EAAAA,EAAOU,MAAK,CACXZ,GAAG,KACHzC,UAAU,8CAA6CH,SACxD,yBAGDM,EAAAA,EAAAA,KAAA,OAAKH,UAAU,OAAMH,UACnBM,EAAAA,EAAAA,KAAA,KAAGH,UAAU,wBAAuBH,SAAC,0DAKvCF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,kBAAiBH,SAAA,EAC9BM,EAAAA,EAAAA,KAAA,UACEmD,KAAK,SACLtD,UAAU,8IACVwD,QA1ECoI,IACfuD,IACJtC,IAEwB,KAApBvC,EAAKgH,YAAyC,OAApBhH,EAAKgH,YAAuC,QAAf3I,EAczDnE,OAAOC,SAASwB,KAAO,UAXpBqC,GAA+B,KAAhBA,EAChBwF,EAAU,CACR7E,UAAWA,KACTzE,OAAOC,SAASwB,KAAO,aAI3BzB,OAAOC,SAASwB,KAAO,YA4DYpG,SACtB,SAGDM,EAAAA,EAAAA,KAAA,UACEmD,KAAK,SACLtD,UAAU,yOACVwD,QAASqJ,EAAWhN,SACrB,+BAcrB,SAAS0R,EAAaC,EAASC,GAC7B,OAAOD,EAAQE,MAAM,EAAGD,EAC1B,CAEA,SAASE,EAAcH,EAASC,GAC9B,GAAID,EAAQ9J,OAAO+J,EACjB,OAAO,EAGT,IAAIG,EAAeJ,EAAQ9J,OACvBmK,EAAW9S,KAAK+S,MAAMF,EAAaH,GAMvC,OALgBG,EAAeH,EAEjB,IACZI,GAAoB,GAEfA,CACT,CAEA,MAAME,EAAoBC,IAA6H,IAA5H,oBAACC,EAAmB,kBAAEC,EAAiB,oBAAEC,EAAmB,0BAAEC,EAAyB,YAAExN,EAAW,gBAAEC,GAAgBmN,EAE/I,MAAOK,EAAQC,IAAa9Q,EAAAA,EAAAA,UAAS,KAC9B+Q,EAAwBC,IAA6BhR,EAAAA,EAAAA,UAAS,KAC9DiR,EAA0BC,IAA+BlR,EAAAA,EAAAA,UAAS,KAClEmR,EAAUC,IAAepR,EAAAA,EAAAA,UAAS,IAClCqR,EAAWC,IAAgBtR,EAAAA,EAAAA,UAAS,IACpCuR,EAAaC,IAAkBxR,EAAAA,EAAAA,UAAS,IAE/CG,EAAAA,EAAAA,YAAU,KACR,IAAI6P,EAAUU,EACdM,EAA0BhB,GAE1BA,EAAUD,EAAaC,EAAQ,GAC/BkB,EAA4BlB,GAE5B,IAAIyB,EAAYtB,EAAcO,EAAkB,GAChDY,EAAaG,KACZ,CAACf,IAEL,MAAMgB,EAAeA,KAClB,IAAIC,EAAQjR,SAASC,eAAe,cAAcoB,MAC9CiO,EAAUU,EAEdV,EAAUU,EAAkBxL,QAAO,SAAS0M,GAC1C,IAAIC,EAAYD,EAAKE,WAAa,IAAMF,EAAKG,UAC7C,OAASH,EAAKE,WAAWE,QAAQL,IAAQ,GAAKC,EAAKG,UAAUC,QAAQL,IAAQ,GAAKC,EAAK/I,MAAMmJ,QAAQL,IAAQ,GAAKE,EAAUG,QAAQL,IAAU,CAChJ,IAEA,IAAIF,EAAYtB,EAAcH,EAAQmB,GACtCG,EAAaG,GACbT,EAA0BhB,GAE1BA,EAAUD,EAAaC,EAAQmB,GAC/BD,EAA4BlB,KAG9B7P,EAAAA,EAAAA,YAAU,KACRuR,MAEC,CAACP,IAEJ,MA4CMc,EAAoBC,IACxBV,EAAeU,GAEf,IAAIlC,EAAUe,EACVoB,GAAeD,EAAK,GAAGf,EAE3BnB,EAAUA,EAAQE,MAAMiC,EAAanC,EAAQ9J,QAC7C8J,EAAUD,EAAaC,EAAQmB,GAE/BD,EAA4BlB,IAmB9B,OACErR,EAAAA,EAAAA,KAAAJ,EAAAA,SAAA,CAAAF,UACAF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,SAAQH,SAAA,EACrBF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,oDAAmDH,SAAA,EAChEM,EAAAA,EAAAA,KAAA,OAAKH,UAAU,0BAAyBH,UACtCM,EAAAA,EAAAA,KAAA,SAAOH,UAAU,kEACjBsD,KAAK,OACLsQ,GAAG,aACHpI,KAAK,SACLD,YAAY,0BACZhI,MAAO8O,EACPvG,SAAWC,IACTmH,IACAZ,EAAUvG,EAAEF,OAAOtI,aAIvBpD,EAAAA,EAAAA,KAAA,OAAKH,UAAU,0BAAyBH,UACtCM,EAAAA,EAAAA,KAAC4M,EAAAA,EAAOC,OAAM,CACdhN,UAAU,kFACV6T,WAAY,CAAEC,gBAAiB,WAC/B7G,SAAU,CAAEC,MAAO,IACnB1J,QAASA,IAAKyO,IAAsBpS,SACnC,mBAKFqS,EAAkBxK,OAAS,GAC5B/H,EAAAA,EAAAA,MAAA,OAAKK,UAAU,2DAA0DH,SAAA,CAAC,SACnEF,EAAAA,EAAAA,MAAA,UAAQK,UAAU,oBAAoB8L,SAAWC,IA7C5D6G,EA6CoF7G,EA7ClEF,OAAOtI,YACzByP,EAAe,IA4CwEnT,SAAA,EAC/EM,EAAAA,EAAAA,KAAA,UAAQoD,MAAM,IAAG1D,SAAC,OAClBM,EAAAA,EAAAA,KAAA,UAAQoD,MAAM,KAAI1D,SAAC,QACnBM,EAAAA,EAAAA,KAAA,UAAQoD,MAAM,KAAI1D,SAAC,QACnBM,EAAAA,EAAAA,KAAA,UAAQoD,MAAM,KAAI1D,SAAC,UACZ,cAET,OAGJM,EAAAA,EAAAA,KAAA,OAAKH,UAAU,kHAAiHH,SAC7H4S,GAA4BA,EAAyB/K,QACtD/H,EAAAA,EAAAA,MAAA,SAAOK,UAAU,yEAAwEH,SAAA,EACvFM,EAAAA,EAAAA,KAAA,SAAAN,UACEF,EAAAA,EAAAA,MAAA,MAAIK,UAAU,cAAaH,SAAA,EACzBM,EAAAA,EAAAA,KAAA,MAAIH,UAAU,qHAAoHH,SAAC,cACnIM,EAAAA,EAAAA,KAAA,MAAIH,UAAU,qHAAoHH,SAAC,WACnIM,EAAAA,EAAAA,KAAA,MAAIH,UAAU,qHAAoHH,SAAC,YACnIM,EAAAA,EAAAA,KAAA,MAAIH,UAAU,qHAAoHH,SAAC,eACnG,eAA9B+E,EAAY3H,eAAoE,eAAlC4H,EAAgB5H,eAC9DkD,EAAAA,EAAAA,KAAA,MAAIH,UAAU,qHAAoHH,SAAC,iBAEnIM,EAAAA,EAAAA,KAAA,MAAIH,UAAU,qHAAoHH,SAAC,sBAGrIM,EAAAA,EAAAA,KAAA,MAAIH,UAAU,qHAAoHH,SAAC,kBAGvIM,EAAAA,EAAAA,KAAA,SAAOH,UAAU,oCAAmCH,SACjD4S,aAAwB,EAAxBA,EAA0B9K,KAAI,CAAC4B,EAAK3B,KACnCjI,EAAAA,EAAAA,MAAA,MAAgBK,UAAU,cAAaH,SAAA,EACvCF,EAAAA,EAAAA,MAAA,MAAIK,UAAU,yGAAwGH,SAAA,CAAE0J,EAAI+J,WAAW,IAAE/J,EAAIgK,cAC7IpT,EAAAA,EAAAA,KAAA,MAAIH,UAAU,yGAAwGH,SAAE0J,EAAIc,SAC5HlK,EAAAA,EAAAA,KAAA,MAAIH,UAAU,yGAAwGH,SAAE0J,EAAIC,UAC5HrJ,EAAAA,EAAAA,KAAA,MAAIH,UAAU,yGAAwGH,UAAE1C,EAAAA,EAAAA,IAAWoM,EAAI+G,eACvInQ,EAAAA,EAAAA,KAAA,MAAIH,UAAU,+FAA8FH,SAEzE,eAA9B+E,EAAY3H,eAAoE,eAAlC4H,EAAgB5H,eAC/D0C,EAAAA,EAAAA,MAAAI,EAAAA,SAAA,CAAAF,SAAA,EACEF,EAAAA,EAAAA,MAAA,KAAAE,SAAA,CAAG,WAAS0J,EAAIvB,gBAChBrI,EAAAA,EAAAA,MAAA,KAAAE,SAAA,CAAG,SAAO0J,EAAIwK,sBACdpU,EAAAA,EAAAA,MAAA,KAAAE,SAAA,CAAG,OAAK0J,EAAIyK,uBAGd7T,EAAAA,EAAAA,KAAA,KAAAN,SAAI0J,EAAIvB,iBAIZ7H,EAAAA,EAAAA,KAAA,MAAIH,UAAU,yGAAwGH,SACnG,WAAf0J,EAAIC,QACNrJ,EAAAA,EAAAA,KAAA,OAAKH,UAAU,cAAaH,UAC1BF,EAAAA,EAAAA,MAACkP,EAAAA,EAAI,CAACpM,GAAG,MAAMzC,UAAU,uDAAsDH,SAAA,EAC7EM,EAAAA,EAAAA,KAAA,OAAKH,UAAU,SAAQH,UACrBM,EAAAA,EAAAA,KAAC0O,EAAAA,EAAKC,OAAM,CAAC9O,UAAU,kHAAiHH,SAAC,eAI3IM,EAAAA,EAAAA,KAACmC,EAAAA,EAAU,CACTG,GAAIC,EAAAA,SACJI,MAAM,mCACNC,UAAU,+BACVC,QAAQ,kCACRC,MAAM,iCACNC,UAAU,kCACVC,QAAQ,+BAA8BtD,UAEtCM,EAAAA,EAAAA,KAAC0O,EAAAA,EAAKE,MAAK,CAAC/O,UAAS,GAAAC,OACnB2H,EAAQ,EAAI,SAAW,SAAQ,0LACwJ/H,UACzLF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,yBAAwBH,SAAA,EACrCM,EAAAA,EAAAA,KAAC0O,EAAAA,EAAKG,KAAI,CAAAnP,UACRM,EAAAA,EAAAA,KAAA,UACAH,UAAU,8FACVwD,QAASA,CAACyQ,EAASX,EAAYC,EAAWlJ,IAjH/C6J,EAACD,EAASX,EAAYC,EAAWlJ,KAClD,IAAI8J,EAAS,CAAC,EACdA,EAAgB,QAAIF,EACpBE,EAAmB,WAAIb,EACvBa,EAAkB,UAAIZ,EACtBY,EAAc,MAAI9J,EAElB+H,GAA0B,EAAK+B,IA0G2CD,CAAW3K,EAAI0K,QAAS1K,EAAI+J,WAAY/J,EAAIgK,UAAWhK,EAAIc,OAAOxK,SACrH,iBAIHM,EAAAA,EAAAA,KAAC0O,EAAAA,EAAKG,KAAI,CAAAnP,UACRM,EAAAA,EAAAA,KAACiU,EAAoB,CAACC,eAAgB9K,EAAI0K,QAAS5J,MAAOd,EAAIc,WAEhElK,EAAAA,EAAAA,KAAC0O,EAAAA,EAAKG,KAAI,CAAAnP,UACRM,EAAAA,EAAAA,KAACmU,EAAY,CAACD,eAAgB9K,EAAI0K,QAAS5J,MAAOd,EAAIc,MAAO8H,oBAAqBA,kBAMnF,OA1DFvK,WAgEbzH,EAAAA,EAAAA,KAAA,OAAAN,UACAF,EAAAA,EAAAA,MAAA,QAAMK,UAAU,4BAA2BH,SAAA,EAACM,EAAAA,EAAAA,KAAC6P,EAAAA,IAAY,CAAChQ,UAAU,wBAAuB,4BAI/FL,EAAAA,EAAAA,MAAA,OAAKK,UAAU,mBAAkBH,SAAA,EAC/BM,EAAAA,EAAAA,KAAA,QAAMH,UAAU,wIAAuIH,SA1KlI0U,MACzB,IAAIC,EAAejC,EAAuB7K,OACtC+M,EAAS1B,EAAYJ,EACrB+B,EAAWD,GAAU9B,EAAS,GAMlC,OAJI8B,EAASD,IACXC,EAASD,GAGJ,WAAYE,EAAW,OAASD,EAAS,OAAUD,EAAe,YAkKlED,MAEHpU,EAAAA,EAAAA,KAAA,MAAIH,UAAU,6GAA4GH,SA7M1G8U,MACpB,IAAIC,EAAU,GAEd,GAAI/B,GAAW,GAAgC,IAA3BX,EAAkBxK,OACpC,OAAOkN,EAGL7B,EAAY,EACd6B,EAAQC,MAAK1U,EAAAA,EAAAA,KAAA,MAAIH,UAAU,qBAAuCwD,QAAUuI,GAAI0H,EAAiBV,EAAY,GAAGlT,SAAC,YAA5D,cAErD+U,EAAQC,MAAK1U,EAAAA,EAAAA,KAAA,MAAIH,UAAU,qBAAoBH,SAAmB,YAAb,cAGvD,IAAK,IAAIiV,EAAI,EAAGA,EAAIjC,EAAWiC,IAAK,CAClC,MACM9U,EAAS,mDAAAC,OADM8S,IAAgB+B,EAAI,EAC2C,SAAW,IAC/FF,EAAQC,MACN1U,EAAAA,EAAAA,KAAA,MAAIH,UAAWA,EAAuBwD,QAAUuI,GAAM0H,EAAiBqB,EAAI,GAAGjV,SAC3EiV,EAAI,GADwBA,EAAI,GAIvC,CAQA,OANI/B,EAAYF,EACd+B,EAAQC,MAAK1U,EAAAA,EAAAA,KAAA,MAAIH,UAAU,qBAAuCwD,QAAUuI,GAAI0H,EAAiBV,EAAY,GAAGlT,SAAC,QAA5D,cAErD+U,EAAQC,MAAK1U,EAAAA,EAAAA,KAAA,MAAIH,UAAU,qBAAoBH,SAAmB,QAAb,cAGhD+U,GAiLFD,cASHI,EAAkBC,IAAsF,IAArF,eAACC,EAAc,sBAAEC,EAAqB,kBAAEC,EAAiB,oBAAEhD,GAAoB6C,EACtG,MAAOI,EAAUC,IAAe7T,EAAAA,EAAAA,UAAS,KAClC8T,EAAaC,IAAkB/T,EAAAA,EAAAA,UAAS,KACxC6I,EAAOmL,IAAYhU,EAAAA,EAAAA,UAAS,KAC5BiU,EAAWC,IAAgBlU,EAAAA,EAAAA,UAAS,KACpCmU,EAAQC,IAAapU,EAAAA,EAAAA,UAAS,IAS/BqU,EAAwBA,KAC5B,IAAI5T,EAAQC,SAASC,eAAe,oBACpC,GAAY,OAARF,EAAa,CAClB,IAAI6T,EAAa5T,SAASC,eAAe,kBACzC2T,EAAWC,MAAQ,GACnBD,EAAWlS,UAAUU,OAAO,mBAE5B,IAAI0R,EAAY9T,SAASC,eAAe,qBACxC6T,EAAUD,MAAQ,GAClBC,EAAUpS,UAAUU,OAAO,mBAK3BrC,EAAMG,MAAMC,QAAU,OACnB6S,GAAsB,EACxB,IAGFvT,EAAAA,EAAAA,YAAU,KACR,IAAI2R,EAAa6B,EAAkB7B,WAAa6B,EAAkB7B,WAAa,GAC3EC,EAAY4B,EAAkB5B,UAAa4B,EAAkB5B,UAAY,GACzElJ,EAAQ8K,EAAkB9K,MAAS8K,EAAkB9K,MAAQ,GAC7D4J,EAAUkB,EAAkBlB,QAAWkB,EAAkBlB,QAAU,GAEvEoB,EAAY/B,EAAa,IAAMC,GAC/BgC,EAAejC,EAAa,IAAMC,GAClCiC,EAASnL,GACTqL,EAAarL,GACbuL,EAAU3B,KACT,CAACkB,SAoCiBpT,IAAjBkT,IAAiD,IAAnBA,GAzELgB,MAC3B,IAAIhU,EAAQC,SAASC,eAAe,oBACxB,OAARF,IACLA,EAAMG,MAAMC,QAAU,UAuErB4T,QAEmBlU,IAAjBkT,IAAiD,IAAnBA,GAChCY,IA6EF,OACE1V,EAAAA,EAAAA,KAAAJ,EAAAA,SAAA,CAAAF,UACAM,EAAAA,EAAAA,KAAA,OAAKyT,GAAG,mBAAmB5T,UAAU,iBAAgBH,UACnDM,EAAAA,EAAAA,KAAA,OAAKP,MAAM,kCAAiCC,UAC1CF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,0BAAyBH,SAAA,EACtCM,EAAAA,EAAAA,KAAA,OAAKH,UAAU,uDAAsDH,SAAC,kBAGtEM,EAAAA,EAAAA,KAAA,OAAKH,UAAU,wCAAuCH,UACpDM,EAAAA,EAAAA,KAAA,QAAMP,MAAM,QAAQ4D,QAASA,IAAKqS,IAAwBhW,SAAC,YAG/DF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,8CAA6CH,SAAA,EACxDF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,mDAAkDH,SAAA,EAC/DM,EAAAA,EAAAA,KAAA,KAAAN,SAAG,eACHM,EAAAA,EAAAA,KAAA,SAAOmD,KAAK,OAAOsQ,GAAG,oBAAoB5T,UAAU,kDACpDuL,YAAY,WACZhI,MAAO6R,EACPtJ,SAAWC,IACTA,EAAEmK,iBACFb,EAAYtJ,EAAEF,OAAOtI,OArHfwI,KACpB,IAAIsH,EAAYtH,EAAEF,OAAOtI,MAAMgE,OAC3B4O,EAAqBjU,SAASC,eAAe,2BAEjC,KAAZkR,GACF8C,EAAmBC,UAAY,wBAClCD,EAAmB/T,MAAMC,QAAU,QAChC0J,EAAEF,OAAOjI,UAAUC,IAAI,qBAEvBsS,EAAmBC,UAAY,GAClCD,EAAmB/T,MAAMC,QAAU,OAChC0J,EAAEF,OAAOjI,UAAUU,OAAO,qBA2Gd+R,CAAatK,IAGfgK,MAAM,MAEN5V,EAAAA,EAAAA,KAAA,OAAKP,MAAM,eAAegU,GAAG,gCAE/BjU,EAAAA,EAAAA,MAAA,OAAKK,UAAU,mDAAkDH,SAAA,EAC/DM,EAAAA,EAAAA,KAAA,KAAAN,SAAG,aACHM,EAAAA,EAAAA,KAAA,SAAOmD,KAAK,OAAOsQ,GAAG,iBAAiB5T,UAAU,kDACjDuL,YAAY,QACZhI,MAAO8G,EACPyB,SAAWC,IACTA,EAAEmK,iBACFV,EAASzJ,EAAEF,OAAOtI,OAvJXwI,KACrB,IAAI1B,EAAQ0B,EAAEF,OAAOtI,MACjB+S,EAAkBpU,SAASC,eAAe,wBAE3B,KAAfkI,EAAM9C,QACR+O,EAAgBF,UAAY,oBAC/BE,EAAgBlU,MAAMC,QAAU,QAC7B0J,EAAEF,OAAOjI,UAAUC,IAAI,oBAChB,eAAewI,KAAKhC,IAK3BiM,EAAgBF,UAAY,GAC/BE,EAAgBlU,MAAMC,QAAU,OAC7B0J,EAAEF,OAAOjI,UAAUU,OAAO,qBAN1BgS,EAAgBF,UAAY,uBAC/BE,EAAgBlU,MAAMC,QAAU,QAC7B0J,EAAEF,OAAOjI,UAAUC,IAAI,qBA6IXuI,CAAcL,IAGhBgK,MAAM,MAEN5V,EAAAA,EAAAA,KAAA,OAAKP,MAAM,eAAegU,GAAG,gCAGnCzT,EAAAA,EAAAA,KAAA,OAAKH,UAAU,GAAEH,UACfF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,aAAYH,SAAA,EACzBM,EAAAA,EAAAA,KAAA,SAAOmD,KAAK,SAASC,MAAM,gBAAgBvD,UAAU,2GAA2GwD,QAASA,IAzHhK0E,WACnB,IAAIqO,EAAgBrU,SAASC,eAAe,qBACxCgU,EAAqBjU,SAASC,eAAe,2BAC7C2T,EAAa5T,SAASC,eAAe,kBACrCmU,EAAkBpU,SAASC,eAAe,wBAI9C,GAA8B,KAA1B2T,EAAWvS,MAAMgE,OAItB,OAHG+O,EAAgBF,UAAY,oBAC/BE,EAAgBlU,MAAMC,QAAU,aAChCyT,EAAWlS,UAAUC,IAAI,mBAEpB,IAAK,eAAewI,KAAKhC,GAI9B,OAHGiM,EAAgBF,UAAY,uBAC/BE,EAAgBlU,MAAMC,QAAU,aAC7ByT,EAAWlS,UAAUC,IAAI,mBAI3B,GAAiC,KAA7B0S,EAAchT,MAAMgE,OAIzB,OAHG4O,EAAmBC,UAAY,wBAClCD,EAAmB/T,MAAMC,QAAU,aAChCkU,EAAc3S,UAAUC,IAAI,mBAM9B,GAFA3B,SAASyB,cAAc,qBAAqBC,UAAUC,IAAI,UAEtDyR,IAAciB,EAAchT,OAASkS,IAAcK,EAAWvS,MAGhE,OAFAsS,SACA3T,SAASyB,cAAc,qBAAqBC,UAAUU,OAAO,UAI/D,IAAIkS,EAAG,GAAAvW,OAAM+D,4BAA6B,2BAC1CF,EAAAA,EAAMC,KAAKyS,EAAK,CACdnC,eAAgBsB,EAChBc,aAAcX,EAAWvS,MACzBmT,gBAAkBH,EAAchT,MAChCoT,oBAAqBrB,EACrBsB,iBAAkBnB,GACjB,CAAExR,QAAS,CAAE,eAAgB,uCAAyCC,MAAK,SAASC,GACrF,IAAIC,EAASD,EAAIvG,KAIjB,GAFAsE,SAASyB,cAAc,qBAAqBC,UAAUU,OAAO,UAE1DF,EAAOC,QACR,GAAIwS,MAAMC,QAAQ1S,EAAOxG,OAASwG,EAAOxG,KAAK8J,OAAS,EACrD,IAAI,IAAIoN,EAAI,EAAGA,EAAI1Q,EAAOxG,KAAK8J,OAAQoN,IAAK,CAC1C,IAAIgB,EAAa5T,SAASC,eAAe,kBACzC2T,EAAWC,MAAQ3R,EAAOxG,KAAKkX,GAAGiC,MAClCjB,EAAWlS,UAAUC,IAAI,mBACzBjC,IAAAA,MAAawC,EAAOxG,KAAKkX,GAAGiC,MAC9B,MAEAlB,IACA1D,IACAvQ,IAAAA,QAAe,uBAGjBiU,IACA1D,IACAvQ,IAAAA,MAAa,uBAGjB,IAAGoV,OAAM,SAAUD,GACjBlB,IACA3T,SAASyB,cAAc,qBAAqBC,UAAUU,OAAO,UAC7D1C,IAAAA,MAAa,mDACf,KAmDwLqV,MAC9K9W,EAAAA,EAAAA,KAAA,SAAOmD,KAAK,SAASC,MAAM,QAAQvD,UAAU,mGAAmGwD,QAASA,IAAKqS,qBAUtKqB,EAAiBC,IAAqI,IAApI,cAACC,EAAa,iBAAEC,EAAgB,oBAAElF,EAAmB,cAAEmF,EAAa,kBAAEpF,EAAiB,MAAEqF,EAAK,YAAEC,EAAW,gBAAEC,GAAiBN,EAEpJ,MAAO3F,EAASkG,IAAclW,EAAAA,EAAAA,UAAS,CACrC,CAAEgK,KAAM,GAAInB,MAAO,GAAI0M,MAAO,OAEzBY,EAAcC,IAAmBpW,EAAAA,EAAAA,WAAS,IAC1CqW,EAAaC,IAAgBtW,EAAAA,EAAAA,WAAS,GAEvCuW,EAAYA,KAChBL,GAAWM,GAAW,IAAIA,EAAS,CAAExM,KAAM,GAAInB,MAAO,GAAI0M,MAAO,OAEjE,IAAIkB,EAA6BzG,EAAQ9J,OAAOwK,EAAkBxK,OAElE+P,GAAgB,GAChBK,GAAa,GACTP,GAASU,EAA6B,GAAKX,GAC7CM,GAAgB,IAIdM,EAAchQ,gBACZwP,GAAWM,GACRA,EAAQtR,QAAO,CAACyR,EAAGrD,IAAMA,IAAM,MAExCiD,IACAH,GAAgB,GAChBE,GAAa,IAGTM,EAAgBxQ,IACpB8P,GAAWM,GACFA,EAAQtR,QAAO,CAACyR,EAAGrD,IAAMA,IAAMlN,MAGxC,IAAIyQ,EAAkB7G,EAAQ9J,OAASwK,EAAkBxK,OAAS,EAE9D6P,GAASc,EAAkBf,IAC7BG,GAAgB,GAChBG,GAAgB,IAGdL,GAA4B,IAAnB/F,EAAQ9J,QACnBoQ,GAAa,IAkFXQ,EAAuBA,KAC3B,IAAIrW,EAAQC,SAASC,eAAe,mBACxB,OAARF,IACFA,EAAMG,MAAMC,QAAU,OACtBgV,GAAiB,KA+DfkB,EAAiBrQ,UACrB,IAAI4M,EAAI,EACJ0D,GAAW,EACXxC,EAAY,GACZF,EAAa,GACbzC,EAAY,GACZoF,EAAc,GACdC,EAAc,GACdC,EAAe,GACfC,GAAY,EAEhB,KAAO9D,EAAItD,EAAQ9J,QAAQ,CACzBsO,EAAY9T,SAASC,eAAe,QAAQ2S,GAC5CgB,EAAa5T,SAASC,eAAe,SAAS2S,GAC9C2D,EAAcvW,SAASC,eAAe,UAAU2S,GAChD4D,EAAcxW,SAASC,eAAe,UAAU2S,GAEhD,IAAIzK,EAAQmH,EAAQsD,GAAGzK,MAAM9C,OAC7B8L,EAAY7B,EAAQsD,GAAGtJ,KAAKjE,OAC5BoR,EAAenH,EAAQsD,GAAGiC,MAAMxP,OAEhCkR,EAAYrW,MAAMC,QAAU,OAC5BqW,EAAYtW,MAAMC,QAAU,OAC5B2T,EAAUD,MAAQ,GAClBC,EAAUpS,UAAUU,OAAO,mBAC3BwR,EAAWC,MAAQ,GACnBD,EAAWlS,UAAUU,OAAO,mBAEhB,KAAR+F,GACFyL,EAAWC,MAAQ,oBACnBD,EAAWlS,UAAUC,IAAI,mBACzB6U,EAAYtW,MAAMC,QAAU,QAC5BqW,EAAYtC,UAAY,oBACxBoC,GAAW,GACD,eAAenM,KAAKhC,KAC9ByL,EAAWC,MAAQ,uBACnBD,EAAWlS,UAAUC,IAAI,mBACzB6U,EAAYtW,MAAMC,QAAU,QAC5BqW,EAAYtC,UAAY,uBACxBoC,GAAW,GAGM,KAAfG,IACF7C,EAAWC,MAAQ4C,EACnB7C,EAAWlS,UAAUC,IAAI,mBACzB2U,GAAW,GAGG,KAAZnF,IACF2C,EAAUD,MAAQ,wBAClBC,EAAUpS,UAAUC,IAAI,mBACxB4U,EAAYrC,UAAY,wBACxBqC,EAAYrW,MAAMC,QAAU,QAC5BmW,GAAW,GAGRA,IACHI,EAAYpH,EAAQ9K,QAAO,SAAS0M,GAClC,OAASA,EAAK/I,MAAM9C,SAAS8C,CAC/B,IAEIuO,EAAUlR,OAAO,IACnBoO,EAAWC,MAAQ,kBACnBD,EAAWlS,UAAUC,IAAI,mBACzB6U,EAAYtC,UAAY,kBACxBsC,EAAYtW,MAAMC,QAAU,QAC5BmW,GAAW,IAIf1D,GACF,CAEA,OAAO0D,GAUT,YAPoBzW,IAAhBqV,IAA+C,IAAlBA,GAtJLyB,MAC1B,IAAI5W,EAAQC,SAASC,eAAe,mBACxB,OAARF,IACFA,EAAMG,MAAMC,QAAU,UAoJxBwW,QAEkB9W,IAAhBqV,IAA+C,IAAlBA,GAC/BkB,KAIAnY,EAAAA,EAAAA,KAAAJ,EAAAA,SAAA,CAAAF,UACFM,EAAAA,EAAAA,KAAA,OAAKyT,GAAG,kBAAkB5T,UAAU,iBAAgBH,UACnDM,EAAAA,EAAAA,KAAA,OAAKP,MAAM,kCAAiCC,UACvCF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,0BAAyBH,SAAA,EACtCM,EAAAA,EAAAA,KAAA,OAAKH,UAAU,uDAAsDH,SAAC,iBAGtEM,EAAAA,EAAAA,KAAA,OAAKH,UAAU,wCAAuCH,UACpDM,EAAAA,EAAAA,KAAA,QAAMP,MAAM,QAAQ4D,QAASA,IAAK8U,IAAuBzY,SAAC,YAG9DF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,mBAAkBH,SAAA,EACjCM,EAAAA,EAAAA,KAAA,KAAAN,SAAG,UAAS,wEAEZF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,GAAEH,SAAA,EACfF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,wCAAuCH,SAAA,EACpDM,EAAAA,EAAAA,KAAA,OAAKH,UAAU,6CAA4CH,SAAC,eAG5DM,EAAAA,EAAAA,KAAA,OAAKH,UAAU,uDAAsDH,SAAC,aAIvE2R,aAAO,EAAPA,EAAS7J,KAAI,CAACmR,EAAKlR,KACpBjI,EAAAA,EAAAA,MAAA,OAAKK,UAAU,6BAA4BH,SAAA,EACzCF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,wCAAuCH,SAAA,EACpDM,EAAAA,EAAAA,KAAA,SAAOmD,KAAK,OAAOsQ,GAAI,QAAQhM,EAAOA,MAAOA,EAAO5H,UAAU,6DAA6DuL,YAAY,kBAAkBhI,MAAOuV,EAAItN,KACpKM,SAAWC,IACT2L,GAAWqB,IACT,MAAMC,EAASD,EAAErH,QAGjB,OAFAsH,EAAOpR,GAAO4D,KAAOO,EAAEF,OAAOtI,MAtI3B8S,EAACtK,EAAGnE,KACvB,IAAIyL,EAAYtH,EAAEF,OAAOtI,MAAMgE,OAC3BuO,EAAa5T,SAASC,eAAe,SAASyF,GAAOrE,MACrDkV,EAAcvW,SAASC,eAAe,UAAUyF,GAEpC,KAAZyL,GAA+B,KAAbyC,GACpB/J,EAAEF,OAAOkK,MAAQ,wBACjBhK,EAAEF,OAAOjI,UAAUC,IAAI,mBACvB4U,EAAYrW,MAAMC,QAAU,QAC5BoW,EAAYrC,UAAY,0BAExBrK,EAAEF,OAAOkK,MAAQ,GACjBhK,EAAEF,OAAOjI,UAAUU,OAAO,mBAC1BmU,EAAYrW,MAAMC,QAAU,OAC5BoW,EAAYrC,UAAY,KAyHVC,CAAatK,EAAGnE,GACToR,MAGXjD,MAAM,MAEN5V,EAAAA,EAAAA,KAAA,OAAKP,MAAM,eAAegU,GAAI,UAAUhM,QAE1CjI,EAAAA,EAAAA,MAAA,OAAKK,UAAU,wCAAuCH,SAAA,EACpDM,EAAAA,EAAAA,KAAA,SAAOmD,KAAK,OAAOsQ,GAAI,SAAShM,EAAOA,MAAOA,EAAO5H,UAAU,6DAA6DuL,YAAY,cAAchI,MAAOuV,EAAIzO,MACjKyB,SAAWC,IACT2L,GAAWqB,IACT,MAAMC,EAASD,EAAErH,QAGjB,OAFAsH,EAAOpR,GAAOyC,MAAQ0B,EAAEF,OAAOtI,MA3L3B6I,EAACL,EAAGnE,KACxB,IAAIyC,EAAQ0B,EAAEF,OAAOtI,MACjB0V,GAAU,EACVjD,EAAY9T,SAASC,eAAe,QAAQyF,GAC5CyL,EAAY2C,EAAUzS,MAAMgE,OAC5BkR,EAAcvW,SAASC,eAAe,UAAUyF,GAChD8Q,EAAcxW,SAASC,eAAe,UAAUyF,GAEjC,KAAfyC,EAAM9C,QACRwE,EAAEF,OAAOkK,MAAQ,oBACjBhK,EAAEF,OAAOjI,UAAUC,IAAI,mBACvB6U,EAAYtW,MAAMC,QAAU,QAC5BqW,EAAYtC,UAAY,oBACxB6C,GAAU,GACD,eAAe5M,KAAKhC,KAC7B0B,EAAEF,OAAOkK,MAAQ,uBACjBhK,EAAEF,OAAOjI,UAAUC,IAAI,mBACvB6U,EAAYtW,MAAMC,QAAU,QAC5BqW,EAAYtC,UAAY,uBACxB6C,GAAU,GAGRA,IACFlN,EAAEF,OAAOkK,MAAQ,GACjBhK,EAAEF,OAAOjI,UAAUU,OAAO,mBAC1BmU,EAAYrW,MAAMC,QAAU,OAC5BoW,EAAYrC,UAAY,GACxBsC,EAAYtW,MAAMC,QAAU,OAC5BqW,EAAYtC,UAAY,IAGtB6C,GAAuB,KAAZ5F,IACb2C,EAAUD,MAAQ,wBAClBC,EAAUpS,UAAUC,IAAI,mBACxB4U,EAAYrW,MAAMC,QAAU,QAC5BoW,EAAYrC,UAAY,0BAyJVhK,CAAcL,EAAGnE,GACVoR,MAGXjD,MAAM,MAEN5V,EAAAA,EAAAA,KAAA,OAAKP,MAAM,eAAegU,GAAI,UAAUhM,QAE1CzH,EAAAA,EAAAA,KAAA,OAAKH,UAAU,yBAAwBH,UACrCM,EAAAA,EAAAA,KAAA,UAAQqD,QAASA,IAAM4U,EAAaxQ,GAAO/H,UACzCM,EAAAA,EAAAA,KAAA,QAAAN,UAAMM,EAAAA,EAAAA,KAAC+Y,EAAAA,IAAO,CAAClZ,UAAU,6CAMjCL,EAAAA,EAAAA,MAAA,OAAKK,UAAS,GAAAC,OAAKsX,IAAUI,EAAe,cAAkBH,EAA8B,kBAAhB,cAAkC,qDAAoD3X,SAAA,CACjK8X,GAAgBH,IACbrX,EAAAA,EAAAA,KAAA,SACEmD,KAAK,SACLC,MAAM,WACNvD,UAAU,wFACVwD,QAASA,IAAMuU,MAGpBF,IACC1X,EAAAA,EAAAA,KAAA,SAAOmD,KAAK,SAASC,MAAM,cAAcvD,UAAU,oGAAoGwD,QAASA,IA9OjJ2V,MACvB,GAAI3H,EAAQ9J,OAAO,EAAE,CACnB,IAAI0R,EAAY5H,EAAQ9J,OAAS,EACjC0Q,EAAagB,EACf,GA0O6KD,QAGtKtB,IACC1X,EAAAA,EAAAA,KAAA,OAAKH,UAAU,aAAYH,UACzBM,EAAAA,EAAAA,KAAA,SAAOmD,KAAK,SAASC,MAAM,aAAavD,UAAU,sGAAsGwD,QAASA,IAjT1J0E,WAGjB,GADiCsJ,EAAQ9J,OAAOwK,EAAkBxK,OACnC4P,EAC7B1V,IAAAA,MAAa,8CAA8C0V,EAAc,kBAK3E,UADqBiB,IACrB,CAIA,IAAI/B,EAAG,GAAAvW,OAAM+D,4BAA6B,0BAEtCwN,EAAQ9J,QAAQ,EAClB4Q,KAIFpW,SAASyB,cAAc,qBAAqBC,UAAUC,IAAI,UAE1DC,EAAAA,EAAMC,KAAKyS,EAAK,CACdhF,QAAS6H,KAAKC,UAAU9H,IACvB,CAAEvN,QAAS,CAAE,eAAgB,uCAAyCC,MAAK,SAASC,GACrF,IAAIC,EAASD,EAAIvG,KAIjB,GAFAsE,SAASyB,cAAc,qBAAqBC,UAAUU,OAAO,UAE1DF,EAAOC,QACR,GAAIwS,MAAMC,QAAQ1S,EAAOxG,OAASwG,EAAOxG,KAAK8J,OAAS,EACrD,IAAI,IAAIoN,EAAI,EAAGA,EAAI1Q,EAAOxG,KAAK8J,OAAQoN,IAAK,CAC1C,IAAIlN,EAAQxD,EAAOxG,KAAKkX,GAAGlN,MACvBkO,EAAa5T,SAASC,eAAe,SAASyF,GAClDkO,EAAWC,MAAQ3R,EAAOxG,KAAKkX,GAAGiC,MAClCjB,EAAWlS,UAAUC,IAAI,mBAEzB,IAAI6U,EAAcxW,SAASC,eAAe,UAAUyF,GACpD8Q,EAAYtW,MAAMC,QAAU,QAC5BqW,EAAYtC,UAAYhS,EAAOxG,KAAKkX,GAAGiC,KACzC,MAEAnV,IAAAA,QAAe,iBACf0W,IACAnG,IACA+F,SAGFtW,IAAAA,MAAa,yBACb0W,IACAnG,IACA+F,GAGJ,IAAGlB,OAAM,SAAUD,GACjBuB,IACApW,SAASyB,cAAc,qBAAqBC,UAAUU,OAAO,UAC7D1C,IAAAA,MAAa,mDACf,IA/CA,GAsSgL2X,iBAU9KjF,EAAekF,IAAqD,IAApD,eAAEnF,EAAc,MAAEhK,EAAK,oBAAE8H,GAAqBqH,EAClE,MAAO7M,EAAaC,IAAkBpL,EAAAA,EAAAA,WAAS,GAIzCqL,EAAaA,KACjBD,GAAe,IAwBjB,OACEjN,EAAAA,EAAAA,MAAAI,EAAAA,SAAA,CAAAF,SAAA,EACEM,EAAAA,EAAAA,KAAA,UACAH,UAAS,wGACTwD,QAhC0BiW,KAC5B7M,GAAe,IA+BkB/M,SAC9B,YAGDM,EAAAA,EAAAA,KAACmC,EAAAA,EAAU,CAACC,QAAM,EAACC,KAAMmK,EAAalK,GAAIC,EAAAA,SAAS7C,UACjDF,EAAAA,EAAAA,MAACgD,EAAAA,EAAM,CAACF,GAAG,MAAMzC,UAAU,gBAAgB4C,QAASiK,EAAWhN,SAAA,EAC7DM,EAAAA,EAAAA,KAACmC,EAAAA,EAAWO,MAAK,CACfJ,GAAIC,EAAAA,SACJI,MAAM,wBACNC,UAAU,YACVC,QAAQ,cACRC,MAAM,uBACNC,UAAU,cACVC,QAAQ,YAAWtD,UAEnBM,EAAAA,EAAAA,KAAA,OAAKH,UAAU,4CAGjBG,EAAAA,EAAAA,KAAA,OAAKH,UAAU,gCAA+BH,UAC5CM,EAAAA,EAAAA,KAAA,OAAKH,UAAU,8DAA6DH,UAC1EM,EAAAA,EAAAA,KAACmC,EAAAA,EAAWO,MAAK,CACfJ,GAAIC,EAAAA,SACJI,MAAM,wBACNC,UAAU,qBACVC,QAAQ,wBACRC,MAAM,uBACNC,UAAU,wBACVC,QAAQ,qBAAoBtD,UAE5BF,EAAAA,EAAAA,MAACgD,EAAAA,EAAOS,MAAK,CAACpD,UAAU,qHAAoHH,SAAA,EAC1IM,EAAAA,EAAAA,KAACwC,EAAAA,EAAOU,MAAK,CACXZ,GAAG,KACHzC,UAAU,sDAAqDH,SAChE,mBAGDM,EAAAA,EAAAA,KAAA,OAAKH,UAAU,OAAMH,UACnBF,EAAAA,EAAAA,MAAA,KAAGK,UAAU,4EAA2EH,SAAA,EACtFM,EAAAA,EAAAA,KAACuZ,EAAAA,IAAa,CAAC1Z,UAAU,qCAAoC,oCAAgCG,EAAAA,EAAAA,KAAA,UAAAN,SAASwK,IAAe,kBAIzH1K,EAAAA,EAAAA,MAAA,OAAKK,UAAU,kBAAiBH,SAAA,EAC9BM,EAAAA,EAAAA,KAAA,UACEmD,KAAK,SACLtD,UAAU,8IACVwD,QAvEEoI,IACpB1J,SAASyB,cAAc,qBAAqBC,UAAUC,IAAI,UAC1DgJ,IAEA/I,EAAAA,EAAMC,KAAK,GAAD9D,OAAI+D,4BAA6B,6BAA6B,CACtEqQ,eAAgBA,EAChBoC,aAAcpM,GACb,CAAEpG,QAAS,CAAE,eAAgB,uCAAyCC,MAAK,SAASC,GACrF,IAAIC,EAASD,EAAIvG,KACjB,GAAGwG,EAAOC,QAIR,OAHAnC,SAASyB,cAAc,qBAAqBC,UAAUU,OAAO,UAC7D1C,IAAAA,QAAe,wBACfuQ,IAGFjQ,SAASyB,cAAc,qBAAqBC,UAAUU,OAAO,UAC1DF,EAAOxG,MAAMgE,IAAAA,MAAawC,EAAOxG,KAAK+G,IAC3C,KAsDwC9E,SACvB,mBAGDM,EAAAA,EAAAA,KAAA,UACEmD,KAAK,SACLtD,UAAU,yOACVwD,QAASqJ,EAAWhN,SACrB,+BAcfuU,EAAuBuF,IAAgC,IAA/B,eAAEtF,EAAc,MAAEhK,GAAOsP,EACrD,MAAOhN,EAAaC,IAAkBpL,EAAAA,EAAAA,WAAS,GAIzCqL,EAAaA,KACjBD,GAAe,IAuBjB,OACEjN,EAAAA,EAAAA,MAAAI,EAAAA,SAAA,CAAAF,SAAA,EACEM,EAAAA,EAAAA,KAAA,UACAH,UAAS,wGACTwD,QA/B0BiW,KAC5B7M,GAAe,IA8BkB/M,SAC9B,qBAGDM,EAAAA,EAAAA,KAACmC,EAAAA,EAAU,CAACC,QAAM,EAACC,KAAMmK,EAAalK,GAAIC,EAAAA,SAAS7C,UACjDF,EAAAA,EAAAA,MAACgD,EAAAA,EAAM,CAACF,GAAG,MAAMzC,UAAU,gBAAgB4C,QAASiK,EAAWhN,SAAA,EAC7DM,EAAAA,EAAAA,KAACmC,EAAAA,EAAWO,MAAK,CACfJ,GAAIC,EAAAA,SACJI,MAAM,wBACNC,UAAU,YACVC,QAAQ,cACRC,MAAM,uBACNC,UAAU,cACVC,QAAQ,YAAWtD,UAEnBM,EAAAA,EAAAA,KAAA,OAAKH,UAAU,4CAGjBG,EAAAA,EAAAA,KAAA,OAAKH,UAAU,gCAA+BH,UAC5CM,EAAAA,EAAAA,KAAA,OAAKH,UAAU,8DAA6DH,UAC1EM,EAAAA,EAAAA,KAACmC,EAAAA,EAAWO,MAAK,CACfJ,GAAIC,EAAAA,SACJI,MAAM,wBACNC,UAAU,qBACVC,QAAQ,wBACRC,MAAM,uBACNC,UAAU,wBACVC,QAAQ,qBAAoBtD,UAE5BF,EAAAA,EAAAA,MAACgD,EAAAA,EAAOS,MAAK,CAACpD,UAAU,qHAAoHH,SAAA,EAC1IM,EAAAA,EAAAA,KAACwC,EAAAA,EAAOU,MAAK,CACXZ,GAAG,KACHzC,UAAU,8CAA6CH,SACxD,qBAGDM,EAAAA,EAAAA,KAAA,OAAKH,UAAU,OAAMH,UACnBF,EAAAA,EAAAA,MAAA,KAAGK,UAAU,oCAAmCH,SAAA,CAAC,4DACSM,EAAAA,EAAAA,KAAA,UAAAN,SAASwK,IAAe,UAIpF1K,EAAAA,EAAAA,MAAA,OAAKK,UAAU,kBAAiBH,SAAA,EAC9BM,EAAAA,EAAAA,KAAA,UACEmD,KAAK,SACLtD,UAAU,8IACVwD,QAtEIoI,IACtB1J,SAASyB,cAAc,qBAAqBC,UAAUC,IAAI,UAC1DgJ,IAEA/I,EAAAA,EAAMC,KAAK,GAAD9D,OAAI+D,4BAA6B,kCAAkC,CAC3EqQ,eAAgBA,EAChBoC,aAAcpM,GACb,CAAEpG,QAAS,CAAE,eAAgB,uCAAyCC,MAAK,SAASC,GACrF,IAAIC,EAASD,EAAIvG,KACjB,GAAGwG,EAAOC,QAGR,OAFAzC,IAAAA,QAAe,2BACfM,SAASyB,cAAc,qBAAqBC,UAAUU,OAAO,UAG/DpC,SAASyB,cAAc,qBAAqBC,UAAUU,OAAO,UAC1DF,EAAOxG,MAAMgE,IAAAA,MAAawC,EAAOxG,KAAK+G,IAC3C,KAsD0C9E,SACzB,qBAGDM,EAAAA,EAAAA,KAAA,UACEmD,KAAK,SACLtD,UAAU,yOACVwD,QAASqJ,EAAWhN,SACrB,+BAcrB,EA7wEA,WACE,MAAOqS,EAAmB0H,IAAwBpY,EAAAA,EAAAA,UAAS,KAEpDuI,EAAW8P,IAAgBrY,EAAAA,EAAAA,UAAS,iBACpCsY,EAAkBC,IAAuBvY,EAAAA,EAAAA,WAAS,IAClDwY,EAAoB9E,IAAyB1T,EAAAA,EAAAA,WAAS,IACtD2T,EAAmB8E,IAAwBzY,EAAAA,EAAAA,UAAS,KAEpD4V,EAAeC,IAAoB7V,EAAAA,EAAAA,WAAS,IAC5C0Y,EAAmB3M,IAAwB/L,EAAAA,EAAAA,WAAS,IACpD2Y,EAAsBC,IAA2B5Y,EAAAA,EAAAA,WAAS,IAC1D6Y,EAAiBC,IAAsB9Y,EAAAA,EAAAA,UAAS,KAChD+Y,EAA4BC,IAAiChZ,EAAAA,EAAAA,UAAS,KACtE0D,EAAiBuV,IAAsBjZ,EAAAA,EAAAA,UAAS,KAChDoD,EAAa8V,IAAkBlZ,EAAAA,EAAAA,UAAS,KACxCqD,EAAiB8V,IAAsBnZ,EAAAA,EAAAA,UAAS,KAChDqM,EAAS+M,IAAcpZ,EAAAA,EAAAA,UAAS,KAEhCP,GAAwBC,KAA6BM,EAAAA,EAAAA,WAAS,IAC9DL,GAAoBqM,KAAyBhM,EAAAA,EAAAA,WAAS,IACtDJ,GAAcqM,KAAmBjM,EAAAA,EAAAA,WAAS,IAC1CH,GAAeqM,KAAoBlM,EAAAA,EAAAA,WAAS,IAC5C+V,GAAOsD,KAAYrZ,EAAAA,EAAAA,WAAS,IAC5BgW,GAAaC,KAAmBjW,EAAAA,EAAAA,WAAS,IAEzCsZ,GAAcC,KAAmBvZ,EAAAA,EAAAA,UAAS,OAC1CwZ,GAAYC,KAAiBzZ,EAAAA,EAAAA,UAAS,KACtC0Z,GAAaC,KAAkB3Z,EAAAA,EAAAA,UAAS,OACxC2F,GAAOiU,KAAY5Z,EAAAA,EAAAA,UAAS,CAAC,IAG7B6Z,GAAgBC,KAAqB9Z,EAAAA,EAAAA,WAAS,IAC9C+Z,GAAcC,KAAmBha,EAAAA,EAAAA,UAAS,OAC1CsD,GAAiB2W,KAAsBja,EAAAA,EAAAA,UAAS,IAChDuD,GAAW2W,KAAgBla,EAAAA,EAAAA,UAAS,IACpCwD,GAAW2W,KAAgBna,EAAAA,EAAAA,aAC3ByD,GAAa2W,KAAkBpa,EAAAA,EAAAA,UAAS,CAAE4E,SAAU,CAAC,EAAGC,OAAQ,KAEjEiE,IAAO8G,EAAAA,EAAAA,MA4Jb,IA1JAzP,EAAAA,EAAAA,YAAU,KACRC,IAAAA,QAAiB,CACfC,cAAe,oBAEjBqG,iBACE,MAAMtK,QAAaie,EAAAA,EAAAA,MACnBT,GAASxd,EACX,CACAke,KACC,KAEHna,EAAAA,EAAAA,YAAU,KAER,IAAIoa,GAAe7b,EAAAA,EAAAA,IAAU,qBAEV6B,IAAfga,GAA2C,KAAfA,GAC9BxX,YAAW,YACTyX,EAAAA,EAAAA,IAAU,eAAe,IACzBpa,IAAAA,MAAama,EACf,GAAG,OAEJ,KAGHpa,EAAAA,EAAAA,YAAU,KACR,QAAaI,IAATuI,GAAmB,CACrB,IAAI2R,EAAiD,OAA5B3R,GAAK2R,mBAA8B,GAAK3R,GAAK2R,mBACtExB,EAAmBwB,GACnBvB,EAAepQ,GAAK/K,MACpBob,EAAmBnW,OAAO6D,UAAUoG,WACpCmM,EAAWtQ,GAAK4R,UAEoB,QAAhC1X,OAAO6D,UAAU8T,YAAyBtC,EAAa,UAC7D,KAA2C,QAAhCrV,OAAO6D,UAAU8T,YAC1BtC,EAAa,aAEd,CAACvP,MAEJ3I,EAAAA,EAAAA,YAAU,UACKI,IAAVoF,KACDuU,GAAavU,GAAMiV,YACnBT,GAAaxU,GAAMkV,YAEpB,CAAClV,MAEJxF,EAAAA,EAAAA,YAAU,UACKI,IAATuI,IAAkC,eAAZA,GAAK/K,WAAiCwC,IAAVoF,KACpDc,EAAkBd,GAAMmV,YACxBvC,GAAoB,MAErB,CAACzP,GAAMnD,MAEVxF,EAAAA,EAAAA,YAAU,KAMJmY,GALc5R,WAChB,IAAIqU,QAAmBlP,IACvBuM,EAAqB2C,IAIrBC,KAED,CAAC1C,KAEJnY,EAAAA,EAAAA,YAAU,KACR,MAAM8a,EAAcvU,UAClB,IAAIwU,QAwIRxU,iBACE,MAAMyU,QAAiB7Y,EAAAA,EAAMC,KAAK,GAAD9D,OAAI+D,4BAA6B,oBAAoB,CACpF,IAAM9D,EAAAA,EAAAA,IAAU,WACf,CAAE+D,QAAS,CAAE,eAAgB,uCAE1BG,EAASuY,EAAS/e,KAAO+e,EAAS/e,KAAO,GAC/C,OAAGwG,EAAOC,QACDD,EAEA,EAEX,CAnJuBwY,QAEC7a,IAAhB2a,EAAO9e,OACT6d,GAAmBiB,EAAO5V,OAE1B8U,GAAe,CACbxV,SAAU,CACR,SAAU,CACRe,MAAOuV,EAAOG,yBACdzV,UAAWsV,EAAOI,mBAEpB,yBAA0B,CACxB3V,MAAOuV,EAAOK,4BACd3V,UAAWsV,EAAOM,kBAEpB,8BAA+B,CAC7B7V,MAAOuV,EAAOO,kCACd7V,UAAWsV,EAAOQ,wBAEpB,aAAc,CACZ/V,MAAOuV,EAAOS,4BACd/V,UAAWsV,EAAOU,sBAEpB,aAAc,CACZjW,MAAOuV,EAAOW,4BACdjW,UAAWsV,EAAOY,sBAEpB,cAAe,CACbnW,MAAOuV,EAAOa,8BACdnW,UAAWsV,EAAOc,wBAEpB,SAAU,CACRrW,MAAOuV,EAAOe,0BACdrW,UAAWsV,EAAOgB,oBAEpB,SAAU,CACRvW,MAAOuV,EAAOiB,0BACdvW,UAAWsV,EAAOkB,oBAEpB,eAAgB,CACdzW,MAAOuV,EAAOmB,mBACdzW,UAAWsV,EAAOoB,kBAEpB,aAAc,CACZ3W,MAAOuV,EAAOqB,iBACd3W,UAAWsV,EAAOsB,gBAEpB,YAAY,CACV7W,MAAOuV,EAAOuB,gBACd7W,UAAWsV,EAAOwB,wBAGtB7X,OAAQqW,EAAO9e,SAKjBgH,GACF6X,IAQe,gBAAd1S,EAA6B0S,IACV,UAAd1S,GANW7B,WACjB,MAAMyU,QAuFVzU,iBACE,MAAMyU,QAAiB7Y,EAAAA,EAAMC,KAAK,GAAD9D,OAAI+D,4BAA6B,gBAAgB,CAChF,IAAM9D,EAAAA,EAAAA,IAAU,WACf,CAAE+D,QAAS,CAAE,eAAgB,uCAE1BG,EAASuY,EAAS/e,KACxB,OAAGwG,EAAOC,QACDD,EAAOxG,KAEP,EAEX,CAlG2BugB,GAEvBhD,GAAewB,IAGcyB,KAC9B,CAACrU,EAAWnF,KAEfjD,EAAAA,EAAAA,YAAU,KAER,GAAmB,OAAhBuZ,GAAqB,CACtB,MAAMmD,EAAc,CAAC,gBAAiB,YAAa,WAE3BC,MACtB,MAEMC,EAAYrD,GAAY5R,MAAM8J,GAClCiL,EAAYnY,SAAwBkN,EAAK/C,MAHLpT,cAAcM,QAAQ,OAAQ,OAKpEsd,GAAS2D,QAAQD,KAGnBD,EACF,IAEA,CAACpD,UAESnZ,IAATuI,GAAoB,OACvB,IAAY,IAATA,GAED,YADA9F,OAAOC,SAASwB,KAAO,UAIzB,MAAMwY,GAAmBC,IACvB7E,EAAa6E,IA8BTvM,GAAsBjK,UAC1B,IAAIqU,QAAmBlP,IACvBuM,EAAqB2C,IAyDvB,OAvBAoC,EAAAA,EAAAA,OAwBEhf,EAAAA,EAAAA,MAAAI,EAAAA,SAAA,CAAAF,SAAA,EACEF,EAAAA,EAAAA,MAACif,EAAAA,EAAM,CAAA/e,SAAA,EACLM,EAAAA,EAAAA,KAAA,SAAAN,SAAO,6BACPM,EAAAA,EAAAA,KAAA,QAAMqL,KAAK,cAAcoJ,QAAQ,6JAEnCzU,EAAAA,EAAAA,KAAC0e,EAAAA,QAAM,CAACvU,KAAMA,GAAMyQ,gBAAiBA,GAAiBE,cAAeA,GAAe1N,qBAAsBA,KAC1GpN,EAAAA,EAAAA,KAAA,OAAKH,UAAU,qDAAoDH,UACjEM,EAAAA,EAAAA,KAAA,OAAKH,UAAU,uCAAsCH,UACnDF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,qCAAoCH,SAAA,EACjDM,EAAAA,EAAAA,KAAA,MAAIH,UAAU,+CAA8CH,SAAC,yBAG7DM,EAAAA,EAAAA,KAAA,OAAKH,UAAU,GAAEH,UACfF,EAAAA,EAAAA,MAAA,MAAIK,UAAU,oCAAmCH,SAAA,CAC5B,KAAlBqF,GACD/E,EAAAA,EAAAA,KAAC0J,EAAO,CACRC,QAAQ,eACRC,UAAWA,EACXvG,QAASib,GAAgB5e,SACxB,iBAGG,GAEHia,GAAsC,KAAlB5U,GACrB/E,EAAAA,EAAAA,KAAC0J,EAAO,CACRC,QAAQ,UACRC,UAAWA,EACXvG,QAASib,GAAgB5e,SACxB,YAGG,GAEe,KAAlBqF,GACD/E,EAAAA,EAAAA,KAAC0J,EAAO,CACNC,QAAQ,QACRC,UAAWA,EACXvG,QAASib,GAAgB5e,SAC1B,YAGG,IAEJM,EAAAA,EAAAA,KAAC0J,EAAO,CACNC,QAAQ,cACRC,UAAWA,EACXvG,QAASib,GAAgB5e,SAC1B,iBAGDM,EAAAA,EAAAA,KAAC0J,EAAO,CACNC,QAAQ,UACRC,UAAWA,EACXvG,QAASib,GAAgB5e,SAC1B,aAGDM,EAAAA,EAAAA,KAAC0J,EAAO,CACNC,QAAQ,OACRC,UAAWA,EACXvG,QAASib,GAAgB5e,SAC1B,UAGDM,EAAAA,EAAAA,KAAC0J,EAAO,CACNC,QAAQ,SACRC,UAAWA,EACXvG,QAASib,GAAgB5e,SAC1B,iBAMLF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,2FAA0FH,SAAA,CACxF,iBAAdkK,IAAgC5J,EAAAA,EAAAA,KAACmN,EAAsB,CAACC,qBAAsBA,EAAsBrM,0BAA2BA,GAA2BsM,sBAAuBA,GAAuBC,gBAAiBA,GAAiBC,iBAAkBA,GAAkBC,SAAUqN,GAAYpN,kBAAmBkN,GAAcjN,QAASA,EAASC,UAlFjVgR,IACjBtD,GAAgBsD,GAChBxD,IAAkB,MAiFO,YAAdvR,IAA2B5J,EAAAA,EAAAA,KAAC4R,EAAiB,CAACE,oBAnK/BA,KAC1B,MAAM8M,EAAiB9W,EAAkBiK,EAAkBxK,OAEvDwK,EAAkBxK,QAAUO,EAC1BiK,EAAkBxK,SAAWsX,OAAO/W,IAAoBsP,GAC1D3V,IAAAA,MAAa,0BAEb6V,IAAgB,GAChBlK,GAAqB,IAEd2E,EAAkBxK,OAAS,GAAK6P,IACzCF,GAAiB,GACbnF,EAAkBxK,OAASO,EAC7BwP,IAAgB,GAGdA,GADqB,IAAnBsH,IAON1H,GAAiB,IA6IgFnF,kBAAmBA,EAAmBC,oBAAqBA,GAAqBC,0BApInJA,CAAC4H,EAAoB7F,KACrDe,GAAsB,GACtB+E,EAAqB9F,IAkIoNvP,YAAaA,EAAaC,gBAAiBA,IAC3P,UAAdkF,IAAyB5J,EAAAA,EAAAA,KAAC8P,EAAe,CAACE,WAAY+K,KACxC,gBAAdnR,IAA+B5J,EAAAA,EAAAA,KAAC8e,EAAiB,CAChDra,YAAaA,EACbC,gBAAiBA,EACjBC,gBAAiBA,GACjBC,UAAWA,GACXC,UAAWA,GACXC,YAAaA,GACbC,gBAAiBA,IAEJ,YAAd6E,IAA2B5J,EAAAA,EAAAA,KAAC8J,EAAiB,CAACK,KAAMA,KACtC,SAAdP,IAAwB5J,EAAAA,EAAAA,KAACqQ,EAAc,IACzB,WAAdzG,IAA0B5J,EAAAA,EAAAA,KAACsQ,EAAa,eAMjDtQ,EAAAA,EAAAA,KAAC4U,EAAe,CAACE,eAAgB+E,EAAoB9E,sBAAuBA,EAAuBC,kBAAmBA,EAAmBhD,oBAAqBA,MAC9JhS,EAAAA,EAAAA,KAAC+W,EAAc,CAACE,cAAeA,EAAeC,iBAAkBA,EAAkBlF,oBAAqBA,GAAqBmF,cAAerP,EAAiBiK,kBAAmBA,EAAmBqF,MAAOA,GAAOC,YAAaA,GAAaC,gBAAiBA,MAC3PtX,EAAAA,EAAAA,KAAC+e,EAAAA,EAAkB,CAAChF,kBAAmBA,EAAmB3M,qBAAsBA,EAAsB+M,mBAAoBA,EAAoBE,8BAA+BA,EAAgCJ,wBAAyBA,KACtOja,EAAAA,EAAAA,KAACgf,EAAAA,EAAsB,CAAC9E,gBAAiBA,EAAiBE,2BAA4BA,EAA4BH,wBAAyBA,EAAyBD,qBAAsBA,KAC1Lha,EAAAA,EAAAA,KAACa,EAAkB,CAACC,uBAAwBA,GAAwBC,0BAA2BA,GAA2BC,mBAAoBA,GAAoBC,aAAcA,GAAcC,cAAeA,MAC7MlB,EAAAA,EAAAA,KAAC4I,EAAU,CACTvG,KAAM6Y,GACNzY,QA5HwBwc,KAC5B9D,IAAkB,IA4HdtS,SAzHYqW,KAChB7a,OAAOC,SAASwB,KAAK,cAAcqC,GAyH/BW,UAtHqBqW,KACzBhE,IAAkB,GACdC,IAAgBA,GAAatS,WAC/BsS,GAAatS,gBAqHb9I,EAAAA,EAAAA,KAACof,EAAAA,QAAM,CAACjV,KAAMA,OAGpB,C,2JCtgBIkV,EAAmB,IACnBvL,EAAU,GAEP,SAASiL,EAAkB5f,GAA0H,IAAxH,kBAAC4a,EAAiB,qBAAE3M,EAAoB,mBAAE+M,EAAkB,8BAAEE,EAA6B,wBAAEJ,GAAwB9a,EACvJ,MAAOmgB,EAAcC,IAAmBle,EAAAA,EAAAA,UAAS,IAC1Cme,EAAaC,IAAkBpe,EAAAA,EAAAA,UAAS,IACxC2H,EAAiB0W,IAAsBre,EAAAA,EAAAA,UAAS,IAEjD8I,GAAO8G,EAAAA,EAAAA,OAEbzP,EAAAA,EAAAA,YAAU,UACKI,IAATuI,GAAkC,eAAZA,EAAK/K,OAC7BigB,EAAmBlV,EAAKkV,iBAEU,YAA9BlV,EAAK7K,SAASxC,cAChB4iB,EAAmB,SAEnBA,EAAmB,WAItB,CAACvV,IAGJ,MAOMwV,EAA2BA,KAC/B,IAAI7d,EAAQC,SAASC,eAAe,uBACxB,OAARF,IACFA,EAAMG,MAAMC,QAAU,OACtBkL,GAAqB,MAczB5L,EAAAA,EAAAA,YAAU,KACRC,IAAAA,QAAiB,CACfC,cAAe,sBAEhB,KAEHF,EAAAA,EAAAA,YAAU,KAERie,EADaH,EAAeD,KAE3B,CAACC,KAEJ9d,EAAAA,EAAAA,YAAU,KACR+d,EAAgB,GAGhBE,EADaH,EAAeD,KAG3B,CAACtF,IAoBJ,YAPwBnY,IAApBmY,IAAuD,IAAtBA,GAvDL6F,MAC9B,IAAI9d,EAAQC,SAASC,eAAe,uBACxB,OAARF,IACFA,EAAMG,MAAMC,QAAU,UAqDxB0d,QAEsBhe,IAApBmY,IAAuD,IAAtBA,GACnC4F,KAIA3f,EAAAA,EAAAA,KAAAJ,EAAAA,SAAA,CAAAF,UACFM,EAAAA,EAAAA,KAAA,OAAKyT,GAAG,sBAAsB5T,UAAU,iBAAgBH,UACvDM,EAAAA,EAAAA,KAAA,OAAKP,MAAM,yDAAwDC,UAC9DF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,0BAAyBH,SAAA,EACtCM,EAAAA,EAAAA,KAAA,OAAKH,UAAU,uDAAsDH,SAAC,sBAGtEM,EAAAA,EAAAA,KAAA,OAAKH,UAAU,wCAAuCH,UACpDM,EAAAA,EAAAA,KAAA,QAAMP,MAAM,QAAQ4D,QAASA,IAAKsc,IAA2BjgB,SAAC,YAGlEF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,gDAA+CH,SAAA,EAC5DM,EAAAA,EAAAA,KAAA,OAAAN,SAAK,gEACLM,EAAAA,EAAAA,KAAA,OAAAN,SAAK,kDACLF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,mBAAkBH,SAAA,EAC/BM,EAAAA,EAAAA,KAAA,QAAMH,UAAU,uDAAsDH,SAAC,SACvEF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,kCAAiCH,SAAA,EAC9CM,EAAAA,EAAAA,KAAA,SAAOH,UAAU,8DAA8DsD,KAAK,SAASC,MAAM,IAAIC,QAASA,KA7DxHic,EAAa,GACfC,EAAgBD,EAAa,OA6DnBtf,EAAAA,EAAAA,KAAA,QAAMH,UAAU,sCAAqCH,SAAE4f,KACvDtf,EAAAA,EAAAA,KAAA,SAAOH,UAAU,8DAA8DsD,KAAK,SAASC,MAAM,IAAIC,QAASA,KAnE5Hkc,EAAgBD,EAAa,UAqEnBtf,EAAAA,EAAAA,KAAA,QAAMH,UAAU,uDAAsDH,SAAC,iBAEzEF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,oBAAmBH,SAAA,CAAC,kBACjB8f,MAElBhgB,EAAAA,EAAAA,MAAA,OAAKK,UAAU,oBAAmBH,SAAA,CAAC,OAC5BsJ,MAEPhJ,EAAAA,EAAAA,KAAA,OAAAN,UACEM,EAAAA,EAAAA,KAAC4M,EAAAA,EAAOC,OAAM,CACdhN,UAAU,oFACV6T,WAAY,CAAEC,gBAAiB,WAC/B7G,SAAU,CAAEC,MAAO,IACnB1J,QAASA,KArDnBgB,OAAOC,SAASwB,KAAO,gBAAgBwZ,GAqDK5f,SACjC,6BAUf,CAEO,SAASsf,EAAsB9e,GAAiG,IAA/F,gBAACga,EAAe,2BAAEE,EAA0B,wBAAEH,EAAuB,qBAAED,GAAqB9Z,EAElI,MAAMiK,GAAO8G,EAAAA,EAAAA,OAEbzP,EAAAA,EAAAA,YAAU,UACKI,IAATuI,GAAkC,eAAZA,EAAK/K,OAC7B0U,EAAU3J,EAAK2J,WAEhB,CAAC3J,IAEJ,MAOM0V,EAAaA,KACjB,IAAI/d,EAAQC,SAASC,eAAe,iBACxB,OAARF,IACFA,EAAMG,MAAMC,QAAU,OACtB+X,GAAwB,KA8C5B,YAP2BrY,IAAvBoY,IAA6D,IAAzBA,GAlDtB8F,MAChB,IAAIhe,EAAQC,SAASC,eAAe,iBACxB,OAARF,IACFA,EAAMG,MAAMC,QAAU,UAgDxB4d,QAEyBle,IAAvBoY,IAA6D,IAAzBA,GACtC6F,KAIA7f,EAAAA,EAAAA,KAAAJ,EAAAA,SAAA,CAAAF,UACAM,EAAAA,EAAAA,KAAA,OAAKyT,GAAG,gBAAgB5T,UAAU,iBAAgBH,UAChDF,EAAAA,EAAAA,MAAA,OAAKC,MAAM,4EAA2EC,SAAA,EACpFM,EAAAA,EAAAA,KAAA,QAAMP,MAAM,QAAQ4D,QAASA,IAAKwc,IAAangB,SAAC,OAChDM,EAAAA,EAAAA,KAAA,OAAKH,UAAU,sCAAqCH,UAACM,EAAAA,EAAAA,KAAA,OAAKuJ,IAAKC,EAAAA,EAAWC,IAAI,cAAc5J,UAAU,yBACtGL,EAAAA,EAAAA,MAAA,MAAIK,UAAU,qEAAoEH,SAAA,CAAC,mBAAeM,EAAAA,EAAAA,KAAA,SAAK,2BACvGA,EAAAA,EAAAA,KAAA,OAAKH,UAAU,yCAAwCH,SAAC,kEACxDM,EAAAA,EAAAA,KAAA,OAAKH,UAAU,yCAAwCH,SAAC,sFACxDF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,OAAMH,SAAA,EACnBF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,wBAAuBH,SAAA,CAAC,mBAAiBwa,MACxD1a,EAAAA,EAAAA,MAAA,OAAKK,UAAU,wBAAuBH,SAAA,CAAC,wBAAsB0a,SAE/D5a,EAAAA,EAAAA,MAAA,OAAKK,UAAU,+DAA8DH,SAAA,EAC3EM,EAAAA,EAAAA,KAAA,OAAKH,UAAU,YAAWH,SAAC,sBAC3BM,EAAAA,EAAAA,KAAA,OAAKH,UAAU,4EAA4EwD,QAASA,KAAK0c,OAnD3G1O,EAAU6I,EACV8F,EAAe5F,EACf/D,EAAG,GAAAvW,OAAM+D,4BAA6B,mCAE1Cgc,IACA9d,SAASyB,cAAc,qBAAqBC,UAAUC,IAAI,eAE1DC,EAAAA,EAAMC,KAAKyS,EAAK,CACdhF,UACA2O,gBACC,CAAElc,QAAS,CAAE,eAAgB,uCAAyCC,MAAK,SAASC,GACrF,IAAIC,EAASD,EAAIvG,KAEjBsE,SAASyB,cAAc,qBAAqBC,UAAUU,OAAO,UAE1DF,EAAOC,QACRzC,IAAAA,QAAe,iBAAiBwC,EAAOxG,MAEvCgE,IAAAA,MAAa,gBAEjB,IAAGoV,OAAM,SAAUD,GACjB7U,SAASyB,cAAc,qBAAqBC,UAAUU,OAAO,UACzDyS,EAAM4F,UAAoC,MAAxB5F,EAAM4F,SAASnT,SACnCtH,SAASyB,cAAc,qBAAqBC,UAAUU,OAAO,UAC7D1C,IAAAA,MAAa,wDAEjB,IA3BmBse,IACf1O,EACA2O,EACA3J,GAiD0H3W,SAAC,oBACzHF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EAAKM,EAAAA,EAAAA,KAAA,QAAMH,UAAU,YAAWH,SAAC,iBAAmB,uCACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EAAKM,EAAAA,EAAAA,KAAA,QAAMH,UAAU,YAAWH,SAAC,WAAa,gBAC9CF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EAAKM,EAAAA,EAAAA,KAAA,QAAMH,UAAU,YAAWH,SAAC,eAAiB,uBAClDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EAAKM,EAAAA,EAAAA,KAAA,QAAMH,UAAU,YAAWH,SAAC,oBAAsB,iBACvDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EAAKM,EAAAA,EAAAA,KAAA,QAAMH,UAAU,YAAWH,SAAC,yCAA2C,iBAC5EF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EAAKM,EAAAA,EAAAA,KAAA,QAAMH,UAAU,YAAWH,SAAC,oBAAsB,sBACvDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EAAKM,EAAAA,EAAAA,KAAA,QAAMH,UAAU,YAAWH,SAAC,qBAAuB,IAAEoU,MAC1DtU,EAAAA,EAAAA,MAAA,OAAKK,UAAU,kDAAiDH,SAAA,EAACM,EAAAA,EAAAA,KAAC6P,EAAAA,IAAY,CAAChQ,UAAU,wBAAuB,0HAGlHG,EAAAA,EAAAA,KAAA,OAAKH,UAAU,8CAA6CH,SAAC,8IAG7DF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,yCAAwCH,SAAA,CAAC,iBACzCM,EAAAA,EAAAA,KAAA,KAAAN,SAAG,qBAAoB,kDAEtCM,EAAAA,EAAAA,KAAA,OAAKH,UAAU,cAAaH,UAC1BM,EAAAA,EAAAA,KAAC4M,EAAAA,EAAOC,OAAM,CACZhN,UAAU,sEACV6T,WAAY,CAAEC,gBAAiB,WAC/B7G,SAAU,CAAEC,MAAO,IACnB1J,QA/EuB4c,KAC/B5b,OAAOC,SAASwB,KAAO,sBA8EmBpG,SACnC,sCAQX,C", "sources": ["core/utils/main.jsx", "modal/manage.jsx", "manage-account/TabContent/TokenUsageContent.jsx", "manage-account/index.jsx", "modal/enterprise.jsx"], "sourcesContent": ["import { GetCookie } from '../../core/utils/cookies';\r\nexport function getCurrency(currency) {\r\n    if(!currency) return \"\";\r\n    if(currency.toLowerCase() === 'usd') return \"$\";\r\n    if(currency.toLowerCase() === 'eur') return \"€\";\r\n    if(currency.toLowerCase() === 'gbp') return \"£\";\r\n    if(currency.toLowerCase() === 'brl') return \"R$\";\r\n    if(currency.toLowerCase() === 'sar') return \"R$\";\r\n    if(currency.toLowerCase() === 'czk') return \"Kč\";\r\n    return \"\";\r\n}\r\n\r\nexport function getCountry(currency) {\r\n    if(!currency) return \"\";\r\n    if(currency.toLowerCase() === 'usd') return \"US\";\r\n    if(currency.toLowerCase() === 'eur') return \"US\";\r\n    if(currency.toLowerCase() === 'gbp') return \"GB\";\r\n    if(currency.toLowerCase() === 'brl') return \"US\";\r\n    if(currency.toLowerCase() === 'sar') return \"AE\";\r\n    if(currency.toLowerCase() === 'chf') return \"CH\";\r\n    if(currency.toLowerCase() === 'sek') return \"SE\";\r\n    return \"US\";\r\n}\r\n\r\nexport function formatDate(dateStr) {\r\n    if(!dateStr) return \"\";\r\n    const month = [\"January\",\"February\",\"March\",\"April\",\"May\",\"June\",\"July\",\"August\",\"September\",\"October\",\"November\",\"December\"];\r\n    const date = new Date(dateStr.replace(/ /g,\"T\"));\r\n\r\n    return month[date.getMonth()] + \" \" + date.getDate() + \" \" + date.getFullYear();\r\n}\r\n\r\nexport function getPrice(data) {\r\n    if(data.trial_price) return data.trial_price;\r\n    if(data.price) return data.price;\r\n    return \"0\";\r\n}\r\n\r\nexport function numberWithCommas(x) {\r\n    return x.toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, \",\");\r\n}\r\n\r\nexport function formatNumber(number) {\r\n  const floatNumber = parseFloat(number);\r\n  return (floatNumber % 1 === 0)\r\n      ? numberWithCommas(floatNumber.toFixed(0))\r\n      : numberWithCommas(floatNumber.toFixed(2)); \r\n}\r\n\r\nexport function getPricePlan(currency, price) {\r\n    if(!currency || !price) return \"\";\r\n    // Check if price is a number\r\n    if(isNaN(price)) return \"\";\r\n    // Check if price is positive number\r\n    if(parseFloat(price) >= 0) {\r\n        if(currency.toLowerCase() === 'sar') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"ر.س.\";\r\n        } else if(currency.toLowerCase() === 'aed') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"AED\";\r\n        } else if(currency.toLowerCase() === 'chf') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"CHF\";\r\n        } else if(currency.toLowerCase() === 'sek') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"SEK\";\r\n        } else if(currency.toLowerCase() === 'pln') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"zł\";\r\n        }else if(currency.toLowerCase() === 'ron') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"LEI\";\r\n        } else if(currency.toLowerCase() === 'huf') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"Ft\";\r\n        } else if(currency.toLowerCase() === 'dkk') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"kr.\";\r\n        } else if(currency.toLowerCase() === 'bgn') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"лв\";\r\n        } else if(currency.toLowerCase() === 'try') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"₺\";\r\n        }\r\n        return getCurrency(currency) + formatNumber(price).toLocaleString(\"en-US\");\r\n    }\r\n    // Negative number\r\n    return \"-\" + getCurrency(currency) + (formatNumber(price) * -1).toLocaleString(\"en-US\");\r\n}\r\n\r\nexport function diffMin(dt1, dt2)\r\n{\r\n    dt1 = new Date(dt1);\r\n    dt2 = new Date(dt2);\r\n    var diff =(dt2.getTime() - dt1.getTime()) / 1000;\r\n    diff /= 60;\r\n    return Math.abs(Math.round(diff));\r\n}\r\n\r\nexport function formatDateTime(dateStr) {\r\n    if(!dateStr) return \"\";\r\n    const month = [\"January\",\"February\",\"March\",\"April\",\"May\",\"June\",\"July\",\"August\",\"September\",\"October\",\"November\",\"December\"];\r\n    const date = new Date(dateStr.replace(/ /g,\"T\"));\r\n\r\n    return month[date.getMonth()] + \" \" + date.getDate() + \" \" + date.getFullYear() + \" \" + date.getHours() + \":\" + date.getMinutes();\r\n}\r\n\r\nexport function DailyPrice({plan}) {\r\n  let dailyPrice = '';\r\n  let interval = '';\r\n  if (plan.payment_interval === 'Yearly') {\r\n    dailyPrice = getPricePlan(plan.currency, parseFloat(plan.price / 365).toFixed(2));\r\n    interval = <div class=\"text-xs mb-4\">billed {getPricePlan(plan.currency, plan.price)}/year</div>;\r\n  }\r\n\r\n  if (plan.payment_interval === 'Monthly') {\r\n    dailyPrice = getPricePlan(plan.currency, parseFloat(plan.price / 30).toFixed(2));\r\n    interval = <div class=\"text-xs mb-4\">billed {getPricePlan(plan.currency, plan.price)}/Month</div>;\r\n  }\r\n\r\n  if (plan.trial_price) {\r\n    dailyPrice = getPricePlan(plan.currency, parseFloat(plan.trial_price / plan.trial_days).toFixed(2));\r\n    interval = <div class=\"text-xs mb-4\">billed {getPricePlan(plan.currency, plan.trial_price)}</div>;\r\n  }\r\n\r\n  return (\r\n    <>\r\n      <p className={`text-4xl font-bold text-gray-800 ${(GetCookie(\"p_toggle\") === '') ? 'mb-4' : ''}`}>\r\n        {dailyPrice} <span className=\"text-sm\"> per Day</span>\r\n      </p>\r\n      {interval}\r\n    </>\r\n  );\r\n}\r\n\r\nexport function PriceFormatted({plan}) {\r\n  if (GetCookie(\"daily\") === 'on') {\r\n    return DailyPrice({plan});\r\n  } \r\n  if (plan.trial_price) {\r\n    return (<p className=\"text-4xl font-bold text-gray-800 mb-4\">{getPricePlan(plan.currency, plan.trial_price)}</p>)\r\n  } \r\n  return (\r\n    <p className=\"text-4xl font-bold text-gray-800 mb-4\">\r\n      {getPricePlan(plan.currency, plan.price)}\r\n      <span className=\"text-sm\"> \r\n        /{ plan.payment_interval === \"Monthly\" ? \"month\" : \"year\" }\r\n      </span>\r\n    </p>\r\n  )\r\n}\r\n\r\nexport function displayTextFormatted(plan) {\r\n  const locales = (GetCookie('locales') ?? 'en').toLowerCase();\r\n  const daily = (GetCookie('daily') ?? 'off').toLowerCase();\r\n  const { trial_days, payment_interval, trial_price, currency, currency_symbol } = plan;\r\n  let { display_txt2: pricing_description, price } = plan;\r\n\r\n  if (trial_days > 0 && trial_price > 0 && locales === 'en') {\r\n    const per_day = payment_interval === 'Yearly' ? 365 : 30;\r\n    let amount = price;\r\n    let price_text = 'month';\r\n\r\n    if (daily === 'on') {\r\n      amount = parseFloat(price / per_day).toFixed(2);\r\n      price_text = `day<br>(billed ${currency_symbol + price} ${payment_interval})`;\r\n    }\r\n\r\n    pricing_description += `<div>${trial_days}-Day Trial, then only ${getPricePlan(currency, amount)} per ${price_text}</div>`;\r\n  }\r\n\r\n  return pricing_description;\r\n}", "import { useEffect, useState, Fragment } from 'react';\r\nimport { GetCookie } from '../core/utils/cookies';\r\nimport axios from 'axios';\r\nimport toastr from 'toastr';\r\nimport 'toastr/build/toastr.min.css';\r\nimport { Dialog, Transition } from '@headlessui/react';\r\n\r\nexport function PausedAccountModal ({showPausedAccountModal, setShowPausedAccountModal, userSubscriptionID, userMerchant, userAccountID}) {\r\n  const [billingCycle, setbillingCycle] = useState(1);\r\n  const [buttonDisable, setButtonDisable] = useState(false);\r\n\r\n  useEffect(() => {\r\n    toastr.options = {\r\n      positionClass: 'toast-top-center'\r\n    };\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    if (billingCycle>=5){\r\n      setButtonDisable(true);\r\n    }else{\r\n      setButtonDisable(false);\r\n    }\r\n  }, [billingCycle]);\r\n\r\n\r\n  const modalPausedAccountOpen = () => {\r\n    let modal = document.getElementById(\"modalPausedAccount\");\t\t\r\n    if (modal!==null){\r\n      modal.style.display = \"block\";\r\n      setShowPausedAccountModal(true);\r\n    }\r\n  }\r\n\r\n  const modalPausedAccountClose = () => {\r\n    // let modal = document.getElementById(\"modalPausedAccount\");\t\t\r\n    // if (modal!==null){\r\n    //   modal.style.display = \"none\";\r\n    //   setShowPausedAccountModal(false);\r\n    // }\r\n    setShowPausedAccountModal(false);\r\n  }\r\n\r\n  const handlePlus = () => {\r\n    setbillingCycle(billingCycle+1);\r\n  }\r\n\r\n  const handleMinus = () => {\r\n    if (billingCycle>1){\r\n      setbillingCycle(billingCycle-1);\r\n    }\r\n  }\r\n\r\n\r\n  const handlePause = () => {\r\n    modalPausedAccountClose();\r\n    document.querySelector(\".loader-container\").classList.add('active');\r\n\r\n    axios.post(`${process.env.REACT_APP_API_URL}/pause-subscription`, {\r\n      'tk': GetCookie('access'),\r\n      'subscription_id': userSubscriptionID,\r\n      'merchant': userMerchant,\r\n      'billing_cycle' : billingCycle,\r\n      'account_pid' : userAccountID,\r\n    }, { headers: { 'content-type': 'application/x-www-form-urlencoded' } }).then(function(res){\r\n      let output = res.data;\r\n      if(output.success) {\r\n        document.querySelector(\".loader-container\").classList.remove('active');\r\n        toastr.success('Success!<br> Your subscription is now paused.');\r\n        setTimeout(function(){ \r\n          window.location.reload();          \r\n        }, 1000);        \r\n      } else {\r\n        document.querySelector(\".loader-container\").classList.remove('active');\r\n        toastr.error(output.data.msg);\r\n      }  \r\n    });\r\n  }\r\n  \r\n  if (showPausedAccountModal!==undefined && showPausedAccountModal === true){\r\n    modalPausedAccountOpen();\r\n  }\r\n  if (showPausedAccountModal!==undefined && showPausedAccountModal === false){\r\n    modalPausedAccountClose();\r\n  }\r\n\r\n  return (\r\n    <>\r\n\r\n<Transition appear show={showPausedAccountModal} as={Fragment}>\r\n        <Dialog as=\"div\" className=\"relative z-10\" onClose={()=> modalPausedAccountClose()}>\r\n          <Transition.Child\r\n            as={Fragment}\r\n            enter=\"ease-out duration-300\"\r\n            enterFrom=\"opacity-0\"\r\n            enterTo=\"opacity-100\"\r\n            leave=\"ease-in duration-200\"\r\n            leaveFrom=\"opacity-100\"\r\n            leaveTo=\"opacity-0\"\r\n          >\r\n            <div className=\"fixed inset-0 bg-black bg-opacity-25\" />\r\n          </Transition.Child>\r\n\r\n          <div className=\"fixed inset-0 overflow-y-auto\">\r\n            <div className=\"flex min-h-full items-center justify-center p-4 text-center\">\r\n              <Transition.Child\r\n                as={Fragment}\r\n                enter=\"ease-out duration-300\"\r\n                enterFrom=\"opacity-0 scale-95\"\r\n                enterTo=\"opacity-100 scale-100\"\r\n                leave=\"ease-in duration-200\"\r\n                leaveFrom=\"opacity-100 scale-100\"\r\n                leaveTo=\"opacity-0 scale-95\"\r\n              >\r\n                <Dialog.Panel className=\"w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all\">\r\n                  <Dialog.Title\r\n                    as=\"h3\"\r\n                    className=\"text-lg font-medium leading-6 text-gray-900\"\r\n                  >\r\n                    Pause Subscription\r\n                  </Dialog.Title>\r\n                  <div className=\"mt-2\">\r\n                    <div className=\"text-sm text-gray-500\">\r\n                    You're about to pause your subscription. Please select how many billing cycles you'd like it to be paused.\r\n                    </div>\r\n                    <div className='text-center'>\r\n                      <input className=\"bg-gray-400 rounded px-3 py-2 m-2 text-white cursor-pointer\" type=\"button\" value=\"-\" onClick={()=>handleMinus()}/>\r\n                      <span className=\"text-black p-2 mx-auto font-bold\">{billingCycle}</span>\r\n                      <input className=\"bg-gray-400 rounded px-3 py-2 m-2 text-white cursor-pointer\" type=\"button\" value=\"+\" onClick={()=>handlePlus()} disabled={buttonDisable}></input>\r\n                    </div>\r\n                  </div>\r\n\r\n                  <div className=\"mt-4 text-right\">\r\n                    <button\r\n                      type=\"button\"\r\n                      className=\"inline-flex justify-center rounded-md border border-transparent bg-red-500 px-4 py-2 mr-2 text-sm font-medium text-white hover:bg-[#2563eb]\"\r\n                      onClick={handlePause}\r\n                    >\r\n                      Confirm\r\n                    </button>\r\n                    <button\r\n                      type=\"button\"\r\n                      className=\"inline-flex justify-center rounded-md border border-transparent bg-gray-100 px-4 py-2 text-sm font-medium text-black hover:bg-gray-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-gray-500 focus-visible:ring-offset-2\"\r\n                      onClick={()=> modalPausedAccountClose()}\r\n                    >\r\n                      Close\r\n                    </button>\r\n                  </div>\r\n                </Dialog.Panel>\r\n              </Transition.Child>\r\n            </div>\r\n          </div> \r\n        </Dialog>\r\n      </Transition>\r\n    </>\r\n  );\r\n}\r\n\r\n", "import { useEffect, useState } from 'react'\r\nimport { GetCookie } from '../../core/utils/cookies'\r\n\r\nconst TokenUsageContent = ({\r\n    currentPlan,\r\n    currentPlanName,\r\n    totalTokenUsage,\r\n    maxTokens,\r\n    resetDate,\r\n    tokenUsages,\r\n    entParentUserID,\r\n}) => {\r\n    const [subdomain, setSubdomain] = useState('')\r\n    const [tokenUsagesPerModel, setTokenUsagesPerModel] = useState([])\r\n    const [tokenUsagesPerApp, setTokenUsagesPerApp] = useState([])\r\n\r\n    currentPlan = currentPlan ? currentPlan.toLowerCase() : ''\r\n    currentPlanName = currentPlanName ? currentPlanName.toLowerCase() : ''\r\n\r\n    const isUnpaid = currentPlan === ''\r\n    const isBasic = currentPlan === 'basic'\r\n    const isPro = currentPlan === 'pro'\r\n    const isProMax = currentPlan === 'promax'\r\n    const isEnterprise = currentPlan === 'enterprise'\r\n    const isAdvanced = currentPlan === 'advanced'\r\n    const isClusterPlan = isEnterprise && currentPlanName !== currentPlan\r\n    const isStaging = window.location.href.includes('staging')\r\n    const auth_version = GetCookie('auth_version') === 'v1'\r\n\r\n    useEffect(() => {\r\n        if (isStaging) {\r\n            setSubdomain('staging.')\r\n        }\r\n    }, [isStaging])\r\n\r\n    useEffect(() => {\r\n        const { perModel, perApp } = tokenUsages\r\n\r\n        let filtered = Object.fromEntries(\r\n            Object.entries(perModel).filter((row) => {\r\n                const [model] = row\r\n\r\n                switch (model) {\r\n                    case 'DALL·E Image Generated':\r\n                    case 'Claude 3.5':\r\n                        return !isEnterprise\r\n                    case 'GPT Image 1 Image Generated':\r\n                        return !isBasic\r\n                    case 'Kling 1.6':\r\n                        return !isBasic && !auth_version \r\n                    case 'Claude 3.7':\r\n                    case 'Grok 3':\r\n                        return !isBasic && !isEnterprise\r\n                    case 'Grok 4':\r\n                        return !isBasic\r\n                    case 'Deepseek R1':\r\n                        return isPro || isProMax\r\n                    case 'o1 Prompts':\r\n                        return isClusterPlan\r\n                    default:\r\n                        return true\r\n                }\r\n            })\r\n        )\r\n\r\n        setTokenUsagesPerModel(filtered)\r\n        setTokenUsagesPerApp(perApp)\r\n    }, [\r\n        isBasic,\r\n        isPro,\r\n        isProMax,\r\n        isEnterprise,\r\n        isClusterPlan,\r\n        tokenUsages,\r\n        currentPlan,\r\n        currentPlanName,\r\n        auth_version,\r\n    ])\r\n\r\n    const convertToLocaleString = (total) => {\r\n        return parseInt(total).toLocaleString('en-US')\r\n    }\r\n\r\n    const totalTokenUsageText = () => {\r\n        let text = convertToLocaleString(totalTokenUsage)\r\n\r\n        if (!isProMax && !isClusterPlan) {\r\n            text += ` out of ${convertToLocaleString(maxTokens)}`\r\n        }\r\n\r\n        return `TOTAL TOKEN USAGE: ${text}`.trim()\r\n    }\r\n\r\n    const hasMaxUsage = (model) => {\r\n        let hasMaxUsage = true\r\n\r\n        switch (model) {\r\n            case 'GPT-4o':\r\n                if (!isBasic) {\r\n                    hasMaxUsage = false\r\n                }\r\n                break\r\n            case 'GPT Image 1 Image Generated':\r\n            case 'Kling 1.6':\r\n            case 'Flux Prompts':\r\n                hasMaxUsage = true\r\n                break\r\n            case 'Claude 3.5':\r\n                if (isAdvanced || isProMax) {\r\n                    hasMaxUsage = false\r\n                }\r\n                break\r\n            default:\r\n                if (isProMax) {\r\n                    hasMaxUsage = false\r\n                }\r\n                break\r\n        }\r\n\r\n        return hasMaxUsage\r\n    }\r\n\r\n    const isMaxedTokenUsage = () => {\r\n        return (\r\n            !isUnpaid &&\r\n            !isProMax &&\r\n            parseInt(totalTokenUsage) >= parseInt(maxTokens)\r\n        )\r\n    }\r\n\r\n    const isMaxedUsage = (model, usage, max_usage) => {\r\n        const withLimit = hasMaxUsage(model)\r\n\r\n        if (withLimit) {\r\n            return !isUnpaid && parseInt(usage) >= parseInt(max_usage)\r\n        }\r\n\r\n        return false\r\n    }\r\n\r\n    const tokenUsageText = (model, usage, max_usage) => {\r\n        let text = convertToLocaleString(usage)\r\n        const withLimit = hasMaxUsage(model)\r\n\r\n        if (withLimit) {\r\n            text += ` out of ${convertToLocaleString(max_usage)}`\r\n        }\r\n\r\n        return text.trim()\r\n    }\r\n\r\n    return (\r\n        <div className=\"w-full\">\r\n            <div className=\"mb-6\">\r\n                <div className=\"text-xl mb-2\">\r\n                    <strong\r\n                        className={`font-medium ${\r\n                            isMaxedTokenUsage() ? 'text-[#db7e00]' : ''\r\n                        }`}>\r\n                        {totalTokenUsageText()}\r\n                    </strong>\r\n                </div>\r\n\r\n                {!isUnpaid && (\r\n                    <div className=\"text-sm text-gray-600 mb-2\">\r\n                        Token count will reset on: {resetDate}\r\n                    </div>\r\n                )}\r\n\r\n                {isMaxedTokenUsage() && (\r\n                    <div className={`flex gap-4 items-start flex-col ${!isEnterprise ? 'sm:items-center sm:flex-row' : 'lg:items-center lg:flex-row'}`}>\r\n                        {entParentUserID === '' &&\r\n                            (!isEnterprise ? (\r\n                                <>\r\n                                    <div className=\"text-[#db7e00]\">\r\n                                        UPGRADE is required to continue\r\n                                    </div>\r\n\r\n                                    <a\r\n                                        href=\"/upgrade/?mx=1\"\r\n                                        class=\"font-bold text-white py-2 px-4 rounded-md bg-[#db7e00] hover:bg-[#f99d1f]\">\r\n                                        Upgrade\r\n                                    </a>\r\n                                </>\r\n                            ) : (\r\n                                <>\r\n                                    <div className=\"text-[#db7e00]\">\r\n                                        For continuous access, kindly reach out\r\n                                        to our support team\r\n                                    </div>\r\n\r\n                                    <a\r\n                                        href={`https://${subdomain}ai-pro.org/contact-us`}\r\n                                        class=\"font-bold text-white py-2 px-4 rounded-md bg-[#db7e00] hover:bg-[#f99d1f]\">\r\n                                        Contact Support\r\n                                    </a>\r\n                                </>\r\n                            ))}\r\n                    </div>\r\n                )}\r\n            </div>\r\n\r\n            <div className=\"flex gap-6 flex-col md:flex-row md:items-start\">\r\n                <div className=\"flex-1 bg-gray-50 rounded-lg shadow-md\">\r\n                    <div className=\"px-4 py-4 border-b border-gray-200\">\r\n                        <strong className=\"text-gray-900 font-medium uppercase\">\r\n                            Token Usage by Model\r\n                        </strong>\r\n                    </div>\r\n\r\n                    <table className=\"w-full\">\r\n                        <thead className=\"border-b border-gray-200\">\r\n                            <tr>\r\n                                <th className=\"px-4 py-3 text-left font-medium text-gray-900 w-1/2\">\r\n                                    Model\r\n                                </th>\r\n                                <th className=\"px-4 py-3 text-center font-medium text-gray-900 w-1/2\">\r\n                                    Token Usage\r\n                                </th>\r\n                            </tr>\r\n                        </thead>\r\n                        <tbody className=\"divide-y divide-gray-200\">\r\n                            {tokenUsagesPerModel &&\r\n                            Object.keys(tokenUsagesPerModel).length > 0 ? (\r\n                                <>\r\n                                    {Object.entries(tokenUsagesPerModel).map(\r\n                                        (\r\n                                            [model, { usage, max_usage }],\r\n                                            index\r\n                                        ) => (\r\n                                            <tr key={index}>\r\n                                                <td className=\"px-4 py-3 text-sm text-left text-gray-900\">\r\n                                                    {model}\r\n                                                </td>\r\n                                                <td\r\n                                                    className={`px-4 py-3 text-sm text-center ${\r\n                                                        isMaxedUsage(\r\n                                                            model,\r\n                                                            usage,\r\n                                                            max_usage\r\n                                                        )\r\n                                                            ? 'text-[#db7e00]'\r\n                                                            : 'text-gray-900'\r\n                                                    }`}>\r\n                                                    {tokenUsageText(\r\n                                                        model,\r\n                                                        usage,\r\n                                                        max_usage\r\n                                                    )}\r\n                                                </td>\r\n                                            </tr>\r\n                                        )\r\n                                    )}\r\n                                </>\r\n                            ) : (\r\n                                <tr>\r\n                                    <td\r\n                                        colspan=\"2\"\r\n                                        className=\"px-4 py-3 text-sm text-center font-medium text-gray-900\">\r\n                                        No data available.\r\n                                    </td>\r\n                                </tr>\r\n                            )}\r\n                        </tbody>\r\n                    </table>\r\n                </div>\r\n\r\n                <div className=\"flex-1 bg-gray-50 rounded-lg shadow-md\">\r\n                    <div className=\"px-4 py-4 border-b border-gray-200\">\r\n                        <strong className=\"text-gray-900 font-medium uppercase\">\r\n                            Token Usage by App\r\n                        </strong>\r\n                    </div>\r\n\r\n                    <table className=\"w-full\">\r\n                        <thead className=\"border-b border-gray-200\">\r\n                            <tr>\r\n                                <th className=\"px-4 py-3 text-left font-medium text-gray-900 w-1/2\">\r\n                                    App\r\n                                </th>\r\n                                <th className=\"px-4 py-3 text-center font-medium text-gray-900 w-1/2\">\r\n                                    Token Usage\r\n                                </th>\r\n                            </tr>\r\n                        </thead>\r\n                        <tbody className=\"divide-y divide-gray-200\">\r\n                            {tokenUsagesPerApp &&\r\n                            Object.keys(tokenUsagesPerApp).length > 0 ? (\r\n                                <>\r\n                                    {Object.entries(tokenUsagesPerApp).map(\r\n                                        ([app, total_token], index) => (\r\n                                            <tr key={index}>\r\n                                                <td className=\"px-4 py-3 text-sm text-left text-gray-900\">\r\n                                                    {app}\r\n                                                </td>\r\n                                                <td className=\"px-4 py-3 text-sm text-center text-gray-900\">\r\n                                                    {convertToLocaleString(\r\n                                                        total_token\r\n                                                    )}\r\n                                                </td>\r\n                                            </tr>\r\n                                        )\r\n                                    )}\r\n                                </>\r\n                            ) : (\r\n                                <tr>\r\n                                    <td\r\n                                        colspan=\"2\"\r\n                                        className=\"px-4 py-3 text-sm text-center font-medium text-gray-900\">\r\n                                        No data available.\r\n                                    </td>\r\n                                </tr>\r\n                            )}\r\n                        </tbody>\r\n                    </table>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    )\r\n}\r\n\r\nexport default TokenUsageContent\r\n", "import React, { Fragment, useEffect, useState } from 'react';\r\nimport './style.css';\r\nimport { motion } from \"framer-motion\";\r\nimport Header from '../header';\r\nimport Footer from '../footer';\r\nimport { Auth, checkUsage } from '../core/utils/auth';\r\nimport { Get<PERSON>ookie, Remove<PERSON>ookie, SetCookie } from '../core/utils/cookies';\r\nimport { AiFillWarning } from \"react-icons/ai\";\r\nimport { FaInfoCircle, FaTrash, FaRegCopy } from 'react-icons/fa';\r\nimport axios from 'axios';\r\nimport { getPricePlan, formatDate, formatDateTime, diffMin } from '../core/utils/main';\r\nimport { Dialog, Menu, Transition } from '@headlessui/react';\r\nimport { Helmet } from 'react-helmet';\r\nimport { install } from \"resize-observer\";\r\nimport toastr from 'toastr';\r\nimport 'toastr/build/toastr.min.css';\r\nimport { AddMoreMemberModal } from '../modal/enterprise';\r\nimport { MemberCompletePurchase } from '../modal/enterprise';\r\nimport { PausedAccountModal } from '../modal/manage';\r\nimport aiproLogo from \"../assets/images/AIPRO.svg\";\r\nimport { useQuery } from \"react-query\";\r\nimport TokenUsageContent from './TabContent/TokenUsageContent'\r\n\r\nvar ent_max_members = 0;\r\nvar plan = null;\r\n\r\nasync function getPlan() {\r\n  if(plan) return plan;\r\n  const response = await axios.post(\r\n    `${process.env.REACT_APP_API_URL}/get-subscription`,\r\n    { tk: GetCookie(\"access\") },\r\n    { headers: { \"content-type\": \"application/x-www-form-urlencoded\" } }\r\n  );\r\n  const output = response.data;\r\n  if(output.success) {\r\n    plan = output.data;\r\n    return output.data;\r\n  } else {\r\n    return [];\r\n  }\r\n}\r\n\r\nconst view_data = window.view_data;\r\nconst dis_plan_id = view_data.offer ? view_data.offer.plan_id : \"\";\r\nconst dis_price = view_data.offer ? view_data.offer.price : \"\";\r\nconst dis_currency = view_data.offer ? view_data.offer.currency : \"\";\r\nconst dis_cancel = view_data.offer ? view_data.offer.cancel : \"\";\r\n// const dis_plan_type = view_data.offer ? view_data.offer.plan_type : \"\";\r\nconst dis_percentage = view_data.offer ? view_data.offer.percentage : \"\";\r\n\r\nconst OfferModal = ({ show, onClose, onAccept, onDecline }) => {\r\n  const { data } = useQuery(\"users\", getPlan);\r\n  const [paymentInterval, setPaymentInterval] = useState(\"MONTH\");\r\n\r\n  useEffect(() => {\r\n    if (data?.length > 0) {\r\n      const activeSub = data.find(sub => sub?.status === 'active');\r\n      if (activeSub?.payment_interval) {\r\n        setPaymentInterval(activeSub.payment_interval.toUpperCase() === 'YEARLY' ? 'YEAR' : 'MONTH');\r\n      }\r\n    }\r\n  }, [data]);\r\n\r\n  if (!show) return null;\r\n  \r\n  return (\r\n    <>\r\n      <div\r\n        className=\"popup-overlay fixed top-0 left-0 w-full h-full bg-black opacity-50 z-[8889] block\"\r\n        onClick={onClose}\r\n      />\r\n      <div className=\"popup-modal fixed top-0 bottom-0 left-0 right-0 mx-auto my-auto w-[90%] max-w-[432px] h-fit bg-white p-4 sm:p-6 md:p-8 rounded-md shadow-lg overflow-auto block z-[9999]\">\r\n        {/* X Close Button */}\r\n        <button\r\n          className=\"absolute top-2 right-4 text-gray-300 hover:text-gray-700 text-2xl font-bold focus:outline-none z-[10000]\"\r\n          onClick={onClose}\r\n          aria-label=\"Close\"\r\n        >\r\n          &times;\r\n        </button>\r\n        <div className=\"space-y-4 sm:space-y-7\">\r\n          <img\r\n            src={aiproLogo}\r\n            alt=\"AI-Pro Logo\"\r\n            className=\"aiprologo mx-auto w-[150px] sm:w-[180px] md:w-[200px]\"\r\n          />\r\n          <div>\r\n            <div className=\"text-center text-2xl sm:text-3xl md:text-4xl font-bold mb-0.5\">\r\n              {dis_percentage}% off forever\r\n            </div>\r\n            <div className=\"text-center text-sm sm:text-base\">\r\n              Keep your account for {dis_percentage}% off forever\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"w-[calc(100%+2rem)] sm:w-[calc(100%+3rem)] md:w-[calc(100%+4rem)] bg-gradient-to-r max-h-[79px] from-[#3E53B8] to-[#2393F1] -mx-4 sm:-mx-6 md:-mx-8 overflow-hidden\">\r\n            <div className=\"text-center text-white text-xl sm:text-2xl font-bold py-6 uppercase flex justify-center items-center gap-2 max-h-[79px]\">\r\n              {dis_currency} <span className=\"text-[40px] sm:text-[45px] md:text-[50px]\">{dis_price}</span>\r\n              <span className=\"flex flex-col text-left text-[12px] leading-[16px] font-semibold\">\r\n                <span>PER</span>\r\n                <span>{paymentInterval}</span>\r\n              </span>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"flex flex-col gap-3 sm:gap-4\">\r\n            <button \r\n              className=\"relative overflow-hidden text-center text-xs sm:text-[14px] font-semibold bg-black text-white rounded-md py-3 sm:py-4 px-12 max-w-[200px] sm:w-[232px] sm:max-w-[232px] max-h-[42px] sm:max-h-[46px] mx-auto hover:scale-105 transition-all duration-300 ease-in-out group\"\r\n              onClick={onAccept}\r\n            >\r\n              <span className=\"text-[12px] sm:text-[14px]\">Accept this Offer</span>\r\n              <span className=\"shine-effect\"></span>\r\n            </button>\r\n            <button \r\n              className=\"text-center text-[10px] sm:text-xs text-black hover:underline hover:text-[#9F1313]\"\r\n              onClick={onDecline}\r\n            >\r\n              Decline Offer\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </>\r\n  );\r\n};\r\n\r\nfunction Manage() {\r\n  const [enterpriseMembers, setEnterpriseMembers] = useState([]);\r\n\r\n  const [activeTab, setActiveTab] = useState('subscription');\r\n  const [isShowEnterprise, setisShowEnterprise] = useState(false);\r\n  const [showEditEnterprise, setShowEditEnterprise] = useState(false);\r\n  const [editMemberDetails, setEditMemberDetails] = useState([]);\r\n\r\n  const [showAddMember, setshowAddMember] = useState(false);\r\n  const [showAddMoreMember, setshowAddMoreMember] = useState(false);\r\n  const [showCompletePurchase, setShowCompletePurchase] = useState(false);\r\n  const [moreToAddMember, setMoreToAddMember] = useState(\"\");\r\n  const [moreToAddMemberTotalAmount, setMoreToAddMemberTotalAmount] = useState(\"\");\r\n  const [entParentUserID, setEntParentUserID] = useState(\"\");\r\n  const [currentPlan, setCurrentPlan] = useState(\"\");\r\n  const [currentPlanName, setCurrentPlanName] = useState(\"\");\r\n  const [userPpg, setUserPpg] = useState(\"\");\r\n\r\n  const [showPausedAccountModal, setShowPausedAccountModal] = useState(false);\r\n  const [userSubscriptionID, setUserSubscriptionID] = useState(false);\r\n  const [userMerchant, setUserMerchant] = useState(false);\r\n  const [userAccountID, setUserAccountID] = useState(false);\r\n  const [isMax, setIsMax] = useState(false);\r\n  const [isVisbleBtn, setIsVisibleBtn] = useState(true);\r\n\r\n  const [subscription, setSubscription] = useState(null);\r\n  const [getDateNow, setGetDateNow] = useState(\"\");\r\n  const [getOrderNow, setGetOrderNow] = useState(null);\r\n  const [usage, setUsage] = useState({});\r\n\r\n  // Add new state for OfferModal\r\n  const [showOfferModal, setShowOfferModal] = useState(false);\r\n  const [offerDetails, setOfferDetails] = useState(null);\r\n  const [totalTokenUsage, setTotalTokenUsage] = useState(0);\r\n  const [maxTokens, setMaxTokens] = useState(0);\r\n  const [resetDate, setResetDate] = useState();\r\n  const [tokenUsages, setTokenUsages] = useState({ perModel: {}, perApp: [] });\r\n\r\n  const auth = Auth();\r\n\r\n  useEffect(() => {\r\n    toastr.options = {\r\n      positionClass: 'toast-top-center'\r\n    };\r\n    async function getUserUsage () {\r\n      const data = await checkUsage();\r\n      setUsage(data);\r\n    }\r\n    getUserUsage();\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    //if error occured in 3d secure\r\n    let threed_error = GetCookie('threed_error');\r\n\r\n    if (threed_error!==undefined && threed_error!==''){\r\n      setTimeout(function(){ \r\n        SetCookie('threed_error','');\r\n        toastr.error(threed_error);\r\n      }, 3000);      \r\n    }\r\n  }, []);\r\n\r\n\r\n  useEffect(() => {\r\n    if (auth !== undefined){\r\n      let ent_parent_user_id = auth.ent_parent_user_id === null ? '' : auth.ent_parent_user_id;\r\n      setEntParentUserID(ent_parent_user_id)\r\n      setCurrentPlan(auth.plan);\r\n      setCurrentPlanName(window.view_data.plan_name);\r\n      setUserPpg(auth.user_ppg);\r\n\r\n      if (window.view_data.active_tab !== 'mem' ) { setActiveTab('account'); }\r\n    } else if (window.view_data.active_tab === 'mem' ) {\r\n      setActiveTab('members');\r\n    }\r\n  }, [auth]);\r\n\r\n  useEffect(()=>{\r\n    if(usage !== undefined) {\r\n      setMaxTokens(usage.max_tokens);\r\n      setResetDate(usage.max_end);\r\n    }\r\n  }, [usage]);\r\n\r\n  useEffect(()=>{\r\n    if (auth !== undefined && auth.plan==='enterprise' && usage !== undefined) {\r\n      ent_max_members = usage.max_members;\r\n      setisShowEnterprise(true);\r\n    }\r\n  }, [auth, usage]);\r\n\r\n  useEffect(() => {\r\n    const fetchData = async () => {\r\n      let entMembers = await getEnterpriseMembers();\r\n      setEnterpriseMembers(entMembers);\r\n    }\r\n\r\n    if (isShowEnterprise) {\r\n      fetchData();\r\n    }\r\n  }, [isShowEnterprise]);\r\n\r\n  useEffect(() => {\r\n    const fetchTokens = async () => {\r\n      let tokens = await getTokenUsage();\r\n\r\n      if (tokens.data !== undefined) {\r\n        setTotalTokenUsage(tokens.total)\r\n\r\n        setTokenUsages({\r\n          perModel: {\r\n            'GPT-4o': {\r\n              usage: tokens.gpt_4o_total_token_usage,\r\n              max_usage: tokens.gpt_4o_max_tokens\r\n            },\r\n            'DALL·E Image Generated': {\r\n              usage: tokens.dalle_total_image_generated,\r\n              max_usage: tokens.dalle_max_images\r\n            },\r\n            'GPT Image 1 Image Generated': {\r\n              usage: tokens.gpt_image_1_total_image_generated,\r\n              max_usage: tokens.gpt_image_1_max_images\r\n            },\r\n            'Claude 3.5': {\r\n              usage: tokens.claude_35_total_token_usage,\r\n              max_usage: tokens.claude_35_max_tokens\r\n            },\r\n            'Claude 3.7': {\r\n              usage: tokens.claude_37_total_token_usage,\r\n              max_usage: tokens.claude_37_max_tokens\r\n            },\r\n            'Deepseek R1': {\r\n              usage: tokens.deepseek_r1_total_token_usage,\r\n              max_usage: tokens.deepseek_r1_max_tokens\r\n            },\r\n            'Grok 3': {\r\n              usage: tokens.grok_v3_total_token_usage,\r\n              max_usage: tokens.grok_v3_max_tokens\r\n            },\r\n            'Grok 4': {\r\n              usage: tokens.grok_v4_total_token_usage,\r\n              max_usage: tokens.grok_v4_max_tokens\r\n            },\r\n            'Flux Prompts': {\r\n              usage: tokens.flux_total_prompts,\r\n              max_usage: tokens.flux_max_prompts\r\n            },\r\n            'o1 Prompts': {\r\n              usage: tokens.o1_total_prompts,\r\n              max_usage: tokens.o1_max_prompts\r\n            },\r\n            'Kling 1.6':{\r\n              usage: tokens.videogen_credit,\r\n              max_usage: tokens.videogen_total_credit\r\n            }\r\n          },\r\n          perApp: tokens.data\r\n        })\r\n      }\r\n    }\r\n\r\n    if (currentPlan) {\r\n      fetchTokens();\r\n    }\r\n\r\n    const fetchOrder = async () => {\r\n      const response = await getOrders()\r\n\r\n      setGetOrderNow(response);\r\n    }\r\n    if(activeTab === \"token-usage\") fetchTokens();\r\n    else if(activeTab === \"order\") fetchOrder();\r\n  }, [activeTab, currentPlan]);\r\n\r\n  useEffect(() => {\r\n    \r\n    if(getOrderNow !== null){\r\n      const targetPlans = ['enterprisemax', 'officemax', 'teammax'];\r\n\r\n      const checkEntMaxPlan = () => {\r\n        const formatPlanName = (name) => name.toLowerCase().replace(/\\s+/g, '');\r\n    \r\n        const user_plan = getOrderNow.find((item) =>\r\n          targetPlans.includes(formatPlanName(item.label))\r\n        );\r\n        setIsMax(Boolean(user_plan));\r\n      }\r\n\r\n      checkEntMaxPlan()\r\n    }\r\n\r\n  },[getOrderNow])\r\n\r\n  if(auth === undefined) return;\r\n  if(auth === false) {\r\n    window.location.href = '/login';\r\n    return;\r\n  }\r\n\r\n  const handleTabChange = (tab) => {\r\n    setActiveTab(tab);\r\n  };\r\n\r\n\r\n  const handleShowAddMember = () => {\r\n    const remainingSlots = ent_max_members - enterpriseMembers.length;\r\n    \r\n    if (enterpriseMembers.length >= ent_max_members) {\r\n      if (enterpriseMembers.length === Number(ent_max_members) && isMax) {\r\n        toastr.error(\"Member limit reached.\");\r\n      } else {\r\n        setIsVisibleBtn(false);\r\n        setshowAddMoreMember(true);\r\n      }\r\n    } else if (enterpriseMembers.length > 0 && isMax) {\r\n      setshowAddMember(true);\r\n      if (enterpriseMembers.length > ent_max_members) {\r\n        setIsVisibleBtn(false);\r\n      } else {\r\n        if (remainingSlots === 1) {\r\n          setIsVisibleBtn(false);\r\n        }else{\r\n          setIsVisibleBtn(true);\r\n        }\r\n      }\r\n    } else {\r\n      setshowAddMember(true);\r\n    }\r\n  };\r\n\r\n  const handleReloadMembers = async () => {\r\n    let entMembers = await getEnterpriseMembers();\r\n    setEnterpriseMembers(entMembers);\r\n  }\r\n\r\n  const handleShowEditMemberModal = (showEditEnterprise, params) => {\r\n    setShowEditEnterprise(true);\r\n    setEditMemberDetails(params)\r\n  }\r\n\r\n  async function getTokenUsage() {\r\n    const response = await axios.post(`${process.env.REACT_APP_API_URL}/get-token-usage`, {\r\n      'tk': GetCookie('access')\r\n    }, { headers: { 'content-type': 'application/x-www-form-urlencoded' } })\r\n\r\n    const output = response.data ? response.data : [];\r\n    if(output.success) {\r\n      return output;\r\n    } else {\r\n      return [];\r\n    }\r\n  }\r\n\r\n  async function getOrders() {\r\n    const response = await axios.post(`${process.env.REACT_APP_API_URL}/get-payment`, {\r\n      'tk': GetCookie('access')\r\n    }, { headers: { 'content-type': 'application/x-www-form-urlencoded' } })\r\n\r\n    const output = response.data;\r\n    if(output.success) {\r\n      return output.data;\r\n    } else {\r\n      return [];\r\n    }\r\n  }\r\n\r\n  install();\r\n\r\n  // Add new handler for OfferModal\r\n  const handleCloseOfferModal = () => {\r\n    setShowOfferModal(false);\r\n  };\r\n\r\n  const handlePay = () => {\r\n    window.location.href='/downgrade/'+dis_plan_id\r\n  };\r\n\r\n  const handleDeclineOffer = () => {\r\n    setShowOfferModal(false);\r\n    if (offerDetails && offerDetails.onDecline) {\r\n      offerDetails.onDecline();\r\n    }\r\n  };\r\n\r\n  const showOffer = (details) => {\r\n    setOfferDetails(details);\r\n    setShowOfferModal(true);\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <Helmet>\r\n        <title>AI Pro | Manage Account</title>\r\n        <meta name=\"description\" content=\"Take control of your account with ease through our My Account page. Review billing details, update contact information, and tailor your preferences.\" />\r\n      </Helmet>\r\n      <Header auth={auth} setSubscription={setSubscription} setGetDateNow={setGetDateNow} setshowAddMoreMember={setshowAddMoreMember} />\r\n      <div className=\"Manage bg-gray-100 min-h-[500px] flex items-center\">\r\n        <div className=\"container mx-auto py-10 px-4 sm:px-0\">\r\n          <div className=\"max-w-6xl mx-auto pt-8 pb-8 sm:p-8\">\r\n            <h1 className=\"text-xl font-bold text-blue-600 my-6 lg:my-8\">\r\n              Manage Your Account\r\n            </h1>\r\n            <div className=\"\">\r\n              <ul className=\"flex flex-wrap text-xs md:text-sm\">\r\n                {entParentUserID==='' ? (\r\n                <TabItem\r\n                tabName=\"subscription\"\r\n                activeTab={activeTab}\r\n                onClick={handleTabChange}\r\n                >\r\n                  Subscription\r\n                </TabItem>\r\n                ) : \"\"}\r\n\r\n                {isShowEnterprise && entParentUserID==='' ? (\r\n                <TabItem\r\n                tabName=\"members\"\r\n                activeTab={activeTab}\r\n                onClick={handleTabChange}\r\n                >\r\n                  Members\r\n                </TabItem>\r\n                ) : \"\"}\r\n\r\n                {entParentUserID==='' ? (\r\n                <TabItem\r\n                  tabName=\"order\"\r\n                  activeTab={activeTab}\r\n                  onClick={handleTabChange}\r\n                >\r\n                  Invoice\r\n                </TabItem>\r\n                ) : \"\"}\r\n\r\n                <TabItem\r\n                  tabName=\"token-usage\"\r\n                  activeTab={activeTab}\r\n                  onClick={handleTabChange}\r\n                >\r\n                  Token Usage\r\n                </TabItem>\r\n                <TabItem\r\n                  tabName=\"account\"\r\n                  activeTab={activeTab}\r\n                  onClick={handleTabChange}\r\n                >\r\n                  Account\r\n                </TabItem>\r\n                <TabItem\r\n                  tabName=\"help\"\r\n                  activeTab={activeTab}\r\n                  onClick={handleTabChange}\r\n                >\r\n                  Help\r\n                </TabItem>\r\n                <TabItem\r\n                  tabName=\"logout\"\r\n                  activeTab={activeTab}\r\n                  onClick={handleTabChange}\r\n                >\r\n                  Logout\r\n                </TabItem>\r\n              </ul>\r\n            </div>\r\n\r\n            <div className=\"bg-white drop-shadow-sm p-6 rounded-tr-md rounded-br-md rounded-bl-md min-h-[400px] flex\">\r\n              {activeTab === 'subscription' && <SubscriptionTabContent setshowAddMoreMember={setshowAddMoreMember} setShowPausedAccountModal={setShowPausedAccountModal} setUserSubscriptionID={setUserSubscriptionID} setUserMerchant={setUserMerchant} setUserAccountID={setUserAccountID} date_now={getDateNow} user_subscription={subscription} userPpg={userPpg} showOffer={showOffer}/>}\r\n              {activeTab === 'members' && <MembersTabContent handleShowAddMember={handleShowAddMember} enterpriseMembers={enterpriseMembers} handleReloadMembers={handleReloadMembers} handleShowEditMemberModal={handleShowEditMemberModal} currentPlan={currentPlan} currentPlanName={currentPlanName}/>}\r\n              {activeTab === 'order' && <OrderTabContent user_order={getOrderNow} />}\r\n              {activeTab === 'token-usage' && <TokenUsageContent\r\n                currentPlan={currentPlan}\r\n                currentPlanName={currentPlanName}\r\n                totalTokenUsage={totalTokenUsage}\r\n                maxTokens={maxTokens}\r\n                resetDate={resetDate}\r\n                tokenUsages={tokenUsages}\r\n                entParentUserID={entParentUserID}\r\n                />}\r\n              {activeTab === 'account' && <AccountTabContent auth={auth} />}\r\n              {activeTab === 'help' && <HelpTabContent />}\r\n              {activeTab === 'logout' && <LogOutContent />}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <EditMemberModal showEditMember={showEditEnterprise} setShowEditEnterprise={setShowEditEnterprise} editMemberDetails={editMemberDetails} handleReloadMembers={handleReloadMembers} />\r\n      <AddMemberModal showAddMember={showAddMember} setshowAddMember={setshowAddMember} handleReloadMembers={handleReloadMembers} entMaxMembers={ent_max_members} enterpriseMembers={enterpriseMembers} isMax={isMax} isVisbleBtn={isVisbleBtn} setIsVisibleBtn={setIsVisibleBtn}/>\r\n      <AddMoreMemberModal showAddMoreMember={showAddMoreMember} setshowAddMoreMember={setshowAddMoreMember} setMoreToAddMember={setMoreToAddMember} setMoreToAddMemberTotalAmount={setMoreToAddMemberTotalAmount}  setShowCompletePurchase={setShowCompletePurchase} />\r\n      <MemberCompletePurchase moreToAddMember={moreToAddMember} moreToAddMemberTotalAmount={moreToAddMemberTotalAmount} setShowCompletePurchase={setShowCompletePurchase} showCompletePurchase={showCompletePurchase}/>\r\n      <PausedAccountModal showPausedAccountModal={showPausedAccountModal} setShowPausedAccountModal={setShowPausedAccountModal} userSubscriptionID={userSubscriptionID} userMerchant={userMerchant} userAccountID={userAccountID} />\r\n      <OfferModal \r\n        show={showOfferModal}\r\n        onClose={handleCloseOfferModal}\r\n        onAccept={handlePay}\r\n        onDecline={handleDeclineOffer}\r\n      />\r\n      <Footer auth={auth}/>\r\n    </>\r\n  );\r\n}\r\n\r\nconst TabItem = ({ tabName, activeTab, onClick, children }) => {\r\n  const isActive = activeTab === tabName;\r\n\r\n  return (\r\n    <li\r\n      className={` pb-2 cursor-pointer py-3 px-6 rounded-t-lg ${\r\n        isActive ? 'border-b-2 border-white bg-white' : ''\r\n      }`}\r\n      onClick={() => onClick(tabName)}\r\n    >\r\n      {children}\r\n    </li>\r\n  );\r\n};\r\n\r\nconst AccountTabContent = (props) => {\r\n  const [newEmail, setNewEmail] = useState(\"\");\r\n  const [email] = useState(props.auth.email);\r\n  const [oldPassword, setOldPassword] = useState(\"\");\r\n  const [newPassword, setNewPassword] = useState(\"\");\r\n  const [confPassword, setConfPassword] = useState(\"\");\r\n  const [userPid] = useState(props.auth.user_pid);\r\n  const [isSocialLogin, setisSocialLogin] = useState(false);\r\n\r\n  const handleCopy = () => {\r\n    // Copy the userPid to clipboard\r\n    navigator.clipboard.writeText(userPid);\r\n    toastr.success(\"Successfully copied\");\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (props.auth !== undefined){\r\n      console.log(\"props.auth.is_social\",props.auth.is_social)\r\n      if (props.auth.is_social !==null && props.auth.is_social!==''){\r\n        setisSocialLogin(true);\r\n      }else{\r\n        setisSocialLogin(false);\r\n      }\r\n    }\r\n  }, [props.auth]);\r\n\r\n  const validatePassword = () => {\r\n    let error_msg = \"\";\r\n    if(!oldPassword) {\r\n      error_msg = \"Current Password is required.\";\r\n    } else if (newPassword.length){\r\n      if(newPassword !== confPassword) {\r\n        error_msg = \"New Password do not match.\";\r\n      } else if (newPassword.length < 6) {\r\n        error_msg = \"New Password should be at least 6 characters.\";\r\n      }\r\n    }\r\n    return error_msg;\r\n  };\r\n\r\n  const validateEmail = () => {\r\n    let error_msg = \"\";\r\n    if (newEmail.length) {\r\n      if (!/\\S+@\\S+\\.\\S+/.test(newEmail)) {\r\n        error_msg = 'Invalid email format';\r\n      }\r\n    }\r\n    return error_msg;\r\n  };\r\n\r\n  const updateUserDetails = () => {\r\n    let error_msg = validatePassword();\r\n    if(error_msg) {\r\n      toastr.error(error_msg);\r\n      return;\r\n    }\r\n    error_msg = validateEmail();\r\n    if(error_msg) {\r\n      toastr.error(error_msg);\r\n      return;\r\n    }\r\n    document.querySelector(\".loader-container\").classList.add('active');\r\n    axios.post(`${process.env.REACT_APP_API_URL}/update-account`, {\r\n      tk: GetCookie('access'),\r\n      newemail: newEmail,\r\n      password: oldPassword,\r\n      newpassword: newPassword,\r\n      confpassword: confPassword\r\n    }, { headers: { 'content-type': 'application/x-www-form-urlencoded' } }).then(function(res) {\r\n      let output = res.data;\r\n      if(output.success) {\r\n        toastr.success(\"Success!<br> Please re-login your account.\");\r\n        setTimeout(function(){\r\n          window.location.reload();\r\n        }, 1000);\r\n        return;\r\n      }\r\n      document.querySelector(\".loader-container\").classList.remove('active');\r\n      if(output.data) toastr.error(output.data.msg);\r\n    });\r\n  };\r\n\r\n  return (\r\n    <div className=\"w-96\">\r\n      <p className=\"text-sm py-4\">You may edit your account details below:</p>\r\n      <div className=\"flex items-center\">\r\n        <label className='text-sm mr-2 font-bold font-s'>Your Account ID: {userPid}</label>\r\n        <button class=\"bg-white hover:bg-gray-100 text-gray-800 font-semibold py-2 ml-1\" onClick={handleCopy}><FaRegCopy /></button>\r\n      </div>\r\n      <div className=\"relative block mt-3\">\r\n        <div className='border-solid border-2 border-gray-100 rounded-sm'>\r\n          <div className='p-3 bg-gray-50 border-b-2 border-gray-100 sm:text-sm'>\r\n            Change Email Address\r\n          </div>\r\n          <div className='p-2 pt-1 pb-1'>\r\n            <input className=\"placeholder:italic placeholder-text-slate-400 block bg-gray-100 w-full border border-slate-300 rounded-md my-3 py-2 pl-3 pr-3 shadow-sm focus:outline-none focus:border-sky-500 focus:ring-sky-500 focus:ring-1 sm:text-sm text-gray-400\"\r\n            placeholder=\"<EMAIL>\"\r\n            type=\"email\"\r\n            name=\"email\"\r\n            readOnly\r\n            value={props.auth.email}\r\n            />\r\n            <input className=\"placeholder:italic placeholder-text-slate-400 block w-full border border-slate-300 rounded-md my-3 py-2 pl-3 pr-3 shadow-sm focus:outline-none focus:border-sky-500 focus:ring-sky-500 focus:ring-1 sm:text-sm\"\r\n            placeholder=\"New Email\"\r\n            type=\"email\"\r\n            name=\"new_email\"\r\n            autocomplete=\"new-email\"\r\n            onKeyUp={(event) => {\r\n              setNewEmail(event.target.value);\r\n            }}\r\n            onChange={(e) => setNewEmail(e.target.value)}\r\n            disabled={isSocialLogin}\r\n            />\r\n          </div>\r\n        </div>\r\n        <div className='border-solid border-2 border-gray-100 rounded-sm mt-2'>\r\n          <div className='p-3 bg-gray-50 border-b-2 border-gray-100 sm:text-sm'>\r\n            Change Password\r\n          </div>\r\n          <div className='p-2 pt-1 pb-1'>\r\n            <input className=\"placeholder:italic placeholder:text-slate-400 block bg-white w-full border border-slate-300 rounded-md my-3 py-2 pl-3 pr-3 shadow-sm focus:outline-none focus:border-sky-500 focus:ring-sky-500 focus:ring-1 sm:text-sm\"\r\n            placeholder=\"New Password\"\r\n            type=\"password\"\r\n            name=\"new_password\"\r\n            autocomplete=\"new-password\"\r\n            onKeyUp={(event) => {\r\n              setNewPassword(event.target.value);\r\n            }}\r\n            disabled={isSocialLogin}\r\n            />\r\n            <input className=\"placeholder:italic placeholder:text-slate-400 block bg-white w-full border border-slate-300 rounded-md my-3 py-2 pl-3 pr-3 shadow-sm focus:outline-none focus:border-sky-500 focus:ring-sky-500 focus:ring-1 sm:text-sm\"\r\n            placeholder=\"Confirm New Password\"\r\n            type=\"password\"\r\n            name=\"conf_password\"\r\n            onKeyUp={(event) => {\r\n              setConfPassword(event.target.value);\r\n            }}\r\n            disabled={isSocialLogin}\r\n            />\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <UpdateAccount updateUserDetails={updateUserDetails} setOldPassword={setOldPassword}></UpdateAccount>\r\n      <div className=\"text-sm text-slate-400 py-4 mt-2 ml-2\">\r\n        <DeleteAccount email={email}></DeleteAccount>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nconst UpdateAccount = (props) => {\r\n  const setOldPassword = props.setOldPassword;\r\n  const [isShowModal, setIsShowModal] = useState(false);\r\n\r\n  const showUpdateAccountModal = () => {\r\n    setIsShowModal(true);\r\n  };\r\n  const closeModal = () => {\r\n    setIsShowModal(false);\r\n  };\r\n\r\n  const proceedUpdateAccount = () => {\r\n    props.updateUserDetails();\r\n  }\r\n  return (\r\n    <>\r\n      <motion.button\r\n        className=\"bg-blue-500 mb-1 text-white font-bold py-3 px-6 my-3 rounded-lg change-password hover:bg-[#2563eb]\"\r\n        whileTap={{ scale: 0.9 }}\r\n        onClick={showUpdateAccountModal}\r\n      >\r\n        Save Changes\r\n      </motion.button>\r\n      <Transition appear show={isShowModal} as={Fragment}>\r\n        <Dialog as=\"div\" className=\"relative z-10\" onClose={closeModal}>\r\n          <Transition.Child\r\n            as={Fragment}\r\n            enter=\"ease-out duration-300\"\r\n            enterFrom=\"opacity-0\"\r\n            enterTo=\"opacity-100\"\r\n            leave=\"ease-in duration-200\"\r\n            leaveFrom=\"opacity-100\"\r\n            leaveTo=\"opacity-0\"\r\n          >\r\n            <div className=\"fixed inset-0 bg-black bg-opacity-25\" />\r\n          </Transition.Child>\r\n\r\n          <div className=\"fixed inset-0 overflow-y-auto\">\r\n            <div className=\"flex min-h-full items-center justify-center p-4 text-center\">\r\n              <Transition.Child\r\n                as={Fragment}\r\n                enter=\"ease-out duration-300\"\r\n                enterFrom=\"opacity-0 scale-95\"\r\n                enterTo=\"opacity-100 scale-100\"\r\n                leave=\"ease-in duration-200\"\r\n                leaveFrom=\"opacity-100 scale-100\"\r\n                leaveTo=\"opacity-0 scale-95\"\r\n              >\r\n                <Dialog.Panel className=\"w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all\">\r\n                  <Dialog.Title\r\n                    as=\"h3\"\r\n                    className=\"text-lg font-medium leading-6 text-gray-900\"\r\n                  >\r\n                    Update Account\r\n                  </Dialog.Title>\r\n                  <div className=\"mt-2\">\r\n                    <div className=\"text-sm text-gray-500\">\r\n                      Please enter your password to proceed.\r\n                    </div>\r\n                    <div>\r\n                      <input className=\"placeholder:italic placeholder:text-slate-400 block bg-white w-full border border-slate-300 rounded-md my-3 py-2 pl-3 pr-3 shadow-sm focus:outline-none focus:border-sky-500 focus:ring-sky-500 focus:ring-1 sm:text-sm\"\r\n                      placeholder=\"Current Password *\"\r\n                      type=\"password\"\r\n                      name=\"current_password\"\r\n                      onKeyUp={(event) => {\r\n                        setOldPassword(event.target.value);\r\n                        if(event.keyCode === 13) proceedUpdateAccount();\r\n                      }}\r\n                      />\r\n                    </div>\r\n                  </div>\r\n\r\n                  <div className=\"mt-4\">\r\n            <button\r\n                      type=\"button\"\r\n                      className=\"inline-flex justify-center rounded-md border border-transparent bg-blue-500 px-4 py-2 mr-2 text-sm font-medium text-white hover:bg-[#2563eb]\"\r\n                      onClick={proceedUpdateAccount}\r\n                    >\r\n                      Proceed\r\n            </button>\r\n                    <button\r\n                      type=\"button\"\r\n                      className=\"inline-flex justify-center rounded-md border border-transparent bg-gray-100 px-4 py-2 text-sm font-medium text-black hover:bg-gray-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-gray-500 focus-visible:ring-offset-2\"\r\n                      onClick={closeModal}\r\n                    >\r\n                      Close\r\n                    </button>\r\n                </div>\r\n                </Dialog.Panel>\r\n              </Transition.Child>\r\n                </div>\r\n              </div>\r\n        </Dialog>\r\n      </Transition>\r\n    </>\r\n  )\r\n}\r\n\r\nasync function getEnterpriseMembers() {\r\n  const response = await axios.post(`${process.env.REACT_APP_API_URL}/get-enterprise-members`, {\r\n    'tk': GetCookie('access')\r\n  }, { headers: { 'content-type': 'application/x-www-form-urlencoded' } })\r\n  const output = response.data;\r\n  if(output.success) {\r\n    return output.data;\r\n  } else {\r\n    return [];\r\n  }\r\n}\r\n\r\nconst SubscriptionTabContent = ({setshowAddMoreMember, setShowPausedAccountModal, setUserSubscriptionID, setUserMerchant, setUserAccountID, date_now, user_subscription, userPpg, showOffer}) => {\r\n  const tk = GetCookie('access');\r\n\r\n  if(user_subscription === undefined || user_subscription === null) return;\r\n  const changeCard = (event) => {\r\n    let p_id = event.target.getAttribute(\"pid\");\r\n    if(!p_id) return;\r\n    document.querySelector(\".loader-container\").classList.add('active');\r\n    window.location.href = '/change-card?pid=' + p_id;\r\n  }\r\n\r\n  const upgradeSub = (plan) => {\r\n    console.log(plan);\r\n    if (plan==='enterprise'){\r\n      setshowAddMoreMember(true);\r\n    }else{\r\n      window.location.href = '/upgrade';\r\n    }\r\n  }\r\n\r\n  const downgradeSub = () => {\r\n    window.location.href = '/downgrade';\r\n  };\r\n\r\n  const pauseSub = (plan) => {\r\n    setUserSubscriptionID(plan.merchant_subscription_id);\r\n    setUserMerchant(plan.merchant);\r\n    setUserAccountID(plan.pid);\r\n    setShowPausedAccountModal(true);\r\n  }\r\n\r\n  const checkPlanEnt = (plan_type) => {\r\n    if (plan_type) return plan_type.toLowerCase() !== 'enterprise';\r\n    return false; \r\n  };\r\n\r\n  const resumeSub = (plan) => {\r\n    window.location.href = '/resume';\r\n  }\r\n\r\n  const getMerchant = (sub) => {\r\n    return sub.merchant ? sub.merchant.toLowerCase() : \"\";\r\n  };\r\n\r\n  const getCurrency = (sub) => {\r\n    return sub.currency ? sub.currency.toLowerCase() : \"\";\r\n  }\r\n\r\n  const getPlanType = (sub) => {\r\n    return sub.plan_type ? sub.plan_type.toLowerCase() : \"\";\r\n  }\r\n\r\n  const getPaymentInterval = (sub) => {\r\n    return sub.payment_interval ? sub.payment_interval.toLowerCase() : \"\";\r\n  };\r\n\r\n  return (\r\n    <>\r\n    <div className=\"overflow-x-auto overflow-y-visible container-full-width cm-scrollbar\">\r\n      {user_subscription && user_subscription.length ?\r\n      <table className=\"subs-table min-w-full divide-y bg-gray-50 divide-gray-200 min-h-[50px]\">\r\n        <thead>\r\n          <tr className=\"sub_tbl h-3\">\r\n            <td className=\"px-6 py-2 bg-gray-50 text-center text-[12px] font-medium bg-gray-200 text-gray-500 uppercase tracking-wider border\">Plan Details</td>\r\n            <td className=\"px-6 py-2 bg-gray-50 text-center text-[12px] font-medium bg-gray-200 text-gray-500 uppercase tracking-wider border\">Amount</td>\r\n            <td className=\"px-6 py-2 bg-gray-50 text-center text-[12px] font-medium bg-gray-200 text-gray-500 uppercase tracking-wider border\">Trial Period</td>\r\n            <td className=\"px-6 py-2 bg-gray-50 text-center text-[12px] font-medium bg-gray-200 text-gray-500 uppercase tracking-wider border\">Starts On</td>\r\n            {((user_subscription && user_subscription.length > 0 && user_subscription.some(sub => sub.status === 'inactive')) || user_subscription.some(sub => sub.status === 'paused')) && (\r\n              <td className=\"px-6 py-2 bg-gray-50 text-center text-[12px] font-medium bg-gray-200 text-gray-500 uppercase tracking-wider border\">Expires On</td>\r\n            )}\r\n            <td className=\"px-6 py-2 bg-gray-50 text-center text-[12px] font-medium bg-gray-200 text-gray-500 uppercase tracking-wider border\">Status</td>\r\n\r\n            <td className=\"px-6 py-2 bg-gray-50 text-center text-[12px] font-medium bg-gray-200 text-gray-500 uppercase tracking-wider border\">Actions</td>\r\n          </tr>\r\n        </thead>\r\n        <tbody className=\"bg-white divide-y divide-gray-200\">\r\n          {user_subscription?.map((sub, index) => (\r\n            <tr key={index} className=\"sub_tbl h-3\">\r\n            <td className=\"px-6 py-2 bg-gray-50 text-center text-[12px] text-gray-500 uppercase tracking-wider border font-medium\">{checkPlanEnt(sub.plan_type) ? sub.plan_type.replace(\"ProMax\",\" pro max\") : sub.plan_name}</td>\r\n            <td className=\"px-6 py-2 bg-gray-50 text-center text-[12px] text-gray-500 uppercase tracking-wider border font-medium\">{sub.price_label}</td>\r\n            <td className=\"px-6 py-2 bg-gray-50 text-center text-[12px] text-gray-500 uppercase tracking-wider border font-medium\">{sub.trial_days ? \"Yes\" : \"No\"}</td>\r\n            <td className=\"px-6 py-2 bg-gray-50 text-center text-[12px] text-gray-500 uppercase tracking-wider border font-medium\">{formatDate(sub.start_date)}</td>\r\n\r\n            { user_subscription && user_subscription.length > 0 && user_subscription.some(sub => sub.status === 'inactive') ? (\r\n                sub.status === 'active' ? (\r\n                  <td className=\"inactive px-6 py-2 bg-gray-50 text-center text-[12px] text-gray-500 uppercase tracking-wider border font-medium\">\r\n                  </td>\r\n                ) : (\r\n                  <td className=\"inactive px-6 py-2 bg-gray-50 text-center text-[12px] text-gray-500 uppercase tracking-wider border font-medium\">\r\n                    {formatDate(sub.end_date)}\r\n                  </td>\r\n                )\r\n\r\n              ) : sub.status === 'active' ? (\r\n                ''\r\n              ) : sub.status === 'paused' ? (\r\n                <td className=\"active px-6 py-2 bg-gray-50 text-center text-[12px] text-gray-500 uppercase tracking-wider border font-medium\">\r\n                  {formatDate(sub.end_date)}\r\n                </td>\r\n              ) : (\r\n                <td className=\"active px-6 py-2 bg-gray-50 text-center text-[12px] text-gray-500 uppercase tracking-wider border font-medium\">\r\n                  {formatDate(sub.end_date)}\r\n                </td>\r\n            )}\r\n\r\n            <td className=\"px-6 py-2 bg-gray-50 text-center text-[12px] text-gray-500 uppercase tracking-wider border font-medium\">{sub.status}</td>\r\n            <td className=\"px-6 py-2 bg-gray-50 text-center text-[12px] text-gray-500 uppercase tracking-wider border font-medium\">\r\n              { sub.status === 'paused'  ?\r\n              <div className=\"text-[12px]\">\r\n                <Menu as=\"div\" className=\"relative inline-block text-center w-full text-[12px]\">\r\n                  <div className='w-full'>\r\n                    <Menu.Button className=\"inline-flex w-full justify-center rounded-md bg-sky-500/100 px-4 py-2 font-medium text-white hover:bg-[#49b1df]\">\r\n                      Options\r\n                    </Menu.Button>\r\n                  </div>\r\n                  <Transition\r\n                    as={Fragment}\r\n                    enter=\"transition ease-out duration-100\"\r\n                    enterFrom=\"transform opacity-0 scale-95\"\r\n                    enterTo=\"transform opacity-100 scale-100\"\r\n                    leave=\"transition ease-in duration-75\"\r\n                    leaveFrom=\"transform opacity-100 scale-100\"\r\n                    leaveTo=\"transform opacity-0 scale-95\"\r\n                  >\r\n\r\n                    <Menu.Items className={`${\r\n                        index < 3 ? \"dp-top\" : \"dp-bot\"\r\n                      } absolute w-50 origin-top-right divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none text-center min-w-full text-center`}>\r\n                      <div className=\"text-center min-w-full\">\r\n                      { getMerchant(sub) === 'recurly' || getMerchant(sub) === 'stripe' ? (\r\n                        <Menu.Item>\r\n                          {({ active }) => (\r\n                            <button\r\n                              className={`${\r\n                                active ? 'bg-sky-500/100 text-white' : 'text-gray-900'\r\n                              } group min-w-full items-center px-4 py-2 text-left border-b border-b-slate-100`}\r\n                              pid={sub.pid}\r\n                              onClick={changeCard}\r\n                            >\r\n                              Change Card\r\n                            </button>\r\n                          )}\r\n                        </Menu.Item>\r\n                        ) : \"\" }\r\n                        { (getMerchant(sub) === 'recurly' || getMerchant(sub) === 'stripe' || getMerchant(sub) === 'fastspring' || getMerchant(sub) === 'cardtransaction') && sub.status === 'paused' && getPaymentInterval(sub) === 'monthly' ? (\r\n                        <Menu.Item>\r\n                        {({ active }) => (\r\n                          <button\r\n                            className={`${\r\n                              active ? 'bg-sky-500/100 text-white' : 'text-gray-900'\r\n                            } group min-w-full items-center px-4 py-2 text-left border-b border-b-slate-100`}\r\n                            onClick={(plan) => resumeSub(sub)}\r\n                            >\r\n                              Resume\r\n                          </button>\r\n                        )}\r\n                        </Menu.Item>\r\n                        ) : \"\" }\r\n                        <Menu.Item>\r\n                          {({ active }) => (\r\n                            <CancelSubscription pid={sub.pid} tk={tk} showOffer={showOffer}/>\r\n                          )}\r\n                        </Menu.Item>\r\n                      </div>\r\n                    </Menu.Items>\r\n                  </Transition>\r\n                </Menu>\r\n              </div> : \"\"}\r\n\r\n              { sub.status === 'active'  ?\r\n              <div className=\"text-[12px]\">\r\n                <Menu as=\"div\" className=\"relative inline-block text-center w-full text-[12px]\">\r\n                  <div className='w-full'>\r\n                    <Menu.Button className=\"inline-flex w-full justify-center rounded-md bg-sky-500/100 px-4 py-2 font-medium text-white hover:bg-[#49b1df]\">\r\n                      Options\r\n                    </Menu.Button>\r\n                  </div>\r\n                  <Transition\r\n                    as={Fragment}\r\n                    enter=\"transition ease-out duration-100\"\r\n                    enterFrom=\"transform opacity-0 scale-95\"\r\n                    enterTo=\"transform opacity-100 scale-100\"\r\n                    leave=\"transition ease-in duration-75\"\r\n                    leaveFrom=\"transform opacity-100 scale-100\"\r\n                    leaveTo=\"transform opacity-0 scale-95\"\r\n                  >\r\n                    <Menu.Items className={`${\r\n                        index < 3 ? \"dp-top\" : \"dp-bot\"\r\n                      } absolute w-50 origin-top-right divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none text-center min-w-full text-center`}>\r\n                      <div className=\"text-center min-w-full\">\r\n                        { getMerchant(sub) === 'recurly' || getMerchant(sub) === 'stripe' ? (\r\n                        <Menu.Item>\r\n                          {({ active }) => (\r\n                            <button\r\n                              className={`${\r\n                                active ? 'bg-sky-500/100 text-white' : 'text-gray-900'\r\n                              } group min-w-full items-center px-4 py-2 text-left border-b border-b-slate-100`}\r\n                              pid={sub.pid}\r\n                              onClick={changeCard}\r\n                            >\r\n                              Change Card\r\n                            </button>\r\n                          )}\r\n                        </Menu.Item>\r\n                        ) : \"\" }\r\n                        { (getCurrency(sub) === 'usd' || getMerchant(sub) === 'paddle' || getMerchant(sub) === 'stripe' || getMerchant(sub) === 'paypal' || getMerchant(sub) === 'fastspring' || getMerchant(sub) === 'cardtransaction')\r\n                        && diffMin(formatDateTime(date_now), formatDateTime(sub.start_date)) >= 0 ?\r\n                        <>\r\n                         { (getPlanType(sub) !== \"enterprise\" && getPaymentInterval(sub) !== 'yearly' && getPlanType(sub) !== \"promax\" && userPpg !== '97') ||\r\n                          (userPpg === '97' && (getPaymentInterval(sub) !== 'yearly' && getPlanType(sub) === \"promax\")) ||\r\n                          (getPlanType(sub) === \"basic\") || (getPlanType(sub) === \"pro\") ? (\r\n                          <Menu.Item>\r\n                            {({ active }) => (\r\n                              <button\r\n                                className={`${\r\n                                  active ? 'bg-sky-500/100 text-white' : 'text-gray-900'\r\n                                } group min-w-full items-center px-4 py-2 text-left border-b border-b-slate-100`}\r\n                                onClick={(plan) => upgradeSub(getPlanType(sub))}\r\n                              >\r\n                                Upgrade\r\n                              </button>\r\n                            )}\r\n                          </Menu.Item>\r\n                          ) : \"\" }\r\n\r\n                          { (!(getPlanType(sub) === \"basic\" && getPaymentInterval(sub) === 'monthly') && getPlanType(sub) !== \"enterprise\") ? (\r\n                          <Menu.Item>\r\n                            {({ active }) => (\r\n                              <button\r\n                                className={`${\r\n                                  active ? 'bg-sky-500/100 text-white' : 'text-gray-900'\r\n                                } group min-w-full items-center px-4 py-2 text-left border-b border-b-slate-100`}\r\n                                onClick={downgradeSub}\r\n                              >\r\n                                Downgrade\r\n                              </button>\r\n                            )}\r\n                          </Menu.Item>\r\n                          ) : \"\" }\r\n                          \r\n                          { (getMerchant(sub) === 'recurly' || getMerchant(sub) === 'stripe' || getMerchant(sub) === 'fastspring' || getMerchant(sub) === 'cardtransaction') && sub.status === 'active' && getPaymentInterval(sub) === 'monthly' && sub.is_trial_end === 'yes' && sub.plan_type !== 'Enterprise' ? (\r\n                          <Menu.Item>\r\n                            {({ active }) => (\r\n                              <button\r\n                                className={`${\r\n                                  active ? 'bg-sky-500/100 text-white' : 'text-gray-900'\r\n                                } group min-w-full items-center px-4 py-2 text-left border-b border-b-slate-100`}\r\n                                onClick={(plan) => pauseSub(sub)}\r\n                              >\r\n                                Pause\r\n                              </button>\r\n                            )}\r\n                          </Menu.Item>\r\n                          ) : \"\" }\r\n\r\n                        </> : <></> }\r\n                        <Menu.Item>\r\n                          {({ active }) => (\r\n                            <CancelSubscription pid={sub.pid} tk={tk} showOffer={showOffer}/>\r\n                          )}\r\n                        </Menu.Item>\r\n                      </div>\r\n                    </Menu.Items>\r\n                  </Transition>\r\n                </Menu>\r\n              </div> : \"\" }\r\n\r\n\r\n            </td>\r\n          </tr>\r\n          ))}\r\n        </tbody>\r\n      </table>\r\n      : <div>\r\n        <span className=\"text-[12px] text-gray-600\"><FaInfoCircle className=\"inline text-lg mr-1\"/> No active subscription. Look for available <a href=\"/pricing\" className=\"text-blue-400 font-bold\">SUBSCRIPTIONS</a></span>\r\n        </div>\r\n      }\r\n    </div>\r\n    </>\r\n  );\r\n};\r\n\r\n\r\nconst OrderTabContent = ({user_order}) => {\r\n\r\n  if(user_order === undefined || user_order === null) return;\r\n  return (\r\n    <div className=\"overflow-x-auto custom-scrollbar container-full-width bg-gray-50\">\r\n      {user_order && user_order.length ?\r\n      <table className=\"min-w-full divide-y divide-gray-200 min-h-[50px]\">\r\n        <thead>\r\n          <tr className=\"sub_tbl h-3\">\r\n            <th className=\"px-6 py-3 bg-gray-50 text-center text-[12px] font-medium bg-gray-200 text-gray-500 uppercase tracking-wider\">Invoice</th>\r\n            <th className=\"px-6 py-3 bg-gray-50 text-center text-[12px] font-medium bg-gray-200 text-gray-500 uppercase tracking-wider\">Membership</th>\r\n            <th className=\"px-6 py-3 bg-gray-50 text-center text-[12px] font-medium bg-gray-200 text-gray-500 uppercase tracking-wider\">Amount</th>\r\n            <th className=\"px-6 py-3 bg-gray-50 text-center text-[12px] font-medium bg-gray-200 text-gray-500 uppercase tracking-wider\">Date</th>\r\n            <th className=\"px-6 py-3 bg-gray-50 text-center text-[12px] font-medium bg-gray-200 text-gray-500 uppercase tracking-wider\">Status</th>\r\n          </tr>\r\n        </thead>\r\n        <tbody className=\"bg-white divide-y divide-gray-200\">\r\n          {user_order?.map((sub, index) => (\r\n            <tr key={index} className=\"sub_tbl h-3\">\r\n            <td className=\"px-6 py-3 bg-gray-50 text-center text-[12px] text-gray-500 uppercase tracking-wider border font-medium\">{sub.charge_id}</td>\r\n            <td className=\"px-6 py-3 bg-gray-50 text-center text-[12px] text-gray-500 uppercase tracking-wider border font-medium\">{sub.label}</td>\r\n            <td className=\"px-6 py-3 bg-gray-50 text-center text-[12px] text-gray-500 uppercase tracking-wider border font-medium\">{sub.amount ? getPricePlan(sub.currency, sub.amount) : \"\"}</td>\r\n            <td className=\"px-6 py-3 bg-gray-50 text-center text-[12px] text-gray-500 uppercase tracking-wider border font-medium\">{formatDate(sub.created_at)}</td>\r\n            <td className=\"px-6 py-3 bg-gray-50 text-center text-[12px] text-gray-500 uppercase tracking-wider border font-medium\">{sub.refund_at !== \"0000-00-00 00:00:00\" ? \"Refunded\" : \"Completed\"}</td>\r\n            </tr>\r\n          ))}\r\n        </tbody>\r\n      </table>\r\n      : <span className=\"text-[12px] text-gray-600\"><FaInfoCircle className=\"inline text-lg mr-1\"/> No orders have been made yet. Look for available <a href=\"/pricing\" className=\"text-blue-400 font-bold\">SUBSCRIPTIONS</a></span> }\r\n    </div>\r\n  );\r\n};\r\n\r\nconst HelpTabContent = () => {\r\n  return (\r\n    <div className=\"w-full\">\r\n      <p className=\"text-sm py-4\">If you have any comments about our website, AI tools. and articles, or if you have questions about your account access, please don't hesitate to get in touch with us. Leave your messages through the <a href=\"https://ai-pro.org/contact-us/\" className=\"text-blue-400 font-bold\">Contact us</a> page.</p>\r\n      <p className=\"font-bold\">Quick Links:</p>\r\n      <ul>\r\n        <li><a href=\"/my-account\" className=\"text-blue-400 font-bold px-2\"> My Apps</a></li>\r\n        <li><a href=\"/my-account\" className=\"text-blue-400 font-bold px-2\"> AI Tools</a></li>\r\n        <li><a href=\"https://ai-pro.org/articles/\" className=\"text-blue-400 font-bold px-2\"> Articles</a></li>\r\n        <li><a href=\"https://ai-pro.org/tutorials/\" className=\"text-blue-400 font-bold px-2\"> Tutorials</a></li>\r\n      </ul>\r\n    </div>\r\n  );\r\n};\r\n\r\nconst LogOutContent = () => {\r\n\r\n  function logout() {\r\n    RemoveCookie(\"access\");\r\n    RemoveCookie(\"ci_session\");\r\n\r\n    axios.get(`${process.env.REACT_APP_API_URL}/logout`).then(function(){\r\n      window.location.href = '/login';\r\n    });\r\n  };\r\n\r\n  return (\r\n    <div>\r\n      <motion.button\r\n        className=\"bg-blue-500 mb-1 text-white font-bold py-3 px-6 my-3 rounded-lg\"\r\n        whileTap={{ scale: 0.9 }}\r\n        onClick={logout}\r\n      >\r\n        Logout\r\n      </motion.button>\r\n    </div>\r\n  );\r\n};\r\n\r\nconst DeleteAccount = (props) => {\r\n  const [email] = useState(props.email);\r\n  const [isShowModal, setIsShowModal] = useState(false);\r\n\r\n  const showDeleteModal = () => {\r\n    setIsShowModal(true);\r\n  };\r\n  const closeModal = () => {\r\n    setIsShowModal(false);\r\n  };\r\n\r\n  const proceedDeleteAccount = () => {\r\n    document.querySelector(\".loader-container\").classList.add('active');\r\n    axios.post(`${process.env.REACT_APP_API_URL}/delete-account`, {\r\n    }, { headers: { 'content-type': 'application/x-www-form-urlencoded' } }).then(function(res) {\r\n      let output = res.data;\r\n      if(output.success) {\r\n        window.mixpanel.people.set_once({ \"$email\": email });\r\n        window.mixpanel.identify(email);\r\n        window.mixpanel.track(\"delete_account\", {});\r\n        //\r\n        toastr.success(\"Success\");\r\n        window.location.reload();\r\n        return;\r\n      }\r\n      document.querySelector(\".loader-container\").classList.remove('active');\r\n      toastr.error(\"Fail\");\r\n    });\r\n  }\r\n  return (\r\n    <>\r\n      <span className=\"cursor-pointer hover:underline hover:decoration-solid\" onClick={showDeleteModal}>Delete Account</span>\r\n      <Transition appear show={isShowModal} as={Fragment}>\r\n        <Dialog as=\"div\" className=\"relative z-10\" onClose={closeModal}>\r\n          <Transition.Child\r\n            as={Fragment}\r\n            enter=\"ease-out duration-300\"\r\n            enterFrom=\"opacity-0\"\r\n            enterTo=\"opacity-100\"\r\n            leave=\"ease-in duration-200\"\r\n            leaveFrom=\"opacity-100\"\r\n            leaveTo=\"opacity-0\"\r\n          >\r\n            <div className=\"fixed inset-0 bg-black bg-opacity-25\" />\r\n          </Transition.Child>\r\n\r\n          <div className=\"fixed inset-0 overflow-y-auto\">\r\n            <div className=\"flex min-h-full items-center justify-center p-4 text-center\">\r\n              <Transition.Child\r\n                as={Fragment}\r\n                enter=\"ease-out duration-300\"\r\n                enterFrom=\"opacity-0 scale-95\"\r\n                enterTo=\"opacity-100 scale-100\"\r\n                leave=\"ease-in duration-200\"\r\n                leaveFrom=\"opacity-100 scale-100\"\r\n                leaveTo=\"opacity-0 scale-95\"\r\n              >\r\n                <Dialog.Panel className=\"w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all\">\r\n                  <Dialog.Title\r\n                    as=\"h3\"\r\n                    className=\"text-lg font-medium leading-6 text-gray-900\"\r\n                  >\r\n                    Delete Account\r\n                  </Dialog.Title>\r\n                  <div className=\"mt-2\">\r\n                    <p className=\"text-sm text-gray-500\">\r\n                    Deleting your account will permanently remove all of your data and information associated with it. This action is irreversible, and you won't be able to recover your account or any of its contents.\r\n                    </p>\r\n                    <p className=\"text-sm text-gray-500\">\r\n                    Are you sure you want to delete your account?\r\n                    </p>\r\n                  </div>\r\n\r\n                  <div className=\"mt-4\">\r\n                    <button\r\n                      type=\"button\"\r\n                      className=\"inline-flex justify-center rounded-md border border-transparent bg-red-500 px-4 py-2 mr-2 text-sm font-medium text-white hover:bg-[#d30101]\"\r\n                      onClick={proceedDeleteAccount}\r\n                    >\r\n                      Yes! Delete My Account\r\n                </button>\r\n                    <button\r\n                      type=\"button\"\r\n                      className=\"inline-flex justify-center rounded-md border border-transparent bg-gray-100 px-4 py-2 text-sm font-medium text-black hover:bg-gray-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-gray-500 focus-visible:ring-offset-2\"\r\n                      onClick={closeModal}\r\n                    >\r\n                      Cancel\r\n                </button>\r\n              </div>\r\n                </Dialog.Panel>\r\n              </Transition.Child>\r\n            </div>\r\n          </div>\r\n        </Dialog>\r\n      </Transition>\r\n    </>\r\n  )\r\n}\r\n\r\nconst CancelSubscription = ({ pid, tk, showOffer }) => {\r\n  const [isShowModal, setIsShowModal] = useState(false);\r\n  const auth = Auth();\r\n  \r\n  const showCancelModal = () => {\r\n    setIsShowModal(true);\r\n  };\r\n  \r\n  const closeModal = () => {\r\n    setIsShowModal(false);\r\n  };\r\n\r\n  // const proceedUnsubscribe = () => {\r\n  //   document.querySelector(\".loader-container\").classList.add('active');\r\n  //   axios.post(`${process.env.REACT_APP_API_URL}/cancel-subscription`, {\r\n  //     tk,\r\n  //     p_id: pid\r\n  //   }, { headers: { 'content-type': 'application/x-www-form-urlencoded' } }).then(function(res){\r\n  //     let output = res.data;\r\n  //     if(output.success) {\r\n  //       toastr.success(\"Success\");\r\n  //       window.location.reload();\r\n  //       return;\r\n  //     }\r\n  //     document.querySelector(\".loader-container\").classList.remove('active');\r\n  //     if(output.data) toastr.error(output.data.msg);\r\n  //   });\r\n  // };\r\n\r\n  const unSubscribe = (event) => {\r\n    if(!pid) return;\r\n    closeModal();\r\n    \r\n    if((auth.surveydata === '' || auth.surveydata === null) && dis_cancel !== 'yes' ) {\r\n      // Only show offer modal if dis_plan_id exists and is not empty\r\n        console.log('survey');\r\n      if(dis_plan_id && dis_plan_id !== '') {\r\n        showOffer({\r\n          onDecline: () => {\r\n            window.location.href = '/survey';\r\n          }\r\n        });\r\n      } else {\r\n        window.location.href = '/survey';\r\n      }\r\n    } else {\r\n      console.log('unsubs');\r\n      window.location.href = '/survey';\r\n      // proceedUnsubscribe();\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <button\r\n      className={`hover:bg-sky-500/100 hover:text-white text-gray-900 group min-w-full items-center px-4 py-2 text-left`}\r\n      pid={pid}\r\n      onClick={showCancelModal}\r\n      >\r\n        Cancel\r\n      </button>\r\n      <Transition appear show={isShowModal} as={Fragment}>\r\n        <Dialog as=\"div\" className=\"relative z-10\" onClose={closeModal}>\r\n          <Transition.Child\r\n            as={Fragment}\r\n            enter=\"ease-out duration-300\"\r\n            enterFrom=\"opacity-0\"\r\n            enterTo=\"opacity-100\"\r\n            leave=\"ease-in duration-200\"\r\n            leaveFrom=\"opacity-100\"\r\n            leaveTo=\"opacity-0\"\r\n          >\r\n            <div className=\"fixed inset-0 bg-black bg-opacity-25\" />\r\n          </Transition.Child>\r\n\r\n          <div className=\"fixed inset-0 overflow-y-auto\">\r\n            <div className=\"flex min-h-full items-center justify-center p-4 text-center\">\r\n              <Transition.Child\r\n                as={Fragment}\r\n                enter=\"ease-out duration-300\"\r\n                enterFrom=\"opacity-0 scale-95\"\r\n                enterTo=\"opacity-100 scale-100\"\r\n                leave=\"ease-in duration-200\"\r\n                leaveFrom=\"opacity-100 scale-100\"\r\n                leaveTo=\"opacity-0 scale-95\"\r\n              >\r\n                <Dialog.Panel className=\"w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all\">\r\n                  <Dialog.Title\r\n                    as=\"h3\"\r\n                    className=\"text-lg font-medium leading-6 text-gray-900\"\r\n                  >\r\n                    Cancel Subscription\r\n                  </Dialog.Title>\r\n                  <div className=\"mt-2\">\r\n                    <p className=\"text-sm text-gray-500\">\r\n                      Are you sure you want to cancel your subscription?\r\n                    </p>\r\n                  </div>\r\n\r\n                  <div className=\"mt-4 text-right\">\r\n                    <button\r\n                      type=\"button\"\r\n                      className=\"inline-flex justify-center rounded-md border border-transparent bg-red-500 px-4 py-2 mr-2 text-sm font-medium text-white hover:bg-[#d30101]\"\r\n                      onClick={unSubscribe}\r\n                    >\r\n                      Yes\r\n                    </button>\r\n                    <button\r\n                      type=\"button\"\r\n                      className=\"inline-flex justify-center rounded-md border border-transparent bg-gray-100 px-4 py-2 text-sm font-medium text-black hover:bg-gray-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-gray-500 focus-visible:ring-offset-2\"\r\n                      onClick={closeModal}\r\n                    >\r\n                      Close\r\n                    </button>\r\n                  </div>\r\n                </Dialog.Panel>\r\n              </Transition.Child>\r\n            </div>\r\n          </div>\r\n        </Dialog>\r\n      </Transition>\r\n    </>\r\n  )\r\n}\r\n\r\nfunction limitDisplay(members, displayLimit) {\r\n  return members.slice(0, displayLimit)\r\n}\r\n\r\nfunction getTotalPages(members, displayLimit) {\r\n  if (members.length<displayLimit){\r\n    return 1;\r\n  }\r\n\r\n  let totalMembers = members.length;\r\n  let quotient = Math.floor(totalMembers/displayLimit); // => 4 => the times 3 fits into 13\r\n  let remainder = totalMembers % displayLimit;\r\n\r\n  if (remainder>0){\r\n    quotient = quotient+1;\r\n  }\r\n  return quotient;\r\n}\r\n\r\nconst MembersTabContent = ({handleShowAddMember, enterpriseMembers, handleReloadMembers, handleShowEditMemberModal, currentPlan, currentPlanName}) => {\r\n\r\n  const [search, setSearch] = useState(\"\");\r\n  const [cloneEnterpriseMembers, setcloneEnterpriseMembers] = useState([]);\r\n  const [displayEnterpriseMembers, setDisplayEnterpriseMembers] = useState([]);\r\n  const [showOnly, setShowOnly] = useState(5);\r\n  const [pageCount, setPageCount] = useState(0);\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n\r\n  useEffect(() => {\r\n    let members = enterpriseMembers;\r\n    setcloneEnterpriseMembers(members);\r\n\r\n    members = limitDisplay(members,5);\r\n    setDisplayEnterpriseMembers(members);\r\n\r\n    let PageCount = getTotalPages(enterpriseMembers,5);\r\n    setPageCount(PageCount);\r\n  }, [enterpriseMembers]);\r\n\r\n\tconst handleSearch = () => {\r\n    let input = document.getElementById(\"ent_search\").value;\r\n    let members = enterpriseMembers;\r\n\r\n    members = enterpriseMembers.filter(function(item){\r\n      let full_name = item.first_name + ' ' + item.last_name;\r\n      return ((item.first_name.indexOf(input)>-1 || item.last_name.indexOf(input)>-1 || item.email.indexOf(input)>-1 || full_name.indexOf(input) > -1))\r\n    })\r\n\r\n    let PageCount = getTotalPages(members,showOnly);\r\n    setPageCount(PageCount);\r\n    setcloneEnterpriseMembers(members);\r\n\r\n    members = limitDisplay(members,showOnly);\r\n    setDisplayEnterpriseMembers(members);\r\n  };\r\n\r\n  useEffect(() => {\r\n    handleSearch();\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [showOnly]);\r\n\r\n  const generatePager = () => {\r\n    let content = [];\r\n\r\n    if (pageCount<=1 || enterpriseMembers.length===0){\r\n      return content;\r\n    }\r\n\r\n    if (currentPage>1){\r\n      content.push(<li className=\"p-2 cursor-pointer\" key={'prev_page'} onClick={(e)=>handlePagerClick(currentPage-1)}>Previous</li>);\r\n    }else{\r\n      content.push(<li className=\"p-2 cursor-default\" key={'prev_page'}>Previous</li>);\r\n    }\r\n\r\n    for (let i = 0; i < pageCount; i++) {\r\n      const isActivePage = currentPage === i + 1;\r\n      const className = `p-2 rounded cursor-pointer w-[30px] text-center ${isActivePage ? 'border' : ''}`;\r\n      content.push(\r\n        <li className={className} key={i + 1} onClick={(e) => handlePagerClick(i + 1)}>\r\n          {i + 1}\r\n        </li>\r\n      );\r\n    }\r\n\r\n    if (currentPage<pageCount){\r\n      content.push(<li className=\"p-2 cursor-pointer\" key={'prev_page'} onClick={(e)=>handlePagerClick(currentPage+1)}>Next</li>);\r\n    }else{\r\n      content.push(<li className=\"p-2 cursor-default\" key={'prev_page'}>Next</li>);\r\n    }\r\n\r\n    return content;\r\n  }\r\n\r\n  const generatePagerStats = () => {\r\n    let totalEntries = cloneEnterpriseMembers.length;\r\n    let ending = currentPage*showOnly;\r\n    let starting = ending - (showOnly-1);\r\n\r\n    if (ending > totalEntries){\r\n      ending = totalEntries;\r\n    }\r\n\r\n    return \"Showing \"+ starting + \" to \" + ending + \" of \" +  totalEntries + \" entries\";\r\n  }\r\n\r\n  const handlePagerClick = (page) => {\r\n    setCurrentPage(page);\r\n\r\n    let members = cloneEnterpriseMembers;\r\n    let arrayToLoad = (page-1)*showOnly;\r\n\r\n    members = members.slice(arrayToLoad, members.length);\r\n    members = limitDisplay(members,showOnly);\r\n\r\n    setDisplayEnterpriseMembers(members);\r\n  }\r\n\r\n  const handleShowPerPage = (event) => {\r\n    setShowOnly(event.target.value);\r\n    setCurrentPage(1);\r\n  }\r\n\r\n\r\n  const handleEdit = (user_id, first_name, last_name, email) => {\r\n    let params = {};\r\n    params['user_id'] = user_id;\r\n    params['first_name'] = first_name;\r\n    params['last_name'] = last_name;\r\n    params['email'] = email;\r\n\r\n    handleShowEditMemberModal(true,params);\r\n  }\r\n\r\n  return (\r\n    <>\r\n    <div className='w-full'>\r\n      <div className=\"block md:grid md:grid-cols-3 md:gap-4 text-[12px]\">\r\n        <div className=\"text-left md:col-span-1\">\r\n          <input className=\"w-full px-3 py-2 mb-2 border border-gray-300 rounded fs-exclude\"\r\n          type=\"text\"\r\n          id=\"ent_search\"\r\n          name=\"search\"\r\n          placeholder=\"Search by Name or Email\"\r\n          value={search}\r\n          onChange={(e) => {\r\n            handleSearch()\r\n            setSearch(e.target.value);\r\n          }}\r\n          />\r\n        </div>\r\n        <div className=\"text-left md:col-span-1\">\r\n          <motion.button\r\n          className=\"bg-sky-500 w-full md:w-48 text-white font-bold py-2 px-6 rounded-md proceed-pmt\"\r\n          whileHover={{ backgroundColor: \"#49b1df\" }}\r\n          whileTap={{ scale: 0.9 }}\r\n          onClick={()=> handleShowAddMember()}\r\n          >\r\n          + Add Member\r\n          </motion.button>\r\n\r\n        </div>\r\n        {enterpriseMembers.length > 0 ?\r\n        <div className=\"text-center md:text-right md:col-span-1 my-2 text-[11px]\">\r\n          Show <select className=\"border rounded-md\" onChange={(e) => handleShowPerPage(e)}>\r\n            <option value=\"5\">5</option>\r\n            <option value=\"10\">10</option>\r\n            <option value=\"15\">15</option>\r\n            <option value=\"20\">20</option>\r\n          </select> entries\r\n        </div>\r\n        : ''}\r\n      </div>\r\n\r\n      <div className=\"overflow-x-scroll lg:overflow-x-visible overflow-y-visible container-full-width cm-scrollbar pb-1 min-h-[400px]\">\r\n        {displayEnterpriseMembers && displayEnterpriseMembers.length ?\r\n        <table className=\"subs-table min-w-full divide-y bg-gray-50 divide-gray-200 min-h-[50px]\">\r\n          <thead>\r\n            <tr className=\"sub_tbl h-3\">\r\n              <td className=\"px-6 py-2 bg-gray-50 text-center text-[12px] font-medium bg-gray-200 text-gray-500 uppercase tracking-wider border\">Fullname</td>\r\n              <td className=\"px-6 py-2 bg-gray-50 text-center text-[12px] font-medium bg-gray-200 text-gray-500 uppercase tracking-wider border\">Email</td>\r\n              <td className=\"px-6 py-2 bg-gray-50 text-center text-[12px] font-medium bg-gray-200 text-gray-500 uppercase tracking-wider border\">Status</td>\r\n              <td className=\"px-6 py-2 bg-gray-50 text-center text-[12px] font-medium bg-gray-200 text-gray-500 uppercase tracking-wider border\">Date Added</td>\r\n              {(currentPlan.toLowerCase() === 'enterprise' && currentPlanName.toLowerCase() !== 'enterprise') ?\r\n                <td className=\"px-6 py-2 bg-gray-50 text-center text-[12px] font-medium bg-gray-200 text-gray-500 uppercase tracking-wider border\">Total Usage</td>\r\n              :\r\n                <td className=\"px-6 py-2 bg-gray-50 text-center text-[12px] font-medium bg-gray-200 text-gray-500 uppercase tracking-wider border\">Total Token Used</td>\r\n              }\r\n              \r\n              <td className=\"px-6 py-2 bg-gray-50 text-center text-[12px] font-medium bg-gray-200 text-gray-500 uppercase tracking-wider border\">Actions</td>\r\n            </tr>\r\n          </thead>\r\n          <tbody className=\"bg-white divide-y divide-gray-200\">\r\n            {displayEnterpriseMembers?.map((sub, index) => (\r\n              <tr key={index} className=\"sub_tbl h-3\">\r\n              <td className=\"px-6 py-2 bg-gray-50 text-center text-[12px] text-gray-500 uppercase tracking-wider border font-medium\">{sub.first_name} {sub.last_name}</td>\r\n              <td className=\"px-6 py-2 bg-gray-50 text-center text-[12px] text-gray-500 uppercase tracking-wider border font-medium\">{sub.email}</td>\r\n              <td className=\"px-6 py-2 bg-gray-50 text-center text-[12px] text-gray-500 uppercase tracking-wider border font-medium\">{sub.status}</td>\r\n              <td className=\"px-6 py-2 bg-gray-50 text-center text-[12px] text-gray-500 uppercase tracking-wider border font-medium\">{formatDate(sub.created_at)}</td>\r\n              <td className=\"px-6 py-2 bg-gray-50 text-center text-[12px] text-gray-500 tracking-wider border font-medium\">\r\n                {\r\n                  (currentPlan.toLowerCase() === 'enterprise' && currentPlanName.toLowerCase() !== 'enterprise') ?\r\n                  <>\r\n                    <p>Tokens: {sub.total_token}</p>\r\n                    <p>Flux: {sub.total_flux_prompt}</p>\r\n                    <p>o1: {sub.total_o1_prompt}</p>\r\n                  </>\r\n                :\r\n                  <p>{sub.total_token}</p>\r\n                }\r\n                \r\n              </td>\r\n              <td className=\"px-6 py-2 bg-gray-50 text-center text-[12px] text-gray-500 uppercase tracking-wider border font-medium\">\r\n                { sub.status === 'active' ?\r\n                <div className=\"text-[12px]\">\r\n                  <Menu as=\"div\" className=\"relative inline-block text-center w-full text-[12px]\">\r\n                    <div className='w-full'>\r\n                      <Menu.Button className=\"inline-flex w-full justify-center rounded-md bg-sky-500/100 px-4 py-2 font-medium text-white hover:bg-[#49b1df]\">\r\n                        Options\r\n                      </Menu.Button>\r\n                    </div>\r\n                    <Transition\r\n                      as={Fragment}\r\n                      enter=\"transition ease-out duration-100\"\r\n                      enterFrom=\"transform opacity-0 scale-95\"\r\n                      enterTo=\"transform opacity-100 scale-100\"\r\n                      leave=\"transition ease-in duration-75\"\r\n                      leaveFrom=\"transform opacity-100 scale-100\"\r\n                      leaveTo=\"transform opacity-0 scale-95\"\r\n                    >\r\n                      <Menu.Items className={`${\r\n                        index < 3 ? \"dp-top\" : \"dp-bot\"\r\n                      } z-[9999] absolute w-50 origin-top-right divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none text-center min-w-full text-center`}>\r\n                      <div className=\"text-center min-w-full\">\r\n                        <Menu.Item>\r\n                          <button\r\n                          className=\"group min-w-full items-center px-4 py-2 text-left border-b border-b-slate-100 text-gray-900\"\r\n                          onClick={(user_id, first_name, last_name, email) => handleEdit(sub.user_id, sub.first_name, sub.last_name, sub.email)}\r\n                          >\r\n                            Edit Info\r\n                          </button>\r\n                      </Menu.Item>\r\n                        <Menu.Item>\r\n                          <ResendPasswordMember member_user_id={sub.user_id} email={sub.email} />\r\n                        </Menu.Item>\r\n                        <Menu.Item>\r\n                          <DeleteMember member_user_id={sub.user_id} email={sub.email} handleReloadMembers={handleReloadMembers} />\r\n                        </Menu.Item>\r\n                      </div>\r\n                      </Menu.Items>\r\n                    </Transition>\r\n                  </Menu>\r\n                </div> : \"\" }\r\n              </td>\r\n            </tr>\r\n            ))}\r\n          </tbody>\r\n        </table>\r\n        : <div>\r\n          <span className=\"text-[12px] text-gray-600\"><FaInfoCircle className=\"inline text-lg mr-1\"/>No members found.</span>\r\n          </div>\r\n        }\r\n      </div>\r\n      <div className=\"py-2 text-[11px]\">\r\n        <span className=\"block md:flex float-left w-full md:w-auto text-center md:text-right py-2 justify-center md:justify-normal items-center md:items-start\">\r\n          {generatePagerStats()}\r\n        </span>\r\n        <ul className=\"w-full md:w-auto flex float-none md:float-right justify-center md:justify-normal items-center md:items-end\">\r\n        {generatePager()}\r\n        </ul>\r\n      </div>\r\n\r\n    </div>\r\n    </>\r\n  );\r\n}\r\n\r\nconst EditMemberModal = ({showEditMember, setShowEditEnterprise, editMemberDetails, handleReloadMembers}) => {\r\n  const [fullName, setfullName] = useState(\"\");\r\n  const [oldFullName, setoldFullName] = useState(\"\");\r\n  const [email, setEamil] = useState(\"\");\r\n  const [origEmail, setorigEmail] = useState(\"\");\r\n  const [userId, setUserId] = useState(\"\");\r\n\r\n  const modalEditMembersOpen = () => {\r\n    let modal = document.getElementById(\"modalEditMembers\");\r\n    if (modal!==null){\r\n\t\t\tmodal.style.display = \"block\";\r\n    }\r\n  }\r\n\r\n  const modalEditMembersClose = () => {\r\n    let modal = document.getElementById(\"modalEditMembers\");\r\n    if (modal!==null){\r\n\t\t\tlet fieldEmail = document.getElementById(\"ent-edit-email\");\r\n\t\t\tfieldEmail.title = \"\";\r\n\t\t\tfieldEmail.classList.remove('ent-field-error');\r\n\r\n\t\t\tlet fieldName = document.getElementById(\"ent-edit-fullname\");\r\n\t\t\tfieldName.title = \"\";\r\n\t\t\tfieldName.classList.remove('ent-field-error');\r\n\r\n\t\t\tconsole.log(\"modal close\");\r\n\r\n\r\n\t\t\tmodal.style.display = \"none\";\r\n      setShowEditEnterprise(false);\r\n    }\r\n  }\r\n\r\n  useEffect(() => {\r\n    let first_name = editMemberDetails.first_name ? editMemberDetails.first_name : '';\r\n    let last_name = editMemberDetails.last_name ?  editMemberDetails.last_name : '';\r\n    let email = editMemberDetails.email ?  editMemberDetails.email : '';\r\n    let user_id = editMemberDetails.user_id ?  editMemberDetails.user_id : '';\r\n\r\n    setfullName(first_name + \" \" + last_name);\r\n    setoldFullName(first_name + \" \" + last_name);\r\n    setEamil(email);\r\n    setorigEmail(email);\r\n    setUserId(user_id);\r\n  }, [editMemberDetails]);\r\n\r\n  const validateEmail = (e) => {\r\n    let email = e.target.value;\r\n    let fieldEmailError = document.getElementById(\"ent-edit-email-error\");\r\n\r\n    if (email.trim()===\"\"){\r\n      fieldEmailError.innerHTML = \"Email is required\";\r\n\t\t\tfieldEmailError.style.display = \"block\";\r\n      e.target.classList.add('ent-field-error');\r\n\t\t}else if (!/\\S+@\\S+\\.\\S+/.test(email)) {\r\n      fieldEmailError.innerHTML = \"Invalid email format\";\r\n\t\t\tfieldEmailError.style.display = \"block\";\r\n      e.target.classList.add('ent-field-error');\r\n    }else{\r\n      fieldEmailError.innerHTML = \"\";\r\n\t\t\tfieldEmailError.style.display = \"none\";\r\n      e.target.classList.remove('ent-field-error');\r\n    }\r\n  };\r\n\r\n  const validateName = (e) => {\r\n    let full_name = e.target.value.trim();\r\n    let fieldFullnameError = document.getElementById(\"ent-edit-fullname-error\");\r\n\r\n    if (full_name===\"\"){\r\n      fieldFullnameError.innerHTML = \"Full Name is required\";\r\n\t\t\tfieldFullnameError.style.display = \"block\";\r\n      e.target.classList.add('ent-field-error');\r\n    }else{\r\n      fieldFullnameError.innerHTML = \"\";\r\n\t\t\tfieldFullnameError.style.display = \"none\";\r\n      e.target.classList.remove('ent-field-error');\r\n    }\r\n\t}\r\n\r\n  if (showEditMember!==undefined && showEditMember === true){\r\n    modalEditMembersOpen();\r\n  }\r\n  if (showEditMember!==undefined && showEditMember === false){\r\n    modalEditMembersClose();\r\n  }\r\n\r\n  const updateMember = async () => {\r\n    let fieldFullname = document.getElementById(\"ent-edit-fullname\");\r\n    let fieldFullnameError = document.getElementById(\"ent-edit-fullname-error\");\r\n    let fieldEmail = document.getElementById(\"ent-edit-email\");\r\n    let fieldEmailError = document.getElementById(\"ent-edit-email-error\");\r\n\r\n\t\t// fieldError2.style.display = \"block\";\r\n\r\n    if (fieldEmail.value.trim()===\"\"){\r\n      fieldEmailError.innerHTML = \"Email is required\";\r\n\t\t\tfieldEmailError.style.display = \"block\";\r\n\t\t\tfieldEmail.classList.add('ent-field-error');\r\n\t\t\treturn;\r\n\t\t}else if (!/\\S+@\\S+\\.\\S+/.test(email)) {\r\n      fieldEmailError.innerHTML = \"Invalid email format\";\r\n\t\t\tfieldEmailError.style.display = \"block\";\r\n      fieldEmail.classList.add('ent-field-error');\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n    if (fieldFullname.value.trim()===\"\"){\r\n      fieldFullnameError.innerHTML = \"Full Name is required\";\r\n\t\t\tfieldFullnameError.style.display = \"block\";\r\n      fieldFullname.classList.add('ent-field-error');\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n    document.querySelector(\".loader-container\").classList.add('active');\r\n\r\n    if (oldFullName===fieldFullname.value && origEmail === fieldEmail.value){\r\n      modalEditMembersClose();\r\n      document.querySelector(\".loader-container\").classList.remove('active');\r\n      return;\r\n    }\r\n\r\n    let url = `${process.env.REACT_APP_API_URL}/edit-enterprise-member`;\r\n    axios.post(url, {\r\n      member_user_id: userId,\r\n      member_email: fieldEmail.value,\r\n      member_fullname : fieldFullname.value,\r\n      member_old_fullname: oldFullName,\r\n      member_old_email: origEmail,\r\n    }, { headers: { 'content-type': 'application/x-www-form-urlencoded' } }).then(function(res) {\r\n      let output = res.data;\r\n\r\n      document.querySelector(\".loader-container\").classList.remove('active');\r\n\r\n      if(output.success) {\r\n        if (Array.isArray(output.data) && output.data.length > 0){\r\n          for(var i = 0; i < output.data.length; i++) {\r\n            let fieldEmail = document.getElementById(\"ent-edit-email\");\r\n            fieldEmail.title = output.data[i].error;\r\n            fieldEmail.classList.add('ent-field-error');\r\n            toastr.error(output.data[i].error);\r\n          }\r\n        }else{\r\n          modalEditMembersClose();\r\n          handleReloadMembers();\r\n          toastr.success(\"Member Updated\");\r\n        }\r\n      }else{\r\n        modalEditMembersClose();\r\n        handleReloadMembers();\r\n        toastr.error(\"Update Member Failed\");\r\n      }\r\n\r\n    }).catch(function (error) {\r\n      modalEditMembersClose();\r\n      document.querySelector(\".loader-container\").classList.remove('active');\r\n      toastr.error(\"Something went wrong. Please try again in a bit!\");\r\n    });\r\n\r\n  }\r\n\r\n  return (\r\n    <>\r\n    <div id=\"modalEditMembers\" className=\"modal z-[9999]\">\r\n      <div class=\"modal-content w-full md:w-[50%]\">\r\n        <div>\r\n          <div className=\"mb-4 flex border-b pb-2\">\r\n            <div className=\"text-blue-500 w-full md:w-2/3 mr-2 md:mr-5 font-bold\">\r\n            Edit Members\r\n            </div>\r\n            <div className=\"float-right mt-[-5px] w-full md:w-1/3\">\r\n              <span class=\"close\" onClick={()=> modalEditMembersClose()}>&times;</span>\r\n            </div>\r\n          </div>\r\n          <div className=\"border-b border-[#dddddd] mx-auto my-4 pb-4\">\r\n              <div className=\"w-full text-[12px] md:text-[12px] font-bold pb-2\">\r\n                <p>Fulname *</p>\r\n                <input type=\"text\" id=\"ent-edit-fullname\" className=\"w-full px-3 py-2 border border-gray-300 rounded\"\r\n                placeholder=\"Fullname\"\r\n                value={fullName}\r\n                onChange={(e) => {\r\n                  e.preventDefault();\r\n                  setfullName(e.target.value);\r\n                  validateName(e);\r\n                  }\r\n                }\r\n                title=\"\"\r\n                ></input>\r\n                <div class=\"member-error\" id=\"ent-edit-fullname-error\"></div>\r\n              </div>\r\n              <div className=\"w-full text-[12px] md:text-[12px] font-bold pb-2\">\r\n                <p>Email *</p>\r\n                <input type=\"text\" id=\"ent-edit-email\" className=\"w-full px-3 py-2 border border-gray-300 rounded\"\r\n                placeholder=\"Email\"\r\n                value={email}\r\n                onChange={(e) => {\r\n                  e.preventDefault();\r\n                  setEamil(e.target.value);\r\n                  validateEmail(e);\r\n                  }\r\n                }\r\n                title=\"\"\r\n                ></input>\r\n                <div class=\"member-error\" id=\"ent-edit-email-error\"></div>\r\n              </div>\r\n          </div>\r\n          <div className=''>\r\n            <div className=\"text-right\">\r\n              <input type=\"button\" value=\"Update Member\" className=\"border rounded font-bold bg-blue-500 text-white py-2 px-4 cursor-pointer text-[12px] md:text-[14px] mr-2\" onClick={()=> updateMember()}/>\r\n              <input type=\"button\" value=\"Close\" className=\"border rounded font-bold bg-white text-black py-2 px-4 cursor-pointer text-[12px] md:text-[14px]\" onClick={()=> modalEditMembersClose()}/>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    </>\r\n  )\r\n}\r\n\r\nconst AddMemberModal = ({showAddMember, setshowAddMember, handleReloadMembers, entMaxMembers, enterpriseMembers, isMax, isVisbleBtn, setIsVisibleBtn }) => {\r\n\r\n  const [members, setMembers] = useState([\r\n    { name: '', email: '', error: '' }\r\n  ]);\r\n  const [addMemberBtn, setAddMemberBtn] = useState(true);\r\n  const [isRemoveBtn, setRemoveBtn] = useState(true)\r\n\r\n  const addMember = () => {\r\n    setMembers(current => [...current, { name: '', email: '', error: '' }]);\r\n\r\n    let total_members_after_insert = members.length+enterpriseMembers.length;\r\n    \r\n    setIsVisibleBtn(true);\r\n    setRemoveBtn(true);\r\n    if (isMax && total_members_after_insert + 1 >= entMaxMembers) {\r\n      setAddMemberBtn(false);\r\n    }\r\n  };\r\n\r\n  const resetMember = async () => {\r\n    await setMembers(current => {\r\n      return current.filter((_, i) => i <= -1)\r\n    });\r\n    addMember();\r\n    setAddMemberBtn(true);\r\n    setRemoveBtn(true);\r\n  };\r\n\r\n  const removeMember = (index) => {\r\n    setMembers(current => {\r\n      return current.filter((_, i) => i !== index)\r\n    });\r\n\r\n    let newTotalMembers = members.length + enterpriseMembers.length - 1;\r\n\r\n    if (isMax && newTotalMembers < entMaxMembers) {\r\n      setIsVisibleBtn(true);\r\n      setAddMemberBtn(true);\r\n    }\r\n\r\n    if (isMax && members.length === 1) { \r\n      setRemoveBtn(false); \r\n    }\r\n   \r\n  };\r\n\r\n  const saveMember = async () => {\r\n\r\n    let total_members_after_insert = members.length+enterpriseMembers.length;\r\n    if (total_members_after_insert>entMaxMembers){\r\n      toastr.error(\"Members exceeded. Max. members should only \"+entMaxMembers+\" members.\");\r\n      return;\r\n    }\r\n\r\n    let hasError = await validateFields();\r\n    if (hasError){\r\n      return;\r\n    }\r\n\r\n    var url = `${process.env.REACT_APP_API_URL}/add-enterprise-member`;\r\n\r\n    if (members.length<=0){\r\n      modalAddMembersClose();\r\n      return;\r\n    }\r\n\r\n    document.querySelector(\".loader-container\").classList.add('active');\r\n\r\n    axios.post(url, {\r\n      members: JSON.stringify(members)\r\n    }, { headers: { 'content-type': 'application/x-www-form-urlencoded' } }).then(function(res) {\r\n      let output = res.data;\r\n\r\n      document.querySelector(\".loader-container\").classList.remove('active');\r\n\r\n      if(output.success) {\r\n        if (Array.isArray(output.data) && output.data.length > 0){\r\n          for(var i = 0; i < output.data.length; i++) {\r\n            let index = output.data[i].index;\r\n            let fieldEmail = document.getElementById(\"email-\"+index);\r\n            fieldEmail.title = output.data[i].error;\r\n            fieldEmail.classList.add('ent-field-error');\r\n\r\n            let fieldError2 = document.getElementById(\"error2-\"+index);\r\n            fieldError2.style.display = \"block\";\r\n            fieldError2.innerHTML = output.data[i].error;\r\n          }\r\n        }else{\r\n          toastr.success(\"Members Added\");\r\n          modalAddMembersClose();\r\n          handleReloadMembers();\r\n          resetMember();\r\n        }\r\n      }else{\r\n        toastr.error(\"Adding Members Failed\");\r\n        modalAddMembersClose();\r\n        handleReloadMembers();\r\n        resetMember();\r\n      }\r\n\r\n    }).catch(function (error) {\r\n      modalAddMembersClose();\r\n      document.querySelector(\".loader-container\").classList.remove('active');\r\n      toastr.error(\"Something went wrong. Please try again in a bit!\");\r\n    });\r\n\r\n  }\r\n\r\n  const removeLastMember = () => {\r\n    if (members.length>0){\r\n      let lastIndex = members.length - 1;\r\n      removeMember(lastIndex);\r\n    }\r\n\r\n  }\r\n\r\n  const modalAddMembersOpen = () => {\r\n    let modal = document.getElementById(\"modalAddMembers\");\r\n    if (modal!==null){\r\n      modal.style.display = \"block\";\r\n    }\r\n  }\r\n\r\n  const modalAddMembersClose = () => {\r\n    let modal = document.getElementById(\"modalAddMembers\");\r\n    if (modal!==null){\r\n      modal.style.display = \"none\";\r\n      setshowAddMember(false);\r\n    }\r\n  }\r\n\r\n\r\n  const validateEmail = (e, index) => {\r\n    let email = e.target.value;\r\n    let isValid = true;\r\n    let fieldName = document.getElementById(\"name-\"+index);\r\n    let full_name = fieldName.value.trim();\r\n    let fieldError1 = document.getElementById(\"error1-\"+index);\r\n    let fieldError2 = document.getElementById(\"error2-\"+index);\r\n\r\n    if (email.trim()===\"\"){\r\n      e.target.title = \"Email is required\";\r\n      e.target.classList.add('ent-field-error');\r\n      fieldError2.style.display = \"block\";\r\n      fieldError2.innerHTML = \"Email is required\";\r\n      isValid = false;\r\n    }else if (!/\\S+@\\S+\\.\\S+/.test(email)) {\r\n      e.target.title = \"Invalid email format\";\r\n      e.target.classList.add('ent-field-error');\r\n      fieldError2.style.display = \"block\";\r\n      fieldError2.innerHTML = \"Invalid email format\";\r\n      isValid = false;\r\n    }\r\n\r\n    if (isValid){\r\n      e.target.title = \"\";\r\n      e.target.classList.remove('ent-field-error');\r\n      fieldError1.style.display = \"none\";\r\n      fieldError1.innerHTML = \"\";\r\n      fieldError2.style.display = \"none\";\r\n      fieldError2.innerHTML = \"\";\r\n    }\r\n\r\n    if (isValid && full_name==='') {\r\n      fieldName.title = \"Full Name is required\";\r\n      fieldName.classList.add('ent-field-error');\r\n      fieldError1.style.display = \"block\";\r\n      fieldError1.innerHTML = \"Full Name is required\";\r\n    }\r\n  };\r\n\r\n  const validateName = (e, index) => {\r\n    let full_name = e.target.value.trim();\r\n    let fieldEmail = document.getElementById(\"email-\"+index).value;\r\n    let fieldError1 = document.getElementById(\"error1-\"+index);\r\n\r\n    if (full_name===\"\" && fieldEmail!==''){\r\n      e.target.title = \"Full Name is required\";\r\n      e.target.classList.add('ent-field-error');\r\n      fieldError1.style.display = \"block\";\r\n      fieldError1.innerHTML = \"Full Name is required\";\r\n    }else{\r\n      e.target.title = \"\";\r\n      e.target.classList.remove('ent-field-error');\r\n      fieldError1.style.display = \"none\";\r\n      fieldError1.innerHTML = \"\";\r\n\r\n    }\r\n  }\r\n\r\n  const validateFields = async () => {\r\n    let i = 0;\r\n    let hasError = false;\r\n    let fieldName = \"\";\r\n    let fieldEmail = \"\";\r\n    let full_name = \"\";\r\n    let fieldError1 = \"\";\r\n    let fieldError2 = \"\";\r\n    let server_error = \"\";\r\n    let doesExist = false;\r\n\r\n    while (i < members.length) {\r\n      fieldName = document.getElementById(\"name-\"+i);\r\n      fieldEmail = document.getElementById(\"email-\"+i);\r\n      fieldError1 = document.getElementById(\"error1-\"+i);\r\n      fieldError2 = document.getElementById(\"error2-\"+i);\r\n\r\n      let email = members[i].email.trim();\r\n      full_name = members[i].name.trim();\r\n      server_error = members[i].error.trim();\r\n\r\n      fieldError1.style.display = \"none\";\r\n      fieldError2.style.display = \"none\";\r\n      fieldName.title = \"\";\r\n      fieldName.classList.remove('ent-field-error');\r\n      fieldEmail.title = \"\";\r\n      fieldEmail.classList.remove('ent-field-error');\r\n\r\n      if (email===\"\") {\r\n        fieldEmail.title = \"Email is required\";\r\n        fieldEmail.classList.add('ent-field-error');\r\n        fieldError2.style.display = \"block\";\r\n        fieldError2.innerHTML = \"Email is required\"\r\n        hasError = true;\r\n      } else if (!/\\S+@\\S+\\.\\S+/.test(email)) {\r\n        fieldEmail.title = \"Invalid email format\";\r\n        fieldEmail.classList.add('ent-field-error');\r\n        fieldError2.style.display = \"block\";\r\n        fieldError2.innerHTML = \"Invalid email format\"\r\n        hasError = true;\r\n      }\r\n\r\n      if (server_error!==\"\"){\r\n        fieldEmail.title = server_error;\r\n        fieldEmail.classList.add('ent-field-error');\r\n        hasError = true;\r\n      }\r\n\r\n      if (full_name===\"\"){\r\n        fieldName.title = \"Full Name is required\";\r\n        fieldName.classList.add('ent-field-error');\r\n        fieldError1.innerHTML = \"Full Name is required\"\r\n        fieldError1.style.display = \"block\";\r\n        hasError = true;\r\n      }\r\n\r\n      if (!hasError){\r\n        doesExist = members.filter(function(item){\r\n          return ((item.email.trim()===email))\r\n        })\r\n\r\n        if (doesExist.length>1){\r\n          fieldEmail.title = \"Duplicate email\";\r\n          fieldEmail.classList.add('ent-field-error');\r\n          fieldError2.innerHTML = \"Duplicate email\"\r\n          fieldError2.style.display = \"block\";\r\n          hasError = true;\r\n        }\r\n      }\r\n\r\n      i++;\r\n    }\r\n\r\n    return hasError;\r\n  }\r\n\r\n  if (showAddMember!==undefined && showAddMember === true){\r\n    modalAddMembersOpen();\r\n  }\r\n  if (showAddMember!==undefined && showAddMember === false){\r\n    modalAddMembersClose();\r\n  }\r\n\r\n  return (\r\n    <>\r\n\t\t<div id=\"modalAddMembers\" className=\"modal z-[9999]\">\r\n\t\t\t<div class=\"modal-content w-full md:w-[60%]\">\r\n        <div>\r\n          <div className=\"mb-4 flex border-b pb-2\">\r\n            <div className=\"text-blue-500 w-full md:w-2/3 mr-2 md:mr-5 font-bold\">\r\n            Add Members\r\n            </div>\r\n            <div className=\"float-right mt-[-5px] w-full md:w-1/3\">\r\n              <span class=\"close\" onClick={()=> modalAddMembersClose()}>&times;</span>\r\n            </div>\r\n          </div>\r\n          <div className=\"text-[11px] pb-2\">\r\n          <b>Note:</b> The password for each member will be sent to their email address\r\n          </div>\r\n          <div className=\"\">\r\n            <div className=\"border rounded-tr rounded-tl flex p-2\">\r\n              <div className=\"w-1/2 text-[12px] md:text-[12px] font-bold\">\r\n              Full Name\r\n              </div>\r\n              <div className=\"w-1/2 ml-[-4px] text-[12px] md:text-[12px] font-bold\">\r\n              Email\r\n              </div>\r\n            </div>\r\n            {members?.map((mem, index) => (\r\n            <div className=\"flex py-2 pl-2 bg-gray-100\">\r\n              <div className=\"w-1/2 mr-2 text-[11px] md:text-[14px]\">\r\n                <input type=\"text\" id={\"name-\"+index} index={index} className=\"w-full px-3 py-2 border border-gray-300 rounded fs-exclude\" placeholder=\"Enter Full Name\" value={mem.name}\r\n                onChange={(e) => {\r\n                  setMembers(s => {\r\n                    const newArr = s.slice();\r\n                    newArr[index].name = e.target.value;\r\n                    validateName(e, index);\r\n                    return newArr;\r\n                  });\r\n                }}\r\n                title=\"\"\r\n                ></input>\r\n                <div class=\"member-error\" id={\"error1-\"+index}></div>\r\n              </div>\r\n              <div className=\"w-1/2 mr-2 text-[11px] md:text-[14px]\">\r\n                <input type=\"text\" id={\"email-\"+index} index={index} className=\"w-full px-3 py-2 border border-gray-300 rounded fs-exclude\" placeholder=\"Enter Email\" value={mem.email}\r\n                onChange={(e) => {\r\n                  setMembers(s => {\r\n                    const newArr = s.slice();\r\n                    newArr[index].email = e.target.value;\r\n                    validateEmail(e, index)\r\n                    return newArr;\r\n                  });\r\n                }}\r\n                title=\"\"\r\n                ></input>\r\n                <div class=\"member-error\" id={\"error2-\"+index}></div>\r\n              </div>\r\n              <div className=\"w-1/20 text-right pt-2\">\r\n                <button onClick={() => removeMember(index)}>\r\n                  <span><FaTrash className=\"text-lg mr-2 text-red-500\" /></span>\r\n                </button>\r\n              </div>\r\n            </div>\r\n            ))}\r\n          </div>\r\n          <div className={`${isMax && !addMemberBtn ? 'justify-end' : (!isVisbleBtn ? 'justify-end' : 'justify-between')} flex border-b border-[#dddddd] mx-auto my-4 pb-4`}>\r\n          {addMemberBtn && isVisbleBtn && (\r\n              <input \r\n                type=\"button\" \r\n                value=\"Add More\" \r\n                className=\"border rounded font-bold bg-white py-2 px-4 cursor-pointer text-[12px] md:text-[14px]\" \r\n                onClick={() => addMember()} \r\n              />\r\n            )}\r\n          {isRemoveBtn && (\r\n            <input type=\"button\" value=\"Remove Last\" className=\"border rounded font-bold bg-white float-right py-2 px-4 cursor-pointer text-[12px] md:text-[14px]\" onClick={()=> removeLastMember()}/>\r\n          )}\r\n          </div>\r\n          {isRemoveBtn && (\r\n            <div className=\"text-right\">\r\n              <input type=\"button\" value=\"Add Member\" className=\"border rounded font-bold bg-blue-500 text-white py-2 px-4 cursor-pointer text-[12px] md:text-[14px]\" onClick={()=> saveMember()}/>\r\n            </div>\r\n          )} \r\n        </div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n    </>\r\n  );\r\n}\r\n\r\nconst DeleteMember = ({ member_user_id, email, handleReloadMembers }) => {\r\n  const [isShowModal, setIsShowModal] = useState(false);\r\n  const showDeleteMemberModal = () => {\r\n    setIsShowModal(true);\r\n  };\r\n  const closeModal = () => {\r\n    setIsShowModal(false);\r\n  };\r\n\r\n  const deleteMember = (event) => {\r\n    document.querySelector(\".loader-container\").classList.add('active');\r\n    closeModal();\r\n\r\n    axios.post(`${process.env.REACT_APP_API_URL}/delete-enterprise-member`, {\r\n      member_user_id: member_user_id,\r\n      member_email: email\r\n    }, { headers: { 'content-type': 'application/x-www-form-urlencoded' } }).then(function(res){\r\n      let output = res.data;\r\n      if(output.success) {\r\n        document.querySelector(\".loader-container\").classList.remove('active');\r\n        toastr.success(\"Delete success.\");\r\n        handleReloadMembers();\r\n        return;\r\n      }\r\n      document.querySelector(\".loader-container\").classList.remove('active');\r\n      if(output.data) toastr.error(output.data.msg);\r\n    });\r\n\r\n\r\n  };\r\n  return (\r\n    <>\r\n      <button\r\n      className={`hover:bg-sky-500/100 hover:text-white text-gray-900 group min-w-full items-center px-4 py-2 text-left`}\r\n      onClick={showDeleteMemberModal}\r\n      >\r\n        Delete\r\n      </button>\r\n      <Transition appear show={isShowModal} as={Fragment}>\r\n        <Dialog as=\"div\" className=\"relative z-10\" onClose={closeModal}>\r\n          <Transition.Child\r\n            as={Fragment}\r\n            enter=\"ease-out duration-300\"\r\n            enterFrom=\"opacity-0\"\r\n            enterTo=\"opacity-100\"\r\n            leave=\"ease-in duration-200\"\r\n            leaveFrom=\"opacity-100\"\r\n            leaveTo=\"opacity-0\"\r\n          >\r\n            <div className=\"fixed inset-0 bg-black bg-opacity-25\" />\r\n          </Transition.Child>\r\n\r\n          <div className=\"fixed inset-0 overflow-y-auto\">\r\n            <div className=\"flex min-h-full items-center justify-center p-4 text-center\">\r\n              <Transition.Child\r\n                as={Fragment}\r\n                enter=\"ease-out duration-300\"\r\n                enterFrom=\"opacity-0 scale-95\"\r\n                enterTo=\"opacity-100 scale-100\"\r\n                leave=\"ease-in duration-200\"\r\n                leaveFrom=\"opacity-100 scale-100\"\r\n                leaveTo=\"opacity-0 scale-95\"\r\n              >\r\n                <Dialog.Panel className=\"w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all\">\r\n                  <Dialog.Title\r\n                    as=\"h3\"\r\n                    className=\"text-red-800 w-full md:w-2/3 mr-2 md:mr-5 font-bold\"\r\n                  >\r\n                    Delete Member\r\n                  </Dialog.Title>\r\n                  <div className=\"mt-2\">\r\n                    <p className=\"text-sm text-gray-500 break-words border-t border-b border-[#dddddd] py-2\">\r\n                      <AiFillWarning className=\"inline text-sm mr-1 text-red-800\"/>Are you sure you want to delete <strong>{email}</strong> account?\r\n                    </p>\r\n                  </div>\r\n\r\n                  <div className=\"mt-4 text-right\">\r\n                    <button\r\n                      type=\"button\"\r\n                      className=\"inline-flex justify-center rounded-md border border-transparent bg-red-500 px-4 py-2 mr-2 text-sm font-medium text-white hover:bg-[#d30101]\"\r\n                      onClick={deleteMember}\r\n                    >\r\n                      Delete Member\r\n                    </button>\r\n                    <button\r\n                      type=\"button\"\r\n                      className=\"inline-flex justify-center rounded-md border border-transparent bg-gray-100 px-4 py-2 text-sm font-medium text-black hover:bg-gray-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-gray-500 focus-visible:ring-offset-2\"\r\n                      onClick={closeModal}\r\n                    >\r\n                      Close\r\n                    </button>\r\n                  </div>\r\n                </Dialog.Panel>\r\n              </Transition.Child>\r\n            </div>\r\n          </div>\r\n        </Dialog>\r\n      </Transition>\r\n    </>\r\n  )\r\n}\r\n\r\nconst ResendPasswordMember = ({ member_user_id, email }) => {\r\n  const [isShowModal, setIsShowModal] = useState(false);\r\n  const showDeleteMemberModal = () => {\r\n    setIsShowModal(true);\r\n  };\r\n  const closeModal = () => {\r\n    setIsShowModal(false);\r\n  };\r\n\r\n  const resendPassword = (event) => {\r\n    document.querySelector(\".loader-container\").classList.add('active');\r\n    closeModal();\r\n\r\n    axios.post(`${process.env.REACT_APP_API_URL}/resend-pass-enterprise-member`, {\r\n      member_user_id: member_user_id,\r\n      member_email: email\r\n    }, { headers: { 'content-type': 'application/x-www-form-urlencoded' } }).then(function(res){\r\n      let output = res.data;\r\n      if(output.success) {\r\n        toastr.success(\"New Password sent.\");\r\n        document.querySelector(\".loader-container\").classList.remove('active');\r\n        return;\r\n      }\r\n      document.querySelector(\".loader-container\").classList.remove('active');\r\n      if(output.data) toastr.error(output.data.msg);\r\n    });\r\n\r\n\r\n  };\r\n  return (\r\n    <>\r\n      <button\r\n      className={`hover:bg-sky-500/100 hover:text-white text-gray-900 group min-w-full items-center px-4 py-2 text-left`}\r\n      onClick={showDeleteMemberModal}\r\n      >\r\n        Resend Password\r\n      </button>\r\n      <Transition appear show={isShowModal} as={Fragment}>\r\n        <Dialog as=\"div\" className=\"relative z-10\" onClose={closeModal}>\r\n          <Transition.Child\r\n            as={Fragment}\r\n            enter=\"ease-out duration-300\"\r\n            enterFrom=\"opacity-0\"\r\n            enterTo=\"opacity-100\"\r\n            leave=\"ease-in duration-200\"\r\n            leaveFrom=\"opacity-100\"\r\n            leaveTo=\"opacity-0\"\r\n          >\r\n            <div className=\"fixed inset-0 bg-black bg-opacity-25\" />\r\n          </Transition.Child>\r\n\r\n          <div className=\"fixed inset-0 overflow-y-auto\">\r\n            <div className=\"flex min-h-full items-center justify-center p-4 text-center\">\r\n              <Transition.Child\r\n                as={Fragment}\r\n                enter=\"ease-out duration-300\"\r\n                enterFrom=\"opacity-0 scale-95\"\r\n                enterTo=\"opacity-100 scale-100\"\r\n                leave=\"ease-in duration-200\"\r\n                leaveFrom=\"opacity-100 scale-100\"\r\n                leaveTo=\"opacity-0 scale-95\"\r\n              >\r\n                <Dialog.Panel className=\"w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all\">\r\n                  <Dialog.Title\r\n                    as=\"h3\"\r\n                    className=\"text-lg font-medium leading-6 text-gray-900\"\r\n                  >\r\n                    Resend Password\r\n                  </Dialog.Title>\r\n                  <div className=\"mt-2\">\r\n                    <p className=\"text-sm text-gray-500 break-words\">\r\n                      Do you want to proceed with sending the new password to <strong>{email}</strong>?\r\n                    </p>\r\n                  </div>\r\n\r\n                  <div className=\"mt-4 text-right\">\r\n                    <button\r\n                      type=\"button\"\r\n                      className=\"inline-flex justify-center rounded-md border border-transparent bg-red-500 px-4 py-2 mr-2 text-sm font-medium text-white hover:bg-[#d30101]\"\r\n                      onClick={resendPassword}\r\n                    >\r\n                      Resend Password\r\n                    </button>\r\n                    <button\r\n                      type=\"button\"\r\n                      className=\"inline-flex justify-center rounded-md border border-transparent bg-gray-100 px-4 py-2 text-sm font-medium text-black hover:bg-gray-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-gray-500 focus-visible:ring-offset-2\"\r\n                      onClick={closeModal}\r\n                    >\r\n                      Close\r\n                    </button>\r\n                  </div>\r\n                </Dialog.Panel>\r\n              </Transition.Child>\r\n            </div>\r\n          </div>\r\n        </Dialog>\r\n      </Transition>\r\n    </>\r\n  )\r\n}\r\n\r\nexport default Manage;", "import { useEffect, useState } from 'react';\r\nimport { motion } from \"framer-motion\";\r\nimport axios from 'axios';\r\nimport aiproLogo from '../assets/images/AIPRO.svg';\r\nimport { FaInfoCircle } from 'react-icons/fa';\r\nimport { Auth } from '../core/utils/auth';\r\nimport toastr from 'toastr';\r\nimport 'toastr/build/toastr.min.css';\r\n\r\nvar price_per_member = '0';\r\nvar user_id = '';\r\n\r\nexport function AddMoreMemberModal ({showAddMoreMember, setshowAddMoreMember, setMoreToAddMember, setMoreToAddMemberTotalAmount, setShowCompletePurchase}) {\r\n  const [membersToAdd, setMembersToAdd] = useState(1);\r\n  const [totalAmount, setTotalAmount] = useState(0);\r\n  const [paymentInterval, setpaymentInterval] = useState(\"\");\r\n\r\n  const auth = Auth();\r\n\r\n  useEffect(() => {\r\n    if (auth !== undefined && auth.plan==='enterprise'){\r\n      price_per_member = auth.price_per_member;\r\n\r\n      if (auth.interval.toLowerCase()===\"monthly\"){\r\n        setpaymentInterval(\"MONTH\");        \r\n      }else{\r\n        setpaymentInterval(\"YEAR\");       \r\n      }\r\n\r\n    }\r\n  }, [auth]);\r\n\r\n\r\n  const modalAddMoreMembersOpen = () => {\r\n    let modal = document.getElementById(\"modalAddMoreMembers\");\t\t\r\n    if (modal!==null){\r\n      modal.style.display = \"block\";\r\n    }\r\n  }\r\n\r\n  const modalAddMoreMembersClose = () => {\r\n    let modal = document.getElementById(\"modalAddMoreMembers\");\t\t\r\n    if (modal!==null){\r\n      modal.style.display = \"none\";\t\t \r\n      setshowAddMoreMember(false);\r\n    }\r\n  }\r\n\r\n  const handlePlus = () => {\r\n    setMembersToAdd(membersToAdd+1);\r\n  }\r\n\r\n  const handleMinus = () => {\r\n    if (membersToAdd>1){\r\n      setMembersToAdd(membersToAdd-1);\r\n    }\r\n  }\r\n  \r\n  useEffect(() => {\r\n    toastr.options = {\r\n      positionClass: 'toast-top-center'\r\n    };\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    let amount = membersToAdd * price_per_member;\r\n    setTotalAmount(amount);\r\n  }, [membersToAdd]);\r\n\r\n  useEffect(() => {\r\n    setMembersToAdd(1);\r\n\r\n    let amount = membersToAdd * price_per_member;\r\n    setTotalAmount(amount);\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps    \r\n  }, [showAddMoreMember]);\r\n\r\n  const handleUupgradeNow = () => {\r\n    window.location.href = '/upgrade-ent/'+membersToAdd;\r\n    return;\r\n\r\n    // setMoreToAddMember(membersToAdd);\r\n    // setMoreToAddMemberTotalAmount(totalAmount);\r\n\r\n    // setshowAddMoreMember(false);\r\n    // setShowCompletePurchase(true)\r\n  }\r\n\r\n  if (showAddMoreMember!==undefined && showAddMoreMember === true){\r\n    modalAddMoreMembersOpen();\r\n  }\r\n  if (showAddMoreMember!==undefined && showAddMoreMember === false){\r\n    modalAddMoreMembersClose();\r\n  }\r\n\r\n  return (\r\n    <>\r\n\t\t<div id=\"modalAddMoreMembers\" className=\"modal z-[9999]\">\r\n\t\t\t<div class=\"modal-content w-full md:w-[60%] max-w-[90%] p-3 md:p-4\">\r\n        <div>\r\n          <div className=\"mb-4 flex border-b pb-2\">\r\n            <div className=\"text-blue-500 w-full md:w-2/3 mr-2 md:mr-5 font-bold\">\r\n            Add More Members\r\n            </div>\r\n            <div className=\"float-right mt-[-5px] w-full md:w-1/3\">\r\n              <span class=\"close\" onClick={()=> modalAddMoreMembersClose()}>&times;</span>\r\n            </div>\r\n          </div>\r\n          <div className=\"p-2 md:p-4 border rounded text-sm text-center\">\r\n            <div>Your enterprise account has hit its maximum user capacity.</div>\r\n            <div>Add more members to your Enterprise Account.</div>\r\n            <div className=\"py-4 text-center\">\r\n              <span className=\"font-bold text-[12px] sm:text-sm inline px-1 sm:px-2\">Add</span>\r\n              <div className=\"border rounded px-2 py-4 inline\">\r\n                <input className=\"bg-blue-500 rounded px-3 py-2 m-2 text-white cursor-pointer\" type=\"button\" value=\"-\" onClick={()=>handleMinus()}/>\r\n                <span className=\"text-blue-500 p-2 mx-auto font-bold\">{membersToAdd}</span>\r\n                <input className=\"bg-blue-500 rounded px-3 py-2 m-2 text-white cursor-pointer\" type=\"button\" value=\"+\" onClick={()=>handlePlus()} />\r\n              </div>\r\n              <span className=\"font-bold text-[12px] sm:text-sm inline px-1 sm:px-2\">Member/s</span>\r\n            </div>\r\n            <div className=\"font-bold text-sm\">\r\n              Total Amount: ${totalAmount}\r\n            </div>\r\n            <div className=\"font-bold text-sm\">\r\n              PER {paymentInterval}\r\n            </div>\r\n            <div>\r\n              <motion.button\r\n              className=\"bg-sky-600 w-full md:w-70 text-white font-bold my-4 py-2 px-6 rounded proceed-pmt\"\r\n              whileHover={{ backgroundColor: \"#49b1df\" }}\r\n              whileTap={{ scale: 0.9 }}\r\n              onClick={()=> handleUupgradeNow()}\r\n              >\r\n              UPGRADE NOW\r\n              </motion.button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n    </>\r\n  );\r\n}\r\n\r\nexport function MemberCompletePurchase ({moreToAddMember, moreToAddMemberTotalAmount, setShowCompletePurchase, showCompletePurchase}) {\r\n\r\n  const auth = Auth();\r\n\r\n  useEffect(() => {\r\n    if (auth !== undefined && auth.plan==='enterprise'){\r\n      user_id = auth.user_id;\r\n    }\r\n  }, [auth]);\r\n\r\n  const modalOpen = () => {\r\n    let modal = document.getElementById(\"modalComplete\");\t\t\r\n    if (modal!==null){\r\n      modal.style.display = \"block\";\t\t\r\n    }\r\n  }\r\n\r\n  const modalClose = () => {\r\n    let modal = document.getElementById(\"modalComplete\");\t\t\r\n    if (modal!==null){\r\n      modal.style.display = \"none\";\t\t \r\n      setShowCompletePurchase(false);\r\n    }\r\n  }\r\n\r\n  const submitPaymentInformation = () => {\r\n    window.location.href = '/payment-reference';\r\n    return;\r\n  }\r\n\r\n  const sendViaEmail = () => {\r\n    var members = moreToAddMember;\r\n    var total_amount = moreToAddMemberTotalAmount;\r\n    var url = `${process.env.REACT_APP_API_URL}/t/send-enterprise-payment-info`;\r\n\r\n    modalClose();    \r\n    document.querySelector(\".loader-container\").classList.add('active');\r\n\r\n    axios.post(url, {\r\n      members,\r\n      total_amount\r\n    }, { headers: { 'content-type': 'application/x-www-form-urlencoded' } }).then(function(res) {\r\n      let output = res.data;\r\n\r\n      document.querySelector(\".loader-container\").classList.remove('active');\r\n\r\n      if(output.success) {\r\n        toastr.success(\"Email sent to \"+output.data);\r\n      }else{\r\n        toastr.error(\"Email Failed.\");\r\n      }\r\n    }).catch(function (error) {\r\n      document.querySelector(\".loader-container\").classList.remove('active');\r\n      if (error.response && error.response.status===429) {\r\n        document.querySelector(\".loader-container\").classList.remove('active');\r\n        toastr.error(\"Sorry, too many requests. Please try again in a bit!\");\r\n      }\r\n    });\r\n  }\r\n\r\n  if (showCompletePurchase!==undefined && showCompletePurchase === true){\r\n    modalOpen();\r\n  }\r\n  if (showCompletePurchase!==undefined && showCompletePurchase === false){\r\n    modalClose();\r\n  }\r\n\r\n  return (\r\n    <>\r\n    <div id=\"modalComplete\" className=\"modal z-[9999]\">\r\n      <div class=\"w-full md:w-[600px] border-[#888] md:mt-[15px] mx-[auto] bg-[#fefefe] p-6\">\r\n        <span class=\"close\" onClick={()=> modalClose()}>&times;</span>\r\n        <div className=\"border-b pb-[10px] border-[#d5d5d5]\"><img src={aiproLogo} alt=\"AI-Pro Logo\" className=\"aiprologo mx-auto\"/></div>\r\n        <h1 className=\"font-bold text-center p-2 text-gray-700 text-[20px] md:text-[24px]\">Payment Details<br/>for Enterprise Order</h1>\r\n        <div className=\"text-center text-[12px] md:text-[14px]\">Adding more than 10 Enterprise users requires prior payment.</div>\r\n        <div className=\"text-center text-[12px] md:text-[14px]\">Please use the provided payment details below to settle your Enterprise account.</div>\r\n        <div className=\"py-2\">\r\n          <div className=\"font-bold text-[11px]\">No. of Members: {moreToAddMember}</div>\r\n          <div className=\"font-bold text-[11px]\">Enterprise - Total: ${moreToAddMemberTotalAmount}</div>\r\n        </div>\r\n        <div className=\"border rounded p-2 text-[12px] md:text-[14px] leading-7 my-2\">\r\n          <div className=\"font-bold\">Bank Information</div>\r\n          <div className=\"float-right text-blue-400 font-bold cursor-pointer mt-[-28px] text-[12px]\" onClick={()=> sendViaEmail()}>Send via Email</div>\r\n          <div><span className=\"font-bold\">Beneficiary:</span> TELECOM BUSINESS SOLUTIONS INC.</div>\r\n          <div><span className=\"font-bold\">SWIFT:</span> BOFAUS3N</div>\r\n          <div><span className=\"font-bold\">Bank Name:</span> Bank of America</div>\r\n          <div><span className=\"font-bold\">Routing (Wire):</span> *********</div>\r\n          <div><span className=\"font-bold\">Routing Number (Paper & Electronic):</span> *********</div>\r\n          <div><span className=\"font-bold\">Account Number:</span> 3810-6766-2647</div>\r\n          <div><span className=\"font-bold\">Customer Number:</span> {user_id}</div>\r\n          <div className=\"bg-[#dddddd] px-4 py-2 rounded text-center mt-4\"><FaInfoCircle className=\"inline text-lg mr-2\"/>Customer Number must be included in the bank transfer description field for your funds to transfer successfully.</div>\r\n        </div>\r\n\r\n        <div className=\"text-center text-[12px] md:text-[14px] mt-4\">\r\n          Once the payment is received, our dedicated account manager will contact you to assist in the seamless setup of your Enterprise account.\r\n        </div>\r\n        <div className=\"text-center text-[12px] md:text-[14px]\">\r\n          Please allow <b>2-3 banking days</b> for the payment to reflect in the account.\r\n        </div>\r\n        <div className=\"text-center\">\r\n          <motion.button\r\n            className=\"bg-blue-500 text-white font-bold py-3 px-4 rounded my-4 proceed-pmt\"\r\n            whileHover={{ backgroundColor: \"#5997fd\" }}\r\n            whileTap={{ scale: 0.9 }}\r\n            onClick={submitPaymentInformation}\r\n          >\r\n            Send Payment Confirmation\r\n          </motion.button>\r\n        </div>\r\n      </div>\r\n    </div>    \r\n    </>\r\n  )\r\n}\r\n"], "names": ["getCurrency", "currency", "toLowerCase", "getCountry", "formatDate", "dateStr", "date", "Date", "replace", "getMonth", "getDate", "getFullYear", "getPrice", "data", "trial_price", "price", "numberWithCommas", "x", "toString", "formatNumber", "number", "floatNumber", "parseFloat", "toFixed", "getPricePlan", "isNaN", "toLocaleString", "diffMin", "dt1", "dt2", "diff", "getTime", "Math", "abs", "round", "formatDateTime", "getHours", "getMinutes", "DailyPrice", "_ref", "plan", "dailyPrice", "interval", "payment_interval", "_jsxs", "class", "children", "trial_days", "_Fragment", "className", "concat", "Get<PERSON><PERSON><PERSON>", "_jsx", "PriceFormatted", "_ref2", "displayTextFormatted", "_<PERSON><PERSON><PERSON><PERSON>", "_GetCookie2", "locales", "daily", "currency_symbol", "display_txt2", "pricing_description", "amount", "price_text", "PausedAccountModal", "showPausedAccountModal", "setShowPausedAccountModal", "userSubscriptionID", "userMerchant", "userAccountID", "billingCycle", "setbillingCycle", "useState", "buttonDisable", "setButtonDisable", "useEffect", "toastr", "positionClass", "modalPausedAccountClose", "undefined", "modalPausedAccountOpen", "modal", "document", "getElementById", "style", "display", "Transition", "appear", "show", "as", "Fragment", "Dialog", "onClose", "Child", "enter", "enterFrom", "enterTo", "leave", "leaveFrom", "leaveTo", "Panel", "Title", "type", "value", "onClick", "disabled", "handlePause", "querySelector", "classList", "add", "axios", "post", "process", "headers", "then", "res", "output", "success", "remove", "setTimeout", "window", "location", "reload", "msg", "currentPlan", "currentPlanName", "totalTokenUsage", "maxTokens", "resetDate", "tokenUsages", "entParentUserID", "subdomain", "setSubdomain", "tokenUsagesPerModel", "setTokenUsagesPerModel", "tokenUsagesPerApp", "setTokenUsagesPerApp", "isUnpaid", "isBasic", "isPro", "isProMax", "isEnterprise", "isAdvanced", "isClusterPlan", "isStaging", "href", "includes", "auth_version", "perModel", "perApp", "filtered", "Object", "fromEntries", "entries", "filter", "row", "model", "convertToLocaleString", "total", "parseInt", "hasMaxUsage", "isMaxedTokenUsage", "isMaxedUsage", "usage", "max_usage", "tokenUsageText", "text", "trim", "totalTokenUsageText", "keys", "length", "map", "index", "colspan", "_ref3", "app", "total_token", "ent_max_members", "async", "getPlan", "tk", "view_data", "dis_plan_id", "offer", "plan_id", "dis_price", "dis_currency", "dis_cancel", "cancel", "dis_percentage", "percentage", "OfferModal", "onAccept", "onDecline", "useQuery", "paymentInterval", "setPaymentInterval", "activeSub", "find", "sub", "status", "toUpperCase", "src", "aiproLogo", "alt", "TabItem", "tabName", "activeTab", "isActive", "Account<PERSON><PERSON><PERSON><PERSON><PERSON>", "props", "newEmail", "setNewEmail", "email", "auth", "oldPassword", "setOldPassword", "newPassword", "setNewPassword", "confPassword", "setConfPassword", "userPid", "user_pid", "isSocialLogin", "setisSocialLogin", "is_social", "handleCopy", "navigator", "clipboard", "writeText", "FaRegCopy", "placeholder", "name", "readOnly", "autocomplete", "onKeyUp", "event", "target", "onChange", "e", "UpdateAccount", "updateUserDetails", "error_msg", "validatePassword", "validateEmail", "test", "newemail", "password", "newpassword", "confpassword", "DeleteAccount", "isShowModal", "setIsShowModal", "closeModal", "proceedUpdateAccount", "motion", "button", "whileTap", "scale", "showUpdateAccountModal", "keyCode", "getEnterpriseMembers", "SubscriptionTabContent", "setshowAddMoreMember", "setUserSubscriptionID", "setUserMerchant", "setUserAccountID", "date_now", "user_subscription", "userPpg", "showOffer", "changeCard", "p_id", "getAttribute", "downgradeSub", "getMerchant", "merchant", "getPlanType", "plan_type", "getPaymentInterval", "some", "plan_name", "price_label", "start_date", "end_date", "<PERSON><PERSON>", "<PERSON><PERSON>", "Items", "<PERSON><PERSON>", "_ref4", "active", "pid", "_ref5", "_ref6", "CancelSubscription", "_ref7", "_ref8", "upgradeSub", "_ref9", "is_trial_end", "_ref0", "merchant_subscription_id", "pauseSub", "_ref1", "FaInfoCircle", "OrderTabContent", "_ref10", "user_order", "charge_id", "label", "created_at", "refund_at", "HelpTabContent", "LogOutContent", "RemoveCookie", "get", "showDeleteModal", "proceedDeleteAccount", "mixpanel", "people", "set_once", "identify", "track", "_ref11", "<PERSON><PERSON>", "showCancelModal", "surveydata", "limitDisplay", "members", "displayLimit", "slice", "getTotalPages", "totalMembers", "quotient", "floor", "MembersTabContent", "_ref12", "handleShowAddMember", "enterpriseMembers", "handleReloadMembers", "handleShowEditMemberModal", "search", "setSearch", "cloneEnterpriseMembers", "setcloneEnterpriseMembers", "displayEnterpriseMembers", "setDisplayEnterpriseMembers", "showOnly", "setShowOnly", "pageCount", "setPageCount", "currentPage", "setCurrentPage", "PageCount", "handleSearch", "input", "item", "full_name", "first_name", "last_name", "indexOf", "handlePagerClick", "page", "arrayToLoad", "id", "whileHover", "backgroundColor", "total_flux_prompt", "total_o1_prompt", "user_id", "handleEdit", "params", "ResendPasswordMember", "member_user_id", "DeleteMember", "generatePagerStats", "totalEntries", "ending", "starting", "generatePager", "content", "push", "i", "EditMemberModal", "_ref13", "showEditMember", "setShowEditEnterprise", "editMemberDetails", "fullName", "setfullName", "oldFull<PERSON>ame", "setoldFullName", "setEamil", "origEmail", "setorigEmail", "userId", "setUserId", "modalEditMembersClose", "fieldEmail", "title", "fieldName", "modalEditMembersOpen", "preventDefault", "fieldFullnameError", "innerHTML", "validateName", "fieldE<PERSON><PERSON><PERSON><PERSON>", "fieldFullname", "url", "member_email", "member_fullname", "member_old_fullname", "member_old_email", "Array", "isArray", "error", "catch", "updateMember", "AddMemberModal", "_ref14", "showAddMember", "setshowAddMember", "entMaxMembers", "isMax", "isVisbleBtn", "setIsVisibleBtn", "setMembers", "addMemberBtn", "setAddMemberBtn", "isRemoveBtn", "setRemoveBtn", "addMember", "current", "total_members_after_insert", "resetMember", "_", "removeMember", "newTotalMembers", "modalAddMembersClose", "validateFields", "<PERSON><PERSON><PERSON><PERSON>", "fieldError1", "fieldError2", "server_error", "doesExist", "modalAddMembersOpen", "mem", "s", "newArr", "<PERSON><PERSON><PERSON><PERSON>", "FaTrash", "removeLastMember", "lastIndex", "JSON", "stringify", "saveMember", "_ref15", "showDeleteMemberModal", "AiFillWarning", "_ref16", "setEnterpriseMembers", "setActiveTab", "isShowEnterprise", "setisShowEnterprise", "showEditEnterprise", "setEditMemberDetails", "showAddMoreMember", "showCompletePurchase", "setShowCompletePurchase", "moreToAddMember", "setMoreToAddMember", "moreToAddMemberTotalAmount", "setMoreToAddMemberTotalAmount", "setEntParentUserID", "setCurrentPlan", "setCurrentPlanName", "setUserPpg", "setIsMax", "subscription", "setSubscription", "getDateNow", "setGetDateNow", "getOrderNow", "setGetOrderNow", "setUsage", "showOfferModal", "setShowOfferModal", "offerDetails", "setOfferDetails", "setTotalTokenUsage", "setMaxTokens", "setResetDate", "setTokenUsages", "checkUsage", "getUserUsage", "threed_error", "<PERSON><PERSON><PERSON><PERSON>", "ent_parent_user_id", "user_ppg", "active_tab", "max_tokens", "max_end", "max_members", "entMembers", "fetchData", "fetchTokens", "tokens", "response", "getTokenUsage", "gpt_4o_total_token_usage", "gpt_4o_max_tokens", "dalle_total_image_generated", "dalle_max_images", "gpt_image_1_total_image_generated", "gpt_image_1_max_images", "claude_35_total_token_usage", "claude_35_max_tokens", "claude_37_total_token_usage", "claude_37_max_tokens", "deepseek_r1_total_token_usage", "deepseek_r1_max_tokens", "grok_v3_total_token_usage", "grok_v3_max_tokens", "grok_v4_total_token_usage", "grok_v4_max_tokens", "flux_total_prompts", "flux_max_prompts", "o1_total_prompts", "o1_max_prompts", "videogen_credit", "videogen_total_credit", "getOrders", "fetchOrder", "targetPlans", "checkEntMaxPlan", "user_plan", "Boolean", "handleTabChange", "tab", "install", "<PERSON><PERSON><PERSON>", "Header", "details", "remainingSlots", "Number", "TokenUsageContent", "AddMoreMemberModal", "MemberCompletePurchase", "handleCloseOfferModal", "handlePay", "handleDeclineOffer", "Footer", "price_per_member", "membersToAdd", "setMembersToAdd", "totalAmount", "setTotalAmount", "setpaymentInterval", "modalAddMoreMembersClose", "modalAddMoreMembersOpen", "modalClose", "modalOpen", "sendViaEmail", "total_amount", "submitPaymentInformation"], "sourceRoot": ""}