{"version": 3, "file": "static/js/6360.25ed9ae8.chunk.js", "mappings": "6HA0FA,MACA,EAAe,IAA0B,yD,sHCvBzC,QA9DA,SAAmBA,GAAkC,IAAjC,YAAEC,EAAW,KAAEC,GAAOC,EAAAA,EAAAA,OAAQH,EAChD,MAAMI,EAAWC,OAAOC,SAASF,SAC3BG,EAAc,yBAAyBC,KAAKJ,GAC5CK,EAAgB,sBAAsBD,KAAKJ,GAC3CM,EAAc,yBAAyBF,KAAKJ,GAC5CO,EAAiBP,EAASQ,SAAS,kBACnCC,GAAYZ,EACZa,EAAwBC,CAAAA,SAAAA,aAAAA,WAAAA,eAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,cAAAA,EAAAA,kBAAAA,4BAAAA,mBAAAA,yBAAAA,oCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,kCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,2BAAAA,iBAAAA,4BAAAA,cAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,oBAAAA,uBAAAA,yBAAAA,8BAAAA,yBAAAA,gFAAAA,uBAAAA,6CAAAA,sBAAAA,KAAAA,uBAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,wBAAAA,KAAAA,mBAAAA,aAAAA,qCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,mCAAAA,KAAAA,8BAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,4BAAAA,4BAAAA,gCAAAA,oBAAAA,gCAAAA,oBAAAA,gCAAAA,oBAAAA,gCAAAA,oBAAAA,kCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,gCAAAA,KAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,iCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,6BAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,yBAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,iCAAAA,8GAAAA,iCAAAA,8GAAAA,oBAAAA,8jBAAYC,iCAAmC,IAE7EC,EAAAA,EAAAA,WAAU,KACR,MAAMC,EAAUC,EAAkBC,EAAAA,EAAW,SACvCC,EAAWF,EAAkBG,EAAe,SAMlD,OALAC,SAASC,KAAKC,OAAOP,EAASG,GAEzBV,GACH,yDAEK,KACLO,EAAQQ,SACRL,EAASK,WAEV,CAACf,IAGJ,MAAMQ,EAAoBA,CAACQ,EAAMC,KAC/B,MAAMC,EAAON,SAASO,cAAc,QAIpC,OAHAD,EAAKE,IAAM,UACXF,EAAKF,KAAOA,EACZE,EAAKD,GAAKA,EACHC,GAGT,OACEG,EAAAA,EAAAA,MAAAC,EAAAA,SAAA,CAAAC,SAAA,CACGpB,IACCqB,EAAAA,EAAAA,KAAA,OAAKC,GAAG,wBAAwBC,UAAU,mCAAkCH,SAAC,sFAI/EC,EAAAA,EAAAA,KAAA,UAAQC,GAAG,SAASC,UAAW,+FAA8FvB,EAAwB,aAAe,IAAKoB,UACvKF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,2DAA0DH,SAAA,EACvEF,EAAAA,EAAAA,MAAA,WAASK,UAAU,YAAWH,SAAA,EAC5BC,EAAAA,EAAAA,KAAA,UAAQG,KAAK,aAAaC,OAAQjB,EAAekB,MAAM,MAAMC,OAAO,KAAKJ,UAAU,eACnFF,EAAAA,EAAAA,KAAA,OAAKO,IAAKtB,EAAAA,EAAWuB,IAAI,cAAcN,UAAU,kBAEjD9B,GAAeE,GAAiBC,IAAgBG,IAChDsB,EAAAA,EAAAA,KAAA,OAAKE,UAAU,uCAAuCD,GAAG,OAAMF,UAC7DC,EAAAA,EAAAA,KAAA,MAAIE,UAAU,2BAA0BH,UACtCC,EAAAA,EAAAA,KAAA,MAAIE,UAAU,uBAAsBH,UAClCC,EAAAA,EAAAA,KAAA,KAAGR,KAAMzB,EAAO,cAAgB,SAAUmC,UAAU,YAAY,aAAYnC,EAAO,aAAe,QAAQgC,SACvGhC,EAAO,UAAY,wBAUxC,C,oLCwBA,MACA,EAAe,IAA0B,0D,qCC1EzC,MAAM0C,GAAUC,EAAAA,EAAAA,IAAU,YAAaA,EAAAA,EAAAA,IAAU,WAAa,GACxDC,GAAKD,EAAAA,EAAAA,IAAU,WAAYA,EAAAA,EAAAA,IAAU,UAAY,GACjDE,GAAMF,EAAAA,EAAAA,IAAU,QAASA,EAAAA,EAAAA,IAAU,OAAS,GAClD,IAAIG,EAAO,KAEXC,eAAeC,IACb,GAAGF,EAAM,OAAOA,EAChB,MACMG,SADiBC,EAAAA,EAAMC,KAAK,qCAA6C,CAAEC,QAASV,GAAW,CAAEW,QAAS,CAAE,eAAgB,wCAC1GC,KACxB,OAAGL,EAAOM,SACRT,EAAOG,EAAOK,KACPL,EAAOK,MAEP,EAEX,CA8OA,QA5OA,YACEvC,EAAAA,EAAAA,WAAU,KACRyC,IAAAA,QAAiB,CACfC,cAAe,qBAEhB,IAEH,MAAM,KAAEH,IAASI,EAAAA,EAAAA,UAAS,QAASV,IAC5BW,EAAcC,IAAmBC,EAAAA,EAAAA,WAAS,GAE3C7D,GAAOC,EAAAA,EAAAA,IAAK,kBAClB,QAAY6D,IAAT9D,IAA+B,IAATA,EAAgB,OAEzC,GAAG2D,GACmB,WAAhB3D,EAAK+D,QAAwC,OAAjB/D,EAAKgE,QAEnC,YADA7D,OAAOC,SAASqB,KAAO,eAK3B,IAAIwC,EAAO,IAAIC,KACfD,EAAKE,QAAQF,EAAKG,UAAY,QAC9B,IAAIC,EAAQ,IAAIH,KACZI,EAAc,IAAIJ,KAGtB,GAFAI,EAAYC,QAAQF,EAAMG,UAAY,IAElClB,GAAQA,EAAKmB,UAAYnB,EAAKoB,MAAM,CACtC,IAAIC,EAASrB,EAAKoB,MACO,KAArBpB,EAAKsB,cACPD,EAASrB,EAAKsB,cAEhBC,EAAAA,EAAAA,IAAU,WAAYvB,EAAKmB,SAAU,CAAEK,QAASR,EAAaS,KAAM,OACnEF,EAAAA,EAAAA,IAAU,WAAYvB,EAAKmB,SAAU,CAAEK,QAASR,EAAaU,OAAQ,cAAeD,KAAM,OAC1FF,EAAAA,EAAAA,IAAU,SAAUF,EAAQ,CAAEG,QAASR,EAAaS,KAAM,OAC1DF,EAAAA,EAAAA,IAAU,SAAUF,EAAQ,CAAEG,QAASR,EAAaU,OAAQ,cAAeD,KAAM,KACnF,CAoFA,OA/BA5E,OAAO8E,mBAnDqB3B,IAC1BnD,OAAO+E,WAAWC,QAAQC,QAC1BjF,OAAO+E,WAAWC,QAAQE,IAAIvC,EAAKwC,YACnCnF,OAAO+E,WAAWC,QAAQI,IAAI,CAAC,MAAQvF,EAAKwF,MAAM,SAAWxF,EAAKyF,SAAU,IAAM5C,IAClF1C,OAAO+E,WAAWC,QAAQO,UACxB,CACE,MAAQ1F,EAAKwF,QAGjBG,QAAQC,IAAI,OAAO9C,IA2CrB3C,OAAO0F,yBAvC2BC,IAChClC,GAAgB,GAEhBvC,SAAS0E,cAAc,qBAAqBC,UAAUX,IAAI,UAC1DhE,SAAS4E,eAAe,cAAcC,MAAMC,WAAc,SAG1DjD,EAAAA,EAAMC,KADI,qDACM,CACdP,KACAV,GAAI4D,EAAU5D,GACdkE,QAASN,EAAUM,QACnBC,UAAWP,EAAUO,UACrBC,OAAQR,EAAUS,MAAM,GAAGC,aAC3BpD,QAASN,EAAKM,SACb,CAAEC,QAAS,CAAE,eAAgB,uCAAyCoD,KAAK,SAASC,GACrF,IAAIzD,EAASyD,EAAIpD,KACdL,EAAOM,SACRC,IAAAA,QAAe,WACfrD,OAAOC,SAASqB,KAAO,mBAAmBqB,EAAK6D,MAAMC,QAAQ,IAAI,IAAIA,QAAQ,IAAI,MAE9E3D,EAAOK,OACLL,EAAOK,KAAKuD,QACbrD,IAAAA,MAAaP,EAAOK,KAAKuD,SAEzBrD,IAAAA,MAAaP,EAAOK,KAAKwD,MAG7BzF,SAAS4E,eAAe,cAAcC,MAAMC,WAAc,UAE9D,GAAGY,MAAM,SAAUC,GACbA,EAAMC,UAAoC,MAAxBD,EAAMC,SAASlD,SACnC1C,SAAS0E,cAAc,qBAAqBC,UAAUxE,OAAO,UAC7DgC,IAAAA,MAAa,yDAEfnC,SAAS4E,eAAe,cAAcC,MAAMC,WAAc,SAC5D,IAKFhG,OAAO+G,aAAeA,EAAAA,GAEPC,MACb,IAAIC,EAAa,GAGfA,EADa,SAAZpH,EAAKqH,KACO,wDAEA,mDAGf,MAAMC,EAAW,UACjB,IAAIC,EAASlG,SAAS4E,eAAeqB,GAChCC,IACHA,EAASlG,SAASO,cAAc,UAChC2F,EAAOnF,KAAO,kBACdmF,EAAOrF,GAAKoF,EACZC,EAAOC,aAAa,kBAAmB,QACvCD,EAAO/E,IAAM,mEACb+E,EAAOE,QAAQC,WAAYN,EAC3BG,EAAOC,aAAa,qBAAsB,sBAC1CD,EAAOC,aAAa,8BAA8B,4BAClDnG,SAASsG,KAAKC,YAAYL,KAK9BJ,IAGErF,EAAAA,EAAAA,MAAAC,EAAAA,SAAA,CAAAC,SAAA,EACEF,EAAAA,EAAAA,MAAC+F,EAAAA,EAAM,CAAA7F,SAAA,EACLC,EAAAA,EAAAA,KAAA,SAAAD,SAAO,6BACPC,EAAAA,EAAAA,KAAA,QAAM6F,KAAK,cAAcC,QAAQ,gGAEnC9F,EAAAA,EAAAA,KAAC+F,EAAAA,QAAM,CAAChI,KAAMA,IACZsD,GACFrB,EAAAA,EAAAA,KAAA,OAAKE,UAAU,wDAAuDH,UAElEC,EAAAA,EAAAA,KAAA,OAAKE,UAAU,6BAA4BH,UACzCC,EAAAA,EAAAA,KAAA,OAAKE,UAAU,4CAA2CH,UACxDF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,oDAAmDH,SAAA,EAChEF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,2CAA0CH,SAAA,EACvDC,EAAAA,EAAAA,KAAA,OAAKE,UAAU,2EAA0EH,UACvFC,EAAAA,EAAAA,KAAA,OAAKE,UAAU,aAAYH,UACzBF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,GAAEH,SAAA,EACjBF,EAAAA,EAAAA,MAAA,OAAKmG,MAAM,6BAA4BjG,SAAA,EAACC,EAAAA,EAAAA,KAAA,OAAKiG,QAAQ,MAAMC,MAAM,6BAA6B7F,MAAM,OAAOC,OAAO,OAAO6F,QAAQ,YAAY,oBAAkB,gBAAgBjG,UAAU,iBAAgBH,UAACF,EAAAA,EAAAA,MAAA,KAAAE,SAAA,EACxMC,EAAAA,EAAAA,KAAA,QAAMoG,KAAK,UAAUC,EAAE,iJACvBrG,EAAAA,EAAAA,KAAA,QAAMoG,KAAK,UAAUC,EAAE,sEAEjBrG,EAAAA,EAAAA,KAAA,MAAIgG,MAAM,+BAA8BjG,SAAC,yBAEjDC,EAAAA,EAAAA,KAAA,OAAKC,GAAG,aAAa+F,MAAM,aAAYjG,UACrCC,EAAAA,EAAAA,KAAA,OAAKC,GAAG,8CAKdJ,EAAAA,EAAAA,MAAA,OAAKK,UAAU,iFAA+EH,SAAA,EAACC,EAAAA,EAAAA,KAACsG,EAAAA,IAAY,CAACpG,UAAU,wBAAuB,4FAGhJF,EAAAA,EAAAA,KAAA,OAAKE,UAAU,iEAAgEH,UAC7EC,EAAAA,EAAAA,KAAA,OAAKE,UAAU,sIAAqIH,UAClJF,EAAAA,EAAAA,MAAA,OAAKmG,MAAM,qBAAoBjG,SAAA,EAC7BC,EAAAA,EAAAA,KAAA,OAAKO,IAAKtB,EAAWuB,IAAI,cAAcN,UAAU,4CACjDF,EAAAA,EAAAA,KAAA,MAAIE,UAAU,iDAAgDH,SAAC,mBAE/DF,EAAAA,EAAAA,MAAA,OAAKmG,MAAM,kCAAiCjG,SAAA,EAC1CF,EAAAA,EAAAA,MAAA,OAAKmG,MAAM,2FAA0FjG,SAAA,EACnGC,EAAAA,EAAAA,KAAA,OAAKgG,MAAM,wBAAuBjG,SAAC,sBAEhCsB,EAAKkF,WAAa,GACnB1G,EAAAA,EAAAA,MAAA,OAAKK,UAAU,6BAA4BH,SAAA,CAAGsB,EAAKmF,kBAAkBC,cAAc9B,QAAQ,QAAS+B,GAAKA,EAAEC,eAAehC,QAAQ,SAAU,IAAI,aAChJ9E,EAAAA,EAAAA,MAAA,OAAKK,UAAU,6BAA4BH,SAAA,CAAGsB,EAAKmF,kBAAkBC,cAAc9B,QAAQ,QAAS+B,GAAKA,EAAEC,eAAehC,QAAQ,SAAU,IAAI,YAElJ3E,EAAAA,EAAAA,KAAA,OAAKE,UAAU,6BAA4BH,SAAGsB,EAAKuF,sBAElDvF,EAAKkF,WAAa,GAA+B,YAA1BlF,EAAKuF,kBAC/B5G,EAAAA,EAAAA,KAAA,OAAKE,UAAU,yCAAuCH,SAAC,+HACtDsB,EAAKkF,WAAa,GAA+B,WAA1BlF,EAAKuF,kBAC7B5G,EAAAA,EAAAA,KAAA,OAAKE,UAAU,yCAAuCH,SAAC,8HAC5B,WAA1BsB,EAAKuF,kBACN5G,EAAAA,EAAAA,KAAA,OAAKE,UAAU,yCAAuCH,SAAC,gEACvDC,EAAAA,EAAAA,KAAA,OAAKE,UAAU,yCAAuCH,SAAC,kEAIzDC,EAAAA,EAAAA,KAAA,OAAKE,UAAU,uGAAsGH,UACnHC,EAAAA,EAAAA,KAAA,OAAKgG,MAAM,+CAA8CjG,UACvDF,EAAAA,EAAAA,MAAA,OAAKmG,MAAM,wBAAuBjG,SAAA,EAChCF,EAAAA,EAAAA,MAAA,OAAKmG,MAAM,0BAAyBjG,SAAA,EAClCC,EAAAA,EAAAA,KAAA,OAAK6G,OAAO,eAAeT,KAAK,eAAe,eAAa,IAAID,QAAQ,cAAcH,MAAM,8CAA8C1F,OAAO,MAAMD,MAAM,MAAM6F,MAAM,6BAA4BnG,UACrMC,EAAAA,EAAAA,KAAA,QAAMqG,EAAE,2NACF,wBAGRxG,EAAAA,EAAAA,MAAA,OAAKmG,MAAM,0BAAyBjG,SAAA,EAClCC,EAAAA,EAAAA,KAAA,OAAKO,IAAKuG,EAAOtG,IAAI,cAAcwF,MAAM,iCACzCnG,EAAAA,EAAAA,MAAA,OAAKmG,MAAM,sBAAqBjG,SAAA,EAC9BC,EAAAA,EAAAA,KAAA,OAAKgG,MAAM,QAAOjG,UAChBC,EAAAA,EAAAA,KAAA,OAAKO,IAAKwG,EAAcvG,IAAI,qBAAqBwF,MAAM,8BAEzDhG,EAAAA,EAAAA,KAAA,OAAKgG,MAAM,QAAOjG,UAClBC,EAAAA,EAAAA,KAAA,UACRgH,QAASA,KACP9I,OAAO+I,KACL,4FACA,eACA,6HAGJC,MAAM,0CACNhH,UAAU,eAAcH,UACtBC,EAAAA,EAAAA,KAAA,OAAKmH,QAAQ,OACX5G,IAAI,wGACJC,IAAI,+CACJN,UAAU,gDAyBvB,KAGf,C,oCC5QO,I,WCCIkH,EAAiB,CAC1BC,WAAOxF,EACPyF,UAAMzF,EACN3B,eAAW2B,EACXoC,WAAOpC,EACP0F,UAAM1F,GAEG2F,EAAcC,EAAAA,eAAuBA,EAAAA,cAAoBL,GCRhEM,EAAoC,WAQtC,OAPAA,EAAWC,OAAOC,QAAU,SAAUC,GACpC,IAAK,IAAInB,EAAGoB,EAAI,EAAGC,EAAIC,UAAUC,OAAQH,EAAIC,EAAGD,IAE9C,IAAK,IAAII,KADTxB,EAAIsB,UAAUF,GACOH,OAAOQ,UAAUC,eAAeC,KAAK3B,EAAGwB,KAAIL,EAAEK,GAAKxB,EAAEwB,IAE5E,OAAOL,CACT,EACOH,EAASY,MAAMC,KAAMP,UAC9B,EACIQ,EAAgC,SAAU9B,EAAG+B,GAC/C,IAAIZ,EAAI,CAAC,EACT,IAAK,IAAIK,KAAKxB,EAAOiB,OAAOQ,UAAUC,eAAeC,KAAK3B,EAAGwB,IAAMO,EAAEC,QAAQR,GAAK,IAAGL,EAAEK,GAAKxB,EAAEwB,IAC9F,GAAS,MAALxB,GAAqD,mBAAjCiB,OAAOgB,sBAA2C,KAAIb,EAAI,EAAb,IAAgBI,EAAIP,OAAOgB,sBAAsBjC,GAAIoB,EAAII,EAAED,OAAQH,IAClIW,EAAEC,QAAQR,EAAEJ,IAAM,GAAKH,OAAOQ,UAAUS,qBAAqBP,KAAK3B,EAAGwB,EAAEJ,MAAKD,EAAEK,EAAEJ,IAAMpB,EAAEwB,EAAEJ,IADuB,CAGvH,OAAOD,CACT,EAGA,SAASgB,EAAaC,GACpB,OAAOA,GAAQA,EAAKC,IAAI,SAAUC,EAAMlB,GACtC,OAAOL,EAAAA,cAAoBuB,EAAK1F,IAAKoE,EAAS,CAC5CuB,IAAKnB,GACJkB,EAAKzB,MAAOsB,EAAaG,EAAKE,OACnC,EACF,CACO,SAASC,EAAQ9H,GAEtB,OAAO,SAAU+H,GACf,OAAO3B,EAAAA,cAAoB4B,EAAU3B,EAAS,CAC5CH,KAAMG,EAAS,CAAC,EAAGrG,EAAKkG,OACvB6B,GAAQP,EAAaxH,EAAK6H,OAC/B,CACF,CACO,SAASG,EAASD,GACvB,IAAIE,EAAO,SAAUC,GACnB,IAKIrJ,EALAqH,EAAO6B,EAAM7B,KACfD,EAAO8B,EAAM9B,KACbJ,EAAQkC,EAAMlC,MACdsC,EAAWhB,EAAOY,EAAO,CAAC,OAAQ,OAAQ,UACxCK,EAAenC,GAAQiC,EAAKjC,MAAQ,MAIxC,OAFIiC,EAAKrJ,YAAWA,EAAYqJ,EAAKrJ,WACjCkJ,EAAMlJ,YAAWA,GAAaA,EAAYA,EAAY,IAAM,IAAMkJ,EAAMlJ,WACrEuH,EAAAA,cAAoB,MAAOC,EAAS,CACzCb,OAAQ,eACRT,KAAM,eACNsD,YAAa,KACZH,EAAKhC,KAAMA,EAAMiC,EAAU,CAC5BtJ,UAAWA,EACX+D,MAAOyD,EAASA,EAAS,CACvBL,MAAO+B,EAAM/B,OAASkC,EAAKlC,OAC1BkC,EAAKtF,OAAQmF,EAAMnF,OACtB3D,OAAQmJ,EACRpJ,MAAOoJ,EACPvD,MAAO,+BACLgB,GAASO,EAAAA,cAAoB,QAAS,KAAMP,GAAQkC,EAAMrJ,SAChE,EACA,YAAuB8B,IAAhB2F,EAA4BC,EAAAA,cAAoBD,EAAYmC,SAAU,KAAM,SAAUJ,GAC3F,OAAOD,EAAKC,EACd,GAAKD,EAAKlC,EACZ,C", "sources": ["assets/images/AIPRO.svg", "header/headerlogo.jsx", "assets/images/AIPRO.svg?webp", "pay/index_06.jsx", "../node_modules/react-icons/lib/esm/iconsManifest.js", "../node_modules/react-icons/lib/esm/iconContext.js", "../node_modules/react-icons/lib/esm/iconBase.js"], "sourcesContent": ["var _g, _defs;\nfunction _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\nimport * as React from \"react\";\nfunction SvgAipro(_ref, svgRef) {\n  let {\n    title,\n    titleId,\n    ...props\n  } = _ref;\n  return /*#__PURE__*/React.createElement(\"svg\", _extends({\n    width: 451,\n    height: 157,\n    viewBox: \"0 0 451 157\",\n    fill: \"none\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, _g || (_g = /*#__PURE__*/React.createElement(\"g\", {\n    clipPath: \"url(#clip0_2054_286)\"\n  }, /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M92.717 79.8955C91.7079 79.898 90.8221 79.5666 90.0844 78.8815C89.438 78.2806 88.7889 77.6721 88.2042 77.0144C87.6096 76.3467 86.6769 76.3763 86.107 77.0193C85.6035 77.5881 85.0483 78.1124 84.5007 78.6392C83.2546 79.8361 81.7939 80.17 80.1927 79.5345C78.5839 78.8964 77.7647 77.6277 77.6759 75.9088C77.5969 74.3657 78.5568 72.8595 79.9853 72.1993C81.4707 71.5118 83.1683 71.7739 84.3896 72.909C85.0064 73.4827 85.6159 74.0689 86.1736 74.6996C86.7732 75.3747 87.6441 75.4266 88.3054 74.697C88.8433 74.1034 89.4207 73.5446 90.0029 72.9931C91.1724 71.8876 92.9095 71.5982 94.3554 72.2437C95.8358 72.9065 96.7735 74.4077 96.7265 76.0424C96.6649 78.1989 94.8908 79.9029 92.717 79.898V79.8955Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M67.7897 52.4641C66.3315 52.4716 65.4408 52.1476 64.703 51.4625C64.0418 50.8467 63.4125 50.1963 62.7685 49.5607C61.9938 48.7965 61.3917 48.799 60.6096 49.5805C60.0273 50.1592 59.4575 50.7528 58.8652 51.324C57.6735 52.479 55.9735 52.7807 54.4807 52.1204C52.9607 51.4453 52.0034 49.9465 52.1022 48.2797C52.2008 46.6375 53.0151 45.4208 54.5324 44.7876C56.0894 44.1372 57.555 44.4093 58.8159 45.5469C59.4253 46.0959 60.0125 46.6771 60.5579 47.2904C61.2882 48.114 62.1296 48.1486 62.9068 47.2706C63.4373 46.6722 64.0196 46.1182 64.6019 45.5667C65.7887 44.4414 67.5455 44.1595 69.0036 44.8371C70.4841 45.5246 71.3971 47.0258 71.3305 48.6605C71.2441 50.7874 69.4824 52.4667 67.7873 52.4667L67.7897 52.4641Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M68.2449 83.3704C68.3484 82.7224 68.504 81.7086 68.6792 80.697C68.8543 79.688 68.5286 79.1711 67.5268 78.9733C66.725 78.8125 65.9107 78.7061 65.1113 78.533C63.2213 78.125 61.8889 76.426 61.9703 74.5142C62.0665 72.2217 63.8307 70.9234 65.3531 70.686C68.0179 70.2704 70.3421 72.6297 69.8979 75.2563C69.7722 76.0055 69.6439 76.7548 69.5205 77.5042C69.4884 77.7069 69.4564 77.9123 69.4515 78.1176C69.4317 78.7557 69.7771 79.2354 70.4063 79.3689C71.1268 79.5223 71.857 79.6335 72.5849 79.7448C74.3713 80.0168 75.6544 81.1248 76.0813 82.7794C76.6538 85.0002 75.1831 87.2457 72.9379 87.6885C70.616 88.146 68.1757 86.2887 68.2399 83.3728L68.2449 83.3704Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M89.2851 68.3141C87.6986 68.3438 85.8727 67.2137 85.4114 65.188C84.9944 63.3655 85.8802 61.518 87.558 60.7093C88.3525 60.326 89.1692 59.9946 89.9711 59.6286C90.8741 59.2181 91.0986 58.6493 90.7163 57.7392C90.4128 57.0194 90.0845 56.3122 89.7835 55.59C88.7645 53.1467 90.0994 50.5919 92.6827 50.033C95.1897 49.4889 97.6644 51.7048 97.4003 54.267C97.24 55.8274 96.4528 56.9577 95.0316 57.623C94.289 57.9716 93.5316 58.2858 92.7839 58.6196C91.8512 59.0375 91.6218 59.6088 92.0214 60.5487C92.3275 61.2658 92.6532 61.9781 92.9517 62.6977C93.9337 65.057 92.6654 67.587 90.1931 68.193C89.8971 68.2647 89.5862 68.277 89.2827 68.3141H89.2851Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M80.3604 40.3706C80.2444 41.0903 80.0816 42.1265 79.9089 43.1603C79.7558 44.0629 80.1382 44.6194 81.0462 44.7851C81.9321 44.9458 82.8252 45.0843 83.7085 45.2574C85.5937 45.6309 86.9063 47.2532 86.9014 49.1699C86.8964 51.1014 85.5715 52.6668 83.7085 53.0946C81.4238 53.6189 78.458 51.7147 79.0032 48.3587C79.1315 47.5673 79.2722 46.7784 79.4079 45.987C79.5584 45.1189 79.181 44.5699 78.2952 44.4018C77.4093 44.2361 76.5186 44.0951 75.6329 43.9269C73.7748 43.5757 72.4475 41.993 72.4105 40.0986C72.3734 38.1646 73.6565 36.5447 75.5119 36.102C77.9028 35.5332 80.422 37.3955 80.3604 40.3681V40.3706Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M58.2714 57.0669C60.1491 57.0916 61.7134 58.3331 62.1574 60.1755C62.5794 61.9363 61.7208 63.8159 60.0749 64.6395C59.2682 65.0426 58.4417 65.4135 57.6002 65.7351C56.781 66.0491 56.3443 66.7812 56.8279 67.7803C57.1857 68.5198 57.5114 69.2764 57.8199 70.0382C58.755 72.3357 57.4621 74.8657 55.0663 75.4494C53.191 75.9069 51.2838 74.967 50.4917 73.1939C49.7145 71.4578 50.2722 69.3951 51.829 68.3071C52.13 68.0969 52.4657 67.9336 52.8011 67.7803C53.5093 67.4563 54.2323 67.1645 54.9404 66.838C55.7571 66.4621 55.989 65.8513 55.6362 65.0179C55.2908 64.2041 54.9034 63.4079 54.5777 62.5867C53.6673 60.2991 54.9231 57.8162 57.2991 57.1979C57.6126 57.1163 57.9482 57.1088 58.2714 57.0669Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M69.1035 62.9101C69.111 59.992 71.492 57.6202 74.4036 57.6276C77.3026 57.6351 79.6812 60.034 79.6812 62.9473C79.6812 65.8804 77.2731 68.2719 74.3344 68.257C71.4378 68.2421 69.0937 65.8458 69.1035 62.9101Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M83.8945 39.572C83.8994 37.7988 85.3526 36.3594 87.1168 36.3792C88.8465 36.399 90.2702 37.8408 90.2801 39.5844C90.2899 41.3427 88.8341 42.8068 87.0749 42.8068C85.3107 42.8068 83.8895 41.36 83.8945 39.5745V39.572Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M58.7456 39.5941C58.7456 37.8233 60.1077 36.3865 61.7929 36.3791C63.4929 36.3716 64.9043 37.8505 64.887 39.6238C64.8697 41.3747 63.4756 42.8116 61.7953 42.8042C60.1053 42.7967 58.7482 41.3673 58.7456 39.5941Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M45.4366 62.2305C45.4292 60.5191 46.8256 59.1342 48.58 59.1143C50.3466 59.0945 51.8271 60.5068 51.8222 62.2106C51.8173 63.8973 50.3788 65.2871 48.6317 65.2947C46.8751 65.302 45.4439 63.9294 45.4366 62.2329V62.2305Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M98.659 59.1122C100.401 59.1071 101.852 60.4946 101.869 62.1787C101.886 63.8827 100.421 65.2973 98.6441 65.2899C96.8899 65.2825 95.4785 63.9025 95.481 62.1961C95.4834 60.4946 96.9022 59.1171 98.659 59.1122Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M87.3443 80.8603C89.0837 80.8678 90.5297 82.2699 90.5297 83.9517C90.5297 85.6606 89.059 87.0579 87.2826 87.0381C85.5307 87.0208 84.1341 85.631 84.1441 83.9196C84.1539 82.2229 85.5899 80.8529 87.3468 80.8603H87.3443Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M58.7471 83.9715C58.7422 82.2502 60.1214 80.8529 61.819 80.8603C63.482 80.8678 64.8712 82.2576 64.8859 83.9318C64.9007 85.6234 63.4893 87.0455 61.7968 87.0406C60.1165 87.0357 58.752 85.6631 58.7446 83.9739L58.7471 83.9715Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M17.2184 120.924L17.2141 116.161H67.6866C68.6013 114.482 69.2906 111.828 70.1946 110.17C70.186 110.154 70.169 110.074 70.1245 110.023C68.151 107.75 65.9356 105.851 63.2682 104.656C61.2906 103.772 59.2301 103.403 57.1039 103.581C55.3319 103.728 53.63 104.228 51.9983 105C46.1564 107.764 40.1531 109.997 33.978 111.664C31.2385 112.406 28.482 113.063 25.6873 113.491C23.994 113.75 22.2942 113.972 20.5903 114.141C19.5038 114.248 18.3472 114.336 17.214 114.373C17.1949 114.221 17.2184 113.963 17.2184 113.818C17.2141 111.968 17.2099 110.119 17.2226 108.269C17.2226 108.049 17.2969 107.813 17.3903 107.612C22.5595 96.3247 27.733 85.0422 32.9064 73.7573C33.3351 72.8219 33.7574 71.8795 34.2115 70.8764C34.6974 71.0845 35.1961 71.2974 35.7415 71.5311C31.3425 83.7116 26.9606 95.8407 22.5404 108.073C22.986 108.024 23.3574 107.996 23.7266 107.937C32.7792 106.473 41.4667 103.606 49.872 99.6569C51.8306 98.7357 53.8592 98.0996 55.9749 97.833C59.3828 97.4028 62.655 97.9967 65.7957 99.5026C68.5989 100.85 71.0242 102.802 73.1993 105.154C73.3373 105.304 73.4434 105.489 73.5897 105.692C74.0269 105.239 74.3941 104.853 74.7675 104.474C77.1378 102.054 79.7882 100.129 82.8461 98.9275C85.1823 98.0083 87.595 97.5876 90.0715 97.7209C92.5966 97.8565 95.0221 98.5111 97.3436 99.6102C103.139 102.355 109.098 104.554 115.23 106.169C117.904 106.876 120.599 107.481 123.328 107.876C123.78 107.942 124.23 108.017 124.786 108.103C120.367 95.8711 115.981 83.7303 111.589 71.5686C111.71 71.4961 111.78 71.4399 111.858 71.4072C112.263 71.2272 112.673 71.0541 113.114 70.8647C113.246 71.1757 113.359 71.4563 113.484 71.73C117.185 79.802 120.887 87.874 124.588 95.9436C126.356 99.7973 128.119 103.653 129.891 107.504C130.029 107.806 130.101 108.106 130.099 108.449C130.086 110.163 130.093 111.875 130.093 113.589V114.26C129.885 114.26 129.73 114.265 129.577 114.26C129.153 114.241 128.73 114.204 128.306 114.199C126.326 114.176 124.363 113.935 122.405 113.626C116.578 112.707 110.895 111.129 105.299 109.141C101.925 107.942 98.6148 106.558 95.3553 105.019C93.5409 104.163 91.6545 103.628 89.6725 103.55C86.933 103.443 84.4035 104.236 82.0418 105.73C80.2338 106.873 78.655 108.33 77.2184 109.986C77.1717 110.039 77.1336 110.107 77.1081 110.144C78.0078 111.795 78.7033 114.468 79.6264 116.161H130.093L130.093 120.906H76.3271C75.9431 119.963 75.5569 118.967 75.1325 117.99C74.7059 117.012 74.237 116.058 73.6577 115.104C72.5351 116.961 71.7564 118.934 70.9966 120.924L17.2184 120.92V120.924Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M197.092 84.997L190.692 67.0263C190.51 66.4944 190.275 65.5826 189.989 64.2908C189.703 62.9991 189.404 61.4161 189.092 59.5417C188.753 61.34 188.427 62.961 188.117 64.4047C187.804 65.8231 187.57 66.7729 187.414 67.2542L181.248 84.997H197.092ZM160.72 106.159L182.457 50.5374H196.428L218.477 106.159H204.584L199.94 94.3432H177.814L173.833 106.159H160.72Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M224.722 106.159V50.5374H236.937V106.159H224.722Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M249.347 92.4056V82.8313H271.201V92.4056H249.347Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M295.63 75.6507H297.386C300.716 75.6507 303.135 74.9795 304.645 73.6371C306.153 72.2946 306.908 70.1417 306.908 67.1782C306.908 64.4428 306.153 62.4545 304.645 61.2133C303.135 59.947 300.716 59.3137 297.386 59.3137H295.63V75.6507ZM283.338 106.159V50.5374H297.386C304.879 50.5374 310.407 51.9304 313.971 54.7165C317.562 57.5028 319.358 61.8086 319.358 67.6342C319.358 73.0291 317.55 77.2591 313.933 80.3238C310.343 83.3886 305.36 84.9209 298.986 84.9209H295.63V106.159H283.338Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M369.154 106.159H355.066L340.041 80.1719V106.159H327.825V50.5374H345.309C352.256 50.5374 357.459 51.8544 360.919 54.4886C364.38 57.0975 366.11 61.0361 366.11 66.3044C366.11 70.1291 364.926 73.3965 362.559 76.1066C360.19 78.8168 357.134 80.3998 353.387 80.8557L369.154 106.159ZM340.041 74.245H341.875C346.819 74.245 350.083 73.7257 351.67 72.6873C353.257 71.6235 354.05 69.7871 354.05 67.1782C354.05 64.4428 353.192 62.5052 351.475 61.3654C349.785 60.2002 346.585 59.6176 341.875 59.6176H340.041V74.245Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M434.793 78.4622C434.793 82.4388 434.026 86.2 432.49 89.746C430.982 93.2921 428.797 96.4075 425.936 99.0923C422.968 101.853 419.626 103.968 415.905 105.437C412.184 106.906 408.335 107.641 404.354 107.641C400.868 107.641 397.447 107.071 394.091 105.931C390.761 104.766 387.703 103.107 384.92 100.954C381.329 98.1678 378.571 94.837 376.647 90.9618C374.747 87.0866 373.797 82.92 373.797 78.4622C373.797 74.4603 374.552 70.7116 376.06 67.2163C377.57 63.6955 379.781 60.5674 382.695 57.832C385.556 55.1218 388.874 53.0195 392.647 51.5251C396.444 50.0308 400.347 49.2836 404.354 49.2836C408.335 49.2836 412.198 50.0308 415.945 51.5251C419.716 53.0195 423.046 55.1218 425.936 57.832C428.823 60.5674 431.02 63.6955 432.53 67.2163C434.038 70.7369 434.793 74.4856 434.793 78.4622ZM404.354 97.0406C409.531 97.0406 413.798 95.2804 417.155 91.7596C420.537 88.2137 422.227 83.7811 422.227 78.4622C422.227 73.1938 420.523 68.7612 417.115 65.1646C413.706 61.5679 409.453 59.7696 404.354 59.7696C399.176 59.7696 394.884 61.5679 391.476 65.1646C388.068 68.7359 386.363 73.1684 386.363 78.4622C386.363 83.8318 388.042 88.2769 391.398 91.7977C394.754 95.293 399.073 97.0406 404.354 97.0406Z\",\n    fill: \"black\"\n  }))), _defs || (_defs = /*#__PURE__*/React.createElement(\"defs\", null, /*#__PURE__*/React.createElement(\"clipPath\", {\n    id: \"clip0_2054_286\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    width: 418,\n    height: 86,\n    fill: \"white\",\n    transform: \"translate(17 36)\"\n  })))));\n}\nconst ForwardRef = /*#__PURE__*/React.forwardRef(SvgAipro);\nexport default __webpack_public_path__ + \"static/media/AIPRO.0580715cba3cbc2179c46b033d087db9.svg\";\nexport { ForwardRef as ReactComponent };", "import React, { useEffect } from 'react';\r\nimport aiproLogo from '../assets/images/AIPRO.svg';\r\nimport aiproLogoWebP from '../assets/images/AIPRO.webp';\r\nimport { Auth } from '../core/utils/auth';\r\nimport './style.css';\r\n\r\nfunction HeaderLogo({ hideNavLink, auth = Auth() }) {\r\n  const pathname = window.location.pathname;\r\n  const isChatGptGo = /\\/start-chatgpt-go\\/?$/.test(pathname);\r\n  const isTexttoImage = /\\/text-to-image\\/?$/.test(pathname);\r\n  const isChatGptV2 = /\\/start-chatgpt-v2\\/?$/.test(pathname);\r\n  const isRegisterPage = pathname.includes('/register-auth');\r\n  const showLink = !hideNavLink;\r\n  const showMaintenanceBanner = process.env.REACT_APP_ShowMaintenanceBanner || '';\r\n\r\n  useEffect(() => {\r\n    const linkPNG = createPreloadLink(aiproLogo, 'image');\r\n    const linkWebP = createPreloadLink(aiproLogoWebP, 'image');\r\n    document.head.append(linkPNG, linkWebP);\r\n\r\n    if (!isRegisterPage) {\r\n      import('@fortawesome/fontawesome-free/css/all.css');\r\n    }\r\n    return () => {\r\n      linkPNG.remove();\r\n      linkWebP.remove();\r\n    };\r\n  }, [isRegisterPage]);\r\n\r\n\r\n  const createPreloadLink = (href, as) => {\r\n    const link = document.createElement('link');\r\n    link.rel = 'preload';\r\n    link.href = href;\r\n    link.as = as;\r\n    return link;\r\n  };\r\n\r\n  return (\r\n    <>\r\n      {showMaintenanceBanner && (\r\n        <div id=\"maintenance-container\" className=\"p-[5px] bg-[#f7a73f] text-center\">\r\n          We are currently undergoing maintenance. We're expecting to be back at 4 AM EST.\r\n        </div>\r\n      )}\r\n      <header id=\"header\" className={`headernav border-gray-500 bg-white text-black w-3/4 mx-auto rounded-3xl mt-3 py-2 z-[8888] ${showMaintenanceBanner ? 'top-[60px]' : ''}`}>\r\n        <div className=\"container mx-auto flex justify-between items-center px-4\">\r\n          <picture className=\"aiprologo\">\r\n            <source type=\"image/webp\" srcSet={aiproLogoWebP} width=\"150\" height=\"52\" className=\"aiprologo\" />\r\n            <img src={aiproLogo} alt=\"AI-Pro Logo\" className=\"aiprologo\"/>\r\n          </picture>\r\n          {(isChatGptGo || isTexttoImage || isChatGptV2) && showLink && (\r\n            <nav className=\"text-xs lg:text-sm block inline-flex\" id=\"menu\">\r\n              <ul className=\"headnav flex inline-flex\">\r\n                <li className=\"mr-1 md:mr-2 lg:mr-6\">\r\n                  <a href={auth ? '/my-account' : '/login'} className=\"font-bold\" aria-label={auth ? 'my-account' : 'login'}>\r\n                    {auth ? 'My Apps' : 'LOG IN'}\r\n                  </a>\r\n                </li>\r\n              </ul>\r\n            </nav>\r\n          )}\r\n        </div>\r\n      </header>\r\n    </>\r\n  );\r\n}\r\n\r\nexport default HeaderLogo;\r\n", "var _g, _defs;\nfunction _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\nimport * as React from \"react\";\nfunction SvgAipro(_ref, svgRef) {\n  let {\n    title,\n    titleId,\n    ...props\n  } = _ref;\n  return /*#__PURE__*/React.createElement(\"svg\", _extends({\n    width: 451,\n    height: 157,\n    viewBox: \"0 0 451 157\",\n    fill: \"none\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, _g || (_g = /*#__PURE__*/React.createElement(\"g\", {\n    clipPath: \"url(#clip0_2054_286)\"\n  }, /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M92.717 79.8955C91.7079 79.898 90.8221 79.5666 90.0844 78.8815C89.438 78.2806 88.7889 77.6721 88.2042 77.0144C87.6096 76.3467 86.6769 76.3763 86.107 77.0193C85.6035 77.5881 85.0483 78.1124 84.5007 78.6392C83.2546 79.8361 81.7939 80.17 80.1927 79.5345C78.5839 78.8964 77.7647 77.6277 77.6759 75.9088C77.5969 74.3657 78.5568 72.8595 79.9853 72.1993C81.4707 71.5118 83.1683 71.7739 84.3896 72.909C85.0064 73.4827 85.6159 74.0689 86.1736 74.6996C86.7732 75.3747 87.6441 75.4266 88.3054 74.697C88.8433 74.1034 89.4207 73.5446 90.0029 72.9931C91.1724 71.8876 92.9095 71.5982 94.3554 72.2437C95.8358 72.9065 96.7735 74.4077 96.7265 76.0424C96.6649 78.1989 94.8908 79.9029 92.717 79.898V79.8955Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M67.7897 52.4641C66.3315 52.4716 65.4408 52.1476 64.703 51.4625C64.0418 50.8467 63.4125 50.1963 62.7685 49.5607C61.9938 48.7965 61.3917 48.799 60.6096 49.5805C60.0273 50.1592 59.4575 50.7528 58.8652 51.324C57.6735 52.479 55.9735 52.7807 54.4807 52.1204C52.9607 51.4453 52.0034 49.9465 52.1022 48.2797C52.2008 46.6375 53.0151 45.4208 54.5324 44.7876C56.0894 44.1372 57.555 44.4093 58.8159 45.5469C59.4253 46.0959 60.0125 46.6771 60.5579 47.2904C61.2882 48.114 62.1296 48.1486 62.9068 47.2706C63.4373 46.6722 64.0196 46.1182 64.6019 45.5667C65.7887 44.4414 67.5455 44.1595 69.0036 44.8371C70.4841 45.5246 71.3971 47.0258 71.3305 48.6605C71.2441 50.7874 69.4824 52.4667 67.7873 52.4667L67.7897 52.4641Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M68.2449 83.3704C68.3484 82.7224 68.504 81.7086 68.6792 80.697C68.8543 79.688 68.5286 79.1711 67.5268 78.9733C66.725 78.8125 65.9107 78.7061 65.1113 78.533C63.2213 78.125 61.8889 76.426 61.9703 74.5142C62.0665 72.2217 63.8307 70.9234 65.3531 70.686C68.0179 70.2704 70.3421 72.6297 69.8979 75.2563C69.7722 76.0055 69.6439 76.7548 69.5205 77.5042C69.4884 77.7069 69.4564 77.9123 69.4515 78.1176C69.4317 78.7557 69.7771 79.2354 70.4063 79.3689C71.1268 79.5223 71.857 79.6335 72.5849 79.7448C74.3713 80.0168 75.6544 81.1248 76.0813 82.7794C76.6538 85.0002 75.1831 87.2457 72.9379 87.6885C70.616 88.146 68.1757 86.2887 68.2399 83.3728L68.2449 83.3704Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M89.2851 68.3141C87.6986 68.3438 85.8727 67.2137 85.4114 65.188C84.9944 63.3655 85.8802 61.518 87.558 60.7093C88.3525 60.326 89.1692 59.9946 89.9711 59.6286C90.8741 59.2181 91.0986 58.6493 90.7163 57.7392C90.4128 57.0194 90.0845 56.3122 89.7835 55.59C88.7645 53.1467 90.0994 50.5919 92.6827 50.033C95.1897 49.4889 97.6644 51.7048 97.4003 54.267C97.24 55.8274 96.4528 56.9577 95.0316 57.623C94.289 57.9716 93.5316 58.2858 92.7839 58.6196C91.8512 59.0375 91.6218 59.6088 92.0214 60.5487C92.3275 61.2658 92.6532 61.9781 92.9517 62.6977C93.9337 65.057 92.6654 67.587 90.1931 68.193C89.8971 68.2647 89.5862 68.277 89.2827 68.3141H89.2851Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M80.3604 40.3706C80.2444 41.0903 80.0816 42.1265 79.9089 43.1603C79.7558 44.0629 80.1382 44.6194 81.0462 44.7851C81.9321 44.9458 82.8252 45.0843 83.7085 45.2574C85.5937 45.6309 86.9063 47.2532 86.9014 49.1699C86.8964 51.1014 85.5715 52.6668 83.7085 53.0946C81.4238 53.6189 78.458 51.7147 79.0032 48.3587C79.1315 47.5673 79.2722 46.7784 79.4079 45.987C79.5584 45.1189 79.181 44.5699 78.2952 44.4018C77.4093 44.2361 76.5186 44.0951 75.6329 43.9269C73.7748 43.5757 72.4475 41.993 72.4105 40.0986C72.3734 38.1646 73.6565 36.5447 75.5119 36.102C77.9028 35.5332 80.422 37.3955 80.3604 40.3681V40.3706Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M58.2714 57.0669C60.1491 57.0916 61.7134 58.3331 62.1574 60.1755C62.5794 61.9363 61.7208 63.8159 60.0749 64.6395C59.2682 65.0426 58.4417 65.4135 57.6002 65.7351C56.781 66.0491 56.3443 66.7812 56.8279 67.7803C57.1857 68.5198 57.5114 69.2764 57.8199 70.0382C58.755 72.3357 57.4621 74.8657 55.0663 75.4494C53.191 75.9069 51.2838 74.967 50.4917 73.1939C49.7145 71.4578 50.2722 69.3951 51.829 68.3071C52.13 68.0969 52.4657 67.9336 52.8011 67.7803C53.5093 67.4563 54.2323 67.1645 54.9404 66.838C55.7571 66.4621 55.989 65.8513 55.6362 65.0179C55.2908 64.2041 54.9034 63.4079 54.5777 62.5867C53.6673 60.2991 54.9231 57.8162 57.2991 57.1979C57.6126 57.1163 57.9482 57.1088 58.2714 57.0669Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M69.1035 62.9101C69.111 59.992 71.492 57.6202 74.4036 57.6276C77.3026 57.6351 79.6812 60.034 79.6812 62.9473C79.6812 65.8804 77.2731 68.2719 74.3344 68.257C71.4378 68.2421 69.0937 65.8458 69.1035 62.9101Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M83.8945 39.572C83.8994 37.7988 85.3526 36.3594 87.1168 36.3792C88.8465 36.399 90.2702 37.8408 90.2801 39.5844C90.2899 41.3427 88.8341 42.8068 87.0749 42.8068C85.3107 42.8068 83.8895 41.36 83.8945 39.5745V39.572Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M58.7456 39.5941C58.7456 37.8233 60.1077 36.3865 61.7929 36.3791C63.4929 36.3716 64.9043 37.8505 64.887 39.6238C64.8697 41.3747 63.4756 42.8116 61.7953 42.8042C60.1053 42.7967 58.7482 41.3673 58.7456 39.5941Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M45.4366 62.2305C45.4292 60.5191 46.8256 59.1342 48.58 59.1143C50.3466 59.0945 51.8271 60.5068 51.8222 62.2106C51.8173 63.8973 50.3788 65.2871 48.6317 65.2947C46.8751 65.302 45.4439 63.9294 45.4366 62.2329V62.2305Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M98.659 59.1122C100.401 59.1071 101.852 60.4946 101.869 62.1787C101.886 63.8827 100.421 65.2973 98.6441 65.2899C96.8899 65.2825 95.4785 63.9025 95.481 62.1961C95.4834 60.4946 96.9022 59.1171 98.659 59.1122Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M87.3443 80.8603C89.0837 80.8678 90.5297 82.2699 90.5297 83.9517C90.5297 85.6606 89.059 87.0579 87.2826 87.0381C85.5307 87.0208 84.1341 85.631 84.1441 83.9196C84.1539 82.2229 85.5899 80.8529 87.3468 80.8603H87.3443Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M58.7471 83.9715C58.7422 82.2502 60.1214 80.8529 61.819 80.8603C63.482 80.8678 64.8712 82.2576 64.8859 83.9318C64.9007 85.6234 63.4893 87.0455 61.7968 87.0406C60.1165 87.0357 58.752 85.6631 58.7446 83.9739L58.7471 83.9715Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M17.2184 120.924L17.2141 116.161H67.6866C68.6013 114.482 69.2906 111.828 70.1946 110.17C70.186 110.154 70.169 110.074 70.1245 110.023C68.151 107.75 65.9356 105.851 63.2682 104.656C61.2906 103.772 59.2301 103.403 57.1039 103.581C55.3319 103.728 53.63 104.228 51.9983 105C46.1564 107.764 40.1531 109.997 33.978 111.664C31.2385 112.406 28.482 113.063 25.6873 113.491C23.994 113.75 22.2942 113.972 20.5903 114.141C19.5038 114.248 18.3472 114.336 17.214 114.373C17.1949 114.221 17.2184 113.963 17.2184 113.818C17.2141 111.968 17.2099 110.119 17.2226 108.269C17.2226 108.049 17.2969 107.813 17.3903 107.612C22.5595 96.3247 27.733 85.0422 32.9064 73.7573C33.3351 72.8219 33.7574 71.8795 34.2115 70.8764C34.6974 71.0845 35.1961 71.2974 35.7415 71.5311C31.3425 83.7116 26.9606 95.8407 22.5404 108.073C22.986 108.024 23.3574 107.996 23.7266 107.937C32.7792 106.473 41.4667 103.606 49.872 99.6569C51.8306 98.7357 53.8592 98.0996 55.9749 97.833C59.3828 97.4028 62.655 97.9967 65.7957 99.5026C68.5989 100.85 71.0242 102.802 73.1993 105.154C73.3373 105.304 73.4434 105.489 73.5897 105.692C74.0269 105.239 74.3941 104.853 74.7675 104.474C77.1378 102.054 79.7882 100.129 82.8461 98.9275C85.1823 98.0083 87.595 97.5876 90.0715 97.7209C92.5966 97.8565 95.0221 98.5111 97.3436 99.6102C103.139 102.355 109.098 104.554 115.23 106.169C117.904 106.876 120.599 107.481 123.328 107.876C123.78 107.942 124.23 108.017 124.786 108.103C120.367 95.8711 115.981 83.7303 111.589 71.5686C111.71 71.4961 111.78 71.4399 111.858 71.4072C112.263 71.2272 112.673 71.0541 113.114 70.8647C113.246 71.1757 113.359 71.4563 113.484 71.73C117.185 79.802 120.887 87.874 124.588 95.9436C126.356 99.7973 128.119 103.653 129.891 107.504C130.029 107.806 130.101 108.106 130.099 108.449C130.086 110.163 130.093 111.875 130.093 113.589V114.26C129.885 114.26 129.73 114.265 129.577 114.26C129.153 114.241 128.73 114.204 128.306 114.199C126.326 114.176 124.363 113.935 122.405 113.626C116.578 112.707 110.895 111.129 105.299 109.141C101.925 107.942 98.6148 106.558 95.3553 105.019C93.5409 104.163 91.6545 103.628 89.6725 103.55C86.933 103.443 84.4035 104.236 82.0418 105.73C80.2338 106.873 78.655 108.33 77.2184 109.986C77.1717 110.039 77.1336 110.107 77.1081 110.144C78.0078 111.795 78.7033 114.468 79.6264 116.161H130.093L130.093 120.906H76.3271C75.9431 119.963 75.5569 118.967 75.1325 117.99C74.7059 117.012 74.237 116.058 73.6577 115.104C72.5351 116.961 71.7564 118.934 70.9966 120.924L17.2184 120.92V120.924Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M197.092 84.997L190.692 67.0263C190.51 66.4944 190.275 65.5826 189.989 64.2908C189.703 62.9991 189.404 61.4161 189.092 59.5417C188.753 61.34 188.427 62.961 188.117 64.4047C187.804 65.8231 187.57 66.7729 187.414 67.2542L181.248 84.997H197.092ZM160.72 106.159L182.457 50.5374H196.428L218.477 106.159H204.584L199.94 94.3432H177.814L173.833 106.159H160.72Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M224.722 106.159V50.5374H236.937V106.159H224.722Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M249.347 92.4056V82.8313H271.201V92.4056H249.347Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M295.63 75.6507H297.386C300.716 75.6507 303.135 74.9795 304.645 73.6371C306.153 72.2946 306.908 70.1417 306.908 67.1782C306.908 64.4428 306.153 62.4545 304.645 61.2133C303.135 59.947 300.716 59.3137 297.386 59.3137H295.63V75.6507ZM283.338 106.159V50.5374H297.386C304.879 50.5374 310.407 51.9304 313.971 54.7165C317.562 57.5028 319.358 61.8086 319.358 67.6342C319.358 73.0291 317.55 77.2591 313.933 80.3238C310.343 83.3886 305.36 84.9209 298.986 84.9209H295.63V106.159H283.338Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M369.154 106.159H355.066L340.041 80.1719V106.159H327.825V50.5374H345.309C352.256 50.5374 357.459 51.8544 360.919 54.4886C364.38 57.0975 366.11 61.0361 366.11 66.3044C366.11 70.1291 364.926 73.3965 362.559 76.1066C360.19 78.8168 357.134 80.3998 353.387 80.8557L369.154 106.159ZM340.041 74.245H341.875C346.819 74.245 350.083 73.7257 351.67 72.6873C353.257 71.6235 354.05 69.7871 354.05 67.1782C354.05 64.4428 353.192 62.5052 351.475 61.3654C349.785 60.2002 346.585 59.6176 341.875 59.6176H340.041V74.245Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M434.793 78.4622C434.793 82.4388 434.026 86.2 432.49 89.746C430.982 93.2921 428.797 96.4075 425.936 99.0923C422.968 101.853 419.626 103.968 415.905 105.437C412.184 106.906 408.335 107.641 404.354 107.641C400.868 107.641 397.447 107.071 394.091 105.931C390.761 104.766 387.703 103.107 384.92 100.954C381.329 98.1678 378.571 94.837 376.647 90.9618C374.747 87.0866 373.797 82.92 373.797 78.4622C373.797 74.4603 374.552 70.7116 376.06 67.2163C377.57 63.6955 379.781 60.5674 382.695 57.832C385.556 55.1218 388.874 53.0195 392.647 51.5251C396.444 50.0308 400.347 49.2836 404.354 49.2836C408.335 49.2836 412.198 50.0308 415.945 51.5251C419.716 53.0195 423.046 55.1218 425.936 57.832C428.823 60.5674 431.02 63.6955 432.53 67.2163C434.038 70.7369 434.793 74.4856 434.793 78.4622ZM404.354 97.0406C409.531 97.0406 413.798 95.2804 417.155 91.7596C420.537 88.2137 422.227 83.7811 422.227 78.4622C422.227 73.1938 420.523 68.7612 417.115 65.1646C413.706 61.5679 409.453 59.7696 404.354 59.7696C399.176 59.7696 394.884 61.5679 391.476 65.1646C388.068 68.7359 386.363 73.1684 386.363 78.4622C386.363 83.8318 388.042 88.2769 391.398 91.7977C394.754 95.293 399.073 97.0406 404.354 97.0406Z\",\n    fill: \"black\"\n  }))), _defs || (_defs = /*#__PURE__*/React.createElement(\"defs\", null, /*#__PURE__*/React.createElement(\"clipPath\", {\n    id: \"clip0_2054_286\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    width: 418,\n    height: 86,\n    fill: \"white\",\n    transform: \"translate(17 36)\"\n  })))));\n}\nconst ForwardRef = /*#__PURE__*/React.forwardRef(SvgAipro);\nexport default __webpack_public_path__ + \"static/media/AIPRO.0580715cba3cbc2179c46b033d087db9.svg\";\nexport { ForwardRef as ReactComponent };", "import React, { useState, useEffect } from 'react';\r\nimport './style.css';\r\nimport Header from '../header/headerlogo';\r\nimport { FaInfoCircle } from 'react-icons/fa';\r\nimport { Auth } from '../core/utils/auth';\r\nimport { GetCookie, RemoveCookie } from '../core/utils/cookies';\r\nimport { useQuery } from \"react-query\";\r\nimport axios from 'axios';\r\nimport { Helmet } from 'react-helmet';\r\nimport { SetCookie } from '../core/utils/cookies';\r\nimport toastr from 'toastr';\r\nimport 'toastr/build/toastr.min.css';\r\nimport aiproLogo from '../assets/images/AIPRO.svg?webp';\r\nimport authorizeNet from '../assets/images/authorizeNet.png';\r\nimport cc_v4 from '../assets/images/cc_v4.png';\r\n\r\n\r\nconst pricing = GetCookie(\"pricing\") ? GetCookie(\"pricing\") : \"\";\r\nconst tk = GetCookie(\"access\") ? GetCookie(\"access\") : \"\";\r\nconst ppg = GetCookie(\"ppg\") ? GetCookie(\"ppg\") : \"\";\r\nvar plan = null;\r\n\r\nasync function getPlan() {\r\n  if(plan) return plan;\r\n  const response = await axios.post(`${process.env.REACT_APP_API_URL}/get-plan`, { plan_id: pricing }, { headers: { 'content-type': 'application/x-www-form-urlencoded' } });\r\n  const output = response.data;\r\n  if(output.success) {\r\n    plan = output.data;\r\n    return output.data;\r\n  } else {\r\n    return [];\r\n  }\r\n}\r\n\r\nfunction Payment() {\r\n  useEffect(() => {\r\n    toastr.options = {\r\n      positionClass: 'toast-top-center'\r\n    };\r\n  }, []);\r\n\r\n  const { data } = useQuery(\"users\", getPlan);\r\n  const [willRedirect, setWillRedirect] = useState(true);\r\n\r\n  const auth = Auth('/register-auth');\r\n  if(auth === undefined || auth === false) return;\r\n\r\n  if(willRedirect) {\r\n    if (auth.status === 'active' && auth.expired === 'no') {\r\n      window.location.href = '/my-account';\r\n      return;\r\n    }\r\n  }\r\n\r\n  var date = new Date();\r\n  date.setTime(date.getTime() + 30 * 24 * 60 * 60 * 1000);\r\n  var today = new Date();\r\n  var expire_date = new Date();\r\n  expire_date.setDate(today.getDate() + 30);\r\n\r\n  if (data && data.currency && data.price){\r\n    var amount = data.price;\r\n    if (data.trial_price !== ''){\r\n      amount = data.trial_price;\r\n    }\r\n    SetCookie('currency', data.currency, { expires: expire_date, path: '/' });\r\n    SetCookie('currency', data.currency, { expires: expire_date, domain: '.ai-pro.org', path: '/' });\r\n    SetCookie('amount', amount, { expires: expire_date, path: '/' });\r\n    SetCookie('amount', amount, { expires: expire_date, domain: '.ai-pro.org', path: '/' });\r\n  }\r\n\r\n  const fastSpringCallBack = (data) => {\r\n    window.fastspring.builder.reset()\r\n    window.fastspring.builder.add(plan.fs_plan_id)\r\n    window.fastspring.builder.tag({'email':auth.email,'user_pid':auth.user_pid, \"ppg\":ppg});      \r\n    window.fastspring.builder.recognize(\r\n      {\r\n        \"email\":auth.email\r\n      }\r\n    );      \r\n    console.log('plan',plan);\r\n\r\n  };\r\n\r\n  const dataPopupWebhookReceived = (dataParam) => {\r\n    setWillRedirect(false);\r\n\r\n    document.querySelector(\".loader-container\").classList.add('active');\r\n    document.getElementById(\"fs-display\").style.visibility  = 'hidden'; \r\n\r\n    var url = `${process.env.REACT_APP_API_URL}/t/create-subscription-fs`;\r\n    axios.post(url, {\r\n      tk,\r\n      id: dataParam.id,\r\n      account: dataParam.account,\r\n      reference: dataParam.reference,\r\n      sub_id: dataParam.items[0].subscription,\r\n      plan_id: plan.plan_id\r\n    }, { headers: { 'content-type': 'application/x-www-form-urlencoded' } }).then(function(res) {\r\n      let output = res.data;\r\n      if(output.success){\r\n        toastr.success(\"Success\");\r\n        window.location.href = '/thankyou/?plan='+plan.label.replace(\" \",\"\").replace(\" \",\"\");\r\n      }else{\r\n        if(output.data) {\r\n          if(output.data.message) {\r\n            toastr.error(output.data.message);\r\n          } else {\r\n            toastr.error(output.data.msg);\r\n          }\r\n        }\r\n        document.getElementById(\"fs-display\").style.visibility  = 'visible'; \r\n      }\r\n    }).catch(function (error) {\r\n      if (error.response && error.response.status===429) {\r\n        document.querySelector(\".loader-container\").classList.remove('active');\r\n        toastr.error(\"Sorry, too many requests. Please try again in a bit!\");\r\n      }\r\n      document.getElementById(\"fs-display\").style.visibility  = 'visible'; \r\n    });\r\n  };\r\n\r\n  window.fastSpringCallBack = fastSpringCallBack;\r\n  window.dataPopupWebhookReceived = dataPopupWebhookReceived;\r\n  window.RemoveCookie = RemoveCookie;\r\n\r\n  const addSBL = () => {\r\n    let storeFront = '';\r\n\r\n    if(auth.mode==='test'){\r\n      storeFront = 'aiproorg.test.onfastspring.com/embedded-startCustomUI';\r\n    }else{\r\n      storeFront = 'aiproorg.onfastspring.com/embedded-startCustomUI'\r\n    }\r\n \r\n    const scriptId = \"fsc-api\";\r\n    let script = document.getElementById(scriptId);\r\n    if (!script) {\r\n      script = document.createElement(\"script\");\r\n      script.type = \"text/javascript\";\r\n      script.id = scriptId;\r\n      script.setAttribute(\"data-continuous\", \"true\");\r\n      script.src = \"https://sbl.onfastspring.com/sbl/1.0.1/fastspring-builder.min.js\";\r\n      script.dataset.storefront =storeFront;\r\n      script.setAttribute(\"data-data-callback\", \"fastSpringCallBack\");\r\n      script.setAttribute(\"data-popup-webhook-received\",\"dataPopupWebhookReceived\");\r\n      document.body.appendChild(script);\r\n    }\r\n  };\r\n\r\n  // Load SBL consistently\r\n  addSBL();\r\n\r\n  return (\r\n    <>\r\n      <Helmet>\r\n        <title>AI Pro | Payment Option</title>\r\n        <meta name=\"description\" content=\"Safely complete your purchase with our secure payment options. Buy now with confidence!\" />\r\n      </Helmet>\r\n      <Header auth={auth} />\r\n      { data ?\r\n      <div className=\"Payment bg-gray-100 md:min-h-[90vh] flex md:pt-[50px]\">\r\n\r\n          <div className=\"px-1 md:px-4 mx-auto py-10\">\r\n            <div className=\"flex flex-col items-center py-10 lg:py-16\">\r\n              <div className=\"flex flex-wrap md:flex-wrap justify-center w-full\">\r\n                <div className=\"pay_left px-4 mb-8 w-[53%] min-w-[24rem]\">\r\n                  <div className=\"bg-white rounded-lg shadow-lg overflow-hidden min-h-[95%] content-center\">\r\n                    <div className=\"pr-6 pl-10\">\r\n                      <div className=\"\">\r\n                      <div class=\"flex w-full justify-center\"><svg version=\"1.0\" xmlns=\"http://www.w3.org/2000/svg\" width=\"80px\" height=\"55px\" viewBox=\"0 0 64 64\" enable-background=\"new 0 0 64 64\" className=\"pt-4 pl-5 pr-4\"><g>\r\n                        <path fill=\"#231F20\" d=\"M0,32v20c0,2.211,1.789,4,4,4h56c2.211,0,4-1.789,4-4V32H0z M24,44h-8c-2.211,0-4-1.789-4-4s1.789-4,4-4h8c2.211,0,4,1.789,4,4S26.211,44,24,44z\"></path>\r\n                        <path fill=\"#231F20\" d=\"M64,24V12c0-2.211-1.789-4-4-4H4c-2.211,0-4,1.789-4,4v12H64z\"></path>\r\n                        </g>\r\n                        </svg><h2 class=\"text-2xl font-bold pt-5 pr-5\"> Billing Details</h2>\r\n                      </div>\r\n                      <div id=\"fs-display\" class=\"fs-display\">\r\n                        <div id=\"fsc-embedded-checkout-container\"></div>\r\n                      </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"text-[12px] text-gray-600 px-5 pt-5 flex justify-center text-center\tflow-root\"><FaInfoCircle className=\"inline text-lg mr-1\"/> By clicking the \"Pay\" button, I have read and agreed to the Terms and Conditions.</div>\r\n                </div>\r\n\r\n                <div className=\"pay_right px-4 mb-8 w-[30%] justify-items-center min-w-[22rem]\">\r\n                  <div className=\"relative bg-white rounded-lg shadow-lg px-8 justify-center min-h-order min-w-[16rem] max-w-[20rem] min-h-[95%] align-content-center\">\r\n                    <div class=\"row justify-center\">\r\n                      <img src={aiproLogo} alt=\"AI-Pro Logo\" className=\"pt-6 max-w-[75%] justify-self-center\t\"/>\r\n                      <h2 className=\"text-lg font-bold pt-[3.5rem] text-center pb-2\">Order Summary</h2>\r\n\r\n                      <div class=\"pb-5 flex flex-col items-center\">\r\n                        <div class=\"mb-2 text-sm flex flex-col items-center py-12 min-w-full border-[#e9e9e9] border-y-[1px]\" >\r\n                          <div class=\"flex-1 font-bold pb-9\">Subscription Plan</div>\r\n                          {\r\n                            (data.trial_days > 0) ?\r\n                            <div className=\"text-lg flex-1 text-center\">{ data.plan_type_display.toLowerCase().replace(/\\b\\w/g, s => s.toUpperCase()).replace('Annual', '')} Trial</div> : \r\n                            <div className=\"text-lg flex-1 text-center\">{ data.plan_type_display.toLowerCase().replace(/\\b\\w/g, s => s.toUpperCase()).replace('Annual', '')} Plan</div> \r\n                          }\r\n                          <div className=\"text-lg flex-1 text-center\">{ data.payment_interval }</div>\r\n                        </div>\r\n                        { (data.trial_days > 0 && data.payment_interval === 'Monthly') ? \r\n                        <div className=\"flex-1 pt-4 text-xs pb-44\ttext-center\">After the trial ends, you will be charged the regular monthly plan rate. Your subscription will renew until you cancel it.</div> : \r\n                        (data.trial_days > 0 && data.payment_interval === 'Yearly') ?\r\n                        <div className=\"flex-1 pt-4 text-xs pb-44\ttext-center\">After the trial ends, you will be charged the regular annual plan rate. Your subscription will renew until you cancel it.</div> :\r\n                        (data.payment_interval === 'Yearly') ?\r\n                        <div className=\"flex-1 pt-4 text-xs pb-44\ttext-center\">Your subscription will renew annually until you cancel it.</div> : \r\n                        <div className=\"flex-1 pt-4 text-xs pb-44\ttext-center\">Your subscription will renew monthly until you cancel it.</div> \r\n                      }\r\n                      </div>\r\n\r\n                      <div className=\"h-36 flex items-center justify-center absolute bottom-0 w-[94%] ml-[-1.5rem] mr-[0.5rem] text-center\">\r\n                        <div class=\"mb-2 text-sm pb-4 flex flex-col items-center\">\r\n                          <div class=\"securecont block pt-5\">\r\n                            <div class=\"securetext mb-2 text-sm\">\r\n                              <svg stroke=\"currentColor\" fill=\"currentColor\" stroke-width=\"0\" viewBox=\"0 0 448 512\" class=\"inline text-lg mr-1 text-orange-500 text-xs\" height=\"1em\" width=\"1em\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                              <path d=\"M400 224h-24v-72C376 68.2 307.8 0 224 0S72 68.2 72 152v72H48c-26.5 0-48 21.5-48 48v192c0 26.5 21.5 48 48 48h352c26.5 0 48-21.5 48-48V272c0-26.5-21.5-48-48-48zm-104 0H152v-72c0-39.7 32.3-72 72-72s72 32.3 72 72v72z\"></path>\r\n                              </svg>  Secure Checkout\r\n                            </div>\r\n                            \r\n                            <div class=\"securelogo mb-2 text-sm\">\r\n                              <img src={cc_v4} alt=\"Secure Logo\" class=\"cclogo inline w-full h-auto\"/>\r\n                              <div class=\"flex justify-center\">\r\n                                <div class=\"w-1/4\">                                \r\n                                  <img src={authorizeNet} alt=\"Authorize.Net Logo\" class=\"authlogo inline h-auto\"/>\r\n                                </div>\r\n                                <div class=\"w-1/4\">\r\n                                <button\r\n                        onClick={() => {\r\n                          window.open(\r\n                            '//www.securitymetrics.com/site_certificate?id=1982469&tk=d41c416c6208f1136426bc8038178d2e',\r\n                            'Verification',\r\n                            'location=no, toolbar=no, resizable=no, scrollbars=yes, directories=no, status=no,top=100,left=100, width=700, height=600'\r\n                          );\r\n                        }}\r\n                        title=\"SecurityMetrics card safe certification\"\r\n                        className=\"inline-block\" >\r\n                          <img loading=\"lazy\"\r\n                            src=\"https://www.securitymetrics.com/portal/app/ngsm/assets/img/GreyContent_Credit_Card_Safe_White_Sqr.png\"\r\n                            alt=\"SecurityMetrics card safe certification logo\"\r\n                            className=\"max-h-full\"/>\r\n                      </button>\r\n\r\n                                </div>\r\n\r\n                              </div>\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n\r\n                      \r\n                     \r\n\r\n                    </div>\r\n\r\n\r\n\r\n                  </div>\r\n           \r\n\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n      </div> : \"\" }\r\n    </>\r\n  );\r\n}\r\n\r\nexport default Payment;", "export var IconsManifest = [\n  {\n    \"id\": \"ci\",\n    \"name\": \"Circum Icons\",\n    \"projectUrl\": \"https://circumicons.com/\",\n    \"license\": \"MPL-2.0 license\",\n    \"licenseUrl\": \"https://github.com/Klarr-Agency/Circum-Icons/blob/main/LICENSE\"\n  },\n  {\n    \"id\": \"fa\",\n    \"name\": \"Font Awesome 5\",\n    \"projectUrl\": \"https://fontawesome.com/\",\n    \"license\": \"CC BY 4.0 License\",\n    \"licenseUrl\": \"https://creativecommons.org/licenses/by/4.0/\"\n  },\n  {\n    \"id\": \"fa6\",\n    \"name\": \"Font Awesome 6\",\n    \"projectUrl\": \"https://fontawesome.com/\",\n    \"license\": \"CC BY 4.0 License\",\n    \"licenseUrl\": \"https://creativecommons.org/licenses/by/4.0/\"\n  },\n  {\n    \"id\": \"io\",\n    \"name\": \"Ionicons 4\",\n    \"projectUrl\": \"https://ionicons.com/\",\n    \"license\": \"MIT\",\n    \"licenseUrl\": \"https://github.com/ionic-team/ionicons/blob/master/LICENSE\"\n  },\n  {\n    \"id\": \"io5\",\n    \"name\": \"Ionicons 5\",\n    \"projectUrl\": \"https://ionicons.com/\",\n    \"license\": \"MIT\",\n    \"licenseUrl\": \"https://github.com/ionic-team/ionicons/blob/master/LICENSE\"\n  },\n  {\n    \"id\": \"md\",\n    \"name\": \"Material Design icons\",\n    \"projectUrl\": \"http://google.github.io/material-design-icons/\",\n    \"license\": \"Apache License Version 2.0\",\n    \"licenseUrl\": \"https://github.com/google/material-design-icons/blob/master/LICENSE\"\n  },\n  {\n    \"id\": \"ti\",\n    \"name\": \"Typicons\",\n    \"projectUrl\": \"http://s-ings.com/typicons/\",\n    \"license\": \"CC BY-SA 3.0\",\n    \"licenseUrl\": \"https://creativecommons.org/licenses/by-sa/3.0/\"\n  },\n  {\n    \"id\": \"go\",\n    \"name\": \"Github Octicons icons\",\n    \"projectUrl\": \"https://octicons.github.com/\",\n    \"license\": \"MIT\",\n    \"licenseUrl\": \"https://github.com/primer/octicons/blob/master/LICENSE\"\n  },\n  {\n    \"id\": \"fi\",\n    \"name\": \"Feather\",\n    \"projectUrl\": \"https://feathericons.com/\",\n    \"license\": \"MIT\",\n    \"licenseUrl\": \"https://github.com/feathericons/feather/blob/master/LICENSE\"\n  },\n  {\n    \"id\": \"lu\",\n    \"name\": \"Lucide\",\n    \"projectUrl\": \"https://lucide.dev/\",\n    \"license\": \"ISC\",\n    \"licenseUrl\": \"https://github.com/lucide-icons/lucide/blob/main/LICENSE\"\n  },\n  {\n    \"id\": \"gi\",\n    \"name\": \"Game Icons\",\n    \"projectUrl\": \"https://game-icons.net/\",\n    \"license\": \"CC BY 3.0\",\n    \"licenseUrl\": \"https://creativecommons.org/licenses/by/3.0/\"\n  },\n  {\n    \"id\": \"wi\",\n    \"name\": \"Weather Icons\",\n    \"projectUrl\": \"https://erikflowers.github.io/weather-icons/\",\n    \"license\": \"SIL OFL 1.1\",\n    \"licenseUrl\": \"http://scripts.sil.org/OFL\"\n  },\n  {\n    \"id\": \"di\",\n    \"name\": \"Devicons\",\n    \"projectUrl\": \"https://vorillaz.github.io/devicons/\",\n    \"license\": \"MIT\",\n    \"licenseUrl\": \"https://opensource.org/licenses/MIT\"\n  },\n  {\n    \"id\": \"ai\",\n    \"name\": \"Ant Design Icons\",\n    \"projectUrl\": \"https://github.com/ant-design/ant-design-icons\",\n    \"license\": \"MIT\",\n    \"licenseUrl\": \"https://opensource.org/licenses/MIT\"\n  },\n  {\n    \"id\": \"bs\",\n    \"name\": \"Bootstrap Icons\",\n    \"projectUrl\": \"https://github.com/twbs/icons\",\n    \"license\": \"MIT\",\n    \"licenseUrl\": \"https://opensource.org/licenses/MIT\"\n  },\n  {\n    \"id\": \"ri\",\n    \"name\": \"Remix Icon\",\n    \"projectUrl\": \"https://github.com/Remix-Design/RemixIcon\",\n    \"license\": \"Apache License Version 2.0\",\n    \"licenseUrl\": \"http://www.apache.org/licenses/\"\n  },\n  {\n    \"id\": \"fc\",\n    \"name\": \"Flat Color Icons\",\n    \"projectUrl\": \"https://github.com/icons8/flat-color-icons\",\n    \"license\": \"MIT\",\n    \"licenseUrl\": \"https://opensource.org/licenses/MIT\"\n  },\n  {\n    \"id\": \"gr\",\n    \"name\": \"Grommet-Icons\",\n    \"projectUrl\": \"https://github.com/grommet/grommet-icons\",\n    \"license\": \"Apache License Version 2.0\",\n    \"licenseUrl\": \"http://www.apache.org/licenses/\"\n  },\n  {\n    \"id\": \"hi\",\n    \"name\": \"Heroicons\",\n    \"projectUrl\": \"https://github.com/tailwindlabs/heroicons\",\n    \"license\": \"MIT\",\n    \"licenseUrl\": \"https://opensource.org/licenses/MIT\"\n  },\n  {\n    \"id\": \"hi2\",\n    \"name\": \"Heroicons 2\",\n    \"projectUrl\": \"https://github.com/tailwindlabs/heroicons\",\n    \"license\": \"MIT\",\n    \"licenseUrl\": \"https://opensource.org/licenses/MIT\"\n  },\n  {\n    \"id\": \"si\",\n    \"name\": \"Simple Icons\",\n    \"projectUrl\": \"https://simpleicons.org/\",\n    \"license\": \"CC0 1.0 Universal\",\n    \"licenseUrl\": \"https://creativecommons.org/publicdomain/zero/1.0/\"\n  },\n  {\n    \"id\": \"sl\",\n    \"name\": \"Simple Line Icons\",\n    \"projectUrl\": \"https://thesabbir.github.io/simple-line-icons/\",\n    \"license\": \"MIT\",\n    \"licenseUrl\": \"https://opensource.org/licenses/MIT\"\n  },\n  {\n    \"id\": \"im\",\n    \"name\": \"IcoMoon Free\",\n    \"projectUrl\": \"https://github.com/Keyamoon/IcoMoon-Free\",\n    \"license\": \"CC BY 4.0 License\",\n    \"licenseUrl\": \"https://github.com/Keyamoon/IcoMoon-Free/blob/master/License.txt\"\n  },\n  {\n    \"id\": \"bi\",\n    \"name\": \"BoxIcons\",\n    \"projectUrl\": \"https://github.com/atisawd/boxicons\",\n    \"license\": \"CC BY 4.0 License\",\n    \"licenseUrl\": \"https://github.com/atisawd/boxicons/blob/master/LICENSE\"\n  },\n  {\n    \"id\": \"cg\",\n    \"name\": \"css.gg\",\n    \"projectUrl\": \"https://github.com/astrit/css.gg\",\n    \"license\": \"MIT\",\n    \"licenseUrl\": \"https://opensource.org/licenses/MIT\"\n  },\n  {\n    \"id\": \"vsc\",\n    \"name\": \"VS Code Icons\",\n    \"projectUrl\": \"https://github.com/microsoft/vscode-codicons\",\n    \"license\": \"CC BY 4.0\",\n    \"licenseUrl\": \"https://creativecommons.org/licenses/by/4.0/\"\n  },\n  {\n    \"id\": \"tb\",\n    \"name\": \"Tabler Icons\",\n    \"projectUrl\": \"https://github.com/tabler/tabler-icons\",\n    \"license\": \"MIT\",\n    \"licenseUrl\": \"https://opensource.org/licenses/MIT\"\n  },\n  {\n    \"id\": \"tfi\",\n    \"name\": \"Themify Icons\",\n    \"projectUrl\": \"https://github.com/lykmapipo/themify-icons\",\n    \"license\": \"MIT\",\n    \"licenseUrl\": \"https://github.com/thecreation/standard-icons/blob/master/modules/themify-icons/LICENSE\"\n  },\n  {\n    \"id\": \"rx\",\n    \"name\": \"Radix Icons\",\n    \"projectUrl\": \"https://icons.radix-ui.com\",\n    \"license\": \"MIT\",\n    \"licenseUrl\": \"https://github.com/radix-ui/icons/blob/master/LICENSE\"\n  },\n  {\n    \"id\": \"pi\",\n    \"name\": \"Phosphor Icons\",\n    \"projectUrl\": \"https://github.com/phosphor-icons/core\",\n    \"license\": \"MIT\",\n    \"licenseUrl\": \"https://github.com/phosphor-icons/core/blob/main/LICENSE\"\n  },\n  {\n    \"id\": \"lia\",\n    \"name\": \"Icons8 Line Awesome\",\n    \"projectUrl\": \"https://icons8.com/line-awesome\",\n    \"license\": \"MIT\",\n    \"licenseUrl\": \"https://github.com/icons8/line-awesome/blob/master/LICENSE.md\"\n  }\n]", "import React from \"react\";\nexport var DefaultContext = {\n  color: undefined,\n  size: undefined,\n  className: undefined,\n  style: undefined,\n  attr: undefined\n};\nexport var IconContext = React.createContext && React.createContext(DefaultContext);", "var __assign = this && this.__assign || function () {\n  __assign = Object.assign || function (t) {\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\n      s = arguments[i];\n      for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n    }\n    return t;\n  };\n  return __assign.apply(this, arguments);\n};\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport React from \"react\";\nimport { IconContext, DefaultContext } from \"./iconContext\";\nfunction Tree2Element(tree) {\n  return tree && tree.map(function (node, i) {\n    return React.createElement(node.tag, __assign({\n      key: i\n    }, node.attr), Tree2Element(node.child));\n  });\n}\nexport function GenIcon(data) {\n  // eslint-disable-next-line react/display-name\n  return function (props) {\n    return React.createElement(IconBase, __assign({\n      attr: __assign({}, data.attr)\n    }, props), Tree2Element(data.child));\n  };\n}\nexport function IconBase(props) {\n  var elem = function (conf) {\n    var attr = props.attr,\n      size = props.size,\n      title = props.title,\n      svgProps = __rest(props, [\"attr\", \"size\", \"title\"]);\n    var computedSize = size || conf.size || \"1em\";\n    var className;\n    if (conf.className) className = conf.className;\n    if (props.className) className = (className ? className + \" \" : \"\") + props.className;\n    return React.createElement(\"svg\", __assign({\n      stroke: \"currentColor\",\n      fill: \"currentColor\",\n      strokeWidth: \"0\"\n    }, conf.attr, attr, svgProps, {\n      className: className,\n      style: __assign(__assign({\n        color: props.color || conf.color\n      }, conf.style), props.style),\n      height: computedSize,\n      width: computedSize,\n      xmlns: \"http://www.w3.org/2000/svg\"\n    }), title && React.createElement(\"title\", null, title), props.children);\n  };\n  return IconContext !== undefined ? React.createElement(IconContext.Consumer, null, function (conf) {\n    return elem(conf);\n  }) : elem(DefaultContext);\n}"], "names": ["_ref", "hideNavLink", "auth", "<PERSON><PERSON>", "pathname", "window", "location", "isChatGptGo", "test", "isTexttoImage", "isChatGptV2", "isRegisterPage", "includes", "showLink", "showMaintenanceBanner", "process", "REACT_APP_ShowMaintenanceBanner", "useEffect", "linkPNG", "createPreloadLink", "aiproLogo", "linkWebP", "aiproLogoWebP", "document", "head", "append", "remove", "href", "as", "link", "createElement", "rel", "_jsxs", "_Fragment", "children", "_jsx", "id", "className", "type", "srcSet", "width", "height", "src", "alt", "pricing", "Get<PERSON><PERSON><PERSON>", "tk", "ppg", "plan", "async", "getPlan", "output", "axios", "post", "plan_id", "headers", "data", "success", "toastr", "positionClass", "useQuery", "willRedirect", "setWillRedirect", "useState", "undefined", "status", "expired", "date", "Date", "setTime", "getTime", "today", "expire_date", "setDate", "getDate", "currency", "price", "amount", "trial_price", "<PERSON><PERSON><PERSON><PERSON>", "expires", "path", "domain", "fastSpringCallBack", "fastspring", "builder", "reset", "add", "fs_plan_id", "tag", "email", "user_pid", "recognize", "console", "log", "dataPopupWebhookReceived", "dataParam", "querySelector", "classList", "getElementById", "style", "visibility", "account", "reference", "sub_id", "items", "subscription", "then", "res", "label", "replace", "message", "msg", "catch", "error", "response", "RemoveCookie", "addSBL", "storeFront", "mode", "scriptId", "script", "setAttribute", "dataset", "storefront", "body", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "name", "content", "Header", "class", "version", "xmlns", "viewBox", "fill", "d", "FaInfoCircle", "trial_days", "plan_type_display", "toLowerCase", "s", "toUpperCase", "payment_interval", "stroke", "cc_v4", "authorizeNet", "onClick", "open", "title", "loading", "DefaultContext", "color", "size", "attr", "IconContext", "React", "__assign", "Object", "assign", "t", "i", "n", "arguments", "length", "p", "prototype", "hasOwnProperty", "call", "apply", "this", "__rest", "e", "indexOf", "getOwnPropertySymbols", "propertyIsEnumerable", "Tree2Element", "tree", "map", "node", "key", "child", "GenIcon", "props", "IconBase", "elem", "conf", "svgProps", "computedSize", "strokeWidth", "Consumer"], "sourceRoot": ""}