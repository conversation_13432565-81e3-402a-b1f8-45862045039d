"use strict";(self.webpackChunkv1=self.webpackChunkv1||[]).push([[7670,9044,7250,2377],{72608:(e,t,a)=>{a.d(t,{Z:()=>r});a(72791);const r=a.p+"static/media/AIPRO.0580715cba3cbc2179c46b033d087db9.svg"},34492:(e,t,a)=>{a.d(t,{aS:()=>o,ar:()=>h,mD:()=>y,mW:()=>x,o0:()=>u,p6:()=>c,rZ:()=>l,tN:()=>p,x6:()=>i,yt:()=>m});var r=a(74335),s=a(80184);function n(e){return e?"usd"===e.toLowerCase()?"$":"eur"===e.toLowerCase()?"€":"gbp"===e.toLowerCase()?"£":"brl"===e.toLowerCase()||"sar"===e.toLowerCase()?"R$":"czk"===e.toLowerCase()?"Kč":"":""}function l(e){return e?"usd"===e.toLowerCase()||"eur"===e.toLowerCase()?"US":"gbp"===e.toLowerCase()?"GB":"brl"===e.toLowerCase()?"US":"sar"===e.toLowerCase()?"AE":"chf"===e.toLowerCase()?"CH":"sek"===e.toLowerCase()?"SE":"US":""}function c(e){if(!e)return"";const t=new Date(e.replace(/ /g,"T"));return["January","February","March","April","May","June","July","August","September","October","November","December"][t.getMonth()]+" "+t.getDate()+" "+t.getFullYear()}function o(e){return e.trial_price?e.trial_price:e.price?e.price:"0"}function i(e){return e.toString().replace(/\B(?=(\d{3})+(?!\d))/g,",")}function d(e){const t=parseFloat(e);return i(t%1==0?t.toFixed(0):t.toFixed(2))}function m(e,t){return e&&t?isNaN(t)?"":parseFloat(t)>=0?"sar"===e.toLowerCase()?d(t).toLocaleString("en-US")+"ر.س.":"aed"===e.toLowerCase()?d(t).toLocaleString("en-US")+"AED":"chf"===e.toLowerCase()?d(t).toLocaleString("en-US")+"CHF":"sek"===e.toLowerCase()?d(t).toLocaleString("en-US")+"SEK":"pln"===e.toLowerCase()?d(t).toLocaleString("en-US")+"zł":"ron"===e.toLowerCase()?d(t).toLocaleString("en-US")+"LEI":"huf"===e.toLowerCase()?d(t).toLocaleString("en-US")+"Ft":"dkk"===e.toLowerCase()?d(t).toLocaleString("en-US")+"kr.":"bgn"===e.toLowerCase()?d(t).toLocaleString("en-US")+"лв":"try"===e.toLowerCase()?d(t).toLocaleString("en-US")+"₺":n(e)+d(t).toLocaleString("en-US"):"-"+n(e)+(-1*d(t)).toLocaleString("en-US"):""}function p(e,t){e=new Date(e);var a=((t=new Date(t)).getTime()-e.getTime())/1e3;return a/=60,Math.abs(Math.round(a))}function u(e){if(!e)return"";const t=new Date(e.replace(/ /g,"T"));return["January","February","March","April","May","June","July","August","September","October","November","December"][t.getMonth()]+" "+t.getDate()+" "+t.getFullYear()+" "+t.getHours()+":"+t.getMinutes()}function x(e){let{plan:t}=e,a="",n="";return"Yearly"===t.payment_interval&&(a=m(t.currency,parseFloat(t.price/365).toFixed(2)),n=(0,s.jsxs)("div",{class:"text-xs mb-4",children:["billed ",m(t.currency,t.price),"/year"]})),"Monthly"===t.payment_interval&&(a=m(t.currency,parseFloat(t.price/30).toFixed(2)),n=(0,s.jsxs)("div",{class:"text-xs mb-4",children:["billed ",m(t.currency,t.price),"/Month"]})),t.trial_price&&(a=m(t.currency,parseFloat(t.trial_price/t.trial_days).toFixed(2)),n=(0,s.jsxs)("div",{class:"text-xs mb-4",children:["billed ",m(t.currency,t.trial_price)]})),(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("p",{className:"text-4xl font-bold text-gray-800 ".concat(""===(0,r.bG)("p_toggle")?"mb-4":""),children:[a," ",(0,s.jsx)("span",{className:"text-sm",children:" per Day"})]}),n]})}function y(e){let{plan:t}=e;return"on"===(0,r.bG)("daily")?x({plan:t}):t.trial_price?(0,s.jsx)("p",{className:"text-4xl font-bold text-gray-800 mb-4",children:m(t.currency,t.trial_price)}):(0,s.jsxs)("p",{className:"text-4xl font-bold text-gray-800 mb-4",children:[m(t.currency,t.price),(0,s.jsxs)("span",{className:"text-sm",children:["/","Monthly"===t.payment_interval?"month":"year"]})]})}function h(e){var t,a;const s=(null!==(t=(0,r.bG)("locales"))&&void 0!==t?t:"en").toLowerCase(),n=(null!==(a=(0,r.bG)("daily"))&&void 0!==a?a:"off").toLowerCase(),{trial_days:l,payment_interval:c,trial_price:o,currency:i,currency_symbol:d}=e;let{display_txt2:p,price:u}=e;if(l>0&&o>0&&"en"===s){let e=u,t="month";"on"===n&&(e=parseFloat(u/("Yearly"===c?365:30)).toFixed(2),t="day<br>(billed ".concat(d+u," ").concat(c,")")),p+="<div>".concat(l,"-Day Trial, then only ").concat(m(i,e)," per ").concat(t,"</div>")}return p}},27250:(e,t,a)=>{a.r(t),a.d(t,{default:()=>o});var r=a(72791),s=a(72608),n=a(19878),l=a(28891),c=(a(29534),a(80184));const o=function(e){let{hideNavLink:t,auth:o=(0,l.gx)()}=e;const i=window.location.pathname,d=/\/start-chatgpt-go\/?$/.test(i),m=/\/text-to-image\/?$/.test(i),p=/\/start-chatgpt-v2\/?$/.test(i),u=i.includes("/register"),x=!t;(0,r.useEffect)((()=>{const e=y(s.Z,"image"),t=y(n,"image");return document.head.append(e,t),u||Promise.all([a.e(7749),a.e(1707)]).then(a.bind(a,51707)),()=>{e.remove(),t.remove()}}),[u]);const y=(e,t)=>{const a=document.createElement("link");return a.rel="preload",a.href=e,a.as=t,a};return(0,c.jsxs)(c.Fragment,{children:["",(0,c.jsx)("header",{id:"header",className:"headernav border-gray-500 bg-white text-black w-3/4 mx-auto rounded-3xl mt-3 py-2 z-[8888] ".concat(""),children:(0,c.jsxs)("div",{className:"container mx-auto flex justify-between items-center px-4",children:[(0,c.jsxs)("picture",{className:"aiprologo",children:[(0,c.jsx)("source",{type:"image/webp",srcSet:n,width:"150",height:"52",className:"aiprologo"}),(0,c.jsx)("img",{src:s.Z,alt:"AI-Pro Logo",className:"aiprologo"})]}),(d||m||p)&&x&&(0,c.jsx)("nav",{className:"text-xs lg:text-sm block inline-flex",id:"menu",children:(0,c.jsx)("ul",{className:"headnav flex inline-flex",children:(0,c.jsx)("li",{className:"mr-1 md:mr-2 lg:mr-6",children:(0,c.jsx)("a",{href:o?"/my-account":"/login",className:"font-bold","aria-label":o?"my-account":"login",children:o?"My Apps":"LOG IN"})})})})]})})]})}},60180:(e,t,a)=>{a.r(t),a.d(t,{default:()=>L});var r=a(72791),s=(a(39832),a(19886)),n=a(27250),l=a(56355),c=a(53647),o=a(28891),i=a(74335),d=a(91933),m=a(31243),p=a(34492),u=a(65764),x=a(95828),y=a.n(x),h=(a(92831),a(80184));const b=(0,i.bG)("pricing")?(0,i.bG)("pricing"):"",g=(0,i.bG)("access")?(0,i.bG)("access"):"",f=(0,i.bG)("pmt")?(0,i.bG)("pmt"):"",v=(0,i.bG)("cta_pmt")?(0,i.bG)("cta_pmt"):"";var w=null;async function j(){if(w)return w;const e=(await m.Z.post("".concat("http://localhost:9002/api","/get-plan"),{plan_id:b},{headers:{"content-type":"application/x-www-form-urlencoded"}})).data;return e.success?(w=e.data,e.data):[]}const N=function(){(0,r.useEffect)((()=>{y().options={positionClass:"toast-top-center"}}),[]),(0,r.useEffect)((()=>{let e=(0,i.bG)("threed_error");void 0!==e&&""!==e&&setTimeout((function(){(0,i.I1)("threed_error",""),y().error(e)}),2e3)}),[]);const{data:e}=(0,d.useQuery)("users",j),[t,a]=(0,r.useState)("creditCard"),[x,N]=(0,r.useState)(""),[S,C]=(0,r.useState)(""),[_,L]=(0,r.useState)(""),[k,E]=(0,r.useState)(""),[M,D]=(0,r.useState)(""),[F,I]=(0,r.useState)(""),[U,P]=(0,r.useState)(""),[G,T]=(0,r.useState)(""),[q,A]=(0,r.useState)(!0),B=(0,o.gx)("/register"),O=(0,u.useStripe)();if(void 0===B||!1===B)return;if(q&&"active"===B.status&&"no"===B.expired)return void(window.location.href="/my-account");var J=new Date;J.setTime(J.getTime()+2592e6);var K=new Date,Z=new Date;if(Z.setDate(K.getDate()+30),e&&e.currency&&e.price){var H=e.price;""!==e.trial_price&&(H=e.trial_price),(0,i.I1)("currency",e.currency,{expires:Z,path:"/"}),(0,i.I1)("currency",e.currency,{expires:Z,domain:".ai-pro.org",path:"/"}),(0,i.I1)("amount",H,{expires:Z,path:"/"}),(0,i.I1)("amount",H,{expires:Z,domain:".ai-pro.org",path:"/"})}if("pay2"===f&&e){var V="0";""!==e.trial_days&&e.trial_days>0?""!==e.trial_price&&e.trial_price>=0&&(V=100*e.trial_price):V=100*e.price,setTimeout((function(){var t=(0,p.rZ)(e.currency),a=O.paymentRequest({country:t,currency:e.currency.toLowerCase(),total:{label:e.plan_type,amount:V},requestPayerName:!0,requestPayerEmail:!0});const r=O.elements().create("paymentRequestButton",{paymentRequest:a});(async()=>{const e=await a.canMakePayment();e?(r.mount("#payment-request-button"),e.applePay&&(document.getElementById("payment-option").style.display="block",document.getElementById("other-label").innerHTML="ApplePay"),e.googlePay&&(document.getElementById("payment-option").style.display="block",document.getElementById("other-label").innerHTML="GooglePay")):document.getElementById("payment-option").style.display="none"})(),a.on("paymentmethod",(async e=>{const t=e.paymentMethod.id,a=e.paymentMethod.payerEmail,r=e.paymentMethod.payerName;let s="",n=document.getElementsByName("referral");n[0]&&(s=n[0].value),document.querySelector(".loader-container").classList.add("active"),m.Z.post("".concat("http://localhost:9002/api","/t/create-subscription-stripe-apple-google"),{tk:g,payment_menthod_id:t,plan_id:b,email:a,cus_name:r,client_reference_id:s},{headers:{"content-type":"application/x-www-form-urlencoded"}}).then((function(e){let t=e.data;if(t.success)return y().success("Success"),(0,i.Sz)("pricing"),void(window.location.href="/thankyou/?plan="+w.label.replace(" ","").replace(" ",""));document.querySelector(".loader-container").classList.remove("active"),t.data&&y().error(t.data.msg)})).catch((function(e){e.response&&429===e.response.status&&(document.querySelector(".loader-container").classList.remove("active"),y().error("Sorry, too many requests. Please try again in a bit!"))}))}))}),5e3)}const $=e=>{"creditCard"===e&&(document.getElementById("card-section").style.display="block",document.getElementById("other-section").style.display="none"),"other"===e&&(document.getElementById("card-section").style.display="none",document.getElementById("other-section").style.display="block"),a(e)};return(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(n.default,{auth:B}),e?(0,h.jsx)("div",{className:"Payment bg-gray-100 md:min-h-[90vh] flex StripePaymentForm md:pt-[50px]",children:(0,h.jsx)("div",{className:"container mx-auto py-10",children:(0,h.jsx)("div",{className:"flex flex-col items-center py-10 lg:py-16",children:(0,h.jsxs)("div",{className:"flex flex-wrap md:flex-wrap justify-center w-full",children:[(0,h.jsx)("div",{className:"pay_left px-4 mb-8 w-full md:w-1/2",children:(0,h.jsx)("div",{className:"bg-white rounded-lg shadow-lg overflow-hidden",children:(0,h.jsxs)("div",{className:"px-6 pb-10",children:["pay2"===f?(0,h.jsxs)("div",{id:"payment-option",className:"",children:[(0,h.jsx)("h1",{className:"text-xl font-bold mb-4 mt-4",children:"Choose Payment Method"}),(0,h.jsx)("button",{className:"bg-white border ".concat("creditCard"===t?"text-blue-500":"text-gray-500"," font-bold py-2 px-3 rounded-lg mr-2 sm:py-3 sm:px-6"),onClick:()=>$("creditCard"),children:"Credit Card"}),(0,h.jsx)("button",{className:"bg-white border ".concat("other"===t?"text-blue-500":"text-gray-500"," font-bold py-2 px-3 rounded-lg mr-2 sm:py-3 sm:px-6"),onClick:()=>$("other"),children:(0,h.jsx)("span",{id:"other-label",children:"GooglePay"})})]}):"",(0,h.jsxs)("div",{id:"card-section",className:"card-section",children:[(0,h.jsx)("h2",{className:"text-xl font-bold mb-4 pt-10",children:"Enter Billing Details"}),(0,h.jsxs)("div",{className:"mb-4",children:[(0,h.jsxs)("label",{className:"text-xs block mb-0 mt-1",htmlFor:"name",children:["Name on Card ",(0,h.jsx)("span",{className:"text-red-500",children:"*"}),M&&(0,h.jsx)("span",{className:"text-red-500 text-xs text-left w-full mb-2 inline",children:M})]}),(0,h.jsx)("input",{className:"w-full px-3 py-2 border border-gray-300 rounded fs-exclude",type:"text",id:"name",name:"name",placeholder:"John Doe",value:x,onChange:e=>{let t=e.target.value;t=t.replace(/[^A-Za-z ]/g,""),t=t.slice(0,50),N(t)},onKeyUp:e=>{N(e.target.value)}})]}),(0,h.jsxs)("div",{className:"mb-4",children:[(0,h.jsxs)("label",{className:"text-xs block mb-0 mt-1",htmlFor:"card-number",children:["Card Number ",(0,h.jsx)("span",{className:"text-red-500",children:"*"}),F&&(0,h.jsx)("span",{className:"text-red-500 text-xs text-left w-full mb-2 inline",children:F})]}),(0,h.jsx)("input",{className:"w-full px-3 py-2 border border-gray-300 rounded fs-exclude",type:"text",id:"card-number",name:"card-number",placeholder:"1234 5678 9012 3456",value:S,onChange:e=>{let t=e.target.value;t=t.replace(/\D/g,""),t=t.replace(/-/g,""),t=t.replace(/(\d{4})/g,"$1-"),t=t.replace(/-$/,""),t=t.slice(0,19),C(t)},onKeyUp:e=>{C(e.target.value)}})]}),(0,h.jsxs)("div",{className:"mb-4 flex",children:[(0,h.jsxs)("div",{className:"expdate w-full md:w-2/3 mr-2 md:mr-5",children:[(0,h.jsxs)("label",{className:"text-xs block mb-0 mt-1",htmlFor:"expiration-date",children:["Expiration Date ",(0,h.jsx)("span",{className:"text-red-500",children:"*"}),U&&(0,h.jsx)("span",{className:"text-red-500 text-xs text-left w-full mb-2 inline",children:U})]}),(0,h.jsx)("input",{className:"w-full px-3 py-2 border border-gray-300 rounded fs-exclude",type:"text",id:"expiration-date",name:"expiration-date",placeholder:"MM/YY",value:_,onChange:e=>{let t=e.target.value;t=t.replace(/\D/g,""),t=t.slice(0,4),t.length>=3&&(t=t.slice(0,2)+"/"+t.slice(2)),L(t)},onKeyUp:e=>{L(e.target.value)}})]}),(0,h.jsxs)("div",{className:"cvv w-full md:w-1/3",children:[(0,h.jsxs)("label",{className:"text-xs block mb-0 mt-1",htmlFor:"cvv",children:["CVV ",(0,h.jsx)("span",{className:"text-red-500",children:"*"}),G&&(0,h.jsx)("span",{className:"text-red-500 text-xs text-left w-full mb-2 inline",children:G})]}),(0,h.jsx)("input",{className:"w-full px-3 py-2 border border-gray-300 rounded fs-exclude",type:"text",id:"cvv",name:"cvv",placeholder:"CVV",value:k,onChange:e=>{let t=e.target.value;t=t.replace(/\D/g,""),t=t.slice(0,5),E(t)},onKeyUp:e=>{E(e.target.value)}})]})]}),(0,h.jsx)(s.E.button,{className:"bg-blue-500 text-white font-bold py-3 px-6 rounded-lg w-full my-4 proceed-pmt",whileHover:{backgroundColor:"#5997fd"},whileTap:{scale:.9},onClick:()=>{A(!1),D(""),I(""),P(""),T("");let e=!0,t="",a=document.getElementsByName("referral");a[0]&&(t=a[0].value),x.includes(" ")||(D("enter at least two names separated by a space"),e=!1),S?/^\d{4}(-\d{4}){3}$/.test(S)||(I("Invalid CC"),e=!1):(I("required"),e=!1),_&&/^(0[1-9]|1[0-2])\/\d{2}$/.test(_)||(P("MM/YY"),e=!1),k&&/^\d{3,5}$/.test(k)||(T("required"),e=!1);var r=x.split(" "),s=r[0],n=r[r.length-1],l=_.split("/")[0],c=_.split("/")[1];""===s&&""===n?(D("required"),e=!1):""!==s&&""!==n||(D("enter at least two names separated by a space"),e=!1),e&&(document.querySelector(".loader-container").classList.add("active"),m.Z.post("".concat("http://localhost:9002/api","/t/create-subscription-stripe"),{tk:g,first_name:s,last_name:n,cc:S,ccmonth:l,ccyr:"20"+c,cvv:k,plan_id:b,client_reference_id:t},{headers:{"content-type":"application/x-www-form-urlencoded"}}).then((function(e){let t=e.data;if(t.success)return t.redirect&&""!==t.redirect?void(window.location.href=t.redirect):(y().success("Success"),(0,i.Sz)("pricing"),void(window.location.href="/thankyou/?plan="+w.label.replace(" ","").replace(" ","")));document.querySelector(".loader-container").classList.remove("active"),t.data&&y().error(t.data.msg)})).catch((function(e){e.response&&429===e.response.status&&(document.querySelector(".loader-container").classList.remove("active"),y().error("Sorry, too many requests. Please try again in a bit!"))})))},children:v||"Complete Purchase"})]}),(0,h.jsx)("div",{id:"other-section",className:"other-section p-8",children:(0,h.jsx)("div",{className:"mt-10 mb-10",id:"payment-request-button"})}),(0,h.jsxs)("span",{className:"text-[12px] text-gray-600",children:[(0,h.jsx)(l.DAO,{className:"inline text-lg mr-1"})," By clicking the “",v||"Complete Purchase","” button, I have read and agreed to the Terms and Conditions."]})]})})}),(0,h.jsxs)("div",{className:"pay_right px-4 mb-8 md:w-2/5",children:[(0,h.jsxs)("div",{className:"border px-8 rounded border-gray-300",children:[(0,h.jsx)("h2",{className:"text-xl font-bold mb-4 pt-10 pb-0",children:"Order Summary"}),(0,h.jsxs)("div",{className:"py-5",children:[(0,h.jsx)("div",{className:"mb-2 text-sm pb-4 border-b border-gray-300",children:(0,h.jsx)("b",{className:"text-lg text-uppercase",children:e.plan_type_display})}),(0,h.jsxs)("div",{className:"flex flex-wrap mb-2 text-sm mt-4 mr-6",children:[(0,h.jsx)("div",{className:"text-lg font-bold mt-4 w-1/2",children:"TOTAL:"}),(0,h.jsx)("div",{className:"text-lg font-bold mt-4 w-1/2 text-right",children:(0,p.yt)(e.currency,function(e){return e.trial_price?e.trial_price:e.price?e.price:"0"}(e))})]}),(0,h.jsx)("div",{className:"mb-8 text-sm mt-6",children:e.display_txt3?e.display_txt3:"Your subscription will renew monthly until you cancel it."})]})]}),(0,h.jsxs)("div",{className:"securecont block p-5 mx-auto text-left",children:[(0,h.jsxs)("div",{className:"securetext mb-2 text-sm w-full",children:[(0,h.jsx)(l.kUi,{className:"inline text-lg mr-1 text-orange-500 text-xs"})," Secure Checkout"]}),(0,h.jsxs)("div",{className:"securelogo mb-2 text-sm w-full flex flex-wrap justify-center items-center",children:[(0,h.jsx)("img",{src:c,alt:"Secure Logo",className:"cclogo inline"}),(0,h.jsx)("button",{onClick:()=>{window.open("//www.securitymetrics.com/site_certificate?id=1982469&tk=d41c416c6208f1136426bc8038178d2e","Verification","location=no, toolbar=no, resizable=no, scrollbars=yes, directories=no, status=no,top=100,left=100, width=700, height=600")},title:"SecurityMetrics card safe certification",className:"h-20",children:(0,h.jsx)("img",{loading:"lazy",src:"https://www.securitymetrics.com/portal/app/ngsm/assets/img/GreyContent_Credit_Card_Safe_White_Sqr.png",alt:"SecurityMetrics card safe certification logo",className:"max-h-full",width:"80",height:"80"})})]})]})]})]})})})}):""]})};var S=a(53473);var C="";C="test"===((0,i.bG)("mode")?(0,i.bG)("mode"):"")?"pk_test_51MH0TVLtUaKDxQEZH5ODSKmyw5TSm1lEVwyKkhVbNPZCv83lu4xL2aK8NbiJkkeG9XJt6td7kRLET8gpby37dKTs00uLTgkXVr":"pk_live_51MH0TVLtUaKDxQEZNwiJOL1O8aLIA6fQEyI7cqjDnuSnMXwnOjOtu2JHjRD6PhF6hyoQAfM91dxrxNUEdyoCv9m900CPfMnfSk";const _=(0,S.J)(C);const L=function(){return(0,h.jsx)(h.Fragment,{children:(0,h.jsx)(u.Elements,{stripe:_,children:(0,h.jsx)(N,{})})})}},29534:()=>{},39832:()=>{},19878:(e,t,a)=>{e.exports=a.p+"static/media/AIPRO.84104dfd05446283b05c.webp"},53647:(e,t,a)=>{e.exports=a.p+"static/media/cc_v3.6ab0d1e0b9d27a1d2bc0.png"}}]);
//# sourceMappingURL=7670.bcef1ec4.chunk.js.map