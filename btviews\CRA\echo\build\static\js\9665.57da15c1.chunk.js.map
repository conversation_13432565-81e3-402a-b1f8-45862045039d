{"version": 3, "file": "static/js/9665.57da15c1.chunk.js", "mappings": "yIAEA,MAQA,EARkBA,IAA6B,IAA5B,IAAEC,EAAG,IAAEC,KAAQC,GAAOH,EACvC,OACEI,EAAAA,EAAAA,KAACC,EAAAA,SAAQ,CAACC,UAAUF,EAAAA,EAAAA,KAAA,OAAAG,SAAK,eAAiBA,UACxCH,EAAAA,EAAAA,KAAA,OAAKH,IAAKA,EAAKC,IAAKA,KAASC,M,4ICJnC,MAsBA,EAtBoBK,KAEhBC,EAAAA,EAAAA,MAAA,MAAIC,UAAU,kDAAiDH,SAAA,CAAC,8BAChC,KAC5BH,EAAAA,EAAAA,KAAA,OAAKM,UAAU,8DAA6DH,UAC1EE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,+CAA8CH,SAAA,EAC3DH,EAAAA,EAAAA,KAAA,OAAKM,UAAU,uEAAsEH,SAAC,WACtFH,EAAAA,EAAAA,KAAA,OAAKM,UAAU,uEAAsEH,SAAC,YACtFH,EAAAA,EAAAA,KAAA,OAAKM,UAAU,uEAAsEH,SAAC,YACtFH,EAAAA,EAAAA,KAAA,OAAKM,UAAU,uEAAsEH,SAAC,YACtFH,EAAAA,EAAAA,KAAA,OAAKM,UAAU,uEAAsEH,SAAC,WAEtFH,EAAAA,EAAAA,KAAA,OAAKM,UAAU,uEAAsEH,SAAC,WACtFH,EAAAA,EAAAA,KAAA,OAAKM,UAAU,uEAAsEH,SAAC,YACtFH,EAAAA,EAAAA,KAAA,OAAKM,UAAU,uEAAsEH,SAAC,YACtFH,EAAAA,EAAAA,KAAA,OAAKM,UAAU,uEAAsEH,SAAC,YACtFH,EAAAA,EAAAA,KAAA,OAAKM,UAAU,uEAAsEH,SAAC,kBCoJlG,QA/JA,SAAeP,GAAyB,IAAxB,kBAAEW,GAAmBX,EAgInC,OAPAY,EAAAA,EAAAA,WAAU,KACR,MAAMC,EAAQC,WAAW,KAvHzBC,OAAOC,YAAY,eAAgB,CACjC,UAAa,CACX,OAAU,CACR,MAAS,IACT,QAAW,CACT,QAAU,EACV,WAAc,MAGlB,MAAS,CACP,MAAS,WAEX,MAAS,CACP,KAAQ,SACR,OAAU,CACR,MAAS,EACT,MAAS,WAEX,QAAW,CACT,SAAY,GAEd,MAAS,CACP,IAAO,iBACP,MAAS,IACT,OAAU,MAGd,QAAW,CACT,MAAS,GACT,QAAU,EACV,KAAQ,CACN,QAAU,EACV,MAAS,EACT,YAAe,GACf,MAAQ,IAGZ,KAAQ,CACN,MAAS,EACT,QAAU,EACV,KAAQ,CACN,QAAU,EACV,MAAS,GACT,SAAY,GACZ,MAAQ,IAGZ,YAAe,CACb,QAAU,EACV,SAAY,IACZ,MAAS,UACT,QAAW,GACX,MAAS,GAEX,KAAQ,CACN,QAAU,EACV,MAAS,EACT,UAAa,OACb,QAAU,EACV,UAAY,EACZ,SAAY,MACZ,QAAW,CACT,QAAU,EACV,QAAW,IACX,QAAW,QAIjB,cAAiB,CACf,UAAa,SACb,OAAU,CACR,QAAW,CACT,QAAU,EACV,KAAQ,UAEV,QAAW,CACT,QAAU,EACV,KAAQ,WAEV,QAAU,GAEZ,MAAS,CACP,KAAQ,CACN,SAAY,IACZ,YAAe,CACb,QAAW,IAGf,OAAU,CACR,SAAY,IACZ,KAAQ,EACR,SAAY,GACZ,QAAW,EACX,MAAS,GAEX,QAAW,CACT,SAAY,KAEd,KAAQ,CACN,aAAgB,GAElB,OAAU,CACR,aAAgB,KAItB,eAAiB,EACjB,YAAe,CACb,WAAa,EACb,iBAAoB,UACpB,iBAAoB,GACpB,oBAAuB,UACvB,kBAAqB,YACrB,gBAAmB,YAQpB,KACH,MAAO,IAAMC,aAAaJ,IACzB,KAGHJ,EAAAA,EAAAA,MAAAS,EAAAA,SAAA,CAAAX,SAAA,EACEH,EAAAA,EAAAA,KAAA,OAAKe,GAAG,kBACRf,EAAAA,EAAAA,KAAA,OAAKM,UAAU,gCAA+BH,UAC9CE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,+DAA8DH,SAAA,EAC3EH,EAAAA,EAAAA,KAACI,EAAW,KACZJ,EAAAA,EAAAA,KAAA,OAAKM,UAAU,iEAAgEH,UAC7EE,EAAAA,EAAAA,MAAA,KAAGC,UAAU,yEAAwEH,SAAA,CAAC,qGACaH,EAAAA,EAAAA,KAAA,UAAKA,EAAAA,EAAAA,KAAA,SAAK,sMACuFA,EAAAA,EAAAA,KAAA,UAAKA,EAAAA,EAAAA,KAAA,SAAK,oKAKhNK,EAAAA,EAAAA,MAAA,UAAQC,UAAU,yEAAyEU,QAAST,EAAkBJ,SAAA,CAAC,gBAAiBH,EAAAA,EAAAA,KAAA,KAAGM,UAAU,gCAIvJN,EAAAA,EAAAA,KAAA,OAAKM,UAAU,0CAAyCH,UACtDH,EAAAA,EAAAA,KAAA,OAAKM,UAAU,4BAA2BH,UACxCH,EAAAA,EAAAA,KAAA,OAAKM,UAAU,wBAAuBH,UACpCH,EAAAA,EAAAA,KAAA,OAAKM,UAAU,cAAaH,UAC5BH,EAAAA,EAAAA,KAACiB,EAAAA,EAAS,CAACpB,IAAKqB,EAAYpB,IAAI,QAAQQ,UAAU,gDAO5D,EC5JMa,EAAWC,EAAAA,KAAW,IAAM,iCAC5BC,EAAUD,EAAAA,KAAW,IAAM,iCAC3BE,EAASF,EAAAA,KAAW,IAAM,iCAkEhC,QAhEA,WACE,MAAMG,GAAOC,EAAAA,EAAAA,MACPC,GAAcC,EAAAA,EAAAA,QAAO,MACrBC,GAAaD,EAAAA,EAAAA,QAAO,MACpBE,GAAYF,EAAAA,EAAAA,QAAO,OAClBG,EAAQC,IAAaC,EAAAA,EAAAA,WAAS,GAyBrC,IAZAvB,EAAAA,EAAAA,WAAU,KACR,MAAMwB,EAASC,SAASC,cAAc,UACtCF,EAAOnC,IAAMsC,6CACbH,EAAOI,iBAAiB,OAAQ,IAAIN,GAAU,IAC9CG,SAASI,KAAKC,YAAYN,IACzB,KAEHxB,EAAAA,EAAAA,WAAU,KACJqB,GAnBiBU,MACrB,GAAI5B,OAAO6B,qBAAsB,CAC/B,MAAMC,EAAa,CACjBC,MAAOP,wBAETxB,OAAO6B,qBAAqBC,EAC9B,MACEE,QAAQC,MAAM,yCAahBL,IACC,CAACV,SAEQgB,IAATtB,EAAoB,OACvB,MAAMhB,EAAoBA,KACnBgB,EAEMA,GAAwB,WAAhBA,EAAKuB,OACtBnC,OAAOoC,SAASC,KAAO,cAEvBrC,OAAOoC,SAASC,KAAO,WAJvBrC,OAAOoC,SAASC,KAAO,kBAQ3B,OACE3C,EAAAA,EAAAA,MAAAS,EAAAA,SAAA,CAAAX,SAAA,EACEE,EAAAA,EAAAA,MAAC4C,EAAAA,EAAM,CAAA9C,SAAA,EACLH,EAAAA,EAAAA,KAAA,SAAAG,SAAO,mBACPH,EAAAA,EAAAA,KAAA,QAAMkD,KAAK,cAAcC,QAAQ,kPAEnCnD,EAAAA,EAAAA,KAACoD,EAAAA,QAAM,CAAC7B,KAAMA,KACZlB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,cAAaH,SAAA,EAC1BH,EAAAA,EAAAA,KAACqD,EAAU,CAAC9C,kBAAmBA,KAC/BF,EAAAA,EAAAA,MAACJ,EAAAA,SAAQ,CAACC,SAAU,KAAKC,SAAA,EACvBH,EAAAA,EAAAA,KAAA,OAAKsD,IAAK7B,EAAaV,GAAG,WAAUZ,UAClCH,EAAAA,EAAAA,KAACmB,EAAQ,CAACZ,kBAAmBA,OAE/BP,EAAAA,EAAAA,KAAA,OAAKsD,IAAK3B,EAAYZ,GAAG,UAASZ,UAChCH,EAAAA,EAAAA,KAACqB,EAAO,OAEVrB,EAAAA,EAAAA,KAACsB,EAAM,CAACC,KAAMA,EAAM+B,IAAK1B,YAKrC,C", "sources": ["LazyImage.jsx", "home/rotatingLLM.jsx", "home/banner.jsx", "home/index.jsx"], "sourcesContent": ["import React, { Suspense } from 'react';\r\n\r\nconst LazyImage = ({ src, alt, ...props }) => {\r\n  return (\r\n    <Suspense fallback={<div>Loading...</div>}>\r\n      <img src={src} alt={alt} {...props} />\r\n    </Suspense>\r\n  );\r\n};\r\n\r\nexport default LazyImage;\r\n", "import React from \"react\";\r\nconst RotatingLLM = () => {\r\n  return (\r\n    <h1 className=\"text-3xl md:text-5xl font-bold text-center mb-5\">\r\n        Experience the AI Power of {' '}\r\n        <div className=\"inline-block h-[30px] md:h-[48px] overflow-hidden align-top\">\r\n          <div className=\"flex flex-col animate-move_a md:animate-move\">\r\n            <div className=\"text-center min-[375px]:text-left text-blue-600 h-[30px] md:h-[48px]\">GPT-4</div>\r\n            <div className=\"text-center min-[375px]:text-left text-blue-600 h-[30px] md:h-[48px]\">GPT-4o</div>\r\n            <div className=\"text-center min-[375px]:text-left text-blue-600 h-[30px] md:h-[48px]\">Gemini</div>\r\n            <div className=\"text-center min-[375px]:text-left text-blue-600 h-[30px] md:h-[48px]\">Claude</div>\r\n            <div className=\"text-center min-[375px]:text-left text-blue-600 h-[30px] md:h-[48px]\">LLaMA</div>\r\n            {/* Duplicated items for infinite scrolling */}\r\n            <div className=\"text-center min-[375px]:text-left text-blue-600 h-[30px] md:h-[48px]\">GPT-4</div>\r\n            <div className=\"text-center min-[375px]:text-left text-blue-600 h-[30px] md:h-[48px]\">GPT-4o</div>\r\n            <div className=\"text-center min-[375px]:text-left text-blue-600 h-[30px] md:h-[48px]\">Gemini</div>\r\n            <div className=\"text-center min-[375px]:text-left text-blue-600 h-[30px] md:h-[48px]\">Claude</div>\r\n            <div className=\"text-center min-[375px]:text-left text-blue-600 h-[30px] md:h-[48px]\">LLaMA</div>\r\n          </div>\r\n        </div>\r\n      </h1>\r\n  );\r\n};\r\nexport default RotatingLLM;\r\n", "import React, {useEffect} from 'react';\r\nimport chatgptGif from '../assets/images/chatfirst.png';\r\nimport LazyImage from '../LazyImage';\r\nimport \"particles.js\";\r\nimport RotatingLLM from './rotatingLLM';\r\n\r\nfunction Banner({ checkSubscription }) {\r\n\r\n  function startParticle() {\r\n    window.particlesJS('particles-js', {\r\n      \"particles\": {\r\n        \"number\": {\r\n          \"value\": 150,\r\n          \"density\": {\r\n            \"enable\": true,\r\n            \"value_area\": 800\r\n          }\r\n        },\r\n        \"color\": {\r\n          \"value\": \"#8bcaff\"\r\n        },\r\n        \"shape\": {\r\n          \"type\": \"circle\",\r\n          \"stroke\": {\r\n            \"width\": 0,\r\n            \"color\": \"#F3F4F6\"\r\n          },\r\n          \"polygon\": {\r\n            \"nb_sides\": 3\r\n          },\r\n          \"image\": {\r\n            \"src\": \"img/github.svg\",\r\n            \"width\": 100,\r\n            \"height\": 100\r\n          }\r\n        },\r\n        \"opacity\": {\r\n          \"value\": 0.5,\r\n          \"random\": false,\r\n          \"anim\": {\r\n            \"enable\": false,\r\n            \"speed\": 1,\r\n            \"opacity_min\": 0.1,\r\n            \"sync\": false\r\n          }\r\n        },\r\n        \"size\": {\r\n          \"value\": 5,\r\n          \"random\": true,\r\n          \"anim\": {\r\n            \"enable\": false,\r\n            \"speed\": 40,\r\n            \"size_min\": 0.1,\r\n            \"sync\": false\r\n          }\r\n        },\r\n        \"line_linked\": {\r\n          \"enable\": true,\r\n          \"distance\": 150,\r\n          \"color\": \"#3ba5ff\",\r\n          \"opacity\": 0.2,\r\n          \"width\": 1\r\n        },\r\n        \"move\": {\r\n          \"enable\": true,\r\n          \"speed\": 2,\r\n          \"direction\": \"none\",\r\n          \"random\": false,\r\n          \"straight\": false,\r\n          \"out_mode\": \"out\",\r\n          \"attract\": {\r\n            \"enable\": false,\r\n            \"rotateX\": 600,\r\n            \"rotateY\": 1200\r\n          }\r\n        }\r\n      },\r\n      \"interactivity\": {\r\n        \"detect_on\": \"canvas\",\r\n        \"events\": {\r\n          \"onhover\": {\r\n            \"enable\": true,\r\n            \"mode\": \"bubble\"\r\n          },\r\n          \"onclick\": {\r\n            \"enable\": true,\r\n            \"mode\": \"repulse\"\r\n          },\r\n          \"resize\": true\r\n        },\r\n        \"modes\": {\r\n          \"grab\": {\r\n            \"distance\": 100,\r\n            \"line_linked\": {\r\n              \"opacity\": 1\r\n            }\r\n          },\r\n          \"bubble\": {\r\n            \"distance\": 100,\r\n            \"size\": 6,\r\n            \"duration\": 10,\r\n            \"opacity\": 1,\r\n            \"speed\": 1\r\n          },\r\n          \"repulse\": {\r\n            \"distance\": 100\r\n          },\r\n          \"push\": {\r\n            \"particles_nb\": 4\r\n          },\r\n          \"remove\": {\r\n            \"particles_nb\": 2\r\n          }\r\n        }\r\n      },\r\n      \"retina_detect\": true,\r\n      \"config_demo\": {\r\n        \"hide_card\": false,\r\n        \"background_color\": \"#b61924\",\r\n        \"background_image\": \"\",\r\n        \"background_position\": \"50% 50%\",\r\n        \"background_repeat\": \"no-repeat\",\r\n        \"background_size\": \"cover\"\r\n      }\r\n    });\r\n  }\r\n\r\n  useEffect(() => {\r\n    const timer = setTimeout(() => {\r\n      startParticle();\r\n    }, 3000);\r\n    return () => clearTimeout(timer);\r\n  }, []);\r\n\r\n  return (\r\n  <>\r\n    <div id=\"particles-js\"></div>\r\n    <div className=\"intro container mx-auto py-10\">\r\n    <div className=\"flex flex-col items-center pt-16 pb-16 lg:p-[100px] lg:pb-16\">\r\n      <RotatingLLM />\r\n      <div className=\"min-h-[195px] md:min-h-[136px] lg:min-h-[280px] mb-10 lg:mb-12\">\r\n        <p className=\"text-shadow-lg text-xs lg:text-lg max-w-xl text-center leading-relaxed\">\r\n          Access the Latest AI Application: ChatBot Pro powered applications, Text to Image, and much more.<br/><br/>\r\n          Unlock the power of Artificial Intelligence and step into a world of endless possibilities. AI-Pro is your gateway to the latest AI applications, revolutionizing the way we live, work, and play.<br/><br/>\r\n          Whether you're a seasoned AI enthusiast or just curious about this rapidly evolving field, we have everything you need to harness the full potential of AI.\r\n        </p>\r\n      </div>\r\n\r\n      <button className=\"ctabtn gradient-hover-effect text-white font-bold py-3 px-6 rounded-lg\" onClick={checkSubscription}>DISCOVER AI&nbsp;<i className=\"fas fa-sign-in-alt\"></i></button>\r\n    </div>\r\n    </div>\r\n\r\n    <div className=\"demogif container mx-auto min-h-[426px]\">\r\n      <div className=\"lg:flex lg:justify-center\">\r\n        <div className=\"lg:w-1/3 custom-class\">\r\n          <div className=\"text-center\">\r\n          <LazyImage src={chatgptGif} alt=\"img28\" className=\"w-full object-cover object-center\"/>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    </>\r\n  );\r\n}\r\n\r\nexport default Banner;\r\n", "import React, { useEffect, Suspense, useRef, useState } from 'react';\r\nimport './style.css';\r\nimport Header from '../header';\r\nimport { Auth } from '../core/utils/auth';\r\nimport { Helmet } from 'react-helmet';\r\n\r\nimport FirstPaint from './banner';\r\nconst Features = React.lazy(() => import('./features'));\r\nconst Powered = React.lazy(() => import('./powered'));\r\nconst Footer = React.lazy(() => import('../footer'));\r\n\r\nfunction Home() {\r\n  const auth = Auth();\r\n  const featuresRef = useRef(null);\r\n  const poweredRef = useRef(null);\r\n  const footerRef = useRef(null);\r\n  const [loaded, setLoaded] = useState(false);\r\n\r\n  const initializeChat = () => {\r\n    if (window.initializeChatWidget) {\r\n      const chatConfig = {\r\n        token: process.env.REACT_APP_BOT_TOKEN // Assuming bot_token is available as an environment variable\r\n      };\r\n      window.initializeChatWidget(chatConfig);\r\n    } else {\r\n      console.error('initializeChatWidget is not defined.');\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    const script = document.createElement('script');\r\n    script.src = process.env.REACT_APP_CHATHEAD_URL;\r\n    script.addEventListener('load', ()=>setLoaded(true));\r\n    document.head.appendChild(script);\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    if(!loaded) return;\r\n    initializeChat();\r\n  }, [loaded]);\r\n\r\n  if(auth === undefined) return;\r\n  const checkSubscription = () => {\r\n    if(!(auth)){\r\n      window.location.href = '/register-auth';\r\n    } else if (auth && auth.status === 'active') {\r\n      window.location.href = '/my-account';\r\n    } else {\r\n      window.location.href = '/pricing';\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <Helmet>\r\n        <title>AI Pro | Home</title>\r\n        <meta name=\"description\" content=\"Join AI-PRO and take your first steps in the exciting world of AI. Our comprehensive collection of resources and expert insights can help you understand and master the latest trends and technologies, and achieve success in the field.\" />\r\n      </Helmet>\r\n      <Header auth={auth}/>\r\n        <div className=\"bg-gray-100\">\r\n          <FirstPaint checkSubscription={checkSubscription} />\r\n          <Suspense fallback={null}>\r\n            <div ref={featuresRef} id=\"features\">\r\n              <Features checkSubscription={checkSubscription} />\r\n            </div>\r\n            <div ref={poweredRef} id=\"powered\">\r\n              <Powered />\r\n            </div>\r\n            <Footer auth={auth} ref={footerRef} />\r\n          </Suspense>\r\n        </div>\r\n    </>\r\n  );\r\n}\r\n\r\nexport default Home;\r\n"], "names": ["_ref", "src", "alt", "props", "_jsx", "Suspense", "fallback", "children", "RotatingLLM", "_jsxs", "className", "checkSubscription", "useEffect", "timer", "setTimeout", "window", "particlesJS", "clearTimeout", "_Fragment", "id", "onClick", "LazyImage", "chatgptGif", "Features", "React", "Powered", "Footer", "auth", "<PERSON><PERSON>", "featuresRef", "useRef", "poweredRef", "footerRef", "loaded", "setLoaded", "useState", "script", "document", "createElement", "process", "addEventListener", "head", "append<PERSON><PERSON><PERSON>", "initializeChat", "initializeChatWidget", "chatConfig", "token", "console", "error", "undefined", "status", "location", "href", "<PERSON><PERSON><PERSON>", "name", "content", "Header", "FirstPaint", "ref"], "sourceRoot": ""}