import React, { useState, useEffect } from 'react';
import HeaderA from './components/Header-a';
import Navigation from './components/Navigation';
import ToolsSection from './components/ToolsSection';
// import EmptyState from './components/EmptyState';
import PopularModels from './components/Header-b';
import { getToolsData } from './data/toolsData';
import Header from '../header';
import Footer from '../footer';
import MostPopular from './components/MostPopular';
import bg from './assets/bg_a.png';
import { Auth, checkUsage } from '../core/utils/auth';
import { Helmet } from 'react-helmet';
import { GetCookie, SetCookie, RemoveCookie } from '../core/utils/cookies';
import axios from 'axios';
import { AddMoreMemberModal } from '../modal/enterprise';
import { MemberCompletePurchase } from '../modal/enterprise';
import { returnAppHref, redirectApp } from '../core/utils/app';

const api_url = process.env.REACT_APP_API_URL || "https://start.ai-pro.org/api";

async function checkBanner() {
  const response = await axios.post(`${api_url}/get-banner`, {
  }, { headers: { 'content-type': 'application/x-www-form-urlencoded' } })

  const output = response.data;
  if (output.success) {
    const banner_data = output.data[0].banner_content;
    return banner_data;
  } else {
    return '';
  }
}

const MyAccountB = () => {
  const auth = Auth();
  const upid = auth?.user_pid ? `?upid=${auth.user_pid}` : '';
  const toolsData = getToolsData(auth);
  const [usage, setUsage] = useState({});

  const [downAppBanner, setDownAppBanner] = useState(false);

  const [showAddMoreMember, setshowAddMoreMember] = useState(false);
  const [showCompletePurchase, setShowCompletePurchase] = useState(false);
  const [moreToAddMember, setMoreToAddMember] = useState("1");
  const [moreToAddMemberTotalAmount, setMoreToAddMemberTotalAmount] = useState("0");
  const [searchQuery, setSearchQuery] = useState('');
  const [activeCategory, setActiveCategory] = useState('popular');
  const [isLoading, setIsLoading] = useState(true);
  const [showPaused, setShowPaused] = useState(false);
  const [showTokenMaxoutWarning, setshowTokenMaxoutWarning] = useState(current => false);
  const [showTokenMaxoutFinal, setshowTokenMaxoutFinal] = useState(current => false);
  const [ShowUpgrade, setShowUpgrade] = useState(false);
  const [ShowExpiredEnterprise, setShowExpiredEnterprise] = useState(false);
  const [entParentUserID, setentParentUserID] = useState('');
  const [isEnterprise, setIsEnterprise] = useState(false);
  const [loaded, setLoaded] = useState(false);


  const initializeChat = () => {
    if (window.initializeChatWidget) {
      const chatConfig = {
        token: process.env.REACT_APP_BOT_TOKEN // Assuming bot_token is available as an environment variable
      };
      window.initializeChatWidget(chatConfig);
    } else {
      console.error('initializeChatWidget is not defined.');
    }
  };

  useEffect(() => {
    const script = document.createElement('script');
    script.src = process.env.REACT_APP_CHATHEAD_URL;
    script.addEventListener('load', ()=>setLoaded(true));
    document.head.appendChild(script);
  }, []);

  useEffect(() => {
    if(!loaded) return;
    initializeChat();
  }, [loaded]);

  const fnRedirectApp = (url) => {
    redirectApp(auth, url);
  };

  const fnReturnAppHref = (url) => {
    return returnAppHref(auth, url);
  };

  useEffect(() => {
    if (auth) {
      if (auth.expired === 'yes') {
        setShowUpgrade(true);
        if (auth.plan === 'enterprise') {
          setShowExpiredEnterprise(true);
          setentParentUserID(auth.ent_parent_user_id);
        }
      } else if (auth.plan === 'enterprise') {
        setIsEnterprise(true);
      }
    }
  }, [auth]);

  

  useEffect(() => {
    const timer = setTimeout(() => setIsLoading(false), 500);
    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    async function fetchBanner() {
      const fetchedBanner = await checkBanner();
      if (fetchedBanner !== undefined) {
        setDownAppBanner(fetchedBanner);
      }
    }
    fetchBanner();
  }, []);

  useEffect(() => {
    if (
      auth !== undefined &&
      auth !== false
    ) {
      if (auth?.status === 'paused') {
        setShowPaused(true);
      }
    }
  }, [auth]);

  useEffect(() => {
    let max_token = usage.max_tokens;
    let total_usage = usage.total_usage;
    let eightyPercentOfToken = (max_token * 0.8);
    //
    setshowTokenMaxoutWarning(false);
    setshowTokenMaxoutFinal(false);

    if (max_token <= total_usage) {
      setshowTokenMaxoutFinal(true);
    } else if (eightyPercentOfToken <= total_usage) {
      setshowTokenMaxoutWarning(true);
    }
  }, [usage]);

  useEffect(() => {
    async function getUserUsage() {
      const data = await checkUsage();
      setUsage(data);
    }
    getUserUsage();
  }, []);
  

  useEffect(() => {
    setTimeout(() => {
      const isPro = GetCookie('user_plan');
      const avatarMaker = GetCookie('avatarmaker');
      const appUrl = GetCookie('appurl');

      if (appUrl && (avatarMaker && appUrl.includes('avatar') && isPro === 'pro')) {
        axios.post(`${api_url}/get-avatar`, {
          folder: avatarMaker
        }, { headers: { 'content-type': 'application/x-www-form-urlencoded' } }).then((res) => {
          const data = res.data;
          if (data.success) {
            const url = data.link;
            if (url !== '') {
              fetch(url)
                .then(response => response.blob())
                .then(blob => {
                  const blobUrl = URL.createObjectURL(blob);
                  const link = document.createElement('a');
                  link.href = blobUrl;
                  link.setAttribute('download', 'highresimage.png');
                  document.body.appendChild(link);
                  link.click();
                  document.body.removeChild(link);
                });
            }
          }
          RemoveCookie('avatarmaker', { domain: '.ai-pro.org', path: '/' });
        });
      }
    }, 500);
  }, [])

  const modifiedToolsData = toolsData.map(tool => ({
    ...tool,
    link: tool.link + upid,
  }));

  const filteredTools = modifiedToolsData.filter(tool => {
    const matchesSearch = tool.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      tool.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = activeCategory === 'all' ||
      (activeCategory === 'popular' && tool.isPopular) ||
      tool.category === activeCategory;
    return matchesSearch && matchesCategory;
  });

  const chatbotTools = filteredTools.filter(tool => tool.category === 'chatbots');
  const artTools = filteredTools.filter(tool => tool.category === 'arts');

  const handleResumeClick = () => {
    window.location.href = '/resume';
  }

  const handleUpgradeClick = () => {
    const DEFAULT_PPG = process.env.REACT_APP_DEFAULT_PPG ? process.env.REACT_APP_DEFAULT_PPG : "14";
    const currency = auth.currency.toLowerCase();

    SetCookie('reactivateSubscription', "reactivateSubscription");
    SetCookie('reactivateSubscription', "reactivateSubscription", { path: '/' });
    SetCookie('reactivateSubscription', "reactivateSubscription", { path: 'ai-pro.org' });
    SetCookie('reactivateSubscription', "reactivateSubscription", { path: '.ai-pro.org' });

    const emailid = process.env.REACT_APP_EMAILID || "comeback5sys";
    SetCookie('emailid', emailid);
    SetCookie('emailid', emailid, { path: '/' });
    SetCookie('emailid', emailid, { path: 'ai-pro.org' });
    SetCookie('emailid', emailid, { path: '.ai-pro.org' });
    RemoveCookie("daily");

    if (currency === 'gbp') {
      window.location.href = '/pricing/?ppg=24&pmt=st';
    } else if (currency === 'eur') {
      window.location.href = '/pricing/?ppg=26&pmt=st';
    } else if (currency === 'brl') {
      window.location.href = '/pricing/?ppg=28&pmt=st';
    } else if (currency === 'sar') {
      window.location.href = '/pricing/?ppg=30&pmt=st';
    } else if (currency === 'aed') {
      window.location.href = '/pricing/?ppg=32&pmt=st';
    } else if (currency === 'pln') {
      window.location.href = '/pricing/?ppg=73&pmt=st';
    } else if (currency === 'ron') {
      window.location.href = '/pricing/?ppg=76&pmt=st';
    } else if (currency === 'czk') {
      window.location.href = '/pricing/?ppg=79&pmt=st';
    } else if (currency === 'huf') {
      window.location.href = '/pricing/?ppg=82&pmt=st';
    } else if (currency === 'dkk') {
      window.location.href = '/pricing/?ppg=85&pmt=st';
    } else if (currency === 'bgn') {
      window.location.href = '/pricing/?ppg=88&pmt=st';
    } else {
      window.location.href = '/pricing/?ppg=' + DEFAULT_PPG;
    }
    return;
  };

  const isProMax = () => {
    if (auth && auth.plan === 'promax' && auth.expired === 'no' && auth.status === 'active') return true;
    return false;
  }

  const isEnterpriseCluter = () => {
    if (auth && auth.plan === 'enterprise' && auth.plan_name?.toLowerCase() !== 'enterprise' && auth.expired === 'no' && auth.status === 'active') return true;
    return false;
  }

  const warningBanner = () => {
        return <svg width="25" height="22" viewBox="0 0 25 22" fill="none" xmlns="http://www.w3.org/2000/svg" className="flex-shrink-0">      <rect x="10" y="6" width="5" height="15" fill="black" />      <path d="M3.43665 22C1.90318 22 0.940189 20.3453 1.69774 19.012L10.7611 3.06049C11.5277 1.71118 13.4723 1.71118 14.2389 3.06049L23.3023 19.012C24.0598 20.3453 23.0968 22 21.5633 22H3.43665ZM12.5 19.4696C12.9855 19.4696 13.4003 19.2914 13.7445 18.9349C14.0948 18.5721 14.2699 18.1392 14.2699 17.6363C14.2699 17.1334 14.0948 16.7037 13.7445 16.3472C13.4003 15.9907 12.9855 15.8125 12.5 15.8125C12.0145 15.8125 11.5966 15.9907 11.2463 16.3472C10.9022 16.7037 10.7301 17.1334 10.7301 17.6363C10.7301 18.1392 10.9022 18.5721 11.2463 18.9349C11.5966 19.2914 12.0145 19.4696 12.5 19.4696ZM11.1799 12.4789C11.1963 13.1962 11.7825 13.7691 12.5 13.7691C13.2175 13.7691 13.8037 13.1962 13.8201 12.4789L13.9051 8.77101C13.9232 7.98239 13.2888 7.33333 12.5 7.33333C11.7112 7.33333 11.0768 7.98239 11.0949 8.77101L11.1799 12.4789Z" fill="#FFB02E" />    </svg>;
  }

  return (
    <div style={{ display: 'flex', flexDirection: 'column', minHeight: '100vh' }}>
      <Helmet>
        <title>AI Pro | Member's Area</title>
        <meta name="description" content="Welcome to your Dashboard, your central hub to access key information and manage all your activities in one place. Stay organized and in control." />
        {process.env.REACT_APP_CONSORTIUM_EMAILS && auth?.email && !process.env.REACT_APP_CONSORTIUM_EMAILS.includes(auth.email) && (
          <script type="text/javascript" src="//widget.trustpilot.com/bootstrap/v5/tp.widget.bootstrap.min.js" async></script>
        )}
      </Helmet>
      <Header auth={auth} setshowAddMoreMember={setshowAddMoreMember} />
      <div className="relative z-10 w-full">
        {showPaused === true ?
          <div className='px-4'>
            <div className="sticky border border-[#FFA500] rounded-[10px] p-6 flex flex-col md:flex-row items-center justify-between bg-[#FFF4E5] mt-[125px] mx-auto w-full md:w-fit z-50 mb-[-50px] max-w-[1230px]">
              <div className='relative w-full md:w-auto'>
                <div className="flex flex-col md:flex-row items-center text-center md:text-left gap-4">
                  <div className="min-w-[20px] flex items-center justify-center">
                    {warningBanner()}
                  </div>
                  <div className='flex-1'>
                    <div className="font-bold text-gray-800">Resume Your Subscription</div>
                    <div className="text-gray-600 text-sm">Account Update: Your subscription is currently Paused until {auth?.resumed_at}. Resume Now and enjoy uninterrupted access!</div>
                  </div>
                  <button className="bg-blue-500 text-white px-4 py-1 rounded-md hover:bg-blue-600 cursor-pointer mt-1" onClick={handleResumeClick}>
                    Resume
                  </button>
                </div>
              </div>
              <button className="text-gray-500/50 hover:text-gray-700 absolute top-2 right-2 md:top-0 md:right-[5px]" onClick={() => setShowPaused(false)}>
                <span className="text-xl">&times;</span>
              </button>
            </div>
          </div>
          : ""}
        {(ShowUpgrade && !ShowExpiredEnterprise && !showPaused) ?
          <div className='px-4'>
            <div className="sticky border border-[#FFA500] rounded-[10px] p-6 flex flex-col md:flex-row items-center justify-between bg-[#FFF4E5] mt-[125px] mx-auto w-full md:w-fit z-50 mb-[-50px] max-w-[1230px]">
              <div className='relative w-full md:w-auto'>
                <div className="flex flex-col md:flex-row items-center text-center md:text-left gap-4">
                  <div className="min-w-[20px] flex items-center justify-center">
                    {warningBanner()}
                  </div>
                  <div className='flex-1'>
                    <div className="font-bold text-gray-800">Renew Your Subscription</div>
                    <div className="text-gray-600 text-sm">Your subscription has ended. Renew now to restore full access to your account.</div>
                  </div>
                  <button className="bg-blue-500 text-white px-4 py-1 rounded-md hover:bg-blue-600 cursor-pointer mt-1" onClick={handleUpgradeClick}>
                    Reactivate
                  </button>
                </div>
              </div>
              <button className="text-gray-500/50 hover:text-gray-700 absolute top-2 right-2 md:top-0 md:right-[5px]" onClick={() => setShowUpgrade(false)}>
                <span className="text-xl">&times;</span>
              </button>
            </div>
          </div>
          : (ShowUpgrade === true && ShowExpiredEnterprise === true && showPaused===false && entParentUserID !== '') ?
            <div className='px-4'>
              <div className="sticky border border-[#FFA500] rounded-[10px] p-6 flex flex-col md:flex-row items-center justify-between bg-[#FFF4E5] mt-[125px] mx-auto w-full md:w-fit z-50 mb-[-50px] max-w-[1230px]">
                <div className='relative w-full md:w-auto'>
                  <div className="flex flex-col md:flex-row items-center text-center md:text-left gap-4">
                    <div className="min-w-[20px] flex items-center justify-center">
                      {warningBanner()}
                    </div>
                    <div className='flex-1'>
                      <div className="font-bold text-gray-800">Important Account Update:</div>
                      <div className="text-gray-600 text-sm">Your subscription is either expired or currently inactive. For continuous access, kindly reach out to our <a href="https://ai-pro.org/contact-us/" rel="noreferrer" target='_blank' className='underline text-blue-500'><span className="pointer">support team</span></a>.</div>
                    </div>
                  </div>
                </div>
                <button className="text-gray-500/50 hover:text-gray-700 absolute top-2 right-2 md:top-0 md:right-[5px]" onClick={() => { setShowExpiredEnterprise(false); setShowUpgrade(false); }}>
                  <span className="text-xl">&times;</span>
                </button>
              </div>
            </div>
             : (ShowUpgrade === true && ShowExpiredEnterprise === true && entParentUserID === '') ?
              <div className='px-4'>
                <div className="sticky border border-[#FFA500] rounded-[10px] p-6 flex flex-col md:flex-row items-center justify-between bg-[#FFF4E5] mt-[125px] mx-auto w-full md:w-fit z-50 mb-[-50px] max-w-[1230px]">
                  <div className='relative w-full md:w-auto'>
                    <div className="flex flex-col md:flex-row items-center text-center md:text-left gap-4">
                      <div className="min-w-[20px] flex items-center justify-center">
                        {warningBanner()}
                      </div>
                      <div className='flex-1'>
                        <span>
                          Important Account Update: Your subscription Has Expired or Is Currently Inactive. Please contact your account administrator for uninterrupted access.
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
                <button className="text-gray-500/50 hover:text-gray-700 absolute top-2 right-2 md:top-0 md:right-[5px]" onClick={() => { setShowExpiredEnterprise(false); setShowUpgrade(false); }}>
                  <span className="text-xl">&times;</span>
                </button>
              </div>
              : (auth && auth.status === 'active' && !isProMax() && !isEnterpriseCluter()) ? (
                <>
                  {isEnterprise ? (
                    <>
                      {showTokenMaxoutFinal ? (
                        <div className='px-4'>
                          <div className="sticky border border-[#FFA500] rounded-[10px] p-6 flex flex-col md:flex-row items-center justify-between bg-[#FFF4E5] mt-[125px] mx-auto w-full md:w-fit z-50 mb-[-50px] max-w-[1230px]">
                            <div className='relative w-full md:w-auto'>
                              <div className="flex flex-col md:flex-row items-center text-center md:text-left gap-4">
                                <div className="min-w-[20px] flex items-center justify-center">
                                  {warningBanner()}
                                </div>
                                <div className='flex-1'>
                                  <div className="font-bold text-gray-800">Token Limit Exceeded:</div>
                                  <div className="text-gray-600 text-sm">You have reached your token limit for this month.&nbsp;
                                    <a href="https://ai-pro.org/contact-us/" className="text-[#3b82f6] underline"><strong>CONTACT US</strong></a> to continue accessing and enjoying our services without interruption.</div>
                                </div>
                              </div>
                            </div>
                            <button className="text-gray-500/50 hover:text-gray-700 absolute top-2 right-2 md:top-0 md:right-[5px]" onClick={() => setshowTokenMaxoutFinal(false)}>
                              <span className="text-xl">&times;</span>
                            </button>
                          </div>
                        </div>
                      ) : showTokenMaxoutWarning ? (
                        <div className='px-4'>
                          <div className="sticky border border-[#FFA500] rounded-[10px] p-6 flex flex-col md:flex-row items-center justify-between bg-[#FFF4E5] mt-[125px] mx-auto w-full md:w-fit z-50 mb-[-50px] max-w-[1230px]">
                            <div className='relative w-full md:w-auto'>
                              <div className="flex flex-col md:flex-row items-center text-center md:text-left gap-4">
                                <div className="min-w-[20px] flex items-center justify-center">
                                  {warningBanner()}
                                </div>
                                <div className='flex-1'>
                                  <div className="font-bold text-gray-800">Token Limit Warning:</div>
                                  <div className="text-gray-600 text-sm">You're just a few tokens away from reaching you limit for this month.&nbsp;
                                    <a href="https://ai-pro.org/contact-us/" className="text-[#3b82f6] underline"><strong>CONTACT US</strong></a> to continue accessing and enjoying our services without interruption.</div>
                                </div>
                              </div>
                            </div>
                            <button className="text-gray-500/50 hover:text-gray-700 absolute top-2 right-2 md:top-0 md:right-[5px]" onClick={() => setshowTokenMaxoutWarning(false)}>
                              <span className="text-xl">&times;</span>
                            </button>
                          </div>
                        </div>
                      ) : (
                        <DownAppBannerContainer downAppBanner={downAppBanner} setDownAppBanner={setDownAppBanner} />
                      )}
                    </>
                  ) : (
                    <>
                      {showTokenMaxoutFinal ? (
                        <div className='px-4'>
                          <div className="sticky border border-[#FFA500] rounded-[10px] p-6 flex flex-col md:flex-row items-center justify-between bg-[#FFF4E5] mt-[125px] mx-auto w-full md:w-fit z-50 mb-[-50px] max-w-[1230px]">
                            <div className='relative w-full md:w-auto'>
                              <div className="flex flex-col md:flex-row items-center text-center md:text-left gap-4">
                                <div className="min-w-[20px] flex items-center justify-center">
                                  {warningBanner()}
                                </div>
                                <div className='flex-1'>
                                  <div className="font-bold text-gray-800">Token Limit Exceeded:</div>
                                  <div className="text-gray-600 text-sm">You have reached your token limit for this month.&nbsp;
                                    <a href="/upgrade/?mx=1" className="text-[#3b82f6] underline"><strong>UPGRADE NOW</strong></a> to continue accessing and enjoying our services without interruption.
                                  </div>
                                </div>
                              </div>
                            </div>
                            <button className="text-gray-500/50 hover:text-gray-700 absolute top-2 right-2 md:top-0 md:right-[5px]" onClick={() => setshowTokenMaxoutFinal(false)}>
                              <span className="text-xl">&times;</span>
                            </button>
                          </div>
                        </div>
                      ) : showTokenMaxoutWarning ? (
                        <div className='px-4'>
                          <div className="sticky border border-[#FFA500] rounded-[10px] p-6 flex flex-col md:flex-row items-center justify-between bg-[#FFF4E5] mt-[125px] mx-auto w-full md:w-fit z-50 mb-[-50px] max-w-[1230px]">
                            <div className='relative w-full md:w-auto'>
                              <div className="flex flex-col md:flex-row items-center text-center md:text-left gap-4">
                                <div className="min-w-[20px] flex items-center justify-center">
                                  {warningBanner()}
                                </div>
                                <div className='flex-1'>
                                  <div className="font-bold text-gray-800">Token Limit Warning:</div>
                                  <div className="text-gray-600 text-sm">You're just a few tokens away from reaching you limit for this month.&nbsp;
                                    <a href="/upgrade/?mx=1" className="text-[#3b82f6] underline"><strong>UPGRADE NOW</strong></a> to continue accessing and enjoying our services without interruption.</div>
                                </div>
                              </div>
                            </div>
                            <button className="text-gray-500/50 hover:text-gray-700 absolute top-2 right-2 md:top-0 md:right-[5px]" onClick={() => setshowTokenMaxoutWarning(false)}>
                              <span className="text-xl">&times;</span>
                            </button>
                          </div>
                        </div>
                      ) : (
                        <DownAppBannerContainer downAppBanner={downAppBanner} setDownAppBanner={setDownAppBanner} />
                      )}
                    </>
                  )}
                </>
              ) : null}
        {false && auth.plan && auth.status === 'active' ? <>
          {process.env.REACT_APP_CONSORTIUM_EMAILS && auth.email && !process.env.REACT_APP_CONSORTIUM_EMAILS.includes(auth.email) && (
            <div id="tp-container" className="flex flex-row text-center md:text-left w-[205px] mx-auto md:mx-0 md:mt-[10px]">
              <div className='flex flex-col justify-start'>
                {/* <!-- TrustBox widget - Mini -->  */}
                <div id="tp-star-container">
                  <div class="trustpilot-widget" data-locale="en-US" data-template-id="53aa8807dec7e10d38f59f32" data-businessunit-id="63f8938353044ed29109ad33" data-style-height="150px" data-style-width="100%" data-theme="light">
                    <a href="https://www.trustpilot.com/review/ai-pro.org" target="_blank" rel="noreferrer">Trustpilot</a>
                  </div>
                </div>
                {/* <!-- End TrustBox widget --> */}
              </div>
              <div className='flex flex-col justify-start'>
                <div className="trustpilot-widget ml-[-10px] sm:ml-[-15px] mt-[27px] h-[40px]" data-locale="en-US" data-template-id="56278e9abfbbba0bdcd568bc" data-businessunit-id="63f8938353044ed29109ad33" data-style-height="52px" data-style-width="225px" data-font-family="Poppins" data-border-color="#00b67a">
                  <a href="https://www.trustpilot.com/review/ai-pro.org" target="_blank" rel="noreferrer">Trustpilot</a>
                </div>
              </div>
            </div>
          )}
        </> : <></>}
        <div className="px-4 justify-self-center py-8 pt-[6rem] md:min-h-[98vh]">
          {activeCategory === 'popular' ? (
            <>
              <div className='relative'>
                <img
                  src={bg}
                  alt="Background"
                  className="absolute top-0 left-0 z-0 w-full object-cover"
                  style={{ height: '100%', minHeight: '300px' }}
                />
                <PopularModels />
                <Navigation
                  activeCategory={activeCategory}
                  setActiveCategory={setActiveCategory}
                  searchQuery={searchQuery}
                  setSearchQuery={setSearchQuery}
                />
              </div>
              <MostPopular tools={modifiedToolsData} fnRedirectApp={fnRedirectApp} fnReturnAppHref={fnReturnAppHref} activeCategory={activeCategory} />
            </>
          ) : (
            <>
              <div className='relative'>
                <HeaderA activeCategory={activeCategory} />
                <Navigation
                  activeCategory={activeCategory}
                  setActiveCategory={setActiveCategory}
                  searchQuery={searchQuery}
                  setSearchQuery={setSearchQuery}
                />
                <img
                  src={bg}
                  alt="Background"
                  className="absolute top-0 left-0 z-0 w-full object-cover"
                  style={{ height: '100%', minHeight: '300px' }}
                />
              </div>
              {activeCategory === 'all' ? (
                <>
                  {searchQuery ? (
                    <ToolsSection
                      title={<span style={{ color: '#373737' }}>Results for <span style={{ color: '#3073D5' }}>"{searchQuery}"</span></span>}
                      icon="all"
                      iconColor="blue"
                      tools={filteredTools}
                      searchQuery={searchQuery}
                      isLoading={isLoading}
                      activeCategory={activeCategory}
                      auth={auth}
                    />
                  ) : (
                    <>
                      <ToolsSection
                        title="AI Chatbots"
                        icon="chat"
                        iconColor="blue"
                        tools={chatbotTools}
                        searchQuery={searchQuery}
                        isLoading={isLoading}
                        activeCategory={activeCategory}
                        auth={auth}
                      />
                      <ToolsSection
                        title="AI Arts"
                        icon="art"
                        iconColor="purple"
                        tools={artTools}
                        searchQuery={searchQuery}
                        isLoading={isLoading}
                        activeCategory={activeCategory}
                        auth={auth}
                      />
                    </>
                  )}
                </>
              ) : (
                <>
                  {activeCategory === 'chatbots' && (
                    <>
                      <ToolsSection
                        title={<span style={{ color: '#373737' }}>Results for <span style={{ color: '#3073D5' }}>"{searchQuery}"</span></span>}
                        icon="chat"
                        iconColor="blue"
                        tools={filteredTools.filter(tool => tool.category === 'chatbots')}
                        searchQuery={searchQuery}
                        isLoading={isLoading}
                        activeCategory={activeCategory}
                        auth={auth}
                      />
                    </>
                  )}

                  {activeCategory === 'arts' && (
                    <>
                      <ToolsSection
                        title={<span style={{ color: '#373737' }}>Results for <span style={{ color: '#3073D5' }}>"{searchQuery}"</span></span>}
                        icon="art"
                        iconColor="purple"
                        tools={filteredTools.filter(tool => tool.category === 'arts')}
                        searchQuery={searchQuery}
                        isLoading={isLoading}
                        activeCategory={activeCategory}
                        auth={auth}
                      />
                    </>
                  )}
                </>
              )}
            </>
          )}
        </div>
      </div>
      <AddMoreMemberModal showAddMoreMember={showAddMoreMember} setshowAddMoreMember={setshowAddMoreMember} setMoreToAddMember={setMoreToAddMember} setMoreToAddMemberTotalAmount={setMoreToAddMemberTotalAmount} setShowCompletePurchase={setShowCompletePurchase} />
      <MemberCompletePurchase moreToAddMember={moreToAddMember} moreToAddMemberTotalAmount={moreToAddMemberTotalAmount} setShowCompletePurchase={setShowCompletePurchase} showCompletePurchase={showCompletePurchase} />
      <Footer />
    </div>
  );
};

const DownAppBannerContainer = ({ downAppBanner, setDownAppBanner }) => {
  const handleClose = () => {
    setDownAppBanner(false);
  };

  if (!downAppBanner) {
    return null;
  }

  return (
    <div className='px-4'>
      <div className="sticky border border-[#FFA500] rounded-[10px] p-6 flex flex-col md:flex-row items-center justify-between bg-[#FFF4E5] mt-[125px] mx-auto w-full md:w-fit z-50 mb-[-50px] max-w-[1230px]">
        <div className='relative w-full md:w-auto'>
          <div className="flex flex-col md:flex-row items-center text-center md:text-left">
            <div dangerouslySetInnerHTML={{ __html: downAppBanner }} />
          </div>
        </div>
        <button className="text-gray-500/50 hover:text-gray-700 absolute top-2 right-2 md:top-0 md:right-[5px]" onClick={handleClose}>
          <span className="text-xl">&times;</span>
        </button>
      </div>
    </div>
  );
};
export default MyAccountB;

