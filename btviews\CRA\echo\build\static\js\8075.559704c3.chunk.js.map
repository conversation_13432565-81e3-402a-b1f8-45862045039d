{"version": 3, "file": "static/js/8075.559704c3.chunk.js", "mappings": "2NACO,SAASA,EAAYC,GACxB,OAAIA,EAC0B,QAA3BA,EAASC,cAAgC,IACd,QAA3BD,EAASC,cAAgC,IACd,QAA3BD,EAASC,cAAgC,IACd,QAA3BD,EAASC,eACkB,QAA3BD,EAASC,cADgC,KAErC,GANc,EAOzB,CAEO,SAASC,EAAWF,GACvB,OAAIA,EAC0B,QAA3BA,EAASC,eACkB,QAA3BD,EAASC,cADgC,KAEd,QAA3BD,EAASC,cAAgC,KACd,QAA3BD,EAASC,cAAgC,KACd,QAA3BD,EAASC,cAAgC,KACd,QAA3BD,EAASC,cAAgC,KACd,QAA3BD,EAASC,cAAgC,KACrC,KARc,EASzB,CAEO,SAASE,EAAWC,GACvB,IAAIA,EAAS,MAAO,GACpB,MACMC,EAAO,IAAIC,KAAKF,EAAQG,QAAQ,KAAK,MAE3C,MAHc,CAAC,UAAU,WAAW,QAAQ,QAAQ,MAAM,OAAO,OAAO,SAAS,YAAY,UAAU,WAAW,YAGrGF,EAAKG,YAAc,IAAMH,EAAKI,UAAY,IAAMJ,EAAKK,aACtE,CAEO,SAASC,EAASC,GACrB,OAAGA,EAAKC,YAAoBD,EAAKC,YAC9BD,EAAKE,MAAcF,EAAKE,MACpB,GACX,CAEO,SAASC,EAAiBC,GAC7B,OAAOA,EAAEC,WAAWV,QAAQ,wBAAyB,IACzD,CAEO,SAASW,EAAaC,GAC3B,MAAMC,EAAcC,WAAWF,GAC/B,OACMJ,EADEK,EAAc,GAAM,EACLA,EAAYE,QAAQ,GACpBF,EAAYE,QAAQ,GAC7C,CAEO,SAASC,EAAavB,EAAUc,GACnC,OAAId,GAAac,EAEdU,MAAMV,GAAe,GAErBO,WAAWP,IAAU,EACU,QAA3Bd,EAASC,cACDiB,EAAaJ,GAAOW,eAAe,SAAW,OACpB,QAA3BzB,EAASC,cACRiB,EAAaJ,GAAOW,eAAe,SAAW,MACpB,QAA3BzB,EAASC,cACRiB,EAAaJ,GAAOW,eAAe,SAAW,MACpB,QAA3BzB,EAASC,cACRiB,EAAaJ,GAAOW,eAAe,SAAW,MACpB,QAA3BzB,EAASC,cACRiB,EAAaJ,GAAOW,eAAe,SAAW,KACrB,QAA3BzB,EAASC,cACPiB,EAAaJ,GAAOW,eAAe,SAAW,MACrB,QAA3BzB,EAASC,cACP,KAAOiB,EAAaJ,GAAOW,eAAe,SAChB,QAA3BzB,EAASC,cACRiB,EAAaJ,GAAOW,eAAe,SAAW,KACpB,QAA3BzB,EAASC,cACRiB,EAAaJ,GAAOW,eAAe,SAAW,MACpB,QAA3BzB,EAASC,cACRiB,EAAaJ,GAAOW,eAAe,SAAW,KACpB,QAA3BzB,EAASC,cACRiB,EAAaJ,GAAOW,eAAe,SAAW,IAElD1B,EAAYC,GAAYkB,EAAaJ,GAAOW,eAAe,SAG/D,IAAM1B,EAAYC,KAAoC,EAAvBkB,EAAaJ,IAAaW,eAAe,SA/BhD,EAgCnC,CAEO,SAASC,EAAQC,EAAKC,GAEzBD,EAAM,IAAIrB,KAAKqB,GAEf,IAAIE,IADJD,EAAM,IAAItB,KAAKsB,IACAE,UAAYH,EAAIG,WAAa,IAE5C,OADAD,GAAQ,GACDE,KAAKC,IAAID,KAAKE,MAAMJ,GAC/B,CAEO,SAASK,EAAe9B,GAC3B,IAAIA,EAAS,MAAO,GACpB,MACMC,EAAO,IAAIC,KAAKF,EAAQG,QAAQ,KAAK,MAE3C,MAHc,CAAC,UAAU,WAAW,QAAQ,QAAQ,MAAM,OAAO,OAAO,SAAS,YAAY,UAAU,WAAW,YAGrGF,EAAKG,YAAc,IAAMH,EAAKI,UAAY,IAAMJ,EAAKK,cAAgB,IAAML,EAAK8B,WAAa,IAAM9B,EAAK+B,YACzH,CAEO,SAASC,EAAUC,GAAU,IAAT,KAACC,GAAKD,EAC3BE,EAAa,GACbC,EAAW,GAgBf,MAf8B,WAA1BF,EAAKG,mBACPF,EAAajB,EAAagB,EAAKvC,SAAUqB,WAAWkB,EAAKzB,MAAQ,KAAKQ,QAAQ,IAC9EmB,GAAWE,EAAAA,EAAAA,MAAA,OAAKC,MAAM,eAAcC,SAAA,CAAC,UAAQtB,EAAagB,EAAKvC,SAAUuC,EAAKzB,OAAO,YAGzD,YAA1ByB,EAAKG,mBACPF,EAAajB,EAAagB,EAAKvC,SAAUqB,WAAWkB,EAAKzB,MAAQ,IAAIQ,QAAQ,IAC7EmB,GAAWE,EAAAA,EAAAA,MAAA,OAAKC,MAAM,eAAcC,SAAA,CAAC,UAAQtB,EAAagB,EAAKvC,SAAUuC,EAAKzB,OAAO,aAGnFyB,EAAK1B,cACP2B,EAAajB,EAAagB,EAAKvC,SAAUqB,WAAWkB,EAAK1B,YAAc0B,EAAKO,YAAYxB,QAAQ,IAChGmB,GAAWE,EAAAA,EAAAA,MAAA,OAAKC,MAAM,eAAcC,SAAA,CAAC,UAAQtB,EAAagB,EAAKvC,SAAUuC,EAAK1B,kBAI9E8B,EAAAA,EAAAA,MAAAI,EAAAA,SAAA,CAAAF,SAAA,EACEF,EAAAA,EAAAA,MAAA,KAAGK,UAAW,qCAA+D,MAA1BC,EAAAA,EAAAA,IAAU,YAAsB,OAAS,IAAKJ,SAAA,CAC9FL,EAAW,KAACU,EAAAA,EAAAA,KAAA,QAAMF,UAAU,UAASH,SAAC,gBAExCJ,IAGP,CAEO,SAASU,EAAcC,GAAU,IAAT,KAACb,GAAKa,EACnC,MAA2B,QAAvBH,EAAAA,EAAAA,IAAU,SACLZ,EAAW,CAACE,SAEjBA,EAAK1B,aACCqC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wCAAuCH,SAAEtB,EAAagB,EAAKvC,SAAUuC,EAAK1B,gBAG/F8B,EAAAA,EAAAA,MAAA,KAAGK,UAAU,wCAAuCH,SAAA,CACjDtB,EAAagB,EAAKvC,SAAUuC,EAAKzB,QAClC6B,EAAAA,EAAAA,MAAA,QAAMK,UAAU,UAASH,SAAA,CAAC,IACK,YAA1BN,EAAKG,iBAAiC,QAAU,YAI3D,CAqBO,SAASW,IACd,MAAMC,EAAkBC,OAAOC,SAASC,KAExC,OAAIH,EAAgBI,QAAQ,YAAc,EAC/B,WACAJ,EAAgBI,QAAQ,QAAU,EAClC,OAGJ,EACT,C,uNCyBA,QAzLA,YACEC,EAAAA,EAAAA,WAAU,KACRC,IAAAA,QAAiB,CACfC,cAAe,qBAEhB,KAEHF,EAAAA,EAAAA,WAAU,KACR,IAAIG,GAAeb,EAAAA,EAAAA,IAAU,qBAEVc,IAAfD,GAA2C,KAAfA,GAC9BE,WAAW,YACTC,EAAAA,EAAAA,IAAU,eAAe,IACzBL,IAAAA,MAAaE,EACf,EAAG,MAEJ,IAEH,MAAMI,GAAOC,EAAAA,EAAAA,IAAK,mBACXC,IAAaC,EAAAA,EAAAA,UAASd,OAAOa,UAAYb,OAAOa,UAAY,CAAC,IAC7DE,IAAWD,EAAAA,EAAAA,YAASD,EAAUG,aAAwC,IAA1BH,EAAUG,cACtD3D,IAAQyD,EAAAA,EAAAA,UAASD,EAAU7B,KAAO6B,EAAU7B,KAAO,MAC1D,QAAYwB,IAATG,IAA+B,IAATA,EAAgB,OACzC,IAAItD,EAAM,OAEV,MAAM4D,GAAKvB,EAAAA,EAAAA,IAAU,UACfwB,EAAWP,EAAKO,SAASxE,cA8F/B,OACE0C,EAAAA,EAAAA,MAAAI,EAAAA,SAAA,CAAAF,SAAA,EACEK,EAAAA,EAAAA,KAACwB,EAAAA,QAAM,CAACR,KAAMA,IACZtD,GACFsC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,2DAA0DH,UAErEK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,0BAAyBH,UACtCK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,4CAA2CH,UACxDK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,oDAAmDH,UAChEF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,+BAA8BH,SAAA,EAC3CK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,+BAA8BH,SAAC,mBAC7CF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,OAAMH,SAAA,EACnBF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,0BAAyBH,SAAA,EAACF,EAAAA,EAAAA,MAAA,KAAGK,UAAU,yBAAwBH,SAAA,CAAGjC,EAAK+D,kBAAmB,YAASzB,EAAAA,EAAAA,KAAA,SAChHoB,GAAW3B,EAAAA,EAAAA,MAAA,OAAAE,SAAA,CAAK,wBAAqBtB,EAAAA,EAAAA,IAAaX,EAAKZ,UAAUW,EAAAA,EAAAA,IAASC,IAAO,QAAOA,EAAK8B,kBAA4D,WAAxC9B,EAAK8B,iBAAiBzC,cAA6B,OAAS,QAAS,yBAA4BiD,EAAAA,EAAAA,KAAAH,EAAAA,SAAA,IAClNnC,EAAKgE,aAAehE,EAAKgE,aAAe,gEAE1CjC,EAAAA,EAAAA,MAAA,OAAKK,UAAU,uBAAsBH,SAAA,CAAC,WAAStB,EAAAA,EAAAA,IAAaX,EAAKZ,UAAUW,EAAAA,EAAAA,IAASC,WAEtFsC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,OAAMH,UACnBK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,sBAAqBH,UAChCK,EAAAA,EAAAA,KAAC2B,EAAAA,EAAOC,OAAM,CACV9B,UAAU,gFACV+B,WAAY,CAAEC,gBAAiB,WAC/BC,SAAU,CAAEC,MAAO,IACnBC,QAjCJ,WACpBC,QAAQC,IAAIZ,GACG,WAAXA,GApFJa,SAASC,cAAc,qBAAqBC,UAAUC,IAAI,UAE1DC,EAAAA,EAAMC,KAAK,uDAA+D,CACxEnB,KACAoB,QAAShF,EAAKgF,QACdC,QAAS,KACR,CAAEC,QAAS,CAAE,eAAgB,uCAAyCC,KAAK,SAASC,GACrF,IAAIC,EAASD,EAAIpF,KAEdqF,EAAOC,QACR3C,OAAOC,SAASjD,QAAQ0F,EAAOrF,KAAKuF,OAItCb,SAASC,cAAc,qBAAqBC,UAAUY,OAAO,UAC1DH,EAAOrF,MAAMgD,IAAAA,MAAaqC,EAAOrF,KAAKyF,KAC3C,KAKAf,SAASC,cAAc,qBAAqBC,UAAUC,IAAI,UAC1DC,EAAAA,EAAMC,KAAK,gDAAwD,CACjEnB,KACAoB,QAAShF,EAAKgF,SACb,CAAEE,QAAS,CAAE,eAAgB,uCAAyCC,KAAK,SAASC,GACrF,IAAIC,EAASD,EAAIpF,KACjB,GAAGqF,EAAOC,QAAS,CAEjB,GAAID,EAAOK,UAA8B,KAAlBL,EAAOK,SAE5B,YADA/C,OAAOC,SAAWyC,EAAOK,UAI3B1C,IAAAA,QAAe,WACf,IAAI2C,EAAWN,EAAOrF,KAAK2F,SACvBC,EAAWP,EAAOrF,KAAK4F,SAS3B,GAPAjD,OAAOkD,SAASC,MAAM,sBAAuB,CAC3C1G,SAAUiG,EAAOrF,KAAKZ,SACtB2G,OAAQvC,EAAUwC,cAClBL,SAAUA,EAASM,UAAY,MAAQN,EAAS7D,iBAChD8D,SAAUA,EAASK,UAAY,MAAQL,EAAS9D,mBAGnB,qBAA5B0B,EAAUwC,cAqBX,OAFArD,OAAOC,SAASC,KAAO,iBACvB6B,SAASC,cAAc,qBAAqBC,UAAUY,OAAO,UAnB7DV,EAAAA,EAAMC,KAAK,wCAAgD,CAAC,GAAGI,KAAMe,IACnE,IAAIC,EAAUD,EAAKlG,KACnB,GAAGmG,EAAQb,QAIT,OAHAtC,IAAAA,QAAe,kCACfL,OAAOC,SAASC,KAAO,iBACvB6B,SAASC,cAAc,qBAAqBC,UAAUY,OAAO,UAG1DW,EAAQnG,OACNqF,EAAOrF,KAAKoG,QACbpD,IAAAA,MAAaqC,EAAOrF,KAAKoG,SAEzBpD,IAAAA,MAAaqC,EAAOrF,KAAKyF,OAUrC,MACKJ,EAAOrF,OACLqF,EAAOrF,KAAKoG,QACbpD,IAAAA,MAAaqC,EAAOrF,KAAKoG,SAEzBpD,IAAAA,MAAaqC,EAAOrF,KAAKyF,KAG3Bf,SAASC,cAAc,qBAAqBC,UAAUY,OAAO,UAGnE,GAUF,EA0BiDvD,SAC1B,2BAKPF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,kDAAiDH,SAAA,EAC9DF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,gCAA+BH,SAAA,EAACK,EAAAA,EAAAA,KAAC+D,EAAAA,IAAM,CAACjE,UAAU,gDAA+C,uBAChHL,EAAAA,EAAAA,MAAA,OAAKK,UAAU,2EAA0EH,SAAA,EACvFK,EAAAA,EAAAA,KAAA,OAAKgE,IAAKC,EAAUC,IAAI,cAAcpE,UAAU,mBAChDL,EAAAA,EAAAA,MAAA,OAAKK,UAAU,6CAA4CH,SAAA,EACzDK,EAAAA,EAAAA,KAAA,OAAKgE,IAAKG,EAAQD,IAAI,YAAYpE,UAAU,yCAC5CE,EAAAA,EAAAA,KAAA,UACEiC,QAASA,KACP5B,OAAO+D,KACL,4FACA,eACA,6HAGJC,MAAM,0CACNvE,UAAU,OAAMH,UACdK,EAAAA,EAAAA,KAAA,OAAKsE,QAAQ,OACXN,IAAI,wGACJE,IAAI,+CACJpE,UAAU,aACVyE,MAAM,KACNC,OAAO,6BAStB,KAGf,C", "sources": ["core/utils/main.jsx", "pay-upgrade/index.jsx"], "sourcesContent": ["import { GetCookie } from '../../core/utils/cookies';\r\nexport function getCurrency(currency) {\r\n    if(!currency) return \"\";\r\n    if(currency.toLowerCase() === 'usd') return \"$\";\r\n    if(currency.toLowerCase() === 'eur') return \"€\";\r\n    if(currency.toLowerCase() === 'gbp') return \"£\";\r\n    if(currency.toLowerCase() === 'brl') return \"R$\";\r\n    if(currency.toLowerCase() === 'sar') return \"R$\";\r\n    return \"\";\r\n}\r\n\r\nexport function getCountry(currency) {\r\n    if(!currency) return \"\";\r\n    if(currency.toLowerCase() === 'usd') return \"US\";\r\n    if(currency.toLowerCase() === 'eur') return \"US\";\r\n    if(currency.toLowerCase() === 'gbp') return \"GB\";\r\n    if(currency.toLowerCase() === 'brl') return \"US\";\r\n    if(currency.toLowerCase() === 'sar') return \"AE\";\r\n    if(currency.toLowerCase() === 'chf') return \"CH\";\r\n    if(currency.toLowerCase() === 'sek') return \"SE\";\r\n    return \"US\";\r\n}\r\n\r\nexport function formatDate(dateStr) {\r\n    if(!dateStr) return \"\";\r\n    const month = [\"January\",\"February\",\"March\",\"April\",\"May\",\"June\",\"July\",\"August\",\"September\",\"October\",\"November\",\"December\"];\r\n    const date = new Date(dateStr.replace(/ /g,\"T\"));\r\n\r\n    return month[date.getMonth()] + \" \" + date.getDate() + \" \" + date.getFullYear();\r\n}\r\n\r\nexport function getPrice(data) {\r\n    if(data.trial_price) return data.trial_price;\r\n    if(data.price) return data.price;\r\n    return \"0\";\r\n}\r\n\r\nexport function numberWithCommas(x) {\r\n    return x.toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, \",\");\r\n}\r\n\r\nexport function formatNumber(number) {\r\n  const floatNumber = parseFloat(number);\r\n  return (floatNumber % 1 === 0)\r\n      ? numberWithCommas(floatNumber.toFixed(0))\r\n      : numberWithCommas(floatNumber.toFixed(2)); \r\n}\r\n\r\nexport function getPricePlan(currency, price) {\r\n    if(!currency || !price) return \"\";\r\n    // Check if price is a number\r\n    if(isNaN(price)) return \"\";\r\n    // Check if price is positive number\r\n    if(parseFloat(price) >= 0) {\r\n        if(currency.toLowerCase() === 'sar') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"ر.س.\";\r\n        } else if(currency.toLowerCase() === 'aed') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"AED\";\r\n        } else if(currency.toLowerCase() === 'chf') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"CHF\";\r\n        } else if(currency.toLowerCase() === 'sek') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"SEK\";\r\n        } else if(currency.toLowerCase() === 'pln') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"zł\";\r\n        }else if(currency.toLowerCase() === 'ron') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"LEI\";\r\n        }else if(currency.toLowerCase() === 'czk') {\r\n            return \"Kč\" + formatNumber(price).toLocaleString(\"en-US\");\r\n        } else if(currency.toLowerCase() === 'huf') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"Ft\";\r\n        } else if(currency.toLowerCase() === 'dkk') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"kr.\";\r\n        } else if(currency.toLowerCase() === 'bgn') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"лв\";\r\n        } else if(currency.toLowerCase() === 'try') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"₺\";\r\n        }\r\n        return getCurrency(currency) + formatNumber(price).toLocaleString(\"en-US\");\r\n    }\r\n    // Negative number\r\n    return \"-\" + getCurrency(currency) + (formatNumber(price) * -1).toLocaleString(\"en-US\");\r\n}\r\n\r\nexport function diffMin(dt1, dt2)\r\n{\r\n    dt1 = new Date(dt1);\r\n    dt2 = new Date(dt2);\r\n    var diff =(dt2.getTime() - dt1.getTime()) / 1000;\r\n    diff /= 60;\r\n    return Math.abs(Math.round(diff));\r\n}\r\n\r\nexport function formatDateTime(dateStr) {\r\n    if(!dateStr) return \"\";\r\n    const month = [\"January\",\"February\",\"March\",\"April\",\"May\",\"June\",\"July\",\"August\",\"September\",\"October\",\"November\",\"December\"];\r\n    const date = new Date(dateStr.replace(/ /g,\"T\"));\r\n\r\n    return month[date.getMonth()] + \" \" + date.getDate() + \" \" + date.getFullYear() + \" \" + date.getHours() + \":\" + date.getMinutes();\r\n}\r\n\r\nexport function DailyPrice({plan}) {\r\n  let dailyPrice = '';\r\n  let interval = '';\r\n  if (plan.payment_interval === 'Yearly') {\r\n    dailyPrice = getPricePlan(plan.currency, parseFloat(plan.price / 365).toFixed(2));\r\n    interval = <div class=\"text-xs mb-4\">billed {getPricePlan(plan.currency, plan.price)}/year</div>;\r\n  }\r\n\r\n  if (plan.payment_interval === 'Monthly') {\r\n    dailyPrice = getPricePlan(plan.currency, parseFloat(plan.price / 30).toFixed(2));\r\n    interval = <div class=\"text-xs mb-4\">billed {getPricePlan(plan.currency, plan.price)}/Month</div>;\r\n  }\r\n\r\n  if (plan.trial_price) {\r\n    dailyPrice = getPricePlan(plan.currency, parseFloat(plan.trial_price / plan.trial_days).toFixed(2));\r\n    interval = <div class=\"text-xs mb-4\">billed {getPricePlan(plan.currency, plan.trial_price)}</div>;\r\n  }\r\n\r\n  return (\r\n    <>\r\n      <p className={`text-4xl font-bold text-gray-800 ${(GetCookie(\"p_toggle\") === '') ? 'mb-4' : ''}`}>\r\n        {dailyPrice} <span className=\"text-sm\"> per Day</span>\r\n      </p>\r\n      {interval}\r\n    </>\r\n  );\r\n}\r\n\r\nexport function PriceFormatted({plan}) {\r\n  if (GetCookie(\"daily\") === 'on') {\r\n    return DailyPrice({plan});\r\n  } \r\n  if (plan.trial_price) {\r\n    return (<p className=\"text-4xl font-bold text-gray-800 mb-4\">{getPricePlan(plan.currency, plan.trial_price)}</p>)\r\n  } \r\n  return (\r\n    <p className=\"text-4xl font-bold text-gray-800 mb-4\">\r\n      {getPricePlan(plan.currency, plan.price)}\r\n      <span className=\"text-sm\"> \r\n        /{ plan.payment_interval === \"Monthly\" ? \"month\" : \"year\" }\r\n      </span>\r\n    </p>\r\n  )\r\n}\r\n\r\nexport function displayTextFormatted(plan) {\r\n  let payment_interval = plan.payment_interval === 'Yearly' ?  365 : 30;\r\n  let trialPricePer = `\r\n    then only ${getPricePlan(plan.currency, plan.price)} per month\r\n  `;\r\n\r\n  if (GetCookie(\"daily\") === 'on') {\r\n    trialPricePer = `\r\n      then only ${getPricePlan(plan.currency, parseFloat(plan.price / payment_interval).toFixed(2))} per day<br>\r\n      (billed ${getPricePlan(plan.currency, plan.price)} ${plan.payment_interval})\r\n    `;\r\n  }\r\n\r\n  return plan.display_txt2\r\n        .replace(\"{trialDays}\", plan.trial_days)\r\n        .replace(\"{trialPricePer}\", trialPricePer);\r\n  \r\n}\r\n\r\nexport function getPrefixLocation() {\r\n  const currentLocation = window.location.href;\r\n\r\n  if (currentLocation.indexOf(\"staging\") > -1) {\r\n      return \"staging.\";\r\n  } else if (currentLocation.indexOf(\"dev\") > -1) {\r\n      return \"dev.\";\r\n  }\r\n\r\n  return \"\"\r\n}", "import React, { useState, useEffect } from 'react';\r\nimport { motion } from \"framer-motion\";\r\nimport './style.css';\r\nimport Header from '../header/headerlogo';\r\nimport { FaLock } from 'react-icons/fa';\r\nimport ccImages from '../assets/images/cc_v2.png';\r\nimport ccAuth from '../assets/images/secure90x72.gif';\r\nimport { Auth } from '../core/utils/auth';\r\nimport { GetCookie, SetCookie } from '../core/utils/cookies';\r\nimport axios from 'axios';\r\nimport { getPrice, getPricePlan } from '../core/utils/main';\r\nimport toastr from 'toastr';\r\nimport 'toastr/build/toastr.min.css';\r\n\r\nfunction Payment() {\r\n  useEffect(() => {\r\n    toastr.options = {\r\n      positionClass: 'toast-top-center'\r\n    };\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    let threed_error = GetCookie('threed_error');\r\n\r\n    if (threed_error!==undefined && threed_error!==''){\r\n      setTimeout(function(){ \r\n        SetCookie('threed_error','');\r\n        toastr.error(threed_error);\r\n      }, 2000);      \r\n    }\r\n  }, []);\r\n\r\n  const auth = Auth('/register-auth');\r\n  const [view_data] = useState(window.view_data ? window.view_data : {});\r\n  const [isTrial] = useState(view_data.isUserTrial ? view_data.isUserTrial === 1 ? true : false : false);\r\n  const [data] = useState(view_data.plan ? view_data.plan : null);\r\n  if(auth === undefined || auth === false) return;\r\n  if(!data) return;\r\n\r\n  const tk = GetCookie(\"access\");\r\n  const merchant = auth.merchant.toLowerCase();\r\n\r\n  const submitPayPal = () => {\r\n    document.querySelector(\".loader-container\").classList.add('active');\r\n\r\n    axios.post(`${process.env.REACT_APP_API_URL}/create-subscription-paypal`, {\r\n      tk,\r\n      plan_id: data.plan_id,\r\n      upgrade: '1'\r\n    }, { headers: { 'content-type': 'application/x-www-form-urlencoded' } }).then(function(res) {\r\n      let output = res.data;\r\n\r\n      if(output.success) {\r\n        window.location.replace(output.data.link);\r\n        return;\r\n      }\r\n\r\n      document.querySelector(\".loader-container\").classList.remove('active');\r\n      if(output.data) toastr.error(output.data.msg);\r\n    });\r\n\r\n  }\r\n\r\n  const submitPayment = () => {\r\n    document.querySelector(\".loader-container\").classList.add('active');\r\n    axios.post(`${process.env.REACT_APP_API_URL}/update-subscription`, {\r\n      tk,\r\n      plan_id: data.plan_id\r\n    }, { headers: { 'content-type': 'application/x-www-form-urlencoded' } }).then(function(res) {\r\n      let output = res.data;\r\n      if(output.success) {\r\n\r\n        if (output.redirect && output.redirect!==''){\r\n          window.location = output.redirect;\r\n          return;  \r\n        }\r\n\r\n        toastr.success(\"Success\");\r\n        var old_plan = output.data.old_plan;\r\n        var new_plan = output.data.new_plan;\r\n\r\n        window.mixpanel.track(\"update_subscription\", {\r\n          currency: output.data.currency,\r\n          action: view_data.update_action,\r\n          old_plan: old_plan.plan_type + \" - \" + old_plan.payment_interval,\r\n          new_plan: new_plan.plan_type + \" - \" + new_plan.payment_interval\r\n        });\r\n        \r\n        if(view_data.update_action === 'cancel_downgrade'){\r\n          axios.post(`${process.env.REACT_APP_API_URL}/save-survey`, {}).then((res2) => {\r\n            let output2 = res2.data;\r\n            if(output2.success){\r\n              toastr.success(\"Survey submitted successfully!\");\r\n              window.location.href = '/thankyou';\r\n              document.querySelector(\".loader-container\").classList.remove('active');\r\n              return;\r\n            }else{\r\n              if(output2.data) {\r\n                if(output.data.message) {\r\n                  toastr.error(output.data.message);\r\n                } else {\r\n                  toastr.error(output.data.msg);\r\n                }\r\n              }\r\n            }\r\n          });\r\n        }else{\r\n          window.location.href = '/thankyou';\r\n          document.querySelector(\".loader-container\").classList.remove('active');\r\n          return;\r\n        }\r\n      }else{\r\n        if(output.data) {\r\n          if(output.data.message) {\r\n            toastr.error(output.data.message);\r\n          } else {\r\n            toastr.error(output.data.msg);\r\n          }\r\n\r\n          document.querySelector(\".loader-container\").classList.remove('active');\r\n        }\r\n      }\r\n    });\r\n  }\r\n  \r\n  const submitHandler = function(){\r\n    console.log(merchant)\r\n    if (merchant==='paypal'){\r\n      submitPayPal();\r\n    }else{\r\n      submitPayment();\r\n    }\r\n  }\r\n\r\n  return (\r\n    <>\r\n      <Header auth={auth} />\r\n      { data ?\r\n      <div className=\"Payment-upgrade bg-gray-100 min-h-[600px] mt-[50px] flex\">\r\n\r\n          <div className=\"container mx-auto py-10\">\r\n            <div className=\"flex flex-col items-center py-10 lg:py-16\">\r\n              <div className=\"flex flex-wrap md:flex-wrap justify-center w-full\">\r\n                <div className=\"pay_right px-4 mb-8 md:w-2/5\">\r\n                  <h2 className=\"text-xl font-bold mb-4 py-10\">Order Summary</h2>\r\n                  <div className=\"flex\">\r\n                    <div className=\"mb-2 w-4/5 text-sm mr-4\"><b className=\"text-md text-uppercase\">{ data.plan_type_display } PLAN</b><br/>\r\n                    { isTrial ? (<div>You will be charged {getPricePlan(data.currency, getPrice(data))} per { data.payment_interval && data.payment_interval.toLowerCase() === 'yearly' ? \"year\" : \"month\" } after trial ends.</div>) : <></> }\r\n                    { data.display_txt3 ? data.display_txt3 : \"Your subscription will renew monthly until you cancel it.\"}\r\n                    </div>\r\n                    <div className=\"font-bold mt-4 w-1/5\">Total: { getPricePlan(data.currency, getPrice(data)) }</div>\r\n                  </div>\r\n                  <div className=\"flex\">\r\n                    <div className=\"mb-2 w-full text-sm\">\r\n                        <motion.button\r\n                            className=\"bg-blue-500 text-white font-bold py-3 px-6 rounded-lg w-full my-4 proceed-pmt\"\r\n                            whileHover={{ backgroundColor: \"#5997fd\" }}\r\n                            whileTap={{ scale: 0.9 }}\r\n                            onClick={submitHandler}\r\n                        >\r\n                            Complete Purchase\r\n                        </motion.button>\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"securecont border-t-2 border-gray-300 flex py-5\">\r\n                    <div className=\"securetext mb-2 text-sm w-1/2\"><FaLock className=\"inline text-lg mr-1 text-orange-500 text-xs\"/> Secure Checkout</div>\r\n                    <div className=\"securelogo mb-2 text-sm w-1/2 flex flex-wrap justify-center items-center\">\r\n                      <img src={ccImages} alt=\"Secure Logo\" className=\"cclogo inline\"/>\r\n                      <div className=\"flex items-center justify-center flex-wrap\">\r\n                        <img src={ccAuth} alt=\"Authorize\" className=\"text-center md:text-right py-2 w-20\"/>\r\n                        <button\r\n                          onClick={() => {\r\n                            window.open(\r\n                              '//www.securitymetrics.com/site_certificate?id=1982469&tk=d41c416c6208f1136426bc8038178d2e',\r\n                              'Verification',\r\n                              'location=no, toolbar=no, resizable=no, scrollbars=yes, directories=no, status=no,top=100,left=100, width=700, height=600'\r\n                            );\r\n                          }}\r\n                          title=\"SecurityMetrics card safe certification\"\r\n                          className=\"h-20\" >\r\n                            <img loading=\"lazy\"\r\n                              src=\"https://www.securitymetrics.com/portal/app/ngsm/assets/img/GreyContent_Credit_Card_Safe_White_Sqr.png\"\r\n                              alt=\"SecurityMetrics card safe certification logo\"\r\n                              className=\"max-h-full\"\r\n                              width=\"80\"\r\n                              height=\"80\" />\r\n                        </button>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n      </div> : \"\" }\r\n    </>\r\n  );\r\n}\r\n\r\nexport default Payment;"], "names": ["getCurrency", "currency", "toLowerCase", "getCountry", "formatDate", "dateStr", "date", "Date", "replace", "getMonth", "getDate", "getFullYear", "getPrice", "data", "trial_price", "price", "numberWithCommas", "x", "toString", "formatNumber", "number", "floatNumber", "parseFloat", "toFixed", "getPricePlan", "isNaN", "toLocaleString", "diffMin", "dt1", "dt2", "diff", "getTime", "Math", "abs", "round", "formatDateTime", "getHours", "getMinutes", "DailyPrice", "_ref", "plan", "dailyPrice", "interval", "payment_interval", "_jsxs", "class", "children", "trial_days", "_Fragment", "className", "Get<PERSON><PERSON><PERSON>", "_jsx", "PriceFormatted", "_ref2", "getPrefixLocation", "currentLocation", "window", "location", "href", "indexOf", "useEffect", "toastr", "positionClass", "threed_error", "undefined", "setTimeout", "<PERSON><PERSON><PERSON><PERSON>", "auth", "<PERSON><PERSON>", "view_data", "useState", "isTrial", "isUserTrial", "tk", "merchant", "Header", "plan_type_display", "display_txt3", "motion", "button", "whileHover", "backgroundColor", "whileTap", "scale", "onClick", "console", "log", "document", "querySelector", "classList", "add", "axios", "post", "plan_id", "upgrade", "headers", "then", "res", "output", "success", "link", "remove", "msg", "redirect", "old_plan", "new_plan", "mixpanel", "track", "action", "update_action", "plan_type", "res2", "output2", "message", "FaLock", "src", "ccImages", "alt", "ccAuth", "open", "title", "loading", "width", "height"], "sourceRoot": ""}