{"version": 3, "file": "static/js/576.e2cac1f8.chunk.js", "mappings": "0KAGA,MAoCA,EApCwBA,IAA0B,IAAzB,gBAAEC,GAAiBD,EAC1C,MAAOE,EAAWC,IAAgBC,EAAAA,EAAAA,WAAS,IAE3CC,EAAAA,EAAAA,WAAU,KACR,MAAMC,EAAeA,KACnBH,EAAaI,OAAOC,YAAc,MAAQP,IAGtCQ,EAA+BA,KACnCN,GAAcF,IAMhB,OAHAM,OAAOG,iBAAiB,SAAUJ,GAClCC,OAAOG,iBAAiB,SAAUD,GAE3B,KACLF,OAAOI,oBAAoB,SAAUL,GACrCC,OAAOI,oBAAoB,SAAUF,KAEtC,CAACR,IAUJ,OACEW,EAAAA,EAAAA,KAAA,OAAKC,UAAW,gBAAeX,EAAY,UAAY,IAAKY,UAC1DF,EAAAA,EAAAA,KAACG,EAAAA,IAAe,CAACC,QATDC,KAClBV,OAAOW,SAAS,CACdC,IAAK,EACLC,SAAU,gB,0ECFhB,QAxBA,WAGE,MAAMC,EAAWC,yBAWjB,OAVAjB,EAAAA,EAAAA,WAAU,KACR,MAAMkB,EAASC,SAASC,cAAc,UACtCF,EAAOG,IAAML,EAAW,iDACxBE,EAAOI,OAAQ,EACfJ,EAAOK,OAAS,OAIhBJ,SAASK,KAAKC,YAAYP,IACzB,CAACF,KAEFT,EAAAA,EAAAA,KAAAmB,EAAAA,SAAA,CAAAjB,UACEF,EAAAA,EAAAA,KAAA,UAAQC,UAAW,iHAMzB,C,sHC4CA,QA9DA,SAAmBb,GAAkC,IAAjC,YAAEgC,EAAW,KAAEC,GAAOC,EAAAA,EAAAA,OAAQlC,EAChD,MAAMmC,EAAW5B,OAAO6B,SAASD,SAC3BE,EAAc,yBAAyBC,KAAKH,GAC5CI,EAAgB,sBAAsBD,KAAKH,GAC3CK,EAAc,yBAAyBF,KAAKH,GAC5CM,EAAiBN,EAASO,SAAS,kBACnCC,GAAYX,EACZY,EAAwBtB,CAAAA,SAAAA,aAAAA,WAAAA,eAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,cAAAA,EAAAA,kBAAAA,4BAAAA,mBAAAA,yBAAAA,oCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,kCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,2BAAAA,iBAAAA,4BAAAA,cAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,oBAAAA,uBAAAA,yBAAAA,8BAAAA,yBAAAA,gFAAAA,uBAAAA,6CAAAA,sBAAAA,KAAAA,uBAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,wBAAAA,KAAAA,mBAAAA,aAAAA,qCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,mCAAAA,KAAAA,8BAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,4BAAAA,4BAAAA,gCAAAA,oBAAAA,gCAAAA,oBAAAA,gCAAAA,oBAAAA,gCAAAA,oBAAAA,kCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,gCAAAA,KAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,iCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,6BAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,yBAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,iCAAAA,8GAAAA,iCAAAA,8GAAAA,oBAAAA,8jBAAYuB,iCAAmC,IAE7ExC,EAAAA,EAAAA,WAAU,KACR,MAAMyC,EAAUC,EAAkBC,EAAAA,EAAW,SACvCC,EAAWF,EAAkBG,EAAe,SAMlD,OALA1B,SAAS2B,KAAKC,OAAON,EAASG,GAEzBR,GACH,yDAEK,KACLK,EAAQO,SACRJ,EAASI,WAEV,CAACZ,IAGJ,MAAMM,EAAoBA,CAACO,EAAMC,KAC/B,MAAMC,EAAOhC,SAASC,cAAc,QAIpC,OAHA+B,EAAKC,IAAM,UACXD,EAAKF,KAAOA,EACZE,EAAKD,GAAKA,EACHC,GAGT,OACEE,EAAAA,EAAAA,MAAA3B,EAAAA,SAAA,CAAAjB,SAAA,CACG8B,IACChC,EAAAA,EAAAA,KAAA,OAAK+C,GAAG,wBAAwB9C,UAAU,mCAAkCC,SAAC,sFAI/EF,EAAAA,EAAAA,KAAA,UAAQ+C,GAAG,SAAS9C,UAAW,+FAA8F+B,EAAwB,aAAe,IAAK9B,UACvK4C,EAAAA,EAAAA,MAAA,OAAK7C,UAAU,2DAA0DC,SAAA,EACvE4C,EAAAA,EAAAA,MAAA,WAAS7C,UAAU,YAAWC,SAAA,EAC5BF,EAAAA,EAAAA,KAAA,UAAQgD,KAAK,aAAaC,OAAQX,EAAeY,MAAM,MAAMC,OAAO,KAAKlD,UAAU,eACnFD,EAAAA,EAAAA,KAAA,OAAKc,IAAKsB,EAAAA,EAAWgB,IAAI,cAAcnD,UAAU,kBAEjDwB,GAAeE,GAAiBC,IAAgBG,IAChD/B,EAAAA,EAAAA,KAAA,OAAKC,UAAU,uCAAuC8C,GAAG,OAAM7C,UAC7DF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,2BAA0BC,UACtCF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,uBAAsBC,UAClCF,EAAAA,EAAAA,KAAA,KAAG0C,KAAMrB,EAAO,cAAgB,SAAUpB,UAAU,YAAY,aAAYoB,EAAO,aAAe,QAAQnB,SACvGmB,EAAO,UAAY,wBAUxC,C,8YCwSA,QAxUA,WACE,MAAMA,GAAOC,EAAAA,EAAAA,OACN+B,EAASC,IAAc9D,EAAAA,EAAAA,UAAS,QACjC+D,GAAWC,EAAAA,EAAAA,QAAO,MAClBC,GAAYD,EAAAA,EAAAA,QAAO,MACnBE,GAAYF,EAAAA,EAAAA,QAAO,MACnBG,GAAaH,EAAAA,EAAAA,QAAO,MACpBI,GAAaJ,EAAAA,EAAAA,QAAO,MACpBK,GAAWL,EAAAA,EAAAA,QAAO,MAClBM,GAAYN,EAAAA,EAAAA,QAAO,OAClBnE,EAAiB0E,IAAsBvE,EAAAA,EAAAA,WAAS,GACjDwE,MAAYC,EAAAA,EAAAA,IAAU,aAAyC,QAA1BA,EAAAA,EAAAA,IAAU,aAE/CC,EAAoBA,KACnB7C,EAEMA,GAAwB,WAAhBA,EAAK8C,OACtBxE,OAAO6B,SAASkB,KAAO,cAEvB/C,OAAO6B,SAASkB,KAAO,WAJvB/C,OAAO6B,SAASkB,KAAO,kBAQtB0B,EAAaA,KAChBb,EAASc,QAAQC,eAAe,CAAE9D,SAAU,aAkB9Cf,EAAAA,EAAAA,WAAU,KAER,MAAM8E,EAAU3D,SAAS4D,OAAOC,MAAM,KACtC,IAAK,IAAIC,EAAI,EAAGA,EAAIH,EAAQI,OAAQD,IAAK,CACvC,MAAMF,EAASD,EAAQG,GAAGE,OAC1B,GAAIJ,EAAOK,WAAW,YAAa,CACjCvB,EAAWkB,EAAOM,UAAU,IAC5B,KACF,CACF,CAGA,MAAMC,EAAgBnE,SAASoE,cAAc,mBAEvCtF,EAAeA,KACnB,MAAMuF,EAAiBtF,OAAOuF,QACxBC,EAAerB,EAAUO,QAAQe,wBAAwBC,OAC/DtB,EAAmBpE,OAAOC,YAAcuF,GACxCJ,EAAcO,MAAMC,UAAY,eAAgC,GAAjBN,QAKjD,OAFAtF,OAAOG,iBAAiB,SAAUJ,GAE3B,KACLC,OAAOI,oBAAoB,SAAUL,KAGtC,CAAC2B,IACJ,MAAMmE,EAAY9E,CAAAA,SAAAA,aAAAA,WAAAA,eAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,cAAAA,EAAAA,kBAAAA,4BAAAA,mBAAAA,yBAAAA,oCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,wCAAAA,KAAAA,kCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,sCAAAA,cAAAA,2BAAAA,iBAAAA,4BAAAA,cAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,+BAAAA,iBAAAA,oBAAAA,uBAAAA,yBAAAA,8BAAAA,yBAAAA,gFAAAA,uBAAAA,6CAAAA,sBAAAA,KAAAA,uBAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,2BAAAA,cAAAA,wBAAAA,KAAAA,mBAAAA,aAAAA,qCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,yCAAAA,iBAAAA,mCAAAA,KAAAA,8BAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,kCAAAA,iCAAAA,4BAAAA,4BAAAA,gCAAAA,oBAAAA,gCAAAA,oBAAAA,gCAAAA,oBAAAA,gCAAAA,oBAAAA,kCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,sCAAAA,WAAAA,gCAAAA,KAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,oCAAAA,QAAAA,iCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,qCAAAA,QAAAA,6BAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,iCAAAA,QAAAA,yBAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,6BAAAA,WAAAA,iCAAAA,8GAAAA,iCAAAA,8GAAAA,oBAAAA,8jBAAY+E,sBAM9B,QAAYC,IAATrE,EACH,OACEyB,EAAAA,EAAAA,MAAA3B,EAAAA,SAAA,CAAAjB,SAAA,EACE4C,EAAAA,EAAAA,MAAC6C,EAAAA,EAAM,CAAAzF,SAAA,EACLF,EAAAA,EAAAA,KAAA,QAAM4F,KAAK,SAASC,QAAQ,uBAC5B7F,EAAAA,EAAAA,KAAA,SAAAE,SAAO,gDACPF,EAAAA,EAAAA,KAAA,QAAM4F,KAAK,cAAcC,QAAQ,2FAEtB,SAAZxC,GACCrD,EAAAA,EAAAA,KAAC8F,EAAAA,QAAM,CAAC1E,YAAa4C,KAErBhE,EAAAA,EAAAA,KAAC+F,EAAAA,QAAU,CAAC3E,YAAa4C,KAEzBhE,EAAAA,EAAAA,KAAA,OAAKgG,IAAKlC,KACV9D,EAAAA,EAAAA,KAACiG,EAAAA,QAAe,CAAC5G,gBAAiBA,KAClCyD,EAAAA,EAAAA,MAAA,OAAK7C,UAAU,mCAAkCC,SAAA,EAE/CF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oCAAmCC,UAChD4C,EAAAA,EAAAA,MAAA,OAAK7C,UAAU,aAAYC,SAAA,EACzB4C,EAAAA,EAAAA,MAAA,OAAK7C,UAAU,gEAA+DC,SAAA,EAC5E4C,EAAAA,EAAAA,MAAA,MAAI7C,UAAU,mEAAkEC,SAAA,CAAC,eAAWF,EAAAA,EAAAA,KAAA,SAAK,cACjG8C,EAAAA,EAAAA,MAAA,KAAG7C,UAAU,2CAA0CC,SAAA,CAAC,oKAC0GF,EAAAA,EAAAA,KAAA,UAAKA,EAAAA,EAAAA,KAAA,SAAK,kGAE5KA,EAAAA,EAAAA,KAACkG,EAAAA,EAAOC,OAAM,CACZ/F,QAAS8D,EACTjE,UAAU,sHACVmG,WAAY,CAAEC,MAAO,KACrBC,SAAU,CAAED,MAAO,IAAMnG,SAC1B,kBAIHF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oDAAmDC,UAChEF,EAAAA,EAAAA,KAAA,OAAKc,IAAKyF,EAAUnD,IAAI,SAASnD,UAAU,sDAKjD6C,EAAAA,EAAAA,MAAA,OAAK7C,UAAU,uDAAsDC,SAAA,EACnEF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,SAAQC,UACrBF,EAAAA,EAAAA,KAAA,UAAQC,UAAU,SAAS8C,GAAG,gBAAgByD,MAAM,kBAAkB1F,IAAK0E,EAAWiB,YAAY,SAGpG3D,EAAAA,EAAAA,MAAA,OAAK7C,UAAU,gCAA+BC,SAAA,EAC5C4C,EAAAA,EAAAA,MAAA,OAAK7C,UAAU,qBAAoBC,SAAA,EACjCF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sCAAqCC,SAAC,4BAGpDF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,eAAcC,SAAC,gCAC7BF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,2CAA0CC,SAAC,yKAI1D4C,EAAAA,EAAAA,MAAA,OAAK7C,UAAU,mCAAkCC,SAAA,EAC/CF,EAAAA,EAAAA,KAAA,OAAKc,IAAK4F,EAAStD,IAAI,SAASnD,UAAU,qCACxCD,EAAAA,EAAAA,KAAA,OAAKc,IAAK6F,EAASvD,IAAI,SAASnD,UAAU,qCAC5CD,EAAAA,EAAAA,KAAA,OAAKc,IAAK8F,EAASxD,IAAI,SAASnD,UAAU,qCAC1CD,EAAAA,EAAAA,KAAA,OAAKc,IAAK+F,EAASzD,IAAI,SAASnD,UAAU,qCACxCD,EAAAA,EAAAA,KAAA,OAAKc,IAAKgG,EAAS1D,IAAI,SAASnD,UAAU,qCAC5CD,EAAAA,EAAAA,KAAA,OAAKc,IAAKiG,EAAS3D,IAAI,SAASnD,UAAU,8CAKhD6C,EAAAA,EAAAA,MAAA,OAAK7C,UAAU,qBAAoBC,SAAA,EACjCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oBACbD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mBAAkBC,UAC/BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gCAA+BC,UAC5C4C,EAAAA,EAAAA,MAAA,OAAK7C,UAAU,sCAAqCC,SAAA,EAClD4C,EAAAA,EAAAA,MAAA,OAAK7C,UAAU,cAAaC,SAAA,EAC1BF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,iCAAgCC,SAAC,sBAG/CF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,eAAcC,SAAC,mCAE/BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,kDAAiDC,UAE9D4C,EAAAA,EAAAA,MAAA,OAAK7C,UAAU,gEAA+DC,SAAA,EAC5E4C,EAAAA,EAAAA,MAAA,OAAK7C,UAAU,4IAA2IC,SAAA,EACxJF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,2BAA0BC,SAAC,yVAGxCF,EAAAA,EAAAA,KAACkG,EAAAA,EAAOC,OAAM,CACZ/F,QAASgE,EACTnE,UAAU,+IACVmG,WAAY,CAAEC,MAAO,KACrBC,SAAU,CAAED,MAAO,IAAMnG,SAC1B,qBAKHF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,8EAA6EC,UAC1FF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mCAAkCC,UAC/CF,EAAAA,EAAAA,KAAA,OAAKc,IAAKkG,EAAO5D,IAAI,SAASnD,UAAU,gCAI5CD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mHAAkHC,UAC/H4C,EAAAA,EAAAA,MAAA,MAAI7C,UAAU,YAAWC,SAAA,EACvB4C,EAAAA,EAAAA,MAAA,MAAA5C,SAAA,EAAIF,EAAAA,EAAAA,KAACiH,EAAAA,IAAS,CAAChH,UAAU,yBAAwBD,EAAAA,EAAAA,KAAA,KAAG0C,KAAK,UAAUtC,QAASgE,EAAWlE,SAAC,wBACxF4C,EAAAA,EAAAA,MAAA,MAAA5C,SAAA,EAAIF,EAAAA,EAAAA,KAACkH,EAAAA,IAAe,CAACjH,UAAU,yBAAwBD,EAAAA,EAAAA,KAAA,KAAG0C,KAAK,UAAUtC,QAxJ/E+G,KAClB1D,EAAUY,QAAQC,eAAe,CAAE9D,SAAU,YAuJyEN,SAAC,uBAC/F4C,EAAAA,EAAAA,MAAA,MAAA5C,SAAA,EAAIF,EAAAA,EAAAA,KAACoH,EAAAA,IAAU,CAACnH,UAAU,yBAAwBD,EAAAA,EAAAA,KAAA,KAAG0C,KAAK,UAAUtC,QAtJ1EiH,KAClB3D,EAAUW,QAAQC,eAAe,CAAE9D,SAAU,YAqJoEN,SAAC,qBAC1F4C,EAAAA,EAAAA,MAAA,MAAA5C,SAAA,EAAIF,EAAAA,EAAAA,KAACsH,EAAAA,IAAU,CAACrH,UAAU,yBAAwBD,EAAAA,EAAAA,KAAA,KAAG0C,KAAK,UAAUtC,QApJzEmH,KACnB5D,EAAWU,QAAQC,eAAe,CAAE9D,SAAU,YAmJoEN,SAAC,sBAC3F4C,EAAAA,EAAAA,MAAA,MAAA5C,SAAA,EAAIF,EAAAA,EAAAA,KAACwH,EAAAA,IAAY,CAACvH,UAAU,yBAAwBD,EAAAA,EAAAA,KAAA,KAAG0C,KAAK,UAAUtC,QAlJ3EqH,KACnB7D,EAAWS,QAAQC,eAAe,CAAE9D,SAAU,YAiJsEN,SAAC,oBAC7F4C,EAAAA,EAAAA,MAAA,MAAA5C,SAAA,EAAIF,EAAAA,EAAAA,KAAC0H,EAAAA,IAAe,CAACzH,UAAU,yBAAwBD,EAAAA,EAAAA,KAAA,KAAG0C,KAAK,UAAUtC,QAhJhFuH,KACjB9D,EAASQ,QAAQC,eAAe,CAAE9D,SAAU,YA+IyEN,SAAC,uCAWhHF,EAAAA,EAAAA,KAAA,OAAKgG,IAAKzC,EAAUtD,UAAU,gEAA+DC,UAC3F4C,EAAAA,EAAAA,MAAA,OAAK7C,UAAU,wDAAuDC,SAAA,EACpE4C,EAAAA,EAAAA,MAAA,OAAK7C,UAAU,wDAAuDC,SAAA,EACpEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,mEAAkEC,SAAC,iCACjFF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,mBAAkBC,SAAC,wUAIlC4C,EAAAA,EAAAA,MAAA,OAAK7C,UAAU,sCAAqCC,SAAA,EAClDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,kBAAiBC,UAC9BF,EAAAA,EAAAA,KAAA,OAAKc,IAAK8G,EAASxE,IAAI,SAASnD,UAAU,+BAE5CD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,kBAAiBC,UAC9BF,EAAAA,EAAAA,KAAA,OAAKc,IAAK+G,EAASzE,IAAI,SAASnD,UAAU,uCAMlDD,EAAAA,EAAAA,KAAA,OAAKgG,IAAKvC,EAAWxD,UAAU,mEAAkEC,UAC/F4C,EAAAA,EAAAA,MAAA,OAAK7C,UAAU,0CAAyCC,SAAA,EACtDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,+BAA8BC,UAC3CF,EAAAA,EAAAA,KAAA,OAAKc,IAAKgH,EAAU1E,IAAI,SAASnD,UAAU,YAE7C6C,EAAAA,EAAAA,MAAA,OAAK7C,UAAU,yDAAwDC,SAAA,EACrEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,8DAA6DC,SAAC,0BAC5EF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,mBAAkBC,SAAC,qWAOtCF,EAAAA,EAAAA,KAAA,OAAKgG,IAAKtC,EAAWzD,UAAU,iEAAgEC,UAC7F4C,EAAAA,EAAAA,MAAA,OAAK7C,UAAU,0CAAyCC,SAAA,EACtD4C,EAAAA,EAAAA,MAAA,OAAK7C,UAAU,wDAAuDC,SAAA,EACpEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,mEAAkEC,SAAC,iCACjFF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,mBAAkBC,SAAC,mVAIlCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,+BAA8BC,UAC3CF,EAAAA,EAAAA,KAAA,OAAKc,IAAKiH,EAAW3E,IAAI,iBAAiBnD,UAAU,mCAM1DD,EAAAA,EAAAA,KAAA,OAAKgG,IAAKrC,EAAY1D,UAAU,kEAAiEC,UAC/F4C,EAAAA,EAAAA,MAAA,OAAK7C,UAAU,0CAAyCC,SAAA,EACtDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,+BAA8BC,UAC3CF,EAAAA,EAAAA,KAAA,OAAKc,IAAKkH,EAAQ5E,IAAI,SAASnD,UAAU,YAE3C6C,EAAAA,EAAAA,MAAA,OAAK7C,UAAU,yDAAwDC,SAAA,EACrEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,8DAA6DC,SAAC,kCAC5EF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,mBAAkBC,SAAC,yTAQtCF,EAAAA,EAAAA,KAAA,OAAKgG,IAAKpC,EAAY3D,UAAU,iEAAgEC,UAC9F4C,EAAAA,EAAAA,MAAA,OAAK7C,UAAU,0CAAyCC,SAAA,EACtD4C,EAAAA,EAAAA,MAAA,OAAK7C,UAAU,wDAAuDC,SAAA,EACpEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,mEAAkEC,SAAC,4BACjFF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,mBAAkBC,SAAC,gSAIlCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,uEAAsEC,UACnF4C,EAAAA,EAAAA,MAAA,OAAK7C,UAAU,eAAcC,SAAA,EAC3BF,EAAAA,EAAAA,KAAA,OAAKc,IAAKmH,EAAU7E,IAAI,SAASnD,UAAU,+CAC3CD,EAAAA,EAAAA,KAAA,OAAKc,IAAKoH,EAAU9E,IAAI,SAASnD,UAAU,yDAOnDD,EAAAA,EAAAA,KAAA,OAAKgG,IAAKnC,EAAU5D,UAAU,6EAA4EC,UACxG4C,EAAAA,EAAAA,MAAA,OAAK7C,UAAU,8BAA6BC,SAAA,EAC1C4C,EAAAA,EAAAA,MAAA,OAAK7C,UAAU,qCAAoCC,SAAA,EACjDF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,mEAAkEC,SAAC,wBACjFF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,mBAAkBC,SAAC,6SAIlC4C,EAAAA,EAAAA,MAAA,OAAK7C,UAAU,sCAAqCC,SAAA,EAClDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,6CAA4CC,UACzDF,EAAAA,EAAAA,KAAA,OAAKc,IAAKqH,EAAI/E,IAAI,SAASnD,UAAU,2BAEvCD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gEAA+DC,UAC5EF,EAAAA,EAAAA,KAAA,OAAKc,IAAKsH,EAAIhF,IAAI,SAASnD,UAAU,2BAEvCD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,6CAA4CC,UACzDF,EAAAA,EAAAA,KAAA,OAAKc,IAAKuH,EAAIjF,IAAI,SAASnD,UAAU,mCAO7C6C,EAAAA,EAAAA,MAAA,OAAK7C,UAAU,gCAA+BC,SAAA,EAC5CF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gEAA+DC,UAC5E4C,EAAAA,EAAAA,MAAA,OAAK7C,UAAU,+CAA8CC,SAAA,EAC3DF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,4DAA2DC,SAAC,iCAG1EF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,kDAAiDC,SAAC,+VAG/DF,EAAAA,EAAAA,KAACkG,EAAAA,EAAOC,OAAM,CACZ/F,QAAS8D,EACTjE,UAAU,mHACVmG,WAAY,CAAEC,MAAO,KACrBC,SAAU,CAAED,MAAO,IAAMnG,SAC1B,0BAKLF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,eAAcC,UAC3BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,kEAAiEC,UAC9EF,EAAAA,EAAAA,KAAA,OAAKc,IAAKwH,EAAQlF,IAAI,iBAAiBnD,UAAU,0CAK3DD,EAAAA,EAAAA,KAACuI,EAAAA,QAAM,CAAClH,KAAMA,EAAMD,YAAa4C,MAGvC,C,gDCvWA,SAAiB,C", "sources": ["footer/backtotop.jsx", "footer/index.jsx", "header/headerlogo.jsx", "lp/start-chatgpt-go.jsx", "webpack://v1/./node_modules/react-responsive-carousel/lib/styles/carousel.css?c782"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { FaArrowCircleUp } from 'react-icons/fa';\r\n\r\nconst BackToTopButton = ({ isHeaderVisible }) => {\r\n  const [isVisible, setIsVisible] = useState(false);\r\n\r\n  useEffect(() => {\r\n    const handleScroll = () => {\r\n      setIsVisible(window.pageYOffset > 300 && !isHeaderVisible);\r\n    };\r\n\r\n    const handleHeaderVisibilityChange = () => {\r\n      setIsVisible(!isHeaderVisible);\r\n    };\r\n\r\n    window.addEventListener('scroll', handleScroll);\r\n    window.addEventListener('resize', handleHeaderVisibilityChange);\r\n\r\n    return () => {\r\n      window.removeEventListener('scroll', handleScroll);\r\n      window.removeEventListener('resize', handleHeaderVisibilityChange);\r\n    };\r\n  }, [isHeaderVisible]);\r\n\r\n\r\n  const scrollToTop = () => {\r\n    window.scrollTo({\r\n      top: 0,\r\n      behavior: 'smooth',\r\n    });\r\n  };\r\n\r\n  return (\r\n    <div className={`back-to-top ${isVisible ? 'visible' : ''}`}>\r\n      <FaArrowCircleUp onClick={scrollToTop} />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default BackToTopButton;\r\n", "import React, { useEffect } from 'react';\r\n\r\nfunction Powered() {\r\n  // const [isHidden, setIsHidden] = useState(false);\r\n\r\n  const base_url = process.env.REACT_APP_BASE_URL;\r\n  useEffect(() => {\r\n    const script = document.createElement('script');\r\n    script.src = base_url + 'snippets/com.global.vuzo/js/com.global.vuzo.js';\r\n    script.async = true;\r\n    script.onload = () => {\r\n      // window.displayGooglePlay();\r\n      // window.displayQR();\r\n    };\r\n    document.body.appendChild(script);\r\n  }, [base_url]);\r\n  return (\r\n    <>\r\n      <footer className={`relative hidden md:block md:absolute bottom-[10px] right-[5px] w-full mt-3 py-12 md:py-2 z-[8888] md:hidden`}>\r\n      {/* <footer className=\"relative hidden md:block md:absolute bottom-[10px] right-[5px] w-full mt-3 py-12 md:py-2 z-[8888]\"> */}\r\n\r\n      </footer>\r\n    </>\r\n  );\r\n}\r\n\r\nexport default Powered;\r\n", "import React, { useEffect } from 'react';\r\nimport aiproLogo from '../assets/images/AIPRO.svg';\r\nimport aiproLogoWebP from '../assets/images/AIPRO.webp';\r\nimport { Auth } from '../core/utils/auth';\r\nimport './style.css';\r\n\r\nfunction HeaderLogo({ hideNavLink, auth = Auth() }) {\r\n  const pathname = window.location.pathname;\r\n  const isChatGptGo = /\\/start-chatgpt-go\\/?$/.test(pathname);\r\n  const isTexttoImage = /\\/text-to-image\\/?$/.test(pathname);\r\n  const isChatGptV2 = /\\/start-chatgpt-v2\\/?$/.test(pathname);\r\n  const isRegisterPage = pathname.includes('/register-auth');\r\n  const showLink = !hideNavLink;\r\n  const showMaintenanceBanner = process.env.REACT_APP_ShowMaintenanceBanner || '';\r\n\r\n  useEffect(() => {\r\n    const linkPNG = createPreloadLink(aiproLogo, 'image');\r\n    const linkWebP = createPreloadLink(aiproLogoWebP, 'image');\r\n    document.head.append(linkPNG, linkWebP);\r\n\r\n    if (!isRegisterPage) {\r\n      import('@fortawesome/fontawesome-free/css/all.css');\r\n    }\r\n    return () => {\r\n      linkPNG.remove();\r\n      linkWebP.remove();\r\n    };\r\n  }, [isRegisterPage]);\r\n\r\n\r\n  const createPreloadLink = (href, as) => {\r\n    const link = document.createElement('link');\r\n    link.rel = 'preload';\r\n    link.href = href;\r\n    link.as = as;\r\n    return link;\r\n  };\r\n\r\n  return (\r\n    <>\r\n      {showMaintenanceBanner && (\r\n        <div id=\"maintenance-container\" className=\"p-[5px] bg-[#f7a73f] text-center\">\r\n          We are currently undergoing maintenance. We're expecting to be back at 4 AM EST.\r\n        </div>\r\n      )}\r\n      <header id=\"header\" className={`headernav border-gray-500 bg-white text-black w-3/4 mx-auto rounded-3xl mt-3 py-2 z-[8888] ${showMaintenanceBanner ? 'top-[60px]' : ''}`}>\r\n        <div className=\"container mx-auto flex justify-between items-center px-4\">\r\n          <picture className=\"aiprologo\">\r\n            <source type=\"image/webp\" srcSet={aiproLogoWebP} width=\"150\" height=\"52\" className=\"aiprologo\" />\r\n            <img src={aiproLogo} alt=\"AI-Pro Logo\" className=\"aiprologo\"/>\r\n          </picture>\r\n          {(isChatGptGo || isTexttoImage || isChatGptV2) && showLink && (\r\n            <nav className=\"text-xs lg:text-sm block inline-flex\" id=\"menu\">\r\n              <ul className=\"headnav flex inline-flex\">\r\n                <li className=\"mr-1 md:mr-2 lg:mr-6\">\r\n                  <a href={auth ? '/my-account' : '/login'} className=\"font-bold\" aria-label={auth ? 'my-account' : 'login'}>\r\n                    {auth ? 'My Apps' : 'LOG IN'}\r\n                  </a>\r\n                </li>\r\n              </ul>\r\n            </nav>\r\n          )}\r\n        </div>\r\n      </header>\r\n    </>\r\n  );\r\n}\r\n\r\nexport default HeaderLogo;\r\n", "import React, { useState, useEffect, useRef } from 'react';\r\nimport './start-chatgpt-go.css';\r\nimport Header from '../header';\r\nimport Headerlogo from '../header/headerlogo';\r\nimport Footer from '../footer';\r\nimport \"particles.js\";\r\nimport { Auth } from '../core/utils/auth';\r\nimport 'react-responsive-carousel/lib/styles/carousel.css';\r\nimport { Helmet } from 'react-helmet';\r\nimport { motion } from \"framer-motion\";\r\nimport introbg1 from '../assets/images/chatgo.gif';\r\nimport simpli1 from '../assets/images/simpli1.png';\r\nimport simpli2 from '../assets/images/simpli2.png';\r\nimport simpli3 from '../assets/images/simpli3.png';\r\nimport simpli4 from '../assets/images/simpli4.gif';\r\nimport simpli5 from '../assets/images/simpli5.png';\r\nimport simpli6 from '../assets/images/simpli6.gif';\r\nimport lpPic1 from '../assets/images/sd2.png';\r\nimport blkpic1 from '../assets/images/x1.gif';\r\nimport blkpic2 from '../assets/images/x2.gif';\r\nimport blkpic2a from '../assets/images/blk2.png';\r\nimport bkgpng from '../assets/images/bkg.png';\r\nimport elonmuska from '../assets/images/elonmusk.gif';\r\nimport aminpic1 from '../assets/images/cchat.gif';\r\nimport aminpic2 from '../assets/images/codex.png';\r\nimport l1 from '../assets/images/l1.png';\r\nimport l2 from '../assets/images/l2.png';\r\nimport l3 from '../assets/images/l3.png';\r\nimport { GetCookie } from '../core/utils/cookies';\r\n\r\nimport para1 from '../assets/images/parallaximage.png';\r\nimport { FaRegEdit, FaFileSignature, FaMoneyCheckAlt, FaMailBulk, FaBookOpen, FaLaptopCode } from 'react-icons/fa';\r\nimport BackToTopButton from '../footer/backtotop';\r\n\r\nfunction StartChatGptgo() {\r\n  const auth = Auth();\r\n  const [navmenu, setNavmenu] = useState('hide');\r\n  const draftRef = useRef(null);\r\n  const scriptRef = useRef(null);\r\n  const emailsRef = useRef(null);\r\n  const storiesRef = useRef(null);\r\n  const promptsRef = useRef(null);\r\n  const moneyRef = useRef(null);\r\n  const headerRef = useRef(null);\r\n  const [isHeaderVisible, setIsHeaderVisible] = useState(true);\r\n  const hideLinks = GetCookie('qW1eMlya') && GetCookie('qW1eMlya') === 'on' ? true : false;\r\n\r\n  const checkSubscription = () => {\r\n    if(!(auth)){\r\n      window.location.href = '/register-auth';\r\n    } else if (auth && auth.status === 'active') {\r\n      window.location.href = '/my-account';\r\n    } else {\r\n      window.location.href = '/pricing';\r\n    }\r\n  };\r\n\r\n const draftClick = () => {\r\n    draftRef.current.scrollIntoView({ behavior: 'smooth' });\r\n  };\r\n  const scriptClick = () => {\r\n    scriptRef.current.scrollIntoView({ behavior: 'smooth' });\r\n  };\r\n  const emailsClick = () => {\r\n    emailsRef.current.scrollIntoView({ behavior: 'smooth' });\r\n  };\r\n  const storiesClick = () => {\r\n    storiesRef.current.scrollIntoView({ behavior: 'smooth' });\r\n  };\r\n  const promptsClick = () => {\r\n    promptsRef.current.scrollIntoView({ behavior: 'smooth' });\r\n  };\r\n  const moneyClick = () => {\r\n    moneyRef.current.scrollIntoView({ behavior: 'smooth' });\r\n  };\r\n\r\n  useEffect(() => {\r\n    // checkflag\r\n    const cookies = document.cookie.split(';');\r\n    for (let i = 0; i < cookies.length; i++) {\r\n      const cookie = cookies[i].trim();\r\n      if (cookie.startsWith('navmenu=')) {\r\n        setNavmenu(cookie.substring('navmenu='.length));\r\n        break;\r\n      }\r\n    }\r\n    // end checkflag\r\n\r\n    const parallaxImage = document.querySelector('.parallax-image');\r\n\r\n    const handleScroll = () => {\r\n      const scrollPosition = window.scrollY;\r\n      const headerBottom = headerRef.current.getBoundingClientRect().bottom;\r\n      setIsHeaderVisible(window.pageYOffset < headerBottom);\r\n      parallaxImage.style.transform = `translateY(-${scrollPosition * 0.3}px)`;\r\n    };\r\n\r\n    window.addEventListener('scroll', handleScroll);\r\n\r\n    return () => {\r\n      window.removeEventListener('scroll', handleScroll);\r\n    };\r\n\r\n  }, [auth]);\r\n  const iframeSrc = process.env.REACT_APP_CHATBOT_URL;\r\n  // const fullHostname = window.location.hostname;\r\n  // const hostnameParts = fullHostname.split('.');\r\n  // const subdomain = hostnameParts.length > 2 ? hostnameParts[0] : '';\r\n  // const iframeSrc = (subdomain === 'dev' || subdomain === 'staging') ? 'https://staging.app.ai-pro.org/start-chatbot' : 'https://app.ai-pro.org/start-chatbot';\r\n\r\n  if(auth === undefined) return;\r\n  return (\r\n    <>\r\n      <Helmet>\r\n        <meta name=\"robots\" content=\"noindex, nofollow\" />\r\n        <title>AI Pro | ChatGPT - Your AI Language Expert</title>\r\n        <meta name=\"description\" content=\"Dive into seamless interactions and witness the future of AI-powered communication.\" />\r\n      </Helmet>\r\n      {navmenu === 'show' ? (\r\n        <Header hideNavLink={hideLinks} />\r\n      ) : (\r\n        <Headerlogo hideNavLink={hideLinks} />\r\n      )}\r\n        <div ref={headerRef}></div>\r\n        <BackToTopButton isHeaderVisible={isHeaderVisible} />\r\n        <div className=\"startchatgptgo block bg-gray-100\">\r\n\r\n          <div className=\"intro mx-auto pt-10 pb-0 md:pt-20\">\r\n            <div className=\"mainbanner\">\r\n              <div className=\"w-full md:w-full text-center pt-20 p-10 sm:p-20 lg:p-20 mb-20\">\r\n                <h1 className=\"text-4xl md:text-6xl mb-4 sm:pt-4 pb-8 font-bold drop-shadow-2xl\">Start Using<br/>ChatGPT</h1>\r\n                <p className=\"text-[16px] mb-4 w-full md:w-3/6 mx-auto\">\r\n                  Discover how to use ChatGPT to automate your workflow and tackle tedious tasks. Streamline your work and free up your time with ChatGPT’s powerful capabilities.<br/><br/> Explore ChatGPT’s various uses, learn the proper prompts, and get access to expert guides.\r\n                </p>\r\n                <motion.button\r\n                  onClick={checkSubscription}\r\n                  className=\"cta bg-blue-800 hover:bg-blue-700 mb-1 text-white mx-auto text-center font-bold py-3 px-6 my-3 rounded-2xl md:w-1/3\"\r\n                  whileHover={{ scale: 1.1 }}\r\n                  whileTap={{ scale: 0.9 }}\r\n                >\r\n                  Start Now\r\n                </motion.button>\r\n              </div>\r\n              <div className=\"introbg w-full pt-40 sm:pt-24 text-center mx-auto\">\r\n                <img src={introbg1} alt=\"AI-Pro\" className=\"w-5/6 lg:w-1/2 drop-shadow-xl rounded-2xl\" />\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"features relative mx-auto bg-white pt-12 p-0 sm:p-12\">\r\n            <div className=\"w-full\">\r\n              <iframe className=\"w-full\" id=\"chatgptv2demo\" title=\"ChatGPT V2 Demo\" src={iframeSrc} frameBorder=\"0\">\r\n              </iframe>\r\n            </div>\r\n            <div className=\"lg:justify-center text-center\">\r\n              <div className=\"w-full mt-24 block\">\r\n                <h2 className=\"text-3xl lg:text-6xl font-bold px-4\">\r\n                  Simplify Your Workflow\r\n                </h2>\r\n                <h4 className=\"text-2xl p-6\">Reinventing the Experience</h4>\r\n                <p className=\"mx-auto max-w-2xl mb-16 text-center px-4\">\r\n                  ChatGPT automates text generation, saving time & effort while improving consistency & accuracy. It can also integrate with other systems to streamline workflow.\r\n                </p>\r\n              </div>\r\n              <div className=\"cascadeimg relative w-full block\">\r\n                <img src={simpli1} alt=\"AI-Pro\" className=\"simpli1 absolute drop-shadow-xl\" />\r\n                  <img src={simpli2} alt=\"AI-Pro\" className=\"simpli2 absolute drop-shadow-xl\" />\r\n                <img src={simpli3} alt=\"AI-Pro\" className=\"simpli3 absolute drop-shadow-xl\" />\r\n                <img src={simpli4} alt=\"AI-Pro\" className=\"simpli4 absolute drop-shadow-xl\" />\r\n                  <img src={simpli5} alt=\"AI-Pro\" className=\"simpli5 absolute drop-shadow-xl\" />\r\n                <img src={simpli6} alt=\"AI-Pro\" className=\"simpli6 absolute drop-shadow-xl\" />\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"parallax-container\">\r\n            <div className=\"parallax-image\"></div>\r\n              <div className=\"parallax-content\">\r\n                <div className=\"lg:justify-center text-center\">\r\n                  <div className=\"pcont w-full mt-16 block text-white\">\r\n                    <div className=\"phead pt-10\">\r\n                      <h2 className=\"text-3xl lg:text-6xl font-bold\">\r\n                        ChatGPT Features\r\n                      </h2>\r\n                      <h4 className=\"text-2xl p-6\">Reinventing the Experience</h4>\r\n                    </div>\r\n                    <div className=\"flex flex-wrap justify-center text-left mx-auto\">\r\n\r\n                      <div className=\"pcontent grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4\">\r\n                        <div className=\"first col-span-1 sm:col-span-2 lg:col-span-1 block justify-center text-center lg:text-left items-end mx-8 p-8 sm:mr-0 lg:pt-[100px] pb-12\">\r\n                          <p className=\"block text-sm lg:text-md\">\r\n                            ChatGPT is a large language model trained by OpenAI that can generate human-like text. It can be fine-tuned for a variety of tasks such as conversation, summarization, question answering, and more. It's based on the transformer architecture and has 175 billion parameters. It can be used for both text generation and text completion tasks.\r\n                          </p>\r\n                          <motion.button\r\n                            onClick={draftClick}\r\n                            className=\"cta bg-white hover:bg-gray-100 mb-1 text-black block font-bold py-3 px-3 my-3 rounded-2xl w-full sm:w-1/2 mx-auto lg:mx-0 text-sm lg:text-md\"\r\n                            whileHover={{ scale: 1.1 }}\r\n                            whileTap={{ scale: 0.9 }}\r\n                          >\r\n                            See Features\r\n                          </motion.button>\r\n                        </div>\r\n\r\n                        <div className=\"second col-span-1 sm:col-span-1 lg:col-span-1 flex justify-center items-end\">\r\n                          <div className=\"flex justify-center items-center\">\r\n                            <img src={para1} alt=\"AI-Pro\" className=\"parallaximg max-w-full\" />\r\n                          </div>\r\n                        </div>\r\n\r\n                        <div className=\"third col-span-1 sm:col-span-1 lg:col-span-1 block justify-center items-end mx-8 p-8 lg:pt-[150px] pb-12 mx-auto\">\r\n                          <ul className=\"text-left\">\r\n                            <li><FaRegEdit className=\"inline text-lg mr-2\" /><a href=\"#anchor\" onClick={draftClick}>Draft Contracts</a></li>\r\n                            <li><FaFileSignature className=\"inline text-lg mr-2\" /><a href=\"#anchor\" onClick={scriptClick}>Create Scripts</a></li>\r\n                            <li><FaMailBulk className=\"inline text-lg mr-2\" /><a href=\"#anchor\" onClick={emailsClick}>Write Emails</a></li>\r\n                            <li><FaBookOpen className=\"inline text-lg mr-2\" /><a href=\"#anchor\" onClick={storiesClick}>Write Stories</a></li>\r\n                            <li><FaLaptopCode className=\"inline text-lg mr-2\" /><a href=\"#anchor\" onClick={promptsClick}>Use Prompts</a></li>\r\n                            <li><FaMoneyCheckAlt className=\"inline text-lg mr-2\" /><a href=\"#anchor\" onClick={moneyClick}>Make Money</a></li>\r\n                          </ul>\r\n                        </div>\r\n                      </div>\r\n\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n          </div>\r\n\r\n          <div ref={draftRef} className=\"cntblack bg-black text-white mx-auto block pt-0 pb-0 md:pt-20\">\r\n            <div className=\"block md:flex md:flex-row pt-20 pb-0 sm:py-20 md:py-0\">\r\n              <div className=\"w-full lg:w-1/2 text-left pt-0 px-10 lg:pt-8 lg:px-20\">\r\n                <h1 className=\"text-2xl lg:text-6xl mb-4 sm:pt-4 pb-8 font-bold drop-shadow-2xl\">Draft Contracts & Proposals</h1>\r\n                <p className=\"text-[16px] mb-4\">\r\n                  ChatGPT can help you create contracts and proposals more easily. This saves you time and effort because you don't have to start from scratch. ChatGPT can also check your drafts for mistakes and make sure they are formatted correctly. Make the process of creating contracts and proposals quicker and easier with ChatGPT.\r\n                </p>\r\n              </div>\r\n              <div className=\"lg:w-1/2 block sm:flex sm:flex-wrap\">\r\n                <div className=\"w-full md:w-1/2\">\r\n                  <img src={blkpic1} alt=\"AI-Pro\" className=\"blkpic h-[auto] mx-auto\" />\r\n                </div>\r\n                <div className=\"w-full md:w-1/2\">\r\n                  <img src={blkpic2} alt=\"AI-Pro\" className=\"blkpic h-[auto] mx-auto\" />\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div ref={scriptRef} className=\"cntblack2 bg-black text-white mx-auto block pt-0 pb-10 md:pt-20 \">\r\n            <div className=\"block md:flex md:flex-row py-20 md:py-0\">\r\n              <div className=\"w-full lg:w-1/2 inline-block\">\r\n                <img src={blkpic2a} alt=\"AI-Pro\" className=\"p-12\" />\r\n              </div>\r\n              <div className=\"w-full lg:w-1/2 text-left pt-0 px-10 lg:pt-40 lg:px-20\">\r\n                <h1 className=\"text-2xl lg:text-6xl mb-4 sm:pt-4 font-bold drop-shadow-2xl\">Create Video Scripts</h1>\r\n                <p className=\"text-[16px] mb-4\">\r\n                  ChatGPT can assist you in creating video scripts by automating the process of generating draft scripts based on certain prompts or keywords. ChatGPT can also help with editing and proofreading the generated drafts to ensure they are accurate and well-written. Utilize ChatGPT's capabilities to streamline the process of creating video scripts.\r\n                </p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div ref={emailsRef} className=\"cntblack3 bg-black text-white mx-auto block pt-0 pb-0 md:pt-20\">\r\n            <div className=\"block md:flex md:flex-row py-20 md:py-0\">\r\n              <div className=\"w-full lg:w-1/2 text-left pt-0 px-10 lg:pt-8 lg:px-20\">\r\n                <h1 className=\"text-2xl lg:text-6xl mb-4 sm:pt-4 pb-8 font-bold drop-shadow-2xl\">Write Emails & Chat Replies</h1>\r\n                <p className=\"text-[16px] mb-4\">\r\n                  ChatGPT can help you save time and effort by writing emails and chat replies for your work or business. ChatGPT can also assist with editing and proofreading the generated responses to ensure they are accurate and professional. Let ChatGPT handle your email and chat communication, freeing you up to focus on more important tasks.\r\n                </p>\r\n              </div>\r\n              <div className=\"w-full lg:w-1/2 inline-block\">\r\n                <img src={elonmuska} alt=\"elon musk chat\" className=\"blkpic w-full h-[auto]\" />\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n\r\n          <div ref={storiesRef} className=\"cntblack4 bg-black text-white mx-auto block pt-0 pb-10 md:pt-20\">\r\n            <div className=\"block md:flex md:flex-row py-20 md:py-0\">\r\n              <div className=\"w-full lg:w-1/2 inline-block\">\r\n                <img src={bkgpng} alt=\"AI-Pro\" className=\"p-12\" />\r\n              </div>\r\n              <div className=\"w-full lg:w-1/2 text-left pt-0 px-10 lg:pt-40 lg:px-20\">\r\n                <h1 className=\"text-2xl lg:text-6xl mb-4 sm:pt-4 font-bold drop-shadow-2xl\">Write Stories, Poems & Songs</h1>\r\n                <p className=\"text-[16px] mb-4\">\r\n                  ChatGPT is a powerful language model that can assist with writing tasks, including songwriting, poetry, stories, and books. Simply provide a prompt and let the model generate creative and original ideas. ChatGPT can write in various styles and tones. Try it out to boost creativity and productivity.\r\n                </p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n\r\n          <div ref={promptsRef} className=\"cntblack5 bg-black text-white mx-auto block pt-0 pb-0 md:pt-20\">\r\n            <div className=\"block md:flex md:flex-row py-20 md:py-0\">\r\n              <div className=\"w-full lg:w-1/2 text-left pt-0 px-10 lg:pt-8 lg:px-20\">\r\n                <h1 className=\"text-2xl lg:text-6xl mb-4 sm:pt-4 pb-8 font-bold drop-shadow-2xl\">Use Prompts & Commands</h1>\r\n                <p className=\"text-[16px] mb-4\">\r\n                  Explore our comprehensive resources and enhance your understanding of AI prompts and commands. From beginner to advanced, our guides will help you improve your skills and strengthen your communication with your AI. Get started now and take your AI capabilities to the next level.\r\n                </p>\r\n              </div>\r\n              <div className=\"block sm:flex sm:flex-wrap max-h-auto xs:max-h-[200px] sm:max-h-auto\">\r\n                <div className=\"inline-block\">\r\n                  <img src={aminpic1} alt=\"AI-Pro\" className=\"p-2 w-full sm:w-1/2 h-[auto] inline-block\" />\r\n                  <img src={aminpic2} alt=\"AI-Pro\" className=\"p-2 w-full sm:w-1/2 h-[auto] inline-block\" />\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n\r\n          <div ref={moneyRef} className=\"cntblack6 text-center bg-black text-white mx-auto block pt-0 pb-0 md:pt-20\">\r\n            <div className=\"block pb-[100px] mb-[300px]\">\r\n              <div className=\"w-full pt-0 px-10 lg:pt-8 lg:px-20\">\r\n                <h1 className=\"text-2xl lg:text-6xl mb-4 sm:pt-4 pb-8 font-bold drop-shadow-2xl\">Make Money with AI</h1>\r\n                <p className=\"text-[16px] mb-4\">\r\n                  Looking to leverage the power of AI to generate income? We will show you how to utilize AI to create new streams of revenue. You'll learn the skills and strategies you need to succeed in the lucrative world of AI. Don't miss out on this opportunity to transform your financial future with AI!\r\n                </p>\r\n              </div>\r\n              <div className=\"flex flex-wrap p-8 mt-8 mb-[-350px]\">\r\n                <div className=\"w-full sm:w-1/2 md:w-1/3 lg:w-1/3 xl:w-1/3\">\r\n                  <img src={l1} alt=\"AI-Pro\" className=\"mx-auto rounded-2xl\" />\r\n                </div>\r\n                <div className=\"w-full sm:w-1/2 md:w-1/3 lg:w-1/3 xl:w-1/3 my-6 md:mt-[120px]\">\r\n                  <img src={l2} alt=\"AI-Pro\" className=\"mx-auto rounded-2xl\" />\r\n                </div>\r\n                <div className=\"w-full sm:w-1/2 md:w-1/3 lg:w-1/3 xl:w-1/3\">\r\n                  <img src={l3} alt=\"AI-Pro\" className=\"mx-auto rounded-2xl\" />\r\n                </div>\r\n              </div>\r\n\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"useai mx-auto pt-10 z-1 my-12\">\r\n            <div className=\"flex flex-col items-center p-10 lg:pt-8 lg:pb-0 min-h-[470px]\">\r\n              <div className=\"w-full lg:w-5/6 p-6 text-center mx-4 lg:mx-0\">\r\n                <h2 className=\"text-white text-xl md:text-5xl font-bold text-center pb-6\">\r\n                  Use Artificial Intelligence\r\n                </h2>\r\n                <p className=\"text-md text-white text-center md:p-6 leading-8\">\r\n                  At AI-PRO, we believe that everyone should have access to the resources and guidance they need to succeed in the world of AI. That’s why we offer a variety of membership options to suit your needs and budget. Whether you’re an individual looking to learn about AI or a business looking to adopt AI solutions, we have a plan that’s right for you.\r\n                </p>\r\n                <motion.button\r\n                  onClick={checkSubscription}\r\n                  className=\"cta bg-white hover:bg-gray-100 mb-1 text-black mx-auto text-center font-bold py-3 px-6 my-3 rounded-2xl md:w-1/2\"\r\n                  whileHover={{ scale: 1.1 }}\r\n                  whileTap={{ scale: 0.9 }}\r\n                >\r\n                  Discover AI Now\r\n                </motion.button>\r\n              </div>\r\n            </div>\r\n            <div className=\"bg-gray-600 \">\r\n              <div className=\"useaibg flex flex-col items-center w-full text-center h-[400px]\">\r\n                <img src={lpPic1} alt=\"ChatGPT-Now AI\" className=\"mx-auto mt-2 md:mt-[-30px]\" />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      <Footer auth={auth} hideNavLink={hideLinks}/>\r\n    </>\r\n  );\r\n}\r\n\r\nexport default StartChatGptgo;", "// extracted by mini-css-extract-plugin\nexport default {};"], "names": ["_ref", "isHeaderVisible", "isVisible", "setIsVisible", "useState", "useEffect", "handleScroll", "window", "pageYOffset", "handleHeaderVisibilityChange", "addEventListener", "removeEventListener", "_jsx", "className", "children", "FaArrowCircleUp", "onClick", "scrollToTop", "scrollTo", "top", "behavior", "base_url", "process", "script", "document", "createElement", "src", "async", "onload", "body", "append<PERSON><PERSON><PERSON>", "_Fragment", "hideNavLink", "auth", "<PERSON><PERSON>", "pathname", "location", "isChatGptGo", "test", "isTexttoImage", "isChatGptV2", "isRegisterPage", "includes", "showLink", "showMaintenanceBanner", "REACT_APP_ShowMaintenanceBanner", "linkPNG", "createPreloadLink", "aiproLogo", "linkWebP", "aiproLogoWebP", "head", "append", "remove", "href", "as", "link", "rel", "_jsxs", "id", "type", "srcSet", "width", "height", "alt", "navmenu", "setNavmenu", "draftRef", "useRef", "scriptRef", "emailsRef", "storiesRef", "promptsRef", "moneyRef", "headerRef", "setIsHeaderVisible", "hideLinks", "Get<PERSON><PERSON><PERSON>", "checkSubscription", "status", "draftClick", "current", "scrollIntoView", "cookies", "cookie", "split", "i", "length", "trim", "startsWith", "substring", "parallaxImage", "querySelector", "scrollPosition", "scrollY", "headerBottom", "getBoundingClientRect", "bottom", "style", "transform", "iframeSrc", "REACT_APP_CHATBOT_URL", "undefined", "<PERSON><PERSON><PERSON>", "name", "content", "Header", "Headerlogo", "ref", "BackToTopButton", "motion", "button", "whileHover", "scale", "whileTap", "introbg1", "title", "frameBorder", "simpli1", "simpli2", "simpli3", "simpli4", "simpli5", "simpli6", "para1", "FaRegEdit", "FaFileSignature", "scriptClick", "FaMailBulk", "emailsClick", "FaBookOpen", "storiesClick", "FaLaptopCode", "promptsClick", "FaMoneyCheckAlt", "moneyClick", "blkpic1", "blkpic2", "blkpic2a", "elonmuska", "bkgpng", "aminpic1", "aminpic2", "l1", "l2", "l3", "lpPic1", "Footer"], "sourceRoot": ""}