{"version": 3, "file": "static/css/8859.f84b869a.chunk.css", "mappings": "AAAA,SACE,qBAAsB,CACtB,iBACF,CAKA,qBAOE,gBAAsD,CAAtD,6BAAsD,CAJtD,QAAS,CAFT,iBAAkB,CAClB,SAAU,CAEV,0BAIF,CAEA,uBACE,WACF,CAEA,+BACE,qBAAsB,CACtB,wBAAyB,CACzB,kBAAmB,CACnB,+BAAgC,CAGhC,eAAgB,CAFhB,aAAc,CACd,YAEF,CACA,8BAEE,gCAAiC,CAUjC,QAIF,CACA,2DAdE,4BAAmC,CACnC,6BAAoC,CACpC,YAAa,CACb,eAAgB,CAChB,aAAc,CACd,QAAS,CACT,SAAU,CACV,eAAgB,CAChB,iBAAkB,CAIlB,yBAA0B,CAF1B,UAAW,CACX,SAkBF,CAfA,6BACE,6BAA8B,CAO9B,QAOF,CAEA,qBAEE,gBAAiB,CADjB,iBAEF,CAEA,kBAAkB,UAAY,CAC9B,kBAAkB,SAAW,CAM7B,WAEI,yBAA2B,CAD3B,iBAEJ,CACA,iCACI,cACJ,CACA,4BAME,iBAAkB,CAFlB,gBAGF,CACA,sEAHE,aAAc,CAFd,eAAgB,CAFhB,gCAA0D,CAA1D,wDAA0D,CAC1D,eAAiC,CAAjC,+BAYF,CANA,0CAIE,iBAEF,CACA,qBACE,kBACF,CAEA,mCAGE,cAAe,CADf,YAAa,CADb,kBAGF,CAEA,mDACI,eACJ,CAEA,eACE,WAAY,CACZ,iBACF,CAEA,wFAGE,eACF,CAEA,8BAEE,kBACF,CAGA,mCAEE,WAAY,CADZ,iBAAkB,CAElB,wCACF,CAGA,SACE,eACF,CAEA,kBAEE,gBAAiB,CADjB,eAEF,CAEA,gBACE,qBAAuB,CACvB,2BACF,CAEA,yCACE,wBAAyB,CAIzB,WAAY,CAEZ,eAAgB,CAChB,+BAAgC,CANhC,eAAgB,CAIhB,oBAAiB,CAFjB,YAKF,CACA,6CAGI,aAAc,CAFd,kBAAmB,CACnB,UAEJ,CAEA,+CAGI,aAAc,CADd,iBAAkB,CADlB,UAGJ,CAEA,+EACE,YACF,CAEA,gBACE,gBACF,CAEA,yBACE,0CACE,eACF,CACA,uBACE,UACF,CACF,CACA,yBAIE,mCACI,oBACJ,CACF,CAEA,WACE,wBAAyB,CACzB,yDACF,CAEA,kFAEE,gBACF,CC9MA,2BAGE,iBAAkB,CADlB,iBAEF,CACA,iDAEE,cACF,CACA,2CAKE,aAAc,CAFd,eAAgB,CAFhB,gCAA0D,CAA1D,wDAA0D,CAC1D,eAAiC,CAAjC,+BAAiC,CAKjC,SAAU,CAHV,gBAAiB,CAEjB,iBAEF,CACA,qCACE,kBACF,CAEA,+BACE,WAAY,CACZ,iBACF,CAEA,8CACE,kBACF,CAEA,gCACE,qBAAuB,CACvB,2BAA6B,CAC7B,wBAEF,CAEA,kCAEE,gBAAiB,CADjB,eAEF,CAEA,uBACE,eACF,CAEA,sBACE,iBAAkB,CAClB,iBACF,CAEA,yBACE,2BACE,oBACF,CACF,CAEA,qBAIE,qBAAsB,CAHtB,wBAAyB,CAMzB,iBAAkB,CAJlB,WAAY,CASZ,gCAAiC,CAPjC,UAAW,CAKX,cAAe,CACf,gBAAiB,CAFjB,eAAgB,CAHhB,WAAY,CAJZ,iBAAkB,CAMlB,UAKF,CAEA,uDAEE,4BAAmC,CACnC,6BAAoC,CAGpC,YAAa,CACb,UAAW,CACX,aAAc,CACd,QAAS,CAJT,qBAAsB,CADtB,iBAAkB,CAMlB,yBACF,CAEA,4BACE,gCAAiC,CAEjC,YAAa,CADb,SAEF,CAEA,2BACE,6BAA8B,CAE9B,WAAY,CADZ,SAEF,CAEA,qBACE,YACF,CAEA,cAEE,cAAe,CADf,iBAEF,CAEA,yCACE,aACF", "sources": ["pricing/components/style.css", "pricing/css/style.css"], "sourcesContent": [".pricing {\r\n  background-color: #fff;\r\n  padding: 20px 10px;\r\n}\r\n.v-pricing .price_col, .v-pricing-02 .price_col {\r\n  /* width: 400px; */\r\n}\r\n\r\n.v-pricing-02 .arrow {\r\n  position: absolute;\r\n  top: -10px;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  border-width: 5px;\r\n  border-style: solid;\r\n  border-color: transparent transparent #000 transparent;\r\n}\r\n\r\n.v-pricing-02 .tooltip {\r\n  width: 320px;\r\n}\r\n\r\n.v-pricing-02 .tooltip_content {\r\n  background-color: #fff;\r\n  border: 3px solid #3b82f6;\r\n  border-radius: 10px;\r\n  box-shadow: 0 3px 15px 10px #fff;\r\n  margin: 0 auto;\r\n  padding: 10px;\r\n  line-height: 1.5;\r\n}\r\n.v-pricing-02 .tooltip:before {\r\n  border-bottom-color:rgba(0,0,0,0.095);\r\n  border-bottom: 15px solid #3b82f6;\r\n  border-left: 15px solid transparent;\r\n  border-right: 15px solid transparent;\r\n  border-top: 0;\r\n  content: \"\\0020\";\r\n  display: block;\r\n  height: 0;\r\n  left: 50px;\r\n  overflow: hidden;\r\n  position: absolute;\r\n  top: -3px;\r\n  width: 30px;\r\n  z-index: 2;\r\n  transform: rotate(-180deg);\r\n}\r\n.v-pricing-02 .tooltip:after {\r\n  border-bottom: 15px solid #fff;\r\n  border-left: 15px solid transparent;\r\n  border-right: 15px solid transparent;\r\n  border-top: 0;\r\n  content: \"\\0020\";\r\n  display: block;\r\n  height: 0;\r\n  top: -7px;\r\n  left: 50px;\r\n  overflow: hidden;\r\n  position: absolute;\r\n  width: 30px;\r\n  z-index: 2;\r\n  transform: rotate(-180deg);\r\n}\r\n\r\n.v-pricing-02 .items {\r\n  position: absolute;\r\n  margin-top: -30px;\r\n}\r\n\r\n.v-pricing-02 .p0{z-index: 99;}\r\n.v-pricing-02 .p1{z-index: 9;}\r\n\r\n.v-pricing-01 .price_col {\r\n  /* width: 500px; */\r\n}\r\n\r\n.price_col {\r\n    text-align: center;\r\n    max-width: 500px !important;\r\n}\r\n.price_col i.fas, .price_col i.fa {\r\n    font-size: 1rem;\r\n}\r\n.price_col ul li > div::before {\r\n  font-family: var(--fa-style-family, \"Font Awesome 6 Free\");\r\n  font-weight: var(--fa-style, 900);\r\n  content: \"\\f00c\";\r\n  margin-right: 5px;\r\n  color: #3b82f6;\r\n  font-style: normal;\r\n}\r\n.v-pricing-02 .price_col ul li > div::before {\r\n  font-family: var(--fa-style-family, \"Font Awesome 6 Free\");\r\n  font-weight: var(--fa-style, 900);\r\n  content: \"\\f00c\";\r\n  margin-right: 10px;\r\n  color: #3b82f6;\r\n}\r\n.price_col ul li>div {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.v-pricing-02 .price_col ul li>div {\r\n  margin-bottom: 10px;\r\n  display: flex;\r\n  cursor: pointer;\r\n}\r\n\r\n.v-pricing-02 .pricing-toggle .price_col ul li>div {\r\n    position: initial;\r\n}\r\n\r\n.price_col > div {\r\n  height: 100%;\r\n  position: relative;\r\n}\r\n\r\n.v-pricing-03 .price_col > div .price-content,\r\n.v-pricing-01 .price_col > div .price-content\r\n{\r\n  margin-bottom: 0;\r\n}\r\n\r\n.price_col > div .price-content\r\n{\r\n  margin-bottom: 50px;\r\n}\r\n\r\n\r\n.v-pricingdl .price_col > div button {\r\n  position: absolute;\r\n  bottom: 15px;\r\n  transform: translate(-50%, -50%) !important;\r\n}\r\n\r\n\r\n.btn-ent {\r\n  min-width: 156px;;\r\n}\r\n\r\n.plan-description {\r\n  text-align: left;\r\n  margin-left: 25px;\r\n}\r\n\r\n.span-highlight {\r\n  height: auto !important;\r\n  position: absolute !important;\r\n}\r\n\r\n.v-pricing-02 .mobile02 .tooltip_content {\r\n  background-color: #f9f9f9;\r\n  line-height: 1.5;\r\n  margin: 0 auto;\r\n  padding: 10px;\r\n  border: none;\r\n  margin-left: 15px;\r\n  border-radius: 0;\r\n  box-shadow: 0 3px 15px 10px #fff;\r\n}\r\n.v-pricing-02 .mobile02 .price_col ul li>div {\r\n    margin-bottom: 10px;\r\n    width: 100%;\r\n    display: table;\r\n}\r\n\r\n.v-pricing-02 .mobile02 .v-pricing-02 .tooltip {\r\n    width: 100%;\r\n    position: relative;\r\n    display: block;\r\n}\r\n\r\n.v-pricing-02 .mobile02 .tooltip:before, .v-pricing-02 .mobile02 .tooltip:after {\r\n  display: none;\r\n}\r\n\r\n.v-pricingdl ul {\r\n  min-height: 350px;\r\n}\r\n\r\n@media (max-width:768px){\r\n  .v-pricing-02 .price_col ul li > div::before {\r\n    content: \"\\f078\";\r\n  }\r\n  .v-pricing-02 .tooltip {\r\n    width: auto;\r\n  }\r\n}\r\n@media (max-width: 500px) {\r\n  .v-pricing-02 .mobile02 {\r\n      width: 100% !important;\r\n  }\r\n  .price_col {\r\n      width: 100% !important;\r\n  }\r\n}\r\n\r\n.button-bg {\r\n  background-color: #2872fa;\r\n  background: linear-gradient(to right, rgb(40, 114, 250), rgb(19, 70, 164)) rgb(40, 114, 250);\r\n}\r\n\r\n.pricing-description.text-left .indent,\r\n.pricing-description.text-left.ppg-119 div {\r\n  margin-left: 20px;\r\n}\r\n", ".pricing-toggle .price_col {\r\n  /* width: 400px; */\r\n  text-align: center;\r\n  position: relative;\r\n}\r\n.pricing-toggle .price_col i.fas,\r\n.price_col i.fa {\r\n  font-size: 1rem;\r\n}\r\n.pricing-toggle.price_col ul li > div::before {\r\n  font-family: var(--fa-style-family, \"Font Awesome 6 Free\");\r\n  font-weight: var(--fa-style, 900);\r\n  content: \"\\f00c\";\r\n  margin-right: 5px;\r\n  color: #3b82f6;\r\n  position: absolute;\r\n  left: 30px;\r\n}\r\n.pricing-toggle .price_col ul li > div {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.pricing-toggle .price_col > div {\r\n  height: 100%;\r\n  position: relative;\r\n}\r\n\r\n.pricing-toggle .price_col > div .price-content {\r\n  margin-bottom: 50px;\r\n}\r\n\r\n.pricing-toggle .span-highlight {\r\n  height: auto !important;\r\n  position: absolute !important;\r\n  width: calc(100% - 14rem);\r\n  /* padding: 10px; */\r\n}\r\n\r\n.pricing-toggle .plan-description {\r\n  text-align: left;\r\n  margin-left: 25px;\r\n}\r\n\r\n.price-content .p_main {\r\n  font-weight: bold;\r\n}\r\n\r\n.price-content .p_sub {\r\n  font-style: italic;\r\n  text-align: center;\r\n}\r\n\r\n@media (max-width: 500px) {\r\n  .pricing-toggle .price_col {\r\n    width: 100% !important;\r\n  }\r\n}\r\n\r\n.description-tooltip {\r\n  border: 3px solid #2872fa;\r\n  position: absolute;\r\n  bottom: 30px;\r\n  background-color: #fff;\r\n  color: #333;\r\n  padding: 5px;\r\n  border-radius: 5px;\r\n  width: auto;\r\n  max-width: 320px;\r\n  font-size: 11px;\r\n  margin-top: -35px;\r\n  box-shadow: 0 3px 15px -13px #000;\r\n}\r\n\r\n.description-tooltip:after,\r\n.description-tooltip:before {\r\n  border-left: 10px solid transparent;\r\n  border-right: 10px solid transparent;\r\n  position: absolute;\r\n  left: calc(50% - 10px);\r\n  border-top: 0;\r\n  content: \"\";\r\n  display: block;\r\n  height: 0;\r\n  transform: rotate(-180deg);\r\n}\r\n\r\n.description-tooltip:before {\r\n  border-bottom: 10px solid #2872fa;\r\n  z-index: 1;\r\n  bottom: -10px;\r\n}\r\n\r\n.description-tooltip:after {\r\n  border-bottom: 10px solid #fff;\r\n  z-index: 2;\r\n  bottom: -6px;\r\n}\r\n\r\n.description-tooltip {\r\n  display: none;\r\n}\r\n\r\n.hover-target {\r\n  position: relative;\r\n  cursor: pointer;\r\n}\r\n\r\n.hover-target:hover .description-tooltip {\r\n  display: block;\r\n}\r\n"], "names": [], "sourceRoot": ""}