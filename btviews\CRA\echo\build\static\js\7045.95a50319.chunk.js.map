{"version": 3, "file": "static/js/7045.95a50319.chunk.js", "mappings": "2NACO,SAASA,EAAYC,GACxB,OAAIA,EAC0B,QAA3BA,EAASC,cAAgC,IACd,QAA3BD,EAASC,cAAgC,IACd,QAA3BD,EAASC,cAAgC,IACd,QAA3BD,EAASC,eACkB,QAA3BD,EAASC,cADgC,KAErC,GANc,EAOzB,CAEO,SAASC,EAAWF,GACvB,OAAIA,EAC0B,QAA3BA,EAASC,eACkB,QAA3BD,EAASC,cADgC,KAEd,QAA3BD,EAASC,cAAgC,KACd,QAA3BD,EAASC,cAAgC,KACd,QAA3BD,EAASC,cAAgC,KACd,QAA3BD,EAASC,cAAgC,KACd,QAA3BD,EAASC,cAAgC,KACrC,KARc,EASzB,CAEO,SAASE,EAAWC,GACvB,IAAIA,EAAS,MAAO,GACpB,MACMC,EAAO,IAAIC,KAAKF,EAAQG,QAAQ,KAAK,MAE3C,MAHc,CAAC,UAAU,WAAW,QAAQ,QAAQ,MAAM,OAAO,OAAO,SAAS,YAAY,UAAU,WAAW,YAGrGF,EAAKG,YAAc,IAAMH,EAAKI,UAAY,IAAMJ,EAAKK,aACtE,CAEO,SAASC,EAASC,GACrB,OAAGA,EAAKC,YAAoBD,EAAKC,YAC9BD,EAAKE,MAAcF,EAAKE,MACpB,GACX,CAEO,SAASC,EAAiBC,GAC7B,OAAOA,EAAEC,WAAWV,QAAQ,wBAAyB,IACzD,CAEO,SAASW,EAAaC,GAC3B,MAAMC,EAAcC,WAAWF,GAC/B,OACMJ,EADEK,EAAc,GAAM,EACLA,EAAYE,QAAQ,GACpBF,EAAYE,QAAQ,GAC7C,CAEO,SAASC,EAAavB,EAAUc,GACnC,OAAId,GAAac,EAEdU,MAAMV,GAAe,GAErBO,WAAWP,IAAU,EACU,QAA3Bd,EAASC,cACDiB,EAAaJ,GAAOW,eAAe,SAAW,OACpB,QAA3BzB,EAASC,cACRiB,EAAaJ,GAAOW,eAAe,SAAW,MACpB,QAA3BzB,EAASC,cACRiB,EAAaJ,GAAOW,eAAe,SAAW,MACpB,QAA3BzB,EAASC,cACRiB,EAAaJ,GAAOW,eAAe,SAAW,MACpB,QAA3BzB,EAASC,cACRiB,EAAaJ,GAAOW,eAAe,SAAW,KACrB,QAA3BzB,EAASC,cACPiB,EAAaJ,GAAOW,eAAe,SAAW,MACrB,QAA3BzB,EAASC,cACP,KAAOiB,EAAaJ,GAAOW,eAAe,SAChB,QAA3BzB,EAASC,cACRiB,EAAaJ,GAAOW,eAAe,SAAW,KACpB,QAA3BzB,EAASC,cACRiB,EAAaJ,GAAOW,eAAe,SAAW,MACpB,QAA3BzB,EAASC,cACRiB,EAAaJ,GAAOW,eAAe,SAAW,KACpB,QAA3BzB,EAASC,cACRiB,EAAaJ,GAAOW,eAAe,SAAW,IAElD1B,EAAYC,GAAYkB,EAAaJ,GAAOW,eAAe,SAG/D,IAAM1B,EAAYC,KAAoC,EAAvBkB,EAAaJ,IAAaW,eAAe,SA/BhD,EAgCnC,CAEO,SAASC,EAAQC,EAAKC,GAEzBD,EAAM,IAAIrB,KAAKqB,GAEf,IAAIE,IADJD,EAAM,IAAItB,KAAKsB,IACAE,UAAYH,EAAIG,WAAa,IAE5C,OADAD,GAAQ,GACDE,KAAKC,IAAID,KAAKE,MAAMJ,GAC/B,CAEO,SAASK,EAAe9B,GAC3B,IAAIA,EAAS,MAAO,GACpB,MACMC,EAAO,IAAIC,KAAKF,EAAQG,QAAQ,KAAK,MAE3C,MAHc,CAAC,UAAU,WAAW,QAAQ,QAAQ,MAAM,OAAO,OAAO,SAAS,YAAY,UAAU,WAAW,YAGrGF,EAAKG,YAAc,IAAMH,EAAKI,UAAY,IAAMJ,EAAKK,cAAgB,IAAML,EAAK8B,WAAa,IAAM9B,EAAK+B,YACzH,CAEO,SAASC,EAAUC,GAAU,IAAT,KAACC,GAAKD,EAC3BE,EAAa,GACbC,EAAW,GAgBf,MAf8B,WAA1BF,EAAKG,mBACPF,EAAajB,EAAagB,EAAKvC,SAAUqB,WAAWkB,EAAKzB,MAAQ,KAAKQ,QAAQ,IAC9EmB,GAAWE,EAAAA,EAAAA,MAAA,OAAKC,MAAM,eAAcC,SAAA,CAAC,UAAQtB,EAAagB,EAAKvC,SAAUuC,EAAKzB,OAAO,YAGzD,YAA1ByB,EAAKG,mBACPF,EAAajB,EAAagB,EAAKvC,SAAUqB,WAAWkB,EAAKzB,MAAQ,IAAIQ,QAAQ,IAC7EmB,GAAWE,EAAAA,EAAAA,MAAA,OAAKC,MAAM,eAAcC,SAAA,CAAC,UAAQtB,EAAagB,EAAKvC,SAAUuC,EAAKzB,OAAO,aAGnFyB,EAAK1B,cACP2B,EAAajB,EAAagB,EAAKvC,SAAUqB,WAAWkB,EAAK1B,YAAc0B,EAAKO,YAAYxB,QAAQ,IAChGmB,GAAWE,EAAAA,EAAAA,MAAA,OAAKC,MAAM,eAAcC,SAAA,CAAC,UAAQtB,EAAagB,EAAKvC,SAAUuC,EAAK1B,kBAI9E8B,EAAAA,EAAAA,MAAAI,EAAAA,SAAA,CAAAF,SAAA,EACEF,EAAAA,EAAAA,MAAA,KAAGK,UAAW,qCAA+D,MAA1BC,EAAAA,EAAAA,IAAU,YAAsB,OAAS,IAAKJ,SAAA,CAC9FL,EAAW,KAACU,EAAAA,EAAAA,KAAA,QAAMF,UAAU,UAASH,SAAC,gBAExCJ,IAGP,CAEO,SAASU,EAAcC,GAAU,IAAT,KAACb,GAAKa,EACnC,MAA2B,QAAvBH,EAAAA,EAAAA,IAAU,SACLZ,EAAW,CAACE,SAEjBA,EAAK1B,aACCqC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wCAAuCH,SAAEtB,EAAagB,EAAKvC,SAAUuC,EAAK1B,gBAG/F8B,EAAAA,EAAAA,MAAA,KAAGK,UAAU,wCAAuCH,SAAA,CACjDtB,EAAagB,EAAKvC,SAAUuC,EAAKzB,QAClC6B,EAAAA,EAAAA,MAAA,QAAMK,UAAU,UAASH,SAAA,CAAC,IACK,YAA1BN,EAAKG,iBAAiC,QAAU,YAI3D,CAqBO,SAASW,IACd,MAAMC,EAAkBC,OAAOC,SAASC,KAExC,OAAIH,EAAgBI,QAAQ,YAAc,EAC/B,WACAJ,EAAgBI,QAAQ,QAAU,EAClC,OAGJ,EACT,C,2KCnKA,MAAMC,EAAUrB,IAAiF,IAAhF,sBAAEsB,EAAqB,0BAAEC,EAAyB,OAAEC,EAAM,cAAEC,GAAczB,EACzF,MAAO0B,EAAWC,IAAgBC,EAAAA,EAAAA,UAAS,KACpCC,EAAYC,IAAiBF,EAAAA,EAAAA,UAAS,KACtCG,EAAYC,IAAiBJ,EAAAA,EAAAA,UAAS,KACtCK,EAAYC,IAAiBN,EAAAA,EAAAA,UAAS,KACtCO,EAAWC,IAAgBR,EAAAA,EAAAA,UAAS,KACpCS,EAAYC,IAAiBV,EAAAA,EAAAA,UAAS,KACtCW,EAAYC,IAAiBZ,EAAAA,EAAAA,UAAS,KACtCa,EAAYC,IAAiBd,EAAAA,EAAAA,UAAS,KACtCe,EAAYC,IAAiBhB,EAAAA,EAAAA,UAAS,KACtCiB,EAAaC,IAAkBlB,EAAAA,EAAAA,UAAS,IAEzCmB,EAAa,oCACbC,EAAa,qCAyCbC,EAAoBC,IACxBd,EAAac,EAAMC,OAAOC,OAC1B3B,EAAe4B,IAAQ,CACrBC,UAAW,CACTC,OAAQP,EACRQ,gDAAiDN,EAAMC,OAAOC,WA0D9DK,EAA0D,YAA1BnC,GAA4D,KAArBI,EAAUgC,OACjFC,EAA2D,YAA1BrC,GAA6D,KAAtBO,EAAW6B,OACnFE,EAA2D,YAA1BtC,GAA6D,KAAtBS,EAAW2B,OACnFG,EAA2D,YAA1BvC,GAA6D,KAAtBW,EAAWyB,OACnFI,EAA0D,YAA1BxC,GAA4D,KAArBa,EAAUuB,OACjFK,EAA2D,YAA1BzC,GAA6D,KAAtBe,EAAWqB,OACnFM,EAA2D,YAA1B1C,GAA6D,KAAtBiB,EAAWmB,OACnFO,EAA2D,YAA1B3C,GAA6D,KAAtBmB,EAAWiB,OACnFQ,EAA2D,YAA1B5C,GAA6D,KAAtBqB,EAAWe,OACnFS,EAA0D,YAA1B7C,GAA8D,KAAvBuB,EAAYa,OACnFU,EACsB,KAA1B9C,GACAmC,GACAE,GACAC,GACAC,GACAC,GACAC,GACAC,GACAC,GACAC,GACAC,EAEF,OACE9D,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,MAAIK,UAAU,iBAAgBH,SAAA,CAAC,eAAWK,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcH,SAAC,UACzEF,EAAAA,EAAAA,MAAA,KAAGK,UAAU,mBAAkBH,SAAA,CAAC,yEAAqEK,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcH,SAAC,UACpIF,EAAAA,EAAAA,MAAA,SAAAE,SAAA,EAAOK,EAAAA,EAAAA,KAAA,SAAOyD,KAAK,QAAQ3D,UAAU,yBAAyB0C,MAAM,UAAUkB,QAAmC,YAA1BhD,EAAqCiD,SAAUhD,IAA4B,oBAGlKX,EAAAA,EAAAA,KAAA,UACAP,EAAAA,EAAAA,MAAA,SAAAE,SAAA,EAAOK,EAAAA,EAAAA,KAAA,SAAOyD,KAAK,QAAQ3D,UAAU,yBAAyB0C,MAAM,UAAUkB,QAAmC,YAA1BhD,EAAqCiD,SAAUhD,IAA4B,8BAGlKX,EAAAA,EAAAA,KAAA,UACAP,EAAAA,EAAAA,MAAA,SAAAE,SAAA,EAAOK,EAAAA,EAAAA,KAAA,SAAOyD,KAAK,QAAQ3D,UAAU,yBAAyB0C,MAAM,UAAUkB,QAAmC,YAA1BhD,EAAqCiD,SAAUhD,IAA4B,wCAGlKX,EAAAA,EAAAA,KAAA,UACAP,EAAAA,EAAAA,MAAA,SAAAE,SAAA,EAAOK,EAAAA,EAAAA,KAAA,SAAOyD,KAAK,QAAQ3D,UAAU,yBAAyB0C,MAAM,UAAUkB,QAAmC,YAA1BhD,EAAqCiD,SAAUhD,IAA4B,yCAGlKX,EAAAA,EAAAA,KAAA,UACAP,EAAAA,EAAAA,MAAA,SAAAE,SAAA,EAAOK,EAAAA,EAAAA,KAAA,SAAOyD,KAAK,QAAQ3D,UAAU,yBAAyB0C,MAAM,UAAUkB,QAAmC,YAA1BhD,EAAqCiD,SAAUhD,IAA4B,6BAGlKX,EAAAA,EAAAA,KAAA,UACAP,EAAAA,EAAAA,MAAA,OAAKK,UAAU,OAAMH,SAAA,CACQ,YAA1Be,IACCjB,EAAAA,EAAAA,MAAAI,EAAAA,SAAA,CAAAF,SAAA,EACEF,EAAAA,EAAAA,MAAA,SAAOK,UAAU,mBAAkBH,SAAA,CAAC,uCAAmCK,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcH,SAAC,UACtGK,EAAAA,EAAAA,KAAA,YAAUyD,KAAK,OAAO3D,UAAU,kEAC9B0C,MAAO1B,EACP6C,SA3JcrB,IACxBvB,EAAauB,EAAMC,OAAOC,OAC1B3B,EAAe4B,IAAQ,CACnBC,UAAW,CACTC,OARW,0BASXiB,6BAA8BtB,EAAMC,OAAOC,gBA2JlB,YAA1B9B,IACCjB,EAAAA,EAAAA,MAAAI,EAAAA,SAAA,CAAAF,SAAA,EACEF,EAAAA,EAAAA,MAAA,SAAOK,UAAU,mBAAkBH,SAAA,CAAC,8DAA0DK,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcH,SAAC,UAC7HK,EAAAA,EAAAA,KAAA,YAAUyD,KAAK,OAAO3D,UAAU,kEAC9B0C,MAAOvB,EACP0C,SA5JerB,IACzBpB,EAAcoB,EAAMC,OAAOC,OAC3B3B,EAAe4B,IAAQ,CACrBC,UAAW,CACTC,OAAQR,EACR0B,8CAA+CvB,EAAMC,OAAOC,cAyJxD/C,EAAAA,EAAAA,MAAA,SAAOK,UAAU,mBAAkBH,SAAA,CAAC,gDAA4CK,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcH,SAAC,UAC/GK,EAAAA,EAAAA,KAAA,YAAUyD,KAAK,OAAO3D,UAAU,kEAC9B0C,MAAOrB,EACPwC,SAxJerB,IACzBlB,EAAckB,EAAMC,OAAOC,OAC3B3B,EAAe4B,IAAQ,CACrBC,UAAW,CACTC,OAAQR,EACR0B,8CAA+CpB,EAASC,UAAUmB,8CAAgDpB,EAASC,UAAUmB,8CAAgD,GACrLC,mCAAoCxB,EAAMC,OAAOC,cAoJ7C/C,EAAAA,EAAAA,MAAA,SAAOK,UAAU,mBAAkBH,SAAA,CAAC,yCAAqCK,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcH,SAAC,UACxGK,EAAAA,EAAAA,KAAA,YAAUyD,KAAK,OAAO3D,UAAU,kEAC9B0C,MAAOnB,EACPsC,SAnJerB,IACzBhB,EAAcgB,EAAMC,OAAOC,OAC3B3B,EAAe4B,IAAQ,CACrBC,UAAW,CACTC,OAAQR,EACR0B,8CAA+CpB,EAASC,UAAUmB,8CAAgDpB,EAASC,UAAUmB,8CAAgD,GACrLC,mCAAoCrB,EAASC,UAAUoB,mCAAqCrB,EAASC,UAAUoB,mCAAqC,GACpJC,6BAA8BzB,EAAMC,OAAOC,gBAiJhB,YAA1B9B,IACCjB,EAAAA,EAAAA,MAAAI,EAAAA,SAAA,CAAAF,SAAA,EACEF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,OAAMH,SAAA,EACnBF,EAAAA,EAAAA,MAAA,SAAOK,UAAU,mBAAkBH,SAAA,CAAC,8DAA0DK,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcH,SAAC,UAC7HF,EAAAA,EAAAA,MAAA,SAAOK,UAAU,QAAOH,SAAA,EAACK,EAAAA,EAAAA,KAAA,SAAOyD,KAAK,QAAQ3D,UAAU,yBAAyB0C,MAAM,UAAUkB,QAAuB,YAAdnC,EAAyBoC,SAAUtB,IAAmB,cAG/J5C,EAAAA,EAAAA,MAAA,SAAOK,UAAU,QAAOH,SAAA,EAACK,EAAAA,EAAAA,KAAA,SAAOyD,KAAK,QAAQ3D,UAAU,yBAAyB0C,MAAM,SAASkB,QAAuB,WAAdnC,EAAwBoC,SAAUtB,IAAmB,aAG7J5C,EAAAA,EAAAA,MAAA,SAAOK,UAAU,QAAOH,SAAA,EAACK,EAAAA,EAAAA,KAAA,SAAOyD,KAAK,QAAQ3D,UAAU,yBAAyB0C,MAAM,SAASkB,QAAuB,WAAdnC,EAAwBoC,SAAUtB,IAAmB,gBAI/J5C,EAAAA,EAAAA,MAAA,SAAOK,UAAU,mBAAkBH,SAAA,CAAC,8BAA0BK,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcH,SAAC,UAC7FK,EAAAA,EAAAA,KAAA,YAAUyD,KAAK,OAAO3D,UAAU,kEAC9B0C,MAAOf,EACPkC,SArJerB,IACzBZ,EAAcY,EAAMC,OAAOC,OAC3B3B,EAAe4B,IAAQ,CACrBC,UAAW,CACTC,OAAQP,EACRQ,gDAAiDH,EAASC,UAAUE,gDAAkDH,EAASC,UAAUE,gDAAkD,GAC3LoB,sBAAuB1B,EAAMC,OAAOC,cAiJhC/C,EAAAA,EAAAA,MAAA,SAAOK,UAAU,mBAAkBH,SAAA,CAAC,kCAA8BK,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcH,SAAC,UACjGK,EAAAA,EAAAA,KAAA,YAAUyD,KAAK,OAAO3D,UAAU,kEAC9B0C,MAAOb,EACPgC,SAhJerB,IACzBV,EAAcU,EAAMC,OAAOC,OAC3B3B,EAAe4B,IAAQ,CACrBC,UAAW,CACTC,OAAQP,EACRQ,gDAAiDH,EAASC,UAAUE,gDAAkDH,EAASC,UAAUE,gDAAkD,GAC3LoB,sBAAuBvB,EAASC,UAAUsB,sBAAwBvB,EAASC,UAAUsB,sBAAwB,GAC7GC,yBAA0B3B,EAAMC,OAAOC,cA2InC/C,EAAAA,EAAAA,MAAA,SAAOK,UAAU,mBAAkBH,SAAA,CAAC,oFAAgFK,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcH,SAAC,UACnJK,EAAAA,EAAAA,KAAA,YAAUyD,KAAK,OAAO3D,UAAU,kEAC9B0C,MAAOX,EACP8B,SA1IerB,IACzBR,EAAcQ,EAAMC,OAAOC,OAC3B3B,EAAe4B,IAAQ,CACrBC,UAAW,CACTC,OAAQP,EACRQ,gDAAiDH,EAASC,UAAUE,gDAAkDH,EAASC,UAAUE,gDAAkD,GAC3LoB,sBAAuBvB,EAASC,UAAUsB,sBAAwBvB,EAASC,UAAUsB,sBAAwB,GAC7GC,yBAA0BxB,EAASC,UAAUuB,yBAA2BxB,EAASC,UAAUuB,yBAA2B,GACtHC,mEAAoE5B,EAAMC,OAAOC,cAoI7E/C,EAAAA,EAAAA,MAAA,SAAOK,UAAU,mBAAkBH,SAAA,CAAC,mFAA+EK,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcH,SAAC,UAClJK,EAAAA,EAAAA,KAAA,YAAUyD,KAAK,OAAO3D,UAAU,kEAC9B0C,MAAOT,EACP4B,SAnIerB,IACzBN,EAAcM,EAAMC,OAAOC,OAC3B3B,EAAe4B,IAAQ,CACrBC,UAAW,CACTC,OAAQP,EACRQ,gDAAiDH,EAASC,UAAUE,gDAAkDH,EAASC,UAAUE,gDAAkD,GAC3LoB,sBAAuBvB,EAASC,UAAUsB,sBAAwBvB,EAASC,UAAUsB,sBAAwB,GAC7GC,yBAA0BxB,EAASC,UAAUuB,yBAA2BxB,EAASC,UAAUuB,yBAA2B,GACtHE,+DAAgE7B,EAAMC,OAAOC,gBAgIlD,YAA1B9B,IACCjB,EAAAA,EAAAA,MAAAI,EAAAA,SAAA,CAAAF,SAAA,EACEK,EAAAA,EAAAA,KAAA,SAAOF,UAAU,sBACjBE,EAAAA,EAAAA,KAAA,YAAUyD,KAAK,OAAO3D,UAAU,kEAC9B0C,MAAOP,EACP0B,SAjIqBrB,IAC/BJ,EAAeI,EAAMC,OAAOC,OAC5B3B,EAAe4B,IAAQ,CACrBC,UAAW,CACTC,OAAQL,EAAMC,OAAOC,oBAmIvBxC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,iCAAgCH,UAC7CK,EAAAA,EAAAA,KAAA,UACEF,UAAW,+DAA8D0D,EAAmB,iCAAmC,8BAC/HY,QAASxD,EACTyD,SAAUb,EAAiB7D,SAC5B,wBAQH2E,EAAUpE,IAA6F,IAA5F,sBAAEqE,EAAqB,0BAAEC,EAAyB,OAAE5D,EAAM,WAAE6D,EAAU,cAAE5D,GAAcX,EACrG,MAAO+B,EAAaC,IAAkBlB,EAAAA,EAAAA,UAAS,IAWzCuC,EAA0D,eAA1BgB,GAAiE,KAAvBtC,EAAYa,OACtFU,EAA6C,KAA1Be,GAAgChB,EAEzD,OACE9D,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,MAAIK,UAAU,iBAAgBH,SAAA,CAAC,cAAUK,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcH,SAAC,UACxEF,EAAAA,EAAAA,MAAA,KAAGK,UAAU,mBAAkBH,SAAA,CAAC,2CAAuCK,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcH,SAAC,UACtGF,EAAAA,EAAAA,MAAA,SAAAE,SAAA,EAAOK,EAAAA,EAAAA,KAAA,SAAOyD,KAAK,QAAQ3D,UAAU,yBAAyB0C,MAAM,aAAakB,QAAmC,eAA1Ba,EAAwCZ,SAAUa,IAA4B,mBAGxKxE,EAAAA,EAAAA,KAAA,UACAP,EAAAA,EAAAA,MAAA,SAAAE,SAAA,EAAOK,EAAAA,EAAAA,KAAA,SAAOyD,KAAK,QAAQ3D,UAAU,yBAAyB0C,MAAM,aAAakB,QAAmC,eAA1Ba,EAAwCZ,SAAUa,IAA4B,sBAGxKxE,EAAAA,EAAAA,KAAA,UACAP,EAAAA,EAAAA,MAAA,SAAAE,SAAA,EAAOK,EAAAA,EAAAA,KAAA,SAAOyD,KAAK,QAAQ3D,UAAU,yBAAyB0C,MAAM,aAAakB,QAAmC,eAA1Ba,EAAwCZ,SAAUa,IAA4B,qBAGxKxE,EAAAA,EAAAA,KAAA,UACAP,EAAAA,EAAAA,MAAA,SAAAE,SAAA,EAAOK,EAAAA,EAAAA,KAAA,SAAOyD,KAAK,QAAQ3D,UAAU,yBAAyB0C,MAAM,aAAakB,QAAmC,eAA1Ba,EAAwCZ,SAAUa,IAA4B,0CAGxKxE,EAAAA,EAAAA,KAAA,UACAP,EAAAA,EAAAA,MAAA,SAAAE,SAAA,EAAOK,EAAAA,EAAAA,KAAA,SAAOyD,KAAK,QAAQ3D,UAAU,yBAAyB0C,MAAM,aAAakB,QAAmC,eAA1Ba,EAAwCZ,SAAUa,IAA4B,6BAGxKxE,EAAAA,EAAAA,KAAA,UACAA,EAAAA,EAAAA,KAAA,OAAKF,UAAU,OAAMH,SACQ,eAA1B4E,IACC9E,EAAAA,EAAAA,MAAAI,EAAAA,SAAA,CAAAF,SAAA,EACEK,EAAAA,EAAAA,KAAA,SAAOF,UAAU,sBACjBE,EAAAA,EAAAA,KAAA,YAAUyD,KAAK,OAAO3D,UAAU,kEAC9B0C,MAAOP,EACP0B,SA3CqBrB,IAC/BJ,EAAeI,EAAMC,OAAOC,OAC5B3B,EAAe4B,IAAQ,IAClBA,EACHiC,UAAW,CACT/B,OAAQL,EAAMC,OAAOC,mBA4CvBxC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wBAAuBH,UACpCK,EAAAA,EAAAA,KAAA,UACEF,UAAU,qFACVsE,QAASK,EAAW9E,SACrB,yBAKHK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,iCAAgCH,UAC7CK,EAAAA,EAAAA,KAAA,UACEF,UAAW,+DAA8D0D,EAAmB,iCAAmC,8BAC/HY,QAASxD,EACTyD,SAAUb,EAAiB7D,SAC5B,wBAQHgF,EAAYC,IAA8H,IAA7H,KAAEC,EAAI,wBAAEC,EAAuB,2BAAEC,EAA0B,4BAAEC,EAA2B,WAAEP,EAAU,SAAEQ,EAAQ,MAAEC,GAAON,EACxI,MAAMO,EAAgD,eAA5BL,EACpBtB,EAA+C,KAA5BsB,GAAkCK,EAK3D,IAAIC,EAAkB,IAuBtB,MAtBoB,QAAhBP,EAAK/H,UAEiB,QAAhB+H,EAAK/H,UAEW,QAAhB+H,EAAK/H,SAHbsI,EAAkB,KAKM,QAAhBP,EAAK/H,SACbsI,EAAkB,KACM,QAAhBP,EAAK/H,SACbsI,EAAkB,MACM,QAAhBP,EAAK/H,SACbsI,EAAkB,OACM,QAAhBP,EAAK/H,SACbsI,EAAkB,KACM,QAAhBP,EAAK/H,SACbsI,EAAkB,IACM,QAAhBP,EAAK/H,SACbsI,EAAkB,KACM,QAAhBP,EAAK/H,WACbsI,EAAkB,OAIlB3F,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,MAAIK,UAAU,iBAAgBH,SAAA,CAAC,eAAWK,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcH,SAAC,UACzEF,EAAAA,EAAAA,MAAA,KAAGK,UAAU,mBAAkBH,SAAA,CAAC,+GAA2GK,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcH,SAAC,UAC1KF,EAAAA,EAAAA,MAAA,SAAAE,SAAA,EAAOK,EAAAA,EAAAA,KAAA,SAAOyD,KAAK,QAAQ3D,UAAU,yBAAyB0C,MAAM,aAAakB,QAASyB,EAAmBxB,SAAUqB,IAA8B,UAGrJhF,EAAAA,EAAAA,KAAA,UACAP,EAAAA,EAAAA,MAAA,SAAAE,SAAA,EAAOK,EAAAA,EAAAA,KAAA,SAAOyD,KAAK,QAAQ3D,UAAU,yBAAyB0C,MAAM,aAAakB,QAAqC,eAA5BoB,EAA0CnB,SAAUqB,IAA8B,SAG5KhF,EAAAA,EAAAA,KAAA,UACAA,EAAAA,EAAAA,KAAA,OAAKF,UAAU,sCAAqCH,SACrB,eAA5BmF,IACCrF,EAAAA,EAAAA,MAAAI,EAAAA,SAAA,CAAAF,SAAA,EACEK,EAAAA,EAAAA,KAAA,OAAKF,UAAW,uEAAkG,eAA5BgF,EAA2C,QAAU,UACzIV,QA3CaiB,KACvBN,EAA2B,QA4CnBtF,EAAAA,EAAAA,MAAA,OAAKK,UAAW,gLAA2M,eAA5BgF,EAA2C,QAAU,UAAWnF,SAAA,EAC7PK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uDAAsDH,SAAC,2BAGtEK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,sCAEdE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,OAAMH,UACnBF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,yCAAwCH,SAAA,EAErDK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,UAASH,UACtBF,EAAAA,EAAAA,MAAA,KAAGK,UAAU,8FAA6FH,SAAA,CAAC,aAChGK,EAAAA,EAAAA,KAAA,KAAAL,SAAG,kBAAiB,kBAAcK,EAAAA,EAAAA,KAAA,SAAM,eACpC,KACbA,EAAAA,EAAAA,KAAA,QAAMF,UAAU,4CAA2CH,SAAC,UAAY,uBAM5EK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,iLAAgLH,UAC7LF,EAAAA,EAAAA,MAAA,KAAGK,UAAU,mFAAkFH,SAAA,EAC7FK,EAAAA,EAAAA,KAAA,QAAMF,UAAU,iEAAgEH,UAC7EtB,EAAAA,EAAAA,IAAawG,EAAK/H,SAAUsI,MAE/BpF,EAAAA,EAAAA,KAAA,QAAMF,UAAU,4CAA2CH,SAAC,sBAOpEK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,sCACdE,EAAAA,EAAAA,KAACsF,EAAAA,EAAOC,OAAM,CACZzF,UAAU,yIACVsE,QAASc,EACTM,WAAY,CAAEC,MAAO,KAAM9F,SAC5B,qBAIDF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,kEAAiEH,SAAA,EAC9EK,EAAAA,EAAAA,KAAA,KAAAL,SAAG,sKAGHF,EAAAA,EAAAA,MAAA,KAAAE,SAAA,EACEK,EAAAA,EAAAA,KAAA,QAAMF,UAAU,yBAAwBH,SAAC,oBAAsB,6IAInEK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,sCAEdL,EAAAA,EAAAA,MAAA,OAAKK,UAAU,uGAAsGH,SAAA,EAEnHF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,gFAA+EH,SAAA,EAC5FK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,+BAA8BH,SAAC,kBAC7CK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,mDAAkDH,UAC9DF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,+CAA8CH,SAAA,EAE3DF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,kBAAiBH,SAAA,EAC9BK,EAAAA,EAAAA,KAAA,MAAAL,SAAI,aACJK,EAAAA,EAAAA,KAAA,MAAAL,SAAI,eACJK,EAAAA,EAAAA,KAAA,MAAAL,SAAI,eACJK,EAAAA,EAAAA,KAAA,MAAAL,SAAI,kBACJK,EAAAA,EAAAA,KAAA,MAAAL,SAAI,eAGNF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,WAAUH,SAAA,EACvBK,EAAAA,EAAAA,KAAA,MAAAL,SAAI,kBACJK,EAAAA,EAAAA,KAAA,MAAAL,SAAI,cACJK,EAAAA,EAAAA,KAAA,MAAAL,SAAI,gBACJK,EAAAA,EAAAA,KAAA,MAAAL,SAAI,wBAOZF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,mEAAkEH,SAAA,EAC/EK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,+BAA8BH,SAAC,0BAC7CF,EAAAA,EAAAA,MAAA,MAAIK,UAAU,mDAAkDH,SAAA,EAC9DK,EAAAA,EAAAA,KAAA,MAAAL,SAAI,gBACJK,EAAAA,EAAAA,KAAA,MAAAL,SAAI,uBACJK,EAAAA,EAAAA,KAAA,MAAAL,SAAI,kBACJK,EAAAA,EAAAA,KAAA,MAAAL,SAAI,eACJK,EAAAA,EAAAA,KAAA,MAAAL,SAAI,oCAWlBK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wBAAuBH,UACpCK,EAAAA,EAAAA,KAAA,UACEF,UAAU,qFACVsE,QAASK,EAAW9E,SACrB,yBAIHK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,iCAAgCH,UAC7CK,EAAAA,EAAAA,KAAA,UACEF,UAAW,+DAA8D0D,EAAmB,iCAAmC,8BAC/HY,QAASa,EACTZ,SAAUb,EAAiB7D,SAC5B,iBAmOT,QA3NA,YACE+F,EAAAA,EAAAA,WAAU,KACRC,IAAAA,QAAiB,CACfC,cAAe,qBAEhB,IACH,MAAOC,EAAaC,IAAkB9E,EAAAA,EAAAA,UAAS,IACxCN,EAAuBqF,IAA4B/E,EAAAA,EAAAA,UAAS,KAC5DuD,EAAuByB,IAA4BhF,EAAAA,EAAAA,UAAS,KAC5D8D,EAAyBC,IAA8B/D,EAAAA,EAAAA,UAAS,KAChEiF,EAAYpF,IAAiBG,EAAAA,EAAAA,UAAS,CAAC,GACxC6D,GAAOqB,EAAAA,EAAAA,MAEb,QAAYC,IAATtB,EAAoB,OACvB,IAAY,IAATA,EAED,YADAxE,OAAOC,SAASC,KAAO,UAIzB,GAAwB,KAApBsE,EAAKuB,YAAyC,OAApBvB,EAAKuB,YAAwC,WAAhBvB,EAAKwB,OAE5D,YADAhG,OAAOC,SAASC,KAAO,cAM3B,MAiDM+F,EAAiBA,KACD,IAAhBT,GAA+C,KAA1BnF,GAA0D,KAA1B6D,GAA4D,KAA5BO,GAGzFgB,EAAgBS,GAAaA,EAAW,IAGpCC,EAAaA,KACG,IAAhBX,GAA+C,KAA1BnF,GAA0D,KAA1B6D,GAA4D,KAA5BO,GAGzFgB,EAAgBS,GAAaA,EAAW,IA0C1C,SAASE,EAASC,EAAIC,GACpB,MAAMC,EAAeD,EAAgB,IAAM,IACrCjJ,EAAOmJ,KAAKC,UAAUb,GAC5Bc,EAAAA,EAAMC,KAAK,wCAAgD,CACzDtJ,OAAMkJ,aAAaA,GAClB,CAAEK,QAAS,CAAE,eAAgB,uCAAyCC,KAAK,SAASC,GACrF,IAAIC,EAASD,EAAIzJ,KAEd0J,EAAOC,QACLX,EACDrG,OAAOC,SAASC,KAAOmG,EAGvBf,IAAAA,QAAe,kCAGjBA,IAAAA,MAAayB,EAAO1J,KAGxB,EACF,CAoBA,OACE+B,EAAAA,EAAAA,MAAAI,EAAAA,SAAA,CAAAF,SAAA,EACEF,EAAAA,EAAAA,MAAC6H,EAAAA,EAAM,CAAA3H,SAAA,EACLK,EAAAA,EAAAA,KAAA,QAAMuH,KAAK,SAASC,QAAQ,uBAC5BxH,EAAAA,EAAAA,KAAA,SAAAL,SAAO,kCACPK,EAAAA,EAAAA,KAAA,QAAMuH,KAAK,cAAcC,QAAQ,4BAEnCxH,EAAAA,EAAAA,KAACyH,EAAAA,QAAM,CAAC5C,KAAMA,KACd7E,EAAAA,EAAAA,KAAA,OAAKF,UAAU,kFAAiFH,UAC9FK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,6DAA4DH,UACzEK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,+BAA8BH,UAC3CK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uBAAsBH,UACrCF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,gDAA+CH,SAAA,EAC1DK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,+CAA8CH,SAAC,wBAC5C,IAAhBkG,IACC7F,EAAAA,EAAAA,KAACS,EAAO,CACNC,sBAAuBA,EACvBC,0BAhGiB2B,IACjCyD,EAAyBzD,EAAMC,OAAOC,OAEtC,MAAMkF,EAAmC,OAA7BpF,EAAMC,OAAOoF,YAAuBrF,EAAMC,OAAOoF,YAAYC,YAAc,GACvF/G,EAAe4B,IAAQ,CACnBC,UAAW,CACTC,OAAQ+E,OA2FEzB,WAAYA,EACZpF,cAAeA,EACfD,OAAQ4F,IAEK,IAAhBX,IACC7F,EAAAA,EAAAA,KAACsE,EAAO,CACNC,sBAAuBA,EACvBC,0BA7FiBlC,IACjC0D,EAAyB1D,EAAMC,OAAOC,OACtC,MAAMkF,EAAmC,OAA7BpF,EAAMC,OAAOoF,YAAuBrF,EAAMC,OAAOoF,YAAYC,YAAc,GACvF/G,EAAe4B,IAAQ,IAClBA,EACHiC,UAAW,CACT/B,OAAQ+E,OAwFI7G,cAAeA,EACf4D,WAAY6B,EACZ1F,OAAQ4F,IAEK,IAAhBX,IACC7F,EAAAA,EAAAA,KAAC2E,EAAS,CACRG,wBAAyBA,EACzBC,2BAA4BA,EAC5BC,4BA3FmB1C,IACnCyC,EAA2BzC,EAAMC,OAAOC,OACxC,MAAMkF,EAAmC,OAA7BpF,EAAMC,OAAOoF,YAAuBrF,EAAMC,OAAOoF,YAAYC,YAAc,GACvF/G,EAAe4B,IAAQ,IAClBA,EACHoF,UAAW,CACTlF,OAAQ+E,OAsFIjD,WAAY6B,EACZrB,SAlFG6C,KACnBC,SAASC,cAAc,qBAAqBC,UAAUC,IAAI,UAC1DzB,IA2BAM,EAAAA,EAAMC,KAAK,gDAAwD,CACjE,EAAG,CAAEC,QAAS,CAAE,eAAgB,uCAAyCC,KAAK,SAASC,GACrF,IAAIC,EAASD,EAAIzJ,KACjB,GAAG0J,EAAOC,QAMR,OALA1B,IAAAA,QAAe,6CACfwC,WAAW,WACPJ,SAASC,cAAc,qBAAqBC,UAAUG,OAAO,UAC/D/H,OAAOC,SAASC,KAAO,aACzB,EAAG,KAGF6G,EAAO1J,MAAMiI,IAAAA,MAAayB,EAAO1J,KAAK2K,IAC3C,IA0CcnD,MAnLAoD,KAEhB,OAAOzD,EAAK/H,SAASC,eACnB,IAAK,MA0CL,QAAS0J,EAAS,iBAAgB,SAvClC,IAAK,MACHA,EAAS,iBAAgB,GAC3B,MACA,IAAK,MACHA,EAAS,iBAAgB,GAC3B,MACA,IAAK,MACHA,EAAS,iBAAgB,GAC3B,MACA,IAAK,MACHA,EAAS,iBAAgB,GAC3B,MACA,IAAK,MACHA,EAAS,iBAAgB,GAC3B,MACA,IAAK,MACHA,EAAS,kBAAiB,GAC5B,MACA,IAAK,MACHA,EAAS,kBAAiB,GAC5B,MACA,IAAK,MACHA,EAAS,kBAAiB,GAC5B,MACA,IAAK,MACHA,EAAS,kBAAiB,GAC5B,MACA,IAAK,MACHA,EAAS,kBAAiB,GAC5B,MACA,IAAK,MACHA,EAAS,kBAAiB,GAC5B,MACA,IAAK,MACHA,EAAS,kBAAkB,GAC7B,MACA,IAAK,MACHA,EAAS,kBAAkB,KAyIf5B,KAAMA,iBAW1B,C", "sources": ["core/utils/main.jsx", "survey/index.jsx"], "sourcesContent": ["import { GetCookie } from '../../core/utils/cookies';\r\nexport function getCurrency(currency) {\r\n    if(!currency) return \"\";\r\n    if(currency.toLowerCase() === 'usd') return \"$\";\r\n    if(currency.toLowerCase() === 'eur') return \"€\";\r\n    if(currency.toLowerCase() === 'gbp') return \"£\";\r\n    if(currency.toLowerCase() === 'brl') return \"R$\";\r\n    if(currency.toLowerCase() === 'sar') return \"R$\";\r\n    return \"\";\r\n}\r\n\r\nexport function getCountry(currency) {\r\n    if(!currency) return \"\";\r\n    if(currency.toLowerCase() === 'usd') return \"US\";\r\n    if(currency.toLowerCase() === 'eur') return \"US\";\r\n    if(currency.toLowerCase() === 'gbp') return \"GB\";\r\n    if(currency.toLowerCase() === 'brl') return \"US\";\r\n    if(currency.toLowerCase() === 'sar') return \"AE\";\r\n    if(currency.toLowerCase() === 'chf') return \"CH\";\r\n    if(currency.toLowerCase() === 'sek') return \"SE\";\r\n    return \"US\";\r\n}\r\n\r\nexport function formatDate(dateStr) {\r\n    if(!dateStr) return \"\";\r\n    const month = [\"January\",\"February\",\"March\",\"April\",\"May\",\"June\",\"July\",\"August\",\"September\",\"October\",\"November\",\"December\"];\r\n    const date = new Date(dateStr.replace(/ /g,\"T\"));\r\n\r\n    return month[date.getMonth()] + \" \" + date.getDate() + \" \" + date.getFullYear();\r\n}\r\n\r\nexport function getPrice(data) {\r\n    if(data.trial_price) return data.trial_price;\r\n    if(data.price) return data.price;\r\n    return \"0\";\r\n}\r\n\r\nexport function numberWithCommas(x) {\r\n    return x.toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, \",\");\r\n}\r\n\r\nexport function formatNumber(number) {\r\n  const floatNumber = parseFloat(number);\r\n  return (floatNumber % 1 === 0)\r\n      ? numberWithCommas(floatNumber.toFixed(0))\r\n      : numberWithCommas(floatNumber.toFixed(2)); \r\n}\r\n\r\nexport function getPricePlan(currency, price) {\r\n    if(!currency || !price) return \"\";\r\n    // Check if price is a number\r\n    if(isNaN(price)) return \"\";\r\n    // Check if price is positive number\r\n    if(parseFloat(price) >= 0) {\r\n        if(currency.toLowerCase() === 'sar') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"ر.س.\";\r\n        } else if(currency.toLowerCase() === 'aed') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"AED\";\r\n        } else if(currency.toLowerCase() === 'chf') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"CHF\";\r\n        } else if(currency.toLowerCase() === 'sek') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"SEK\";\r\n        } else if(currency.toLowerCase() === 'pln') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"zł\";\r\n        }else if(currency.toLowerCase() === 'ron') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"LEI\";\r\n        }else if(currency.toLowerCase() === 'czk') {\r\n            return \"Kč\" + formatNumber(price).toLocaleString(\"en-US\");\r\n        } else if(currency.toLowerCase() === 'huf') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"Ft\";\r\n        } else if(currency.toLowerCase() === 'dkk') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"kr.\";\r\n        } else if(currency.toLowerCase() === 'bgn') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"лв\";\r\n        } else if(currency.toLowerCase() === 'try') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"₺\";\r\n        }\r\n        return getCurrency(currency) + formatNumber(price).toLocaleString(\"en-US\");\r\n    }\r\n    // Negative number\r\n    return \"-\" + getCurrency(currency) + (formatNumber(price) * -1).toLocaleString(\"en-US\");\r\n}\r\n\r\nexport function diffMin(dt1, dt2)\r\n{\r\n    dt1 = new Date(dt1);\r\n    dt2 = new Date(dt2);\r\n    var diff =(dt2.getTime() - dt1.getTime()) / 1000;\r\n    diff /= 60;\r\n    return Math.abs(Math.round(diff));\r\n}\r\n\r\nexport function formatDateTime(dateStr) {\r\n    if(!dateStr) return \"\";\r\n    const month = [\"January\",\"February\",\"March\",\"April\",\"May\",\"June\",\"July\",\"August\",\"September\",\"October\",\"November\",\"December\"];\r\n    const date = new Date(dateStr.replace(/ /g,\"T\"));\r\n\r\n    return month[date.getMonth()] + \" \" + date.getDate() + \" \" + date.getFullYear() + \" \" + date.getHours() + \":\" + date.getMinutes();\r\n}\r\n\r\nexport function DailyPrice({plan}) {\r\n  let dailyPrice = '';\r\n  let interval = '';\r\n  if (plan.payment_interval === 'Yearly') {\r\n    dailyPrice = getPricePlan(plan.currency, parseFloat(plan.price / 365).toFixed(2));\r\n    interval = <div class=\"text-xs mb-4\">billed {getPricePlan(plan.currency, plan.price)}/year</div>;\r\n  }\r\n\r\n  if (plan.payment_interval === 'Monthly') {\r\n    dailyPrice = getPricePlan(plan.currency, parseFloat(plan.price / 30).toFixed(2));\r\n    interval = <div class=\"text-xs mb-4\">billed {getPricePlan(plan.currency, plan.price)}/Month</div>;\r\n  }\r\n\r\n  if (plan.trial_price) {\r\n    dailyPrice = getPricePlan(plan.currency, parseFloat(plan.trial_price / plan.trial_days).toFixed(2));\r\n    interval = <div class=\"text-xs mb-4\">billed {getPricePlan(plan.currency, plan.trial_price)}</div>;\r\n  }\r\n\r\n  return (\r\n    <>\r\n      <p className={`text-4xl font-bold text-gray-800 ${(GetCookie(\"p_toggle\") === '') ? 'mb-4' : ''}`}>\r\n        {dailyPrice} <span className=\"text-sm\"> per Day</span>\r\n      </p>\r\n      {interval}\r\n    </>\r\n  );\r\n}\r\n\r\nexport function PriceFormatted({plan}) {\r\n  if (GetCookie(\"daily\") === 'on') {\r\n    return DailyPrice({plan});\r\n  } \r\n  if (plan.trial_price) {\r\n    return (<p className=\"text-4xl font-bold text-gray-800 mb-4\">{getPricePlan(plan.currency, plan.trial_price)}</p>)\r\n  } \r\n  return (\r\n    <p className=\"text-4xl font-bold text-gray-800 mb-4\">\r\n      {getPricePlan(plan.currency, plan.price)}\r\n      <span className=\"text-sm\"> \r\n        /{ plan.payment_interval === \"Monthly\" ? \"month\" : \"year\" }\r\n      </span>\r\n    </p>\r\n  )\r\n}\r\n\r\nexport function displayTextFormatted(plan) {\r\n  let payment_interval = plan.payment_interval === 'Yearly' ?  365 : 30;\r\n  let trialPricePer = `\r\n    then only ${getPricePlan(plan.currency, plan.price)} per month\r\n  `;\r\n\r\n  if (GetCookie(\"daily\") === 'on') {\r\n    trialPricePer = `\r\n      then only ${getPricePlan(plan.currency, parseFloat(plan.price / payment_interval).toFixed(2))} per day<br>\r\n      (billed ${getPricePlan(plan.currency, plan.price)} ${plan.payment_interval})\r\n    `;\r\n  }\r\n\r\n  return plan.display_txt2\r\n        .replace(\"{trialDays}\", plan.trial_days)\r\n        .replace(\"{trialPricePer}\", trialPricePer);\r\n  \r\n}\r\n\r\nexport function getPrefixLocation() {\r\n  const currentLocation = window.location.href;\r\n\r\n  if (currentLocation.indexOf(\"staging\") > -1) {\r\n      return \"staging.\";\r\n  } else if (currentLocation.indexOf(\"dev\") > -1) {\r\n      return \"dev.\";\r\n  }\r\n\r\n  return \"\"\r\n}", "import React, { useState, useEffect } from 'react';\r\nimport './style.css';\r\nimport Header from '../header';\r\nimport { Auth } from '../core/utils/auth';\r\nimport { Helmet } from 'react-helmet';\r\nimport { motion } from \"framer-motion\";\r\nimport axios from 'axios';\r\nimport { getPricePlan } from '../core/utils/main';\r\nimport toastr from 'toastr';\r\nimport 'toastr/build/toastr.min.css';\r\n\r\nconst PageOne = ({ selectedOptionPageOne, handleOptionChangePageOne, onNext, setSurveyData}) => {\r\n  const [page2opt2, setPage2opt2] = useState('');\r\n  const [page2opt3a, setPage2opt3a] = useState('');\r\n  const [page2opt3b, setPage2opt3b] = useState('');\r\n  const [page2opt3c, setPage2opt3c] = useState('');\r\n  const [page2opt4, setPage2opt4] = useState('');\r\n  const [page2opt4a, setPage2opt4a] = useState('');\r\n  const [page2opt4b, setPage2opt4b] = useState('');\r\n  const [page2opt4c, setPage2opt4c] = useState('');\r\n  const [page2opt4d, setPage2opt4d] = useState('');\r\n  const [otherReason, setOtherReason] = useState('');\r\n  const option2Ans = 'Missing functionalities';\r\n  const option3Ans = 'Difficulty in navigating the site';\r\n  const option4Ans = 'Technical issues (bugs, downtimes)';\r\n\r\n  const handleopt2Change = (event) => { \r\n    setPage2opt2(event.target.value);\r\n    setSurveyData((prevData) => ({\r\n        Question1: {\r\n          Answer: option2Ans,\r\n          WhatfeaturesAreYoulookingFor: event.target.value \r\n        }\r\n    }));\r\n  };\r\n  const handleopt3aChange = (event) => { \r\n    setPage2opt3a(event.target.value);\r\n    setSurveyData((prevData) => ({\r\n      Question1: {\r\n        Answer: option3Ans,\r\n        WhichPartOfTheSiteWereYouHavingAHardTimeUsing: event.target.value \r\n      }\r\n    }));\r\n  };\r\n  const handleopt3bChange = (event) => { \r\n    setPage2opt3b(event.target.value);\r\n    setSurveyData((prevData) => ({\r\n      Question1: {\r\n        Answer: option3Ans,\r\n        WhichPartOfTheSiteWereYouHavingAHardTimeUsing: prevData.Question1.WhichPartOfTheSiteWereYouHavingAHardTimeUsing ? prevData.Question1.WhichPartOfTheSiteWereYouHavingAHardTimeUsing : '',\r\n        WhichPartOfTheSiteWhereYouGotStuck: event.target.value \r\n      }\r\n    }));\r\n  };\r\n  const handleopt3cChange = (event) => { \r\n    setPage2opt3c(event.target.value);\r\n    setSurveyData((prevData) => ({\r\n      Question1: {\r\n        Answer: option3Ans,\r\n        WhichPartOfTheSiteWereYouHavingAHardTimeUsing: prevData.Question1.WhichPartOfTheSiteWereYouHavingAHardTimeUsing ? prevData.Question1.WhichPartOfTheSiteWereYouHavingAHardTimeUsing : '',\r\n        WhichPartOfTheSiteWhereYouGotStuck: prevData.Question1.WhichPartOfTheSiteWhereYouGotStuck ? prevData.Question1.WhichPartOfTheSiteWhereYouGotStuck : '',\r\n        WhatDoYouThinkWeCanImproveOn: event.target.value\r\n      }\r\n    }));\r\n  };\r\n  const handleopt4Change = (event) => { \r\n    setPage2opt4(event.target.value);\r\n    setSurveyData((prevData) => ({\r\n      Question1: {\r\n        Answer: option4Ans,\r\n        WhatDeviceWereYouUsingWhenYouEncounteredAnIssue: event.target.value \r\n      }\r\n    }));\r\n  };\r\n  const handleopt4aChange = (event) => { \r\n    setPage2opt4a(event.target.value);\r\n    setSurveyData((prevData) => ({\r\n      Question1: {\r\n        Answer: option4Ans,\r\n        WhatDeviceWereYouUsingWhenYouEncounteredAnIssue: prevData.Question1.WhatDeviceWereYouUsingWhenYouEncounteredAnIssue ? prevData.Question1.WhatDeviceWereYouUsingWhenYouEncounteredAnIssue : '',\r\n        WhatBrowserDidYyouUse: event.target.value\r\n      }\r\n    }));\r\n  };\r\n  const handleopt4bChange = (event) => { \r\n    setPage2opt4b(event.target.value);\r\n    setSurveyData((prevData) => ({\r\n      Question1: {\r\n        Answer: option4Ans,\r\n        WhatDeviceWereYouUsingWhenYouEncounteredAnIssue: prevData.Question1.WhatDeviceWereYouUsingWhenYouEncounteredAnIssue ? prevData.Question1.WhatDeviceWereYouUsingWhenYouEncounteredAnIssue : '',\r\n        WhatBrowserDidYyouUse: prevData.Question1.WhatBrowserDidYyouUse ? prevData.Question1.WhatBrowserDidYyouUse : '',\r\n        WhatIssueDidYouEncounter: event.target.value\r\n      }\r\n    }));\r\n  };\r\n  const handleopt4cChange = (event) => { \r\n    setPage2opt4c(event.target.value);\r\n    setSurveyData((prevData) => ({\r\n      Question1: {\r\n        Answer: option4Ans,\r\n        WhatDeviceWereYouUsingWhenYouEncounteredAnIssue: prevData.Question1.WhatDeviceWereYouUsingWhenYouEncounteredAnIssue ? prevData.Question1.WhatDeviceWereYouUsingWhenYouEncounteredAnIssue : '',\r\n        WhatBrowserDidYyouUse: prevData.Question1.WhatBrowserDidYyouUse ? prevData.Question1.WhatBrowserDidYyouUse : '',\r\n        WhatIssueDidYouEncounter: prevData.Question1.WhatIssueDidYouEncounter ? prevData.Question1.WhatIssueDidYouEncounter : '',\r\n        PleaseSpecifyTheProcessStepsThatYouDidBeforeYouEncounteredTheIssue: event.target.value\r\n      }\r\n    }));\r\n  };\r\n  const handleopt4dChange = (event) => { \r\n    setPage2opt4d(event.target.value);\r\n    setSurveyData((prevData) => ({\r\n      Question1: {\r\n        Answer: option4Ans,\r\n        WhatDeviceWereYouUsingWhenYouEncounteredAnIssue: prevData.Question1.WhatDeviceWereYouUsingWhenYouEncounteredAnIssue ? prevData.Question1.WhatDeviceWereYouUsingWhenYouEncounteredAnIssue : '',\r\n        WhatBrowserDidYyouUse: prevData.Question1.WhatBrowserDidYyouUse ? prevData.Question1.WhatBrowserDidYyouUse : '',\r\n        WhatIssueDidYouEncounter: prevData.Question1.WhatIssueDidYouEncounter ? prevData.Question1.WhatIssueDidYouEncounter : '',\r\n        AreYouWillingToBeContactedSoWeCanAskMoreDetailsToResolveTheBug: event.target.value\r\n      }\r\n    }));\r\n  };\r\n  const handleOtherReasonChange = (event) => { \r\n    setOtherReason(event.target.value);\r\n    setSurveyData((prevData) => ({\r\n      Question1: {\r\n        Answer: event.target.value,\r\n      }\r\n    }));\r\n  };\r\n\r\n  const isOption2SelectedWithNoReason = selectedOptionPageOne === 'option2' && page2opt2.trim() === '';\r\n  const isOption3aSelectedWithNoReason = selectedOptionPageOne === 'option3' && page2opt3a.trim() === '';\r\n  const isOption3bSelectedWithNoReason = selectedOptionPageOne === 'option3' && page2opt3b.trim() === '';\r\n  const isOption3cSelectedWithNoReason = selectedOptionPageOne === 'option3' && page2opt3c.trim() === '';\r\n  const isOption4SelectedWithNoReason = selectedOptionPageOne === 'option4' && page2opt4.trim() === '';\r\n  const isOption4aSelectedWithNoReason = selectedOptionPageOne === 'option4' && page2opt4a.trim() === '';\r\n  const isOption4bSelectedWithNoReason = selectedOptionPageOne === 'option4' && page2opt4b.trim() === '';\r\n  const isOption4cSelectedWithNoReason = selectedOptionPageOne === 'option4' && page2opt4c.trim() === '';\r\n  const isOption4dSelectedWithNoReason = selectedOptionPageOne === 'option4' && page2opt4d.trim() === '';\r\n  const isOption5SelectedWithNoReason = selectedOptionPageOne === 'option5' && otherReason.trim() === '';\r\n  const isButtonDisabled =\r\n    selectedOptionPageOne === '' ||\r\n    isOption2SelectedWithNoReason ||\r\n    isOption3aSelectedWithNoReason ||\r\n    isOption3bSelectedWithNoReason ||\r\n    isOption3cSelectedWithNoReason ||\r\n    isOption4SelectedWithNoReason ||\r\n    isOption4aSelectedWithNoReason ||\r\n    isOption4bSelectedWithNoReason ||\r\n    isOption4cSelectedWithNoReason ||\r\n    isOption4dSelectedWithNoReason ||\r\n    isOption5SelectedWithNoReason;\r\n\r\n  return (\r\n    <div>\r\n      <h2 className=\"font-bold py-4\">Question 1 <span className=\"text-red-500\">*</span></h2>\r\n      <p className=\"mt-0 font-medium\">What is your primary reason for cancelling your AI-Pro subscription? <span className=\"text-red-500\">*</span></p>\r\n      <label><input type=\"radio\" className=\"mx-2 mr-4 my-4 sm:mx-6\" value=\"option1\" checked={selectedOptionPageOne === 'option1'} onChange={handleOptionChangePageOne}/>\r\n        Too expensive\r\n      </label>\r\n      <br />\r\n      <label><input type=\"radio\" className=\"mx-2 mr-4 my-4 sm:mx-6\" value=\"option2\" checked={selectedOptionPageOne === 'option2'} onChange={handleOptionChangePageOne}/>\r\n        Missing functionalities\r\n      </label>\r\n      <br />\r\n      <label><input type=\"radio\" className=\"mx-2 mr-4 my-4 sm:mx-6\" value=\"option3\" checked={selectedOptionPageOne === 'option3'} onChange={handleOptionChangePageOne}/>\r\n        Difficulty in navigating the site\r\n      </label>\r\n      <br />\r\n      <label><input type=\"radio\" className=\"mx-2 mr-4 my-4 sm:mx-6\" value=\"option4\" checked={selectedOptionPageOne === 'option4'} onChange={handleOptionChangePageOne}/>\r\n        Technical issues (bugs, downtimes)\r\n      </label>\r\n      <br />\r\n      <label><input type=\"radio\" className=\"mx-2 mr-4 my-4 sm:mx-6\" value=\"option5\" checked={selectedOptionPageOne === 'option5'} onChange={handleOptionChangePageOne}/>\r\n        Other (please specify)\r\n      </label>\r\n      <br />\r\n      <div className=\"my-8\">\r\n        {selectedOptionPageOne === 'option2' && (\r\n          <>\r\n            <label className=\"mt-4 font-medium\">What features are you looking for? <span className=\"text-red-500\">*</span></label>\r\n            <textarea type=\"text\" className=\"border border-gray-400 p-2 rounded-md mt-1 mb-6 w-full h-[80px]\"\r\n              value={page2opt2}\r\n              onChange={handleopt2Change}\r\n            />\r\n          </>\r\n        )}\r\n\r\n        {selectedOptionPageOne === 'option3' && (\r\n          <>\r\n            <label className=\"mt-4 font-medium\">Which part of the site were you having a hard time using? <span className=\"text-red-500\">*</span></label>\r\n            <textarea type=\"text\" className=\"border border-gray-400 p-2 rounded-md mt-1 mb-6 w-full h-[80px]\"\r\n              value={page2opt3a}\r\n              onChange={handleopt3aChange}\r\n            />\r\n            <label className=\"mt-4 font-medium\">Which part of the site where you got stuck? <span className=\"text-red-500\">*</span></label>\r\n            <textarea type=\"text\" className=\"border border-gray-400 p-2 rounded-md mt-1 mb-6 w-full h-[80px]\"\r\n              value={page2opt3b}\r\n              onChange={handleopt3bChange}\r\n            />\r\n            <label className=\"mt-4 font-medium\">What do you think we can improve on? <span className=\"text-red-500\">*</span></label>\r\n            <textarea type=\"text\" className=\"border border-gray-400 p-2 rounded-md mt-1 mb-6 w-full h-[80px]\"\r\n              value={page2opt3c}\r\n              onChange={handleopt3cChange}\r\n            />\r\n          </>\r\n        )}\r\n\r\n        {selectedOptionPageOne === 'option4' && (\r\n          <>\r\n            <div className=\"my-6\">\r\n              <label className=\"mt-4 font-medium\">What device were you using when you encountered an issue? <span className=\"text-red-500\">*</span></label>\r\n              <label className=\"block\"><input type=\"radio\" className=\"mx-2 mr-4 my-4 sm:mx-6\" value='desktop' checked={page2opt4 === 'desktop'} onChange={handleopt4Change}/>\r\n                  Desktop\r\n              </label>\r\n              <label className=\"block\"><input type=\"radio\" className=\"mx-2 mr-4 my-4 sm:mx-6\" value='mobile' checked={page2opt4 === 'mobile'} onChange={handleopt4Change}/>\r\n                  Mobile\r\n              </label>\r\n              <label className=\"block\"><input type=\"radio\" className=\"mx-2 mr-4 my-4 sm:mx-6\" value='tablet' checked={page2opt4 === 'tablet'} onChange={handleopt4Change}/>\r\n                  Tablet\r\n              </label>\r\n            </div>\r\n            <label className=\"mt-4 font-medium\">What browser did you use? <span className=\"text-red-500\">*</span></label>\r\n            <textarea type=\"text\" className=\"border border-gray-400 p-2 rounded-md mt-1 mb-6 w-full h-[80px]\"\r\n              value={page2opt4a}\r\n              onChange={handleopt4aChange}\r\n            />\r\n            <label className=\"mt-4 font-medium\">What issue did you encounter? <span className=\"text-red-500\">*</span></label>\r\n            <textarea type=\"text\" className=\"border border-gray-400 p-2 rounded-md mt-1 mb-6 w-full h-[80px]\"\r\n              value={page2opt4b}\r\n              onChange={handleopt4bChange}\r\n            />\r\n            <label className=\"mt-4 font-medium\">Please specify the process/steps that you did before you encountered the issue. <span className=\"text-red-500\">*</span></label>\r\n            <textarea type=\"text\" className=\"border border-gray-400 p-2 rounded-md mt-1 mb-6 w-full h-[80px]\"\r\n              value={page2opt4c}\r\n              onChange={handleopt4cChange}\r\n            />\r\n            <label className=\"mt-4 font-medium\">Are you willing to be contacted so we can ask more details to resolve the bug? <span className=\"text-red-500\">*</span></label>\r\n            <textarea type=\"text\" className=\"border border-gray-400 p-2 rounded-md mt-1 mb-6 w-full h-[80px]\"\r\n              value={page2opt4d}\r\n              onChange={handleopt4dChange}\r\n            />\r\n          </>\r\n        )}\r\n\r\n        {selectedOptionPageOne === 'option5' && (\r\n          <>\r\n            <label className=\"mt-4 font-medium\"></label>\r\n            <textarea type=\"text\" className=\"border border-gray-400 p-2 rounded-md mt-1 mb-6 w-full h-[80px]\"\r\n              value={otherReason}\r\n              onChange={handleOtherReasonChange}\r\n            />\r\n          </>\r\n        )}\r\n      </div>\r\n\r\n      <div className=\"flex justify-end mt-4 sm:mt-10\">\r\n        <button\r\n          className={`w-full sm:w-auto font-bold text-white px-8 py-2 rounded-md ${isButtonDisabled ? 'bg-gray-400 cursor-not-allowed' : 'bg-blue-500 cursor-pointer'}`}\r\n          onClick={onNext}\r\n          disabled={isButtonDisabled}\r\n        >\r\n          Next Question\r\n        </button>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nconst PageTwo = ({ selectedOptionPageTwo, handleOptionChangePageTwo, onNext, onPrevious, setSurveyData}) => {\r\n  const [otherReason, setOtherReason] = useState('');\r\n  const handleOtherReasonChange = (event) => { \r\n    setOtherReason(event.target.value);\r\n    setSurveyData((prevData) => ({\r\n      ...prevData,\r\n      Question2: {\r\n        Answer: event.target.value\r\n      }\r\n    }));\r\n  };\r\n\r\n  const isOption5SelectedWithNoReason = selectedOptionPageTwo === 'p2_option5' && otherReason.trim() === '';\r\n  const isButtonDisabled = selectedOptionPageTwo === '' || isOption5SelectedWithNoReason;\r\n\r\n  return (\r\n    <div>\r\n      <h2 className=\"font-bold py-4\">Question 2<span className=\"text-red-500\">*</span></h2>\r\n      <p className=\"mt-0 font-medium\">What features did not meet your needs? <span className=\"text-red-500\">*</span></p>\r\n      <label><input type=\"radio\" className=\"mx-2 mr-4 my-4 sm:mx-6\" value=\"p2_option1\" checked={selectedOptionPageTwo === 'p2_option1'} onChange={handleOptionChangePageTwo}/>\r\n        Chat prompts\r\n      </label>\r\n      <br />\r\n      <label><input type=\"radio\" className=\"mx-2 mr-4 my-4 sm:mx-6\" value=\"p2_option2\" checked={selectedOptionPageTwo === 'p2_option2'} onChange={handleOptionChangePageTwo}/>\r\n        Image generator\r\n      </label>\r\n      <br />\r\n      <label><input type=\"radio\" className=\"mx-2 mr-4 my-4 sm:mx-6\" value=\"p2_option3\" checked={selectedOptionPageTwo === 'p2_option3'} onChange={handleOptionChangePageTwo}/>\r\n        Text generator\r\n      </label>\r\n      <br />\r\n      <label><input type=\"radio\" className=\"mx-2 mr-4 my-4 sm:mx-6\" value=\"p2_option4\" checked={selectedOptionPageTwo === 'p2_option4'} onChange={handleOptionChangePageTwo}/>\r\n        Sharing/Downloading Functionalities\r\n      </label>\r\n      <br />\r\n      <label><input type=\"radio\" className=\"mx-2 mr-4 my-4 sm:mx-6\" value=\"p2_option5\" checked={selectedOptionPageTwo === 'p2_option5'} onChange={handleOptionChangePageTwo}/>\r\n        Other (please specify)\r\n      </label>\r\n      <br />\r\n      <div className=\"my-8\">\r\n        {selectedOptionPageTwo === 'p2_option5' && (\r\n          <>\r\n            <label className=\"mt-4 font-medium\"></label>\r\n            <textarea type=\"text\" className=\"border border-gray-400 p-2 rounded-md mt-1 mb-6 w-full h-[80px]\"\r\n              value={otherReason}\r\n              onChange={handleOtherReasonChange}\r\n            />\r\n          </>\r\n        )}\r\n      </div>\r\n\r\n      <div className=\"flex sm:absolute mt-2\">\r\n        <button\r\n          className='w-full sm:w-auto font-bold text-white px-8 py-2 rounded-md bg-black cursor-pointer'\r\n          onClick={onPrevious}\r\n        >\r\n          Previous Question\r\n        </button>\r\n      </div>\r\n\r\n      <div className=\"flex justify-end mt-4 sm:mt-10\">\r\n        <button\r\n          className={`w-full sm:w-auto font-bold text-white px-8 py-2 rounded-md ${isButtonDisabled ? 'bg-gray-400 cursor-not-allowed' : 'bg-blue-500 cursor-pointer'}`}\r\n          onClick={onNext}\r\n          disabled={isButtonDisabled}\r\n        >\r\n          Next Question\r\n        </button>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nconst PageThree = ({ auth, selectedOptionPageThree, setSelectedOptionPageThree, handleOptionChangePageThree, onPrevious, onSubmit, onPay }) => {\r\n  const isOption1Selected = selectedOptionPageThree === 'p3_option1';\r\n  const isButtonDisabled = selectedOptionPageThree === '' || isOption1Selected;\r\n  const handleCloseModal = () => {\r\n    setSelectedOptionPageThree('');\r\n  };\r\n\r\n  let downgrade_price = '5';\r\n  if (auth.currency==='SAR' ){\r\n    downgrade_price = '20';\r\n  }else if (auth.currency==='AED' ){\r\n    downgrade_price = '20';\r\n  }else if (auth.currency==='PLN' ){\r\n    downgrade_price = '20';\r\n  }else if (auth.currency==='RON' ){\r\n    downgrade_price = '23';\r\n  }else if (auth.currency==='CZK' ){\r\n    downgrade_price = '115';\r\n  }else if (auth.currency==='HUF' ){\r\n    downgrade_price = '1800';\r\n  }else if (auth.currency==='DKK' ){\r\n    downgrade_price = '35';\r\n  }else if (auth.currency==='BGN' ){\r\n    downgrade_price = '9';\r\n  }else if (auth.currency==='SEK'){\r\n    downgrade_price = '50';\r\n  }else if (auth.currency==='BRL'){\r\n    downgrade_price = '25';\r\n  }\r\n\r\n  return (\r\n    <div>\r\n      <h2 className=\"font-bold py-4\">Question 3 <span className=\"text-red-500\">*</span></h2>\r\n      <p className=\"mt-0 font-medium\">If we offer our subscription plan at a discounted rate, will you be interested in continuing using AI-PRO? <span className=\"text-red-500\">*</span></p>\r\n      <label><input type=\"radio\" className=\"mx-2 mr-4 my-4 sm:mx-6\" value=\"p3_option1\" checked={isOption1Selected} onChange={handleOptionChangePageThree}/>\r\n        Yes\r\n      </label>\r\n      <br />\r\n      <label><input type=\"radio\" className=\"mx-2 mr-4 my-4 sm:mx-6\" value=\"p3_option2\" checked={selectedOptionPageThree === 'p3_option2'} onChange={handleOptionChangePageThree}/>\r\n        No\r\n      </label>\r\n      <br />\r\n      <div className=\"my-8 relative text-center z-[99999]\">\r\n        {selectedOptionPageThree === 'p3_option1' && (\r\n          <>\r\n            <div className={`popup-overlay fixed top-0 left-0 w-full h-full bg-black opacity-50 ${selectedOptionPageThree === 'p3_option1' ? 'block' : 'hidden'}`}\r\n              onClick={handleCloseModal}\r\n            />\r\n            <div className={`popup-modal fixed top-0 bottom-0 left-0 right-0 mx-auto my-auto w-[90%] h-[80vh] bg-white p-6 sm:p-8 rounded-md shadow-lg md:w-[650px] md:max-h-[39rem] overflow-auto block ${selectedOptionPageThree === 'p3_option1' ? 'block' : 'hidden'}`}>\r\n              <div className=\"md:text-[36px] text-[24px] font-bold text-black mb-4\">\r\n                We Hate To See You Go\r\n              </div>\r\n              <hr className=\"border-t border-gray-300 h-[1px]\" />\r\n\r\n              <div className=\"my-4\">\r\n                <div className=\"flex md:flex-row flex-col items-center\">\r\n                  {/* Left Column */}\r\n                  <div className=\"text-md\">\r\n                    <p className=\"text-center md:text-left leading-relaxed xs:text-sm xs:leading-relaxed md:mr-5 md:mb-0 mb-2\">\r\n                      Here’s a <b>special offer</b> just for you!<br />\r\n                      Downgrade to{' '}\r\n                      <span className=\"text-gradient whitespace-nowrap font-bold\">BASIC</span> plan for only\r\n                    </p>\r\n                  </div>\r\n\r\n                  {/* Right Column */}\r\n\r\n                  <div className=\"flex flex-col items-center justify-center text-center bg-gradient-to-r from-[#379BEB30] via-[#FFFFFF] to-[#287BE236] rounded-full py-2 px-4 sm:py-3 sm:px-6 mx-auto max-w-full\">\r\n                    <p className=\"text-base xs:text-lg sm:text-xl flex flex-wrap items-center justify-center gap-1\">\r\n                      <span className=\"text-3xl xs:text-2xl sm:text-4xl font-extrabold text-[#297DE2]\">\r\n                        {getPricePlan(auth.currency, downgrade_price)}\r\n                      </span>\r\n                      <span className=\"text-sm xs:text-sm sm:text-xl font-normal\">/month</span>\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n\r\n              <hr className=\"border-t border-gray-300 h-[1px]\" />\r\n              <motion.button\r\n                className=\"w-full font-bold text-white px-8 py-2 rounded-full bg-black hover:bg-gray-700 my-6 cursor-pointer shadow-lg h-12 custom-gradient-hover\"\r\n                onClick={onPay}\r\n                whileHover={{ scale: 1.1 }}\r\n              >\r\n                Switch to Basic\r\n              </motion.button>\r\n\r\n              <div className=\"text-sm md:text-left leading-relaxed space-y-4 mb-4 text-center\">\r\n                <p>\r\n                  By downgrading to the BASIC plan, your subscription's start date will reset immediately. This action will also result in the cancellation of your existing plan.\r\n                </p>\r\n                <p>\r\n                  <span className=\"font-bold text-red-500\">Important Note:</span> By selecting the BASIC plan, you will only have access to 250,000 tokens per month and you will lose access to the following apps:\r\n                </p>\r\n              </div>\r\n\r\n              <hr className=\"border-t border-gray-300 h-[1px]\" />\r\n\r\n              <div className=\"flex flex-col md:flex-row justify-center md:justify-start md:space-x-6 text-center md:text-left mt-4\">\r\n                {/* Combined Column */}\r\n                <div className=\"mb-4 md:mb-0 flex flex-col items-center md:items-start space-y-2 md:space-y-0\">\r\n                  <h2 className=\"font-bold text-sm md:text-sm\">Chatbot Apps</h2>\r\n                  <ul className=\"list-none md:list-disc pl-0 md:pl-5 mt-2 text-sm\">\r\n                    <div className=\"flex flex-col md:flex-row md:justify-between\">\r\n\r\n                      <div className=\"md:mr-[5.25rem]\">\r\n                        <li>ChatPDF</li>\r\n                        <li>GrammarAI</li>\r\n                        <li>TeacherAI</li>\r\n                        <li>Recipe Maker</li>\r\n                        <li>TripAI</li>\r\n                      </div>\r\n\r\n                      <div className=\"md:mr-10\">\r\n                        <li>TranslateNow</li>\r\n                        <li>SearchAI</li>\r\n                        <li>Multi-Chat</li>\r\n                        <li>Sitebot</li>\r\n                      </div>\r\n\r\n                    </div>\r\n                  </ul>\r\n                </div>\r\n                {/* Third Column */}\r\n                <div className=\"flex flex-col items-center md:items-start space-y-2 md:space-y-0\">\r\n                  <h2 className=\"font-bold text-sm md:text-sm\">Image Generator Apps</h2>\r\n                  <ul className=\"list-none md:list-disc pl-0 md:pl-5 mt-2 text-sm\">\r\n                    <li>InteriorAI</li>\r\n                    <li>Remove Background</li>\r\n                    <li>Avatar Maker</li>\r\n                    <li>Storybook</li>\r\n                    <li>Restore Photo</li>\r\n                  </ul>\r\n                </div>\r\n              </div>\r\n\r\n\r\n            </div>\r\n\r\n          </>\r\n        )}\r\n      </div>\r\n      <div className=\"flex sm:absolute mt-2\">\r\n        <button\r\n          className='w-full sm:w-auto font-bold text-white px-8 py-2 rounded-md bg-black cursor-pointer'\r\n          onClick={onPrevious}\r\n        >\r\n          Previous Question\r\n        </button>\r\n      </div>\r\n      <div className=\"flex justify-end mt-4 sm:mt-10\">\r\n        <button\r\n          className={`w-full sm:w-auto font-bold text-white px-8 py-2 rounded-md ${isButtonDisabled ? 'bg-gray-400 cursor-not-allowed' : 'bg-blue-500 cursor-pointer'}`}\r\n          onClick={onSubmit}\r\n          disabled={isButtonDisabled}\r\n        >\r\n          Submit\r\n        </button>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nfunction Survey() {\r\n  useEffect(() => {\r\n    toastr.options = {\r\n      positionClass: 'toast-top-center'\r\n    };\r\n  }, []);\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n  const [selectedOptionPageOne, setSelectedOptionPageOne] = useState('');\r\n  const [selectedOptionPageTwo, setSelectedOptionPageTwo] = useState('');\r\n  const [selectedOptionPageThree, setSelectedOptionPageThree] = useState('');\r\n  const [surveyData, setSurveyData] = useState({});\r\n  const auth = Auth();\r\n\r\n  if(auth === undefined) return;\r\n  if(auth === false) {\r\n    window.location.href = '/login';\r\n    return;\r\n  }\r\n\r\n  if((auth.surveydata !== '' && auth.surveydata !== null) || auth.status !== 'active') {\r\n      window.location.href = 'my-account'\r\n      return;\r\n  }\r\n\r\n\r\n\r\n  const handlePay = () => {\r\n    \r\n    switch(auth.currency.toLowerCase()) {\r\n      case 'usd':\r\n        saveData('/downgrade/23',true);\r\n      break;\r\n      case 'eur':\r\n        saveData('/downgrade/24',true);\r\n      break;\r\n      case 'gbp':\r\n        saveData('/downgrade/25',true);\r\n      break;\r\n      case 'brl':\r\n        saveData('/downgrade/26',true);\r\n      break;\r\n      case 'sar':\r\n        saveData('/downgrade/57',true);\r\n      break;\r\n      case 'aed':\r\n        saveData('/downgrade/58',true);\r\n      break;\r\n      case 'pln':\r\n        saveData('/downgrade/111',true);\r\n      break;\r\n      case 'ron':\r\n        saveData('/downgrade/117',true);\r\n      break;\r\n      case 'czk':\r\n        saveData('/downgrade/123',true);\r\n      break;\r\n      case 'huf':\r\n        saveData('/downgrade/129',true);\r\n      break;\r\n      case 'dkk':\r\n        saveData('/downgrade/135',true);\r\n      break;\r\n      case 'bgn':\r\n        saveData('/downgrade/141',true);\r\n      break;\r\n      case 'sek':\r\n        saveData('/downgrade/149', true);\r\n      break;\r\n      case 'chf':\r\n        saveData('/downgrade/150', true);\r\n      break;\r\n      default: saveData('/downgrade/23',true);\r\n    }\r\n  };\r\n\r\n  const handlePrevious = () => {\r\n    if (currentPage === 1 && selectedOptionPageOne === '' && selectedOptionPageTwo === '' && selectedOptionPageThree === '') {\r\n      return;\r\n    }\r\n    setCurrentPage((prevPage) => prevPage - 1);\r\n  };\r\n\r\n  const handleNext = () => {\r\n    if (currentPage === 1 && selectedOptionPageOne === '' && selectedOptionPageTwo === '' && selectedOptionPageThree === '') {\r\n      return;\r\n    }\r\n    setCurrentPage((prevPage) => prevPage + 1);\r\n  };\r\n\r\n  const handleOptionChangePageOne = (event) => {\r\n    setSelectedOptionPageOne(event.target.value);\r\n    \r\n    const val = event.target.nextSibling !== null ? event.target.nextSibling.textContent : '';\r\n    setSurveyData((prevData) => ({\r\n        Question1: {\r\n          Answer: val\r\n        }\r\n    }));\r\n  };\r\n\r\n  const handleOptionChangePageTwo = (event) => {\r\n    setSelectedOptionPageTwo(event.target.value);\r\n    const val = event.target.nextSibling !== null ? event.target.nextSibling.textContent : '';\r\n    setSurveyData((prevData) => ({\r\n      ...prevData,\r\n      Question2: {\r\n        Answer: val\r\n      }\r\n    }));\r\n  };\r\n\r\n  const handleOptionChangePageThree = (event) => {\r\n    setSelectedOptionPageThree(event.target.value);\r\n    const val = event.target.nextSibling !== null ? event.target.nextSibling.textContent : '';\r\n    setSurveyData((prevData) => ({\r\n      ...prevData,\r\n      Question3: {\r\n        Answer: val\r\n      }\r\n    }));\r\n  };\r\n\r\n  const handleSubmit = () => {\r\n    document.querySelector(\".loader-container\").classList.add('active');\r\n    saveData();\r\n    cancelUser();\r\n  };\r\n\r\n  function saveData(loc,saveTosession){\r\n    const save_session = saveTosession ? '1' : '0'; \r\n    const data = JSON.stringify(surveyData);\r\n    axios.post(`${process.env.REACT_APP_API_URL}/save-survey`, {\r\n      data, save_session:save_session\r\n    }, { headers: { 'content-type': 'application/x-www-form-urlencoded' } }).then(function(res){\r\n      let output = res.data;\r\n\r\n      if(output.success) {\r\n        if(loc){\r\n          window.location.href = loc;\r\n          return;\r\n        }else{\r\n          toastr.success(\"Survey submitted successfully!\");\r\n        }\r\n      }else{\r\n        toastr.error(output.data);\r\n        return;\r\n      }\r\n    });\r\n  }\r\n\r\n  function cancelUser() {\r\n    axios.post(`${process.env.REACT_APP_API_URL}/cancel-subscription`, {\r\n      }, { headers: { 'content-type': 'application/x-www-form-urlencoded' } }).then(function(res){\r\n        let output = res.data;\r\n        if(output.success) {\r\n          toastr.success(\"Subscription cancelled successfully!\");\r\n          setTimeout(function(){\r\n              document.querySelector(\".loader-container\").classList.remove('active');\r\n            window.location.href = '/my-account';\r\n          }, 3000);\r\n          return;\r\n        }\r\n        if(output.data) toastr.error(output.data.msg);\r\n      });\r\n\r\n  }\r\n\r\n\r\n  return (\r\n    <>\r\n      <Helmet>\r\n        <meta name=\"robots\" content=\"noindex, nofollow\" />\r\n        <title>AI-Pro | Cancellation Survey</title>\r\n        <meta name=\"description\" content=\"Cancellation Survey\" />\r\n      </Helmet>\r\n      <Header auth={auth} />\r\n      <div className=\"survey flex justify-center items-center lg:mt-26 pt-10 md:pt-28 md:min-h-[90vh]\">\r\n        <div className=\"container mx-auto py-4 px-0 md:px-4 sm:px-0 w-full mx-auto\">\r\n          <div className=\"max-w-6xl mx-auto p-4 sm:p-8\">\r\n            <div className=\"pt-2 sm:pt-10 md:p-8\">\r\n            <div className=\"block p-4 sm:p-8 rounded-md border-2 bg-white\">\r\n                <h1 className=\"text-lg sm:text-3xl font-bold text-left mb-6\">Cancellation Survey</h1>\r\n                {currentPage === 1 && \r\n                  <PageOne \r\n                    selectedOptionPageOne={selectedOptionPageOne} \r\n                    handleOptionChangePageOne={handleOptionChangePageOne}\r\n                    surveyData={surveyData}\r\n                    setSurveyData={setSurveyData}\r\n                    onNext={handleNext} \r\n                  />}\r\n                {currentPage === 2 && \r\n                  <PageTwo \r\n                    selectedOptionPageTwo={selectedOptionPageTwo} \r\n                    handleOptionChangePageTwo={handleOptionChangePageTwo}\r\n                    setSurveyData={setSurveyData}  \r\n                    onPrevious={handlePrevious}\r\n                    onNext={handleNext} \r\n                  />}\r\n                {currentPage === 3 && \r\n                  <PageThree \r\n                    selectedOptionPageThree={selectedOptionPageThree}  \r\n                    setSelectedOptionPageThree={setSelectedOptionPageThree} \r\n                    handleOptionChangePageThree={handleOptionChangePageThree} \r\n                    onPrevious={handlePrevious} \r\n                    onSubmit={handleSubmit} \r\n                    onPay={handlePay}\r\n                    auth={auth} \r\n                  />}\r\n              </div>\r\n\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n    </>\r\n  );\r\n}\r\n\r\nexport default Survey;"], "names": ["getCurrency", "currency", "toLowerCase", "getCountry", "formatDate", "dateStr", "date", "Date", "replace", "getMonth", "getDate", "getFullYear", "getPrice", "data", "trial_price", "price", "numberWithCommas", "x", "toString", "formatNumber", "number", "floatNumber", "parseFloat", "toFixed", "getPricePlan", "isNaN", "toLocaleString", "diffMin", "dt1", "dt2", "diff", "getTime", "Math", "abs", "round", "formatDateTime", "getHours", "getMinutes", "DailyPrice", "_ref", "plan", "dailyPrice", "interval", "payment_interval", "_jsxs", "class", "children", "trial_days", "_Fragment", "className", "Get<PERSON><PERSON><PERSON>", "_jsx", "PriceFormatted", "_ref2", "getPrefixLocation", "currentLocation", "window", "location", "href", "indexOf", "PageOne", "selectedOptionPageOne", "handleOptionChangePageOne", "onNext", "setSurveyData", "page2opt2", "setPage2opt2", "useState", "page2opt3a", "setPage2opt3a", "page2opt3b", "setPage2opt3b", "page2opt3c", "setPage2opt3c", "page2opt4", "setPage2opt4", "page2opt4a", "setPage2opt4a", "page2opt4b", "setPage2opt4b", "page2opt4c", "setPage2opt4c", "page2opt4d", "setPage2opt4d", "otherReason", "setOtherReason", "option3Ans", "option4Ans", "handleopt4Change", "event", "target", "value", "prevData", "Question1", "Answer", "WhatDeviceWereYouUsingWhenYouEncounteredAnIssue", "isOption2SelectedWithNoReason", "trim", "isOption3aSelectedWithNoReason", "isOption3bSelectedWithNoReason", "isOption3cSelectedWithNoReason", "isOption4SelectedWithNoReason", "isOption4aSelectedWithNoReason", "isOption4bSelectedWithNoReason", "isOption4cSelectedWithNoReason", "isOption4dSelectedWithNoReason", "isOption5SelectedWithNoReason", "isButtonDisabled", "type", "checked", "onChange", "WhatfeaturesAreYoulookingFor", "WhichPartOfTheSiteWereYouHavingAHardTimeUsing", "WhichPartOfTheSiteWhereYouGotStuck", "WhatDoYouThinkWeCanImproveOn", "WhatBrowserDidYyouUse", "WhatIssueDidYouEncounter", "PleaseSpecifyTheProcessStepsThatYouDidBeforeYouEncounteredTheIssue", "AreYouWillingToBeContactedSoWeCanAskMoreDetailsToResolveTheBug", "onClick", "disabled", "PageTwo", "selectedOptionPageTwo", "handleOptionChangePageTwo", "onPrevious", "Question2", "<PERSON><PERSON><PERSON><PERSON>", "_ref3", "auth", "selectedOptionPageThree", "setSelectedOptionPageThree", "handleOptionChangePageThree", "onSubmit", "onPay", "isOption1Selected", "downgrade_price", "handleCloseModal", "motion", "button", "whileHover", "scale", "useEffect", "toastr", "positionClass", "currentPage", "setCurrentPage", "setSelectedOptionPageOne", "setSelectedOptionPageTwo", "surveyData", "<PERSON><PERSON>", "undefined", "surveydata", "status", "handlePrevious", "prevPage", "handleNext", "saveData", "loc", "saveTosession", "save_session", "JSON", "stringify", "axios", "post", "headers", "then", "res", "output", "success", "<PERSON><PERSON><PERSON>", "name", "content", "Header", "val", "nextS<PERSON>ling", "textContent", "Question3", "handleSubmit", "document", "querySelector", "classList", "add", "setTimeout", "remove", "msg", "handlePay"], "sourceRoot": ""}