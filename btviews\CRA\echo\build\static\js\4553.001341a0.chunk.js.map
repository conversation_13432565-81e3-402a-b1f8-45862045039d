{"version": 3, "file": "static/js/4553.001341a0.chunk.js", "mappings": "mOAgSA,QAvRA,SAAkBA,GAChB,MAAMC,EAAOD,EAAMC,KAAOD,EAAMC,KAAO,KACjCC,EAAaF,EAAME,WAAaF,EAAME,WAAa,OACzD,IAAIC,GAAMC,EAAAA,EAAAA,IAAU,QAASA,EAAAA,EAAAA,IAAU,OAA6CC,KAChFC,GAAYF,EAAAA,EAAAA,IAAU,cAAeA,EAAAA,EAAAA,IAAU,aAAe,SAClE,MAAOG,IAAWC,EAAAA,EAAAA,WAAUJ,EAAAA,EAAAA,IAAU,YAAaA,EAAAA,EAAAA,IAAU,WAAa,eAGpEK,KADqB,CAAC,KAAK,KAAK,KAAK,KAAK,MAAM,MAAM,OACrBC,SAASP,KAAQH,EAAMS,aAAcT,EAAMS,WAClF,IAAIE,GAAsB,EAEhB,OAANR,IACFQ,GAAsB,GAGxB,MAAQC,EAAcC,IAAoBL,EAAAA,EAAAA,UAASG,EAAsB,SAAW,WAC9EG,GAAYV,EAAAA,EAAAA,IAAU,eAAgBA,EAAAA,EAAAA,IAAU,cAAgB,GAChEW,GAAaX,EAAAA,EAAAA,IAAU,eAAgBA,EAAAA,EAAAA,IAAU,cAAgB,OACjE,EAAEY,IAAMC,EAAAA,EAAAA,MAgBRC,EAAqB,SAASC,GAClC,IAAIC,EAAY,GACZC,EAAU,GACVC,EAAiB,GACjBC,EAAa,GA4DjB,OA1DAH,GAAa,QACbA,GAAa,oDACbA,GAAa,kCACbA,GAAa,wEACbA,GAAa,2FACbA,GAAa,8FACbA,GAAa,QACbA,GAAa,uFACbA,GAAa,QACbA,GAAa,mEACbA,GAAa,0BAEbC,GAAW,QACXA,GAAW,4FACyB,WAAjCF,EAAKK,UAAUC,cAA4BJ,GAAW,mDACpDA,GAAW,uGAChBA,GAAW,2FACXA,GAAW,8FACS,OAAjBF,EAAKO,UAAkBL,GAAW,kFACrCA,GAAW,oFACXA,GAAW,QACXA,GAAW,8EACXA,GAAW,uFACXA,GAAW,yEACXA,GAAW,wEACXA,GAAW,+EACXA,GAAW,6EACXA,GAAW,QACXA,GAAW,8DACXA,GAAW,mEACXA,GAAW,mCACXA,GAAW,0BAEXE,GAAc,QACdA,GAAc,4FACdA,GAAc,oCACdA,GAAc,mDACdA,GAAc,2FACdA,GAAc,8FACdA,GAAc,iFACdA,GAAc,oFACdA,GAAc,QACdA,GAAc,8EACdA,GAAc,uFACdA,GAAc,yEACdA,GAAc,wEACdA,GAAc,+EACdA,GAAc,6EACdA,GAAc,QACdA,GAAc,8DACdA,GAAc,mEACdA,GAAc,mCACdA,GAAc,0BAEdD,GAAkB,QAClBA,GAAkB,sHAClBA,GAAkB,oOAEiB,UAA/BH,EAAKK,UAAUC,cAhDnBL,qgBAkDyC,WAA/BD,EAAKK,UAAUC,cAChBF,EACgC,eAA/BJ,EAAKK,UAAUC,cANzBH,4VAQyC,WAA/BH,EAAKK,UAAUC,cAChBF,EAEAF,CAIX,EAEMM,EAAgB,WACpB,OACEC,EAAAA,EAAAA,KAAA,OAAKC,UAAU,4FAA2FC,UACxGF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gDAA+CC,UAC5DC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,yBAAwBC,SAAEd,EAAE,8CAC1CY,EAAAA,EAAAA,KAAA,KAAGC,UAAW,qCAAqD,WAAjBjB,GAAqC,OAART,EAAgB,GAAI,QAAS2B,SAAEd,EAAE,6CAC7F,WAAjBJ,GAAqC,OAART,GAAeyB,EAAAA,EAAAA,KAAA,OAAKC,UAAU,eAAcC,SAAC,oBAAwB,IACpGF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,OAAMC,UACnBF,EAAAA,EAAAA,KAACI,EAAAA,EAAOC,OAAM,CACZJ,UAAU,oDACVK,MAAO,CAACC,iBAAiBC,EAAAA,EAAAA,IAAQ9B,IACjC+B,WAAY,CAAEC,MAAO,IAAKH,iBAAiBI,EAAAA,EAAAA,IAAYjC,IACvDkC,SAAU,CAAEF,MAAO,IACnBG,aAAcA,KACZC,QAAQC,IAAI,SAAU1C,GACtByC,QAAQC,IAAI,UAAW1C,EAAK,GAAG2C,WAEjCC,QAASA,IAAM3C,EAAW,IAAI4B,SAE/Bd,EAAE,8CAGLY,EAAAA,EAAAA,KAAA,OAAKC,UAAU,yBAAwBC,UACrCF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,wBAAuBC,UACnCC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,iBAAgBC,SAAA,EAACF,EAAAA,EAAAA,KAAA,UAC7BA,EAAAA,EAAAA,KAAA,OAAKC,UAAU,YAAWC,SAAEd,EAAE,8CAC9BY,EAAAA,EAAAA,KAAA,OAAAE,SAAMd,EAAE,0DAQxB,EAEA,OACIY,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mCAAkCC,UAC/CF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mDAAkDC,UAC/DC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,0DAAyDC,SAAA,EACtEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,8EAA6EC,SAAC,kBAG5FC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,cAAaC,SAAA,CAAC,oCAAkCvB,KAE7DE,GACAsB,EAAAA,EAAAA,MAAA,OAAKF,UAAU,MAAKC,SAAA,EAClBF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,qDAGfD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,+CAA8CC,UAC3DC,EAAAA,EAAAA,MAAA,SAAOe,IAAI,UAAUjB,UAAU,mCAAkCC,SAAA,EAC/DF,EAAAA,EAAAA,KAAA,OAAKC,WAA+B,YAAjBjB,EAA6B,0BAA4B,iBAA5D,kBAA6FkB,SAAC,aAG9GC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,WAAUC,SAAA,EACvBF,EAAAA,EAAAA,KAAA,SAAOmB,KAAK,WAAWC,GAAG,UAAUnB,UAAU,iBAAiBoB,SAtJ5D,WAEnBpC,EADkB,YAAjBD,EACe,SAEA,UAEpB,EAgJ6GsC,eAAgBvC,KACzGiB,EAAAA,EAAAA,KAAA,OAAKC,UAAU,6CACfD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,2EAEjBD,EAAAA,EAAAA,KAAA,OAAKC,WAA+B,WAAjBjB,EAA4B,0BAA4B,iBAA3D,kBAA4FkB,SAAC,mBAMjH,GAEFrB,GACAsB,EAAAA,EAAAA,MAAA,OAAKF,UAAU,0DAAyDC,SAAA,CACrE7B,aAAI,EAAJA,EAAMkD,IAAI,CAAChC,EAAMiC,IA5JN,SAASjC,GACjC,OAAIV,GACDU,EAAKkC,iBAAiB5B,gBAAkBb,CAE7C,CAyJgB0C,CAAkBnC,IAChBY,EAAAA,EAAAA,MAAA,OAAiBF,UAAW,qEAA+E,IAAVuB,EAAc,WAAa,IAAMtB,SAAA,EAChIF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gDAA+CC,UAC5DC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,2BAA0BC,SAAA,EACvCF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,yBAAwBC,SAAEX,EAAKoC,SAC7C3B,EAAAA,EAAAA,KAAC4B,EAAAA,GAAc,CAACrC,KAAMA,KACtBS,EAAAA,EAAAA,KAAA,OAAKC,UAAU,OAAMC,UACnBF,EAAAA,EAAAA,KAACI,EAAAA,EAAOC,OAAM,CACZJ,UAAU,4CACVK,MAAO,CAACC,iBAAiBC,EAAAA,EAAAA,IAAQ9B,IACjC+B,WAAY,CAAEC,MAAO,IAAKH,iBAAiBI,EAAAA,EAAAA,IAAYjC,IACvDkC,SAAU,CAAEF,MAAO,IACnBO,QAASA,IAAM3C,EAAWiB,EAAKO,SAASI,SACzC,iBAIHF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,yBAAwBC,UACrCF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,wBAAuBC,SACjCX,EAAKsC,kBAAmB7B,EAAAA,EAAAA,KAAA,MAAIC,UAAU,OAAO6B,wBAAyB,CAACC,OAAQzC,EAAmBC,MAAgB,cAKzG,WAAjBP,GAA6BH,GAAamB,EAAAA,EAAAA,KAAA,OAAKC,UAAU,2IAA0IC,UACnMC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,CAAK,UAAMF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,+BAA8BC,SAAC,YAAc,kCACjE,OAzBDsB,GA2BR,IAEW,OAAfrC,GAA+B,OAARZ,GAAiC,WAAjBS,GAAkD,QAArBX,EAAK,GAAG2C,UAC5EhB,EAAAA,EAAAA,KAAAgC,EAAAA,SAAA,CAAA9B,SACCH,MAED,SAGJI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,2CAA0CC,SAAA,CACtD7B,aAAI,EAAJA,EAAMkD,IAAI,CAAChC,EAAMiC,KAChBrB,EAAAA,EAAAA,MAAA,OAAiBF,UAAW,qEAA+E,IAAVuB,EAAc,WAAa,IAAMtB,SAAA,EAChIF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gDAA+CC,UAC5DC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,2BAA0BC,SAAA,EACvCF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,yBAAwBC,SAAEX,EAAKoC,QAElB,eAA3BpC,EAAKoC,MAAM9B,eAA+BG,EAAAA,EAAAA,KAAA,KAAGC,UAAU,oDAAmDC,SAAC,iBAC3GF,EAAAA,EAAAA,KAAC4B,EAAAA,GAAc,CAACrC,KAAMA,KAEtBS,EAAAA,EAAAA,KAAA,OAAKC,UAAU,OAAMC,UACnBF,EAAAA,EAAAA,KAACI,EAAAA,EAAOC,OAAM,CACZJ,UAAU,4CACVK,MAAO,CAACC,iBAAiBC,EAAAA,EAAAA,IAAQ9B,IACjC+B,WAAY,CAAEC,MAAO,IAAKH,iBAAiBI,EAAAA,EAAAA,IAAYjC,IACvDkC,SAAU,CAAEF,MAAO,IACnBO,QAASA,IAAM3C,EAAWiB,EAAKO,SAASI,SACzC,iBAIHF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,yBAAwBC,UACrCF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,wBAAuBC,SACjCX,EAAKsC,kBAAmB7B,EAAAA,EAAAA,KAAA,MAAIC,UAAU,OAAO6B,wBAAyB,CAACC,OAAQzC,EAAmBC,MAAgB,cAKhH,IAAViC,GAAcxB,EAAAA,EAAAA,KAAA,OAAKC,UAAU,2IAA0IC,UACvKF,EAAAA,EAAAA,KAAA,QAAAE,SAAM,mBACC,OA5BDsB,IA+BK,OAAfrC,GAA+B,OAARZ,GAAqC,QAArBF,EAAK,GAAG2C,UAC/ChB,EAAAA,EAAAA,KAAAgC,EAAAA,SAAA,CAAA9B,SACCH,MAED,SAINC,EAAAA,EAAAA,KAAA,KAAGC,UAAU,8DAA6DC,SAAC,kFAI3D,OAAdhB,GACFc,EAAAA,EAAAA,KAAAgC,EAAAA,SAAA,CAAA9B,UACEF,EAAAA,EAAAA,KAACiC,EAAAA,QAAS,MAEZ,WAMZ,C", "sources": ["pricing/components/vprice_03.jsx"], "sourcesContent": ["import { React, useState } from 'react';\r\nimport { motion } from \"framer-motion\";\r\nimport { PriceFormatted } from '../../core/utils/main';\r\nimport { GetCookie } from '../../core/utils/cookies';\r\nimport { hexHash, hoverDarken } from '../../core/utils/helper';\r\nimport '../css/style.css';\r\nimport TpReviews from '../../features/tpreviews';\r\nimport { useTranslation } from 'react-i18next';\r\n\r\nfunction VPrice03(props) {\r\n  const data = props.data ? props.data : null;\r\n  const setPricing = props.setPricing ? props.setPricing : ()=>{};\r\n  var ppg = GetCookie(\"ppg\") ? GetCookie(\"ppg\") : process.env.REACT_APP_DEFAULT_PPG ? process.env.REACT_APP_DEFAULT_PPG : \"14\";\r\n  var pp_ctaclr = GetCookie(\"pp_ctaclr\") ? GetCookie(\"pp_ctaclr\") : \"1559ED\";\r\n  const [appName] = useState((GetCookie('appName') ? GetCookie('appName') : \"ChatBot Pro\"))\r\n\r\n  const ppgArrayWithToggle = ['40','48','52','97','101','109','110'];\r\n  const showToggle = (ppgArrayWithToggle.includes(ppg) && props.showToggle) ? props.showToggle : false;\r\n  var billedAnnualDisplay = false;\r\n\r\n  if (ppg==='48'){\r\n    billedAnnualDisplay = true;\r\n  }\r\n\r\n  const [ planInterval, setPlanInterval ] = useState(billedAnnualDisplay ? \"yearly\" : \"monthly\");\r\n  const tpreviews = GetCookie(\"tp_reviews\") ? GetCookie(\"tp_reviews\") : \"\";\r\n  const enterprise = GetCookie(\"enterprise\") ? GetCookie(\"enterprise\") : \"off\";\r\n  const { t } = useTranslation();\r\n\r\n  const intervalChange = function() {\r\n    if(planInterval === \"monthly\") {\r\n      setPlanInterval(\"yearly\");\r\n    } else {\r\n      setPlanInterval(\"monthly\");\r\n    }\r\n  };\r\n\r\n  const checkPlanInterval = function(plan) {\r\n    if(!showToggle) return true;\r\n    if(plan.payment_interval.toLowerCase() === planInterval) return true;\r\n    return false;\r\n  }\r\n\r\n  const displayDescription = function(plan) {\r\n    let basicPlan = \"\";\r\n    let proPlan = \"\";\r\n    let enterprisePlan = \"\";\r\n    let proMaxPlan = \"\";\r\n\r\n    basicPlan += \"<br/>\";\r\n    basicPlan += \"<div><strong>ChatGPT-powered tools</strong></div>\";\r\n    basicPlan += \"<div>4,096 context window</div>\";\r\n    basicPlan += \"<div>250,000 Token Cap - comprehensive and exhaustive responses</div>\";\r\n    basicPlan += \"<div>Customizable Response Models - can provide scenario & persona-based responses</div>\";\r\n    basicPlan += \"<div>Save Chat History - store up to hundreds of research results accessible any time</div>\";\r\n    basicPlan += \"<br/>\";\r\n    basicPlan += \"<div><strong>DreamPhoto - Stable Diffusion-powered</strong> AI image generator</div>\";\r\n    basicPlan += \"<br/>\";\r\n    basicPlan += \"<div>Export Conversations - Image, Text, CSV or JSON files</div>\";\r\n    basicPlan += \"<div>Live Support</div>\";\r\n\r\n    proPlan += \"<br/>\";\r\n    proPlan += \"<div><strong>ChatGPT 4-powered tools</strong> to generate any text content you need</div>\";\r\n    if(plan.plan_type.toLowerCase() === 'promax') proPlan += \"<div>No Response Cap - no word count limit</div>\";\r\n    else proPlan += \"<div>8,192 context window</div><div>500,000 Token Cap - comprehensive and exhaustive responses</div>\";\r\n    proPlan += \"<div>Customizable Response Models - can provide scenario & persona-based responses</div>\";\r\n    proPlan += \"<div>Save Chat History - store up to hundreds of research results accessible any time</div>\";\r\n    if(plan.plan_id !== '86') proPlan += \"<div><strong>ChatPDF</strong> - ask questions based on your PDF document</div>\";\r\n    proPlan += \"<div><strong>Teacher AI</strong> - streamlined learning app for any subject</div>\";\r\n    proPlan += \"<br/>\";\r\n    proPlan += \"<div><strong>Stable Diffusion-powered tools</strong> to create images</div>\";\r\n    proPlan += \"<div><strong>DreamPhoto</strong> - Stable Diffusion-powered AI image generator</div>\";\r\n    proPlan += \"<div><strong>Storybook AI</strong> - generate images for stories</div>\";\r\n    proPlan += \"<div><strong>Interior AI</strong> - reinvent your rooms with AI</div>\";\r\n    proPlan += \"<div><strong>AvatarMaker</strong> - create fantastical digital avatars</div>\";\r\n    proPlan += \"<div><strong>RestorePhoto</strong> - preserve and restore old photos</div>\";\r\n    proPlan += \"<br/>\";\r\n    proPlan += \"<div>Choose Light or Dark Mode - for all screen types</div>\";\r\n    proPlan += \"<div>Export Conversations - Image, Text, CSV or JSON files</div>\";\r\n    proPlan += \"<div>AI Art Prompt Gallery</div>\";\r\n    proPlan += \"<div>Live Support</div>\";\r\n\r\n    proMaxPlan += \"<br/>\";\r\n    proMaxPlan += \"<div><strong>ChatGPT 4-powered tools</strong> to generate any text content you need</div>\";\r\n    proMaxPlan += \"<div>128,000 context window</div>\";\r\n    proMaxPlan += \"<div>No Response Cap - no word count limit</div>\";\r\n    proMaxPlan += \"<div>Customizable Response Models - can provide scenario & persona-based responses</div>\";\r\n    proMaxPlan += \"<div>Save Chat History - store up to hundreds of research results accessible any time</div>\";\r\n    proMaxPlan += \"<div><strong>ChatPDF</strong> - ask questions based on your PDF document</div>\";\r\n    proMaxPlan += \"<div><strong>Teacher AI</strong> - streamlined learning app for any subject</div>\";\r\n    proMaxPlan += \"<br/>\";\r\n    proMaxPlan += \"<div><strong>Stable Diffusion-powered tools</strong> to create images</div>\";\r\n    proMaxPlan += \"<div><strong>DreamPhoto</strong> - Stable Diffusion-powered AI image generator</div>\";\r\n    proMaxPlan += \"<div><strong>Storybook AI</strong> - generate images for stories</div>\";\r\n    proMaxPlan += \"<div><strong>Interior AI</strong> - reinvent your rooms with AI</div>\";\r\n    proMaxPlan += \"<div><strong>AvatarMaker</strong> - create fantastical digital avatars</div>\";\r\n    proMaxPlan += \"<div><strong>RestorePhoto</strong> - preserve and restore old photos</div>\";\r\n    proMaxPlan += \"<br/>\";\r\n    proMaxPlan += \"<div>Choose Light or Dark Mode - for all screen types</div>\";\r\n    proMaxPlan += \"<div>Export Conversations - Image, Text, CSV or JSON files</div>\";\r\n    proMaxPlan += \"<div>AI Art Prompt Gallery</div>\";\r\n    proMaxPlan += \"<div>Live Support</div>\";\r\n\r\n    enterprisePlan += \"<br/>\";\r\n    enterprisePlan += \"<div><strong>Take your AI-powered applications to the next level with our exclusive Enterprise Plan.</strong></div>\";\r\n    enterprisePlan += \"<div>Crafted for businesses seeking a tailored approach to harnessing the power of artificial intelligence, <br>this plan offers a suite of features designed to drive innovation and deliver exceptional user experiences.</div>\";\r\n\r\n    if (plan.plan_type.toLowerCase()==='basic'){\r\n      return basicPlan;\r\n    }else if (plan.plan_type.toLowerCase()==='promax'){\r\n      return proMaxPlan;\r\n    }else if (plan.plan_type.toLowerCase()==='enterprise'){\r\n      return enterprisePlan;\r\n    }else if (plan.plan_type.toLowerCase()==='promax'){\r\n      return proMaxPlan;\r\n    }else{\r\n      return proPlan;\r\n    }\r\n\r\n\r\n  }\r\n\r\n  const enterpriseTab = function() {\r\n    return (\r\n      <div className=\"price_col w-full lg:w-[330px] xl:w-[380px] text-center px-4 mb-8 relative md:min-h-[90vh]\">\r\n        <div className=\"bg-white rounded-lg shadow-lg overflow-hidden\">\r\n          <div className=\"px-6 py-10 price-content  md:pt-[60px]\">\r\n            <h3 className=\"text-xl font-bold mb-4\">{t('echo.pricing.vprice_02.enterprise.title')}</h3>\r\n            <p className={`text-4xl font-bold text-gray-800 ${planInterval === 'yearly' && ppg === '48'  ? \"\" :\"mb-4\"}`}>{t('echo.pricing.vprice_02.enterprise.price')}</p>\r\n            { planInterval === 'yearly' && ppg === '48' ? <div className='text-xs mb-4'>(billed yearly)</div> : '' }\r\n            <div className='py-4'>\r\n              <motion.button\r\n                className=\"text-white font-bold py-3 px-3 rounded-lg btn-ent\"\r\n                style={{backgroundColor: hexHash(pp_ctaclr)}}\r\n                whileHover={{ scale: 1.1, backgroundColor: hoverDarken(pp_ctaclr) }}\r\n                whileTap={{ scale: 0.9 }}\r\n                onHoverStart={() => {\r\n                  console.log(\"hover:\", data)\r\n                  console.log(\"hover2:\", data[0].currency)\r\n                }}\r\n                onClick={() => setPricing(62)}\r\n              >\r\n              {t('echo.pricing.vprice_02.enterprise.cta')}\r\n              </motion.button>\r\n            </div>\r\n            <div className=\"mb-6 plan-description \">\r\n              <ul className=\"text-sm text-gray-600\">\r\n                <li className=\"mb-2 text-left\"><br />\r\n                  <div className=\"font-bold\">{t('echo.pricing.vprice_02.enterprise.desc1')}</div>\r\n                  <div>{t('echo.pricing.vprice_02.enterprise.desc2')}</div>\r\n                </li>\r\n              </ul>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  };\r\n\r\n  return (\r\n      <div className=\"v-pricing-01 pricing bg-gray-100\">\r\n        <div className=\"pricing_columns container mx-auto py-20 md:py-10\">\r\n          <div className=\"flex flex-col items-center pt-50px md:pt py-10 lg:py-14\">\r\n            <h1 className=\"text-4xl lg:text-4xl font-bold text-center mb-4 min-h-[40px] text-[#336CEB]\">\r\n              Pricing Plan\r\n            </h1>\r\n            <div className='text-normal'>345 Unlock the full potential of {appName}</div>\r\n\r\n            { showToggle ? (\r\n              <div className=\"p-4\">\r\n                <div className=\"text-1xl lg:text-1xl font-bold text-center mb-4\">\r\n                  {/* <div>Choose between our monthly and yearly options below</div> */}\r\n                </div>\r\n                <div className=\"flex items-center justify-center w-full mb-8\">\r\n                  <label for=\"toggleB\" className=\"flex items-center cursor-pointer\">\r\n                    <div className={`${planInterval === 'monthly' ? \"text-blue-700 font-bold\" : \"text-gray-700\"} mr-3 uppercase`}>\r\n                      Monthly\r\n                    </div>\r\n                    <div className=\"relative\">\r\n                      <input type=\"checkbox\" id=\"toggleB\" className=\"sr-only toggle\" onChange={intervalChange} defaultChecked={billedAnnualDisplay}/>\r\n                      <div className=\"block bg-gray-400 w-12 h-6 rounded-full\"></div>\r\n                      <div className=\"dot absolute left-1 top-1 bg-white w-4 h-4 rounded-full transition\"></div>\r\n                    </div>\r\n                    <div className={`${planInterval === 'yearly' ? \"text-blue-700 font-bold\" : \"text-gray-700\"} ml-3 uppercase`}>\r\n                      Yearly\r\n                    </div>\r\n                  </label>\r\n                </div>\r\n              </div>\r\n            ) : \"\"}\r\n\r\n            { showToggle ? (\r\n              <div className=\"pricing-toggle flex flex-col lg:flex-row justify-center\">\r\n                {data?.map((plan, index) => (\r\n                  checkPlanInterval(plan) ? (\r\n                    <div key={index} className={`price_col w-full lg:w-[330px] xl:w-[380px] text-center px-4 mb-8 ${ index === 1 ? \"relative\" : \"\" }`}>\r\n                      <div className=\"bg-white rounded-lg shadow-lg overflow-hidden\">\r\n                        <div className=\"px-6 py-10 price-content\">\r\n                          <h3 className=\"text-xl font-bold mb-4\">{plan.label}</h3>\r\n                          <PriceFormatted plan={plan}/>\r\n                          <div className='py-4'>\r\n                            <motion.button\r\n                              className=\"text-white font-bold py-3 px-6 rounded-lg\"\r\n                              style={{backgroundColor: hexHash(pp_ctaclr)}}\r\n                              whileHover={{ scale: 1.1, backgroundColor: hoverDarken(pp_ctaclr) }}\r\n                              whileTap={{ scale: 0.9 }}\r\n                              onClick={() => setPricing(plan.plan_id)}\r\n                            >\r\n                              Subscribe\r\n                            </motion.button>\r\n                          </div>\r\n                          <div className=\"mb-6 plan-description \">\r\n                            <ul className=\"text-sm text-gray-600\">\r\n                              { plan.plan_description ? <li className=\"mb-2\" dangerouslySetInnerHTML={{__html: displayDescription(plan)}}></li> : null }\r\n                            </ul>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                      { planInterval === 'yearly' && showToggle ? <div className=\"absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-blue-100 text-blue-500 font-bold py-1 px-4 text-xs span-highlight\">\r\n                        <div>Up to <span className='font-bold underline-offset-1'>20% OFF</span> on an annual subscription</div>\r\n                      </div> : null }\r\n                    </div>\r\n                  ) : \"\"\r\n                ))}\r\n                {(enterprise === 'on' && ppg !== '46' && planInterval === 'yearly' && data[0].currency === 'USD') ?\r\n                  <>\r\n                  {enterpriseTab()}\r\n                  </>\r\n                : null }\r\n              </div>\r\n            ) : (\r\n              <div className=\"flex flex-col lg:flex-row justify-center\">\r\n                {data?.map((plan, index) => (\r\n                  <div key={index} className={`price_col w-full lg:w-[330px] xl:w-[380px] text-center px-4 mb-8 ${ index === 1 ? \"relative\" : \"\" }`}>\r\n                    <div className=\"bg-white rounded-lg shadow-lg overflow-hidden\">\r\n                      <div className=\"px-6 py-10 price-content\">\r\n                        <h3 className=\"text-xl font-bold mb-4\">{plan.label}</h3>\r\n                        {\r\n                        plan.label.toLowerCase()===\"enterprise\" ? <p className=\"text-3xl md:text-4x1 font-bold text-gray-800 mb-4\">Custom Plan</p> :\r\n                        <PriceFormatted plan={plan}/>\r\n                        }\r\n                        <div className='py-4'>\r\n                          <motion.button\r\n                            className=\"text-white font-bold py-3 px-6 rounded-lg\"\r\n                            style={{backgroundColor: hexHash(pp_ctaclr)}}\r\n                            whileHover={{ scale: 1.1, backgroundColor: hoverDarken(pp_ctaclr) }}\r\n                            whileTap={{ scale: 0.9 }}\r\n                            onClick={() => setPricing(plan.plan_id)}\r\n                          >\r\n                            Subscribe\r\n                          </motion.button>\r\n                        </div>\r\n                        <div className=\"mb-6 plan-description \">\r\n                          <ul className=\"text-sm text-gray-600\">\r\n                            { plan.plan_description ? <li className=\"mb-2\" dangerouslySetInnerHTML={{__html: displayDescription(plan)}}></li> : null }\r\n                          </ul>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                    { index === 1 ? <div className=\"absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-blue-100 text-blue-500 font-bold py-1 px-4 text-xs span-highlight\">\r\n                      <span>Most Popular</span>\r\n                    </div> : null }\r\n                  </div>\r\n                ))}\r\n                {(enterprise === 'on' && ppg !== '46' && data[0].currency === 'USD') ?\r\n                  <>\r\n                  {enterpriseTab()}\r\n                  </>\r\n                : null }\r\n              </div>\r\n            )}\r\n\r\n            <p className=\"text-xs max-w-md text-center leading-relaxed mb-10 lg:mb-12\">\r\n              *The pricing is exclusive of taxes and additional local tax may be collected.\r\n            </p>\r\n\r\n            { tpreviews === 'on' ?\r\n            <>\r\n              <TpReviews/>\r\n            </>\r\n          : null }\r\n          </div>\r\n        </div>\r\n\r\n      </div>\r\n  )\r\n}\r\n\r\nexport default VPrice03;"], "names": ["props", "data", "setPricing", "ppg", "Get<PERSON><PERSON><PERSON>", "process", "pp_ctaclr", "appName", "useState", "showToggle", "includes", "billedAnnualDisplay", "planInterval", "setPlanInterval", "tpreviews", "enterprise", "t", "useTranslation", "displayDescription", "plan", "basicPlan", "proPlan", "enterprisePlan", "proMaxPlan", "plan_type", "toLowerCase", "plan_id", "enterpriseTab", "_jsx", "className", "children", "_jsxs", "motion", "button", "style", "backgroundColor", "hexHash", "whileHover", "scale", "hoverDarken", "whileTap", "onHoverStart", "console", "log", "currency", "onClick", "for", "type", "id", "onChange", "defaultChecked", "map", "index", "payment_interval", "checkPlanInterval", "label", "PriceFormatted", "plan_description", "dangerouslySetInnerHTML", "__html", "_Fragment", "TpReviews"], "sourceRoot": ""}