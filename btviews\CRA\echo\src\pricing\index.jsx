import React, { Suspense, lazy, useEffect } from "react";
import "./components/style.css";
// import Header from "../header/headerlogo";
import { useQuery } from "react-query";
import axios from "axios";
import { GetCookie, SetCookie } from "../core/utils/cookies";
import { Helmet } from "react-helmet";
import _ from "underscore";
import { getPrefixLocation } from "../core/utils/main.jsx";

import VPrice from "./components/vprice.jsx";
const VPriceDL = lazy(() => import("./components/vprice_dl.jsx"));
const VPrice01 = lazy(() => import("./components/vprice_01.jsx"));
const VPrice02 = lazy(() => import("./components/vprice_02.jsx"));
const VPrice03 = lazy(() => import("./components/vprice_03.jsx"));
const VPrice04 = lazy(() => import("./components/vprice_04.jsx"));

const VPriceClusterPlan = lazy(() =>
  import("./components/vprice_cluster_plan.jsx")
);

var plan = null;
var showToggle = false;
var hasAnnual = false;
async function getPPG() {
  var ppg = GetCookie("ppg") ?? process.env.REACT_APP_DEFAULT_PPG ?? "14";
  if (plan) return plan;
  const response = await axios.post(
    `${process.env.REACT_APP_API_URL}/get-pricing`,
    { ppg },
    { headers: { "content-type": "application/x-www-form-urlencoded" } }
  );
  const output = response.data;
  if (output.success) {
    plan = output.data.filter((value) => {
      if (parseInt(ppg) === 59)
        return value.plan_id !== process.env.REACT_APP_ENTERPRISE_ID;

      return true;
    });
    hasAnnual = _.some(plan, function (o) {
      return o.payment_interval.toLowerCase() === "yearly";
    });
    showToggle =
      _.some(plan, function (o) {
        return o.payment_interval.toLowerCase() === "monthly";
      }) && hasAnnual;
    return plan;
  } else {
    return [];
  }
}

function Pricing() {
  const { data } = useQuery("users", getPPG);

  useEffect(() => {
    // window.close();
  }, []);

  if (data === undefined) return;
  const tk = GetCookie("access");

  const setPricing = function (id, ent) {
    SetCookie("pricing", id, { path: "/" });
    const redirectToPay = "https://" + getPrefixLocation() + "start.ai-pro.org/pay";
    if (window.top !== window.self) {
      window.top.location.href = redirectToPay;
    } else {
      setTimeout(() => {
        window.location.href = redirectToPay;
      }, 300);
    }
  };
  const vPrice_token = GetCookie("vprice") ? GetCookie("vprice") : "";
  const pp_echo = GetCookie("pp_echo") ? GetCookie("pp_echo") : "";
  const commonProps = { showToggle, hasAnnual, data, setPricing };
  const ppg = GetCookie("ppg");

  return (
    <>
      <Helmet auth={tk} >
        <title>AI Pro | Plans and Pricing</title>
        <meta
          name="description"
          content="Discover our flexible AI pricing plans designed to suit your needs. Explore our features and find the perfect plan to elevate your projects."
        />
      </Helmet>
      {/* <Header auth={tk} /> */}
      <Suspense fallback={null}>
        {(() => {
          if (pp_echo === '02') {
            return <VPrice04 {...commonProps} />;
          } else if (ppg === "108") {
            return <VPriceDL {...commonProps} />;
          } else if (ppg === "118") {
            return <VPriceClusterPlan {...commonProps} />;
          } else if (
            (vPrice_token === "" && ppg === "46") ||
            vPrice_token === "vP1zx12mXk"
          ) {
            return <VPrice01 {...commonProps} />;
          } else if (vPrice_token === "vP2xyYxjjj") {
            return <VPrice02 {...commonProps} />;
          } else if (vPrice_token === "waYmXFAgLs") {
            return <VPrice03 {...commonProps} />;
          } else {
            return <VPrice {...commonProps} />;
          }
        })()}
      </Suspense>
    </>
  );
}

export default Pricing;
