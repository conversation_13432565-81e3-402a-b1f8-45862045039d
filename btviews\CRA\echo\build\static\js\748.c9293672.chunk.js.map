{"version": 3, "file": "static/js/748.c9293672.chunk.js", "mappings": "uNA2KA,QAlKA,YACEA,EAAAA,EAAAA,MACA,MAAOC,IAAQC,EAAAA,EAAAA,UAASC,OAAOC,YACxBC,EAASC,IAAcJ,EAAAA,EAAAA,UAAS,KAChCK,EAAKC,IAAUN,EAAAA,EAAAA,UAAS,KACxBO,EAAmBC,IAAwBR,EAAAA,EAAAA,WAAS,IAG3DS,EAAAA,EAAAA,WAAU,KACR,MAAMC,GAAaC,EAAAA,EAAAA,IAAU,QAC7B,IAAIC,EAAkBX,OAAOY,SAASC,KAClCC,EAAS,GAOb,GALIH,EAAgBI,QAAQ,YAAc,EACxCD,EAAS,WACAH,EAAgBI,QAAQ,QAAU,IAC3CD,EAAS,QAEQ,YAAfL,EAA0B,CAC5B,MAAMO,EAAkBC,WAAW,KACjCjB,OAAOY,SAAW,WAAaE,EAAS,uBACvC,KACH,MAAO,IAAMI,aAAaF,EAC5B,CAAO,GAAmB,YAAfP,EAA0B,CACnC,MAAMO,EAAkBC,WAAW,KACjCjB,OAAOY,SAAW,WAAaE,EAAS,uBACvC,KACH,MAAO,IAAMI,aAAaF,EAC5B,GAEC,KAEHR,EAAAA,EAAAA,WAAU,KAEHV,EAAKqB,OAIVnB,OAAOoB,GAAK,WACVC,QAAQC,IAAI,cACd,EAGAtB,OAAOoB,GAAG,QAAS,YAEfpB,OAAOuB,IACTvB,OAAOuB,IAAI,QAAS,iBAAkB,CACpCC,MAAO,wBACPC,SAAU,CAAC,CACTC,WAAY,GAAG5B,EAAK6B,WACpBC,aAAc,GAAG9B,EAAK+B,OACtBC,aAAc,GAAGhC,EAAKqB,YAI1BE,QAAQU,MAAM,8BApBd/B,OAAOY,SAASC,KAAO,UAsBxB,CAACf,KAEJU,EAAAA,EAAAA,WAAU,KACR,MAAMwB,GAActB,EAAAA,EAAAA,IAAU,eACxBuB,GAASvB,EAAAA,EAAAA,IAAU,UAErBuB,GAAWD,GAAeC,EAAOC,SAAS,WAC5CC,EAAAA,EAAMC,KAAK,uCAAyB,CAClCC,OAAQL,GACP,CAAEM,QAAS,CAAE,eAAgB,uCAAyCC,KAAMC,IAC7E,MAAMC,EAAOD,EAAIC,KACjB,GAAGA,EAAKC,QAAS,CACf,MAAMtC,EAAMqC,EAAKE,KACL,KAARvC,GACFwC,MAAMxC,GACLmC,KAAKM,GAAYA,EAASC,QAC1BP,KAAKO,IACJ,MAAMC,EAAUC,IAAIC,gBAAgBH,GAC9BH,EAAOO,SAASC,cAAc,KACpCR,EAAK9B,KAAOkC,EACZJ,EAAKS,aAAa,WAAY,oBAC9BF,SAASG,KAAKC,YAAYX,GAC1BA,EAAKY,QACLL,SAASG,KAAKG,YAAYb,IAGhC,EACAc,EAAAA,EAAAA,IAAa,cAAe,CAAEC,OAAQ,cAAeC,KAAM,SAG9D,IAGH,MAAMC,GAASlD,EAAAA,EAAAA,IAAU,WAAYA,EAAAA,EAAAA,IAAU,UAAY,GACrDmD,GAAUnD,EAAAA,EAAAA,IAAU,YAAaA,EAAAA,EAAAA,IAAU,WAAa,GAC1DN,GAAmB,MAAZyD,EAaCzD,IACVD,EAAW,4BACXE,EAAO,gBAdJuD,GACEA,EAAO1B,SAAS,UAAY0B,EAAO1B,SAAS,UAC7C7B,EAAOuD,GAEPvD,EAAO,WAAauD,GAEtBrD,GAAqB,GACrBJ,EAAW,cAEXA,EAAW,4BACXE,EAAO,gBAOX,MAAMc,EAAQrB,EAAKqB,MAqBnB,OAnBAX,EAAAA,EAAAA,WAAU,KAEJR,OAAO8D,KACT9D,OAAO8D,IAAIC,SAAS,CAAE,MAAS,GAAG5C,MAClCnB,OAAO8D,IAAIE,MAAM,eAAgB,CAC/B,SAAY,CAAC,CACT,WAAc,GAAGlE,EAAK6B,WACtB,aAAgB,GAAG7B,EAAK+B,OACxB,aAAgB,GAAG/B,EAAKqB,UAE1B,MAAS,GAAGrB,EAAKmE,UACjB,SAAY,GAAGnE,EAAKoE,SAASC,gBAC7B,YAAe,2BAGnB9C,QAAQU,MAAM,6BAEf,CAACZ,EAAOrB,EAAK6B,SAAU7B,EAAK+B,KAAM/B,EAAKqB,MAAOrB,EAAKmE,QAASnE,EAAKoE,YAGlEE,EAAAA,EAAAA,MAAAC,EAAAA,SAAA,CAAAC,SAAA,EACEC,EAAAA,EAAAA,KAACC,EAAAA,QAAM,CAACC,KAAM3E,KACdyE,EAAAA,EAAAA,KAAA,OAAKG,UAAU,gGAA+FJ,UAC5GC,EAAAA,EAAAA,KAAA,OAAKG,UAAU,uCAAsCJ,UACnDF,EAAAA,EAAAA,MAAA,OAAKM,UAAU,2BAA0BJ,SAAA,EACvCF,EAAAA,EAAAA,MAAA,MAAIM,UAAU,0DAAyDJ,SAAA,EAACC,EAAAA,EAAAA,KAAA,QAAMG,UAAU,2BAA0BJ,SAAC,cAAgB,wBACnIC,EAAAA,EAAAA,KAAA,OAAKG,UAAU,kBAAiBJ,UAC9BC,EAAAA,EAAAA,KAAA,OAAKG,UAAU,aAAYJ,UACvBF,EAAAA,EAAAA,MAAA,OAAKM,UAAU,iBAAgBJ,SAAA,CAAC,mFACiDC,EAAAA,EAAAA,KAAA,SAC7EjE,EAAoB,qDAAuD,4BAC7EiE,EAAAA,EAAAA,KAAA,SAAOI,KAAK,SAASC,GAAG,UAAUpD,MAAOL,YAIjDoD,EAAAA,EAAAA,KAACM,EAAAA,EAAOC,EAAC,CACPJ,UAAU,wDACVK,WAAY,CAAEC,MAAO,IAAKC,gBAAiB,WAC3CC,SAAU,CAAEF,MAAO,IACnBnE,KAAMT,EAAIkE,SAERpE,aAOhB,C", "sources": ["thankyou/index.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport './style.css';\r\nimport { motion } from \"framer-motion\";\r\nimport Header from '../header';\r\nimport { GetCookie, RemoveCookie } from '../core/utils/cookies';\r\nimport { Auth } from '../core/utils/auth';\r\nimport axios from 'axios';\r\nconst api_url = process.env.REACT_APP_API_URL || \"https://start.ai-pro.org/api\";\r\n\r\nfunction Thankyou() {\r\n  Auth();\r\n  const [user] = useState(window.user_data);\r\n  const [ctaText, setCtaText] = useState('');\r\n  const [url, setUrl] = useState('');\r\n  const [redirectMyAccount, setRedirectMyAccount] = useState(true);\r\n  // var CHATPDF_URL = process.env.REACT_APP_DEFAULT_PPG ? process.env.REACT_APP_DEFAULT_PPG : \"https://chatpdf.ai-pro.org\";\r\n\r\n  useEffect(() => {\r\n    const flowCookie = GetCookie('flow');\r\n    let currentLocation = window.location.href;\r\n    let prefix = \"\";\r\n\r\n    if (currentLocation.indexOf(\"staging\") > -1) {\r\n      prefix = \"staging.\";\r\n    } else if (currentLocation.indexOf(\"dev\") > -1) {\r\n      prefix = \"dev.\";\r\n    }\r\n    if (flowCookie === 'chatpdf') {\r\n      const redirectTimeout = setTimeout(() => {\r\n        window.location = \"https://\" + prefix + \"chatpdf.ai-pro.org/\";\r\n      }, 3000);\r\n      return () => clearTimeout(redirectTimeout);\r\n    } else if (flowCookie === 'landing') {\r\n      const redirectTimeout = setTimeout(() => {\r\n        window.location = \"https://\" + prefix + \"chatpro.ai-pro.org/\";\r\n      }, 8000);\r\n      return () => clearTimeout(redirectTimeout);\r\n    }\r\n\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n\r\n    if (!user.email){\r\n      window.location.href = '/login';\r\n      return;\r\n    }\r\n    window.qp = function () {\r\n      console.log('qp function');\r\n    };\r\n\r\n    // Call the overwritten qp function\r\n    window.qp('track', 'Purchase');\r\n\r\n    if (window.twq) {\r\n      window.twq('event', 'tw-oebtr-oebtv', {\r\n        value: \"Thank You Page (echo)\",\r\n        contents: [{\r\n          content_id: `${user.user_pid}`,\r\n          content_type: `${user.plan}`,\r\n          content_name: `${user.email}`\r\n        }]\r\n      });\r\n    } else {\r\n      console.error('Twitter script not loaded');\r\n    }\r\n  }, [user]);\r\n\r\n  useEffect(() => {\r\n    const avatarMaker = GetCookie('avatarmaker');\r\n    const appUrl = GetCookie('appurl');\r\n\r\n    if (appUrl && (avatarMaker && appUrl.includes('avatar'))) {\r\n      axios.post(`${api_url}/get-avatar`, {\r\n        folder: avatarMaker\r\n      }, { headers: { 'content-type': 'application/x-www-form-urlencoded' } }).then((res) => {\r\n        const data = res.data;\r\n        if(data.success) {\r\n          const url = data.link;\r\n          if (url !== '') {\r\n            fetch(url)\r\n            .then(response => response.blob())\r\n            .then(blob => {\r\n              const blobUrl = URL.createObjectURL(blob);\r\n              const link = document.createElement('a');\r\n              link.href = blobUrl;\r\n              link.setAttribute('download', 'highresimage.png');\r\n              document.body.appendChild(link);\r\n              link.click();\r\n              document.body.removeChild(link);\r\n            });\r\n          }\r\n        }\r\n        RemoveCookie('avatarmaker', { domain: '.ai-pro.org', path: '/' });\r\n      });\r\n    }\r\n  }, []);\r\n\r\n\r\n  const appurl = GetCookie('appurl') ? GetCookie('appurl') : '';\r\n  const unpFlow = GetCookie('unpFlow') ? GetCookie('unpFlow') : ''; // #28596\r\n  if(!url && unpFlow === '1') {\r\n    if(appurl) {\r\n      if(appurl.includes(\"http:\") || appurl.includes(\"https:\")) {\r\n        setUrl(appurl);\r\n      } else {\r\n        setUrl(\"https://\" + appurl);\r\n      }\r\n      setRedirectMyAccount(false);\r\n      setCtaText(\"Continue\");\r\n    } else {\r\n      setCtaText(\"Continue to your Account\");\r\n      setUrl('/my-account');\r\n    }\r\n  } else if (!url) {\r\n    setCtaText(\"Continue to your Account\");\r\n    setUrl('/my-account');\r\n  }\r\n\r\n  const email = user.email;\r\n\r\n  useEffect(() => {\r\n      // TikTok pixel tracking code\r\n    if (window.ttq) {\r\n      window.ttq.identify({ \"email\": `${email}` })\r\n      window.ttq.track('Payment Page', {\r\n        \"contents\": [{\r\n            \"content_id\": `${user.user_pid}`,\r\n            \"content_type\": `${user.plan}`,\r\n            \"content_name\": `${user.email}`\r\n          }],\r\n          \"value\": `${user.plan_id}`,\r\n          \"currency\": `${user.currency.toUpperCase()}`,\r\n          \"description\": \"Thank you Page (echo)\"\r\n      });\r\n    } else {\r\n      console.error('Tiktok script not loaded');\r\n    }\r\n  }, [email, user.user_pid, user.plan, user.email, user.plan_id, user.currency]);\r\n\r\n  return (\r\n    <>\r\n      <Header auth={user} />\r\n      <div className=\"Thankyou bg-gray-100 mt-[100px] md:mt-[50px] md:min-h-[85vh] flex justify-center items-center\">\r\n        <div className=\"container mx-auto py-10 px-4 sm:px-0\">\r\n          <div className=\"reg_col text-center mb-8\">\r\n            <h1 className=\"text-2xl lg:text-3xl font-bold text-center mb-6 lg:mb-8\"><span className=\"text-gradient font-black\">Thank you</span> for subscribing.</h1>\r\n            <div className=\"overflow-hidden\">\r\n              <div className=\"px-6 py-10\">\r\n                  <div className=\"relative block\">\r\n                    You now have access to our entire library of content, dedicated to AI learning.<br/>\r\n                    { redirectMyAccount ? \"Click below to continue to your main account page.\" : \"Click below to continue.\" }\r\n                    <input type=\"hidden\" id=\"tyemail\" value={email} />\r\n                  </div>\r\n              </div>\r\n            </div>\r\n            <motion.a\r\n              className=\"bg-blue-500 text-white font-bold py-3 px-6 rounded-lg\"\r\n              whileHover={{ scale: 1.1, backgroundColor: \"#5997fd\" }}\r\n              whileTap={{ scale: 0.9 }}\r\n              href={url}\r\n            >\r\n              { ctaText }\r\n            </motion.a>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </>\r\n  );\r\n}\r\n\r\nexport default Thankyou;"], "names": ["<PERSON><PERSON>", "user", "useState", "window", "user_data", "ctaText", "setCtaText", "url", "setUrl", "redirectMyAccount", "setRedirectMyAccount", "useEffect", "flowCookie", "Get<PERSON><PERSON><PERSON>", "currentLocation", "location", "href", "prefix", "indexOf", "redirectTimeout", "setTimeout", "clearTimeout", "email", "qp", "console", "log", "twq", "value", "contents", "content_id", "user_pid", "content_type", "plan", "content_name", "error", "avatar<PERSON><PERSON>", "appUrl", "includes", "axios", "post", "folder", "headers", "then", "res", "data", "success", "link", "fetch", "response", "blob", "blobUrl", "URL", "createObjectURL", "document", "createElement", "setAttribute", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "RemoveCookie", "domain", "path", "appurl", "unpFlow", "ttq", "identify", "track", "plan_id", "currency", "toUpperCase", "_jsxs", "_Fragment", "children", "_jsx", "Header", "auth", "className", "type", "id", "motion", "a", "whileHover", "scale", "backgroundColor", "whileTap"], "sourceRoot": ""}