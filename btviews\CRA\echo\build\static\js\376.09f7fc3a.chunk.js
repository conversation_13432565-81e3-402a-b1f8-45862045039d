"use strict";(self.webpackChunkv1=self.webpackChunkv1||[]).push([[376,9044,7250],{72608:(e,_,t)=>{t.d(_,{Z:()=>A});t(72791);const A=t.p+"static/media/AIPRO.0580715cba3cbc2179c46b033d087db9.svg"},45689:(e,_,t)=>{t.r(_),t.d(_,{default:()=>o});var A=t(72791),a=t(96347),s=t(27250),l=t(28891),r=t(74335),P=t(31243),R=t(95828),c=t.n(R),n=(t(92831),t(80184));const i=(0,r.bG)("access")?(0,r.bG)("access"):"";const o=function(){(0,A.useEffect)(()=>{c().options={positionClass:"toast-top-center"}},[]);const[e,_]=(0,A.useState)(""),[t,r]=(0,A.useState)(""),[R,o]=(0,A.useState)(""),[d,E]=(0,A.useState)(""),[D,p]=(0,A.useState)(""),[m,N]=(0,A.useState)(""),[x,h]=(0,A.useState)(""),[u,C]=(0,A.useState)(""),[O,T]=(0,A.useState)(""),[I,g]=(0,A.useState)(""),[U,b]=(0,A.useState)(""),[G,L]=(0,A.useState)(""),f=(0,l.gx)("/register-auth"),v=window.location.search,S=new URLSearchParams(v).get("pid");if(void 0===f||!1===f)return;return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(s.default,{auth:f}),(0,n.jsx)("div",{className:"Payment bg-gray-100 min-h-[600px] flex",children:(0,n.jsx)("div",{className:"container mx-auto py-10",children:(0,n.jsx)("div",{className:"flex flex-col items-center py-10 lg:py-16",children:(0,n.jsx)("div",{className:"flex flex-wrap md:flex-wrap justify-center w-full",children:(0,n.jsx)("div",{className:"pay_left px-4 mb-8 w-full md:w-1/2",children:(0,n.jsx)("div",{className:"bg-white rounded-lg shadow-lg overflow-hidden",children:(0,n.jsx)("div",{className:"px-6 pb-10",children:(0,n.jsxs)("div",{className:"",children:[(0,n.jsx)("h2",{className:"text-xl font-bold mb-4 pt-10",children:"Enter Billing Details"}),(0,n.jsxs)("div",{className:"mb-4",children:[(0,n.jsxs)("label",{className:"text-xs block mb-0 mt-1",htmlFor:"name",children:["Name on Card ",(0,n.jsx)("span",{className:"text-red-500",children:"*"}),D&&(0,n.jsx)("span",{className:"text-red-500 text-xs text-left w-full mb-2 inline",children:D})]}),(0,n.jsx)("input",{className:"w-full px-3 py-2 border border-gray-300 rounded fs-exclude",type:"text",id:"name",name:"name",placeholder:"John Doe",value:e,onChange:e=>{let t=e.target.value;t=t.replace(/[^A-Za-z ]/g,""),t=t.slice(0,50),_(t)},onKeyUp:e=>{_(e.target.value)}})]}),(0,n.jsxs)("div",{className:"mb-4",children:[(0,n.jsxs)("label",{className:"text-xs block mb-0 mt-1",htmlFor:"card-number",children:["Card Number ",(0,n.jsx)("span",{className:"text-red-500",children:"*"}),m&&(0,n.jsx)("span",{className:"text-red-500 text-xs text-left w-full mb-2 inline",children:m})]}),(0,n.jsx)("input",{className:"w-full px-3 py-2 border border-gray-300 rounded fs-exclude",type:"text",id:"card-number",name:"card-number",placeholder:"1234 5678 9012 3456",value:t,onChange:e=>{let _=e.target.value;_=_.replace(/\D/g,""),_=_.replace(/-/g,""),_=_.replace(/(\d{4})/g,"$1-"),_=_.replace(/-$/,""),_=_.slice(0,19),r(_)},onKeyUp:e=>{r(e.target.value)}})]}),(0,n.jsxs)("div",{className:"mb-4 flex",children:[(0,n.jsxs)("div",{className:"expdate w-full md:w-2/3 mr-2 md:mr-5",children:[(0,n.jsxs)("label",{className:"text-xs block mb-0 mt-1",htmlFor:"expiration-date",children:["Expiration Date ",(0,n.jsx)("span",{className:"text-red-500",children:"*"}),x&&(0,n.jsx)("span",{className:"text-red-500 text-xs text-left w-full mb-2 inline",children:x})]}),(0,n.jsx)("input",{className:"w-full px-3 py-2 border border-gray-300 rounded fs-exclude",type:"text",id:"expiration-date",name:"expiration-date",placeholder:"MM/YY",value:R,onChange:e=>{let _=e.target.value;_=_.replace(/\D/g,""),_=_.slice(0,4),_.length>=3&&(_=_.slice(0,2)+"/"+_.slice(2)),o(_)},onKeyUp:e=>{o(e.target.value)}})]}),(0,n.jsxs)("div",{className:" w-full sm:w-full md:w-2/4 flex",children:[(0,n.jsxs)("div",{className:"cvv w-full sm:w-3/6 mr-2 md:mr-5",children:[(0,n.jsxs)("label",{className:"text-xs block mb-0 mt-1",htmlFor:"cvv",children:["CVV ",(0,n.jsx)("span",{className:"text-red-500",children:"*"}),u&&(0,n.jsx)("span",{className:"text-red-500 text-xs text-left w-full mb-2 inline",children:u})]}),(0,n.jsx)("input",{className:"w-full px-3 py-2 border border-gray-300 rounded fs-exclude",type:"text",id:"cvv",name:"cvv",placeholder:"CVV",value:d,onChange:e=>{let _=e.target.value;_=_.replace(/\D/g,""),_=_.slice(0,5),E(_)},onKeyUp:e=>{E(e.target.value)}})]}),(0,n.jsxs)("div",{className:"zip w-full sm:w-3/6",children:[(0,n.jsxs)("label",{className:"text-xs block mb-0 mt-1",htmlFor:"zip",children:["Zip ",(0,n.jsx)("span",{className:"text-red-500",children:"*"}),I&&(0,n.jsx)("span",{className:"text-red-500 text-xs text-left w-full mb-2 inline",children:I})]}),(0,n.jsx)("input",{className:"w-full px-3 py-2 border border-gray-300 rounded fs-exclude",type:"text",id:"zip",name:"zip",placeholder:"ZIP",value:O,onChange:e=>{let _=e.target.value;T(_)},onKeyUp:e=>{T(e.target.value)}})]})]})]}),(0,n.jsxs)("div",{className:"mb-4",children:[(0,n.jsxs)("label",{className:"text-xs block mb-0 mt-1",htmlFor:"phone-number",children:["Phone Number ",(0,n.jsx)("span",{className:"text-red-500",children:"*"}),G&&(0,n.jsx)("span",{className:"text-red-500 text-xs text-left w-full mb-2 inline",children:G})]}),(0,n.jsx)("input",{className:"w-full px-3 py-2 border border-gray-300 rounded fs-exclude",type:"text",id:"phone-number",name:"phone-number",placeholder:"1234567890",value:U,onChange:e=>{let _=e.target.value;_=_.replace(/\D/g,""),b(_)},onKeyUp:e=>{b(e.target.value)}})]}),(0,n.jsx)(a.E.button,{className:"bg-blue-500 text-white font-bold py-3 px-6 rounded-lg w-full my-4 proceed-pmt",whileHover:{backgroundColor:"#5997fd"},whileTap:{scale:.9},onClick:()=>{p(""),N(""),h(""),C("");let _=!0;e.includes(" ")||(p("enter at least two names separated by a space"),_=!1),t||(N("required"),_=!1),R&&/^(0[1-9]|1[0-2])\/\d{2}$/.test(R)||(h("MM/YY"),_=!1),d&&/^\d{3,5}$/.test(d)||(C("required"),_=!1),O||(g("required"),_=!1),U||(L("required"),_=!1);var A=e.split(" "),a=A[0],s=A[A.length-1],l=R.split("/")[0],r=R.split("/")[1];""===a&&""===s?(p("required"),_=!1):""!==a&&""!==s||(p("enter at least two names separated by a space"),_=!1),_&&(document.querySelector(".loader-container").classList.add("active"),P.Z.post("http://localhost:9002/api/change-card",{tk:i,first_name:a,last_name:s,cc:t,ccmonth:l,ccyr:"20"+r,cvv:d,pid:S,phone:U,postal:O},{headers:{"content-type":"application/x-www-form-urlencoded"}}).then(function(e){let _=e.data;if(_.success)return _.redirect&&""!==_.redirect?void(window.location.href=_.redirect):(c().success("Success"),void setTimeout(function(){window.location.href="/manage-account"},2e3));document.querySelector(".loader-container").classList.remove("active"),_.data&&c().error(_.data.msg)}))},children:"Submit"})]})})})})})})})})]})}},27250:(e,_,t)=>{t.r(_),t.d(_,{default:()=>P});var A=t(72791),a=t(72608),s=t(19878),l=t(28891),r=(t(29534),t(80184));const P=function(e){let{hideNavLink:_,auth:P=(0,l.gx)()}=e;const R=window.location.pathname,c=/\/start-chatgpt-go\/?$/.test(R),n=/\/text-to-image\/?$/.test(R),i=/\/start-chatgpt-v2\/?$/.test(R),o=R.includes("/register-auth"),d=!_,E={NODE_ENV:"production",PUBLIC_URL:"/themes/echo",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_API_URL:"http://localhost:9002/api",REACT_APP_BASE_URL:"http://localhost:9002/",REACT_APP_BASIC_ANNUAL_DOWNGRADE_ID:"15",REACT_APP_BASIC_ANNUAL_DOWNGRADE_ID_AED:"45",REACT_APP_BASIC_ANNUAL_DOWNGRADE_ID_BRL:"37",REACT_APP_BASIC_ANNUAL_DOWNGRADE_ID_EUR:"33",REACT_APP_BASIC_ANNUAL_DOWNGRADE_ID_GBP:"29",REACT_APP_BASIC_ANNUAL_DOWNGRADE_ID_SAR:"41",REACT_APP_BASIC_ANNUAL_UPGRADE_ID:"16,20,73,75",REACT_APP_BASIC_ANNUAL_UPGRADE_ID_AED:"46,48,84,85",REACT_APP_BASIC_ANNUAL_UPGRADE_ID_BRL:"38,40,80,81",REACT_APP_BASIC_ANNUAL_UPGRADE_ID_EUR:"34,36,78,79",REACT_APP_BASIC_ANNUAL_UPGRADE_ID_GBP:"30,32,76,77",REACT_APP_BASIC_ANNUAL_UPGRADE_ID_SAR:"42,44,82,83",REACT_APP_BASIC_UPGRADE_ID:"16,19,20,73,75",REACT_APP_BASIC_UPGRADE_ID2:"16,20,73,75",REACT_APP_BASIC_UPGRADE_ID_AED:"46,47,48,84,85",REACT_APP_BASIC_UPGRADE_ID_BRL:"38,39,40,80,81",REACT_APP_BASIC_UPGRADE_ID_EUR:"34,35,36,78,79",REACT_APP_BASIC_UPGRADE_ID_GBP:"30,31,32,76,77",REACT_APP_BASIC_UPGRADE_ID_SAR:"42,43,44,82,83",REACT_APP_BOT_TOKEN:"XXLx73ThdbDdKDmqajsv",REACT_APP_BTUTIL_API_URL:"https://dev.api.ai-pro.org/",REACT_APP_BTUTIL_CSS_URL:"https://dev.api.ai-pro.org/ext-app/css/btutil-regUpgradeModal-v1.min.css?ver=",REACT_APP_CHATHEAD_URL:"https://staging.sitebot.ai-pro.org/chat.js",REACT_APP_DEFAULT_PPG:"12",REACT_APP_DOWNGRADE_ID:"29,30,31,32",REACT_APP_DOWNGRADE_ID_AED:"45,46,47,48",REACT_APP_DOWNGRADE_ID_BRL:"37,38,39,40",REACT_APP_DOWNGRADE_ID_EUR:"33,34,35,36",REACT_APP_DOWNGRADE_ID_GBP:"29,30,31,32",REACT_APP_DOWNGRADE_ID_SAR:"41,42,43,44",REACT_APP_ENTERPRISE_ID:"62",REACT_APP_HOST_ENV:"production",REACT_APP_PROMAX_ANNUAL_DOWNGRADE_ID:"15,16,19,20,73",REACT_APP_PROMAX_ANNUAL_DOWNGRADE_ID_AED:"45,46,47,48,84",REACT_APP_PROMAX_ANNUAL_DOWNGRADE_ID_BRL:"37,38,39,40,80",REACT_APP_PROMAX_ANNUAL_DOWNGRADE_ID_EUR:"33,34,35,36,78",REACT_APP_PROMAX_ANNUAL_DOWNGRADE_ID_GBP:"29,30,31,32,76",REACT_APP_PROMAX_ANNUAL_DOWNGRADE_ID_SAR:"41,42,43,44,82",REACT_APP_PROMAX_ANNUAL_UPGRADE_ID:"62",REACT_APP_PROMAX_DOWNGRADE_ID:"70,74 #PRO YEARLY, PRO MONTHLY",REACT_APP_PROMAX_DOWNGRADE_ID_AED:"48,46 #PRO YEARLY, PRO MONTHLY",REACT_APP_PROMAX_DOWNGRADE_ID_BRL:"40,38 #PRO YEARLY, PRO MONTHLY",REACT_APP_PROMAX_DOWNGRADE_ID_EUR:"36,34 #PRO YEARLY, PRO MONTHLY",REACT_APP_PROMAX_DOWNGRADE_ID_GBP:"32,30 #PRO YEARLY, PRO MONTHLY",REACT_APP_PROMAX_DOWNGRADE_ID_SAR:"44,42 #PRO YEARLY, PRO MONTHLY",REACT_APP_PROMAX_UPGRADE_ID:"75,62  YEARLY, ENTERPRISE",REACT_APP_PROMAX_UPGRADE_ID_BRL:"81 #PROMAX YEARLY",REACT_APP_PROMAX_UPGRADE_ID_EUR:"79 #PROMAX YEARLY",REACT_APP_PROMAX_UPGRADE_ID_GBP:"77 #PROMAX YEARLY",REACT_APP_PROMAX_UPGRADE_ID_SAR:"85 #PROMAX YEARLY",REACT_APP_PRO_ANNUAL_DOWNGRADE_ID:"15,16,19",REACT_APP_PRO_ANNUAL_DOWNGRADE_ID_AED:"45,46,47",REACT_APP_PRO_ANNUAL_DOWNGRADE_ID_BRL:"37,38,39",REACT_APP_PRO_ANNUAL_DOWNGRADE_ID_EUR:"33,34,35",REACT_APP_PRO_ANNUAL_DOWNGRADE_ID_GBP:"29,30,31",REACT_APP_PRO_ANNUAL_DOWNGRADE_ID_SAR:"41,42,43",REACT_APP_PRO_ANNUAL_UPGRADE_ID:"62",REACT_APP_PRO_ANNUAL_UPGRADE_ID_AED:"84,85",REACT_APP_PRO_ANNUAL_UPGRADE_ID_BRL:"80,81",REACT_APP_PRO_ANNUAL_UPGRADE_ID_EUR:"78,79",REACT_APP_PRO_ANNUAL_UPGRADE_ID_GBP:"76,77",REACT_APP_PRO_ANNUAL_UPGRADE_ID_SAR:"82,83",REACT_APP_PRO_DOWNGRADE_BASIC_ID:"15,19",REACT_APP_PRO_DOWNGRADE_BASIC_ID_AED:"45,47",REACT_APP_PRO_DOWNGRADE_BASIC_ID_BRL:"37,39",REACT_APP_PRO_DOWNGRADE_BASIC_ID_EUR:"33,35",REACT_APP_PRO_DOWNGRADE_BASIC_ID_GBP:"29,31",REACT_APP_PRO_DOWNGRADE_BASIC_ID_SAR:"41,43",REACT_APP_PRO_MAX_UPGRADE_ID:"73,75",REACT_APP_PRO_MAX_UPGRADE_ID_AED:"84,85",REACT_APP_PRO_MAX_UPGRADE_ID_BRL:"80,81",REACT_APP_PRO_MAX_UPGRADE_ID_EUR:"78,79",REACT_APP_PRO_MAX_UPGRADE_ID_GBP:"76,77",REACT_APP_PRO_MAX_UPGRADE_ID_SAR:"80,81",REACT_APP_PRO_UPGRADE_ID:"20,73,75",REACT_APP_PRO_UPGRADE_ID_AED:"48,84,85",REACT_APP_PRO_UPGRADE_ID_BRL:"40,80,81",REACT_APP_PRO_UPGRADE_ID_EUR:"36,78,79",REACT_APP_PRO_UPGRADE_ID_GBP:"32,76,77",REACT_APP_PRO_UPGRADE_ID_SAR:"44,82,83",REACT_APP_STRIPE_PUBLIC_KEY_LIVE:"pk_live_51MH0TVLtUaKDxQEZNwiJOL1O8aLIA6fQEyI7cqjDnuSnMXwnOjOtu2JHjRD6PhF6hyoQAfM91dxrxNUEdyoCv9m900CPfMnfSk",REACT_APP_STRIPE_PUBLIC_KEY_TEST:"pk_test_51MH0TVLtUaKDxQEZH5ODSKmyw5TSm1lEVwyKkhVbNPZCv83lu4xL2aK8NbiJkkeG9XJt6td7kRLET8gpby37dKTs00uLTgkXVr",REACT_APP_TOOLS_URL:'{"chatbot":"https://chat.ai-pro.org/chatbot","chat":"https://chat.ai-pro.org/chat","chatpro":"https://chatpro.ai-pro.org/chat","chatgpt":"https://chatgpt.ai-pro.org","chatpdf":"https://chatpdf.ai-pro.org","convert2english":"https://app.ai-pro.org/convert-to-proper-english","interiorgpt":"https://interiorgpt.ai-pro.org","promptlibrary":"https://chatlibrary.ai-pro.org","removebg":"https://clearbg.ai-pro.org","restorephoto":"https://restorephotos.ai-pro.org","stablediffusion":"https://ai-pro.org/ai-tool-stable-diffusion","txt2img":"https://app.ai-pro.org/create-art"}'}.REACT_APP_ShowMaintenanceBanner||"";(0,A.useEffect)(()=>{const e=D(a.Z,"image"),_=D(s,"image");return document.head.append(e,_),o||Promise.all([t.e(7749),t.e(1707)]).then(t.bind(t,51707)),()=>{e.remove(),_.remove()}},[o]);const D=(e,_)=>{const t=document.createElement("link");return t.rel="preload",t.href=e,t.as=_,t};return(0,r.jsxs)(r.Fragment,{children:[E&&(0,r.jsx)("div",{id:"maintenance-container",className:"p-[5px] bg-[#f7a73f] text-center",children:"We are currently undergoing maintenance. We're expecting to be back at 4 AM EST."}),(0,r.jsx)("header",{id:"header",className:"headernav border-gray-500 bg-white text-black w-3/4 mx-auto rounded-3xl mt-3 py-2 z-[8888] "+(E?"top-[60px]":""),children:(0,r.jsxs)("div",{className:"container mx-auto flex justify-between items-center px-4",children:[(0,r.jsxs)("picture",{className:"aiprologo",children:[(0,r.jsx)("source",{type:"image/webp",srcSet:s,width:"150",height:"52",className:"aiprologo"}),(0,r.jsx)("img",{src:a.Z,alt:"AI-Pro Logo",className:"aiprologo"})]}),(c||n||i)&&d&&(0,r.jsx)("nav",{className:"text-xs lg:text-sm block inline-flex",id:"menu",children:(0,r.jsx)("ul",{className:"headnav flex inline-flex",children:(0,r.jsx)("li",{className:"mr-1 md:mr-2 lg:mr-6",children:(0,r.jsx)("a",{href:P?"/my-account":"/login",className:"font-bold","aria-label":P?"my-account":"login",children:P?"My Apps":"LOG IN"})})})})]})})]})}},29534:()=>{},19878:(e,_,t)=>{e.exports=t.p+"static/media/AIPRO.84104dfd05446283b05c.webp"}}]);
//# sourceMappingURL=376.09f7fc3a.chunk.js.map