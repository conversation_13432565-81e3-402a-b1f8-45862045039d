"use strict";(self.webpackChunkv1=self.webpackChunkv1||[]).push([[7087,9044],{71201:(e,t,s)=>{s.r(t),s.d(t,{default:()=>m});var n=s(72791),r=s(28891),o=s(74335),a=s(54270),c=s(10728),i=s(31243),l=s(64429),d=s(30203),u=s(80184);const h={1:(0,u.jsx)(d.dXA,{}),2:(0,u.jsx)(d.sjm,{}),3:(0,u.jsx)(d.ct5,{}),4:(0,u.jsx)(d.OWE,{}),5:(0,u.jsx)(d.xbg,{})},m=e=>{const t=(0,r.gx)(),[s,d]=(0,n.useState)(null),[m,x]=(0,n.useState)(!1),[p,g]=(0,n.useState)(!0),[j,w]=(0,n.useState)(!1);if((0,n.useEffect)(()=>{if(m){const e=setTimeout(()=>{window.location.href="/my-account"},3e3);return()=>clearTimeout(e)}},[m]),(0,n.useEffect)(()=>{if(t){(async()=>{try{(await i.Z.post("http://localhost:9002/api/get-subscription",{tk:(0,o.bG)("access")},{headers:{"content-type":"application/x-www-form-urlencoded"}})).data.success?w(!0):window.location.href="/my-account"}catch(e){console.error("Error getting subscription:",e),window.location.href="/my-account"}finally{g(!1)}})()}else!1===t&&(window.location.href="/login")},[t]),void 0===t||p)return null;const y=async e=>{try{return(await i.Z.post("http://localhost:9002/api/set-mood-rating",{user_email:t.email,rating:e},{headers:{"Content-Type":"application/json"}})).data}catch(e){throw console.error("Error setting mood rating:",e),e}};return(0,u.jsx)(u.Fragment,{children:j&&(0,u.jsx)(u.Fragment,{children:p?(0,u.jsx)(l.g,{}):(0,u.jsxs)(u.Fragment,{children:[(0,u.jsxs)(a.q,{children:[(0,u.jsx)("meta",{name:"robots",content:"noindex, nofollow"}),(0,u.jsx)("title",{children:"AI-Pro | Experience Survey"}),(0,u.jsx)("meta",{name:"description",content:"Experience Survey"})]}),(0,u.jsx)(c.default,{auth:t}),(0,u.jsx)("div",{className:"mood-survey bg-gray-100",children:(0,u.jsx)("div",{className:"mood-container",children:m?(0,u.jsxs)("div",{className:"survey-col",children:[(0,u.jsx)("h1",{className:"title text-gradient",children:"Thank you for your feedback!"}),(0,u.jsx)("p",{className:"description",children:"We've received your survey and appreciate your feedback. Even small suggestions can lead to great enhancements."})]}):(0,u.jsxs)("div",{className:"survey-col",children:[(0,u.jsx)("h1",{className:"title",children:"How's your experience with us?"}),(0,u.jsx)("div",{className:"rating-icons",children:Object.keys(h).map(e=>(0,u.jsx)("div",{onClick:()=>(async e=>{try{d(e),e>=4&&(await y(e),window.location.href="https://www.trustpilot.com/review/ai-pro.org")}catch(e){console.error(e)}})(e),className:"rating-icon "+(s===e?"active":""),children:h[e]},e))}),s>=1&&s<=3&&(0,u.jsx)("button",{className:"submit-button",onClick:async()=>{if(s)try{const e=await y(s);e.success?x(!0):console.error("Failed to submit rating:",e)}catch(e){console.error("Error submitting rating:",e)}else console.error("Rating not selected!")},children:"Submit"})]})})})]})})})}}}]);
//# sourceMappingURL=7087.845c80ba.chunk.js.map