"use strict";(self.webpackChunkv1=self.webpackChunkv1||[]).push([[3853],{23853:(t,r,e)=>{e.d(r,{rDJ:()=>o});var n=e(89983);function o(t){return(0,n.w_)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"}},{tag:"circle",attr:{cx:"12",cy:"12",r:"3"}}]})(t)}}}]);
//# sourceMappingURL=3853.d34de289.chunk.js.map