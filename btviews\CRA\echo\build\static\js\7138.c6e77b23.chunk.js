"use strict";(self.webpackChunkv1=self.webpackChunkv1||[]).push([[7138],{96506:(e,t,i)=>{i.d(t,{Z:()=>o});var l=i(72791),n=i(80184);const o=e=>{let{src:t,alt:i,...o}=e;return(0,n.jsx)(l.<PERSON>,{fallback:(0,n.jsx)("div",{children:"Loading..."}),children:(0,n.jsx)("img",{src:t,alt:i,...o})})}},19009:(e,t,i)=>{i.d(t,{BV:()=>s,aI:()=>o,im:()=>a,on:()=>n});var l=i(72791);i(80184);function n(){const e=document.createElement("canvas");return!(!e.getContext||!e.getContext("2d"))&&0===e.toDataURL("image/webp").indexOf("data:image/webp")}function o(e){return null==e||e.length<6||!/^[0-9a-f]*$/i.test(e)?"#1559ED":`#${e=e.slice(0,6)}`}function a(e){(null==e||e.length<6||!/^[0-9a-f]*$/i.test(e))&&(e="1559ED");let t=parseInt(e.slice(0,2),16),i=parseInt(e.slice(2,4),16),l=parseInt(e.slice(4,6),16);t=Math.max(0,Math.min(255,t-.15*t)),i=Math.max(0,Math.min(255,i-.15*i)),l=Math.max(0,Math.min(255,l-.15*l));const n=e=>Math.round(e).toString(16).padStart(2,"0");return`#${n(t)}${n(i)}${n(l)}`}function s(){const[e,t]=(0,l.useState)(!1),[i,n]=(0,l.useState)(!1),[o,a]=(0,l.useState)(!1);return(0,l.useEffect)(()=>{const e=()=>{const e=window.innerWidth;t(e>430),n(e>729&&e<=828),a(e>901)};return e(),window.addEventListener("resize",e),()=>{window.removeEventListener("resize",e)}},[]),{isMobile:e,isTablet:i,isDesktop:o}}},47138:(e,t,i)=>{i.r(t),i.d(t,{default:()=>b});var l=i(72791),n=i(96506),o=i(19009),a=i(74335),s=i(3633),r=i(5714),c=i(74333),d=i(27070),p=i(56649),h=i(40642),g=i(31243),w=i(39230),x=i(80184);const b=e=>{let{handleRedirect:t,regAppleShown:i,regGoogleShown:b,reg_google:u,reg_apple:m,isAiHub:f=!1}=e;const{t:j}=(0,w.$G)(),y=(()=>{try{if(window.top!==window.self){const e=window.top.document.cookie.match(/lp=([^;]+)/);return e?e[1]:null}return(0,a.bG)("lp")}catch(e){return(0,a.bG)("lp")}})(),v=(0,o.on)()?c:s,N=(0,o.on)()?d:r,k=(0,o.on)()?h:p,[E,D]=(0,l.useState)(""),[C,G]=(0,l.useState)(""),[L,A]=(0,l.useState)(""),R=window.screen.width,_=`width=600,height=700,top=${(window.screen.height-700)/2},left=${(R-600)/2}`;function S(){try{if("aihub"===y){window.top.open(C,"AppleRegister",_)||(window.top.location.href=C)}else{window.open(C,"AppleRegister",_)||(window.location.href=C)}}catch(e){"aihub"===y?window.top.location.href=C:window.location.href=C}}function Z(){try{if("aihub"===y){window.top.open(L,"AppleRegister",_)||(window.top.location.href=L)}else{window.open(L,"AppleRegister",_)||(window.location.href=L)}}catch(e){"aihub"===y?window.top.location.href=L:window.location.href=L}}function $(){try{if("aihub"===y){window.top.open(E,"GoogleRegister",_)||(console.log("Error opening new GoogleRegister"),window.top.location.href=E)}else{window.open(E,"GoogleRegister",_)||(console.log("Error opening new GoogleRegister"),window.location.href=E)}}catch{console.log("Error opening new window"),"aihub"===y?window.top.location.href=E:window.location.href=E}}return(0,l.useEffect)(()=>{let e=!0;return(async()=>{try{const t=await g.Z.post("http://localhost:9002/api/register-google");e&&t.data.success&&t.data.url&&D(t.data.url)}catch(e){console.error("Error fetching Google register URL:",e)}})(),(async()=>{try{const e=await g.Z.post("http://localhost:9002/api/register-apple",{reg_num:""},{headers:{"content-type":"application/x-www-form-urlencoded"}});e.data.success&&e.data.url&&G(e.data.url)}catch(e){console.error("Error fetching Apple register URL:",e)}})(),(async()=>{try{const e=await g.Z.post("http://localhost:9002/api/register-apple",{reg_num:"0"},{headers:{"content-type":"application/x-www-form-urlencoded"}});e.data.success&&e.data.url&&A(e.data.url)}catch(e){console.error("Error fetching Apple register URL:",e)}})(),()=>{e=!1}},[]),(0,x.jsx)(x.Fragment,{children:"aihub"===y?(0,x.jsxs)(x.Fragment,{children:[(0,x.jsxs)("div",{className:"flex flex-col items-center w-full gap-2",children:["on"===m&&(0,x.jsx)("div",{className:"inline-flex w-full",children:(0,x.jsxs)("button",{type:"button",className:"w-full py-3 border rounded-lg bg-[#EDEDED] text-black",disabled:!C,tabIndex:0,onClick:()=>{L&&Z()},onKeyDown:e=>{"Enter"===e.key&&L&&Z()},"aria-label":"apple register",children:[(0,x.jsx)("div",{className:"inline-flex align-middle mr-[5px]",children:(0,x.jsx)(n.Z,{src:k,alt:"AppleLogin",className:"w-[16px] max-h-[20px] mb-1",width:"20",height:"20"})}),(0,x.jsx)("div",{className:"inline-flex font-semibold",children:"Continue with Apple"})]})}),"on"===u&&(0,x.jsx)("div",{className:"inline-flex w-full",children:(0,x.jsxs)("button",{type:"button",className:"w-full py-3 border rounded-lg bg-[#EDEDED] text-black",disabled:!E,tabIndex:0,onClick:()=>E&&$(),onKeyDown:e=>{"Enter"===e.key&&E&&$()},"aria-label":"google register",children:[(0,x.jsx)("div",{className:"inline-flex align-middle mr-[5px]",children:(0,x.jsx)(n.Z,{src:N,alt:"GoogleLogin",className:"w-[20px] max-h-[20px] mb-1",width:"20",height:"20"})}),(0,x.jsx)("div",{className:"inline-flex font-semibold",children:"Continue with Google"})]})})]}),("02"===m||"02"===u)&&(0,x.jsxs)("div",{className:"mb-[10px] mt-[10px]",children:[(0,x.jsxs)("span",{className:"text-base text-white/70",children:["Do you have an account?"," "]}),(0,x.jsx)("button",{type:"button",className:"font-bold cursor-pointer",onClick:()=>t("login"),"aria-label":"apple login",children:(0,x.jsx)("div",{className:"inline-flex font-semibold text-white",children:"Login"})})]})]}):(0,x.jsxs)(x.Fragment,{children:[(0,x.jsxs)("div",{className:"flex flex-col items-center w-full gap-2",children:["on"===u&&(0,x.jsx)("div",{className:"inline-flex w-full",children:(0,x.jsxs)("button",{type:"button",className:"w-full py-3 border rounded-lg "+(f?"bg-white/10 text-white border-white/30 hover:bg-white/20 backdrop-blur-sm":"bg-white border-slate-300"),disabled:!E,onClick:()=>E&&$(),onKeyDown:e=>{"Enter"===e.key&&E&&$()},"aria-label":"google register",children:[(0,x.jsx)("div",{className:"inline-flex align-middle mr-[5px]",children:(0,x.jsx)(n.Z,{src:v,alt:"GoogleLogin",className:"w-[17px] max-h-[17px] mb-1",width:"20",height:"20"})}),(0,x.jsx)("div",{className:"inline-flex",children:j("echo.register.buttons.registerGoogleText")})]})}),b&&(0,x.jsx)("div",{className:"inline-flex w-full",children:(0,x.jsxs)("button",{type:"button",className:"w-full py-3 border rounded-lg "+(f?"bg-white/10 text-white border-white/30 hover:bg-white/20 backdrop-blur-sm":"bg-gray-300 border-slate-300"),disabled:!E,onClick:()=>E&&$(),onKeyDown:e=>{"Enter"===e.key&&E&&$()},"aria-label":"google register",children:[(0,x.jsx)("div",{className:"inline-flex align-middle mr-[5px]",children:(0,x.jsx)(n.Z,{src:N,alt:"GoogleLogin",className:"w-[20px] max-h-[20px] mb-1",width:"20",height:"20"})}),(0,x.jsx)("div",{className:"inline-flex font-semibold",children:"Continue with Google"})]})}),"on"===m&&(0,x.jsx)("div",{className:"inline-flex w-full",children:(0,x.jsxs)("button",{type:"button",className:"w-full py-3 border rounded-lg "+(f?"bg-white/10 text-white border-white/30 hover:bg-white/20 backdrop-blur-sm":"bg-white border-slate-300"),disabled:!C,onClick:()=>C&&S(),onKeyDown:e=>{"Enter"===e.key&&C&&S()},"aria-label":"apple register",children:[(0,x.jsx)("div",{className:"inline-flex align-middle mr-[5px]",children:(0,x.jsx)(n.Z,{src:k,alt:"AppleLogin",className:"w-[16px] max-h-[20px] mb-1",width:"20",height:"20"})}),(0,x.jsx)("div",{className:"inline-flex",children:j("echo.register.buttons.registerAppleText")})]})}),i&&(0,x.jsx)("div",{className:"inline-flex w-full",children:(0,x.jsxs)("button",{type:"button",className:"w-full py-3 border rounded-lg "+(f?"bg-white/10 text-white border-white/30 hover:bg-white/20 backdrop-blur-sm":"bg-gray-300 border-slate-300"),disabled:!C,onClick:()=>{L&&Z()},onKeyDown:e=>{"Enter"===e.key&&L&&Z()},"aria-label":"apple register",children:[(0,x.jsx)("div",{className:"inline-flex align-middle mr-[5px]",children:(0,x.jsx)(n.Z,{src:k,alt:"AppleLogin",className:"w-[16px] max-h-[20px] mb-1",width:"20",height:"20"})}),(0,x.jsx)("div",{className:"inline-flex font-semibold",children:"Continue with Apple"})]})})]}),("02"===m||"02"===u)&&(0,x.jsxs)("div",{className:"mb-[10px] mt-[10px]",children:[(0,x.jsxs)("span",{className:"text-base "+(f?"text-white/70":"text-gray-500"),children:["Do you have an account?"," "]}),(0,x.jsx)("button",{type:"button",className:"font-bold cursor-pointer",onClick:()=>t("login"),"aria-label":"apple login",children:(0,x.jsx)("div",{className:"inline-flex font-semibold "+(f?"text-white":""),children:"Login"})})]})]})})}},40642:(e,t,i)=>{e.exports=i.p+"static/media/apple_ico.af13ada0dd55a0d30879.webp"},74333:(e,t,i)=>{e.exports=i.p+"static/media/google_icon.9ca8f9eebce3363154d6.webp"},27070:(e,t,i)=>{e.exports=i.p+"static/media/google_icon_black.df46b77a70d5878d755a.webp"},56649:(e,t,i)=>{e.exports=i.p+"static/media/apple_ico.41bca9ceb26376cf5876.png"},3633:(e,t,i)=>{e.exports=i.p+"static/media/google_icon.aeb309a8678785836262.png"},5714:(e,t,i)=>{e.exports=i.p+"static/media/google_icon_black.34017bc9bbc03ec6f505.png"}}]);
//# sourceMappingURL=7138.c6e77b23.chunk.js.map