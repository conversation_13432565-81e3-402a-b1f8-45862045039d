const HtmlWebpackPlugin = require('html-webpack-plugin');
const TerserPlugin = require('terser-webpack-plugin');
const paths = require('react-scripts/config/paths');
const CompressionPlugin = require('compression-webpack-plugin');
const BrotliPlugin = require('brotli-webpack-plugin');
const CssMinimizerPlugin = require('css-minimizer-webpack-plugin');

module.exports = function override(config) {
  // Remove existing HtmlWebpackPlugin instance and add a new one
  config.plugins = config.plugins.filter(plugin => !(plugin instanceof HtmlWebpackPlugin));
  config.plugins.push(
    new HtmlWebpackPlugin({
      inject: true,
      template: paths.appHtml,
      scriptLoading: 'defer',
      minify: {
        removeComments: true,
        collapseWhitespace: true,
        removeRedundantAttributes: true,
        useShortDoctype: true,
        removeEmptyAttributes: true,
        removeStyleLinkTypeAttributes: true,
        keepClosingSlash: true,
        minifyJS: true,
        minifyCSS: true,
        minifyURLs: true,
        html5: true,
        removeOptionalTags: true,
        removeTagWhitespace: true,
        processScripts: ['text/html'],
      },
      meta: { 'http-equiv': { 'http-equiv': 'Cache-Control', content: 'public, max-age=604800' } }
    })
  );

  // Add Brotli and Gzip compression plugins
  config.plugins.push(
    new BrotliPlugin({
      asset: '[path].br[query]',
      test: /\.(jsx|js|css|html|svg)$/,
      threshold: 10240,
      minRatio: 0.8,
    }),
    new CompressionPlugin({
      filename: '[path][base].gz',
      algorithm: 'gzip',
      test: /\.(jsx|js|css|html|svg)$/,
      threshold: 10240,
      minRatio: 0.8,
      exclude: /\.(br)$/,
    })
  );

  // Update optimization settings
  config.optimization = {
    ...config.optimization,
    minimizer: [
      new TerserPlugin({
        terserOptions: { compress: { drop_console: false } },
      }),
      new CssMinimizerPlugin(),
    ],
    splitChunks: { chunks: 'all' },
  };

  return config;
};
