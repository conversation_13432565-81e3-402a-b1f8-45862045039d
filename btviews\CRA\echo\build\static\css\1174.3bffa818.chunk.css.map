{"version": 3, "file": "static/css/1174.3bffa818.chunk.css", "mappings": "sFACA,EAAG,iBAAmB,CAEtB,KACE,wBACF,CACA,KAEE,8BAAkC,CADlC,QAAS,CAET,kCAAmC,CACnC,iCAAkC,CAClC,eACF,CACA,iBAAiB,8CAAiD,CAAC,eAAe,CAAC,aAAa,WAAW,CAAC,cAAc,CAAC,4BAAyG,0BAA8B,CAA3G,YAAY,CAAqD,WAAW,CAAnC,MAAM,CAAlC,cAAc,CAAqB,KAAK,CAAC,UAAU,CAApC,YAAgF,CAA8R,kEAAoD,gBAAkB,CAAhD,2BAA6B,CAAoB,eAAiB,CAAwB,wBAA2B,CAAlD,oBAAmD,CAAC,qBAAsB,+BAAkC,CAAC,QAAQ,+BAAiC,CAAC,gEAAqE,CAAyH,sMAA4J,aAAc,CAAC,eAAgB,CAAC,sBAAuB,CAAC,kBAAmB,CAAE,uLAAuL,YAAa,CAAC,wDAAwD,sBAAwB,CAC78C,KACE,uEAEF,CAEA,gBACE,wBACF,CAEA,yBACE,WACE,iBAAkB,CAClB,kBACF,CACF,CAQA,+BACE,YACF,CACA,kBAKE,WAAY,CAFZ,MAAS,CAFT,cAAe,CACf,KAAQ,CAER,UAAW,CAEX,cACF,CACA,2BAEE,eAAiB,CAIjB,WAAY,CADZ,MAAS,CAFT,UAAY,CAFZ,iBAAkB,CAGlB,KAAQ,CAGR,UACF,CACA,QAIE,WAAY,CAEZ,qBAAsB,CAJtB,iBAAkB,CAGlB,oBAAqB,CAFrB,UAIF,CACA,oBAPE,oBAcF,CAPA,YAME,sDAA4D,CAD5D,eAAgB,CAFhB,QAAS,CADT,iBAAkB,CAElB,UAGF,CACA,wBAEE,qBAAuB,CADvB,QAEF,CACA,yBAEE,qBAAuB,CADvB,SAEF,CACA,yBAEE,iBAAkB,CADlB,SAEF,CACA,kBACE,GAEE,WAAY,CADZ,OAEF,CACA,OAEE,WAAY,CADZ,QAEF,CACF,CAOA,0BACE,0BACF,CAEA,eACI,uDAAgE,CAChE,4BAA6B,CAC7B,6BACJ,CAEA,uBAGE,uDAAgE,CAEhE,UAAW,CAJX,YAAa,CAGb,mBAAoB,CAFpB,kBAAoB,CAMpB,oBAAqB,CAFrB,oBAAqB,CACrB,8BAEF,CAEA,0DACI,kCAAoC,CACpC,UACJ,CAEA,WACI,gBAAiB,CACjB,gBACJ,CAEA,+BACI,cAAe,CACf,gBACJ,CACA,QACI,8CACJ,CAEA,KACI,kBAA4B,CAG5B,8FAAoJ,CACpJ,+GACJ,CAGA,gBACE,eACF,CAGA,mCACE,SACF,CAEA,yCACE,eACF,CAEA,yCACE,kBAAmB,CACnB,iBACF,CAGA,0CACE,YACF", "sources": ["index.css"], "sourcesContent": ["@import url('https://fonts.googleapis.com/css?family=Poppins:400,500,600&display=swap');\r\n* {font-display: swap;}\r\n\r\nhtml {\r\n  background-color: #F6F7F8;\r\n}\r\nbody {\r\n  margin: 0;\r\n  font-family: 'Poppins', sans-serif;\r\n  -webkit-font-smoothing: antialiased;\r\n  -moz-osx-font-smoothing: grayscale;\r\n  background: #FFFFFF;\r\n}\r\n#modal-container{font-family:'Alegreya Sans',sans-serif !important;z-index:9999999}button.close{float:right;font-size:25px}#modal-container-enterprise{display:none;position:fixed;z-index:9999;left:0;top:0;width:100%;height:100%;background-color:rgba(0,0,0,.5)}header.openchat-relative {position: relative !important;left: 0 !important;top: 0 !important;width: 100% !important;transform: unset !important;}header.openchat-p-2 {position: relative !important;left: 0 !important;top: 0 !important;width: 100% !important;transform: unset !important;}header.openchat-py-4 {position: relative !important;left: 0 !important;top: 0 !important;width: 100% !important;transform: unset !important;}.openchat-bg-accent2 {background-color: gray !important;}.appbox{--tw-border-opacity: 1 !important;border-color: rgb(229 231 235 / var(--tw-border-opacity)) !important;}.openchat-text-base.openchat-font-semibold {max-width: 75%;overflow: hidden;text-overflow: ellipsis;white-space:nowrap;}.fade-in.openchat-px-2.openchat-py-1.openchat-font-bold.openchat-text-center.openchat-text-lg.openchat-overflow-hidden.openchat-truncate.openchat-absolute {max-width: 75%;overflow: hidden;text-overflow: ellipsis;white-space:nowrap;} .openchat-w-full.openchat-bg-white.openchat-flex.openchat-flex-col.openchat-gap-2.openchat-p-2.openchat-text-base.openchat-leading-6.openchat-mt-5.openchat-rounded-lg.openchat-shadow{display:none;}.openchat-fixed.openchat-bottom-5.openchat-right-5.z-50{z-index:8889 !important;}\r\ncode {\r\n  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',\r\n    monospace;\r\n}\r\n\r\n.text-uppercase {\r\n  text-transform: uppercase;\r\n}\r\n\r\n@media (max-width: 500px) {\r\n  .container {\r\n    padding-left: 10px;\r\n    padding-right: 10px;\r\n  }\r\n}\r\n\r\n\r\n/*\r\n    ==========\r\n      LOADER\r\n    ==========\r\n*/\r\n.loader-container:not(.active) {\r\n  display: none;\r\n}\r\n.loader-container {\r\n  position: fixed;\r\n  top: 0px;\r\n  left: 0px;\r\n  width: 100%;\r\n  height: 100%;\r\n  z-index: 999999;\r\n}\r\n.loader-container .overlay {\r\n  position: absolute;\r\n  background: black;\r\n  opacity: 0.5;\r\n  top: 0px;\r\n  left: 0px;\r\n  height: 100%;\r\n  width: 100%;\r\n}\r\n.lds-ai {\r\n  display: inline-block;\r\n  position: relative;\r\n  width: 80px;\r\n  height: 80px;\r\n  top: calc(50% - 40px);\r\n  left: calc(50% - 40px);\r\n}\r\n.lds-ai div {\r\n  display: inline-block;\r\n  position: absolute;\r\n  left: 8px;\r\n  width: 16px;\r\n  background: #fff;\r\n  animation: lds-ai 1.2s cubic-bezier(0, 0.5, 0.5, 1) infinite;\r\n}\r\n.lds-ai div:nth-child(1) {\r\n  left: 8px;\r\n  animation-delay: -0.24s;\r\n}\r\n.lds-ai div:nth-child(2) {\r\n  left: 32px;\r\n  animation-delay: -0.12s;\r\n}\r\n.lds-ai div:nth-child(3) {\r\n  left: 56px;\r\n  animation-delay: 0;\r\n}\r\n@keyframes lds-ai {\r\n  0% {\r\n    top: 8px;\r\n    height: 64px;\r\n  }\r\n  50%, 100% {\r\n    top: 24px;\r\n    height: 32px;\r\n  }\r\n}\r\n\r\n/*\r\n    ==========\r\n      Toggle\r\n    ==========\r\n*/\r\ninput.toggle:checked ~ .dot {\r\n  transform: translateX(150%);\r\n}\r\n\r\n.text-gradient {\r\n    background: linear-gradient(90deg, #3f51b5, transparent) #3fcad7;\r\n    -webkit-background-clip: text;\r\n    -webkit-text-fill-color: transparent;\r\n}\r\n\r\n.gradient-hover-effect {\r\n  display: flex;\r\n  padding: 0.875em 2em;\r\n  background: linear-gradient(90deg, #3f51b5, transparent) #2196f3;\r\n  font-family: inherit;\r\n  color: #fff;\r\n  text-decoration: none;\r\n  transition: background-color 1s;\r\n  place-content: center;\r\n}\r\n\r\n.gradient-hover-effect:hover, .gradient-hover-effect:focus {\r\n    background-color: #F7941D !important;\r\n    color: #fff;\r\n}\r\n\r\ni.fas, i.fa {\r\n    font-size: 1.5rem;\r\n    margin: 0 0 0 7px;\r\n}\r\n\r\n.features i.fas, .features i.fa {\r\n    font-size: 1rem;\r\n    margin: 0 5px 0 0;\r\n}\r\n.ctabtn {\r\n    box-shadow: 0 11px 12px -10px #282828 !important;\r\n}\r\n\r\n.ask {\r\n    background: rgb(171,211,238);\r\n    background: -moz-radial-gradient(ellipse at 50% 150%, rgba(171,211,238,1) 0%, rgba(235,241,246,1) 50%, rgba(236,241,246,1) 62%, rgba(243,244,246,1) 100%);\r\n    background: -webkit-radial-gradient(ellipse at 50% 150%, rgba(171,211,238,1) 0%, rgba(235,241,246,1) 50%, rgba(236,241,246,1) 62%, rgba(243,244,246,1) 100%);\r\n    background: radial-gradient(ellipse at 50% 150%, rgba(171,211,238,1) 0%, rgba(235,241,246,1) 50%, rgba(236,241,246,1) 62%, rgba(243,244,246,1) 100%);\r\n    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#abd3ee', endColorstr='#f3f4f6',GradientType=1 );\r\n}\r\n\r\n/* Hide scrollbar track & buttons */\r\n.custom-sidebar {\r\n  overflow-y: auto; /* Enable vertical scrolling */\r\n}\r\n\r\n/* Customize scrollbar */\r\n.custom-sidebar::-webkit-scrollbar {\r\n  width: 8px; /* Scrollbar thickness */\r\n}\r\n\r\n.custom-sidebar::-webkit-scrollbar-track {\r\n  background: #fff; /* Hide track */\r\n}\r\n\r\n.custom-sidebar::-webkit-scrollbar-thumb {\r\n  background: #e5e7eb; /* Scrollbar bar color */\r\n  border-radius: 5px;\r\n}\r\n\r\n/* Hide up and down buttons */\r\n.custom-sidebar::-webkit-scrollbar-button {\r\n  display: none;\r\n}\r\n"], "names": [], "sourceRoot": ""}