<?php

namespace App\Support;

use App\Enums\Locales;
use App\Enums\PlanType;
use Exception;
use NumberFormatter;

class PricingDescription
{
    private string $directory = __DIR__ . '/PricingDescription';

    private ?string $currency_symbol = null;

    private float $price = 0;

    private ?int $trial_days = null;

    private Locales $locale = Locales::EN;

    public function __construct(private PlanType $plan_type, private bool $is_trial = false) {}

    public static function make(PlanType $plan_type, bool $is_trial = false): self
    {
        return new self($plan_type, $is_trial);
    }

    public function setCurrencySymbol(string $currency_symbol)
    {
        $this->currency_symbol = $currency_symbol;

        return $this;
    }

    public function setPrice(float $price)
    {
        $this->price = $price;

        return $this;
    }

    public function setTrialDays(int $trial_days)
    {
        $this->trial_days = $trial_days;

        return $this;
    }

    public function setLocale(string|Locales $locale)
    {
        $locale = strtoupper($locale);
        $this->locale = !$locale instanceof Locales ? Locales::from($locale) : $locale;

        return $this;
    }

    public function get(): ?string
    {
        $plan_type = $this->plan_type;
        $locale = $this->locale;

        $translation_path = "$this->directory/Locales/" . strtolower($locale->value) . '.json';
        $template_path = "$this->directory/Views/Default.php";

        if (!file_exists($translation_path)) {
            throw new Exception('Translation file not found.');
        }

        if (!file_exists($template_path)) {
            throw new Exception('Template file not found.');
        }

        $data = json_decode(file_get_contents($translation_path), true);

        $models = match ($plan_type) {
            PlanType::Basic => $this->getBasicPlanChatbotModels(),
            PlanType::Pro => $this->getProPlanChatbotModels(),
            PlanType::ProMax => $this->getProMaxPlanChatbotModels(),
            PlanType::Enterprise => $this->getEnterprisePlanChatbotModels(),
            default => [],
        };

        $pricing_description = [
            'access_to_chatbots' => $models,
            'image_generation' => $this->getImageModels(),
            'video_generation' => $this->getVideoModels(),
            'features' => $this->getFeatures(),
        ];

        if ($plan_type === PlanType::Basic) {
            unset($pricing_description['video_generation']);
        }

        return $this->renderOutput($template_path, [
            'pricing_description' => $this->updatePricingDescriptionValues($pricing_description, $data),
        ]);
    }

    public function getWithFreeTrialPlan(): ?string
    {
        $template_path = "$this->directory/Views/WithFreeTrial.php";

        if (!file_exists($template_path)) {
            throw new Exception('Template file not found.');
        }

        $plan_type = $this->plan_type->value;

        if ($plan_type === 'ProMax') {
            $plan_type = 'Pro Max';
        }

        $price_text = $this->formatCurrency($this->price, $this->currency_symbol);

        $trial_template = $this->renderOutput($template_path, [
            'trial_days' => $this->trial_days,
            'plan_type' => $plan_type,
            'price_text' => $price_text,
        ]);

        return $trial_template . $this->get();
    }

    public function getWithClusterPlan(string $plan_name): ?string
    {
        $template_path = "$this->directory/Views/WithClusterPlan.php";

        if (!file_exists($template_path)) {
            throw new Exception('Template file not found.');
        }

        $token_limit = null;
        $prompts = match ($plan_name) {
            'Team Max' => 60,
            'Office Max' => 100,
            'Enterprise Max' => 120,
            default => null,
        };

        $models_with_prompts = [];

        switch ($this->plan_type) {
            case PlanType::Pro:
                $unlimited_access_to_models = false;
                $token_limit = '500,000 tokens per month';

                $models = [
                    'GPT-4o mini, GPT-4o',
                    'LLaMA3-8B',
                    'Gemini Pro',
                ];
                break;
            default:
                $unlimited_access_to_models = true;

                $models = [
                    'GPT-4, GPT-4o, GPT-4o mini',
                    'Claude 3.7 Sonnet',
                    'Llama 3.1',
                    'Gemini Pro',
                    'DALL-E',
                    'more_models' => [
                        'Gemma-2',
                        'Mistral',
                        'DeepSeek',
                        'DBRX',
                        'Qwen',
                        'Mixtral',
                    ],
                ];

                $models_with_prompts = [
                    'Flux.1 Pro',
                    'OpenAI o1-preview',
                    'OpenAI o1-mini',
                ];
                break;
        }

        $unlimited_access_to_ai_apps = $unlimited_access_to_models;

        $apps = [
            'Language apps' => [
                'ChatPDF',
                'Grammar AI',
                'Teacher AI',
                'Search AI',
                'Multi-Chat',
                'SiteBot',
                'Coding AI',
                'Homework Help',
            ],
            'Image apps' => [
                'DreamPhoto',
                'Interior AI',
                'Restore Photo',
                'Remove Background',
                'Avatar Maker',
                'Storybook',
            ],
        ];

        return $this->renderOutput($template_path, [
            'unlimited_access_to_models' => $unlimited_access_to_models,
            'token_limit' => $token_limit,
            'models' => $models,
            'prompts' => $prompts,
            'models_with_prompts' => $models_with_prompts,
            'unlimited_access_to_ai_apps' => $unlimited_access_to_ai_apps,
            'apps' => $apps,
        ]);
    }

    public function getWithAdvancedPlan(): ?string
    {
        $plan_type = $this->plan_type;

        $translation_path = "$this->directory/Locales/en.json";
        $template_path = "$this->directory/Views/WithAdvancedPlan.php";

        if (!file_exists($translation_path)) {
            throw new Exception('Translation file not found.');
        }

        if (!file_exists($template_path)) {
            throw new Exception('Template file not found.');
        }

        $data = json_decode(file_get_contents($translation_path), true);

        switch ($plan_type) {
            case PlanType::Pro:
                $token_limit = '1,000,000 tokens per month';
                $sentences = '(approximately 40,000 sentences)';
                $models = $this->getProPlanChatbotModels();
                break;
            case PlanType::Advanced:
                $token_limit = '2,500,000 tokens per month';
                $sentences = '(approximately 100,000 sentences)';
                $models = $this->getAdvancedPlanChatbotModels();
                break;
            case PlanType::ProMax:
                $token_limit = 'No token limit';
                $sentences = '(unlimited chatbot responses)';
                $models = $this->getProMaxPlanChatbotModels();
                break;
        }

        $pricing_description = [
            'access_to_chatbots' => $models,
            'image_generation' => $this->getImageModels(),
            'video_generation' => $this->getVideoModels(),
        ];

        return $this->renderOutput($template_path, [
            'token_limit' => $token_limit,
            'sentences' => $sentences,
            'pricing_description' => $this->updatePricingDescriptionValues($pricing_description, $data),
        ]);
    }

    private function getBasicPlanChatbotModels(): array
    {
        return [
            'GPT-4o',
            'GPT-5 mini' => [
                'new_badge' => true,
            ],
            'DeepSeek',
            [
                'placeholder' => [
                    'format' => 'tokens_per_month_in_model',
                    'values' => [
                        'model' => 'Claude 3.5',
                        'tokens_per_month' => '25k',
                    ]
                ],
                'tooltip' => [
                    'placeholder' => [
                        'format' => 'sentences_tooltip',
                        'values' => [
                            'sentences' => $this->formatNumber(1000),
                        ]
                    ],
                ],
            ],
            'LLaMA3-8B',
        ];
    }

    private function getProPlanChatbotModels(): array
    {
        return [
            'GPT-4o',
            'GPT-5' => [
                'new_badge' => true,
            ],
            'DeepSeek',
            [
                'placeholder' => [
                    'format' => 'tokens_per_month_in_model',
                    'values' => [
                        'model' => 'Claude 4',
                        'tokens_per_month' => '50k',
                    ]
                ],
                'tooltip' => [
                    'placeholder' => [
                        'format' => 'sentences_tooltip',
                        'values' => [
                            'sentences' => $this->formatNumber(2000),
                        ]
                    ],
                ],
            ],
            'Grok 4' => [
                'new_badge' => true,
            ],
            'LLaMA3-70B',
            'Gemini 2.5' => [
                'new_badge' => true,
            ],
        ];
    }

    private function getProMaxPlanChatbotModels(): array
    {
        return [
            'GPT-4o',
            'GPT-5' => [
                'new_badge' => true,
            ],
            'DeepSeek',
            'Claude 4: Unlimited',
            'OpenAI o1 & o3 mini',
            'Grok 4' => [
                'new_badge' => true,
            ],
            'LLaMA3-70B',
            'Gemini 2.5' => [
                'new_badge' => true,
            ],
            '(10+ other LLM Models)',
        ];
    }

    private function getEnterprisePlanChatbotModels(): array
    {
        return [
            'GPT-4o',
            'GPT-5' => [
                'new_badge' => true,
            ],
            'DeepSeek',
            'Grok 4' => [
                'new_badge' => true,
            ],
            'LLaMA3-70B',
            'Gemini 2.5' => [
                'new_badge' => true,
            ],
        ];
    }

    private function getAdvancedPlanChatbotModels(): array
    {
        return [
            'GPT-4o',
            'GPT-5' => [
                'new_badge' => true,
            ],
            'DeepSeek',
            [
                'placeholder' => [
                    'format' => 'tokens_per_month_in_model',
                    'values' => [
                        'model' => 'Claude 4',
                        'tokens_per_month' => '50k',
                    ]
                ],
                'tooltip' => [
                    'placeholder' => [
                        'format' => 'sentences_tooltip',
                        'values' => [
                            'sentences' => $this->formatNumber(2000),
                        ]
                    ],
                ],
            ],
            'Grok 4' => [
                'new_badge' => true,
            ],
            'LLaMA3-70B',
            'Gemini 2.5' => [
                'new_badge' => true,
            ],
        ];
    }

    private function getImageModels(): array
    {
        $plan_type = $this->plan_type;

        $dalle_limit = match ($plan_type) {
            PlanType::Basic => 20,
            PlanType::Pro => 50,
            PlanType::Advanced => 80,
            default => null,
        };

        $flux_limit = match ($plan_type) {
            PlanType::Basic => 15,
            PlanType::Pro => 30,
            PlanType::Advanced => 80,
            PlanType::ProMax => 160,
            PlanType::Enterprise => 30,
            default => null,
        };

        $dalle_model = 'DALL-E 3';
        $models = [];

        if ($plan_type !== PlanType::ProMax) {
            if (!is_null($dalle_limit)) {
                $models[] = [
                    'placeholder' => [
                        'format' => 'images_per_month_in_model',
                        'values' => [
                            'model' => $dalle_model,
                            'images_per_month' => $dalle_limit,
                        ]
                    ],
                ];
            }
        } else {
            $models[$dalle_model] = "$dalle_model: Unlimited";
        }

        if (!is_null($flux_limit)) {
            $models[] =  [
                'placeholder' => [
                    'format' => 'images_per_month_in_model',
                    'values' => [
                        'model' => 'Flux',
                        'images_per_month' => $flux_limit,
                    ]
                ],
            ];
        }

        return $models;
    }

    private function getVideoModels(): array
    {
        $klingai_limit = match ($this->plan_type) {
            PlanType::Pro => 3,
            PlanType::Advanced => 7,
            PlanType::ProMax => 15,
            PlanType::Enterprise => 3,
            default => null,
        };

        $models = [];

        if (!is_null($klingai_limit)) {
            $models[] =  [
                'placeholder' => [
                    'format' => 'videos_per_month_in_model',
                    'values' => [
                        'model' => 'KlingAI',
                        'videos_per_month' => $klingai_limit,
                    ]
                ],
            ];
        }

        return $models;
    }

    private function getFeatures(): array
    {
        $locale = $this->locale;
        $plan_type = $this->plan_type;

        $dialogue_limit = match ($plan_type) {
            PlanType::Basic => '500k',
            PlanType::Pro, PlanType::Enterprise => '1M',
            default => null,
        };

        if ($dialogue_limit === '1M') {
            $dialogue_limit = match ($locale) {
                Locales::DE => '1 Mio.',
                Locales::IT, Locales::PL => '1 mln',
                default => $dialogue_limit
            };
        }

        if (!is_null($dialogue_limit)) {
            if (is_int($dialogue_limit)) {
                $dialogue_limit = number_format($dialogue_limit);
            }
        }

        $features = [];

        if ($plan_type !== PlanType::ProMax) {
            $features[] = [
                'placeholder' => [
                    'format' => 'dialogue_limit',
                    'values' => [
                        'tokens_per_month' => $dialogue_limit,
                    ]
                ],
            ];
        } else {
            $features[] = 'Unlimited Dialogue';
        }

        if ($plan_type !== PlanType::Basic) {
            $features[] = [
                'placeholder' => 'full_access',
                'tooltip' => 'full_access_tooltip',
            ];
        }

        if ($this->is_trial) {
            if ($locale !== Locales::EN) {
                $price_text = $this->formatCurrency($this->price, $this->currency_symbol);

                $features[] = [
                    'placeholder' => [
                        'format' => 'trial_text',
                        'values' => [
                            'trial_days' => $this->trial_days,
                            'price_text' => $price_text,
                        ]
                    ],
                ];
            }
        }

        return $features;
    }

    public function updatePricingDescriptionValues(array $pricing_description, array $data): array
    {
        foreach ($pricing_description as $section => $section_values) {
            foreach ($section_values as $key => $value) {
                if (is_array($value)) {
                    foreach ($value as $option => $current_value) {
                        switch ($option) {
                            case 'new_badge':
                                if ($current_value === true) {
                                    $section_values[$key][$option] = $data[$option];
                                    $section_values[$key]['text'] = $key;
                                }
                                break;
                            case 'placeholder':
                                $text = null;

                                if (isset($current_value['format']) && isset($current_value['values'])) {
                                    $current_format = $current_value['format'];

                                    if (isset($data[$current_format])) {
                                        $current_placeholder = $data[$current_format];
                                        $text = $this->replacePlaceholder($current_placeholder, $current_value['values']);
                                    }
                                } else {
                                    if (isset($data[$current_value])) {
                                        $text = $data[$current_value];
                                    }
                                }

                                if (!is_null($text)) {
                                    $section_values[$key]['text'] = $text;

                                    unset($section_values[$key]['placeholder']);
                                }
                                break;
                            case 'tooltip':
                                $tooltip_text = null;

                                if (isset($current_value['placeholder'])) {
                                    $tooltip_placeholder = $current_value['placeholder'];

                                    if (isset($tooltip_placeholder['format']) && isset($tooltip_placeholder['values'])) {
                                        $tooltip_format = $tooltip_placeholder['format'];

                                        if (isset($data[$tooltip_format])) {
                                            $current_tooltip_placeholder = $data[$tooltip_format];
                                            $tooltip_text = $this->replacePlaceholder($current_tooltip_placeholder, $tooltip_placeholder['values']);
                                        }
                                    }
                                } else {
                                    if (isset($data[$current_value])) {
                                        $tooltip_text = $data[$current_value];
                                    }
                                }

                                if (!is_null($tooltip_text)) {
                                    $section_values[$key]['tooltip_text'] = $tooltip_text;

                                    unset($section_values[$key]['tooltip']);
                                }
                                break;
                        }
                    }
                }
            }

            if (isset($data[$section])) {
                $translated_section = $data[$section];
                $pricing_description[$translated_section] = $section_values;

                unset($pricing_description[$section]);
            }
        }

        return $pricing_description;
    }

    private function replacePlaceholder(string $placeholder, string|array $replacement): string
    {
        $result = preg_replace_callback('/\{(\w+)\}/', function ($matches) use ($replacement) {
            if (is_array($replacement)) {
                $key = $matches[1];

                return $replacement[$key] ?? $matches[0];
            }

            return $replacement;
        }, $placeholder);

        return $result;
    }

    private function formatNumber(int|float $number): string
    {
        $formatter = new NumberFormatter($this->locale->value, NumberFormatter::DECIMAL);

        return $formatter->format($number);
    }

    private function formatCurrency(int|float $price, string $currency_symbol, ?Locales $locale = null): string
    {
        $amount = $this->formatNumber($price);

        $currency_symbols = [
            'ر.س.',
            'AED',
            'CHF',
            'SEK',
            'zł',
            'LEI',
            'Ft',
            'kr.',
            'лв',
            '₺',
        ];

        switch ($locale) {
            case Locales::DE:
            case Locales::FR:
            case Locales::TR:
                return "$amount $currency_symbol";
        }

        if (in_array($currency_symbol, $currency_symbols)) {
            return $amount . $currency_symbol;
        }

        return $currency_symbol . $amount;
    }

    private function renderOutput($template_path, $data)
    {
        extract($data);
        ob_start();

        include $template_path;

        $output = ob_get_clean();
        $output = preg_replace('/>\s+</', '><', $output);
        $output = preg_replace('/\s+/', ' ', $output);
        $output = trim($output);

        return $output;
    }
}
