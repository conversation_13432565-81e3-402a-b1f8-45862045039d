"use strict";(self.webpackChunkv1=self.webpackChunkv1||[]).push([[9915,7719,9044],{34492:(e,t,s)=>{s.d(t,{aS:()=>o,mD:()=>h,mW:()=>p,o0:()=>x,oB:()=>b,p6:()=>i,rZ:()=>n,tN:()=>u,x6:()=>d,yt:()=>m});var r=s(74335),a=s(80184);function l(e){return e?"usd"===e.toLowerCase()?"$":"eur"===e.toLowerCase()?"€":"gbp"===e.toLowerCase()?"£":"brl"===e.toLowerCase()||"sar"===e.toLowerCase()?"R$":"":""}function n(e){return e?"usd"===e.toLowerCase()||"eur"===e.toLowerCase()?"US":"gbp"===e.toLowerCase()?"GB":"brl"===e.toLowerCase()?"US":"sar"===e.toLowerCase()?"AE":"chf"===e.toLowerCase()?"CH":"sek"===e.toLowerCase()?"SE":"US":""}function i(e){if(!e)return"";const t=new Date(e.replace(/ /g,"T"));return["January","February","March","April","May","June","July","August","September","October","November","December"][t.getMonth()]+" "+t.getDate()+" "+t.getFullYear()}function o(e){return e.trial_price?e.trial_price:e.price?e.price:"0"}function d(e){return e.toString().replace(/\B(?=(\d{3})+(?!\d))/g,",")}function c(e){const t=parseFloat(e);return d(t%1==0?t.toFixed(0):t.toFixed(2))}function m(e,t){return e&&t?isNaN(t)?"":parseFloat(t)>=0?"sar"===e.toLowerCase()?c(t).toLocaleString("en-US")+"ر.س.":"aed"===e.toLowerCase()?c(t).toLocaleString("en-US")+"AED":"chf"===e.toLowerCase()?c(t).toLocaleString("en-US")+"CHF":"sek"===e.toLowerCase()?c(t).toLocaleString("en-US")+"SEK":"pln"===e.toLowerCase()?c(t).toLocaleString("en-US")+"zł":"ron"===e.toLowerCase()?c(t).toLocaleString("en-US")+"LEI":"czk"===e.toLowerCase()?"Kč"+c(t).toLocaleString("en-US"):"huf"===e.toLowerCase()?c(t).toLocaleString("en-US")+"Ft":"dkk"===e.toLowerCase()?c(t).toLocaleString("en-US")+"kr.":"bgn"===e.toLowerCase()?c(t).toLocaleString("en-US")+"лв":"try"===e.toLowerCase()?c(t).toLocaleString("en-US")+"₺":l(e)+c(t).toLocaleString("en-US"):"-"+l(e)+(-1*c(t)).toLocaleString("en-US"):""}function u(e,t){e=new Date(e);var s=((t=new Date(t)).getTime()-e.getTime())/1e3;return s/=60,Math.abs(Math.round(s))}function x(e){if(!e)return"";const t=new Date(e.replace(/ /g,"T"));return["January","February","March","April","May","June","July","August","September","October","November","December"][t.getMonth()]+" "+t.getDate()+" "+t.getFullYear()+" "+t.getHours()+":"+t.getMinutes()}function p(e){let{plan:t}=e,s="",l="";return"Yearly"===t.payment_interval&&(s=m(t.currency,parseFloat(t.price/365).toFixed(2)),l=(0,a.jsxs)("div",{class:"text-xs mb-4",children:["billed ",m(t.currency,t.price),"/year"]})),"Monthly"===t.payment_interval&&(s=m(t.currency,parseFloat(t.price/30).toFixed(2)),l=(0,a.jsxs)("div",{class:"text-xs mb-4",children:["billed ",m(t.currency,t.price),"/Month"]})),t.trial_price&&(s=m(t.currency,parseFloat(t.trial_price/t.trial_days).toFixed(2)),l=(0,a.jsxs)("div",{class:"text-xs mb-4",children:["billed ",m(t.currency,t.trial_price)]})),(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("p",{className:"text-4xl font-bold text-gray-800 "+(""===(0,r.bG)("p_toggle")?"mb-4":""),children:[s," ",(0,a.jsx)("span",{className:"text-sm",children:" per Day"})]}),l]})}function h(e){let{plan:t}=e;return"on"===(0,r.bG)("daily")?p({plan:t}):t.trial_price?(0,a.jsx)("p",{className:"text-4xl font-bold text-gray-800 mb-4",children:m(t.currency,t.trial_price)}):(0,a.jsxs)("p",{className:"text-4xl font-bold text-gray-800 mb-4",children:[m(t.currency,t.price),(0,a.jsxs)("span",{className:"text-sm",children:["/","Monthly"===t.payment_interval?"month":"year"]})]})}function b(){const e=window.location.href;return e.indexOf("staging")>-1?"staging.":e.indexOf("dev")>-1?"dev.":""}},77719:(e,t,s)=>{s.r(t),s.d(t,{default:()=>l});var r=s(72791),a=s(80184);const l=function(){const e="http://localhost:9002/";return(0,r.useEffect)(()=>{const t=document.createElement("script");t.src=e+"snippets/com.global.vuzo/js/com.global.vuzo.js",t.async=!0,t.onload=()=>{},document.body.appendChild(t)},[e]),(0,a.jsx)(a.Fragment,{children:(0,a.jsx)("footer",{className:"relative hidden md:block md:absolute bottom-[10px] right-[5px] w-full mt-3 py-12 md:py-2 z-[8888] md:hidden"})})}},80953:(e,t,s)=>{s.r(t),s.d(t,{default:()=>Y});var r=s(72791),a=s(96347),l=s(10728),n=s(77719),i=s(28891),o=s(74335),d=s(78820),c=s(56355),m=s(31243),u=s(34492),x=s(82801),p=s(2777),h=s(84947),b=s(54270),g=s(85187),y=s(95828),f=s.n(y),v=(s(92831),s(29540)),j=s(80184);function w(e){let{showPausedAccountModal:t,setShowPausedAccountModal:s,userSubscriptionID:a,userMerchant:l,userAccountID:n}=e;const[i,d]=(0,r.useState)(1),[c,u]=(0,r.useState)(!1);(0,r.useEffect)(()=>{f().options={positionClass:"toast-top-center"}},[]),(0,r.useEffect)(()=>{u(i>=5)},[i]);const h=()=>{s(!1)};return void 0!==t&&!0===t&&(()=>{let e=document.getElementById("modalPausedAccount");null!==e&&(e.style.display="block",s(!0))})(),void 0!==t&&!1===t&&h(),(0,j.jsx)(j.Fragment,{children:(0,j.jsx)(x.u,{appear:!0,show:t,as:r.Fragment,children:(0,j.jsxs)(p.V,{as:"div",className:"relative z-10",onClose:()=>h(),children:[(0,j.jsx)(x.u.Child,{as:r.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:(0,j.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-25"})}),(0,j.jsx)("div",{className:"fixed inset-0 overflow-y-auto",children:(0,j.jsx)("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:(0,j.jsx)(x.u.Child,{as:r.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:(0,j.jsxs)(p.V.Panel,{className:"w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all",children:[(0,j.jsx)(p.V.Title,{as:"h3",className:"text-lg font-medium leading-6 text-gray-900",children:"Pause Subscription"}),(0,j.jsxs)("div",{className:"mt-2",children:[(0,j.jsx)("div",{className:"text-sm text-gray-500",children:"You're about to pause your subscription. Please select how many billing cycles you'd like it to be paused."}),(0,j.jsxs)("div",{className:"text-center",children:[(0,j.jsx)("input",{className:"bg-gray-400 rounded px-3 py-2 m-2 text-white cursor-pointer",type:"button",value:"-",onClick:()=>{i>1&&d(i-1)}}),(0,j.jsx)("span",{className:"text-black p-2 mx-auto font-bold",children:i}),(0,j.jsx)("input",{className:"bg-gray-400 rounded px-3 py-2 m-2 text-white cursor-pointer",type:"button",value:"+",onClick:()=>{d(i+1)},disabled:c})]})]}),(0,j.jsxs)("div",{className:"mt-4 text-right",children:[(0,j.jsx)("button",{type:"button",className:"inline-flex justify-center rounded-md border border-transparent bg-red-500 px-4 py-2 mr-2 text-sm font-medium text-white hover:bg-[#2563eb]",onClick:()=>{h(),document.querySelector(".loader-container").classList.add("active"),m.Z.post("http://localhost:9002/api/pause-subscription",{tk:(0,o.bG)("access"),subscription_id:a,merchant:l,billing_cycle:i,account_pid:n},{headers:{"content-type":"application/x-www-form-urlencoded"}}).then(function(e){let t=e.data;t.success?(document.querySelector(".loader-container").classList.remove("active"),f().success("Success!<br> Your subscription is now paused."),setTimeout(function(){window.location.reload()},1e3)):(document.querySelector(".loader-container").classList.remove("active"),f().error(t.data.msg))})},children:"Confirm"}),(0,j.jsx)("button",{type:"button",className:"inline-flex justify-center rounded-md border border-transparent bg-gray-100 px-4 py-2 text-sm font-medium text-black hover:bg-gray-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-gray-500 focus-visible:ring-offset-2",onClick:()=>h(),children:"Close"})]})]})})})})]})})})}var N=s(59896),k=[],S=0;const A=e=>{let{tabName:t,activeTab:s,onClick:r,children:a}=e;const l=s===t;return(0,j.jsx)("li",{className:" pb-2 cursor-pointer py-3 px-6 rounded-t-lg "+(l?"border-b-2 border-white bg-white":""),onClick:()=>r(t),children:a})},C=e=>{const[t,s]=(0,r.useState)(""),[a]=(0,r.useState)(e.auth.email),[l,n]=(0,r.useState)(""),[i,d]=(0,r.useState)(""),[u,x]=(0,r.useState)(""),[p]=(0,r.useState)(e.auth.user_pid),[h,b]=(0,r.useState)(!1);(0,r.useEffect)(()=>{void 0!==e.auth&&(console.log("props.auth.is_social",e.auth.is_social),null!==e.auth.is_social&&""!==e.auth.is_social?b(!0):b(!1))},[e.auth]);return(0,j.jsxs)("div",{className:"w-96",children:[(0,j.jsx)("p",{className:"text-sm py-4",children:"You may edit your account details below:"}),(0,j.jsxs)("div",{className:"flex items-center",children:[(0,j.jsxs)("label",{className:"text-sm mr-2 font-bold font-s",children:["Your Account ID: ",p]}),(0,j.jsx)("button",{class:"bg-white hover:bg-gray-100 text-gray-800 font-semibold py-2 ml-1",onClick:()=>{navigator.clipboard.writeText(p),f().success("Successfully copied")},children:(0,j.jsx)(c.Dup,{})})]}),(0,j.jsxs)("div",{className:"relative block mt-3",children:[(0,j.jsxs)("div",{className:"border-solid border-2 border-gray-100 rounded-sm",children:[(0,j.jsx)("div",{className:"p-3 bg-gray-50 border-b-2 border-gray-100 sm:text-sm",children:"Change Email Address"}),(0,j.jsxs)("div",{className:"p-2 pt-1 pb-1",children:[(0,j.jsx)("input",{className:"placeholder:italic placeholder-text-slate-400 block bg-gray-100 w-full border border-slate-300 rounded-md my-3 py-2 pl-3 pr-3 shadow-sm focus:outline-none focus:border-sky-500 focus:ring-sky-500 focus:ring-1 sm:text-sm text-gray-400",placeholder:"<EMAIL>",type:"email",name:"email",readOnly:!0,value:e.auth.email}),(0,j.jsx)("input",{className:"placeholder:italic placeholder-text-slate-400 block w-full border border-slate-300 rounded-md my-3 py-2 pl-3 pr-3 shadow-sm focus:outline-none focus:border-sky-500 focus:ring-sky-500 focus:ring-1 sm:text-sm",placeholder:"New Email",type:"email",name:"new_email",autocomplete:"new-email",onKeyUp:e=>{s(e.target.value)},onChange:e=>s(e.target.value),disabled:h})]})]}),(0,j.jsxs)("div",{className:"border-solid border-2 border-gray-100 rounded-sm mt-2",children:[(0,j.jsx)("div",{className:"p-3 bg-gray-50 border-b-2 border-gray-100 sm:text-sm",children:"Change Password"}),(0,j.jsxs)("div",{className:"p-2 pt-1 pb-1",children:[(0,j.jsx)("input",{className:"placeholder:italic placeholder:text-slate-400 block bg-white w-full border border-slate-300 rounded-md my-3 py-2 pl-3 pr-3 shadow-sm focus:outline-none focus:border-sky-500 focus:ring-sky-500 focus:ring-1 sm:text-sm",placeholder:"New Password",type:"password",name:"new_password",autocomplete:"new-password",onKeyUp:e=>{d(e.target.value)},disabled:h}),(0,j.jsx)("input",{className:"placeholder:italic placeholder:text-slate-400 block bg-white w-full border border-slate-300 rounded-md my-3 py-2 pl-3 pr-3 shadow-sm focus:outline-none focus:border-sky-500 focus:ring-sky-500 focus:ring-1 sm:text-sm",placeholder:"Confirm New Password",type:"password",name:"conf_password",onKeyUp:e=>{x(e.target.value)},disabled:h})]})]})]}),(0,j.jsx)(M,{updateUserDetails:()=>{let e=(()=>{let e="";return l?i.length&&(i!==u?e="New Password do not match.":i.length<6&&(e="New Password should be at least 6 characters.")):e="Current Password is required.",e})();e?f().error(e):(e=(()=>{let e="";return t.length&&(/\S+@\S+\.\S+/.test(t)||(e="Invalid email format")),e})(),e?f().error(e):(document.querySelector(".loader-container").classList.add("active"),m.Z.post("http://localhost:9002/api/update-account",{tk:(0,o.bG)("access"),newemail:t,password:l,newpassword:i,confpassword:u},{headers:{"content-type":"application/x-www-form-urlencoded"}}).then(function(e){let t=e.data;if(t.success)return f().success("Success!<br> Please re-login your account."),void setTimeout(function(){window.location.reload()},1e3);document.querySelector(".loader-container").classList.remove("active"),t.data&&f().error(t.data.msg)})))},setOldPassword:n}),(0,j.jsx)("div",{className:"text-sm text-slate-400 py-4 mt-2 ml-2",children:(0,j.jsx)(P,{email:a})})]})},M=e=>{const t=e.setOldPassword,[s,l]=(0,r.useState)(!1),n=()=>{l(!1)},i=()=>{e.updateUserDetails()};return(0,j.jsxs)(j.Fragment,{children:[(0,j.jsx)(a.E.button,{className:"bg-blue-500 mb-1 text-white font-bold py-3 px-6 my-3 rounded-lg change-password hover:bg-[#2563eb]",whileTap:{scale:.9},onClick:()=>{l(!0)},children:"Save Changes"}),(0,j.jsx)(x.u,{appear:!0,show:s,as:r.Fragment,children:(0,j.jsxs)(p.V,{as:"div",className:"relative z-10",onClose:n,children:[(0,j.jsx)(x.u.Child,{as:r.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:(0,j.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-25"})}),(0,j.jsx)("div",{className:"fixed inset-0 overflow-y-auto",children:(0,j.jsx)("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:(0,j.jsx)(x.u.Child,{as:r.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:(0,j.jsxs)(p.V.Panel,{className:"w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all",children:[(0,j.jsx)(p.V.Title,{as:"h3",className:"text-lg font-medium leading-6 text-gray-900",children:"Update Account"}),(0,j.jsxs)("div",{className:"mt-2",children:[(0,j.jsx)("div",{className:"text-sm text-gray-500",children:"Please enter your password to proceed."}),(0,j.jsx)("div",{children:(0,j.jsx)("input",{className:"placeholder:italic placeholder:text-slate-400 block bg-white w-full border border-slate-300 rounded-md my-3 py-2 pl-3 pr-3 shadow-sm focus:outline-none focus:border-sky-500 focus:ring-sky-500 focus:ring-1 sm:text-sm",placeholder:"Current Password *",type:"password",name:"current_password",onKeyUp:e=>{t(e.target.value),13===e.keyCode&&i()}})})]}),(0,j.jsxs)("div",{className:"mt-4",children:[(0,j.jsx)("button",{type:"button",className:"inline-flex justify-center rounded-md border border-transparent bg-blue-500 px-4 py-2 mr-2 text-sm font-medium text-white hover:bg-[#2563eb]",onClick:i,children:"Proceed"}),(0,j.jsx)("button",{type:"button",className:"inline-flex justify-center rounded-md border border-transparent bg-gray-100 px-4 py-2 text-sm font-medium text-black hover:bg-gray-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-gray-500 focus-visible:ring-offset-2",onClick:n,children:"Close"})]})]})})})})]})})]})};async function L(){const e=(await m.Z.post("http://localhost:9002/api/get-enterprise-members",{tk:(0,o.bG)("access")},{headers:{"content-type":"application/x-www-form-urlencoded"}})).data;return e.success?e.data:[]}const T=e=>{let{setshowAddMoreMember:t,setShowPausedAccountModal:s,setUserSubscriptionID:a,setUserMerchant:l,setUserAccountID:n,date_now:i,user_subscription:d,userPpg:m}=e;const p=(0,o.bG)("access");if(void 0===d)return;const b=e=>{let t=e.target.getAttribute("pid");t&&(document.querySelector(".loader-container").classList.add("active"),window.location.href="/change-card?pid="+t)},g=()=>{window.location.href="/downgrade"},y=e=>e.merchant?e.merchant.toLowerCase():"",f=e=>e.currency?e.currency.toLowerCase():"",v=e=>e.plan_type?e.plan_type.toLowerCase():"",w=e=>e.payment_interval?e.payment_interval.toLowerCase():"";return(0,j.jsx)(j.Fragment,{children:(0,j.jsx)("div",{className:"overflow-x-auto overflow-y-visible container-full-width cm-scrollbar",children:d&&d.length?(0,j.jsxs)("table",{className:"subs-table min-w-full divide-y bg-gray-50 divide-gray-200 min-h-[50px]",children:[(0,j.jsx)("thead",{children:(0,j.jsxs)("tr",{className:"sub_tbl h-3",children:[(0,j.jsx)("td",{className:"px-6 py-2 bg-gray-50 text-center text-[12px] font-medium bg-gray-200 text-gray-500 uppercase tracking-wider border",children:"Plan Details"}),(0,j.jsx)("td",{className:"px-6 py-2 bg-gray-50 text-center text-[12px] font-medium bg-gray-200 text-gray-500 uppercase tracking-wider border",children:"Amount"}),(0,j.jsx)("td",{className:"px-6 py-2 bg-gray-50 text-center text-[12px] font-medium bg-gray-200 text-gray-500 uppercase tracking-wider border",children:"Trial Period"}),(0,j.jsx)("td",{className:"px-6 py-2 bg-gray-50 text-center text-[12px] font-medium bg-gray-200 text-gray-500 uppercase tracking-wider border",children:"Starts On"}),(d&&d.length>0&&d.some(e=>"inactive"===e.status)||d.some(e=>"paused"===e.status))&&(0,j.jsx)("td",{className:"px-6 py-2 bg-gray-50 text-center text-[12px] font-medium bg-gray-200 text-gray-500 uppercase tracking-wider border",children:"Expires On"}),(0,j.jsx)("td",{className:"px-6 py-2 bg-gray-50 text-center text-[12px] font-medium bg-gray-200 text-gray-500 uppercase tracking-wider border",children:"Status"}),(0,j.jsx)("td",{className:"px-6 py-2 bg-gray-50 text-center text-[12px] font-medium bg-gray-200 text-gray-500 uppercase tracking-wider border",children:"Actions"})]})}),(0,j.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:null==d?void 0:d.map((e,o)=>{return(0,j.jsxs)("tr",{className:"sub_tbl h-3",children:[(0,j.jsx)("td",{className:"px-6 py-2 bg-gray-50 text-center text-[12px] text-gray-500 uppercase tracking-wider border font-medium",children:(c=e.plan_type,c&&"enterprise"!==c.toLowerCase()?e.plan_type.replace("ProMax"," pro max"):e.plan_name)}),(0,j.jsx)("td",{className:"px-6 py-2 bg-gray-50 text-center text-[12px] text-gray-500 uppercase tracking-wider border font-medium",children:e.price_label}),(0,j.jsx)("td",{className:"px-6 py-2 bg-gray-50 text-center text-[12px] text-gray-500 uppercase tracking-wider border font-medium",children:e.trial_days?"Yes":"No"}),(0,j.jsx)("td",{className:"px-6 py-2 bg-gray-50 text-center text-[12px] text-gray-500 uppercase tracking-wider border font-medium",children:(0,u.p6)(e.start_date)}),d&&d.length>0&&d.some(e=>"inactive"===e.status)?"active"===e.status?(0,j.jsx)("td",{className:"inactive px-6 py-2 bg-gray-50 text-center text-[12px] text-gray-500 uppercase tracking-wider border font-medium"}):(0,j.jsx)("td",{className:"inactive px-6 py-2 bg-gray-50 text-center text-[12px] text-gray-500 uppercase tracking-wider border font-medium",children:(0,u.p6)(e.end_date)}):"active"===e.status?"":(e.status,(0,j.jsx)("td",{className:"active px-6 py-2 bg-gray-50 text-center text-[12px] text-gray-500 uppercase tracking-wider border font-medium",children:(0,u.p6)(e.end_date)})),(0,j.jsx)("td",{className:"px-6 py-2 bg-gray-50 text-center text-[12px] text-gray-500 uppercase tracking-wider border font-medium",children:e.status}),(0,j.jsxs)("td",{className:"px-6 py-2 bg-gray-50 text-center text-[12px] text-gray-500 uppercase tracking-wider border font-medium",children:["paused"===e.status?(0,j.jsx)("div",{className:"text-[12px]",children:(0,j.jsxs)(h.v,{as:"div",className:"relative inline-block text-center w-full text-[12px]",children:[(0,j.jsx)("div",{className:"w-full",children:(0,j.jsx)(h.v.Button,{className:"inline-flex w-full justify-center rounded-md bg-sky-500/100 px-4 py-2 font-medium text-white hover:bg-[#49b1df]",children:"Options"})}),(0,j.jsx)(x.u,{as:r.Fragment,enter:"transition ease-out duration-100",enterFrom:"transform opacity-0 scale-95",enterTo:"transform opacity-100 scale-100",leave:"transition ease-in duration-75",leaveFrom:"transform opacity-100 scale-100",leaveTo:"transform opacity-0 scale-95",children:(0,j.jsx)(h.v.Items,{className:(o<3?"dp-top":"dp-bot")+" absolute w-50 origin-top-right divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none text-center min-w-full text-center",children:(0,j.jsxs)("div",{className:"text-center min-w-full",children:["recurly"===y(e)||"stripe"===y(e)?(0,j.jsx)(h.v.Item,{children:t=>{let{active:s}=t;return(0,j.jsx)("button",{className:(s?"bg-sky-500/100 text-white":"text-gray-900")+" group min-w-full items-center px-4 py-2 text-left border-b border-b-slate-100",pid:e.pid,onClick:b,children:"Change Card"})}}):"","recurly"!==y(e)&&"stripe"!==y(e)&&"fastspring"!==y(e)||"paused"!==e.status||"monthly"!==w(e)?"":(0,j.jsx)(h.v.Item,{children:e=>{let{active:t}=e;return(0,j.jsx)("button",{className:(t?"bg-sky-500/100 text-white":"text-gray-900")+" group min-w-full items-center px-4 py-2 text-left border-b border-b-slate-100",onClick:e=>{window.location.href="/resume"},children:"Resume"})}}),(0,j.jsx)(h.v.Item,{children:t=>{let{active:s}=t;return(0,j.jsx)(D,{pid:e.pid,tk:p})}})]})})})]})}):"","active"===e.status?(0,j.jsx)("div",{className:"text-[12px]",children:(0,j.jsxs)(h.v,{as:"div",className:"relative inline-block text-center w-full text-[12px]",children:[(0,j.jsx)("div",{className:"w-full",children:(0,j.jsx)(h.v.Button,{className:"inline-flex w-full justify-center rounded-md bg-sky-500/100 px-4 py-2 font-medium text-white hover:bg-[#49b1df]",children:"Options"})}),(0,j.jsx)(x.u,{as:r.Fragment,enter:"transition ease-out duration-100",enterFrom:"transform opacity-0 scale-95",enterTo:"transform opacity-100 scale-100",leave:"transition ease-in duration-75",leaveFrom:"transform opacity-100 scale-100",leaveTo:"transform opacity-0 scale-95",children:(0,j.jsx)(h.v.Items,{className:(o<3?"dp-top":"dp-bot")+" absolute w-50 origin-top-right divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none text-center min-w-full text-center",children:(0,j.jsxs)("div",{className:"text-center min-w-full",children:["recurly"===y(e)||"stripe"===y(e)?(0,j.jsx)(h.v.Item,{children:t=>{let{active:s}=t;return(0,j.jsx)("button",{className:(s?"bg-sky-500/100 text-white":"text-gray-900")+" group min-w-full items-center px-4 py-2 text-left border-b border-b-slate-100",pid:e.pid,onClick:b,children:"Change Card"})}}):"",("usd"===f(e)||"paddle"===y(e)||"stripe"===y(e)||"paypal"===y(e)||"fastspring"===y(e))&&(0,u.tN)((0,u.o0)(i),(0,u.o0)(e.start_date))>=0?(0,j.jsxs)(j.Fragment,{children:["enterprise"!==v(e)&&"yearly"!==w(e)&&"promax"!==v(e)&&"97"!==m||"97"===m&&"yearly"!==w(e)&&"promax"===v(e)||"basic"===v(e)||"pro"===v(e)?(0,j.jsx)(h.v.Item,{children:s=>{let{active:r}=s;return(0,j.jsx)("button",{className:(r?"bg-sky-500/100 text-white":"text-gray-900")+" group min-w-full items-center px-4 py-2 text-left border-b border-b-slate-100",onClick:s=>(e=>{console.log(e),"enterprise"===e?t(!0):window.location.href="/upgrade"})(v(e)),children:"Upgrade"})}}):"","basic"===v(e)&&"monthly"===w(e)||"enterprise"===v(e)?"":(0,j.jsx)(h.v.Item,{children:e=>{let{active:t}=e;return(0,j.jsx)("button",{className:(t?"bg-sky-500/100 text-white":"text-gray-900")+" group min-w-full items-center px-4 py-2 text-left border-b border-b-slate-100",onClick:g,children:"Downgrade"})}}),"recurly"!==y(e)&&"stripe"!==y(e)&&"fastspring"!==y(e)||"active"!==e.status||"monthly"!==w(e)||"yes"!==e.is_trial_end||"Enterprise"===e.plan_type?"":(0,j.jsx)(h.v.Item,{children:t=>{let{active:r}=t;return(0,j.jsx)("button",{className:(r?"bg-sky-500/100 text-white":"text-gray-900")+" group min-w-full items-center px-4 py-2 text-left border-b border-b-slate-100",onClick:t=>(e=>{a(e.merchant_subscription_id),l(e.merchant),n(e.pid),s(!0)})(e),children:"Pause"})}})]}):(0,j.jsx)(j.Fragment,{}),(0,j.jsx)(h.v.Item,{children:t=>{let{active:s}=t;return(0,j.jsx)(D,{pid:e.pid,tk:p})}})]})})})]})}):""]})]},o);var c})})]}):(0,j.jsx)("div",{children:(0,j.jsxs)("span",{className:"text-[12px] text-gray-600",children:[(0,j.jsx)(c.DAO,{className:"inline text-lg mr-1"})," No active subscription. Look for available ",(0,j.jsx)("a",{href:"/pricing",className:"text-blue-400 font-bold",children:"SUBSCRIPTIONS"})]})})})})},E=e=>{let{user_order:t}=e;if(void 0!==t)return(0,j.jsx)("div",{className:"overflow-x-auto custom-scrollbar container-full-width bg-gray-50",children:t&&t.length?(0,j.jsxs)("table",{className:"min-w-full divide-y divide-gray-200 min-h-[50px]",children:[(0,j.jsx)("thead",{children:(0,j.jsxs)("tr",{className:"sub_tbl h-3",children:[(0,j.jsx)("th",{className:"px-6 py-3 bg-gray-50 text-center text-[12px] font-medium bg-gray-200 text-gray-500 uppercase tracking-wider",children:"Invoice"}),(0,j.jsx)("th",{className:"px-6 py-3 bg-gray-50 text-center text-[12px] font-medium bg-gray-200 text-gray-500 uppercase tracking-wider",children:"Membership"}),(0,j.jsx)("th",{className:"px-6 py-3 bg-gray-50 text-center text-[12px] font-medium bg-gray-200 text-gray-500 uppercase tracking-wider",children:"Amount"}),(0,j.jsx)("th",{className:"px-6 py-3 bg-gray-50 text-center text-[12px] font-medium bg-gray-200 text-gray-500 uppercase tracking-wider",children:"Date"}),(0,j.jsx)("th",{className:"px-6 py-3 bg-gray-50 text-center text-[12px] font-medium bg-gray-200 text-gray-500 uppercase tracking-wider",children:"Status"})]})}),(0,j.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:null==t?void 0:t.map((e,t)=>(0,j.jsxs)("tr",{className:"sub_tbl h-3",children:[(0,j.jsx)("td",{className:"px-6 py-3 bg-gray-50 text-center text-[12px] text-gray-500 uppercase tracking-wider border font-medium",children:e.charge_id}),(0,j.jsx)("td",{className:"px-6 py-3 bg-gray-50 text-center text-[12px] text-gray-500 uppercase tracking-wider border font-medium",children:e.label}),(0,j.jsx)("td",{className:"px-6 py-3 bg-gray-50 text-center text-[12px] text-gray-500 uppercase tracking-wider border font-medium",children:e.amount?(0,u.yt)(e.currency,e.amount):""}),(0,j.jsx)("td",{className:"px-6 py-3 bg-gray-50 text-center text-[12px] text-gray-500 uppercase tracking-wider border font-medium",children:(0,u.p6)(e.created_at)}),(0,j.jsx)("td",{className:"px-6 py-3 bg-gray-50 text-center text-[12px] text-gray-500 uppercase tracking-wider border font-medium",children:"0000-00-00 00:00:00"!==e.refund_at?"Refunded":"Completed"})]},t))})]}):(0,j.jsxs)("span",{className:"text-[12px] text-gray-600",children:[(0,j.jsx)(c.DAO,{className:"inline text-lg mr-1"})," No orders have been made yet. Look for available ",(0,j.jsx)("a",{href:"/pricing",className:"text-blue-400 font-bold",children:"SUBSCRIPTIONS"})]})})},I=e=>{let{tokenUsage:t,tokenUsageTotal:s,userMaxToken:r,currentDate:a,isShowEnterprise:l,currentPlan:n,gpt4oTotalToken:i,gpt4oMaxTokenUsage:o,plan:d,dalleTotalImage:c,dalleMaxImage:m,claudeTotalToken:u,claudeMaxTokenUsage:x,currentPlanName:p,fluxPrompt:h,fluxTotalPrompt:b,membersTotalFluxPrompt:g,o1Prompt:y,o1TotalPrompt:f,membersTotalo1Prompt:v,deepseekTotalToken:w,deepseekMaxTokenUsage:k}=e;const S=()=>"promax"===d.toLowerCase(),A=()=>"advanced"===d.toLowerCase(),C=()=>""===d||null===d,M=()=>"enterprise"===d.toLowerCase()&&"enterprise"!==p.toLowerCase(),L=e=>parseInt(e).toLocaleString("en-US"),T=()=>r<=s&&!S(),E=()=>parseInt(c)>=parseInt(m)&&!S(),I=()=>parseInt(u)>=parseInt(x)&&!S()&&!A(),F=()=>parseInt(w)>=parseInt(k)&&!S();return(0,j.jsxs)("div",{className:"w-full",children:[(0,j.jsxs)("div",{children:[(0,j.jsxs)("p",{className:"text-md font-bold\n          "+(!T()||C()||M()?"":"text-red-700"),children:[(0,j.jsxs)("span",{className:"total1",children:[T()&&!C()&&!M()&&(0,j.jsx)("img",{src:N,alt:"usage-warning",className:"block pr-2 w-8 inline-flex"}),"TOTAL TOKEN USAGE: ",(0,j.jsx)(j.Fragment,{})]}),(0,j.jsxs)("span",{className:"total2",children:[L(s),S()||M()?"":(0,j.jsxs)("span",{children:[" out of ",L(r)]})]})]}),(0,j.jsxs)("p",{className:"text-sm\n            "+("basic"===d.toLowerCase()&&parseInt(i)>=parseInt(o)||T()&&!C()&&!M()?"text-red-700":""),children:[(0,j.jsxs)("span",{className:"total1",children:[T()&&!C()&&!M()&&(0,j.jsx)("img",{src:N,alt:"usage-warning",className:"block pr-2 w-8 inline-flex"}),"TOTAL GPT-4o TOKEN USAGE: ",(0,j.jsx)(j.Fragment,{})]}),(0,j.jsxs)("span",{className:"total2",children:[L(i),"basic"===d.toLowerCase()?(0,j.jsxs)("span",{children:[" out of ",L(o)]}):""]})]}),"enterprise"!==d.toLowerCase()?S()?(0,j.jsxs)(j.Fragment,{children:[(0,j.jsxs)("p",{className:"text-sm",children:[(0,j.jsx)("span",{className:"total1",children:"TOTAL DALL·E IMAGE GENERATED: "}),(0,j.jsx)("span",{className:"total2",children:L(c)})]}),(0,j.jsxs)("p",{className:"text-sm",children:[(0,j.jsx)("span",{className:"total1",children:"TOTAL CLAUDE TOKEN USAGE: "}),(0,j.jsx)("span",{className:"total2",children:L(c)})]}),(0,j.jsxs)("p",{className:"text-sm",children:[(0,j.jsx)("span",{className:"total1",children:"TOTAL DEEPSEEK R1 TOKEN USAGE: "}),(0,j.jsx)("span",{className:"total2",children:L(w)})]})]}):(0,j.jsxs)(j.Fragment,{children:[(0,j.jsxs)("p",{className:`text-sm\n                  ${E()&&!C()?"text-red-700":""}\n                `,children:[(0,j.jsxs)("span",{className:"total1",children:[E()&&T()&&!C()&&(0,j.jsx)("img",{src:N,alt:"usage-warning",className:"block pr-2 w-8 inline-flex"}),"TOTAL DALL·E IMAGE GENERATED: ",(0,j.jsx)(j.Fragment,{})]}),(0,j.jsxs)("span",{className:"total2",children:[(0,j.jsxs)("span",{children:[" ",L(c)]})," out of ",(0,j.jsx)("span",{children:L(m)})]})]}),(0,j.jsxs)("p",{className:`text-sm\n                  ${!I()&&!T()||C()?"":"text-red-700"}\n                `,children:[(0,j.jsxs)("span",{className:"total1",children:[(I()&&T()&&!C()||T()&&A())&&(0,j.jsx)("img",{src:N,alt:"usage-warning",className:"block pr-2 w-8 inline-flex"}),"TOTAL CLAUDE TOKEN USAGE: ",(0,j.jsx)(j.Fragment,{})]}),(0,j.jsxs)("span",{className:"total2",children:[(0,j.jsxs)("span",{children:[" ",L(u)]}),A()?"":(0,j.jsxs)("span",{children:[" out of ",L(x)]})]})]}),"pro"===d.toLowerCase()?(0,j.jsxs)("p",{className:`text-sm\n                    ${!F()&&!T()||C()?"":"text-red-700"}\n                  `,children:[(0,j.jsxs)("span",{className:"total1",children:[(F()&&T()&&!C()||T()&&A())&&(0,j.jsx)("img",{src:N,alt:"usage-warning",className:"block pr-2 w-8 inline-flex"}),"TOTAL DEEPSEEK R1 TOKEN USAGE: ",(0,j.jsx)(j.Fragment,{})]}),(0,j.jsxs)("span",{className:"total2",children:[(0,j.jsxs)("span",{children:[" ",L(w)]}),A()?"":(0,j.jsxs)("span",{children:[" out of ",L(k)]})]})]}):(0,j.jsx)(j.Fragment,{})]}):"enterprise"===d.toLowerCase()&&"enterprise"!==p.toLowerCase()?(0,j.jsxs)(j.Fragment,{children:[(0,j.jsxs)("p",{className:`text-sm\n                ${h>=b?"text-red-700":""}\n              `,children:[(0,j.jsxs)("span",{className:"total1",children:["TOTAL FLUX PROMPTS: ",(0,j.jsx)(j.Fragment,{})]}),(0,j.jsxs)("span",{className:"total2",children:[(0,j.jsxs)("span",{children:[" ",L(h)]})," out of ",(0,j.jsx)("span",{children:L(b)})]})]}),(0,j.jsxs)("p",{className:`text-sm\n                ${y>=f?"text-red-700":""}\n              `,children:[(0,j.jsxs)("span",{className:"total1",children:["TOTAL o1 PROMPTS: ",(0,j.jsx)(j.Fragment,{})]}),(0,j.jsxs)("span",{className:"total2",children:[(0,j.jsxs)("span",{children:[" ",L(y)]})," out of ",(0,j.jsx)("span",{children:L(f)})]})]})]}):(0,j.jsx)(j.Fragment,{}),d&&!S()?(0,j.jsxs)("p",{className:"text-sm font-bold pb-2",children:[(0,j.jsx)("span",{className:"total1",children:"Token count will reset on:"}),"  ",(0,j.jsx)("span",{className:"total2",children:a})]}):""]}),""!==n&&!S()&&T()&&!1===l?(0,j.jsxs)("div",{children:[(0,j.jsx)("p",{className:"text-md text-red-700",children:"UPGRADE is required to continue"}),(0,j.jsx)("div",{className:"flex inline-flex sm:mr-2",children:(0,j.jsx)("p",{children:(0,j.jsx)("a",{href:"/upgrade/?mx=1",class:"block w-full font-bold text-white py-2 px-4 rounded-md bg-[#ffa500] hover:bg-[#FFC034]",children:"Upgrade"})})})]}):"",""===n||S()||!T()||!0!==l||M()?"":(0,j.jsxs)("div",{children:[(0,j.jsx)("p",{className:"text-md text-red-700",children:"For continous access, kindly reach out to our support team"}),(0,j.jsx)("div",{className:"flex inline-flex sm:mr-2",children:(0,j.jsx)("p",{children:(0,j.jsx)("a",{href:"https://ai-pro.org/contact-us",class:"block w-full font-bold text-white py-2 px-4 rounded-md bg-[#ffa500] hover:bg-[#FFC034]",children:"Contact Support"})})})]}),(0,j.jsx)("ul",{className:"pt-5",children:null==t?void 0:t.map((e,t)=>(0,j.jsxs)("li",{children:[(0,j.jsxs)("span",{className:"font-bold",children:[e.app,":"]})," ",parseInt(e.total_token).toLocaleString("en-US")]}))})]})},F=()=>(0,j.jsxs)("div",{className:"w-full",children:[(0,j.jsxs)("p",{className:"text-sm py-4",children:["If you have any comments about our website, AI tools. and articles, or if you have questions about your account access, please don’t hesitate to get in touch with us. Leave your messages through the ",(0,j.jsx)("a",{href:"https://ai-pro.org/contact-us/",className:"text-blue-400 font-bold",children:"Contact us"})," page."]}),(0,j.jsx)("p",{className:"font-bold",children:"Quick Links:"}),(0,j.jsxs)("ul",{children:[(0,j.jsx)("li",{children:(0,j.jsx)("a",{href:"/my-account",className:"text-blue-400 font-bold px-2",children:" My Apps"})}),(0,j.jsx)("li",{children:(0,j.jsx)("a",{href:"/my-account",className:"text-blue-400 font-bold px-2",children:" AI Tools"})}),(0,j.jsx)("li",{children:(0,j.jsx)("a",{href:"https://ai-pro.org/articles/",className:"text-blue-400 font-bold px-2",children:" Articles"})}),(0,j.jsx)("li",{children:(0,j.jsx)("a",{href:"https://ai-pro.org/tutorials/",className:"text-blue-400 font-bold px-2",children:" Tutorials"})})]})]}),_=()=>(0,j.jsx)("div",{children:(0,j.jsx)(a.E.button,{className:"bg-blue-500 mb-1 text-white font-bold py-3 px-6 my-3 rounded-lg",whileTap:{scale:.9},onClick:function(){(0,o.Sz)("access"),(0,o.Sz)("ci_session"),m.Z.get("http://localhost:9002/api/logout").then(function(){window.location.href="/login"})},children:"Logout"})}),P=e=>{const[t]=(0,r.useState)(e.email),[s,a]=(0,r.useState)(!1),l=()=>{a(!1)};return(0,j.jsxs)(j.Fragment,{children:[(0,j.jsx)("span",{className:"cursor-pointer hover:underline hover:decoration-solid",onClick:()=>{a(!0)},children:"Delete Account"}),(0,j.jsx)(x.u,{appear:!0,show:s,as:r.Fragment,children:(0,j.jsxs)(p.V,{as:"div",className:"relative z-10",onClose:l,children:[(0,j.jsx)(x.u.Child,{as:r.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:(0,j.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-25"})}),(0,j.jsx)("div",{className:"fixed inset-0 overflow-y-auto",children:(0,j.jsx)("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:(0,j.jsx)(x.u.Child,{as:r.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:(0,j.jsxs)(p.V.Panel,{className:"w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all",children:[(0,j.jsx)(p.V.Title,{as:"h3",className:"text-lg font-medium leading-6 text-gray-900",children:"Delete Account"}),(0,j.jsxs)("div",{className:"mt-2",children:[(0,j.jsx)("p",{className:"text-sm text-gray-500",children:"Deleting your account will permanently remove all of your data and information associated with it. This action is irreversible, and you won't be able to recover your account or any of its contents."}),(0,j.jsx)("p",{className:"text-sm text-gray-500",children:"Are you sure you want to delete your account?"})]}),(0,j.jsxs)("div",{className:"mt-4",children:[(0,j.jsx)("button",{type:"button",className:"inline-flex justify-center rounded-md border border-transparent bg-red-500 px-4 py-2 mr-2 text-sm font-medium text-white hover:bg-[#d30101]",onClick:()=>{document.querySelector(".loader-container").classList.add("active"),m.Z.post("http://localhost:9002/api/delete-account",{},{headers:{"content-type":"application/x-www-form-urlencoded"}}).then(function(e){if(e.data.success)return window.mixpanel.people.set_once({$email:t}),window.mixpanel.identify(t),window.mixpanel.track("delete_account",{}),f().success("Success"),void window.location.reload();document.querySelector(".loader-container").classList.remove("active"),f().error("Fail")})},children:"Yes! Delete My Account"}),(0,j.jsx)("button",{type:"button",className:"inline-flex justify-center rounded-md border border-transparent bg-gray-100 px-4 py-2 text-sm font-medium text-black hover:bg-gray-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-gray-500 focus-visible:ring-offset-2",onClick:l,children:"Cancel"})]})]})})})})]})})]})},D=e=>{let{pid:t,tk:s}=e;const[a,l]=(0,r.useState)(!1),n=(0,i.gx)(),o=()=>{l(!1)};return(0,j.jsxs)(j.Fragment,{children:[(0,j.jsx)("button",{className:"hover:bg-sky-500/100 hover:text-white text-gray-900 group min-w-full items-center px-4 py-2 text-left",pid:t,onClick:()=>{l(!0)},children:"Cancel"}),(0,j.jsx)(x.u,{appear:!0,show:a,as:r.Fragment,children:(0,j.jsxs)(p.V,{as:"div",className:"relative z-10",onClose:o,children:[(0,j.jsx)(x.u.Child,{as:r.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:(0,j.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-25"})}),(0,j.jsx)("div",{className:"fixed inset-0 overflow-y-auto",children:(0,j.jsx)("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:(0,j.jsx)(x.u.Child,{as:r.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:(0,j.jsxs)(p.V.Panel,{className:"w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all",children:[(0,j.jsx)(p.V.Title,{as:"h3",className:"text-lg font-medium leading-6 text-gray-900",children:"Cancel Subscription"}),(0,j.jsx)("div",{className:"mt-2",children:(0,j.jsx)("p",{className:"text-sm text-gray-500",children:"Are you sure you want to cancel your subscription?"})}),(0,j.jsxs)("div",{className:"mt-4 text-right",children:[(0,j.jsx)("button",{type:"button",className:"inline-flex justify-center rounded-md border border-transparent bg-red-500 px-4 py-2 mr-2 text-sm font-medium text-white hover:bg-[#d30101]",onClick:e=>{t&&(o(),document.querySelector(".loader-container").classList.add("active"),""===n.surveydata||null===n.surveydata?window.location.href="/survey":m.Z.post("http://localhost:9002/api/cancel-subscription",{tk:s,p_id:t},{headers:{"content-type":"application/x-www-form-urlencoded"}}).then(function(e){let t=e.data;if(t.success)return f().success("Success"),void window.location.reload();document.querySelector(".loader-container").classList.remove("active"),t.data&&f().error(t.data.msg)}))},children:"Yes"}),(0,j.jsx)("button",{type:"button",className:"inline-flex justify-center rounded-md border border-transparent bg-gray-100 px-4 py-2 text-sm font-medium text-black hover:bg-gray-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-gray-500 focus-visible:ring-offset-2",onClick:o,children:"Close"})]})]})})})})]})})]})};function O(e,t){return e.slice(0,t)}function R(e,t){if(e.length<t)return 1;let s=e.length,r=Math.floor(s/t);return s%t>0&&(r+=1),r}const U=e=>{let{handleShowAddMember:t,enterpriseMembers:s,handleReloadMembers:l,handleShowEditMemberModal:n,currentPlan:i,currentPlanName:o}=e;const[d,m]=(0,r.useState)(""),[p,b]=(0,r.useState)([]),[g,y]=(0,r.useState)([]),[f,v]=(0,r.useState)(5),[w,N]=(0,r.useState)(0),[k,S]=(0,r.useState)(1);(0,r.useEffect)(()=>{let e=s;b(e),e=O(e,5),y(e);let t=R(s,5);N(t)},[s]);const A=()=>{let e=document.getElementById("ent_search").value,t=s;t=s.filter(function(t){let s=t.first_name+" "+t.last_name;return t.first_name.indexOf(e)>-1||t.last_name.indexOf(e)>-1||t.email.indexOf(e)>-1||s.indexOf(e)>-1});let r=R(t,f);N(r),b(t),t=O(t,f),y(t)};(0,r.useEffect)(()=>{A()},[f]);const C=e=>{S(e);let t=p,s=(e-1)*f;t=t.slice(s,t.length),t=O(t,f),y(t)};return(0,j.jsx)(j.Fragment,{children:(0,j.jsxs)("div",{className:"w-full",children:[(0,j.jsxs)("div",{className:"block md:grid md:grid-cols-3 md:gap-4 text-[12px]",children:[(0,j.jsx)("div",{className:"text-left md:col-span-1",children:(0,j.jsx)("input",{className:"w-full px-3 py-2 mb-2 border border-gray-300 rounded fs-exclude",type:"text",id:"ent_search",name:"search",placeholder:"Search by Name or Email",value:d,onChange:e=>{A(),m(e.target.value)}})}),(0,j.jsx)("div",{className:"text-left md:col-span-1",children:(0,j.jsx)(a.E.button,{className:"bg-sky-500 w-full md:w-48 text-white font-bold py-2 px-6 rounded-md proceed-pmt",whileHover:{backgroundColor:"#49b1df"},whileTap:{scale:.9},onClick:()=>t(),children:"+ Add Member"})}),s.length>0?(0,j.jsxs)("div",{className:"text-center md:text-right md:col-span-1 my-2 text-[11px]",children:["Show ",(0,j.jsxs)("select",{className:"border rounded-md",onChange:e=>(v(e.target.value),void S(1)),children:[(0,j.jsx)("option",{value:"5",children:"5"}),(0,j.jsx)("option",{value:"10",children:"10"}),(0,j.jsx)("option",{value:"15",children:"15"}),(0,j.jsx)("option",{value:"20",children:"20"})]})," entries"]}):""]}),(0,j.jsx)("div",{className:"overflow-x-scroll lg:overflow-x-visible overflow-y-visible container-full-width cm-scrollbar pb-1 min-h-[400px]",children:g&&g.length?(0,j.jsxs)("table",{className:"subs-table min-w-full divide-y bg-gray-50 divide-gray-200 min-h-[50px]",children:[(0,j.jsx)("thead",{children:(0,j.jsxs)("tr",{className:"sub_tbl h-3",children:[(0,j.jsx)("td",{className:"px-6 py-2 bg-gray-50 text-center text-[12px] font-medium bg-gray-200 text-gray-500 uppercase tracking-wider border",children:"Fullname"}),(0,j.jsx)("td",{className:"px-6 py-2 bg-gray-50 text-center text-[12px] font-medium bg-gray-200 text-gray-500 uppercase tracking-wider border",children:"Email"}),(0,j.jsx)("td",{className:"px-6 py-2 bg-gray-50 text-center text-[12px] font-medium bg-gray-200 text-gray-500 uppercase tracking-wider border",children:"Status"}),(0,j.jsx)("td",{className:"px-6 py-2 bg-gray-50 text-center text-[12px] font-medium bg-gray-200 text-gray-500 uppercase tracking-wider border",children:"Date Added"}),"enterprise"===i.toLowerCase()&&"enterprise"!==o.toLowerCase()?(0,j.jsx)("td",{className:"px-6 py-2 bg-gray-50 text-center text-[12px] font-medium bg-gray-200 text-gray-500 uppercase tracking-wider border",children:"Total Usage"}):(0,j.jsx)("td",{className:"px-6 py-2 bg-gray-50 text-center text-[12px] font-medium bg-gray-200 text-gray-500 uppercase tracking-wider border",children:"Total Token Used"}),(0,j.jsx)("td",{className:"px-6 py-2 bg-gray-50 text-center text-[12px] font-medium bg-gray-200 text-gray-500 uppercase tracking-wider border",children:"Actions"})]})}),(0,j.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:null==g?void 0:g.map((e,t)=>(0,j.jsxs)("tr",{className:"sub_tbl h-3",children:[(0,j.jsxs)("td",{className:"px-6 py-2 bg-gray-50 text-center text-[12px] text-gray-500 uppercase tracking-wider border font-medium",children:[e.first_name," ",e.last_name]}),(0,j.jsx)("td",{className:"px-6 py-2 bg-gray-50 text-center text-[12px] text-gray-500 uppercase tracking-wider border font-medium",children:e.email}),(0,j.jsx)("td",{className:"px-6 py-2 bg-gray-50 text-center text-[12px] text-gray-500 uppercase tracking-wider border font-medium",children:e.status}),(0,j.jsx)("td",{className:"px-6 py-2 bg-gray-50 text-center text-[12px] text-gray-500 uppercase tracking-wider border font-medium",children:(0,u.p6)(e.created_at)}),(0,j.jsx)("td",{className:"px-6 py-2 bg-gray-50 text-center text-[12px] text-gray-500 tracking-wider border font-medium",children:"enterprise"===i.toLowerCase()&&"enterprise"!==o.toLowerCase()?(0,j.jsxs)(j.Fragment,{children:[(0,j.jsxs)("p",{children:["Tokens: ",e.total_token]}),(0,j.jsxs)("p",{children:["Flux: ",e.total_flux_prompt]}),(0,j.jsxs)("p",{children:["o1: ",e.total_o1_prompt]})]}):(0,j.jsx)("p",{children:e.total_token})}),(0,j.jsx)("td",{className:"px-6 py-2 bg-gray-50 text-center text-[12px] text-gray-500 uppercase tracking-wider border font-medium",children:"active"===e.status?(0,j.jsx)("div",{className:"text-[12px]",children:(0,j.jsxs)(h.v,{as:"div",className:"relative inline-block text-center w-full text-[12px]",children:[(0,j.jsx)("div",{className:"w-full",children:(0,j.jsx)(h.v.Button,{className:"inline-flex w-full justify-center rounded-md bg-sky-500/100 px-4 py-2 font-medium text-white hover:bg-[#49b1df]",children:"Options"})}),(0,j.jsx)(x.u,{as:r.Fragment,enter:"transition ease-out duration-100",enterFrom:"transform opacity-0 scale-95",enterTo:"transform opacity-100 scale-100",leave:"transition ease-in duration-75",leaveFrom:"transform opacity-100 scale-100",leaveTo:"transform opacity-0 scale-95",children:(0,j.jsx)(h.v.Items,{className:(t<3?"dp-top":"dp-bot")+" z-[9999] absolute w-50 origin-top-right divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none text-center min-w-full text-center",children:(0,j.jsxs)("div",{className:"text-center min-w-full",children:[(0,j.jsx)(h.v.Item,{children:(0,j.jsx)("button",{className:"group min-w-full items-center px-4 py-2 text-left border-b border-b-slate-100 text-gray-900",onClick:(t,s,r,a)=>((e,t,s,r)=>{let a={};a.user_id=e,a.first_name=t,a.last_name=s,a.email=r,n(!0,a)})(e.user_id,e.first_name,e.last_name,e.email),children:"Edit Info"})}),(0,j.jsx)(h.v.Item,{children:(0,j.jsx)(Z,{member_user_id:e.user_id,email:e.email})}),(0,j.jsx)(h.v.Item,{children:(0,j.jsx)(G,{member_user_id:e.user_id,email:e.email,handleReloadMembers:l})})]})})})]})}):""})]},t))})]}):(0,j.jsx)("div",{children:(0,j.jsxs)("span",{className:"text-[12px] text-gray-600",children:[(0,j.jsx)(c.DAO,{className:"inline text-lg mr-1"}),"No members found."]})})}),(0,j.jsxs)("div",{className:"py-2 text-[11px]",children:[(0,j.jsx)("span",{className:"block md:flex float-left w-full md:w-auto text-center md:text-right py-2 justify-center md:justify-normal items-center md:items-start",children:(()=>{let e=p.length,t=k*f,s=t-(f-1);return t>e&&(t=e),"Showing "+s+" to "+t+" of "+e+" entries"})()}),(0,j.jsx)("ul",{className:"w-full md:w-auto flex float-none md:float-right justify-center md:justify-normal items-center md:items-end",children:(()=>{let e=[];if(w<=1||0===s.length)return e;k>1?e.push((0,j.jsx)("li",{className:"p-2 cursor-pointer",onClick:e=>C(k-1),children:"Previous"},"prev_page")):e.push((0,j.jsx)("li",{className:"p-2 cursor-default",children:"Previous"},"prev_page"));for(let t=0;t<w;t++){const s="p-2 rounded cursor-pointer w-[30px] text-center "+(k===t+1?"border":"");e.push((0,j.jsx)("li",{className:s,onClick:e=>C(t+1),children:t+1},t+1))}return k<w?e.push((0,j.jsx)("li",{className:"p-2 cursor-pointer",onClick:e=>C(k+1),children:"Next"},"prev_page")):e.push((0,j.jsx)("li",{className:"p-2 cursor-default",children:"Next"},"prev_page")),e})()})]})]})})},B=e=>{let{showEditMember:t,setShowEditEnterprise:s,editMemberDetails:a,handleReloadMembers:l}=e;const[n,i]=(0,r.useState)(""),[o,d]=(0,r.useState)(""),[c,u]=(0,r.useState)(""),[x,p]=(0,r.useState)(""),[h,b]=(0,r.useState)(""),g=()=>{let e=document.getElementById("modalEditMembers");if(null!==e){let t=document.getElementById("ent-edit-email");t.title="",t.classList.remove("ent-field-error");let r=document.getElementById("ent-edit-fullname");r.title="",r.classList.remove("ent-field-error"),console.log("modal close"),e.style.display="none",s(!1)}};(0,r.useEffect)(()=>{let e=a.first_name?a.first_name:"",t=a.last_name?a.last_name:"",s=a.email?a.email:"",r=a.user_id?a.user_id:"";i(e+" "+t),d(e+" "+t),u(s),p(s),b(r)},[a]);void 0!==t&&!0===t&&(()=>{let e=document.getElementById("modalEditMembers");null!==e&&(e.style.display="block")})(),void 0!==t&&!1===t&&g();return(0,j.jsx)(j.Fragment,{children:(0,j.jsx)("div",{id:"modalEditMembers",className:"modal z-[9999]",children:(0,j.jsx)("div",{class:"modal-content w-full md:w-[50%]",children:(0,j.jsxs)("div",{children:[(0,j.jsxs)("div",{className:"mb-4 flex border-b pb-2",children:[(0,j.jsx)("div",{className:"text-blue-500 w-full md:w-2/3 mr-2 md:mr-5 font-bold",children:"Edit Members"}),(0,j.jsx)("div",{className:"float-right mt-[-5px] w-full md:w-1/3",children:(0,j.jsx)("span",{class:"close",onClick:()=>g(),children:"×"})})]}),(0,j.jsxs)("div",{className:"border-b border-[#dddddd] mx-auto my-4 pb-4",children:[(0,j.jsxs)("div",{className:"w-full text-[12px] md:text-[12px] font-bold pb-2",children:[(0,j.jsx)("p",{children:"Fulname *"}),(0,j.jsx)("input",{type:"text",id:"ent-edit-fullname",className:"w-full px-3 py-2 border border-gray-300 rounded",placeholder:"Fullname",value:n,onChange:e=>{e.preventDefault(),i(e.target.value),(e=>{let t=e.target.value.trim(),s=document.getElementById("ent-edit-fullname-error");""===t?(s.innerHTML="Full Name is required",s.style.display="block",e.target.classList.add("ent-field-error")):(s.innerHTML="",s.style.display="none",e.target.classList.remove("ent-field-error"))})(e)},title:""}),(0,j.jsx)("div",{class:"member-error",id:"ent-edit-fullname-error"})]}),(0,j.jsxs)("div",{className:"w-full text-[12px] md:text-[12px] font-bold pb-2",children:[(0,j.jsx)("p",{children:"Email *"}),(0,j.jsx)("input",{type:"text",id:"ent-edit-email",className:"w-full px-3 py-2 border border-gray-300 rounded",placeholder:"Email",value:c,onChange:e=>{e.preventDefault(),u(e.target.value),(e=>{let t=e.target.value,s=document.getElementById("ent-edit-email-error");""===t.trim()?(s.innerHTML="Email is required",s.style.display="block",e.target.classList.add("ent-field-error")):/\S+@\S+\.\S+/.test(t)?(s.innerHTML="",s.style.display="none",e.target.classList.remove("ent-field-error")):(s.innerHTML="Invalid email format",s.style.display="block",e.target.classList.add("ent-field-error"))})(e)},title:""}),(0,j.jsx)("div",{class:"member-error",id:"ent-edit-email-error"})]})]}),(0,j.jsx)("div",{className:"",children:(0,j.jsxs)("div",{className:"text-right",children:[(0,j.jsx)("input",{type:"button",value:"Update Member",className:"border rounded font-bold bg-blue-500 text-white py-2 px-4 cursor-pointer text-[12px] md:text-[14px] mr-2",onClick:()=>(async()=>{let e=document.getElementById("ent-edit-fullname"),t=document.getElementById("ent-edit-fullname-error"),s=document.getElementById("ent-edit-email"),r=document.getElementById("ent-edit-email-error");if(""===s.value.trim())return r.innerHTML="Email is required",r.style.display="block",void s.classList.add("ent-field-error");if(!/\S+@\S+\.\S+/.test(c))return r.innerHTML="Invalid email format",r.style.display="block",void s.classList.add("ent-field-error");if(""===e.value.trim())return t.innerHTML="Full Name is required",t.style.display="block",void e.classList.add("ent-field-error");if(document.querySelector(".loader-container").classList.add("active"),o===e.value&&x===s.value)return g(),void document.querySelector(".loader-container").classList.remove("active");m.Z.post("http://localhost:9002/api/edit-enterprise-member",{member_user_id:h,member_email:s.value,member_fullname:e.value,member_old_fullname:o,member_old_email:x},{headers:{"content-type":"application/x-www-form-urlencoded"}}).then(function(e){let t=e.data;if(document.querySelector(".loader-container").classList.remove("active"),t.success)if(Array.isArray(t.data)&&t.data.length>0)for(var s=0;s<t.data.length;s++){let e=document.getElementById("ent-edit-email");e.title=t.data[s].error,e.classList.add("ent-field-error"),f().error(t.data[s].error)}else g(),l(),f().success("Member Updated");else g(),l(),f().error("Update Member Failed")}).catch(function(e){g(),document.querySelector(".loader-container").classList.remove("active"),f().error("Something went wrong. Please try again in a bit!")})})()}),(0,j.jsx)("input",{type:"button",value:"Close",className:"border rounded font-bold bg-white text-black py-2 px-4 cursor-pointer text-[12px] md:text-[14px]",onClick:()=>g()})]})})]})})})})},z=e=>{let{showAddMember:t,setshowAddMember:s,handleReloadMembers:a,entMaxMembers:l,enterpriseMembers:n,isMax:i,isVisbleBtn:o,setIsVisibleBtn:d}=e;const[u,x]=(0,r.useState)([{name:"",email:"",error:""}]),[p,h]=(0,r.useState)(!0),[b,g]=(0,r.useState)(!0),y=()=>{x(e=>[...e,{name:"",email:"",error:""}]);let e=u.length+n.length;d(!0),g(!0),i&&e+1>=l&&h(!1)},v=async()=>{await x(e=>e.filter((e,t)=>t<=-1)),y(),h(!0),g(!0)},w=e=>{x(t=>t.filter((t,s)=>s!==e));let t=u.length+n.length-1;i&&t<l&&(d(!0),h(!0)),i&&1===u.length&&g(!1)},N=()=>{let e=document.getElementById("modalAddMembers");null!==e&&(e.style.display="none",s(!1))},k=async()=>{let e=0,t=!1,s="",r="",a="",l="",n="",i="",o=!1;for(;e<u.length;){s=document.getElementById("name-"+e),r=document.getElementById("email-"+e),l=document.getElementById("error1-"+e),n=document.getElementById("error2-"+e);let d=u[e].email.trim();a=u[e].name.trim(),i=u[e].error.trim(),l.style.display="none",n.style.display="none",s.title="",s.classList.remove("ent-field-error"),r.title="",r.classList.remove("ent-field-error"),""===d?(r.title="Email is required",r.classList.add("ent-field-error"),n.style.display="block",n.innerHTML="Email is required",t=!0):/\S+@\S+\.\S+/.test(d)||(r.title="Invalid email format",r.classList.add("ent-field-error"),n.style.display="block",n.innerHTML="Invalid email format",t=!0),""!==i&&(r.title=i,r.classList.add("ent-field-error"),t=!0),""===a&&(s.title="Full Name is required",s.classList.add("ent-field-error"),l.innerHTML="Full Name is required",l.style.display="block",t=!0),t||(o=u.filter(function(e){return e.email.trim()===d}),o.length>1&&(r.title="Duplicate email",r.classList.add("ent-field-error"),n.innerHTML="Duplicate email",n.style.display="block",t=!0)),e++}return t};return void 0!==t&&!0===t&&(()=>{let e=document.getElementById("modalAddMembers");null!==e&&(e.style.display="block")})(),void 0!==t&&!1===t&&N(),(0,j.jsx)(j.Fragment,{children:(0,j.jsx)("div",{id:"modalAddMembers",className:"modal z-[9999]",children:(0,j.jsx)("div",{class:"modal-content w-full md:w-[60%]",children:(0,j.jsxs)("div",{children:[(0,j.jsxs)("div",{className:"mb-4 flex border-b pb-2",children:[(0,j.jsx)("div",{className:"text-blue-500 w-full md:w-2/3 mr-2 md:mr-5 font-bold",children:"Add Members"}),(0,j.jsx)("div",{className:"float-right mt-[-5px] w-full md:w-1/3",children:(0,j.jsx)("span",{class:"close",onClick:()=>N(),children:"×"})})]}),(0,j.jsxs)("div",{className:"text-[11px] pb-2",children:[(0,j.jsx)("b",{children:"Note:"})," The password for each member will be sent to their email address"]}),(0,j.jsxs)("div",{className:"",children:[(0,j.jsxs)("div",{className:"border rounded-tr rounded-tl flex p-2",children:[(0,j.jsx)("div",{className:"w-1/2 text-[12px] md:text-[12px] font-bold",children:"Full Name"}),(0,j.jsx)("div",{className:"w-1/2 ml-[-4px] text-[12px] md:text-[12px] font-bold",children:"Email"})]}),null==u?void 0:u.map((e,t)=>(0,j.jsxs)("div",{className:"flex py-2 pl-2 bg-gray-100",children:[(0,j.jsxs)("div",{className:"w-1/2 mr-2 text-[11px] md:text-[14px]",children:[(0,j.jsx)("input",{type:"text",id:"name-"+t,index:t,className:"w-full px-3 py-2 border border-gray-300 rounded fs-exclude",placeholder:"Enter Full Name",value:e.name,onChange:e=>{x(s=>{const r=s.slice();return r[t].name=e.target.value,((e,t)=>{let s=e.target.value.trim(),r=document.getElementById("email-"+t).value,a=document.getElementById("error1-"+t);""===s&&""!==r?(e.target.title="Full Name is required",e.target.classList.add("ent-field-error"),a.style.display="block",a.innerHTML="Full Name is required"):(e.target.title="",e.target.classList.remove("ent-field-error"),a.style.display="none",a.innerHTML="")})(e,t),r})},title:""}),(0,j.jsx)("div",{class:"member-error",id:"error1-"+t})]}),(0,j.jsxs)("div",{className:"w-1/2 mr-2 text-[11px] md:text-[14px]",children:[(0,j.jsx)("input",{type:"text",id:"email-"+t,index:t,className:"w-full px-3 py-2 border border-gray-300 rounded fs-exclude",placeholder:"Enter Email",value:e.email,onChange:e=>{x(s=>{const r=s.slice();return r[t].email=e.target.value,((e,t)=>{let s=e.target.value,r=!0,a=document.getElementById("name-"+t),l=a.value.trim(),n=document.getElementById("error1-"+t),i=document.getElementById("error2-"+t);""===s.trim()?(e.target.title="Email is required",e.target.classList.add("ent-field-error"),i.style.display="block",i.innerHTML="Email is required",r=!1):/\S+@\S+\.\S+/.test(s)||(e.target.title="Invalid email format",e.target.classList.add("ent-field-error"),i.style.display="block",i.innerHTML="Invalid email format",r=!1),r&&(e.target.title="",e.target.classList.remove("ent-field-error"),n.style.display="none",n.innerHTML="",i.style.display="none",i.innerHTML=""),r&&""===l&&(a.title="Full Name is required",a.classList.add("ent-field-error"),n.style.display="block",n.innerHTML="Full Name is required")})(e,t),r})},title:""}),(0,j.jsx)("div",{class:"member-error",id:"error2-"+t})]}),(0,j.jsx)("div",{className:"w-1/20 text-right pt-2",children:(0,j.jsx)("button",{onClick:()=>w(t),children:(0,j.jsx)("span",{children:(0,j.jsx)(c.Xm5,{className:"text-lg mr-2 text-red-500"})})})})]}))]}),(0,j.jsxs)("div",{className:(i&&!p?"justify-end":o?"justify-between":"justify-end")+" flex border-b border-[#dddddd] mx-auto my-4 pb-4",children:[p&&o&&(0,j.jsx)("input",{type:"button",value:"Add More",className:"border rounded font-bold bg-white py-2 px-4 cursor-pointer text-[12px] md:text-[14px]",onClick:()=>y()}),b&&(0,j.jsx)("input",{type:"button",value:"Remove Last",className:"border rounded font-bold bg-white float-right py-2 px-4 cursor-pointer text-[12px] md:text-[14px]",onClick:()=>(()=>{if(u.length>0){let e=u.length-1;w(e)}})()})]}),b&&(0,j.jsx)("div",{className:"text-right",children:(0,j.jsx)("input",{type:"button",value:"Add Member",className:"border rounded font-bold bg-blue-500 text-white py-2 px-4 cursor-pointer text-[12px] md:text-[14px]",onClick:()=>(async()=>{u.length+n.length>l?f().error("Members exceeded. Max. members should only "+l+" members."):await k()||(u.length<=0?N():(document.querySelector(".loader-container").classList.add("active"),m.Z.post("http://localhost:9002/api/add-enterprise-member",{members:JSON.stringify(u)},{headers:{"content-type":"application/x-www-form-urlencoded"}}).then(function(e){let t=e.data;if(document.querySelector(".loader-container").classList.remove("active"),t.success)if(Array.isArray(t.data)&&t.data.length>0)for(var s=0;s<t.data.length;s++){let e=t.data[s].index,r=document.getElementById("email-"+e);r.title=t.data[s].error,r.classList.add("ent-field-error");let a=document.getElementById("error2-"+e);a.style.display="block",a.innerHTML=t.data[s].error}else f().success("Members Added"),N(),a(),v();else f().error("Adding Members Failed"),N(),a(),v()}).catch(function(e){N(),document.querySelector(".loader-container").classList.remove("active"),f().error("Something went wrong. Please try again in a bit!")})))})()})})]})})})})},G=e=>{let{member_user_id:t,email:s,handleReloadMembers:a}=e;const[l,n]=(0,r.useState)(!1),i=()=>{n(!1)};return(0,j.jsxs)(j.Fragment,{children:[(0,j.jsx)("button",{className:"hover:bg-sky-500/100 hover:text-white text-gray-900 group min-w-full items-center px-4 py-2 text-left",onClick:()=>{n(!0)},children:"Delete"}),(0,j.jsx)(x.u,{appear:!0,show:l,as:r.Fragment,children:(0,j.jsxs)(p.V,{as:"div",className:"relative z-10",onClose:i,children:[(0,j.jsx)(x.u.Child,{as:r.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:(0,j.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-25"})}),(0,j.jsx)("div",{className:"fixed inset-0 overflow-y-auto",children:(0,j.jsx)("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:(0,j.jsx)(x.u.Child,{as:r.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:(0,j.jsxs)(p.V.Panel,{className:"w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all",children:[(0,j.jsx)(p.V.Title,{as:"h3",className:"text-red-800 w-full md:w-2/3 mr-2 md:mr-5 font-bold",children:"Delete Member"}),(0,j.jsx)("div",{className:"mt-2",children:(0,j.jsxs)("p",{className:"text-sm text-gray-500 break-words border-t border-b border-[#dddddd] py-2",children:[(0,j.jsx)(d.CSE,{className:"inline text-sm mr-1 text-red-800"}),"Are you sure you want to delete ",(0,j.jsx)("strong",{children:s})," account?"]})}),(0,j.jsxs)("div",{className:"mt-4 text-right",children:[(0,j.jsx)("button",{type:"button",className:"inline-flex justify-center rounded-md border border-transparent bg-red-500 px-4 py-2 mr-2 text-sm font-medium text-white hover:bg-[#d30101]",onClick:e=>{document.querySelector(".loader-container").classList.add("active"),i(),m.Z.post("http://localhost:9002/api/delete-enterprise-member",{member_user_id:t,member_email:s},{headers:{"content-type":"application/x-www-form-urlencoded"}}).then(function(e){let t=e.data;if(t.success)return document.querySelector(".loader-container").classList.remove("active"),f().success("Delete success."),void a();document.querySelector(".loader-container").classList.remove("active"),t.data&&f().error(t.data.msg)})},children:"Delete Member"}),(0,j.jsx)("button",{type:"button",className:"inline-flex justify-center rounded-md border border-transparent bg-gray-100 px-4 py-2 text-sm font-medium text-black hover:bg-gray-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-gray-500 focus-visible:ring-offset-2",onClick:i,children:"Close"})]})]})})})})]})})]})},Z=e=>{let{member_user_id:t,email:s}=e;const[a,l]=(0,r.useState)(!1),n=()=>{l(!1)};return(0,j.jsxs)(j.Fragment,{children:[(0,j.jsx)("button",{className:"hover:bg-sky-500/100 hover:text-white text-gray-900 group min-w-full items-center px-4 py-2 text-left",onClick:()=>{l(!0)},children:"Resend Password"}),(0,j.jsx)(x.u,{appear:!0,show:a,as:r.Fragment,children:(0,j.jsxs)(p.V,{as:"div",className:"relative z-10",onClose:n,children:[(0,j.jsx)(x.u.Child,{as:r.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:(0,j.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-25"})}),(0,j.jsx)("div",{className:"fixed inset-0 overflow-y-auto",children:(0,j.jsx)("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:(0,j.jsx)(x.u.Child,{as:r.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:(0,j.jsxs)(p.V.Panel,{className:"w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all",children:[(0,j.jsx)(p.V.Title,{as:"h3",className:"text-lg font-medium leading-6 text-gray-900",children:"Resend Password"}),(0,j.jsx)("div",{className:"mt-2",children:(0,j.jsxs)("p",{className:"text-sm text-gray-500 break-words",children:["Do you want to proceed with sending the new password to ",(0,j.jsx)("strong",{children:s}),"?"]})}),(0,j.jsxs)("div",{className:"mt-4 text-right",children:[(0,j.jsx)("button",{type:"button",className:"inline-flex justify-center rounded-md border border-transparent bg-red-500 px-4 py-2 mr-2 text-sm font-medium text-white hover:bg-[#d30101]",onClick:e=>{document.querySelector(".loader-container").classList.add("active"),n(),m.Z.post("http://localhost:9002/api/resend-pass-enterprise-member",{member_user_id:t,member_email:s},{headers:{"content-type":"application/x-www-form-urlencoded"}}).then(function(e){let t=e.data;if(t.success)return f().success("New Password sent."),void document.querySelector(".loader-container").classList.remove("active");document.querySelector(".loader-container").classList.remove("active"),t.data&&f().error(t.data.msg)})},children:"Resend Password"}),(0,j.jsx)("button",{type:"button",className:"inline-flex justify-center rounded-md border border-transparent bg-gray-100 px-4 py-2 text-sm font-medium text-black hover:bg-gray-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-gray-500 focus-visible:ring-offset-2",onClick:n,children:"Close"})]})]})})})})]})})]})},Y=function(){const[e,t]=(0,r.useState)([]),[s,a]=(0,r.useState)([]),[d,c]=(0,r.useState)(0),[u,x]=(0,r.useState)(0),[p,h]=(0,r.useState)(0),[y,N]=(0,r.useState)(0),[M,P]=(0,r.useState)(0),[D,O]=(0,r.useState)(0),[R,G]=(0,r.useState)(0),[Z,Y]=(0,r.useState)(0),[W,H]=(0,r.useState)(0),[q,V]=(0,r.useState)(0),[J,X]=(0,r.useState)(0),[Q,K]=(0,r.useState)(0),[$,ee]=(0,r.useState)(0),[te,se]=(0,r.useState)(0),[re,ae]=(0,r.useState)(0),[le,ne]=(0,r.useState)(0),[ie,oe]=(0,r.useState)(""),[de,ce]=(0,r.useState)("subscription"),[me,ue]=(0,r.useState)(!1),[xe,pe]=(0,r.useState)(!1),[he,be]=(0,r.useState)([]),[ge,ye]=(0,r.useState)(!1),[fe,ve]=(0,r.useState)(!1),[je,we]=(0,r.useState)(!1),[Ne,ke]=(0,r.useState)(""),[Se,Ae]=(0,r.useState)(""),[Ce,Me]=(0,r.useState)(""),[Le,Te]=(0,r.useState)(""),[Ee,Ie]=(0,r.useState)(""),[Fe,_e]=(0,r.useState)(""),[Pe,De]=(0,r.useState)(!1),[Oe,Re]=(0,r.useState)(!1),[Ue,Be]=(0,r.useState)(!1),[ze,Ge]=(0,r.useState)(!1),[Ze,Ye]=(0,r.useState)(!1),[We,He]=(0,r.useState)(!0),[qe,Ve]=(0,r.useState)([]),[Je,Xe]=(0,r.useState)(""),[Qe,Ke]=(0,r.useState)([]),$e=(0,i.gx)();if((0,r.useEffect)(()=>{f().options={positionClass:"toast-top-center"}},[]),(0,r.useEffect)(()=>{let e=(0,o.bG)("threed_error");void 0!==e&&""!==e&&setTimeout(function(){(0,o.I1)("threed_error",""),f().error(e)},3e3)},[]),(0,r.useEffect)(()=>{if(void 0!==$e&&"enterprise"===$e.plan&&(S=$e.max_members,ue(!0)),void 0!==$e){let e=null===$e.ent_parent_user_id?"":$e.ent_parent_user_id;Me(e),c($e.max_tokens),oe($e.max_end),Te($e.plan),Ie($e.plan_name),_e($e.user_ppg),"mem"!==window.view_data.active_tab&&ce("account")}else"mem"===window.view_data.active_tab&&ce("members")},[$e]),(0,r.useEffect)(()=>{(async()=>{let e=await L();t(e)})(),f().options={positionClass:"toast-top-center"}},[]),(0,r.useEffect)(()=>{(async()=>{let e=await async function(){const e=await m.Z.post("http://localhost:9002/api/get-token-usage",{tk:(0,o.bG)("access")},{headers:{"content-type":"application/x-www-form-urlencoded"}}),t=e.data?e.data:[];return t.success?t:[]}();console.log("tokens",e),void 0!==e.data&&(a(JSON.parse(e.data)),x(e.total),h(e.gpt4o_total_token),N(e.gpt_4o_max_token_usage),P(e.dalle_total_image),O(e.dalle_max_image),G(e.claude_total_token),Y(e.claude_max_token_limit),X(e.flux_prompt),K(e.flux_total_prompt),ee(e.members_total_flux_prompts),se(e.o1_prompt),ae(e.o1_total_prompt),ne(e.members_total_o1_prompts),H(e.deepseek_total_token),V(e.deepseek_max_token_usage))})()},[]),(0,r.useEffect)(()=>{0===k.length&&(async()=>{const e=await async function(){const e=await m.Z.post("http://localhost:9002/api/get-payment",{tk:(0,o.bG)("access")},{headers:{"content-type":"application/x-www-form-urlencoded"}}),t=e.data;return t.success?t.data:[]}();Ke(e)})()},[]),(0,r.useEffect)(()=>{if(void 0!==Qe){const e=["enterprisemax","officemax","teammax"];(()=>{const t=Qe.find(t=>e.includes(t.label.toLowerCase().replace(/\s+/g,"")));Ye(Boolean(t))})()}},[Qe]),void 0===$e)return;if(!1===$e)return void(window.location.href="/login");const et=e=>{ce(e)},tt=async()=>{let e=await L();t(e)};return(0,g.N9)(),(0,j.jsxs)(j.Fragment,{children:[(0,j.jsxs)(b.q,{children:[(0,j.jsx)("title",{children:"AI Pro | Manage Account"}),(0,j.jsx)("meta",{name:"description",content:"Take control of your account with ease through our My Account page. Review billing details, update contact information, and tailor your preferences."})]}),(0,j.jsx)(l.default,{auth:$e,setSubscription:Ve,setGetDateNow:Xe,setshowAddMoreMember:ve}),(0,j.jsx)("div",{className:"Manage bg-gray-100 min-h-[500px] flex items-center",children:(0,j.jsx)("div",{className:"container mx-auto py-10 px-4 sm:px-0",children:(0,j.jsxs)("div",{className:"max-w-6xl mx-auto pt-8 pb-8 sm:p-8",children:[(0,j.jsx)("h1",{className:"text-xl font-bold text-blue-600 my-6 lg:my-8",children:"Manage Your Account"}),(0,j.jsx)("div",{className:"",children:(0,j.jsxs)("ul",{className:"flex flex-wrap text-xs md:text-sm",children:[""===Ce?(0,j.jsx)(A,{tabName:"subscription",activeTab:de,onClick:et,children:"Subscription"}):"",me&&""===Ce?(0,j.jsx)(A,{tabName:"members",activeTab:de,onClick:et,children:"Members"}):"",""===Ce?(0,j.jsx)(A,{tabName:"order",activeTab:de,onClick:et,children:"Invoice"}):"",(0,j.jsx)(A,{tabName:"token-usage",activeTab:de,onClick:et,children:"Token Usage"}),(0,j.jsx)(A,{tabName:"account",activeTab:de,onClick:et,children:"Account"}),(0,j.jsx)(A,{tabName:"help",activeTab:de,onClick:et,children:"Help"}),(0,j.jsx)(A,{tabName:"logout",activeTab:de,onClick:et,children:"Logout"})]})}),(0,j.jsxs)("div",{className:"bg-white drop-shadow-sm p-6 rounded-tr-md rounded-br-md rounded-bl-md min-h-[400px] flex",children:["subscription"===de&&(0,j.jsx)(T,{setshowAddMoreMember:ve,setShowPausedAccountModal:De,setUserSubscriptionID:Re,setUserMerchant:Be,setUserAccountID:Ge,date_now:Je,user_subscription:qe,userPpg:Fe}),"members"===de&&(0,j.jsx)(U,{handleShowAddMember:()=>{const t=S-e.length;e.length>=S?e.length===Number(S)&&Ze?f().error("Member limit reached."):(He(!1),ve(!0)):e.length>0&&Ze?(ye(!0),e.length>S?He(!1):He(1!==t)):ye(!0)},enterpriseMembers:e,handleReloadMembers:tt,handleShowEditMemberModal:(e,t)=>{pe(!0),be(t)},currentPlan:Le,currentPlanName:Ee}),"order"===de&&(0,j.jsx)(E,{user_order:Qe}),"token-usage"===de&&(0,j.jsx)(I,{tokenUsage:s,tokenUsageTotal:u,userMaxToken:d,currentDate:ie,isShowEnterprise:me,currentPlan:Le,gpt4oTotalToken:p,gpt4oMaxTokenUsage:y,plan:$e.plan,dalleTotalImage:M,dalleMaxImage:D,claudeTotalToken:R,claudeMaxTokenUsage:Z,currentPlanName:Ee,fluxPrompt:J,fluxTotalPrompt:Q,membersTotalFluxPrompt:$,o1Prompt:te,o1TotalPrompt:re,membersTotalo1Prompt:le,deepseekTotalToken:W,deepseekMaxTokenUsage:q}),"account"===de&&(0,j.jsx)(C,{auth:$e}),"help"===de&&(0,j.jsx)(F,{}),"logout"===de&&(0,j.jsx)(_,{})]})]})})}),(0,j.jsx)(B,{showEditMember:xe,setShowEditEnterprise:pe,editMemberDetails:he,handleReloadMembers:tt}),(0,j.jsx)(z,{showAddMember:ge,setshowAddMember:ye,handleReloadMembers:tt,entMaxMembers:S,enterpriseMembers:e,isMax:Ze,isVisbleBtn:We,setIsVisibleBtn:He}),(0,j.jsx)(v.W,{showAddMoreMember:fe,setshowAddMoreMember:ve,setMoreToAddMember:ke,setMoreToAddMemberTotalAmount:Ae,setShowCompletePurchase:we}),(0,j.jsx)(v.U,{moreToAddMember:Ne,moreToAddMemberTotalAmount:Se,setShowCompletePurchase:we,showCompletePurchase:je}),(0,j.jsx)(w,{showPausedAccountModal:Pe,setShowPausedAccountModal:De,userSubscriptionID:Oe,userMerchant:Ue,userAccountID:ze}),(0,j.jsx)(n.default,{auth:$e})]})}},29540:(e,t,s)=>{s.d(t,{U:()=>h,W:()=>p});var r=s(72791),a=s(96347),l=s(31243),n=s(72608),i=s(56355),o=s(28891),d=s(95828),c=s.n(d),m=(s(92831),s(80184)),u="0",x="";function p(e){let{showAddMoreMember:t,setshowAddMoreMember:s,setMoreToAddMember:l,setMoreToAddMemberTotalAmount:n,setShowCompletePurchase:i}=e;const[d,x]=(0,r.useState)(1),[p,h]=(0,r.useState)(0),[b,g]=(0,r.useState)(""),y=(0,o.gx)();(0,r.useEffect)(()=>{void 0!==y&&"enterprise"===y.plan&&(u=y.price_per_member,"monthly"===y.interval.toLowerCase()?g("MONTH"):g("YEAR"))},[y]);const f=()=>{let e=document.getElementById("modalAddMoreMembers");null!==e&&(e.style.display="none",s(!1))};(0,r.useEffect)(()=>{c().options={positionClass:"toast-top-center"}},[]),(0,r.useEffect)(()=>{h(d*u)},[d]),(0,r.useEffect)(()=>{x(1),h(d*u)},[t]);return void 0!==t&&!0===t&&(()=>{let e=document.getElementById("modalAddMoreMembers");null!==e&&(e.style.display="block")})(),void 0!==t&&!1===t&&f(),(0,m.jsx)(m.Fragment,{children:(0,m.jsx)("div",{id:"modalAddMoreMembers",className:"modal z-[9999]",children:(0,m.jsx)("div",{class:"modal-content w-full md:w-[60%] max-w-[90%] p-3 md:p-4",children:(0,m.jsxs)("div",{children:[(0,m.jsxs)("div",{className:"mb-4 flex border-b pb-2",children:[(0,m.jsx)("div",{className:"text-blue-500 w-full md:w-2/3 mr-2 md:mr-5 font-bold",children:"Add More Members"}),(0,m.jsx)("div",{className:"float-right mt-[-5px] w-full md:w-1/3",children:(0,m.jsx)("span",{class:"close",onClick:()=>f(),children:"×"})})]}),(0,m.jsxs)("div",{className:"p-2 md:p-4 border rounded text-sm text-center",children:[(0,m.jsx)("div",{children:"Your enterprise account has hit its maximum user capacity."}),(0,m.jsx)("div",{children:"Add more members to your Enterprise Account."}),(0,m.jsxs)("div",{className:"py-4 text-center",children:[(0,m.jsx)("span",{className:"font-bold text-[12px] sm:text-sm inline px-1 sm:px-2",children:"Add"}),(0,m.jsxs)("div",{className:"border rounded px-2 py-4 inline",children:[(0,m.jsx)("input",{className:"bg-blue-500 rounded px-3 py-2 m-2 text-white cursor-pointer",type:"button",value:"-",onClick:()=>{d>1&&x(d-1)}}),(0,m.jsx)("span",{className:"text-blue-500 p-2 mx-auto font-bold",children:d}),(0,m.jsx)("input",{className:"bg-blue-500 rounded px-3 py-2 m-2 text-white cursor-pointer",type:"button",value:"+",onClick:()=>{x(d+1)}})]}),(0,m.jsx)("span",{className:"font-bold text-[12px] sm:text-sm inline px-1 sm:px-2",children:"Member/s"})]}),(0,m.jsxs)("div",{className:"font-bold text-sm",children:["Total Amount: $",p]}),(0,m.jsxs)("div",{className:"font-bold text-sm",children:["PER ",b]}),(0,m.jsx)("div",{children:(0,m.jsx)(a.E.button,{className:"bg-sky-600 w-full md:w-70 text-white font-bold my-4 py-2 px-6 rounded proceed-pmt",whileHover:{backgroundColor:"#49b1df"},whileTap:{scale:.9},onClick:()=>{window.location.href="/upgrade-ent/"+d},children:"UPGRADE NOW"})})]})]})})})})}function h(e){let{moreToAddMember:t,moreToAddMemberTotalAmount:s,setShowCompletePurchase:d,showCompletePurchase:u}=e;const p=(0,o.gx)();(0,r.useEffect)(()=>{void 0!==p&&"enterprise"===p.plan&&(x=p.user_id)},[p]);const h=()=>{let e=document.getElementById("modalComplete");null!==e&&(e.style.display="none",d(!1))};return void 0!==u&&!0===u&&(()=>{let e=document.getElementById("modalComplete");null!==e&&(e.style.display="block")})(),void 0!==u&&!1===u&&h(),(0,m.jsx)(m.Fragment,{children:(0,m.jsx)("div",{id:"modalComplete",className:"modal z-[9999]",children:(0,m.jsxs)("div",{class:"w-full md:w-[600px] border-[#888] md:mt-[15px] mx-[auto] bg-[#fefefe] p-6",children:[(0,m.jsx)("span",{class:"close",onClick:()=>h(),children:"×"}),(0,m.jsx)("div",{className:"border-b pb-[10px] border-[#d5d5d5]",children:(0,m.jsx)("img",{src:n.Z,alt:"AI-Pro Logo",className:"aiprologo mx-auto"})}),(0,m.jsxs)("h1",{className:"font-bold text-center p-2 text-gray-700 text-[20px] md:text-[24px]",children:["Payment Details",(0,m.jsx)("br",{}),"for Enterprise Order"]}),(0,m.jsx)("div",{className:"text-center text-[12px] md:text-[14px]",children:"Adding more than 10 Enterprise users requires prior payment."}),(0,m.jsx)("div",{className:"text-center text-[12px] md:text-[14px]",children:"Please use the provided payment details below to settle your Enterprise account."}),(0,m.jsxs)("div",{className:"py-2",children:[(0,m.jsxs)("div",{className:"font-bold text-[11px]",children:["No. of Members: ",t]}),(0,m.jsxs)("div",{className:"font-bold text-[11px]",children:["Enterprise - Total: $",s]})]}),(0,m.jsxs)("div",{className:"border rounded p-2 text-[12px] md:text-[14px] leading-7 my-2",children:[(0,m.jsx)("div",{className:"font-bold",children:"Bank Information"}),(0,m.jsx)("div",{className:"float-right text-blue-400 font-bold cursor-pointer mt-[-28px] text-[12px]",onClick:()=>{return e=t,r=s,h(),document.querySelector(".loader-container").classList.add("active"),void l.Z.post("http://localhost:9002/api/t/send-enterprise-payment-info",{members:e,total_amount:r},{headers:{"content-type":"application/x-www-form-urlencoded"}}).then(function(e){let t=e.data;document.querySelector(".loader-container").classList.remove("active"),t.success?c().success("Email sent to "+t.data):c().error("Email Failed.")}).catch(function(e){document.querySelector(".loader-container").classList.remove("active"),e.response&&429===e.response.status&&(document.querySelector(".loader-container").classList.remove("active"),c().error("Sorry, too many requests. Please try again in a bit!"))});var e,r},children:"Send via Email"}),(0,m.jsxs)("div",{children:[(0,m.jsx)("span",{className:"font-bold",children:"Beneficiary:"})," TELECOM BUSINESS SOLUTIONS INC."]}),(0,m.jsxs)("div",{children:[(0,m.jsx)("span",{className:"font-bold",children:"SWIFT:"})," BOFAUS3N"]}),(0,m.jsxs)("div",{children:[(0,m.jsx)("span",{className:"font-bold",children:"Bank Name:"})," Bank of America"]}),(0,m.jsxs)("div",{children:[(0,m.jsx)("span",{className:"font-bold",children:"Routing (Wire):"})," *********"]}),(0,m.jsxs)("div",{children:[(0,m.jsx)("span",{className:"font-bold",children:"Routing Number (Paper & Electronic):"})," *********"]}),(0,m.jsxs)("div",{children:[(0,m.jsx)("span",{className:"font-bold",children:"Account Number:"})," 3810-6766-2647"]}),(0,m.jsxs)("div",{children:[(0,m.jsx)("span",{className:"font-bold",children:"Customer Number:"})," ",x]}),(0,m.jsxs)("div",{className:"bg-[#dddddd] px-4 py-2 rounded text-center mt-4",children:[(0,m.jsx)(i.DAO,{className:"inline text-lg mr-2"}),"Customer Number must be included in the bank transfer description field for your funds to transfer successfully."]})]}),(0,m.jsx)("div",{className:"text-center text-[12px] md:text-[14px] mt-4",children:"Once the payment is received, our dedicated account manager will contact you to assist in the seamless setup of your Enterprise account."}),(0,m.jsxs)("div",{className:"text-center text-[12px] md:text-[14px]",children:["Please allow ",(0,m.jsx)("b",{children:"2-3 banking days"})," for the payment to reflect in the account."]}),(0,m.jsx)("div",{className:"text-center",children:(0,m.jsx)(a.E.button,{className:"bg-blue-500 text-white font-bold py-3 px-4 rounded my-4 proceed-pmt",whileHover:{backgroundColor:"#5997fd"},whileTap:{scale:.9},onClick:()=>{window.location.href="/payment-reference"},children:"Send Payment Confirmation"})})]})})})}},59896:e=>{e.exports="data:image/png;base64,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"}}]);
//# sourceMappingURL=9915.7eec60fc.chunk.js.map