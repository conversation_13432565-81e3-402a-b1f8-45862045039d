"use strict";(self.webpackChunkv1=self.webpackChunkv1||[]).push([[748,9044],{52508:(e,t,n)=>{n.r(t),n.d(t,{default:()=>d});var o=n(72791),a=(n(46904),n(96347)),c=n(10728),i=n(74335),s=n(28891),r=n(31243),l=n(80184);const d=function(){(0,s.gx)();const[e]=(0,o.useState)(window.user_data),[t,n]=(0,o.useState)(""),[d,u]=(0,o.useState)(""),[p,h]=(0,o.useState)(!0);(0,o.useEffect)(()=>{const e=(0,i.bG)("flow");let t=window.location.href,n="";if(t.indexOf("staging")>-1?n="staging.":t.indexOf("dev")>-1&&(n="dev."),"chatpdf"===e){const e=setTimeout(()=>{window.location="https://"+n+"chatpdf.ai-pro.org/"},3e3);return()=>clearTimeout(e)}if("landing"===e){const e=setTimeout(()=>{window.location="https://"+n+"chatpro.ai-pro.org/"},8e3);return()=>clearTimeout(e)}},[]),(0,o.useEffect)(()=>{e.email?(window.qp=function(){console.log("qp function")},window.qp("track","Purchase"),window.twq?window.twq("event","tw-oebtr-oebtv",{value:"Thank You Page (echo)",contents:[{content_id:`${e.user_pid}`,content_type:`${e.plan}`,content_name:`${e.email}`}]}):console.error("Twitter script not loaded")):window.location.href="/login"},[e]),(0,o.useEffect)(()=>{const e=(0,i.bG)("avatarmaker"),t=(0,i.bG)("appurl");t&&e&&t.includes("avatar")&&r.Z.post("http://localhost:9002/api/get-avatar",{folder:e},{headers:{"content-type":"application/x-www-form-urlencoded"}}).then(e=>{const t=e.data;if(t.success){const e=t.link;""!==e&&fetch(e).then(e=>e.blob()).then(e=>{const t=URL.createObjectURL(e),n=document.createElement("a");n.href=t,n.setAttribute("download","highresimage.png"),document.body.appendChild(n),n.click(),document.body.removeChild(n)})}(0,i.Sz)("avatarmaker",{domain:".ai-pro.org",path:"/"})})},[]);const m=(0,i.bG)("appurl")?(0,i.bG)("appurl"):"",w=(0,i.bG)("unpFlow")?(0,i.bG)("unpFlow"):"";d||"1"!==w?d||(n("Continue to your Account"),u("/my-account")):m?(m.includes("http:")||m.includes("https:")?u(m):u("https://"+m),h(!1),n("Continue")):(n("Continue to your Account"),u("/my-account"));const f=e.email;return(0,o.useEffect)(()=>{window.ttq?(window.ttq.identify({email:`${f}`}),window.ttq.track("Payment Page",{contents:[{content_id:`${e.user_pid}`,content_type:`${e.plan}`,content_name:`${e.email}`}],value:`${e.plan_id}`,currency:`${e.currency.toUpperCase()}`,description:"Thank you Page (echo)"})):console.error("Tiktok script not loaded")},[f,e.user_pid,e.plan,e.email,e.plan_id,e.currency]),(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(c.default,{auth:e}),(0,l.jsx)("div",{className:"Thankyou bg-gray-100 mt-[100px] md:mt-[50px] md:min-h-[85vh] flex justify-center items-center",children:(0,l.jsx)("div",{className:"container mx-auto py-10 px-4 sm:px-0",children:(0,l.jsxs)("div",{className:"reg_col text-center mb-8",children:[(0,l.jsxs)("h1",{className:"text-2xl lg:text-3xl font-bold text-center mb-6 lg:mb-8",children:[(0,l.jsx)("span",{className:"text-gradient font-black",children:"Thank you"})," for subscribing."]}),(0,l.jsx)("div",{className:"overflow-hidden",children:(0,l.jsx)("div",{className:"px-6 py-10",children:(0,l.jsxs)("div",{className:"relative block",children:["You now have access to our entire library of content, dedicated to AI learning.",(0,l.jsx)("br",{}),p?"Click below to continue to your main account page.":"Click below to continue.",(0,l.jsx)("input",{type:"hidden",id:"tyemail",value:f})]})})}),(0,l.jsx)(a.E.a,{className:"bg-blue-500 text-white font-bold py-3 px-6 rounded-lg",whileHover:{scale:1.1,backgroundColor:"#5997fd"},whileTap:{scale:.9},href:d,children:t})]})})})]})}},46904:()=>{}}]);
//# sourceMappingURL=748.c9293672.chunk.js.map