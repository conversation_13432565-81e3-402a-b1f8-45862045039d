{"version": 3, "file": "static/js/7780.0f67936c.chunk.js", "mappings": "wRAwKA,QA3JA,WACE,MAAMA,GAAOC,EAAAA,EAAAA,IAAK,mBACXC,IAAaC,EAAAA,EAAAA,UAASC,OAAOF,UAAYE,OAAOF,UAAY,CAAC,IAE7DG,IAAQF,EAAAA,EAAAA,UAASD,EAAUI,KAAOJ,EAAUI,KAAO,OACnDC,IAAWJ,EAAAA,EAAAA,UAASD,EAAUK,QAAUL,EAAUK,QAAU,OAC5DC,EAAuBC,IAA4BN,EAAAA,EAAAA,UAAS,IAC5DO,EAAiBC,IAAsBR,EAAAA,EAAAA,UAAS,KAChDS,EAAgBC,IAAqBV,EAAAA,EAAAA,UAAS,GA+BrD,IA7BAW,EAAAA,EAAAA,WAAU,UACKC,IAATf,GAAkC,eAAZA,EAAKM,OAC7BO,EAAkBb,EAAKgB,kBACW,YAA9BhB,EAAKiB,SAASC,cAChBP,EAAmB,WAEnBA,EAAmB,YAGtB,CAACX,KAEJc,EAAAA,EAAAA,WAAU,KACRL,EAAyBF,EAAQK,IAChC,CAACL,EAAQK,KAUZE,EAAAA,EAAAA,WAAU,KACRK,IAAAA,QAAiB,CACfC,cAAe,qBAEhB,SAESL,IAATf,IAA+B,IAATA,EAAgB,OAEzC,MAAMqB,GAAKC,EAAAA,EAAAA,IAAU,UAmCrB,OACEC,EAAAA,EAAAA,MAAAC,EAAAA,SAAA,CAAAC,SAAA,EACEC,EAAAA,EAAAA,KAACC,EAAAA,QAAM,CAAC3B,KAAMA,IACZK,GACFqB,EAAAA,EAAAA,KAAA,OAAKE,UAAU,mDAAkDH,UAE7DC,EAAAA,EAAAA,KAAA,OAAKE,UAAU,0BAAyBH,UACtCC,EAAAA,EAAAA,KAAA,OAAKE,UAAU,4CAA2CH,UACxDC,EAAAA,EAAAA,KAAA,OAAKE,UAAU,oDAAmDH,UAChEF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,+BAA8BH,SAAA,EAC3CC,EAAAA,EAAAA,KAAA,MAAIE,UAAU,+BAA8BH,SAAC,mBAC7CF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EAEEC,EAAAA,EAAAA,KAAA,OAAKE,UAAU,QAAOH,UACpBC,EAAAA,EAAAA,KAAA,OAAKE,UAAU,2BAA0BH,UAACC,EAAAA,EAAAA,KAAA,KAAGE,UAAU,yBAAwBH,SAAC,yBAGlFF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,cAAaH,SAAA,EAC1BC,EAAAA,EAAAA,KAAA,OAAKE,UAAU,+BAA8BH,UAACC,EAAAA,EAAAA,KAAA,KAAAD,SAAG,qCACjDC,EAAAA,EAAAA,KAAA,OAAKE,UAAU,6BAA4BH,SAAElB,QAG/CgB,EAAAA,EAAAA,MAAA,OAAKK,UAAU,cAAaH,SAAA,EAC1BC,EAAAA,EAAAA,KAAA,OAAKE,UAAU,+BAA8BH,UAACC,EAAAA,EAAAA,KAAA,KAAAD,SAAG,qBACjDF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,6BAA4BH,SAAA,CAAC,IAAEjB,SAGhDkB,EAAAA,EAAAA,KAAA,OAAKE,UAAU,QAAOH,UACpBF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,2BAA0BH,SAAA,CAAC,gCAA8Bf,EAAgB,iCAI5FgB,EAAAA,EAAAA,KAAA,OAAKE,UAAU,OAAMH,UACnBC,EAAAA,EAAAA,KAAA,OAAKE,UAAU,sBAAqBH,UAChCC,EAAAA,EAAAA,KAACG,EAAAA,EAAOC,OAAM,CACVF,UAAU,gFACVG,WAAY,CAAEC,gBAAiB,WAC/BC,SAAU,CAAEC,MAAO,IACnBC,QA1CJ,WA5BpBC,SAASC,cAAc,qBAAqBC,UAAUC,IAAI,UAC1DC,EAAAA,EAAMC,KAAK,oDAA4D,CACrEpB,KACAd,WACC,CAAEmC,QAAS,CAAE,eAAgB,uCAAyCC,KAAK,SAASC,GACrF,IAAIC,EAASD,EAAIvC,KACjB,GAAGwC,EAAOC,QASR,OARA3B,IAAAA,QAAe,WAEff,OAAO2C,SAASC,MAAM,sBAAuB,CAC3CC,mBAAoB1C,IAGtBH,OAAO8C,SAASC,KAAO,iBACvBf,SAASC,cAAc,qBAAqBC,UAAUc,OAAO,UAG1DP,EAAOxC,OACLwC,EAAOxC,KAAKgD,QACblC,IAAAA,MAAa0B,EAAOxC,KAAKgD,SAEzBlC,IAAAA,MAAa0B,EAAOxC,KAAKiD,KAIjC,EAKF,EAwCiD7B,SAC1B,2BAKPF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,kDAAiDH,SAAA,EAC9DF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,gCAA+BH,SAAA,EAACC,EAAAA,EAAAA,KAAC6B,EAAAA,IAAM,CAAC3B,UAAU,gDAA+C,uBAChHL,EAAAA,EAAAA,MAAA,OAAKK,UAAU,2EAA0EH,SAAA,EACvFC,EAAAA,EAAAA,KAAA,OAAK8B,IAAKC,EAAUC,IAAI,cAAc9B,UAAU,mBAChDL,EAAAA,EAAAA,MAAA,OAAKK,UAAU,6CAA4CH,SAAA,EACzDC,EAAAA,EAAAA,KAAA,OAAK8B,IAAKG,EAAQD,IAAI,YAAY9B,UAAU,yCAC5CF,EAAAA,EAAAA,KAAA,UACES,QAASA,KACP/B,OAAOwD,KACL,4FACA,eACA,6HAGJC,MAAM,0CACNjC,UAAU,OAAMH,UACdC,EAAAA,EAAAA,KAAA,OAAKoC,QAAQ,OACXN,IAAI,wGACJE,IAAI,+CACJ9B,UAAU,aACVmC,MAAM,KACNC,OAAO,6BAStB,KAGf,C", "sources": ["pay-upgrade/index_ent.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { motion } from \"framer-motion\";\r\nimport './style.css';\r\nimport Header from '../header/headerlogo';\r\nimport { FaLock } from 'react-icons/fa';\r\nimport ccImages from '../assets/images/cc_v2.png';\r\nimport ccAuth from '../assets/images/secure90x72.gif';\r\nimport { Auth } from '../core/utils/auth';\r\nimport { GetCookie } from '../core/utils/cookies';\r\nimport axios from 'axios';\r\nimport toastr from 'toastr';\r\nimport 'toastr/build/toastr.min.css';\r\n\r\nfunction Payment() {\r\n  const auth = Auth('/register-auth');\r\n  const [view_data] = useState(window.view_data ? window.view_data : {});\r\n  // const [isTrial] = useState(view_data.isUserTrial ? view_data.isUserTrial === 1 ? true : false : false);\r\n  const [data] = useState(view_data.plan ? view_data.plan : null);\r\n  const [members] = useState(view_data.members ? view_data.members : null);\r\n  const [totalEnterpriseAmount, settotalEnterpriseAmount] = useState(0);\r\n  const [paymentInterval, setpaymentInterval] = useState(\"\");\r\n  const [pricePerMember, setpricePerMember] = useState(0);\r\n\r\n  useEffect(() => {\r\n    if (auth !== undefined && auth.plan==='enterprise'){\r\n      setpricePerMember(auth.price_per_member) \r\n      if (auth.interval.toLowerCase()===\"monthly\"){\r\n        setpaymentInterval(\"monthly\");        \r\n      }else{\r\n        setpaymentInterval(\"yearly\");       \r\n      }\r\n    }\r\n  }, [auth]);\r\n\r\n  useEffect(() => {\r\n    settotalEnterpriseAmount(members*pricePerMember);\r\n  }, [members,pricePerMember]);\r\n\r\n\r\n  // useEffect(() => {\r\n  //   alert(members);\r\n  //   alert(pricePerMember);\r\n\r\n  //   settotalEnterpriseAmount(members*pricePerMember);\r\n  // }, [members]);\r\n\r\n  useEffect(() => {\r\n    toastr.options = {\r\n      positionClass: 'toast-top-center'\r\n    };\r\n  }, []);\r\n\r\n  if(auth === undefined || auth === false) return;\r\n \r\n  const tk = GetCookie(\"access\");\r\n\r\n  const submitPayment = () => {\r\n    document.querySelector(\".loader-container\").classList.add('active');\r\n    axios.post(`${process.env.REACT_APP_API_URL}/update-subscription-ent`, {\r\n      tk,\r\n      members\r\n    }, { headers: { 'content-type': 'application/x-www-form-urlencoded' } }).then(function(res) {\r\n      let output = res.data;\r\n      if(output.success) {\r\n        toastr.success(\"Success\");\r\n\r\n        window.mixpanel.track(\"update_subscription\", {\r\n          additional_members: members,\r\n        });\r\n        \r\n        window.location.href = '/thankyou';\r\n        document.querySelector(\".loader-container\").classList.remove('active');\r\n        return;\r\n    }else{\r\n        if(output.data) {\r\n          if(output.data.message) {\r\n            toastr.error(output.data.message);\r\n          } else {\r\n            toastr.error(output.data.msg);\r\n          }\r\n        }\r\n      }\r\n    });\r\n  }\r\n  \r\n  const submitHandler = function(){\r\n    submitPayment();\r\n  }\r\n\r\n  return (\r\n    <>\r\n      <Header auth={auth} />\r\n      { data ?\r\n      <div className=\"Payment-upgrade bg-gray-100 md:min-h-[90vh] flex\">\r\n\r\n          <div className=\"container mx-auto py-10\">\r\n            <div className=\"flex flex-col items-center py-10 lg:py-16\">\r\n              <div className=\"flex flex-wrap md:flex-wrap justify-center w-full\">\r\n                <div className=\"pay_right px-4 mb-8 md:w-2/5\">\r\n                  <h2 className=\"text-xl font-bold mb-4 py-10\">Order Summary</h2>\r\n                  <div>\r\n\r\n                    <div className='block'>\r\n                      <div className=\"mb-2 w-full text-sm mr-4\"><b className=\"text-md text-uppercase\">ENTERPRISE PLAN</b></div>\r\n                    </div>\r\n\r\n                    <div className='flex w-full'>\r\n                      <div className=\"mb-2 w-4/5 text-sm mr-4 left\"><b>Total number of member added:</b></div>\r\n                      <div className=\"font-bold w-1/5 text-right\">{members}</div>\r\n                    </div>\r\n\r\n                    <div className='flex w-full'>\r\n                      <div className=\"mb-2 w-4/5 text-sm mr-4 left\"><b>Total amount:</b></div>\r\n                      <div className=\"font-bold w-1/5 text-right\">${totalEnterpriseAmount}</div>\r\n                    </div>\r\n\r\n                    <div className='block'>\r\n                      <div className=\"mb-5 w-full text-sm mr-4\">Your subscription will renew {paymentInterval} until you cancel it.</div>\r\n                    </div>\r\n  \r\n                  </div>\r\n                  <div className=\"flex\">\r\n                    <div className=\"mb-2 w-full text-sm\">\r\n                        <motion.button\r\n                            className=\"bg-blue-500 text-white font-bold py-3 px-6 rounded-lg w-full my-4 proceed-pmt\"\r\n                            whileHover={{ backgroundColor: \"#5997fd\" }}\r\n                            whileTap={{ scale: 0.9 }}\r\n                            onClick={submitHandler}\r\n                        >\r\n                            Complete Purchase\r\n                        </motion.button>\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"securecont border-t-2 border-gray-300 flex py-5\">\r\n                    <div className=\"securetext mb-2 text-sm w-1/2\"><FaLock className=\"inline text-lg mr-1 text-orange-500 text-xs\"/> Secure Checkout</div>\r\n                    <div className=\"securelogo mb-2 text-sm w-1/2 flex flex-wrap justify-center items-center\">\r\n                      <img src={ccImages} alt=\"Secure Logo\" className=\"cclogo inline\"/>\r\n                      <div className=\"flex items-center justify-center flex-wrap\">\r\n                        <img src={ccAuth} alt=\"Authorize\" className=\"text-center md:text-right py-2 w-20\"/>\r\n                        <button\r\n                          onClick={() => {\r\n                            window.open(\r\n                              '//www.securitymetrics.com/site_certificate?id=1982469&tk=d41c416c6208f1136426bc8038178d2e',\r\n                              'Verification',\r\n                              'location=no, toolbar=no, resizable=no, scrollbars=yes, directories=no, status=no,top=100,left=100, width=700, height=600'\r\n                            );\r\n                          }}\r\n                          title=\"SecurityMetrics card safe certification\"\r\n                          className=\"h-20\" >\r\n                            <img loading=\"lazy\"\r\n                              src=\"https://www.securitymetrics.com/portal/app/ngsm/assets/img/GreyContent_Credit_Card_Safe_White_Sqr.png\"\r\n                              alt=\"SecurityMetrics card safe certification logo\"\r\n                              className=\"max-h-full\"\r\n                              width=\"80\"\r\n                              height=\"80\" />\r\n                        </button>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n      </div> : \"\" }\r\n    </>\r\n  );\r\n}\r\n\r\nexport default Payment;"], "names": ["auth", "<PERSON><PERSON>", "view_data", "useState", "window", "data", "plan", "members", "totalEnterpriseAmount", "settotalEnterpriseAmount", "paymentInterval", "setpaymentInterval", "pricePerMember", "setpricePerMember", "useEffect", "undefined", "price_per_member", "interval", "toLowerCase", "toastr", "positionClass", "tk", "Get<PERSON><PERSON><PERSON>", "_jsxs", "_Fragment", "children", "_jsx", "Header", "className", "motion", "button", "whileHover", "backgroundColor", "whileTap", "scale", "onClick", "document", "querySelector", "classList", "add", "axios", "post", "headers", "then", "res", "output", "success", "mixpanel", "track", "additional_members", "location", "href", "remove", "message", "msg", "FaLock", "src", "ccImages", "alt", "ccAuth", "open", "title", "loading", "width", "height"], "sourceRoot": ""}