{"version": 3, "file": "static/js/834.bb7c385d.chunk.js", "mappings": "qJA0BA,QAxBA,WAGE,MAAMA,EAAWC,yBAWjB,OAVAC,EAAAA,EAAAA,WAAU,KACR,MAAMC,EAASC,SAASC,cAAc,UACtCF,EAAOG,IAAMN,EAAW,iDACxBG,EAAOI,OAAQ,EACfJ,EAAOK,OAAS,OAIhBJ,SAASK,KAAKC,YAAYP,IACzB,CAACH,KAEFW,EAAAA,EAAAA,KAAAC,EAAAA,SAAA,CAAAC,UACEF,EAAAA,EAAAA,KAAA,UAAQG,UAAW,iHAMzB,C,oJCsOA,QArPA,WACE,MAAOC,EAAOC,IAAYC,EAAAA,EAAAA,UAAS,KAC5BC,EAAYC,IAAiBF,EAAAA,EAAAA,UAAS,IAEvCG,GAAeC,EAAAA,EAAAA,IAAU,iBAAmB,MAC5CC,GAAgBC,EAAAA,EAAAA,QAAO,IACvBC,GAAUD,EAAAA,EAAAA,QAAO,MAEjBE,GAAiBC,EAAAA,EAAAA,aAAY,KACZ,OAAjBN,GACFE,EAAcK,QAAQC,QAAQ,CAACC,EAAKC,KAClC,GAAID,EAAK,CAAC,IAADE,EACP,MAAMC,EAASH,EAAII,YACbC,EAAaL,EAAIM,aACjBC,EAAWJ,EACE,IAAfE,EACEA,GACiB,QAAjBH,EAACF,EAAIQ,oBAAY,IAAAN,EAAAA,EAAI,GAAK,GAC5B,EAEJO,WAAW,KACTT,EAAIU,MAAMH,OAAS,GAAGA,OACrB,IACL,KAGH,CAAChB,IAEJkB,WAAW,KACT,MAAME,EAAOhB,EAAQG,QACfc,EACJC,UAAUC,UAAUC,SAAS,WAC7BF,UAAUC,UAAUC,SAAS,OAE/B,GAAIJ,GAAQC,EAAY,CACtB,MAAMI,EAASL,EAAKM,iBAAiB,SAEjCD,GAAUA,EAAOE,OAAS,GAC5BF,EAAOjB,QAASoB,IACVA,EAAMC,QAAQ,iCAChBD,EAAME,UAAUC,IAAI,eAI5B,GACC,MAEHjD,EAAAA,EAAAA,WAAU,KACR,GAAqB,OAAjBkB,EAIF,OAHAK,IACA2B,OAAOC,iBAAiB,SAAU5B,GAE3B,KACL2B,OAAOE,oBAAoB,SAAU7B,KAGxC,CAACL,EAAcK,KAElBvB,EAAAA,EAAAA,WAAU,KACa,OAAjBkB,GACFK,KAED,CAACP,EAAYE,EAAcK,KAE9BvB,EAAAA,EAAAA,WAAU,KACRqD,IAAAA,QAAiB,CACfC,cAAe,qBAEhB,IAEH,MAAMC,EAAgBA,KACpB,IAAIC,GAAW,EASf,OARK3C,EAEO,eAAe4C,KAAK5C,IAG9BI,EAAc,IACduC,GAAW,GAHXvC,EAAc,wBAFdA,EAAc,qBAOTuC,GAGHE,EAAsBA,KACHH,MAGvBrD,SAASyD,cAAc,qBAAqBX,UAAUC,IAAI,UAC1DW,EAAAA,EACGC,KACC,4CACA,CACEhD,SAEF,CAAEiD,QAAS,CAAE,eAAgB,uCAE9BC,KAAK,SAAUC,GACd,IAAIC,EAASD,EAAIE,KACjB,GAAID,EAAOE,QAKT,OAJAd,IAAAA,QAAe,0CACfjB,WAAW,WACTc,OAAOkB,SAASC,QAClB,EAAG,KAGLnE,SAASyD,cAAc,qBAAqBX,UAAUsB,OAAO,UACzDL,EAAOC,KAAMb,IAAAA,MAAaY,EAAOC,KAAKK,KACrClB,IAAAA,MAAa,wBACpB,KAGEmB,EAAeA,CAACC,EAAOC,KAC3B,MAAMC,EAAKF,EAAMG,OACXC,EAAQF,EAAGE,MACXC,EAAyB,UAAfL,EAAMM,KAChBC,EAAyB,WAAfP,EAAMM,MAAoC,UAAfN,EAAMM,KAQjD,GANMF,GAASC,GAAWE,EACxBL,EAAG3B,UAAUC,IAAI,cAEjB0B,EAAG3B,UAAUsB,OAAO,cAGD,OAAjBpD,GAA2C,OAAjBA,GAAyB8D,EACrD,GACO,UADCN,EAEJzD,EAAc,IACdH,EAAS+D,QAGa,KAAlBJ,EAAMQ,SACRvB,KAOV,OACEwB,EAAAA,EAAAA,MAAAxE,EAAAA,SAAA,CAAAC,SAAA,EAEEF,EAAAA,EAAAA,KAAA,OAAKG,UAAU,+EAA8ED,UAC3FF,EAAAA,EAAAA,KAAA,OAAKG,UAAU,oDAAmDD,UAChEuE,EAAAA,EAAAA,MAAA,OAAKtE,UAAU,2BAA0BD,SAAA,EACvCF,EAAAA,EAAAA,KAAA,MAAIG,UAAU,0DAAyDD,SAAC,sBAGxEF,EAAAA,EAAAA,KAAA,OACEG,UAAU,gDACVe,IAAKL,EAAQX,UAEbuE,EAAAA,EAAAA,MAAA,OAAKtE,UAAU,wBAAuBD,SAAA,CAClB,OAAjBO,GACCgE,EAAAA,EAAAA,MAAAxE,EAAAA,SAAA,CAAAC,SAAA,EACEuE,EAAAA,EAAAA,MAAA,OAAKtE,UAAU,2CAA0CD,SAAA,EACvDF,EAAAA,EAAAA,KAAA,SACEG,UAAU,wNACVuE,YAAY,UACZJ,KAAK,QACLK,KAAK,QACLC,OAASC,GAAMd,EAAac,EAAG,SAC/BC,SAAWD,GAAMd,EAAac,EAAG,SACjCE,QAAUF,GAAMd,EAAac,EAAG,SAChCG,QAASjB,KAEX/D,EAAAA,EAAAA,KAAA,SACEG,UAAU,iMACV8E,IAAI,QAAO/E,SACZ,cAIHF,EAAAA,EAAAA,KAAA,QACEG,UAAU,6GACVe,IAAMgD,GAAQvD,EAAcK,QAAQ,GAAKkD,EAAIhE,SAE5CK,QAILkE,EAAAA,EAAAA,MAAA,SAAOtE,UAAU,iBAAgBD,SAAA,CAC9BK,IACCP,EAAAA,EAAAA,KAAA,QAAMG,UAAU,6CAA4CD,SACzDK,KAGLP,EAAAA,EAAAA,KAAA,SACEG,UAAU,0NACVuE,YAAY,kBACZ,aAAW,gBACXJ,KAAK,QACLK,KAAK,QACLK,QAAUH,IACRxE,EAASwE,EAAEV,OAAOC,OACA,KAAdS,EAAEL,SAAgBvB,KAGxBiC,UAAQ,QAIdlF,EAAAA,EAAAA,KAACmF,EAAAA,EAAOC,OAAM,CACZjF,UAAU,oEACVkF,WAAY,CAAEC,MAAO,KACrBC,SAAU,CAAED,MAAO,IACnBE,QAASA,KACU,OAAjB/E,GAAyBqC,IACzBG,KAEF,aAAW,iBAAgB/C,SAC5B,oBAGDF,EAAAA,EAAAA,KAACmF,EAAAA,EAAOC,OAAM,CACZjF,UAAU,oDACVkF,WAAY,CAAEI,gBAAiB,QAC/BF,SAAU,CAAED,MAAO,IACnB,aAAW,gBACXE,QAASA,KACP/C,OAAOkB,SAAS+B,KAAO,kBACvBxF,SACH,cAGDF,EAAAA,EAAAA,KAACmF,EAAAA,EAAOC,OAAM,CACZI,QAASA,KACP/C,OAAOkB,SAAS+B,KAAO,UAEzBvF,UAAU,oDACVkF,WAAY,CAAEI,gBAAiB,QAC/BF,SAAU,CAAED,MAAO,IACnB,aAAW,gBAAepF,SAC3B,uBAQXF,EAAAA,EAAAA,KAAC2F,EAAAA,QAAM,MAGb,C", "sources": ["footer/index.jsx", "forgot/index.jsx"], "sourcesContent": ["import React, { useEffect } from 'react';\r\n\r\nfunction Powered() {\r\n  // const [isHidden, setIsHidden] = useState(false);\r\n\r\n  const base_url = process.env.REACT_APP_BASE_URL;\r\n  useEffect(() => {\r\n    const script = document.createElement('script');\r\n    script.src = base_url + 'snippets/com.global.vuzo/js/com.global.vuzo.js';\r\n    script.async = true;\r\n    script.onload = () => {\r\n      // window.displayGooglePlay();\r\n      // window.displayQR();\r\n    };\r\n    document.body.appendChild(script);\r\n  }, [base_url]);\r\n  return (\r\n    <>\r\n      <footer className={`relative hidden md:block md:absolute bottom-[10px] right-[5px] w-full mt-3 py-12 md:py-2 z-[8888] md:hidden`}>\r\n      {/* <footer className=\"relative hidden md:block md:absolute bottom-[10px] right-[5px] w-full mt-3 py-12 md:py-2 z-[8888]\"> */}\r\n\r\n      </footer>\r\n    </>\r\n  );\r\n}\r\n\r\nexport default Powered;\r\n", "import React, { useState, useEffect, useRef, useCallback } from \"react\";\r\nimport \"./style.css\";\r\nimport { motion } from \"framer-motion\";\r\nimport Footer from \"../footer\";\r\nimport axios from \"axios\";\r\nimport { GetCookie } from \"../core/utils/cookies\";\r\nimport toastr from 'toastr';\r\nimport 'toastr/build/toastr.min.css';\r\n\r\nfunction Forgotpass() {\r\n  const [email, setEmail] = useState(\"\");\r\n  const [emailError, setEmailError] = useState(\"\");\r\n  // const renderHeader = !GetSubdomain();\r\n  const smooth_login = GetCookie(\"smooth_login\") || \"off\";\r\n  const spanErrorRefs = useRef([]);\r\n  const formRef = useRef(null);\r\n\r\n  const setSpanHeights = useCallback(() => {\r\n    if (smooth_login === \"on\") {\r\n      spanErrorRefs.current.forEach((ref, i) => {\r\n        if (ref) {\r\n          const errMsg = ref.textContent;\r\n          const currHeight = ref.offsetHeight;\r\n          const height = !!errMsg\r\n            ? currHeight !== 0\r\n              ? currHeight\r\n              : (ref.scrollHeight ?? 0) + 12\r\n            : 0;\r\n\r\n          setTimeout(() => {\r\n            ref.style.height = `${height}px`;\r\n          }, 100);\r\n        }\r\n      });\r\n    }\r\n  }, [smooth_login]);\r\n\r\n  setTimeout(() => {\r\n    const form = formRef.current;\r\n    const isChromium =\r\n      navigator.userAgent.includes(\"Chrome\") ||\r\n      navigator.userAgent.includes(\"Edg\");\r\n\r\n    if (form && isChromium) {\r\n      const inputs = form.querySelectorAll(\"input\");\r\n\r\n      if (inputs && inputs.length > 0) {\r\n        inputs.forEach((input) => {\r\n          if (input.matches(\":-internal-autofill-selected\")) {\r\n            input.classList.add(\"autofilled\");\r\n          }\r\n        });\r\n      }\r\n    }\r\n  }, 200);\r\n\r\n  useEffect(() => {\r\n    if (smooth_login === \"on\") {\r\n      setSpanHeights();\r\n      window.addEventListener(\"resize\", setSpanHeights);\r\n\r\n      return () => {\r\n        window.removeEventListener(\"resize\", setSpanHeights);\r\n      };\r\n    }\r\n  }, [smooth_login, setSpanHeights]);\r\n\r\n  useEffect(() => {\r\n    if (smooth_login === \"on\") {\r\n      setSpanHeights();\r\n    }\r\n  }, [emailError, smooth_login, setSpanHeights]);\r\n\r\n  useEffect(() => {\r\n    toastr.options = {\r\n      positionClass: 'toast-top-center'\r\n    };\r\n  }, []);\r\n\r\n  const validateEmail = () => {\r\n    let isPassed = false;\r\n    if (!email) {\r\n      setEmailError(\"Email is required\");\r\n    } else if (!/\\S+@\\S+\\.\\S+/.test(email)) {\r\n      setEmailError(\"Invalid email format\");\r\n    } else {\r\n      setEmailError(\"\");\r\n      isPassed = true;\r\n    }\r\n    return isPassed;\r\n  };\r\n\r\n  const handleResetPassword = () => {\r\n    let isPassedValidate = validateEmail();\r\n    if (!isPassedValidate) return;\r\n\r\n    document.querySelector(\".loader-container\").classList.add(\"active\");\r\n    axios\r\n      .post(\r\n        `${process.env.REACT_APP_API_URL}/forgot-password`,\r\n        {\r\n          email,\r\n        },\r\n        { headers: { \"content-type\": \"application/x-www-form-urlencoded\" } }\r\n      )\r\n      .then(function (res) {\r\n        let output = res.data;\r\n        if (output.success) {\r\n          toastr.success(\"Success. Please check your email.\");\r\n          setTimeout(function () {\r\n            window.location.reload();\r\n          }, 3000);\r\n          return;\r\n        }\r\n        document.querySelector(\".loader-container\").classList.remove(\"active\");\r\n        if (output.data) toastr.error(output.data.msg);\r\n        else toastr.error(\"Invalid email address\");\r\n      });\r\n  };\r\n\r\n  const handleChange = (event, inputType) => {\r\n    const el = event.target;\r\n    const value = el.value;\r\n    const isFocus = event.type === \"focus\";\r\n    const isInput = event.type === \"change\" || event.type === \"keyup\";\r\n\r\n    if (!!value || isFocus || isInput) {\r\n      el.classList.add(\"autofilled\");\r\n    } else {\r\n      el.classList.remove(\"autofilled\");\r\n    }\r\n\r\n    if (smooth_login !== \"on\" || (smooth_login === \"on\" && isInput)) {\r\n      switch (inputType) {\r\n        case \"email\":\r\n          setEmailError(\"\");\r\n          setEmail(value);\r\n          break;\r\n        default:\r\n          if (event.keyCode === 13) {\r\n            handleResetPassword();\r\n          }\r\n          break;\r\n      }\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      {/* {renderHeader && <Header />} */}\r\n      <div className=\"register bg-gray-100 h-screen flex justify-center items-center pt-10 md:pt-2\">\r\n        <div className=\"container py-10 px-0 md:px-4 sm:px-0 w-96 mx-auto\">\r\n          <div className=\"reg_col text-center mb-8\">\r\n            <h1 className=\"text-2xl lg:text-2xl font-bold text-center mb-6 lg:mb-8\">\r\n              Forgot Password?\r\n            </h1>\r\n            <div\r\n              className=\"bg-white rounded-lg shadow-lg overflow-hidden\"\r\n              ref={formRef}\r\n            >\r\n              <div className=\"px-6 py-10 text-black\">\r\n                {smooth_login === \"on\" ? (\r\n                  <>\r\n                    <div className=\"bg-white w-full relative mb-3 sm:text-sm\">\r\n                      <input\r\n                        className=\"peer block border-2 border-slate-300 rounded-md shadow-sm focus:outline-none focus:border-sky-500 focus:ring-sky-500 focus:ring-1  w-full pt-6 pb-2 px-6 placeholder:text-transparent placeholder:[user-select:_none]\"\r\n                        placeholder=\"Email *\"\r\n                        type=\"email\"\r\n                        name=\"email\"\r\n                        onBlur={(e) => handleChange(e, \"email\")}\r\n                        onChange={(e) => handleChange(e, \"email\")}\r\n                        onFocus={(e) => handleChange(e, \"email\")}\r\n                        onKeyUp={handleChange}\r\n                      />\r\n                      <label\r\n                        className=\"transition-all text-[#597291] duration-100 ease-in absolute top-1/2 -translate-y-1/2 left-6 pointer-events-none [.autofilled~&]:top-[17px] [.autofilled~&]:text-xs [.autofilled~&]:left-[25px]\"\r\n                        for=\"email\"\r\n                      >\r\n                        Email\r\n                      </label>\r\n                    </div>\r\n                    <span\r\n                      className=\"block text-red-500 text-xs text-center w-full h-0 transition-[height] duration-200 ease-in overflow-hidden\"\r\n                      ref={(el) => (spanErrorRefs.current[0] = el)}\r\n                    >\r\n                      {emailError}\r\n                    </span>\r\n                  </>\r\n                ) : (\r\n                  <label className=\"relative block\">\r\n                    {emailError && (\r\n                      <span className=\"text-red-500 text-xs text-left w-full mb-2\">\r\n                        {emailError}\r\n                      </span>\r\n                    )}\r\n                    <input\r\n                      className=\"placeholder:italic placeholder:text-[#597291] block bg-white w-full border border-slate-300 rounded-md mb-3 py-2 pl-3 pr-3 shadow-sm focus:outline-none focus:border-sky-500 focus:ring-sky-500 focus:ring-1 sm:text-sm\"\r\n                      placeholder=\"Email Address *\"\r\n                      aria-label=\"Email Address\"\r\n                      type=\"email\"\r\n                      name=\"email\"\r\n                      onKeyUp={(e) => {\r\n                        setEmail(e.target.value);\r\n                        if (e.keyCode === 13) handleResetPassword();\r\n                      }}\r\n                      // onBlur={validateEmail}\r\n                      required\r\n                    />\r\n                  </label>\r\n                )}\r\n                <motion.button\r\n                  className=\"bg-blue-600 text-white font-bold py-3 px-6 my-3 rounded-lg w-full\"\r\n                  whileHover={{ scale: 1.1 }}\r\n                  whileTap={{ scale: 0.9 }}\r\n                  onClick={() => {\r\n                    smooth_login === \"on\" && validateEmail();\r\n                    handleResetPassword();\r\n                  }}\r\n                  aria-label=\"Reset Password\"\r\n                >\r\n                  Reset Password\r\n                </motion.button>\r\n                <motion.button\r\n                  className=\"bg-gray-50 mb-1 text-black py-3 rounded-lg w-full\"\r\n                  whileHover={{ backgroundColor: \"#eee\" }}\r\n                  whileTap={{ scale: 0.9 }}\r\n                  aria-label=\"Back to Login\"\r\n                  onClick={() => {\r\n                    window.location.href = \"/register-auth\";\r\n                  }}\r\n                >\r\n                  Register\r\n                </motion.button>\r\n                <motion.button\r\n                  onClick={() => {\r\n                    window.location.href = \"/login\";\r\n                  }}\r\n                  className=\"bg-gray-50 mb-1 text-black py-3 rounded-lg w-full\"\r\n                  whileHover={{ backgroundColor: \"#eee\" }}\r\n                  whileTap={{ scale: 0.9 }}\r\n                  aria-label=\"Back to Login\"\r\n                >\r\n                  Login\r\n                </motion.button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <Footer />\r\n    </>\r\n  );\r\n}\r\n\r\nexport default Forgotpass;\r\n"], "names": ["base_url", "process", "useEffect", "script", "document", "createElement", "src", "async", "onload", "body", "append<PERSON><PERSON><PERSON>", "_jsx", "_Fragment", "children", "className", "email", "setEmail", "useState", "emailError", "setEmailError", "smooth_login", "Get<PERSON><PERSON><PERSON>", "spanErrorRefs", "useRef", "formRef", "setSpanHeights", "useCallback", "current", "for<PERSON>ach", "ref", "i", "_ref$scrollHeight", "errMsg", "textContent", "currHeight", "offsetHeight", "height", "scrollHeight", "setTimeout", "style", "form", "isChromium", "navigator", "userAgent", "includes", "inputs", "querySelectorAll", "length", "input", "matches", "classList", "add", "window", "addEventListener", "removeEventListener", "toastr", "positionClass", "validateEmail", "isPassed", "test", "handleResetPassword", "querySelector", "axios", "post", "headers", "then", "res", "output", "data", "success", "location", "reload", "remove", "msg", "handleChange", "event", "inputType", "el", "target", "value", "isFocus", "type", "isInput", "keyCode", "_jsxs", "placeholder", "name", "onBlur", "e", "onChange", "onFocus", "onKeyUp", "for", "required", "motion", "button", "whileHover", "scale", "whileTap", "onClick", "backgroundColor", "href", "Footer"], "sourceRoot": ""}