<?php echo $access_to; ?>

<?php foreach ($models as $key => $model): ?>
    <?php if (!is_array($model)): ?>
        <div class="indent"><?php echo $model; ?></div>
    <?php else: ?>
        <?php if (isset($model['text'])) : ?>
            <div class="indent <?php echo (isset($model['tooltip']) ? 'hover-target' : ''); ?>"><?php echo $model['text']; ?>
                <?php if (isset($model['tooltip'])) : ?>
                    <span class="description-tooltip"><?php echo $model['tooltip']; ?></span>
                <?php endif; ?>
            </div>
        <?php elseif (isset($model['new_badge'])) : ?>
            <div class="indent newBadge"><?php echo $key; ?>✨
                <span style="background: #d6d6ff; padding: 2px 10px; border-radius: 10px; color: #2872fa !important; font-weight: bold; font-size: 12px;"><?php echo $new_badge_text; ?></span>
            </div>
        <?php endif; ?>
    <?php endif; ?>
<?php endforeach; ?>

<br />

<div class="hover-target"><?php echo $context_memory; ?>
    <span class="description-tooltip"><?php echo $context_memory_tooltip; ?></span>
</div>

<div class="hover-target"><?php echo $dialogue_limit; ?>
    <span class="description-tooltip"><?php echo $dialogue_limit_tooltip; ?></span>
</div>

<?php if ($advanced_tool_access): ?>
    <div class="hover-target"><?php echo $advanced_tool_access; ?>
        <span class="description-tooltip"><?php echo $advanced_tool_access_tooltip; ?></span>
    </div>
<?php endif; ?>

<?php if ($is_trial && !is_null($trial_text)) : ?>
    <div><?php echo $trial_text; ?></div>
<?php endif; ?>