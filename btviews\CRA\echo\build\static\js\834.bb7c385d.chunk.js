"use strict";(self.webpackChunkv1=self.webpackChunkv1||[]).push([[834,7719],{77719:(e,t,l)=>{l.r(t),l.d(t,{default:()=>o});var s=l(72791),a=l(80184);const o=function(){const e="http://localhost:9002/";return(0,s.useEffect)(()=>{const t=document.createElement("script");t.src=e+"snippets/com.global.vuzo/js/com.global.vuzo.js",t.async=!0,t.onload=()=>{},document.body.appendChild(t)},[e]),(0,a.jsx)(a.Fragment,{children:(0,a.jsx)("footer",{className:"relative hidden md:block md:absolute bottom-[10px] right-[5px] w-full mt-3 py-12 md:py-2 z-[8888] md:hidden"})})}},9165:(e,t,l)=>{l.r(t),l.d(t,{default:()=>u});var s=l(72791),a=l(96347),o=l(77719),r=l(31243),n=l(74335),i=l(95828),c=l.n(i),d=(l(92831),l(80184));const u=function(){const[e,t]=(0,s.useState)(""),[l,i]=(0,s.useState)(""),u=(0,n.bG)("smooth_login")||"off",m=(0,s.useRef)([]),f=(0,s.useRef)(null),h=(0,s.useCallback)(()=>{"on"===u&&m.current.forEach((e,t)=>{if(e){var l;const t=e.textContent,s=e.offsetHeight,a=t?0!==s?s:(null!==(l=e.scrollHeight)&&void 0!==l?l:0)+12:0;setTimeout(()=>{e.style.height=`${a}px`},100)}})},[u]);setTimeout(()=>{const e=f.current,t=navigator.userAgent.includes("Chrome")||navigator.userAgent.includes("Edg");if(e&&t){const t=e.querySelectorAll("input");t&&t.length>0&&t.forEach(e=>{e.matches(":-internal-autofill-selected")&&e.classList.add("autofilled")})}},200),(0,s.useEffect)(()=>{if("on"===u)return h(),window.addEventListener("resize",h),()=>{window.removeEventListener("resize",h)}},[u,h]),(0,s.useEffect)(()=>{"on"===u&&h()},[l,u,h]),(0,s.useEffect)(()=>{c().options={positionClass:"toast-top-center"}},[]);const p=()=>{let t=!1;return e?/\S+@\S+\.\S+/.test(e)?(i(""),t=!0):i("Invalid email format"):i("Email is required"),t},x=()=>{p()&&(document.querySelector(".loader-container").classList.add("active"),r.Z.post("http://localhost:9002/api/forgot-password",{email:e},{headers:{"content-type":"application/x-www-form-urlencoded"}}).then(function(e){let t=e.data;if(t.success)return c().success("Success. Please check your email."),void setTimeout(function(){window.location.reload()},3e3);document.querySelector(".loader-container").classList.remove("active"),t.data?c().error(t.data.msg):c().error("Invalid email address")}))},g=(e,l)=>{const s=e.target,a=s.value,o="focus"===e.type,r="change"===e.type||"keyup"===e.type;if(a||o||r?s.classList.add("autofilled"):s.classList.remove("autofilled"),"on"!==u||"on"===u&&r)if("email"===l)i(""),t(a);else 13===e.keyCode&&x()};return(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("div",{className:"register bg-gray-100 h-screen flex justify-center items-center pt-10 md:pt-2",children:(0,d.jsx)("div",{className:"container py-10 px-0 md:px-4 sm:px-0 w-96 mx-auto",children:(0,d.jsxs)("div",{className:"reg_col text-center mb-8",children:[(0,d.jsx)("h1",{className:"text-2xl lg:text-2xl font-bold text-center mb-6 lg:mb-8",children:"Forgot Password?"}),(0,d.jsx)("div",{className:"bg-white rounded-lg shadow-lg overflow-hidden",ref:f,children:(0,d.jsxs)("div",{className:"px-6 py-10 text-black",children:["on"===u?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsxs)("div",{className:"bg-white w-full relative mb-3 sm:text-sm",children:[(0,d.jsx)("input",{className:"peer block border-2 border-slate-300 rounded-md shadow-sm focus:outline-none focus:border-sky-500 focus:ring-sky-500 focus:ring-1  w-full pt-6 pb-2 px-6 placeholder:text-transparent placeholder:[user-select:_none]",placeholder:"Email *",type:"email",name:"email",onBlur:e=>g(e,"email"),onChange:e=>g(e,"email"),onFocus:e=>g(e,"email"),onKeyUp:g}),(0,d.jsx)("label",{className:"transition-all text-[#597291] duration-100 ease-in absolute top-1/2 -translate-y-1/2 left-6 pointer-events-none [.autofilled~&]:top-[17px] [.autofilled~&]:text-xs [.autofilled~&]:left-[25px]",for:"email",children:"Email"})]}),(0,d.jsx)("span",{className:"block text-red-500 text-xs text-center w-full h-0 transition-[height] duration-200 ease-in overflow-hidden",ref:e=>m.current[0]=e,children:l})]}):(0,d.jsxs)("label",{className:"relative block",children:[l&&(0,d.jsx)("span",{className:"text-red-500 text-xs text-left w-full mb-2",children:l}),(0,d.jsx)("input",{className:"placeholder:italic placeholder:text-[#597291] block bg-white w-full border border-slate-300 rounded-md mb-3 py-2 pl-3 pr-3 shadow-sm focus:outline-none focus:border-sky-500 focus:ring-sky-500 focus:ring-1 sm:text-sm",placeholder:"Email Address *","aria-label":"Email Address",type:"email",name:"email",onKeyUp:e=>{t(e.target.value),13===e.keyCode&&x()},required:!0})]}),(0,d.jsx)(a.E.button,{className:"bg-blue-600 text-white font-bold py-3 px-6 my-3 rounded-lg w-full",whileHover:{scale:1.1},whileTap:{scale:.9},onClick:()=>{"on"===u&&p(),x()},"aria-label":"Reset Password",children:"Reset Password"}),(0,d.jsx)(a.E.button,{className:"bg-gray-50 mb-1 text-black py-3 rounded-lg w-full",whileHover:{backgroundColor:"#eee"},whileTap:{scale:.9},"aria-label":"Back to Login",onClick:()=>{window.location.href="/register-auth"},children:"Register"}),(0,d.jsx)(a.E.button,{onClick:()=>{window.location.href="/login"},className:"bg-gray-50 mb-1 text-black py-3 rounded-lg w-full",whileHover:{backgroundColor:"#eee"},whileTap:{scale:.9},"aria-label":"Back to Login",children:"Login"})]})})]})})}),(0,d.jsx)(o.default,{})]})}}}]);
//# sourceMappingURL=834.bb7c385d.chunk.js.map