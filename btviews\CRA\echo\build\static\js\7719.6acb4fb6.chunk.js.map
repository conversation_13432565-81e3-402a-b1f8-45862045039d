{"version": 3, "file": "static/js/7719.6acb4fb6.chunk.js", "mappings": "iJA0BA,QAxBA,WAGE,MAAMA,EAAWC,yBAWjB,OAVAC,EAAAA,EAAAA,WAAU,KACR,MAAMC,EAASC,SAASC,cAAc,UACtCF,EAAOG,IAAMN,EAAW,iDACxBG,EAAOI,OAAQ,EACfJ,EAAOK,OAAS,OAIhBJ,SAASK,KAAKC,YAAYP,IACzB,CAACH,KAEFW,EAAAA,EAAAA,KAAAC,EAAAA,SAAA,CAAAC,UACEF,EAAAA,EAAAA,KAAA,UAAQG,UAAW,iHAMzB,C", "sources": ["footer/index.jsx"], "sourcesContent": ["import React, { useEffect } from 'react';\r\n\r\nfunction Powered() {\r\n  // const [isHidden, setIsHidden] = useState(false);\r\n\r\n  const base_url = process.env.REACT_APP_BASE_URL;\r\n  useEffect(() => {\r\n    const script = document.createElement('script');\r\n    script.src = base_url + 'snippets/com.global.vuzo/js/com.global.vuzo.js';\r\n    script.async = true;\r\n    script.onload = () => {\r\n      // window.displayGooglePlay();\r\n      // window.displayQR();\r\n    };\r\n    document.body.appendChild(script);\r\n  }, [base_url]);\r\n  return (\r\n    <>\r\n      <footer className={`relative hidden md:block md:absolute bottom-[10px] right-[5px] w-full mt-3 py-12 md:py-2 z-[8888] md:hidden`}>\r\n      {/* <footer className=\"relative hidden md:block md:absolute bottom-[10px] right-[5px] w-full mt-3 py-12 md:py-2 z-[8888]\"> */}\r\n\r\n      </footer>\r\n    </>\r\n  );\r\n}\r\n\r\nexport default Powered;\r\n"], "names": ["base_url", "process", "useEffect", "script", "document", "createElement", "src", "async", "onload", "body", "append<PERSON><PERSON><PERSON>", "_jsx", "_Fragment", "children", "className"], "sourceRoot": ""}