{"version": 3, "file": "static/js/7087.845c80ba.chunk.js", "mappings": "mOAiBA,MAAMA,EAAO,CACX,GAAGC,EAAAA,EAAAA,KAACC,EAAAA,IAAkB,IACtB,GAAGD,EAAAA,EAAAA,KAACE,EAAAA,IAAc,IAClB,GAAGF,EAAAA,EAAAA,KAACG,EAAAA,IAAY,IAChB,GAAGH,EAAAA,EAAAA,KAACI,EAAAA,IAAc,IAClB,GAAGJ,EAAAA,EAAAA,KAACK,EAAAA,IAAkB,KAkLxB,EA/KgBC,IACd,MAAMC,GAAOC,EAAAA,EAAAA,OACNC,EAAgBC,IAAqBC,EAAAA,EAAAA,UAAS,OAC9CC,EAAWC,IAAgBF,EAAAA,EAAAA,WAAS,IACpCG,EAASC,IAAcJ,EAAAA,EAAAA,WAAS,IAEhCK,EAAcC,IAAmBN,EAAAA,EAAAA,WAAS,GAgEjD,IA9DAO,EAAAA,EAAAA,WAAU,KACR,GAAIN,EAAW,CACb,MAAMO,EAAQC,WAAW,KACvBC,OAAOC,SAASC,KAAO,eACtB,KAEH,MAAO,IAAMC,aAAaL,EAC5B,GACC,CAACP,KAEJM,EAAAA,EAAAA,WAAU,KACR,GAAIX,EAAM,CAuBgBkB,WACtB,WACyBC,EAAAA,EAAMC,KAC3B,6CACA,CAAEC,IAAIC,EAAAA,EAAAA,IAAU,WAChB,CAAEC,QAAS,CAAE,eAAgB,wCAGlBC,KAAKC,QAChBf,GAAgB,GAEhBI,OAAOC,SAASC,KAAO,aAE3B,CAAE,MAAOU,GACPC,QAAQD,MAAM,8BAA+BA,GAC7CZ,OAAOC,SAASC,KAAO,aACzB,CAAC,QACCR,GAAW,EACb,GAIFoB,EACF,MAAoB,IAAT5B,IACTc,OAAOC,SAASC,KAAO,WAExB,CAAChB,SAES6B,IAAT7B,GAAsBO,EAAS,OAAO,KAE1C,MAAMuB,EAAgBZ,UACpB,IAME,aALuBC,EAAAA,EAAMC,KAC3B,4CACA,CAAEW,WAAY/B,EAAKgC,MAAOC,UAC1B,CAAEV,QAAS,CAAE,eAAgB,uBAEfC,IAClB,CAAE,MAAOE,GAEP,MADAC,QAAQD,MAAM,6BAA8BA,GACtCA,CACR,GAmCF,OACEjC,EAAAA,EAAAA,KAAAyC,EAAAA,SAAA,CAAAC,SACG1B,IACChB,EAAAA,EAAAA,KAAAyC,EAAAA,SAAA,CAAAC,SACG5B,GACCd,EAAAA,EAAAA,KAAC2C,EAAAA,EAAO,KAERC,EAAAA,EAAAA,MAAAH,EAAAA,SAAA,CAAAC,SAAA,EACEE,EAAAA,EAAAA,MAACC,EAAAA,EAAM,CAAAH,SAAA,EACL1C,EAAAA,EAAAA,KAAA,QAAM8C,KAAK,SAASC,QAAQ,uBAC5B/C,EAAAA,EAAAA,KAAA,SAAA0C,SAAO,gCACP1C,EAAAA,EAAAA,KAAA,QAAM8C,KAAK,cAAcC,QAAQ,0BAEnC/C,EAAAA,EAAAA,KAACgD,EAAAA,QAAM,CAACzC,KAAMA,KACdP,EAAAA,EAAAA,KAAA,OAAKiD,UAAU,0BAAyBP,UACtC1C,EAAAA,EAAAA,KAAA,OAAKiD,UAAU,iBAAgBP,SAC3B9B,GAuBAgC,EAAAA,EAAAA,MAAA,OAAKK,UAAU,aAAYP,SAAA,EACzB1C,EAAAA,EAAAA,KAAA,MAAIiD,UAAU,sBAAqBP,SAAC,kCACpC1C,EAAAA,EAAAA,KAAA,KAAGiD,UAAU,cAAaP,SAAC,wHAxB7BE,EAAAA,EAAAA,MAAA,OAAKK,UAAU,aAAYP,SAAA,EACzB1C,EAAAA,EAAAA,KAAA,MAAIiD,UAAU,QAAOP,SAAC,oCACtB1C,EAAAA,EAAAA,KAAA,OAAKiD,UAAU,eAAcP,SAC1BQ,OAAOC,KAAKpD,GAAMqD,IAAKC,IACtBrD,EAAAA,EAAAA,KAAA,OAEEsD,QAASA,IAvDf7B,WAClB,IAEEf,EAAkB6C,GACdA,GAAO,UACHlB,EAAckB,GACpBlC,OAAOC,SAASC,KAAO,+CAE3B,CAAE,MAAOU,GACPC,QAAQD,MAAMA,EAChB,GA6CuCuB,CAAYH,GAC3BJ,UAAW,gBACTxC,IAAmB4C,EAAM,SAAW,IACnCX,SAEF3C,EAAKsD,IANDA,MAUV5C,GAAkB,GAAKA,GAAkB,IACxCT,EAAAA,EAAAA,KAAA,UAAQiD,UAAU,gBAAgBK,QApDnC7B,UACnB,GAAKhB,EAIL,IACE,MAAMgD,QAAiBpB,EAAc5B,GAGjCgD,EAASzB,QACXnB,GAAa,GAEbqB,QAAQD,MAAM,2BAA4BwB,EAE9C,CAAE,MAAOxB,GACPC,QAAQD,MAAM,2BAA4BA,EAC5C,MAdEC,QAAQD,MAAM,yBAkD4DS,SAAC,yB", "sources": ["review/index.jsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport { Auth } from \"../core/utils/auth\";\r\nimport { GetCookie } from \"../core/utils/cookies\";\r\nimport { Helmet } from \"react-helmet\";\r\nimport Header from \"../header\";\r\nimport axios from \"axios\";\r\nimport { Loading } from \"../aiapps/loading\";\r\n\r\nimport {\r\n  FaRegFaceFrownOpen,\r\n  FaRegFaceFrown,\r\n  FaRegFaceMeh,\r\n  FaRegFaceSmile,\r\n  FaRegFaceSmileBeam,\r\n} from \"react-icons/fa6\";\r\nimport \"./style.css\";\r\n\r\nconst SVGs = {\r\n  1: <FaRegFaceFrownOpen />,\r\n  2: <FaRegFaceFrown />,\r\n  3: <FaRegFaceMeh />,\r\n  4: <FaRegFaceSmile />,\r\n  5: <FaRegFaceSmileBeam />,\r\n};\r\n\r\nconst Review = (props) => {\r\n  const auth = Auth();\r\n  const [selectedRating, setSelectedRating] = useState(null);\r\n  const [submitted, setSubmitted] = useState(false);\r\n  const [loading, setLoading] = useState(true);\r\n  // const [userEmailExists, setUserEmailExists] = useState(false);\r\n  const [hasActiveSub, setHasActiveSub] = useState(false);\r\n\r\n  useEffect(() => {\r\n    if (submitted) {\r\n      const timer = setTimeout(() => {\r\n        window.location.href = '/my-account';\r\n      }, 3000);\r\n\r\n      return () => clearTimeout(timer); \r\n    }\r\n  }, [submitted]);\r\n  \r\n  useEffect(() => {\r\n    if (auth) {\r\n      // const checkUserEmail = async () => {\r\n      //   try {\r\n      //     const response = await axios.post(\r\n      //       `${process.env.REACT_APP_API_URL}/get-trustpilot`,\r\n      //       { user_email: auth.email },\r\n      //       { headers: { \"Content-Type\": \"application/json\" } }\r\n      //     );\r\n\r\n      //     // console.log(\"Check Email Response:\", response.data);\r\n\r\n      //     if (response.data.success === 1) {\r\n      //       setUserEmailExists(true);\r\n      //       window.location.href = \"/my-account\";\r\n      //     } else {\r\n      //       setLoading(false);\r\n      //     }\r\n      //   } catch (error) {\r\n      //     // console.error(\"Error checking email:\", error);\r\n      //     setLoading(false);\r\n      //   }\r\n      // };\r\n\r\n      const getSubscription = async () => {\r\n        try {\r\n          const response = await axios.post(\r\n            `${process.env.REACT_APP_API_URL}/get-subscription`,\r\n            { tk: GetCookie(\"access\") },\r\n            { headers: { \"content-type\": \"application/x-www-form-urlencoded\" } }\r\n          );\r\n\r\n          if (response.data.success) {\r\n            setHasActiveSub(true);\r\n          } else {\r\n            window.location.href = \"/my-account\";\r\n          }\r\n        } catch (error) {\r\n          console.error(\"Error getting subscription:\", error);\r\n          window.location.href = \"/my-account\";\r\n        } finally {\r\n          setLoading(false);\r\n        }\r\n      };\r\n\r\n      // checkUserEmail();\r\n      getSubscription();\r\n    } else if (auth === false) {\r\n      window.location.href = \"/login\";\r\n    }\r\n  }, [auth]);\r\n\r\n  if (auth === undefined || loading) return null;\r\n\r\n  const setMoodRating = async (rating) => {\r\n    try {\r\n      const response = await axios.post(\r\n        `${process.env.REACT_APP_API_URL}/set-mood-rating`,\r\n        { user_email: auth.email, rating },\r\n        { headers: { \"Content-Type\": \"application/json\" } }\r\n      );\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(\"Error setting mood rating:\", error);\r\n      throw error;\r\n    }\r\n  };\r\n\r\n  const handleClick = async (num) => {\r\n    try {\r\n      // console.log(`Selected rating: ${num}`);\r\n      setSelectedRating(num);\r\n      if (num >= 4) {\r\n        await setMoodRating(num);\r\n        window.location.href = \"https://www.trustpilot.com/review/ai-pro.org\";\r\n      }\r\n    } catch (error) {\r\n      console.error(error);\r\n    }\r\n  };\r\n\r\n  const handleSubmit = async () => {\r\n    if (!selectedRating) {\r\n      console.error(\"Rating not selected!\");\r\n      return;\r\n    }\r\n    try {\r\n      const response = await setMoodRating(selectedRating);\r\n      // console.log(\"API Response:\", response);\r\n\r\n      if (response.success) {\r\n        setSubmitted(true);\r\n      } else {\r\n        console.error(\"Failed to submit rating:\", response);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error submitting rating:\", error);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      {hasActiveSub && (\r\n        <>\r\n          {loading ? (\r\n            <Loading />\r\n          ) : (\r\n            <>\r\n              <Helmet>\r\n                <meta name=\"robots\" content=\"noindex, nofollow\" />\r\n                <title>AI-Pro | Experience Survey</title>\r\n                <meta name=\"description\" content=\"Experience Survey\" />\r\n              </Helmet>\r\n              <Header auth={auth} />\r\n              <div className=\"mood-survey bg-gray-100\">\r\n                <div className=\"mood-container\">\r\n                  {!submitted ? (\r\n                    <div className=\"survey-col\">\r\n                      <h1 className=\"title\">How's your experience with us?</h1>\r\n                      <div className=\"rating-icons\">\r\n                        {Object.keys(SVGs).map((key) => (\r\n                          <div\r\n                            key={key}\r\n                            onClick={() => handleClick(key)}\r\n                            className={`rating-icon ${\r\n                              selectedRating === key ? \"active\" : \"\"\r\n                            }`}\r\n                          >\r\n                            {SVGs[key]}\r\n                          </div>\r\n                        ))}\r\n                      </div>\r\n                      {selectedRating >= 1 && selectedRating <= 3 && (\r\n                        <button className=\"submit-button\" onClick={handleSubmit}>\r\n                          Submit\r\n                        </button>\r\n                      )}\r\n                    </div>\r\n                  ) : (\r\n                    <div className=\"survey-col\">\r\n                      <h1 className=\"title text-gradient\">Thank you for your feedback!</h1>\r\n                      <p className=\"description\">\r\n                        We've received your survey and appreciate your feedback. Even\r\n                        small suggestions can lead to great enhancements.\r\n                      </p>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              </div>\r\n            </>\r\n          )}\r\n        </>\r\n      )}\r\n    </>\r\n  );\r\n};\r\n\r\nexport default Review;\r\n"], "names": ["SVGs", "_jsx", "FaRegFaceFrownOpen", "FaRegFaceFrown", "FaRegFaceMeh", "FaRegFaceSmile", "FaRegFaceSmileBeam", "props", "auth", "<PERSON><PERSON>", "selectedRating", "setSelectedRating", "useState", "submitted", "setSubmitted", "loading", "setLoading", "hasActiveSub", "setHasActiveSub", "useEffect", "timer", "setTimeout", "window", "location", "href", "clearTimeout", "async", "axios", "post", "tk", "Get<PERSON><PERSON><PERSON>", "headers", "data", "success", "error", "console", "getSubscription", "undefined", "setMoodRating", "user_email", "email", "rating", "_Fragment", "children", "Loading", "_jsxs", "<PERSON><PERSON><PERSON>", "name", "content", "Header", "className", "Object", "keys", "map", "key", "onClick", "num", "handleClick", "response"], "sourceRoot": ""}