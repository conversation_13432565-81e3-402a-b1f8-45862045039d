{"version": 3, "file": "static/css/8075.903aa26a.chunk.css", "mappings": "AAAA,yBACI,4BACE,eACF,CACJ,CCJA,WACE,gBAAiB,CAEjB,KAAM,CADN,UAEF,CACA,WAAY,WAAa,CACzB,OAGE,QAAS,CAFT,cAAe,CACf,QAAS,CAGT,8BAAgC,CADhC,UAEF,CACA,UACE,SACF,CACA,WAEE,eAAmB,CADpB,gCAED,CACA,cAEC,4BAA8B,CAC9B,eAAiB,CAFjB,0BAGD,CAEA,yBACE,uBACE,cACF,CACF,CACA,yBACE,wBACE,kBACF,CACF,CAEA,yBACE,WACE,mBACF,CACF,CAEA,yBACE,WACE,mBACF,CACF,CAEA,qDAEE,WAAY,CACZ,oBAAqB,CAErB,gBAAiB,CACjB,SAAU,CACV,kCAAoC,CAHpC,SAIF,CAEA,iEAEE,SACF,CAEA,wBACE,GAEE,SAAU,CADV,2BAEF,CACA,GAEE,SAAU,CADV,uBAEF,CACF,CAEA,kBACE,iBACF,CAEA,UASE,qBAAuB,CACvB,iBAAkB,CAElB,gCAAkC,CAPlC,YAAa,CAGb,MAAO,CAJP,eAAgB,CADhB,iBAAkB,CAFlB,2BAA4B,CAU5B,cAAe,CALf,iBAAkB,CAJlB,oBAAqB,CAKrB,QAMF,CAEA,aACE,QACF,CAEA,YAEE,aAAe,CACf,WACF,CAEA,8CALE,aAOF,CAEA,gBACE,8BAA+B,CAC/B,eACF,CAQA,yBACE,WACE,YACF,CACF,CAEA,yBACE,aACE,yBACF,CACF,CAEA,oBAEI,eAAgB,CAKhB,kBAAmB,CACnB,+BAAiC,CAHjC,cAAe,CAIf,aAAc,CACd,eAAgB,CAChB,iBAAkB,CAPlB,YAAa,CAHb,OAAQ,CAER,eAAgB,CAGhB,QAMJ,CAEA,6CACE,SACE,YACF,CACA,cAAe,kBAAqB,CACpC,gBACE,iBAAkB,CAClB,UACF,CACA,mBAIE,qBAAsB,CACtB,YACF,CACA,mCAJE,kBAAmB,CAFnB,YAAa,CACb,qBAgBF,CAXA,gBAQE,eAAgB,CAChB,WAAY,CAFZ,cAAe,CADf,WAAY,CAHZ,sBAAuB,CAOvB,SAAU,CALV,UAMF,CACA,gBAIE,+BAAiC,CAFjC,WAAY,CACZ,oBAAsB,CAEtB,iBAAkB,CAJlB,UAKF,CACF,CACA,yBACE,WAAY,WAAa,CACzB,cAAe,eAAkB,CACjC,yFACI,QACJ,CACA,gBACE,iBAAkB,CAClB,UACF,CACF,CC/LA,aAAa,eAAe,CAAC,eAAe,wBAAwB,CAAC,oBAAoB,CAAC,sCAAsC,UAAU,CAAC,uBAAuB,UAAU,CAAC,oBAAoB,CAAC,oBAAuG,UAAU,CAAsE,8DAA8D,CAAC,wBAAwB,CAAlN,WAAW,CAAC,cAAc,CAAC,eAAe,CAAyK,aAAY,CAA/G,UAAU,CAAlK,iBAAiB,CAAC,WAAW,CAAiE,gCAAgC,CAAC,wBAAwB,CAAzH,SAA0O,CAAC,oDAAoD,UAAU,CAAsB,cAAc,CAAY,8DAA8D,CAAC,wBAAuB,CAAjG,UAAU,CAA9C,oBAAsI,CAAC,yBAAoC,UAAU,CAArB,UAAU,CAAY,UAAU,CAAC,0BAA2E,uBAAsB,CAA9C,cAAc,CAAC,QAAQ,CAAtC,cAAc,CAAxB,SAAwE,CAAC,kBAAwB,OAAO,CAAb,KAAK,CAAS,UAAU,CAAC,qBAAqB,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAC,sBAA4B,OAAO,CAAb,KAAK,CAAS,UAAU,CAAC,yBAAyB,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAC,gBAAyB,SAAQ,CAAjB,QAAkB,CAAC,iBAA0B,UAAS,CAAlB,QAAmB,CAAC,oBAA+B,WAAU,CAArB,UAAsB,CAAC,mBAAmB,WAAW,CAAC,SAAS,CAAC,iBAA+C,mBAAkB,CAAhD,cAAc,CAAC,cAAkC,CAAC,mBAA4E,qBAAqB,CAAC,qBAAqM,wBAA+B,CAAC,2BAA2B,CAA7E,iBAAiB,CAA4H,wBAAwB,CAAC,UAAU,CAAY,8DAA8D,CAAC,wBAAuB,CAA1X,cAAc,CAA2Q,UAAU,CAAnT,eAAe,CAAgB,2BAA2B,CAA9E,mBAAmB,CAArC,iBAAiB,CAAgF,WAAgV,CAAC,yBAAmE,qCAAoC,CAA9E,aAAa,CAAC,2BAAiE,CAAC,2BAA0F,wBAAwB,CAAqG,cAAa,CAAvG,+DAA+D,CAAC,yBAAyB,CAAnG,SAAkH,CAAC,6BAA6B,swBAAmxG,+BAAe,8yBAAi0B,iCAA4B,kgBAAgE", "sources": ["pay-upgrade/style.css", "header/style.css", "../node_modules/toastr/build/toastr.min.css"], "sourcesContent": ["@media (min-width: 620px) {\r\n    .Payment-upgrade .pay_right {\r\n      min-width: 600px;\r\n    }\r\n}", ".headerRef {\r\n  display: absolute;\r\n  width: 100%;\r\n  top: 0;\r\n}\r\n.aiprologo {width: 150px;}\r\nheader {\r\n  position: fixed;\r\n  top: 25px;\r\n  left: 50%;\r\n  width: 100%;\r\n  transform: translate(-50%, -50%);\r\n}\r\n.svg-icon {\r\n  fill: white;\r\n}\r\n.headernav {\r\n\tbox-shadow: 0 3px 19px -14px black;\r\n  background: #ffffff;\r\n}\r\n.headerctabtn {\r\n\tpadding: 5px 10px !important;\r\n\tborder-radius: 15px !important;\r\n\tfont-size: 0.9rem;\r\n}\r\n\r\n@media (max-width: 680px) {\r\n  #maintenance-container {\r\n    font-size: 14px;\r\n  }\r\n}\r\n@media (max-width: 590px) {\r\n  .headernav.top-\\[60px\\] {\r\n    top: 70px !important;\r\n  }\r\n}\r\n\r\n@media (max-width: 991px) {\r\n  .headernav {\r\n    width: 98% !important;\r\n  }\r\n}\r\n\r\n@media (max-width: 500px) {\r\n  .headernav {\r\n    width: 90% !important;\r\n  }\r\n}\r\n\r\n.headernav nav#menu ul li a:before,\r\n.menuArrow:before {\r\n  content: \">\";\r\n  display: inline-block;\r\n  width: 7px;\r\n  margin-right: 2px;\r\n  opacity: 0;\r\n  transition: opacity 0.2s ease-in-out;\r\n}\r\n\r\n.headernav nav#menu ul li a:hover:before,\r\n.menuArrow:hover:before {\r\n  opacity: 1;\r\n}\r\n\r\n@keyframes slideInRight {\r\n  from {\r\n    transform: translateX(-100%);\r\n    opacity: 0;\r\n  }\r\n  to {\r\n    transform: translateX(0);\r\n    opacity: 1;\r\n  }\r\n}\r\n\r\n.dropdown-wrapper {\r\n  position: relative;\r\n}\r\n\r\n.dropdown {\r\n  overscroll-behavior: contain;\r\n  scrollbar-width: none;\r\n  overflow-y: scroll;\r\n  max-height: 75vh;\r\n  display: none;\r\n  position: absolute;\r\n  top: 100%;\r\n  left: 0;\r\n  background-color: white;\r\n  border-radius: 5px;\r\n  padding: 1rem 0;\r\n  box-shadow: 0 7px 17px -10px black;\r\n}\r\n\r\n.dropdown li {\r\n  margin: 0;\r\n}\r\n\r\n.dropdown a {\r\n  display: block;\r\n  padding: 0.5rem;\r\n  width: 250px;\r\n}\r\n\r\n.dropdown-wrapper:hover .dropdown {\r\n  display: block;\r\n}\r\n\r\n.menu-container {\r\n  max-height: calc(90vh - 3.5rem);\r\n  overflow-y: auto;\r\n}\r\n\r\n\r\n/* .dropdown-wrapper:focus-within .dropdown {\r\n  display: block;\r\n} */\r\n/*WILL FIX SHIFT+TAB ACCESSIBILITY IN ANOTHER TICKET*/\r\n\r\n@media (min-width: 768px) {\r\n  .mobilenav {\r\n    display: none;\r\n  }\r\n}\r\n\r\n@media (max-width: 991px){\r\n  .headerStyle {\r\n    max-width: 980px !important;\r\n  }\r\n}\r\n\r\n.mobilenav .headnav{\r\n    right: 0;\r\n    background: #fff;\r\n    text-align: left;\r\n    padding: 15px;\r\n    font-size: 14px;\r\n    top: 72px;\r\n    border-radius: 15px;\r\n    box-shadow: 0 2px 13px -9px black;\r\n    line-height: 2;\r\n    max-height: 83vh;\r\n    overflow-y: scroll;\r\n}\r\n\r\n@media (min-width: 0px) and (max-width: 991px) {\r\n  .headnav {\r\n    display: none;\r\n  }\r\n  .ctaStartHere {margin: 0 !important;}\r\n  .hamburger-menu {\r\n    position: absolute;\r\n    right: 25px;\r\n  }\r\n  .headnav.show-menu {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    background-color: #fff;\r\n    padding: 10px;\r\n  }\r\n  .hamburger-menu {\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: center;\r\n    align-items: center;\r\n    width: 30px;\r\n    height: 30px;\r\n    cursor: pointer;\r\n    background: none;\r\n    border: none;\r\n    padding: 0;\r\n  }\r\n  .hamburger-line {\r\n    width: 30px;\r\n    height: 100%;\r\n    height: 3px !important;\r\n    background-color: #000 !important;\r\n    margin-bottom: 4px;\r\n  }\r\n}\r\n@media (max-width: 639px) {\r\n  .aiprologo {width: 110px;}\r\n  .headerctabtn {font-size: 0.8rem;}\r\n  .headerctabtn.gradient-hover-effect.text-white.rounded-3xl.block.sm\\:hidden.ml-auto.mr-3 {\r\n      margin: 0;\r\n  }\r\n  .hamburger-menu {\r\n    position: absolute;\r\n    right: 25px;\r\n  }\r\n}", ".toast-title{font-weight:700}.toast-message{-ms-word-wrap:break-word;word-wrap:break-word}.toast-message a,.toast-message label{color:#FFF}.toast-message a:hover{color:#CCC;text-decoration:none}.toast-close-button{position:relative;right:-.3em;top:-.3em;float:right;font-size:20px;font-weight:700;color:#FFF;-webkit-text-shadow:0 1px 0 #fff;text-shadow:0 1px 0 #fff;opacity:.8;-ms-filter:progid:DXImageTransform.Microsoft.Alpha(Opacity=80);filter:alpha(opacity=80);line-height:1}.toast-close-button:focus,.toast-close-button:hover{color:#000;text-decoration:none;cursor:pointer;opacity:.4;-ms-filter:progid:DXImageTransform.Microsoft.Alpha(Opacity=40);filter:alpha(opacity=40)}.rtl .toast-close-button{left:-.3em;float:left;right:.3em}button.toast-close-button{padding:0;cursor:pointer;background:0 0;border:0;-webkit-appearance:none}.toast-top-center{top:0;right:0;width:100%}.toast-bottom-center{bottom:0;right:0;width:100%}.toast-top-full-width{top:0;right:0;width:100%}.toast-bottom-full-width{bottom:0;right:0;width:100%}.toast-top-left{top:12px;left:12px}.toast-top-right{top:12px;right:12px}.toast-bottom-right{right:12px;bottom:12px}.toast-bottom-left{bottom:12px;left:12px}#toast-container{position:fixed;z-index:999999;pointer-events:none}#toast-container *{-moz-box-sizing:border-box;-webkit-box-sizing:border-box;box-sizing:border-box}#toast-container>div{position:relative;pointer-events:auto;overflow:hidden;margin:0 0 6px;padding:15px 15px 15px 50px;width:300px;-moz-border-radius:3px;-webkit-border-radius:3px;border-radius:3px;background-position:15px center;background-repeat:no-repeat;-moz-box-shadow:0 0 12px #999;-webkit-box-shadow:0 0 12px #999;box-shadow:0 0 12px #999;color:#FFF;opacity:.8;-ms-filter:progid:DXImageTransform.Microsoft.Alpha(Opacity=80);filter:alpha(opacity=80)}#toast-container>div.rtl{direction:rtl;padding:15px 50px 15px 15px;background-position:right 15px center}#toast-container>div:hover{-moz-box-shadow:0 0 12px #000;-webkit-box-shadow:0 0 12px #000;box-shadow:0 0 12px #000;opacity:1;-ms-filter:progid:DXImageTransform.Microsoft.Alpha(Opacity=100);filter:alpha(opacity=100);cursor:pointer}#toast-container>.toast-info{background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGwSURBVEhLtZa9SgNBEMc9sUxxRcoUKSzSWIhXpFMhhYWFhaBg4yPYiWCXZxBLERsLRS3EQkEfwCKdjWJAwSKCgoKCcudv4O5YLrt7EzgXhiU3/4+b2ckmwVjJSpKkQ6wAi4gwhT+z3wRBcEz0yjSseUTrcRyfsHsXmD0AmbHOC9Ii8VImnuXBPglHpQ5wwSVM7sNnTG7Za4JwDdCjxyAiH3nyA2mtaTJufiDZ5dCaqlItILh1NHatfN5skvjx9Z38m69CgzuXmZgVrPIGE763Jx9qKsRozWYw6xOHdER+nn2KkO+Bb+UV5CBN6WC6QtBgbRVozrahAbmm6HtUsgtPC19tFdxXZYBOfkbmFJ1VaHA1VAHjd0pp70oTZzvR+EVrx2Ygfdsq6eu55BHYR8hlcki+n+kERUFG8BrA0BwjeAv2M8WLQBtcy+SD6fNsmnB3AlBLrgTtVW1c2QN4bVWLATaIS60J2Du5y1TiJgjSBvFVZgTmwCU+dAZFoPxGEEs8nyHC9Bwe2GvEJv2WXZb0vjdyFT4Cxk3e/kIqlOGoVLwwPevpYHT+00T+hWwXDf4AJAOUqWcDhbwAAAAASUVORK5CYII=)!important}#toast-container>.toast-error{background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAHOSURBVEhLrZa/SgNBEMZzh0WKCClSCKaIYOED+AAKeQQLG8HWztLCImBrYadgIdY+gIKNYkBFSwu7CAoqCgkkoGBI/E28PdbLZmeDLgzZzcx83/zZ2SSXC1j9fr+I1Hq93g2yxH4iwM1vkoBWAdxCmpzTxfkN2RcyZNaHFIkSo10+8kgxkXIURV5HGxTmFuc75B2RfQkpxHG8aAgaAFa0tAHqYFfQ7Iwe2yhODk8+J4C7yAoRTWI3w/4klGRgR4lO7Rpn9+gvMyWp+uxFh8+H+ARlgN1nJuJuQAYvNkEnwGFck18Er4q3egEc/oO+mhLdKgRyhdNFiacC0rlOCbhNVz4H9FnAYgDBvU3QIioZlJFLJtsoHYRDfiZoUyIxqCtRpVlANq0EU4dApjrtgezPFad5S19Wgjkc0hNVnuF4HjVA6C7QrSIbylB+oZe3aHgBsqlNqKYH48jXyJKMuAbiyVJ8KzaB3eRc0pg9VwQ4niFryI68qiOi3AbjwdsfnAtk0bCjTLJKr6mrD9g8iq/S/B81hguOMlQTnVyG40wAcjnmgsCNESDrjme7wfftP4P7SP4N3CJZdvzoNyGq2c/HWOXJGsvVg+RA/k2MC/wN6I2YA2Pt8GkAAAAASUVORK5CYII=)!important}#toast-container>.toast-success{background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADsSURBVEhLY2AYBfQMgf///3P8+/evAIgvA/FsIF+BavYDDWMBGroaSMMBiE8VC7AZDrIFaMFnii3AZTjUgsUUWUDA8OdAH6iQbQEhw4HyGsPEcKBXBIC4ARhex4G4BsjmweU1soIFaGg/WtoFZRIZdEvIMhxkCCjXIVsATV6gFGACs4Rsw0EGgIIH3QJYJgHSARQZDrWAB+jawzgs+Q2UO49D7jnRSRGoEFRILcdmEMWGI0cm0JJ2QpYA1RDvcmzJEWhABhD/pqrL0S0CWuABKgnRki9lLseS7g2AlqwHWQSKH4oKLrILpRGhEQCw2LiRUIa4lwAAAABJRU5ErkJggg==)!important}#toast-container>.toast-warning{background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGYSURBVEhL5ZSvTsNQFMbXZGICMYGYmJhAQIJAICYQPAACiSDB8AiICQQJT4CqQEwgJvYASAQCiZiYmJhAIBATCARJy+9rTsldd8sKu1M0+dLb057v6/lbq/2rK0mS/TRNj9cWNAKPYIJII7gIxCcQ51cvqID+GIEX8ASG4B1bK5gIZFeQfoJdEXOfgX4QAQg7kH2A65yQ87lyxb27sggkAzAuFhbbg1K2kgCkB1bVwyIR9m2L7PRPIhDUIXgGtyKw575yz3lTNs6X4JXnjV+LKM/m3MydnTbtOKIjtz6VhCBq4vSm3ncdrD2lk0VgUXSVKjVDJXJzijW1RQdsU7F77He8u68koNZTz8Oz5yGa6J3H3lZ0xYgXBK2QymlWWA+RWnYhskLBv2vmE+hBMCtbA7KX5drWyRT/2JsqZ2IvfB9Y4bWDNMFbJRFmC9E74SoS0CqulwjkC0+5bpcV1CZ8NMej4pjy0U+doDQsGyo1hzVJttIjhQ7GnBtRFN1UarUlH8F3xict+HY07rEzoUGPlWcjRFRr4/gChZgc3ZL2d8oAAAAASUVORK5CYII=)!important}#toast-container.toast-bottom-center>div,#toast-container.toast-top-center>div{width:300px;margin-left:auto;margin-right:auto}#toast-container.toast-bottom-full-width>div,#toast-container.toast-top-full-width>div{width:96%;margin-left:auto;margin-right:auto}.toast{background-color:#030303}.toast-success{background-color:#51A351}.toast-error{background-color:#BD362F}.toast-info{background-color:#2F96B4}.toast-warning{background-color:#F89406}.toast-progress{position:absolute;left:0;bottom:0;height:4px;background-color:#000;opacity:.4;-ms-filter:progid:DXImageTransform.Microsoft.Alpha(Opacity=40);filter:alpha(opacity=40)}@media all and (max-width:240px){#toast-container>div{padding:8px 8px 8px 50px;width:11em}#toast-container>div.rtl{padding:8px 50px 8px 8px}#toast-container .toast-close-button{right:-.2em;top:-.2em}#toast-container .rtl .toast-close-button{left:-.2em;right:.2em}}@media all and (min-width:241px) and (max-width:480px){#toast-container>div{padding:8px 8px 8px 50px;width:18em}#toast-container>div.rtl{padding:8px 50px 8px 8px}#toast-container .toast-close-button{right:-.2em;top:-.2em}#toast-container .rtl .toast-close-button{left:-.2em;right:.2em}}@media all and (min-width:481px) and (max-width:768px){#toast-container>div{padding:15px 15px 15px 50px;width:25em}#toast-container>div.rtl{padding:15px 50px 15px 15px}}"], "names": [], "sourceRoot": ""}