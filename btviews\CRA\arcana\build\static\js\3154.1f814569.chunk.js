"use strict";(self.webpackChunkv1=self.webpackChunkv1||[]).push([[3154,9044,7250,2377],{72608:(e,t,a)=>{a.d(t,{Z:()=>r});a(72791);const r=a.p+"static/media/AIPRO.0580715cba3cbc2179c46b033d087db9.svg"},34492:(e,t,a)=>{a.d(t,{aS:()=>i,ar:()=>b,mD:()=>h,mW:()=>x,o0:()=>p,p6:()=>c,rZ:()=>n,tN:()=>u,x6:()=>o,yt:()=>m});var r=a(74335),s=a(80184);function l(e){return e?"usd"===e.toLowerCase()?"$":"eur"===e.toLowerCase()?"€":"gbp"===e.toLowerCase()?"£":"brl"===e.toLowerCase()||"sar"===e.toLowerCase()?"R$":"czk"===e.toLowerCase()?"Kč":"":""}function n(e){return e?"usd"===e.toLowerCase()||"eur"===e.toLowerCase()?"US":"gbp"===e.toLowerCase()?"GB":"brl"===e.toLowerCase()?"US":"sar"===e.toLowerCase()?"AE":"chf"===e.toLowerCase()?"CH":"sek"===e.toLowerCase()?"SE":"US":""}function c(e){if(!e)return"";const t=new Date(e.replace(/ /g,"T"));return["January","February","March","April","May","June","July","August","September","October","November","December"][t.getMonth()]+" "+t.getDate()+" "+t.getFullYear()}function i(e){return e.trial_price?e.trial_price:e.price?e.price:"0"}function o(e){return e.toString().replace(/\B(?=(\d{3})+(?!\d))/g,",")}function d(e){const t=parseFloat(e);return o(t%1==0?t.toFixed(0):t.toFixed(2))}function m(e,t){return e&&t?isNaN(t)?"":parseFloat(t)>=0?"sar"===e.toLowerCase()?d(t).toLocaleString("en-US")+"ر.س.":"aed"===e.toLowerCase()?d(t).toLocaleString("en-US")+"AED":"chf"===e.toLowerCase()?d(t).toLocaleString("en-US")+"CHF":"sek"===e.toLowerCase()?d(t).toLocaleString("en-US")+"SEK":"pln"===e.toLowerCase()?d(t).toLocaleString("en-US")+"zł":"ron"===e.toLowerCase()?d(t).toLocaleString("en-US")+"LEI":"huf"===e.toLowerCase()?d(t).toLocaleString("en-US")+"Ft":"dkk"===e.toLowerCase()?d(t).toLocaleString("en-US")+"kr.":"bgn"===e.toLowerCase()?d(t).toLocaleString("en-US")+"лв":"try"===e.toLowerCase()?d(t).toLocaleString("en-US")+"₺":l(e)+d(t).toLocaleString("en-US"):"-"+l(e)+(-1*d(t)).toLocaleString("en-US"):""}function u(e,t){e=new Date(e);var a=((t=new Date(t)).getTime()-e.getTime())/1e3;return a/=60,Math.abs(Math.round(a))}function p(e){if(!e)return"";const t=new Date(e.replace(/ /g,"T"));return["January","February","March","April","May","June","July","August","September","October","November","December"][t.getMonth()]+" "+t.getDate()+" "+t.getFullYear()+" "+t.getHours()+":"+t.getMinutes()}function x(e){let{plan:t}=e,a="",l="";return"Yearly"===t.payment_interval&&(a=m(t.currency,parseFloat(t.price/365).toFixed(2)),l=(0,s.jsxs)("div",{class:"text-xs mb-4",children:["billed ",m(t.currency,t.price),"/year"]})),"Monthly"===t.payment_interval&&(a=m(t.currency,parseFloat(t.price/30).toFixed(2)),l=(0,s.jsxs)("div",{class:"text-xs mb-4",children:["billed ",m(t.currency,t.price),"/Month"]})),t.trial_price&&(a=m(t.currency,parseFloat(t.trial_price/t.trial_days).toFixed(2)),l=(0,s.jsxs)("div",{class:"text-xs mb-4",children:["billed ",m(t.currency,t.trial_price)]})),(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("p",{className:"text-4xl font-bold text-gray-800 ".concat(""===(0,r.bG)("p_toggle")?"mb-4":""),children:[a," ",(0,s.jsx)("span",{className:"text-sm",children:" per Day"})]}),l]})}function h(e){let{plan:t}=e;return"on"===(0,r.bG)("daily")?x({plan:t}):t.trial_price?(0,s.jsx)("p",{className:"text-4xl font-bold text-gray-800 mb-4",children:m(t.currency,t.trial_price)}):(0,s.jsxs)("p",{className:"text-4xl font-bold text-gray-800 mb-4",children:[m(t.currency,t.price),(0,s.jsxs)("span",{className:"text-sm",children:["/","Monthly"===t.payment_interval?"month":"year"]})]})}function b(e){var t,a;const s=(null!==(t=(0,r.bG)("locales"))&&void 0!==t?t:"en").toLowerCase(),l=(null!==(a=(0,r.bG)("daily"))&&void 0!==a?a:"off").toLowerCase(),{trial_days:n,payment_interval:c,trial_price:i,currency:o,currency_symbol:d}=e;let{display_txt2:u,price:p}=e;if(n>0&&i>0&&"en"===s){let e=p,t="month";"on"===l&&(e=parseFloat(p/("Yearly"===c?365:30)).toFixed(2),t="day<br>(billed ".concat(d+p," ").concat(c,")")),u+="<div>".concat(n,"-Day Trial, then only ").concat(m(o,e)," per ").concat(t,"</div>")}return u}},27250:(e,t,a)=>{a.r(t),a.d(t,{default:()=>i});var r=a(72791),s=a(72608),l=a(19878),n=a(28891),c=(a(29534),a(80184));const i=function(e){let{hideNavLink:t,auth:i=(0,n.gx)()}=e;const o=window.location.pathname,d=/\/start-chatgpt-go\/?$/.test(o),m=/\/text-to-image\/?$/.test(o),u=/\/start-chatgpt-v2\/?$/.test(o),p=o.includes("/register"),x=!t;(0,r.useEffect)((()=>{const e=h(s.Z,"image"),t=h(l,"image");return document.head.append(e,t),p||Promise.all([a.e(7749),a.e(1707)]).then(a.bind(a,51707)),()=>{e.remove(),t.remove()}}),[p]);const h=(e,t)=>{const a=document.createElement("link");return a.rel="preload",a.href=e,a.as=t,a};return(0,c.jsxs)(c.Fragment,{children:["",(0,c.jsx)("header",{id:"header",className:"headernav border-gray-500 bg-white text-black w-3/4 mx-auto rounded-3xl mt-3 py-2 z-[8888] ".concat(""),children:(0,c.jsxs)("div",{className:"container mx-auto flex justify-between items-center px-4",children:[(0,c.jsxs)("picture",{className:"aiprologo",children:[(0,c.jsx)("source",{type:"image/webp",srcSet:l,width:"150",height:"52",className:"aiprologo"}),(0,c.jsx)("img",{src:s.Z,alt:"AI-Pro Logo",className:"aiprologo"})]}),(d||m||u)&&x&&(0,c.jsx)("nav",{className:"text-xs lg:text-sm block inline-flex",id:"menu",children:(0,c.jsx)("ul",{className:"headnav flex inline-flex",children:(0,c.jsx)("li",{className:"mr-1 md:mr-2 lg:mr-6",children:(0,c.jsx)("a",{href:i?"/my-account":"/login",className:"font-bold","aria-label":i?"my-account":"login",children:i?"My Apps":"LOG IN"})})})})]})})]})}},41244:(e,t,a)=>{a.r(t),a.d(t,{default:()=>j});var r=a(72791),s=(a(39832),a(19886)),l=a(27250),n=a(56355),c=a(53647),i=a(28891),o=a(74335),d=a(91933),m=a(31243),u=a(34492),p=a(54270),x=a(95828),h=a.n(x),b=(a(92831),a(80184));const g=(0,o.bG)("pricing")?(0,o.bG)("pricing"):"",y=(0,o.bG)("access")?(0,o.bG)("access"):"",f=(0,o.bG)("cta_pmt")?(0,o.bG)("cta_pmt"):"";var v=null;async function w(){if(v)return v;const e=(await m.Z.post("".concat("http://localhost:9002/api","/get-plan"),{plan_id:g},{headers:{"content-type":"application/x-www-form-urlencoded"}})).data;return e.success?(v=e.data,e.data):[]}const j=function(){(0,r.useEffect)((()=>{h().options={positionClass:"toast-top-center"}}),[]);const{data:e}=(0,d.useQuery)("users",w),[t,a]=(0,r.useState)(""),[x,j]=(0,r.useState)(""),[N,S]=(0,r.useState)(""),[C,L]=(0,r.useState)(""),[_,k]=(0,r.useState)(""),[F,D]=(0,r.useState)(""),[U,O]=(0,r.useState)(""),[M,E]=(0,r.useState)(""),[P,z]=(0,r.useState)(""),[A,G]=(0,r.useState)(""),[I,q]=(0,r.useState)(""),[T,Y]=(0,r.useState)(""),[Z,$]=(0,r.useState)(!0),K=(0,i.gx)("/register");if(void 0===K||!1===K)return;if(Z&&"active"===K.status)return void(window.location.href="/my-account");var J=new Date;J.setTime(J.getTime()+2592e6);var B=new Date,V=new Date;if(V.setDate(B.getDate()+30),e&&e.currency&&e.price){var H=e.price;""!==e.trial_price&&(H=e.trial_price),(0,o.I1)("currency",e.currency,{expires:V,path:"/"}),(0,o.I1)("currency",e.currency,{expires:V,domain:".ai-pro.org",path:"/"}),(0,o.I1)("amount",H,{expires:V,path:"/"}),(0,o.I1)("amount",H,{expires:V,domain:".ai-pro.org",path:"/"})}return(0,b.jsxs)(b.Fragment,{children:[(0,b.jsxs)(p.q,{children:[(0,b.jsx)("title",{children:"AI Pro | Payment Option"}),(0,b.jsx)("meta",{name:"description",content:"Safely complete your purchase with our secure payment options. Buy now with confidence!"})]}),(0,b.jsx)(l.default,{auth:K}),e?(0,b.jsx)("div",{className:"Payment bg-gray-100 md:min-h-[90vh] flex md:pt-[50px]",children:(0,b.jsx)("div",{className:"px-4 mx-auto py-10",children:(0,b.jsx)("div",{className:"flex flex-col items-center py-10 lg:py-16",children:(0,b.jsxs)("div",{className:"flex flex-wrap md:flex-wrap justify-center w-full",children:[(0,b.jsx)("div",{className:"pay_left px-4 mb-8 w-full md:w-1/2",children:(0,b.jsx)("div",{className:"bg-white rounded-lg shadow-lg overflow-hidden",children:(0,b.jsxs)("div",{className:"px-6 pb-10",children:[(0,b.jsxs)("div",{className:"",children:[(0,b.jsx)("h2",{className:"text-xl font-bold mb-4 pt-10",children:"Enter Billing Details"}),(0,b.jsxs)("div",{className:"mb-4",children:[(0,b.jsxs)("label",{className:"text-xs block mb-0 mt-1",htmlFor:"name",children:["Name on Card ",(0,b.jsx)("span",{className:"text-red-500",children:"*"}),_&&(0,b.jsx)("span",{className:"text-red-500 text-xs text-left w-full mb-2 inline",children:_})]}),(0,b.jsx)("input",{className:"w-full px-3 py-2 border border-gray-300 rounded",type:"text",id:"name",name:"name",placeholder:"John Doe",value:t,onChange:e=>{let t=e.target.value;t=t.replace(/[^A-Za-z ]/g,""),t=t.slice(0,50),a(t)},onKeyUp:e=>{a(e.target.value)}})]}),(0,b.jsxs)("div",{className:"mb-4",children:[(0,b.jsxs)("label",{className:"text-xs block mb-0 mt-1",htmlFor:"card-number",children:["Card Number ",(0,b.jsx)("span",{className:"text-red-500",children:"*"}),F&&(0,b.jsx)("span",{className:"text-red-500 text-xs text-left w-full mb-2 inline",children:F})]}),(0,b.jsx)("input",{className:"w-full px-3 py-2 border border-gray-300 rounded fs-exclude",type:"text",id:"card-number",name:"card-number",placeholder:"1234 5678 9012 3456",value:x,onChange:e=>{let t=e.target.value;t=t.replace(/\D/g,""),t=t.replace(/-/g,""),t=t.replace(/(\d{4})/g,"$1-"),t=t.replace(/-$/,""),t=t.slice(0,19),j(t)},onKeyUp:e=>{j(e.target.value)}})]}),(0,b.jsxs)("div",{className:"mb-4 md:flex",children:[(0,b.jsxs)("div",{className:"expdate w-full md:w-2/3 mr-2 md:mr-5",children:[(0,b.jsxs)("label",{className:"text-xs block mb-0 mt-1",htmlFor:"expiration-date",children:["Expiration Date ",(0,b.jsx)("span",{className:"text-red-500",children:"*"}),U&&(0,b.jsx)("span",{className:"text-red-500 text-xs text-left w-full mb-2 inline",children:U})]}),(0,b.jsx)("input",{className:"w-full px-3 py-2 border border-gray-300 rounded fs-exclude",type:"text",id:"expiration-date",name:"expiration-date",placeholder:"MM/YY",value:N,onChange:e=>{let t=e.target.value;t=t.replace(/\D/g,""),t=t.slice(0,4),t.length>=3&&(t=t.slice(0,2)+"/"+t.slice(2)),S(t)},onKeyUp:e=>{S(e.target.value)}})]}),(0,b.jsxs)("div",{className:" w-full sm:w-full md:w-2/4 md:flex",children:[(0,b.jsxs)("div",{className:"cvv w-full mr-2 md:mr-5",children:[(0,b.jsxs)("label",{className:"text-xs block mb-0 mt-1",htmlFor:"cvv",children:["CVV ",(0,b.jsx)("span",{className:"text-red-500",children:"*"}),M&&(0,b.jsx)("span",{className:"text-red-500 text-xs text-left w-full mb-2 inline",children:M})]}),(0,b.jsx)("input",{className:"w-full px-3 py-2 border border-gray-300 rounded fs-exclude",type:"text",id:"cvv",name:"cvv",placeholder:"CVV",value:C,onChange:e=>{let t=e.target.value;t=t.replace(/\D/g,""),t=t.slice(0,5),L(t)},onKeyUp:e=>{L(e.target.value)}})]}),(0,b.jsxs)("div",{className:"zip w-full w-6/6",children:[(0,b.jsxs)("label",{className:"text-xs block mb-0 mt-1",htmlFor:"zip",children:["Zip ",(0,b.jsx)("span",{className:"text-red-500",children:"*"}),A&&(0,b.jsx)("span",{className:"text-red-500 text-xs text-left w-full mb-2 inline",children:A})]}),(0,b.jsx)("input",{className:"w-full px-3 py-2 border border-gray-300 rounded fs-exclude",type:"text",id:"zip",name:"zip",placeholder:"ZIP",value:P,onChange:e=>{let t=e.target.value;z(t)},onKeyUp:e=>{z(e.target.value)}})]})]})]}),(0,b.jsxs)("div",{className:"mb-4",children:[(0,b.jsxs)("label",{className:"text-xs block mb-0 mt-1",htmlFor:"phone-number",children:["Phone Number ",(0,b.jsx)("span",{className:"text-red-500",children:"*"}),T&&(0,b.jsx)("span",{className:"text-red-500 text-xs text-left w-full mb-2 inline",children:T})]}),(0,b.jsx)("input",{className:"w-full px-3 py-2 border border-gray-300 rounded fs-exclude",type:"text",id:"phone-number",name:"phone-number",placeholder:"1234567890",value:I,onChange:e=>{let t=e.target.value;t=t.replace(/\D/g,""),q(t)},onKeyUp:e=>{q(e.target.value)}})]}),(0,b.jsx)(s.E.button,{className:"bg-blue-500 text-white font-bold py-3 px-6 rounded-lg w-full my-4 proceed-pmt",whileHover:{backgroundColor:"#5997fd"},whileTap:{scale:.9},onClick:()=>{$(!1),k(""),D(""),O(""),E("");let e=!0;if(t&&0!==t.length?t.includes(" ")||(k("enter at least two names separated by a space"),e=!1):(k("required"),e=!1),x||(D("required"),e=!1),N&&/^(0[1-9]|1[0-2])\/\d{2}$/.test(N)||(O("MM/YY"),e=!1),C&&/^\d{3,5}$/.test(C)||(E("required"),e=!1),P||(G("required"),e=!1),I||(Y("required"),e=!1),e){document.querySelector(".loader-container").classList.add("active");var a=t.split(" "),r=a[0],s=a[a.length-1],l=N.split("/")[0],n=N.split("/")[1];m.Z.post("".concat("http://localhost:9002/api","/t/create-subscription"),{tk:y,first_name:r,last_name:s,cc:x,ccmonth:l,ccyr:"20"+n,cvv:C,plan_id:g,postal:P,phone:I,gateway:"2"},{headers:{"content-type":"application/x-www-form-urlencoded"}}).then((function(e){let t=e.data;if(t.success)return h().success("Success"),(0,o.Sz)("pricing"),void(window.location.href="/thankyou/?plan="+v.label.replace(" ","").replace(" ",""));document.querySelector(".loader-container").classList.remove("active"),t.data&&h().error(t.data.msg)})).catch((function(e){e.response&&429===e.response.status&&(document.querySelector(".loader-container").classList.remove("active"),h().error("Sorry, too many requests. Please try again in a bit!"))}))}},children:f||"Complete Purchase"})]}),(0,b.jsxs)("span",{className:"text-[12px] text-gray-600",children:[(0,b.jsx)(n.DAO,{className:"inline text-lg mr-1"})," By clicking the “",f||"Complete Purchase","” button, I have read and agreed to the Terms and Conditions."]})]})})}),(0,b.jsxs)("div",{className:"pay_right px-4 mb-8 md:w-2/5",children:[(0,b.jsxs)("div",{className:"border px-8 rounded border-gray-300",children:[(0,b.jsx)("h2",{className:"text-xl font-bold mb-4 pt-10 pb-0",children:"Order Summary"}),(0,b.jsxs)("div",{className:"py-5",children:[(0,b.jsx)("div",{className:"mb-2 text-sm pb-4 border-b border-gray-300",children:(0,b.jsx)("b",{className:"text-lg text-uppercase",children:e.plan_type_display})}),(0,b.jsxs)("div",{className:"flex flex-wrap mb-2 text-sm mt-4 mr-6",children:[(0,b.jsx)("div",{className:"text-lg font-bold mt-4 w-1/2",children:"TOTAL:"}),(0,b.jsx)("div",{className:"text-lg font-bold mt-4 w-1/2 text-right",children:(0,u.yt)(e.currency,(0,u.aS)(e))})]}),(0,b.jsx)("div",{className:"mb-8 text-sm mt-6",children:e.display_txt3?e.display_txt3:"Your subscription will renew monthly until you cancel it."})]})]}),(0,b.jsxs)("div",{className:"securecont block p-5 mx-auto text-left",children:[(0,b.jsxs)("div",{className:"securetext mb-2 text-sm w-full",children:[(0,b.jsx)(n.kUi,{className:"inline text-lg mr-1 text-orange-500 text-xs"})," Secure Checkout"]}),(0,b.jsxs)("div",{className:"securelogo mb-2 text-sm w-full flex flex-wrap justify-center items-center",children:[(0,b.jsx)("img",{src:c,alt:"Secure Logo",className:"cclogo inline"}),(0,b.jsx)("button",{onClick:()=>{window.open("//www.securitymetrics.com/site_certificate?id=1982469&tk=d41c416c6208f1136426bc8038178d2e","Verification","location=no, toolbar=no, resizable=no, scrollbars=yes, directories=no, status=no,top=100,left=100, width=700, height=600")},title:"SecurityMetrics card safe certification",className:"h-20",children:(0,b.jsx)("img",{loading:"lazy",src:"https://www.securitymetrics.com/portal/app/ngsm/assets/img/GreyContent_Credit_Card_Safe_White_Sqr.png",alt:"SecurityMetrics card safe certification logo",className:"max-h-full",width:"80",height:"80"})})]})]})]})]})})})}):""]})}},89983:(e,t,a)=>{a.d(t,{w_:()=>o});var r=a(72791),s={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},l=r.createContext&&r.createContext(s),n=function(){return n=Object.assign||function(e){for(var t,a=1,r=arguments.length;a<r;a++)for(var s in t=arguments[a])Object.prototype.hasOwnProperty.call(t,s)&&(e[s]=t[s]);return e},n.apply(this,arguments)},c=function(e,t){var a={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(a[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var s=0;for(r=Object.getOwnPropertySymbols(e);s<r.length;s++)t.indexOf(r[s])<0&&Object.prototype.propertyIsEnumerable.call(e,r[s])&&(a[r[s]]=e[r[s]])}return a};function i(e){return e&&e.map((function(e,t){return r.createElement(e.tag,n({key:t},e.attr),i(e.child))}))}function o(e){return function(t){return r.createElement(d,n({attr:n({},e.attr)},t),i(e.child))}}function d(e){var t=function(t){var a,s=e.attr,l=e.size,i=e.title,o=c(e,["attr","size","title"]),d=l||t.size||"1em";return t.className&&(a=t.className),e.className&&(a=(a?a+" ":"")+e.className),r.createElement("svg",n({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,s,o,{className:a,style:n(n({color:e.color||t.color},t.style),e.style),height:d,width:d,xmlns:"http://www.w3.org/2000/svg"}),i&&r.createElement("title",null,i),e.children)};return void 0!==l?r.createElement(l.Consumer,null,(function(e){return t(e)})):t(s)}},29534:()=>{},39832:()=>{},19878:(e,t,a)=>{e.exports=a.p+"static/media/AIPRO.84104dfd05446283b05c.webp"},53647:(e,t,a)=>{e.exports=a.p+"static/media/cc_v3.6ab0d1e0b9d27a1d2bc0.png"}}]);
//# sourceMappingURL=3154.1f814569.chunk.js.map