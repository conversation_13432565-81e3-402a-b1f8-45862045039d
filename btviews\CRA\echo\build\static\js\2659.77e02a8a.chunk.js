"use strict";(self.webpackChunkv1=self.webpackChunkv1||[]).push([[2659],{75740:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0});t.ContentRect=function(e){if("getBBox"in e){var t=e.getBBox();return Object.freeze({height:t.height,left:0,top:0,width:t.width})}var n=window.getComputedStyle(e);return Object.freeze({height:parseFloat(n.height||"0"),left:parseFloat(n.paddingLeft||"0"),top:parseFloat(n.paddingTop||"0"),width:parseFloat(n.width||"0")})}},50879:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0});var r=n(75740),o=function(){function e(e){this.target=e,this.$$broadcastWidth=this.$$broadcastHeight=0}return Object.defineProperty(e.prototype,"broadcastWidth",{get:function(){return this.$$broadcastWidth},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"broadcastHeight",{get:function(){return this.$$broadcastHeight},enumerable:!0,configurable:!0}),e.prototype.isActive=function(){var e=r.ContentRect(this.target);return!!e&&(e.width!==this.broadcastWidth||e.height!==this.broadcastHeight)},e}();t.ResizeObservation=o},85187:(e,t,n)=>{var r=n(50879),o=n(40996),i=[],a=function(){function e(e){this.$$observationTargets=[],this.$$activeTargets=[],this.$$skippedTargets=[];var t=function(e){if(void 0===e)return"Failed to construct 'ResizeObserver': 1 argument required, but only 0 present.";if("function"!=typeof e)return"Failed to construct 'ResizeObserver': The callback provided as parameter 1 is not a function."}(e);if(t)throw TypeError(t);this.$$callback=e}return e.prototype.observe=function(e){var t=l("observe",e);if(t)throw TypeError(t);s(this.$$observationTargets,e)>=0||(this.$$observationTargets.push(new r.ResizeObservation(e)),function(e){var t=i.indexOf(e);t<0&&(i.push(e),g())}(this))},e.prototype.unobserve=function(e){var t=l("unobserve",e);if(t)throw TypeError(t);var n=s(this.$$observationTargets,e);n<0||(this.$$observationTargets.splice(n,1),0===this.$$observationTargets.length&&u(this))},e.prototype.disconnect=function(){this.$$observationTargets=[],this.$$activeTargets=[],u(this)},e}();function u(e){var t=i.indexOf(e);t>=0&&(i.splice(t,1),b())}function l(e,t){return void 0===t?"Failed to execute '"+e+"' on 'ResizeObserver': 1 argument required, but only 0 present.":t&&t.nodeType===window.Node.ELEMENT_NODE?void 0:"Failed to execute '"+e+"' on 'ResizeObserver': parameter 1 is not of type 'Element'."}function s(e,t){for(var n=0;n<e.length;n+=1)if(e[n].target===t)return n;return-1}var c,d=function(e){i.forEach(function(t){t.$$activeTargets=[],t.$$skippedTargets=[],t.$$observationTargets.forEach(function(n){n.isActive()&&(v(n.target)>e?t.$$activeTargets.push(n):t.$$skippedTargets.push(n))})})},f=function(){return i.some(function(e){return!!e.$$activeTargets.length})},p=function(){var e=1/0;return i.forEach(function(t){if(t.$$activeTargets.length){var n=[];t.$$activeTargets.forEach(function(t){var r=new o.ResizeObserverEntry(t.target);n.push(r),t.$$broadcastWidth=r.contentRect.width,t.$$broadcastHeight=r.contentRect.height;var i=v(t.target);i<e&&(e=i)}),t.$$callback(n,t),t.$$activeTargets=[]}}),e},v=function(e){for(var t=0;e.parentNode;)e=e.parentNode,t+=1;return t},m=function(){var e,t=0;for(d(t);f();)t=p(),d(t);i.some(function(e){return!!e.$$skippedTargets.length})&&(e=new window.ErrorEvent("ResizeLoopError",{message:"ResizeObserver loop completed with undelivered notifications."}),window.dispatchEvent(e))},g=function(){c||h()},h=function(){c=window.requestAnimationFrame(function(){m(),h()})},b=function(){c&&!i.some(function(e){return!!e.$$observationTargets.length})&&(window.cancelAnimationFrame(c),c=void 0)};t.N9=function(){return window.ResizeObserver=a}},40996:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0});var r=n(75740),o=function(e){this.target=e,this.contentRect=r.ContentRect(e)};t.ResizeObserverEntry=o},2777:(e,t,n)=>{n.d(t,{V:()=>Ae});var r=n(72791),o=n.t(r,2),i=n(79904),a=n(15612),u=n(94159),l=n(17369),s=n(72953),c=n(47003),d=n(81511);var f=(e=>(e[e.None=1]="None",e[e.Focusable=2]="Focusable",e[e.Hidden=4]="Hidden",e))(f||{});let p=(0,a.yV)(function(e,t){let{features:n=1,...r}=e,o={ref:t,"aria-hidden":!(2&~n)||void 0,style:{position:"fixed",top:1,left:1,width:1,height:0,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0",...!(4&~n)&&!!(2&~n)&&{display:"none"}}};return(0,a.sY)({ourProps:o,theirProps:r,slot:{},defaultTag:"div",name:"Hidden"})});var v=n(60981),m=n(23654),g=n(70642),h=(e=>(e[e.Forwards=0]="Forwards",e[e.Backwards=1]="Backwards",e))(h||{});var b=n(11262),E=n(37281),w=n(22806);function y(e,t,n,o){let i=(0,w.E)(n);(0,r.useEffect)(()=>{function n(e){i.current(e)}return(e=null!=e?e:window).addEventListener(t,n,o),()=>e.removeEventListener(t,n,o)},[e,t,o])}var T=n(28106);function R(e,t){let n=(0,r.useRef)([]),o=(0,m.z)(e);(0,r.useEffect)(()=>{let e=[...n.current];for(let[r,i]of t.entries())if(n.current[r]!==i){let r=o(t,e);return n.current=t,r}},[o,...t])}var P=n(5623);function M(e){let t=(0,m.z)(e),n=(0,r.useRef)(!1);(0,r.useEffect)(()=>(n.current=!1,()=>{n.current=!0,(0,T.Y)(()=>{n.current&&t()})}),[t])}function S(e){if(!e)return new Set;if("function"==typeof e)return new Set(e());let t=new Set;for(let n of e.current)n.current instanceof HTMLElement&&t.add(n.current);return t}var x=(e=>(e[e.None=1]="None",e[e.InitialFocus=2]="InitialFocus",e[e.TabLock=4]="TabLock",e[e.FocusLock=8]="FocusLock",e[e.RestoreFocus=16]="RestoreFocus",e[e.All=30]="All",e))(x||{});let I=(0,a.yV)(function(e,t){let n=(0,r.useRef)(null),o=(0,u.T)(n,t),{initialFocus:l,containers:s,features:c=30,...w}=e;(0,d.H)()||(c=1);let T=(0,E.i)(n);D({ownerDocument:T},Boolean(16&c));let R=L({ownerDocument:T,container:n,initialFocus:l},Boolean(2&c));!function(e,t){let{ownerDocument:n,container:r,containers:o,previousActiveElement:i}=e,a=(0,b.t)();y(null==n?void 0:n.defaultView,"focus",e=>{if(!t||!a.current)return;let n=S(o);r.current instanceof HTMLElement&&n.add(r.current);let u=i.current;if(!u)return;let l=e.target;l&&l instanceof HTMLElement?C(n,l)?(i.current=l,(0,v.C5)(l)):(e.preventDefault(),e.stopPropagation(),(0,v.C5)(u)):(0,v.C5)(i.current)},!0)}({ownerDocument:T,container:n,containers:s,previousActiveElement:R},Boolean(8&c));let M=function(){let e=(0,r.useRef)(0);return(0,g.s)("keydown",t=>{"Tab"===t.key&&(e.current=t.shiftKey?1:0)},!0),e}(),x=(0,m.z)(e=>{let t=n.current;t&&(0,i.E)(M.current,{[h.Forwards]:()=>{(0,v.jA)(t,v.TO.First,{skipElements:[e.relatedTarget]})},[h.Backwards]:()=>{(0,v.jA)(t,v.TO.Last,{skipElements:[e.relatedTarget]})}})}),I=(0,P.G)(),O=(0,r.useRef)(!1),F={ref:o,onKeyDown(e){"Tab"==e.key&&(O.current=!0,I.requestAnimationFrame(()=>{O.current=!1}))},onBlur(e){let t=S(s);n.current instanceof HTMLElement&&t.add(n.current);let r=e.relatedTarget;r instanceof HTMLElement&&"true"!==r.dataset.headlessuiFocusGuard&&(C(t,r)||(O.current?(0,v.jA)(n.current,(0,i.E)(M.current,{[h.Forwards]:()=>v.TO.Next,[h.Backwards]:()=>v.TO.Previous})|v.TO.WrapAround,{relativeTo:e.target}):e.target instanceof HTMLElement&&(0,v.C5)(e.target)))}};return r.createElement(r.Fragment,null,Boolean(4&c)&&r.createElement(p,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:x,features:f.Focusable}),(0,a.sY)({ourProps:F,theirProps:w,defaultTag:"div",name:"FocusTrap"}),Boolean(4&c)&&r.createElement(p,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:x,features:f.Focusable}))}),O=Object.assign(I,{features:x}),F=[];function D(e,t){let{ownerDocument:n}=e,o=function(){let e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],t=(0,r.useRef)(F.slice());return R((e,n)=>{let[r]=e,[o]=n;!0===o&&!1===r&&(0,T.Y)(()=>{t.current.splice(0)}),!1===o&&!0===r&&(t.current=F.slice())},[e,F,t]),(0,m.z)(()=>{var e;return null!=(e=t.current.find(e=>null!=e&&e.isConnected))?e:null})}(t);R(()=>{t||(null==n?void 0:n.activeElement)===(null==n?void 0:n.body)&&(0,v.C5)(o())},[t]),M(()=>{t&&(0,v.C5)(o())})}function L(e,t){let{ownerDocument:n,container:o,initialFocus:i}=e,a=(0,r.useRef)(null),u=(0,b.t)();return R(()=>{if(!t)return;let e=o.current;e&&(0,T.Y)(()=>{if(!u.current)return;let t=null==n?void 0:n.activeElement;if(null!=i&&i.current){if((null==i?void 0:i.current)===t)return void(a.current=t)}else if(e.contains(t))return void(a.current=t);null!=i&&i.current?(0,v.C5)(i.current):(0,v.jA)(e,v.TO.First)===v.fE.Error&&console.warn("There are no focusable elements inside the <FocusTrap />"),a.current=null==n?void 0:n.activeElement})},[t]),a}function C(e,t){for(let n of e)if(n.contains(t))return!0;return!1}!function(e){function t(){"loading"!==document.readyState&&(e(),document.removeEventListener("DOMContentLoaded",t))}"undefined"!=typeof window&&"undefined"!=typeof document&&(document.addEventListener("DOMContentLoaded",t),t())}(()=>{function e(e){e.target instanceof HTMLElement&&e.target!==document.body&&F[0]!==e.target&&(F.unshift(e.target),F=F.filter(e=>null!=e&&e.isConnected),F.splice(10))}window.addEventListener("click",e,{capture:!0}),window.addEventListener("mousedown",e,{capture:!0}),window.addEventListener("focus",e,{capture:!0}),document.body.addEventListener("click",e,{capture:!0}),document.body.addEventListener("mousedown",e,{capture:!0}),document.body.addEventListener("focus",e,{capture:!0})});var A=n(54164),k=n(84705);let $=(0,r.createContext)(!1);function N(){return(0,r.useContext)($)}function z(e){return r.createElement($.Provider,{value:e.force},e.children)}var H=n(43784);let _=r.Fragment;let B=r.Fragment,V=(0,r.createContext)(null);let j=(0,r.createContext)(null);function Y(){let e=(0,r.useContext)(j),t=(0,r.useRef)([]),n=(0,m.z)(n=>(t.current.push(n),e&&e.register(n),()=>o(n))),o=(0,m.z)(n=>{let r=t.current.indexOf(n);-1!==r&&t.current.splice(r,1),e&&e.unregister(n)}),i=(0,r.useMemo)(()=>({register:n,unregister:o,portals:t}),[n,o,t]);return[t,(0,r.useMemo)(()=>function(e){let{children:t}=e;return r.createElement(j.Provider,{value:i},t)},[i])]}let U=(0,a.yV)(function(e,t){let n=e,o=(0,r.useRef)(null),i=(0,u.T)((0,u.h)(e=>{o.current=e}),t),l=(0,E.i)(o),s=function(e){let t=N(),n=(0,r.useContext)(V),o=(0,E.i)(e),[i,a]=(0,r.useState)(()=>{if(!t&&null!==n||H.O.isServer)return null;let e=null==o?void 0:o.getElementById("headlessui-portal-root");if(e)return e;if(null===o)return null;let r=o.createElement("div");return r.setAttribute("id","headlessui-portal-root"),o.body.appendChild(r)});return(0,r.useEffect)(()=>{null!==i&&(null!=o&&o.body.contains(i)||null==o||o.body.appendChild(i))},[i,o]),(0,r.useEffect)(()=>{t||null!==n&&a(n.current)},[n,a,t]),i}(o),[c]=(0,r.useState)(()=>{var e;return H.O.isServer?null:null!=(e=null==l?void 0:l.createElement("div"))?e:null}),f=(0,r.useContext)(j),p=(0,d.H)();return(0,k.e)(()=>{!s||!c||s.contains(c)||(c.setAttribute("data-headlessui-portal",""),s.appendChild(c))},[s,c]),(0,k.e)(()=>{if(c&&f)return f.register(c)},[f,c]),M(()=>{var e;!s||!c||(c instanceof Node&&s.contains(c)&&s.removeChild(c),s.childNodes.length<=0&&(null==(e=s.parentElement)||e.removeChild(s)))}),p&&s&&c?(0,A.createPortal)((0,a.sY)({ourProps:{ref:i},theirProps:n,defaultTag:_,name:"Portal"}),c):null}),W=(0,a.yV)(function(e,t){let{target:n,...o}=e,i={ref:(0,u.T)(t)};return r.createElement(V.Provider,{value:n},(0,a.sY)({ourProps:i,theirProps:o,defaultTag:B,name:"Popover.Group"}))}),G=Object.assign(U,{Group:W}),K=(0,r.createContext)(null);function q(){let e=(0,r.useContext)(K);if(null===e){let e=new Error("You used a <Description /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(e,q),e}return e}let Q=(0,a.yV)(function(e,t){let n=(0,c.M)(),{id:r=`headlessui-description-${n}`,...o}=e,i=q(),l=(0,u.T)(t);(0,k.e)(()=>i.register(r),[r,i.register]);let s={ref:l,...i.props,id:r};return(0,a.sY)({ourProps:s,theirProps:o,slot:i.slot||{},defaultTag:"p",name:i.name||"Description"})}),Z=Object.assign(Q,{});var J=n(4510);let X=(0,r.createContext)(()=>{});X.displayName="StackContext";var ee=(e=>(e[e.Add=0]="Add",e[e.Remove=1]="Remove",e))(ee||{});function te(e){let{children:t,onUpdate:n,type:o,element:i,enabled:a}=e,u=(0,r.useContext)(X),l=(0,m.z)(function(){null==n||n(...arguments),u(...arguments)});return(0,k.e)(()=>{let e=void 0===a||!0===a;return e&&l(0,o,i),()=>{e&&l(1,o,i)}},[l,o,i,a]),r.createElement(X.Provider,{value:l},t)}var ne=n(19541);const re="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},{useState:oe,useEffect:ie,useLayoutEffect:ae,useDebugValue:ue}=o;function le(e){const t=e.getSnapshot,n=e.value;try{const e=t();return!re(n,e)}catch{return!0}}"undefined"!=typeof window&&void 0!==window.document&&window.document.createElement;const se=(e=>e.useSyncExternalStore)(o);var ce=n(56958);function de(){let e;return{before(t){let{doc:n}=t;var r;let o=n.documentElement;e=(null!=(r=n.defaultView)?r:window).innerWidth-o.clientWidth},after(t){let{doc:n,d:r}=t,o=n.documentElement,i=o.clientWidth-o.offsetWidth,a=e-i;r.style(o,"paddingRight",`${a}px`)}}}function fe(){return/iPhone/gi.test(window.navigator.platform)||/Mac/gi.test(window.navigator.platform)&&window.navigator.maxTouchPoints>0}function pe(){if(!fe())return{};let e;return{before(){e=window.pageYOffset},after(t){let{doc:n,d:r,meta:o}=t;function i(e){return o.containers.flatMap(e=>e()).some(t=>t.contains(e))}r.style(n.body,"marginTop",`-${e}px`),window.scrollTo(0,0);let a=null;r.addEventListener(n,"click",e=>{if(e.target instanceof HTMLElement)try{let t=e.target.closest("a");if(!t)return;let{hash:r}=new URL(t.href),o=n.querySelector(r);o&&!i(o)&&(a=o)}catch{}},!0),r.addEventListener(n,"touchmove",e=>{e.target instanceof HTMLElement&&!i(e.target)&&e.preventDefault()},{passive:!1}),r.add(()=>{window.scrollTo(0,window.pageYOffset+e),a&&a.isConnected&&(a.scrollIntoView({block:"nearest"}),a=null)})}}}function ve(e){let t={};for(let n of e)Object.assign(t,n(t));return t}let me=function(e,t){let n=e(),r=new Set;return{getSnapshot:()=>n,subscribe:e=>(r.add(e),()=>r.delete(e)),dispatch(e){for(var o=arguments.length,i=new Array(o>1?o-1:0),a=1;a<o;a++)i[a-1]=arguments[a];let u=t[e].call(n,...i);u&&(n=u,r.forEach(e=>e()))}}}(()=>new Map,{PUSH(e,t){var n;let r=null!=(n=this.get(e))?n:{doc:e,count:0,d:(0,ce.k)(),meta:new Set};return r.count++,r.meta.add(t),this.set(e,r),this},POP(e,t){let n=this.get(e);return n&&(n.count--,n.meta.delete(t)),this},SCROLL_PREVENT(e){let{doc:t,d:n,meta:r}=e,o={doc:t,d:n,meta:ve(r)},i=[pe(),de(),{before(e){let{doc:t,d:n}=e;n.style(t.documentElement,"overflow","hidden")}}];i.forEach(e=>{let{before:t}=e;return null==t?void 0:t(o)}),i.forEach(e=>{let{after:t}=e;return null==t?void 0:t(o)})},SCROLL_ALLOW(e){let{d:t}=e;t.dispose()},TEARDOWN(e){let{doc:t}=e;this.delete(t)}});function ge(e,t,n){let r=function(e){return se(e.subscribe,e.getSnapshot,e.getSnapshot)}(me),o=e?r.get(e):void 0,i=!!o&&o.count>0;return(0,k.e)(()=>{if(e&&t)return me.dispatch("PUSH",e,n),()=>me.dispatch("POP",e,n)},[t,e]),i}me.subscribe(()=>{let e=me.getSnapshot(),t=new Map;for(let[n]of e)t.set(n,n.documentElement.style.overflow);for(let n of e.values()){let e="hidden"===t.get(n.doc),r=0!==n.count;(r&&!e||!r&&e)&&me.dispatch(n.count>0?"SCROLL_PREVENT":"SCROLL_ALLOW",n),0===n.count&&me.dispatch("TEARDOWN",n)}});let he=new Map,be=new Map;function Ee(e){let t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];(0,k.e)(()=>{var n;if(!t)return;let r="function"==typeof e?e():e.current;if(!r)return;let o=null!=(n=be.get(r))?n:0;return be.set(r,o+1),0!==o||(he.set(r,{"aria-hidden":r.getAttribute("aria-hidden"),inert:r.inert}),r.setAttribute("aria-hidden","true"),r.inert=!0),function(){var e;if(!r)return;let t=null!=(e=be.get(r))?e:1;if(1===t?be.delete(r):be.set(r,t-1),1!==t)return;let n=he.get(r);n&&(null===n["aria-hidden"]?r.removeAttribute("aria-hidden"):r.setAttribute("aria-hidden",n["aria-hidden"]),r.inert=n.inert,he.delete(r))}},[e,t])}function we(){let{defaultContainers:e=[],portals:t}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=(0,r.useRef)(null),o=(0,E.i)(n),i=(0,m.z)(()=>{var r;let i=[];for(let t of e)null!==t&&(t instanceof HTMLElement?i.push(t):"current"in t&&t.current instanceof HTMLElement&&i.push(t.current));if(null!=t&&t.current)for(let e of t.current)i.push(e);for(let e of null!=(r=null==o?void 0:o.querySelectorAll("html > *, body > *"))?r:[])e!==document.body&&e!==document.head&&e instanceof HTMLElement&&"headlessui-portal-root"!==e.id&&(e.contains(n.current)||i.some(t=>e.contains(t))||i.push(e));return i});return{resolveContainers:i,contains:(0,m.z)(e=>i().some(t=>t.contains(e))),mainTreeNodeRef:n,MainTreeNode:(0,r.useMemo)(()=>function(){return r.createElement(p,{features:f.Hidden,ref:n})},[n])}}var ye,Te=((ye=Te||{})[ye.Open=0]="Open",ye[ye.Closed=1]="Closed",ye),Re=(e=>(e[e.SetTitleId=0]="SetTitleId",e))(Re||{});let Pe={0:(e,t)=>e.titleId===t.id?e:{...e,titleId:t.id}},Me=(0,r.createContext)(null);function Se(e){let t=(0,r.useContext)(Me);if(null===t){let t=new Error(`<${e} /> is missing a parent <Dialog /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,Se),t}return t}function xe(e,t){return(0,i.E)(t.type,Pe,e,t)}Me.displayName="DialogContext";let Ie=a.AN.RenderStrategy|a.AN.Static;let Oe=(0,a.yV)(function(e,t){var n;let o=(0,c.M)(),{id:s=`headlessui-dialog-${o}`,open:f,onClose:p,initialFocus:v,__demoMode:g=!1,...h}=e,[b,w]=(0,r.useState)(0),T=(0,J.oJ)();void 0===f&&null!==T&&(f=(T&J.ZM.Open)===J.ZM.Open);let R=(0,r.useRef)(null),P=(0,u.T)(R,t),M=(0,E.i)(R),S=e.hasOwnProperty("open")||null!==T,x=e.hasOwnProperty("onClose");if(!S&&!x)throw new Error("You have to provide an `open` and an `onClose` prop to the `Dialog` component.");if(!S)throw new Error("You provided an `onClose` prop to the `Dialog`, but forgot an `open` prop.");if(!x)throw new Error("You provided an `open` prop to the `Dialog`, but forgot an `onClose` prop.");if("boolean"!=typeof f)throw new Error(`You provided an \`open\` prop to the \`Dialog\`, but the value is not a boolean. Received: ${f}`);if("function"!=typeof p)throw new Error(`You provided an \`onClose\` prop to the \`Dialog\`, but the value is not a function. Received: ${p}`);let I=f?0:1,[F,D]=(0,r.useReducer)(xe,{titleId:null,descriptionId:null,panelRef:(0,r.createRef)()}),L=(0,m.z)(()=>p(!1)),C=(0,m.z)(e=>D({type:0,id:e})),A=!!(0,d.H)()&&(!g&&0===I),k=b>1,$=null!==(0,r.useContext)(Me),[N,H]=Y(),{resolveContainers:_,mainTreeNodeRef:B,MainTreeNode:V}=we({portals:N,defaultContainers:[null!=(n=F.panelRef.current)?n:R.current]}),j=k?"parent":"leaf",U=null!==T&&(T&J.ZM.Closing)===J.ZM.Closing,W=!$&&!U&&A,q=(0,r.useCallback)(()=>{var e,t;return null!=(t=Array.from(null!=(e=null==M?void 0:M.querySelectorAll("body > *"))?e:[]).find(e=>"headlessui-portal-root"!==e.id&&(e.contains(B.current)&&e instanceof HTMLElement)))?t:null},[B]);Ee(q,W);let Q=!!k||A,Z=(0,r.useCallback)(()=>{var e,t;return null!=(t=Array.from(null!=(e=null==M?void 0:M.querySelectorAll("[data-headlessui-portal]"))?e:[]).find(e=>e.contains(B.current)&&e instanceof HTMLElement))?t:null},[B]);Ee(Z,Q);let X=!(!A||k);(0,ne.O)(_,L,X);let re=!(k||0!==I);y(null==M?void 0:M.defaultView,"keydown",e=>{re&&(e.defaultPrevented||e.key===l.R.Escape&&(e.preventDefault(),e.stopPropagation(),L()))}),function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:()=>[document.body];ge(e,t,e=>{var t;return{containers:[...null!=(t=e.containers)?t:[],n]}})}(M,!(U||0!==I||$),_),(0,r.useEffect)(()=>{if(0!==I||!R.current)return;let e=new ResizeObserver(e=>{for(let t of e){let e=t.target.getBoundingClientRect();0===e.x&&0===e.y&&0===e.width&&0===e.height&&L()}});return e.observe(R.current),()=>e.disconnect()},[I,R,L]);let[oe,ie]=function(){let[e,t]=(0,r.useState)([]);return[e.length>0?e.join(" "):void 0,(0,r.useMemo)(()=>function(e){let n=(0,m.z)(e=>(t(t=>[...t,e]),()=>t(t=>{let n=t.slice(),r=n.indexOf(e);return-1!==r&&n.splice(r,1),n}))),o=(0,r.useMemo)(()=>({register:n,slot:e.slot,name:e.name,props:e.props}),[n,e.slot,e.name,e.props]);return r.createElement(K.Provider,{value:o},e.children)},[t])]}(),ae=(0,r.useMemo)(()=>[{dialogState:I,close:L,setTitleId:C},F],[I,F,L,C]),ue=(0,r.useMemo)(()=>({open:0===I}),[I]),le={ref:P,id:s,role:"dialog","aria-modal":0===I||void 0,"aria-labelledby":F.titleId,"aria-describedby":oe};return r.createElement(te,{type:"Dialog",enabled:0===I,element:R,onUpdate:(0,m.z)((e,t)=>{"Dialog"===t&&(0,i.E)(e,{[ee.Add]:()=>w(e=>e+1),[ee.Remove]:()=>w(e=>e-1)})})},r.createElement(z,{force:!0},r.createElement(G,null,r.createElement(Me.Provider,{value:ae},r.createElement(G.Group,{target:R},r.createElement(z,{force:!1},r.createElement(ie,{slot:ue,name:"Dialog.Description"},r.createElement(O,{initialFocus:v,containers:_,features:A?(0,i.E)(j,{parent:O.features.RestoreFocus,leaf:O.features.All&~O.features.FocusLock}):O.features.None},r.createElement(H,null,(0,a.sY)({ourProps:le,theirProps:h,slot:ue,defaultTag:"div",features:Ie,visible:0===I,name:"Dialog"}))))))))),r.createElement(V,null))}),Fe=(0,a.yV)(function(e,t){let n=(0,c.M)(),{id:o=`headlessui-dialog-backdrop-${n}`,...i}=e,[{dialogState:l},s]=Se("Dialog.Backdrop"),d=(0,u.T)(t);(0,r.useEffect)(()=>{if(null===s.panelRef.current)throw new Error("A <Dialog.Backdrop /> component is being used, but a <Dialog.Panel /> component is missing.")},[s.panelRef]);let f=(0,r.useMemo)(()=>({open:0===l}),[l]);return r.createElement(z,{force:!0},r.createElement(G,null,(0,a.sY)({ourProps:{ref:d,id:o,"aria-hidden":!0},theirProps:i,slot:f,defaultTag:"div",name:"Dialog.Backdrop"})))}),De=(0,a.yV)(function(e,t){let n=(0,c.M)(),{id:o=`headlessui-dialog-panel-${n}`,...i}=e,[{dialogState:l},s]=Se("Dialog.Panel"),d=(0,u.T)(t,s.panelRef),f=(0,r.useMemo)(()=>({open:0===l}),[l]),p=(0,m.z)(e=>{e.stopPropagation()});return(0,a.sY)({ourProps:{ref:d,id:o,onClick:p},theirProps:i,slot:f,defaultTag:"div",name:"Dialog.Panel"})}),Le=(0,a.yV)(function(e,t){let n=(0,c.M)(),{id:o=`headlessui-dialog-overlay-${n}`,...i}=e,[{dialogState:l,close:d}]=Se("Dialog.Overlay"),f=(0,u.T)(t),p=(0,m.z)(e=>{if(e.target===e.currentTarget){if((0,s.P)(e.currentTarget))return e.preventDefault();e.preventDefault(),e.stopPropagation(),d()}}),v=(0,r.useMemo)(()=>({open:0===l}),[l]);return(0,a.sY)({ourProps:{ref:f,id:o,"aria-hidden":!0,onClick:p},theirProps:i,slot:v,defaultTag:"div",name:"Dialog.Overlay"})}),Ce=(0,a.yV)(function(e,t){let n=(0,c.M)(),{id:o=`headlessui-dialog-title-${n}`,...i}=e,[{dialogState:l,setTitleId:s}]=Se("Dialog.Title"),d=(0,u.T)(t);(0,r.useEffect)(()=>(s(o),()=>s(null)),[o,s]);let f=(0,r.useMemo)(()=>({open:0===l}),[l]);return(0,a.sY)({ourProps:{ref:d,id:o},theirProps:i,slot:f,defaultTag:"h2",name:"Dialog.Title"})}),Ae=Object.assign(Oe,{Backdrop:Fe,Panel:De,Overlay:Le,Title:Ce,Description:Z})},17369:(e,t,n)=>{n.d(t,{R:()=>o});var r,o=((r=o||{}).Space=" ",r.Enter="Enter",r.Escape="Escape",r.Backspace="Backspace",r.Delete="Delete",r.ArrowLeft="ArrowLeft",r.ArrowUp="ArrowUp",r.ArrowRight="ArrowRight",r.ArrowDown="ArrowDown",r.Home="Home",r.End="End",r.PageUp="PageUp",r.PageDown="PageDown",r.Tab="Tab",r)},84947:(e,t,n)=>{n.d(t,{v:()=>U});var r=n(72791),o=n(79904),i=n(15612),a=n(56958),u=n(5623),l=n(84705),s=n(94159),c=n(47003),d=n(17369);var f,p=((f=p||{})[f.First=0]="First",f[f.Previous=1]="Previous",f[f.Next=2]="Next",f[f.Last=3]="Last",f[f.Specific=4]="Specific",f[f.Nothing=5]="Nothing",f);function v(e,t){let n=t.resolveItems();if(n.length<=0)return null;let r=t.resolveActiveIndex(),o=null!=r?r:-1,i=(()=>{switch(e.focus){case 0:return n.findIndex(e=>!t.resolveDisabled(e));case 1:{let e=n.slice().reverse().findIndex((e,n,r)=>!(-1!==o&&r.length-n-1>=o)&&!t.resolveDisabled(e));return-1===e?e:n.length-1-e}case 2:return n.findIndex((e,n)=>!(n<=o)&&!t.resolveDisabled(e));case 3:{let e=n.slice().reverse().findIndex(e=>!t.resolveDisabled(e));return-1===e?e:n.length-1-e}case 4:return n.findIndex(n=>t.resolveId(n)===e.id);case 5:return null;default:!function(e){throw new Error("Unexpected object: "+e)}(e)}})();return-1===i?r:i}var m=n(72953),g=n(60981),h=n(19541),b=n(55718);var E=n(4510);function w(e){var t;if(e.type)return e.type;let n=null!=(t=e.as)?t:"button";return"string"==typeof n&&"button"===n.toLowerCase()?"button":void 0}function y(e,t){let[n,o]=(0,r.useState)(()=>w(e));return(0,l.e)(()=>{o(w(e))},[e.type,e.as]),(0,l.e)(()=>{n||t.current&&t.current instanceof HTMLButtonElement&&!t.current.hasAttribute("type")&&o("button")},[n,t]),n}var T=n(37281),R=n(23654);function P(e){return[e.screenX,e.screenY]}function M(){let e=(0,r.useRef)([-1,-1]);return{wasMoved(t){let n=P(t);return(e.current[0]!==n[0]||e.current[1]!==n[1])&&(e.current=n,!0)},update(t){e.current=P(t)}}}let S=/([\u2700-\u27BF]|[\uE000-\uF8FF]|\uD83C[\uDC00-\uDFFF]|\uD83D[\uDC00-\uDFFF]|[\u2011-\u26FF]|\uD83E[\uDD10-\uDDFF])/g;function x(e){var t,n;let r=null!=(t=e.innerText)?t:"",o=e.cloneNode(!0);if(!(o instanceof HTMLElement))return r;let i=!1;for(let e of o.querySelectorAll('[hidden],[aria-hidden],[role="img"]'))e.remove(),i=!0;let a=i?null!=(n=o.innerText)?n:"":r;return S.test(a)&&(a=a.replace(S,"")),a}function I(e){let t=(0,r.useRef)(""),n=(0,r.useRef)("");return(0,R.z)(()=>{let r=e.current;if(!r)return"";let o=r.innerText;if(t.current===o)return n.current;let i=function(e){let t=e.getAttribute("aria-label");if("string"==typeof t)return t.trim();let n=e.getAttribute("aria-labelledby");if(n){let e=n.split(" ").map(e=>{let t=document.getElementById(e);if(t){let e=t.getAttribute("aria-label");return"string"==typeof e?e.trim():x(t).trim()}return null}).filter(Boolean);if(e.length>0)return e.join(", ")}return x(e).trim()}(r).trim().toLowerCase();return t.current=o,n.current=i,i})}var O,F=((O=F||{})[O.Open=0]="Open",O[O.Closed=1]="Closed",O),D=(e=>(e[e.Pointer=0]="Pointer",e[e.Other=1]="Other",e))(D||{}),L=(e=>(e[e.OpenMenu=0]="OpenMenu",e[e.CloseMenu=1]="CloseMenu",e[e.GoToItem=2]="GoToItem",e[e.Search=3]="Search",e[e.ClearSearch=4]="ClearSearch",e[e.RegisterItem=5]="RegisterItem",e[e.UnregisterItem=6]="UnregisterItem",e))(L||{});function C(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e=>e,n=null!==e.activeItemIndex?e.items[e.activeItemIndex]:null,r=(0,g.z2)(t(e.items.slice()),e=>e.dataRef.current.domRef.current),o=n?r.indexOf(n):null;return-1===o&&(o=null),{items:r,activeItemIndex:o}}let A={1:e=>1===e.menuState?e:{...e,activeItemIndex:null,menuState:1},0:e=>0===e.menuState?e:{...e,__demoMode:!1,menuState:0},2:(e,t)=>{var n;let r=C(e),o=v(t,{resolveItems:()=>r.items,resolveActiveIndex:()=>r.activeItemIndex,resolveId:e=>e.id,resolveDisabled:e=>e.dataRef.current.disabled});return{...e,...r,searchQuery:"",activeItemIndex:o,activationTrigger:null!=(n=t.trigger)?n:1}},3:(e,t)=>{let n=""!==e.searchQuery?0:1,r=e.searchQuery+t.value.toLowerCase(),o=(null!==e.activeItemIndex?e.items.slice(e.activeItemIndex+n).concat(e.items.slice(0,e.activeItemIndex+n)):e.items).find(e=>{var t;return(null==(t=e.dataRef.current.textValue)?void 0:t.startsWith(r))&&!e.dataRef.current.disabled}),i=o?e.items.indexOf(o):-1;return-1===i||i===e.activeItemIndex?{...e,searchQuery:r}:{...e,searchQuery:r,activeItemIndex:i,activationTrigger:1}},4:e=>""===e.searchQuery?e:{...e,searchQuery:"",searchActiveItemIndex:null},5:(e,t)=>{let n=C(e,e=>[...e,{id:t.id,dataRef:t.dataRef}]);return{...e,...n}},6:(e,t)=>{let n=C(e,e=>{let n=e.findIndex(e=>e.id===t.id);return-1!==n&&e.splice(n,1),e});return{...e,...n,activationTrigger:1}}},k=(0,r.createContext)(null);function $(e){let t=(0,r.useContext)(k);if(null===t){let t=new Error(`<${e} /> is missing a parent <Menu /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,$),t}return t}function N(e,t){return(0,o.E)(t.type,A,e,t)}k.displayName="MenuContext";let z=r.Fragment;let H=i.AN.RenderStrategy|i.AN.Static;let _=r.Fragment;let B=(0,i.yV)(function(e,t){let{__demoMode:n=!1,...a}=e,u=(0,r.useReducer)(N,{__demoMode:n,menuState:n?0:1,buttonRef:(0,r.createRef)(),itemsRef:(0,r.createRef)(),items:[],searchQuery:"",activeItemIndex:null,activationTrigger:1}),[{menuState:l,itemsRef:c,buttonRef:d},f]=u,p=(0,s.T)(t);(0,h.O)([d,c],(e,t)=>{var n;f({type:1}),(0,g.sP)(t,g.tJ.Loose)||(e.preventDefault(),null==(n=d.current)||n.focus())},0===l);let v=(0,R.z)(()=>{f({type:1})}),m=(0,r.useMemo)(()=>({open:0===l,close:v}),[l,v]),b={ref:p};return r.createElement(k.Provider,{value:u},r.createElement(E.up,{value:(0,o.E)(l,{0:E.ZM.Open,1:E.ZM.Closed})},(0,i.sY)({ourProps:b,theirProps:a,slot:m,defaultTag:z,name:"Menu"})))}),V=(0,i.yV)(function(e,t){var n;let o=(0,c.M)(),{id:a=`headlessui-menu-button-${o}`,...l}=e,[f,v]=$("Menu.Button"),g=(0,s.T)(f.buttonRef,t),h=(0,u.G)(),b=(0,R.z)(e=>{switch(e.key){case d.R.Space:case d.R.Enter:case d.R.ArrowDown:e.preventDefault(),e.stopPropagation(),v({type:0}),h.nextFrame(()=>v({type:2,focus:p.First}));break;case d.R.ArrowUp:e.preventDefault(),e.stopPropagation(),v({type:0}),h.nextFrame(()=>v({type:2,focus:p.Last}))}}),E=(0,R.z)(e=>{if(e.key===d.R.Space)e.preventDefault()}),w=(0,R.z)(t=>{if((0,m.P)(t.currentTarget))return t.preventDefault();e.disabled||(0===f.menuState?(v({type:1}),h.nextFrame(()=>{var e;return null==(e=f.buttonRef.current)?void 0:e.focus({preventScroll:!0})})):(t.preventDefault(),v({type:0})))}),T=(0,r.useMemo)(()=>({open:0===f.menuState}),[f]),P={ref:g,id:a,type:y(e,f.buttonRef),"aria-haspopup":"menu","aria-controls":null==(n=f.itemsRef.current)?void 0:n.id,"aria-expanded":e.disabled?void 0:0===f.menuState,onKeyDown:b,onKeyUp:E,onClick:w};return(0,i.sY)({ourProps:P,theirProps:l,slot:T,defaultTag:"button",name:"Menu.Button"})}),j=(0,i.yV)(function(e,t){var n,o;let f=(0,c.M)(),{id:v=`headlessui-menu-items-${f}`,...m}=e,[h,w]=$("Menu.Items"),y=(0,s.T)(h.itemsRef,t),P=(0,T.i)(h.itemsRef),M=(0,u.G)(),S=(0,E.oJ)(),x=null!==S?(S&E.ZM.Open)===E.ZM.Open:0===h.menuState;(0,r.useEffect)(()=>{let e=h.itemsRef.current;e&&0===h.menuState&&e!==(null==P?void 0:P.activeElement)&&e.focus({preventScroll:!0})},[h.menuState,h.itemsRef,P]),function(e){let{container:t,accept:n,walk:o,enabled:i=!0}=e,a=(0,r.useRef)(n),u=(0,r.useRef)(o);(0,r.useEffect)(()=>{a.current=n,u.current=o},[n,o]),(0,l.e)(()=>{if(!t||!i)return;let e=(0,b.r)(t);if(!e)return;let n=a.current,r=u.current,o=Object.assign(e=>n(e),{acceptNode:n}),l=e.createTreeWalker(t,NodeFilter.SHOW_ELEMENT,o,!1);for(;l.nextNode();)r(l.currentNode)},[t,i,a,u])}({container:h.itemsRef.current,enabled:0===h.menuState,accept:e=>"menuitem"===e.getAttribute("role")?NodeFilter.FILTER_REJECT:e.hasAttribute("role")?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT,walk(e){e.setAttribute("role","none")}});let I=(0,R.z)(e=>{var t,n;switch(M.dispose(),e.key){case d.R.Space:if(""!==h.searchQuery)return e.preventDefault(),e.stopPropagation(),w({type:3,value:e.key});case d.R.Enter:if(e.preventDefault(),e.stopPropagation(),w({type:1}),null!==h.activeItemIndex){let{dataRef:e}=h.items[h.activeItemIndex];null==(n=null==(t=e.current)?void 0:t.domRef.current)||n.click()}(0,g.wI)(h.buttonRef.current);break;case d.R.ArrowDown:return e.preventDefault(),e.stopPropagation(),w({type:2,focus:p.Next});case d.R.ArrowUp:return e.preventDefault(),e.stopPropagation(),w({type:2,focus:p.Previous});case d.R.Home:case d.R.PageUp:return e.preventDefault(),e.stopPropagation(),w({type:2,focus:p.First});case d.R.End:case d.R.PageDown:return e.preventDefault(),e.stopPropagation(),w({type:2,focus:p.Last});case d.R.Escape:e.preventDefault(),e.stopPropagation(),w({type:1}),(0,a.k)().nextFrame(()=>{var e;return null==(e=h.buttonRef.current)?void 0:e.focus({preventScroll:!0})});break;case d.R.Tab:e.preventDefault(),e.stopPropagation(),w({type:1}),(0,a.k)().nextFrame(()=>{(0,g.EO)(h.buttonRef.current,e.shiftKey?g.TO.Previous:g.TO.Next)});break;default:1===e.key.length&&(w({type:3,value:e.key}),M.setTimeout(()=>w({type:4}),350))}}),O=(0,R.z)(e=>{if(e.key===d.R.Space)e.preventDefault()}),F=(0,r.useMemo)(()=>({open:0===h.menuState}),[h]),D={"aria-activedescendant":null===h.activeItemIndex||null==(n=h.items[h.activeItemIndex])?void 0:n.id,"aria-labelledby":null==(o=h.buttonRef.current)?void 0:o.id,id:v,onKeyDown:I,onKeyUp:O,role:"menu",tabIndex:0,ref:y};return(0,i.sY)({ourProps:D,theirProps:m,slot:F,defaultTag:"div",features:H,visible:x,name:"Menu.Items"})}),Y=(0,i.yV)(function(e,t){let n=(0,c.M)(),{id:o=`headlessui-menu-item-${n}`,disabled:u=!1,...d}=e,[f,v]=$("Menu.Item"),m=null!==f.activeItemIndex&&f.items[f.activeItemIndex].id===o,h=(0,r.useRef)(null),b=(0,s.T)(t,h);(0,l.e)(()=>{if(f.__demoMode||0!==f.menuState||!m||0===f.activationTrigger)return;let e=(0,a.k)();return e.requestAnimationFrame(()=>{var e,t;null==(t=null==(e=h.current)?void 0:e.scrollIntoView)||t.call(e,{block:"nearest"})}),e.dispose},[f.__demoMode,h,m,f.menuState,f.activationTrigger,f.activeItemIndex]);let E=I(h),w=(0,r.useRef)({disabled:u,domRef:h,get textValue(){return E()}});(0,l.e)(()=>{w.current.disabled=u},[w,u]),(0,l.e)(()=>(v({type:5,id:o,dataRef:w}),()=>v({type:6,id:o})),[w,o]);let y=(0,R.z)(()=>{v({type:1})}),T=(0,R.z)(e=>{if(u)return e.preventDefault();v({type:1}),(0,g.wI)(f.buttonRef.current)}),P=(0,R.z)(()=>{if(u)return v({type:2,focus:p.Nothing});v({type:2,focus:p.Specific,id:o})}),S=M(),x=(0,R.z)(e=>S.update(e)),O=(0,R.z)(e=>{S.wasMoved(e)&&(u||m||v({type:2,focus:p.Specific,id:o,trigger:0}))}),F=(0,R.z)(e=>{S.wasMoved(e)&&(u||m&&v({type:2,focus:p.Nothing}))}),D=(0,r.useMemo)(()=>({active:m,disabled:u,close:y}),[m,u,y]);return(0,i.sY)({ourProps:{id:o,ref:b,role:"menuitem",tabIndex:!0===u?void 0:-1,"aria-disabled":!0===u||void 0,disabled:void 0,onClick:T,onFocus:P,onPointerEnter:x,onMouseEnter:x,onPointerMove:O,onMouseMove:O,onPointerLeave:F,onMouseLeave:F},theirProps:d,slot:D,defaultTag:_,name:"Menu.Item"})}),U=Object.assign(B,{Button:V,Items:j,Item:Y})},47003:(e,t,n)=>{n.d(t,{M:()=>l});var r,o=n(72791),i=n(84705),a=n(81511),u=n(43784);let l=null!=(r=o.useId)?r:function(){let e=(0,a.H)(),[t,n]=o.useState(e?()=>u.O.nextId():null);return(0,i.e)(()=>{null===t&&n(u.O.nextId())},[t]),null!=t?""+t:void 0}},19541:(e,t,n)=>{n.d(t,{O:()=>l});var r=n(72791),o=n(60981),i=n(22806);function a(e,t,n){let o=(0,i.E)(t);(0,r.useEffect)(()=>{function t(e){o.current(e)}return document.addEventListener(e,t,n),()=>document.removeEventListener(e,t,n)},[e,n])}var u=n(70642);function l(e,t){let n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],i=(0,r.useRef)(!1);function l(n,r){if(!i.current||n.defaultPrevented)return;let a=r(n);if(null===a||!a.getRootNode().contains(a))return;let u=function e(t){return"function"==typeof t?e(t()):Array.isArray(t)||t instanceof Set?t:[t]}(e);for(let e of u){if(null===e)continue;let t=e instanceof HTMLElement?e:e.current;if(null!=t&&t.contains(a)||n.composed&&n.composedPath().includes(t))return}return!(0,o.sP)(a,o.tJ.Loose)&&-1!==a.tabIndex&&n.preventDefault(),t(n,a)}(0,r.useEffect)(()=>{requestAnimationFrame(()=>{i.current=n})},[n]);let s=(0,r.useRef)(null);a("mousedown",e=>{var t,n;i.current&&(s.current=(null==(n=null==(t=e.composedPath)?void 0:t.call(e))?void 0:n[0])||e.target)},!0),a("click",e=>{s.current&&(l(e,()=>s.current),s.current=null)},!0),(0,u.s)("blur",e=>l(e,()=>window.document.activeElement instanceof HTMLIFrameElement?window.document.activeElement:null),!0)}},37281:(e,t,n)=>{n.d(t,{i:()=>i});var r=n(72791),o=n(55718);function i(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,r.useMemo)(()=>(0,o.r)(...t),[...t])}},70642:(e,t,n)=>{n.d(t,{s:()=>i});var r=n(72791),o=n(22806);function i(e,t,n){let i=(0,o.E)(t);(0,r.useEffect)(()=>{function t(e){i.current(e)}return window.addEventListener(e,t,n),()=>window.removeEventListener(e,t,n)},[e,n])}},72953:(e,t,n)=>{function r(e){let t=e.parentElement,n=null;for(;t&&!(t instanceof HTMLFieldSetElement);)t instanceof HTMLLegendElement&&(n=t),t=t.parentElement;let r=""===(null==t?void 0:t.getAttribute("disabled"));return(!r||!function(e){if(!e)return!1;let t=e.previousElementSibling;for(;null!==t;){if(t instanceof HTMLLegendElement)return!1;t=t.previousElementSibling}return!0}(n))&&r}n.d(t,{P:()=>r})},60981:(e,t,n)=>{n.d(t,{C5:()=>b,EO:()=>y,TO:()=>c,fE:()=>d,jA:()=>T,sP:()=>m,tJ:()=>v,wI:()=>g,z2:()=>w});var r=n(56958),o=n(79904),i=n(55718);let a=["[contentEditable=true]","[tabindex]","a[href]","area[href]","button:not([disabled])","iframe","input:not([disabled])","select:not([disabled])","textarea:not([disabled])"].map(e=>`${e}:not([tabindex='-1'])`).join(",");var u,l,s,c=((s=c||{})[s.First=1]="First",s[s.Previous=2]="Previous",s[s.Next=4]="Next",s[s.Last=8]="Last",s[s.WrapAround=16]="WrapAround",s[s.NoScroll=32]="NoScroll",s),d=((l=d||{})[l.Error=0]="Error",l[l.Overflow=1]="Overflow",l[l.Success=2]="Success",l[l.Underflow=3]="Underflow",l),f=((u=f||{})[u.Previous=-1]="Previous",u[u.Next=1]="Next",u);function p(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:document.body;return null==e?[]:Array.from(e.querySelectorAll(a)).sort((e,t)=>Math.sign((e.tabIndex||Number.MAX_SAFE_INTEGER)-(t.tabIndex||Number.MAX_SAFE_INTEGER)))}var v=(e=>(e[e.Strict=0]="Strict",e[e.Loose=1]="Loose",e))(v||{});function m(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;var n;return e!==(null==(n=(0,i.r)(e))?void 0:n.body)&&(0,o.E)(t,{0:()=>e.matches(a),1(){let t=e;for(;null!==t;){if(t.matches(a))return!0;t=t.parentElement}return!1}})}function g(e){let t=(0,i.r)(e);(0,r.k)().nextFrame(()=>{t&&!m(t.activeElement,0)&&b(e)})}var h=(e=>(e[e.Keyboard=0]="Keyboard",e[e.Mouse=1]="Mouse",e))(h||{});function b(e){null==e||e.focus({preventScroll:!0})}"undefined"!=typeof window&&"undefined"!=typeof document&&(document.addEventListener("keydown",e=>{e.metaKey||e.altKey||e.ctrlKey||(document.documentElement.dataset.headlessuiFocusVisible="")},!0),document.addEventListener("click",e=>{1===e.detail?delete document.documentElement.dataset.headlessuiFocusVisible:0===e.detail&&(document.documentElement.dataset.headlessuiFocusVisible="")},!0));let E=["textarea","input"].join(",");function w(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e=>e;return e.slice().sort((e,n)=>{let r=t(e),o=t(n);if(null===r||null===o)return 0;let i=r.compareDocumentPosition(o);return i&Node.DOCUMENT_POSITION_FOLLOWING?-1:i&Node.DOCUMENT_POSITION_PRECEDING?1:0})}function y(e,t){return T(p(),t,{relativeTo:e})}function T(e,t){let{sorted:n=!0,relativeTo:r=null,skipElements:o=[]}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},i=Array.isArray(e)?e.length>0?e[0].ownerDocument:document:e.ownerDocument,a=Array.isArray(e)?n?w(e):e:p(e);o.length>0&&a.length>1&&(a=a.filter(e=>!o.includes(e))),r=null!=r?r:i.activeElement;let u,l=(()=>{if(5&t)return 1;if(10&t)return-1;throw new Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),s=(()=>{if(1&t)return 0;if(2&t)return Math.max(0,a.indexOf(r))-1;if(4&t)return Math.max(0,a.indexOf(r))+1;if(8&t)return a.length-1;throw new Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),c=32&t?{preventScroll:!0}:{},d=0,f=a.length;do{if(d>=f||d+f<=0)return 0;let e=s+d;if(16&t)e=(e+f)%f;else{if(e<0)return 3;if(e>=f)return 1}u=a[e],null==u||u.focus(c),d+=l}while(u!==i.activeElement);return 6&t&&function(e){var t,n;return null!=(n=null==(t=null==e?void 0:e.matches)?void 0:t.call(e,E))&&n}(u)&&u.select(),2}},55718:(e,t,n)=>{n.d(t,{r:()=>o});var r=n(43784);function o(e){return r.O.isServer?null:e instanceof Node?e.ownerDocument:null!=e&&e.hasOwnProperty("current")&&e.current instanceof Node?e.current.ownerDocument:document}}}]);
//# sourceMappingURL=2659.77e02a8a.chunk.js.map