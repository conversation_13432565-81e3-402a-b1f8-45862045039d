"use strict";(self.webpackChunkv1=self.webpackChunkv1||[]).push([[5393,9044,7250,2377],{72608:(e,t,r)=>{r.d(t,{Z:()=>a});r(72791);const a=r.p+"static/media/AIPRO.0580715cba3cbc2179c46b033d087db9.svg"},34492:(e,t,r)=>{r.d(t,{aS:()=>l,ar:()=>f,mD:()=>x,mW:()=>g,o0:()=>m,p6:()=>o,rZ:()=>c,tN:()=>p,x6:()=>i,yt:()=>u});var a=r(74335),n=r(80184);function s(e){return e?"usd"===e.toLowerCase()?"$":"eur"===e.toLowerCase()?"€":"gbp"===e.toLowerCase()?"£":"brl"===e.toLowerCase()||"sar"===e.toLowerCase()?"R$":"czk"===e.toLowerCase()?"Kč":"":""}function c(e){return e?"usd"===e.toLowerCase()||"eur"===e.toLowerCase()?"US":"gbp"===e.toLowerCase()?"GB":"brl"===e.toLowerCase()?"US":"sar"===e.toLowerCase()?"AE":"chf"===e.toLowerCase()?"CH":"sek"===e.toLowerCase()?"SE":"US":""}function o(e){if(!e)return"";const t=new Date(e.replace(/ /g,"T"));return["January","February","March","April","May","June","July","August","September","October","November","December"][t.getMonth()]+" "+t.getDate()+" "+t.getFullYear()}function l(e){return e.trial_price?e.trial_price:e.price?e.price:"0"}function i(e){return e.toString().replace(/\B(?=(\d{3})+(?!\d))/g,",")}function d(e){const t=parseFloat(e);return i(t%1==0?t.toFixed(0):t.toFixed(2))}function u(e,t){return e&&t?isNaN(t)?"":parseFloat(t)>=0?"sar"===e.toLowerCase()?d(t).toLocaleString("en-US")+"ر.س.":"aed"===e.toLowerCase()?d(t).toLocaleString("en-US")+"AED":"chf"===e.toLowerCase()?d(t).toLocaleString("en-US")+"CHF":"sek"===e.toLowerCase()?d(t).toLocaleString("en-US")+"SEK":"pln"===e.toLowerCase()?d(t).toLocaleString("en-US")+"zł":"ron"===e.toLowerCase()?d(t).toLocaleString("en-US")+"LEI":"huf"===e.toLowerCase()?d(t).toLocaleString("en-US")+"Ft":"dkk"===e.toLowerCase()?d(t).toLocaleString("en-US")+"kr.":"bgn"===e.toLowerCase()?d(t).toLocaleString("en-US")+"лв":"try"===e.toLowerCase()?d(t).toLocaleString("en-US")+"₺":s(e)+d(t).toLocaleString("en-US"):"-"+s(e)+(-1*d(t)).toLocaleString("en-US"):""}function p(e,t){e=new Date(e);var r=((t=new Date(t)).getTime()-e.getTime())/1e3;return r/=60,Math.abs(Math.round(r))}function m(e){if(!e)return"";const t=new Date(e.replace(/ /g,"T"));return["January","February","March","April","May","June","July","August","September","October","November","December"][t.getMonth()]+" "+t.getDate()+" "+t.getFullYear()+" "+t.getHours()+":"+t.getMinutes()}function g(e){let{plan:t}=e,r="",s="";return"Yearly"===t.payment_interval&&(r=u(t.currency,parseFloat(t.price/365).toFixed(2)),s=(0,n.jsxs)("div",{class:"text-xs mb-4",children:["billed ",u(t.currency,t.price),"/year"]})),"Monthly"===t.payment_interval&&(r=u(t.currency,parseFloat(t.price/30).toFixed(2)),s=(0,n.jsxs)("div",{class:"text-xs mb-4",children:["billed ",u(t.currency,t.price),"/Month"]})),t.trial_price&&(r=u(t.currency,parseFloat(t.trial_price/t.trial_days).toFixed(2)),s=(0,n.jsxs)("div",{class:"text-xs mb-4",children:["billed ",u(t.currency,t.trial_price)]})),(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)("p",{className:"text-4xl font-bold text-gray-800 ".concat(""===(0,a.bG)("p_toggle")?"mb-4":""),children:[r," ",(0,n.jsx)("span",{className:"text-sm",children:" per Day"})]}),s]})}function x(e){let{plan:t}=e;return"on"===(0,a.bG)("daily")?g({plan:t}):t.trial_price?(0,n.jsx)("p",{className:"text-4xl font-bold text-gray-800 mb-4",children:u(t.currency,t.trial_price)}):(0,n.jsxs)("p",{className:"text-4xl font-bold text-gray-800 mb-4",children:[u(t.currency,t.price),(0,n.jsxs)("span",{className:"text-sm",children:["/","Monthly"===t.payment_interval?"month":"year"]})]})}function f(e){var t,r;const n=(null!==(t=(0,a.bG)("locales"))&&void 0!==t?t:"en").toLowerCase(),s=(null!==(r=(0,a.bG)("daily"))&&void 0!==r?r:"off").toLowerCase(),{trial_days:c,payment_interval:o,trial_price:l,currency:i,currency_symbol:d}=e;let{display_txt2:p,price:m}=e;if(c>0&&l>0&&"en"===n){let e=m,t="month";"on"===s&&(e=parseFloat(m/("Yearly"===o?365:30)).toFixed(2),t="day<br>(billed ".concat(d+m," ").concat(o,")")),p+="<div>".concat(c,"-Day Trial, then only ").concat(u(i,e)," per ").concat(t,"</div>")}return p}},27250:(e,t,r)=>{r.r(t),r.d(t,{default:()=>l});var a=r(72791),n=r(72608),s=r(19878),c=r(28891),o=(r(29534),r(80184));const l=function(e){let{hideNavLink:t,auth:l=(0,c.gx)()}=e;const i=window.location.pathname,d=/\/start-chatgpt-go\/?$/.test(i),u=/\/text-to-image\/?$/.test(i),p=/\/start-chatgpt-v2\/?$/.test(i),m=i.includes("/register"),g=!t;(0,a.useEffect)((()=>{const e=x(n.Z,"image"),t=x(s,"image");return document.head.append(e,t),m||Promise.all([r.e(7749),r.e(1707)]).then(r.bind(r,51707)),()=>{e.remove(),t.remove()}}),[m]);const x=(e,t)=>{const r=document.createElement("link");return r.rel="preload",r.href=e,r.as=t,r};return(0,o.jsxs)(o.Fragment,{children:["",(0,o.jsx)("header",{id:"header",className:"headernav border-gray-500 bg-white text-black w-3/4 mx-auto rounded-3xl mt-3 py-2 z-[8888] ".concat(""),children:(0,o.jsxs)("div",{className:"container mx-auto flex justify-between items-center px-4",children:[(0,o.jsxs)("picture",{className:"aiprologo",children:[(0,o.jsx)("source",{type:"image/webp",srcSet:s,width:"150",height:"52",className:"aiprologo"}),(0,o.jsx)("img",{src:n.Z,alt:"AI-Pro Logo",className:"aiprologo"})]}),(d||u||p)&&g&&(0,o.jsx)("nav",{className:"text-xs lg:text-sm block inline-flex",id:"menu",children:(0,o.jsx)("ul",{className:"headnav flex inline-flex",children:(0,o.jsx)("li",{className:"mr-1 md:mr-2 lg:mr-6",children:(0,o.jsx)("a",{href:l?"/my-account":"/login",className:"font-bold","aria-label":l?"my-account":"login",children:l?"My Apps":"LOG IN"})})})})]})})]})}},9643:(e,t,r)=>{r.r(t),r.d(t,{default:()=>x});var a=r(72791),n=r(19886),s=r(27250),c=r(56355),o=r(63019),l=r(91615),i=r(31243),d=r(34492),u=r(95828),p=r.n(u),m=(r(92831),r(74335)),g=r(80184);const x=function(){(0,a.useEffect)((()=>{p().options={positionClass:"toast-top-center"}}),[]);const[e]=(0,a.useState)(window.view_data?window.view_data:{}),[t]=(0,a.useState)(e.plan?e.plan:null),r=(0,m.bG)("access");if(!t)return;return(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)(s.default,{auth:r}),t?(0,g.jsx)("div",{className:"Payment-upgrade bg-gray-100 min-h-[600px] flex",children:(0,g.jsx)("div",{className:"container mx-auto py-10",children:(0,g.jsx)("div",{className:"flex flex-col items-center py-10 lg:py-16",children:(0,g.jsx)("div",{className:"flex flex-wrap md:flex-wrap justify-center w-full",children:(0,g.jsxs)("div",{className:"pay_right px-4 mb-8 md:w-2/5",children:[(0,g.jsx)("h2",{className:"text-xl font-bold mb-4 py-10",children:"Order Summary"}),(0,g.jsxs)("div",{className:"flex",children:[(0,g.jsxs)("div",{className:"mb-2 w-4/5 text-sm mr-4",children:[(0,g.jsxs)("b",{className:"text-md text-uppercase",children:[t.plan_type_display," PLAN"]}),(0,g.jsx)("br",{}),"Your subscription will resume on the next billing period and will renew until you cancel it."]}),(0,g.jsxs)("div",{className:"font-bold mt-4 w-1/5",children:["Total: ",(0,d.yt)(t.currency,(0,d.aS)(t))]})]}),(0,g.jsx)("div",{className:"flex",children:(0,g.jsx)("div",{className:"mb-2 w-full text-sm",children:(0,g.jsx)(n.E.button,{className:"bg-blue-500 text-white font-bold py-3 px-6 rounded-lg w-full my-4 proceed-pmt",whileHover:{backgroundColor:"#5997fd"},whileTap:{scale:.9},onClick:function(){document.querySelector(".loader-container").classList.add("active"),i.Z.post("".concat("http://localhost:9002/api","/resume-subscription"),{tk:(0,m.bG)("access"),subscription_id:t.merchant_subscription_id,merchant:t.merchant,account_pid:t.account_pid},{headers:{"content-type":"application/x-www-form-urlencoded"}}).then((function(e){let t=e.data;t.success?(document.querySelector(".loader-container").classList.remove("active"),p().success("Success!<br> Your subscription is now resumed."),setTimeout((function(){window.location.href="/manage-account/"}),1e3)):(document.querySelector(".loader-container").classList.remove("active"),p().error(t.data.msg))}))},children:"Resume Subscription"})})}),(0,g.jsxs)("div",{className:"securecont border-t-2 border-gray-300 flex py-5",children:[(0,g.jsxs)("div",{className:"securetext mb-2 text-sm w-1/2",children:[(0,g.jsx)(c.kUi,{className:"inline text-lg mr-1 text-orange-500 text-xs"})," Secure Checkout"]}),(0,g.jsxs)("div",{className:"securelogo mb-2 text-sm w-1/2 flex flex-wrap justify-center items-center",children:[(0,g.jsx)("img",{src:o,alt:"Secure Logo",className:"cclogo inline"}),(0,g.jsxs)("div",{className:"flex items-center justify-center flex-wrap",children:[(0,g.jsx)("img",{src:l,alt:"Authorize",className:"text-center md:text-right py-2 w-20"}),(0,g.jsx)("button",{onClick:()=>{window.open("//www.securitymetrics.com/site_certificate?id=1982469&tk=d41c416c6208f1136426bc8038178d2e","Verification","location=no, toolbar=no, resizable=no, scrollbars=yes, directories=no, status=no,top=100,left=100, width=700, height=600")},title:"SecurityMetrics card safe certification",className:"h-20",children:(0,g.jsx)("img",{loading:"lazy",src:"https://www.securitymetrics.com/portal/app/ngsm/assets/img/GreyContent_Credit_Card_Safe_White_Sqr.png",alt:"SecurityMetrics card safe certification logo",className:"max-h-full",width:"80",height:"80"})})]})]})]})]})})})})}):""]})}},89983:(e,t,r)=>{r.d(t,{w_:()=>i});var a=r(72791),n={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},s=a.createContext&&a.createContext(n),c=function(){return c=Object.assign||function(e){for(var t,r=1,a=arguments.length;r<a;r++)for(var n in t=arguments[r])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e},c.apply(this,arguments)},o=function(e,t){var r={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(r[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var n=0;for(a=Object.getOwnPropertySymbols(e);n<a.length;n++)t.indexOf(a[n])<0&&Object.prototype.propertyIsEnumerable.call(e,a[n])&&(r[a[n]]=e[a[n]])}return r};function l(e){return e&&e.map((function(e,t){return a.createElement(e.tag,c({key:t},e.attr),l(e.child))}))}function i(e){return function(t){return a.createElement(d,c({attr:c({},e.attr)},t),l(e.child))}}function d(e){var t=function(t){var r,n=e.attr,s=e.size,l=e.title,i=o(e,["attr","size","title"]),d=s||t.size||"1em";return t.className&&(r=t.className),e.className&&(r=(r?r+" ":"")+e.className),a.createElement("svg",c({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,n,i,{className:r,style:c(c({color:e.color||t.color},t.style),e.style),height:d,width:d,xmlns:"http://www.w3.org/2000/svg"}),l&&a.createElement("title",null,l),e.children)};return void 0!==s?a.createElement(s.Consumer,null,(function(e){return t(e)})):t(n)}},29534:()=>{},19878:(e,t,r)=>{e.exports=r.p+"static/media/AIPRO.84104dfd05446283b05c.webp"},63019:(e,t,r)=>{e.exports=r.p+"static/media/cc_v2.60526f108e89e087278a.png"},91615:e=>{e.exports="data:image/gif;base64,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"}}]);
//# sourceMappingURL=5393.021cf7f0.chunk.js.map