{"version": 3, "file": "static/js/1875.6877a199.chunk.js", "mappings": "sOASe,SAASA,EAAYC,GAAoB,IAAnB,eAACC,GAAeD,EACrD,MAAM,EAAEE,IAAMC,EAAAA,EAAAA,MACRC,GAAcC,EAAAA,EAAAA,QAAO,OACpBC,EAAOC,IAAYC,EAAAA,EAAAA,UAAS,KAC5BC,EAAUC,IAAeF,EAAAA,EAAAA,UAAS,KAClCG,IAAcH,EAAAA,EAAAA,WAAS,IACvBI,EAAYC,IAAiBL,EAAAA,EAAAA,UAAS,KACtCM,EAAeC,IAAoBP,EAAAA,EAAAA,UAAS,KAC5CQ,EAAcC,IAAmBT,EAAAA,EAAAA,WAAS,GAC3CU,GAAWC,EAAAA,EAAAA,IAAU,MACrBC,GAAUD,EAAAA,EAAAA,IAAU,YAAc,IACjCE,EAAWC,IAAgBd,EAAAA,EAAAA,UAAS,KACpCe,EAAYC,IAAiBhB,EAAAA,EAAAA,UAAS,IAEvCiB,EAAcC,OAAOC,OAAOC,MAS5BC,EAAiB,6BARFH,OAAOC,OAAOG,OAGd,KAGuB,WAD9BL,EAHM,KAGuB,IAgFrCM,EAAuBA,KACzB,MAAMC,GAAOb,EAAAA,EAAAA,IAAU,QACjBc,GAAUd,EAAAA,EAAAA,IAAU,WACpBe,GAASf,EAAAA,EAAAA,IAAU,WAEzB,GAAa,YAATa,EACgB,OAAZC,EACiB,UAAbf,EACAQ,OAAOS,IAAIC,SAASC,KAAO,WAAWX,OAAOU,SAASE,2BACpC,MAAXJ,EACPR,OAAOa,KAAKH,SAASC,KAAO,OAE5BX,OAAOU,SAASC,KAAO,OAGV,UAAbnB,EACAQ,OAAOS,IAAIC,SAASC,KAAO,WAAWX,OAAOU,SAASE,2BACpC,MAAXJ,EACPR,OAAOa,KAAKH,SAASC,KAAO,WAAWX,OAAOU,SAASE,8BAEvDZ,OAAOU,SAASC,KAAO,WAAWX,OAAOU,SAASE,mCAI1D,GAAgB,OAAZlB,EACiB,UAAbF,EACAQ,OAAOS,IAAIC,SAASC,KAAO,cACT,MAAXH,EACPR,OAAOa,KAAKH,SAASC,KAAO,cAE5BpC,EAAe,mBAEhB,CACH,MAAMuC,GAAUrB,EAAAA,EAAAA,IAAU,WACpBsB,GAAYtB,EAAAA,EAAAA,IAAU,aAExBuB,OAAwCF,GAAyB,MAAdC,EAClC,UAAbvB,EACAQ,OAAOS,IAAIC,SAASC,KAAO,WAAWX,OAAOU,SAASE,2BACpC,MAAXJ,EACPR,OAAOa,KAAKH,SAASC,KAAO,kBAE5BX,OAAOU,SAASC,KAAO,kBAGV,UAAbnB,EACAQ,OAAOS,IAAIC,SAASC,KAAO,WAAWX,OAAOU,SAASE,2BACpC,MAAXJ,EACPR,OAAOa,KAAKH,SAASC,KAAO,OAE5BX,OAAOU,SAASC,KAAO,MAGnC,GA+GR,OAzCAM,EAAAA,EAAAA,WAAU,KACN,IAAIC,GAAY,EAmChB,MAjCqBC,WACjB,IACA,MAAMC,QAAiBC,EAAAA,EAAMC,KACzB,6CAEAJ,GAAaE,EAASG,KAAKC,SAAWJ,EAASG,KAAKE,KACpD7B,EAAawB,EAASG,KAAKE,IAE/B,CAAE,MAAOC,GACTC,QAAQD,MAAM,sCAAuCA,EACrD,GAoBJE,GAjBsBT,WAClB,IACA,MAAMC,QAAiBC,EAAAA,EAAMC,KACzB,2CACA,CACAO,QAAS,KAET,CAAEC,QAAS,CAAE,eAAgB,uCAE7BV,EAASG,KAAKC,SAAWJ,EAASG,KAAKE,KACvC3B,EAAcsB,EAASG,KAAKE,IAEhC,CAAE,MAAOC,GACTC,QAAQD,MAAM,qCAAsCA,EACpD,GAIJK,GAEO,KACHb,GAAY,IAEjB,KAGHc,EAAAA,EAAAA,MAAA,WAAAC,SAAA,EACID,EAAAA,EAAAA,MAAA,OAAKE,UAAU,uCAAsCD,SAAA,EACjDE,EAAAA,EAAAA,KAAA,KAAGD,UAAU,uCAAsCD,SAAC,aACpDE,EAAAA,EAAAA,KAAA,KAAGD,UAAU,8CAA6CD,SAAC,qBAG/DE,EAAAA,EAAAA,KAAA,SACIC,IAAK1D,EACL2D,KAAK,OACLC,KAAK,aACLJ,UAAU,iCACVK,aAAa,SAGjBP,EAAAA,EAAAA,MAAA,OAAKE,UAAU,YAAWD,SAAA,EACtBE,EAAAA,EAAAA,KAAA,SAAOD,UAAU,wCAAuCD,SAAC,mBAG7DE,EAAAA,EAAAA,KAAA,SACIE,KAAK,QACLC,KAAK,QACLE,MAAO5D,EACP6D,SAAWC,IACX7D,EAAS6D,EAAEC,OAAOH,OAClBrD,EAAc,KAEdyD,YAAY,2BACZV,UAAU,+HAEbhD,IACGiD,EAAAA,EAAAA,KAAA,KAAGD,UAAU,4BAA2BD,SAAE/C,QAI9C8C,EAAAA,EAAAA,MAAA,OAAKE,UAAU,YAAWD,SAAA,EACtBE,EAAAA,EAAAA,KAAA,SAAOD,UAAU,wCAAuCD,SAAC,cAGzDD,EAAAA,EAAAA,MAAA,OAAKE,UAAU,WAAUD,SAAA,EACrBE,EAAAA,EAAAA,KAAA,SACAE,KAAM/C,EAAe,OAAS,WAC9BgD,KAAK,WACLE,MAAOzD,EACP0D,SAAWC,IACP1D,EAAY0D,EAAEC,OAAOH,OACrBnD,EAAiB,KAErBuD,YAAY,sBACZV,UAAU,gIAEVC,EAAAA,EAAAA,KAAA,OACAD,UAAU,4EACVW,QAASA,IAAMtD,EAAiBuD,IAAUA,GAAMb,SAE/C3C,GACG0C,EAAAA,EAAAA,MAAA,OACAe,MAAM,6BACNb,UAAU,WACVhC,MAAM,KACNE,OAAO,KACP4C,QAAQ,YACRC,KAAK,OACLC,OAAO,UACPC,YAAY,IACZC,cAAc,QACdC,eAAe,QAAOpB,SAAA,EAEtBE,EAAAA,EAAAA,KAAA,QAAMmB,EAAE,kDACRnB,EAAAA,EAAAA,KAAA,QAAMmB,EAAE,gBACRnB,EAAAA,EAAAA,KAAA,UAAQoB,GAAG,KAAKC,GAAG,KAAKC,EAAE,UAG1BzB,EAAAA,EAAAA,MAAA,OACAe,MAAM,6BACNb,UAAU,WACVhC,MAAM,KACNE,OAAO,KACP4C,QAAQ,YACRC,KAAK,OACLC,OAAO,UACPC,YAAY,IACZC,cAAc,QACdC,eAAe,QAAOpB,SAAA,EAEtBE,EAAAA,EAAAA,KAAA,QAAMmB,EAAE,kDACRnB,EAAAA,EAAAA,KAAA,UAAQoB,GAAG,KAAKC,GAAG,KAAKC,EAAE,cAKjCrE,IACG+C,EAAAA,EAAAA,KAAA,KAAGD,UAAU,4BAA2BD,SAAE7C,QAIlD+C,EAAAA,EAAAA,KAAA,UACIU,QArTaa,KAAO,IAADC,EAAAC,EACJ,QAAvBD,EAAIjF,EAAYmF,eAAO,IAAAF,GAAnBA,EAAqBnB,SACrBsB,EAAAA,EAAAA,IAAU,aAAc,MAAO,CAAEC,KAAM,OACvCD,EAAAA,EAAAA,IAAU,aAAc,MAAO,CAAEE,OAAQ,cAAeD,KAAM,OAGlE,MAAME,EAjCgBC,MACtB,IAAIC,GAAW,EAaX,OAZCvF,EAIO,eAAewF,KAAKxF,IAK5BO,EAAc,IACdgF,GAAW,GALXhF,EACIX,EAAE,wCAA0C,0BALhDW,EACIX,EAAE,oCAAsC,sBAUrC2F,GAmBWD,GAChBG,EAjBmBC,MACzB,IAAIC,GAAMC,EAAAA,EAAAA,GAAiBzF,GAC3B,OAAIwF,GACAlF,EAAiBkF,IACV,IAEXlF,EAAiB,KACV,IAUkBiF,GACpBL,GAAkBI,IAEoB,QAA3CT,EAAAa,SAASC,cAAc,4BAAoB,IAAAd,GAA3CA,EAA6Ce,UAAUC,IAAI,UAE3DvD,EAAAA,EACKC,KACD,uCACA,CACI1C,QACAG,WACA8F,SAAU9F,EACVE,cAEJ,CAAE6C,QAAS,CAAE,eAAgB,uCAE5BgD,KAAMC,IAAS,IAADC,EACf,MAAMC,EAASF,EAAIxD,KAYX,IAAD2D,GAXoC,QAA3CF,EAAAP,SAASC,cAAc,4BAAoB,IAAAM,GAA3CA,EAA6CL,UAAUQ,OAAO,UAE1DF,EAAOzD,UACP4D,IAAAA,QAAe,YACftB,EAAAA,EAAAA,IAAU,SAAUmB,EAAO1D,KAAK8D,cAChCvB,EAAAA,EAAAA,IAAU,aAAcmB,EAAO1D,KAAK3C,MAAO,CACvCoF,OAAQ,cACRD,KAAM,MAGV1D,KAEA+E,IAAAA,OAAwB,QAAXF,EAAAD,EAAO1D,YAAI,IAAA2D,OAAA,EAAXA,EAAaX,MAAO,yBAGpCe,MAAO5D,IAAW,IAAD6D,EAAAC,EAC6B,QAA3CD,EAAAd,SAASC,cAAc,4BAAoB,IAAAa,GAA3CA,EAA6CZ,UAAUQ,OAAO,UAC/B,OAAb,QAAdK,EAAA9D,EAAMN,gBAAQ,IAAAoE,OAAA,EAAdA,EAAgBC,SAChBL,IAAAA,MAAa,kDA2QjBM,MAAO,CACHC,WAAY,sDACZC,UAAW,uCAEf1D,UAAU,qKAAoKD,SAC7K,aAILD,EAAAA,EAAAA,MAAA,OAAKE,UAAU,uEAAsED,SAAA,EACjFE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,iCACfC,EAAAA,EAAAA,KAAA,QAAMD,UAAU,+CAA8CD,SAAC,sBAG/DE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,oCAGnBF,EAAAA,EAAAA,MAAA,OAAKE,UAAU,wDAAuDD,SAAA,EAClED,EAAAA,EAAAA,MAAA,UAAQa,QAASA,IA/NFgD,MACnB,IACqB,UAAbrG,EACkBQ,OAAOS,IAAIqF,KACzBnG,EACA,iBACAQ,KAGAwB,QAAQoE,IAAI,oCACZ/F,OAAOS,IAAIC,SAASC,KAAOhB,GAIbK,OAAO8F,KACrBnG,EACA,iBACAQ,KAGAwB,QAAQoE,IAAI,oCACZ/F,OAAOU,SAASC,KAAOhB,EAGnC,CAAE,MACEgC,QAAQoE,IAAI,4BACK,UAAbvG,EACAQ,OAAOS,IAAIC,SAASC,KAAOhB,EAE3BK,OAAOU,SAASC,KAAOhB,CAE/B,GAgM2BkG,GAAkB3D,UAAU,yHAAwHD,SAAA,EACvKE,EAAAA,EAAAA,KAAA,OAAAF,UACIE,EAAAA,EAAAA,KAAA,OAAK6D,IAAKC,EAAWC,IAAI,GAAGhE,UAAU,gBAE1CC,EAAAA,EAAAA,KAAA,KAAGD,UAAU,0BAAyBD,SAAC,eAE3CD,EAAAA,EAAAA,MAAA,UAAQa,QAASA,IAnMDsD,MACpB,IACqB,UAAb3G,EAEkBQ,OAAOS,IAAIqF,KACzBjG,EACA,gBACAM,KAGAH,OAAOS,IAAIC,SAASC,KAAOd,GAIbG,OAAO8F,KACrBjG,EACA,gBACAM,KAGAH,OAAOU,SAASC,KAAOd,EAGnC,CAAE,MAAO6B,GACY,UAAblC,EACAQ,OAAOS,IAAIC,SAASC,KAAOd,EAE3BG,OAAOU,SAASC,KAAOd,CAE/B,GAsK2BsG,GAAmBjE,UAAU,yHAAwHD,SAAA,EACxKE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,GAAED,UACbE,EAAAA,EAAAA,KAAA,OAAK6D,IAAKI,EAAUF,IAAI,GAAGhE,UAAU,gBAEzCC,EAAAA,EAAAA,KAAA,KAAGD,UAAU,0BAAyBD,SAAC,iBAI/CE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,0BAAyBD,UACpCD,EAAAA,EAAAA,MAAA,QAAME,UAAU,sCAAqCD,SAAA,CAAC,2BACzB,KACzBE,EAAAA,EAAAA,KAAA,QAAMU,QAASA,IAAM7C,OAAOS,IAAIC,SAASC,KAAO,SAAUuB,UAAU,0DAAyDD,SAAC,kBAO1I,CC5ZA,MAAMoE,EAAgB,CACtB,CACI/D,KAAM,gBACNgE,KAAM,+EACNC,KAAM,MAEV,CACIjE,KAAM,cACNgE,KAAM,oEACNC,KAAK,MAET,CACIjE,KAAM,eACNgE,KAAM,gFACNC,KAAM,MAEV,CACIjE,KAAM,eACNgE,KAAM,+EACNC,KAAM,OAIJC,EAAc,CAClBC,GAAI,eACJC,GAAI,eACJC,GAAI,eACJC,GAAI,gBAGAC,EAAgB,CACpB,eACA,eACA,eACA,eACA,eACA,eACA,eACA,eACA,eACA,gBAsCF,EAnC4BC,KAExB3E,EAAAA,EAAAA,KAAA,OAAKD,UAAU,kFAAiFD,UAC9FE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,mDAAkDD,SAC9D,IAAIoE,KAAkBA,GAAeU,IAAI,CAACC,EAAMC,KAC/C,MAAMC,EACJV,EAAYQ,EAAKT,OAASM,EAAcI,EAAQJ,EAAcM,QAEhE,OACEhF,EAAAA,EAAAA,KAAA,OAEED,UAAU,oGAAmGD,UAE7GD,EAAAA,EAAAA,MAAA,OAAKE,UAAU,+BAA8BD,SAAA,EAC3CE,EAAAA,EAAAA,KAAA,OACED,UAAW,8CAA8CgF,gDAAyDjF,UAElHE,EAAAA,EAAAA,KAAA,KAAAF,SAAI+E,EAAKT,UAEXvE,EAAAA,EAAAA,MAAA,OAAKE,UAAU,uCAAsCD,SAAA,EACnDE,EAAAA,EAAAA,KAAA,KAAGD,UAAU,oEAAmED,SAC7E+E,EAAKV,QAERnE,EAAAA,EAAAA,KAAA,KAAGD,UAAU,wDAAuDD,SACjE+E,EAAK1E,cAdP2E,SCrDbZ,EAAgB,CACtB,CACI/D,KAAM,cACNgE,KAAM,gIACNC,KAAM,MAEV,CACIjE,KAAM,eACNgE,KAAM,yHACNC,KAAM,MAEV,CACIjE,KAAM,cACNgE,KAAM,sHACNC,KAAM,MAEV,CACIjE,KAAM,UACNgE,KAAM,0GACNC,KAAM,OAIJC,EAAc,CAClBY,GAAI,eACJC,GAAI,eACJC,GAAI,eACJC,GAAI,gBAEAV,EAAgB,CACpB,eACA,eACA,eACA,eACA,eACA,eACA,eACA,eACA,eACA,gBAuCF,EApCmCW,KAE/BrF,EAAAA,EAAAA,KAAA,OAAKD,UAAU,6EAA4ED,UACzFE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,2EAA0ED,SACtF,IAAIoE,KAAkBA,GAAeU,IAAI,CAACC,EAAMC,KAC/C,MAAMC,EACJV,EAAYQ,EAAKT,OAASM,EAAcI,EAAQJ,EAAcM,QAEhE,OACEhF,EAAAA,EAAAA,KAAA,OAEED,UAAU,oGAAmGD,UAE7GD,EAAAA,EAAAA,MAAA,OAAKE,UAAU,+BAA8BD,SAAA,EAC3CE,EAAAA,EAAAA,KAAA,OACED,UAAW,8CAA8CgF,gDAAyDjF,UAElHE,EAAAA,EAAAA,KAAA,KAAAF,SAAI+E,EAAKT,UAEXvE,EAAAA,EAAAA,MAAA,OAAKE,UAAU,uCAAsCD,SAAA,EACnDE,EAAAA,EAAAA,KAAA,KAAGD,UAAU,oEAAmED,SAC7E+E,EAAKV,QAERnE,EAAAA,EAAAA,KAAA,KAAGD,UAAU,wDAAuDD,SACjE+E,EAAK1E,cAdP2E,S,yBC/CJ,SAASQ,EAAmBnJ,GAAoB,IAAnB,eAACC,GAAeD,EAC5D,OACI6D,EAAAA,EAAAA,KAAA,OAAKD,UAAU,gEAA+DD,UAC1ED,EAAAA,EAAAA,MAAA,OAAKE,UAAU,oBAAoBwD,MAAO,CAACgC,OAAQ,KAAKzF,SAAA,EACpDD,EAAAA,EAAAA,MAAA,OAAKE,UAAU,4DAA2DD,SAAA,EACtED,EAAAA,EAAAA,MAAA,OAAKE,UAAU,4CAA2CD,SAAA,EACtDE,EAAAA,EAAAA,KAAA,OAAK6D,IAAK2B,EAAezB,IAAI,GAAGhE,UAAU,yBAC1CC,EAAAA,EAAAA,KAAA,OAAK6D,IAAK4B,EAAoB1B,IAAI,GAAGhE,UAAU,4BAEnDC,EAAAA,EAAAA,KAAC2E,EAAmB,KACpB3E,EAAAA,EAAAA,KAACqF,EAA0B,KAC3BrF,EAAAA,EAAAA,KAAA,OAAKD,UAAU,4DAA2DD,UACtEE,EAAAA,EAAAA,KAAA,OAAK6D,IAAI,sBAAsBE,IAAI,WAG3C/D,EAAAA,EAAAA,KAAA,OAAKD,UAAU,iHAAgHD,UAC3HE,EAAAA,EAAAA,KAAC9D,EAAY,CAACE,eAAgBA,UAK9C,C", "sources": ["register/components/RegPopSignUp.jsx", "register/components/TestimonialScroller.jsx", "register/components/TestimonialScrollerReverse.jsx", "register/components/RegPopupContent.jsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from \"react\";\r\nimport axios from \"axios\";\r\nimport toastr from \"toastr\";\r\nimport { SetCookie, GetCookie } from \"../../core/utils/cookies\";\r\nimport { ValidatePassword } from \"../../core/utils/validation\";\r\nimport { useTranslation } from \"react-i18next\";\r\nimport imgApple from '../../assets/images/apple_ico.webp'\r\nimport imgGoogle from '../../assets/images/google_icon.png'\r\n\r\nexport default function RegPopSignup({handleRedirect}) {\r\nconst { t } = useTranslation();\r\nconst honeypotRef = useRef(null);\r\nconst [email, setEmail] = useState(\"\");\r\nconst [password, setPassword] = useState(\"\");\r\nconst [emailOptIn] = useState(false); // setEmailOptIn is intentionally unused for now\r\nconst [emailError, setEmailError] = useState(\"\");\r\nconst [passwordError, setPasswordError] = useState(\"\");\r\nconst [showPassword, setShowPassword] = useState(false);\r\nconst lpCookie = GetCookie(\"lp\");\r\nconst members = GetCookie(\"members\") || \"\";\r\nconst [googleUrl, setGoogleUrl] = useState(\"\");\r\nconst [appleUrl_0, setAppleUrl_0] = useState(\"\");\r\n\r\nconst screenWidth = window.screen.width;\r\nconst screenHeight = window.screen.height;\r\n\r\nconst windowWidth = 600;\r\nconst windowHeight = 700;\r\n\r\nconst left = (screenWidth - windowWidth) / 2;\r\nconst top = (screenHeight - windowHeight) / 2;\r\n\r\nconst windowFeatures = `width=${windowWidth},height=${windowHeight},top=${top},left=${left}`;\r\n\r\nconst validateEmail = () => {\r\nlet isPassed = false;\r\nif (!email) {\r\n    setEmailError(\r\n        t(\"echo.register.form.emailReqText\") || \"Email is required.\"\r\n    );\r\n} else if (!/\\S+@\\S+\\.\\S+/.test(email)) {\r\n    setEmailError(\r\n        t(\"echo.register.form.emailInvalidText\") || \"Invalid email address.\"\r\n    );\r\n} else {\r\n    setEmailError(\"\");\r\n    isPassed = true;\r\n}\r\n    return isPassed;\r\n};\r\n\r\nconst validatePassword = () => {\r\nlet msg = ValidatePassword(password);\r\nif (msg) {\r\n    setPasswordError(msg);\r\n    return false;\r\n}\r\nsetPasswordError(\"\");\r\nreturn true;\r\n};\r\n\r\nconst registerUser = () => {\r\nif (honeypotRef.current?.value) {\r\n    SetCookie(\"ishoneypot\", \"yes\", { path: \"/\" });\r\n    SetCookie(\"ishoneypot\", \"yes\", { domain: \".ai-pro.org\", path: \"/\" });\r\n}\r\n\r\nconst isEmailPassed = validateEmail();\r\nconst isPasswordPassed = validatePassword();\r\nif (!isEmailPassed || !isPasswordPassed) return;\r\n\r\ndocument.querySelector(\".loader-container\")?.classList.add(\"active\");\r\n\r\naxios\r\n    .post(\r\n    `${process.env.REACT_APP_API_URL}/t/register`,\r\n    {\r\n        email,\r\n        password,\r\n        pass_con: password,\r\n        emailOptIn,\r\n    },\r\n    { headers: { \"content-type\": \"application/x-www-form-urlencoded\" } }\r\n    )\r\n    .then((res) => {\r\n    const output = res.data;\r\n    document.querySelector(\".loader-container\")?.classList.remove(\"active\");\r\n\r\n    if (output.success) {\r\n        toastr.success(\"Success\");\r\n        SetCookie(\"access\", output.data.login_token);\r\n        SetCookie(\"user_email\", output.data.email, {\r\n            domain: \".ai-pro.org\",\r\n            path: \"/\",\r\n        });\r\n\r\n        redirectWithinIframe();\r\n    } else {\r\n        toastr.error(output.data?.msg || \"Registration failed\");\r\n    }\r\n    })\r\n    .catch((error) => {\r\n        document.querySelector(\".loader-container\")?.classList.remove(\"active\");\r\n        if (error.response?.status === 429) {\r\n            toastr.error(\"Too many requests. Please try again later.\");\r\n        }\r\n    });\r\n};\r\n\r\nconst redirectWithinIframe = () => {\r\n    const flow = GetCookie(\"flow\");\r\n    const chatpdf = GetCookie(\"chatpdf\");\r\n    const regPop = GetCookie(\"reg_pop\");\r\n\r\n    if (flow === \"chatpdf\") {\r\n        if (chatpdf === \"01\") {\r\n            if (lpCookie === \"aihub\") {\r\n                window.top.location.href = `https://${window.location.hostname}/chat.ai-pro.org`;\r\n            } else if (regPop === \"1\") {\r\n                window.self.location.href = \"/pay\";\r\n            } else {\r\n                window.location.href = \"/pay\";\r\n            }\r\n        } else {\r\n            if (lpCookie === \"aihub\") {\r\n                window.top.location.href = `https://${window.location.hostname}/chat.ai-pro.org`;\r\n            } else if (regPop === \"1\") {\r\n                window.self.location.href = `https://${window.location.hostname}/chatpdf.ai-pro.org`;\r\n            } else {\r\n                window.location.href = `https://${window.location.hostname}/chatpdf.ai-pro.org`;\r\n            }\r\n        }\r\n    } else {\r\n        if (members === \"on\") {\r\n            if (lpCookie === \"aihub\") {\r\n                window.top.location.href = \"/my-account\";\r\n            } else if (regPop === \"1\") {\r\n                window.self.location.href = \"/my-account\";\r\n            } else {\r\n                handleRedirect(\"/my-account\");\r\n            }\r\n        } else {\r\n            const pricing = GetCookie(\"pricing\");\r\n            const iSplanEnt = GetCookie(\"iSplanEnt\");\r\n\r\n            if (process.env.REACT_APP_ENTERPRISE_ID === pricing || iSplanEnt === '1') {\r\n                if (lpCookie === \"aihub\") {\r\n                    window.top.location.href = `https://${window.location.hostname}/chat.ai-pro.org`;\r\n                } else if (regPop === \"1\") {\r\n                    window.self.location.href = '/pay/mcWiDilmgQ';\r\n                } else {\r\n                    window.location.href = '/pay/mcWiDilmgQ';\r\n                }\r\n            } else {\r\n                if (lpCookie === \"aihub\") {\r\n                    window.top.location.href = `https://${window.location.hostname}/chat.ai-pro.org`;\r\n                } else if (regPop === \"1\") {\r\n                    window.self.location.href = '/pay';\r\n                } else {\r\n                    window.location.href = '/pay';\r\n                }\r\n            }\r\n        }\r\n    }\r\n};\r\n\r\nconst registerGoogle = () => {\r\n    try {\r\n        if (lpCookie === \"aihub\") {\r\n            const newWindow = window.top.open(\r\n                googleUrl,\r\n                \"GoogleRegister\",\r\n                windowFeatures\r\n            );\r\n            if (!newWindow) {\r\n                console.log(\"Error opening new GoogleRegister\");\r\n                window.top.location.href = googleUrl;\r\n            }\r\n        } else {\r\n            // Regular behavior for non-AI Hub\r\n            const newWindow = window.open(\r\n                googleUrl,\r\n                \"GoogleRegister\",\r\n                windowFeatures\r\n            );\r\n            if (!newWindow) {\r\n                console.log(\"Error opening new GoogleRegister\");\r\n                window.location.href = googleUrl;\r\n            }\r\n        }\r\n    } catch {\r\n        console.log(\"Error opening new window\");\r\n        if (lpCookie === \"aihub\") {\r\n            window.top.location.href = googleUrl;\r\n        } else {\r\n            window.location.href = googleUrl;\r\n        }\r\n    }\r\n}\r\n\r\nconst registerApple_0 = () => {\r\n    try {\r\n        if (lpCookie === \"aihub\") {\r\n            // Break out of iframe for AI Hub\r\n            const newWindow = window.top.open(\r\n                appleUrl_0,\r\n                \"AppleRegister\",\r\n                windowFeatures\r\n            );\r\n            if (!newWindow) {\r\n                window.top.location.href = appleUrl_0;\r\n            }\r\n        } else {\r\n            // Regular behavior for non-AI Hub\r\n            const newWindow = window.open(\r\n                appleUrl_0,\r\n                \"AppleRegister\",\r\n                windowFeatures\r\n            );\r\n            if (!newWindow) {\r\n                window.location.href = appleUrl_0;\r\n            }\r\n        }\r\n    } catch (error) {\r\n        if (lpCookie === \"aihub\") {\r\n            window.top.location.href = appleUrl_0;\r\n        } else {\r\n            window.location.href = appleUrl_0;\r\n        }\r\n    }\r\n}\r\n\r\nuseEffect(() => {\r\n    let isMounted = true;\r\n\r\n    const getGoogleUrl = async () => {\r\n        try {\r\n        const response = await axios.post(\r\n            `${process.env.REACT_APP_API_URL}/register-google`\r\n        );\r\n        if (isMounted && response.data.success && response.data.url) {\r\n            setGoogleUrl(response.data.url);\r\n        }\r\n        } catch (error) {\r\n        console.error(\"Error fetching Google register URL:\", error);\r\n        }\r\n    };\r\n\r\n    const getAppleUrl_0 = async () => {\r\n        try {\r\n        const response = await axios.post(\r\n            `${process.env.REACT_APP_API_URL}/register-apple`,\r\n            {\r\n            reg_num: \"0\",\r\n            },\r\n            { headers: { \"content-type\": \"application/x-www-form-urlencoded\" } }\r\n        );\r\n        if (response.data.success && response.data.url) {\r\n            setAppleUrl_0(response.data.url);\r\n        }\r\n        } catch (error) {\r\n        console.error(\"Error fetching Apple register URL:\", error);\r\n        }\r\n    };\r\n\r\n    getGoogleUrl();\r\n    getAppleUrl_0();\r\n\r\n    return () => {\r\n        isMounted = false;\r\n    };\r\n}, []);\r\n\r\nreturn (\r\n<section>\r\n    <div className=\"border-b-2 border-gray-200 pb-4 mb-4\">\r\n        <p className=\"text-[36px] text-[#3B5ABF] font-bold\">Sign Up</p>\r\n        <p className=\"text-[#000] text-[18px] font-bold mt-[20px]\">Get Started!</p>\r\n    </div>\r\n\r\n    <input\r\n        ref={honeypotRef}\r\n        type=\"text\"\r\n        name=\"user_phone\"\r\n        className=\"hidden absolute left-[-9999px]\"\r\n        autoComplete=\"off\"\r\n    />\r\n\r\n    <div className=\"mb-[20px]\">\r\n        <label className=\"font-semibold text-[14px] text-[#333]\">\r\n            Email Address\r\n        </label>\r\n    <input\r\n        type=\"email\"\r\n        name=\"email\"\r\n        value={email}\r\n        onChange={(e) => {\r\n        setEmail(e.target.value);\r\n        setEmailError(\"\");\r\n        }}\r\n        placeholder=\"Enter your email address\"\r\n        className=\"w-full h-[48px] mt-[8px] px-4 py-2 bg-[#f7f7f7] text-[14px] rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n    />\r\n    {emailError && (\r\n        <p className=\"text-red-500 text-sm mt-1\">{emailError}</p>\r\n    )}\r\n    </div>\r\n\r\n    <div className=\"mb-[20px]\">\r\n        <label className=\"font-semibold text-[14px] text-[#333]\">\r\n            Password\r\n        </label>\r\n        <div className=\"relative\">\r\n            <input\r\n            type={showPassword ? \"text\" : \"password\"}\r\n            name=\"password\"\r\n            value={password}\r\n            onChange={(e) => {\r\n                setPassword(e.target.value);\r\n                setPasswordError(\"\");\r\n            }}\r\n            placeholder=\"Enter your password\"\r\n            className=\"w-full h-[48px] mt-[8px] px-4 py-2 bg-[#f7f7f7] rounded-md text-[14px] focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n            />\r\n            <div\r\n            className=\"absolute inset-y-0 right-3 flex items-center cursor-pointer text-gray-500\"\r\n            onClick={() => setShowPassword((prev) => !prev)}\r\n            >\r\n            {showPassword ? (\r\n                <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                className=\"mt-[6px]\"\r\n                width=\"20\"\r\n                height=\"20\"\r\n                viewBox=\"0 0 24 24\"\r\n                fill=\"none\"\r\n                stroke=\"#708090\"\r\n                strokeWidth=\"2\"\r\n                strokeLinecap=\"round\"\r\n                strokeLinejoin=\"round\"\r\n                >\r\n                <path d=\"M1 12s4-7 11-7 11 7 11 7-4 7-11 7-11-7-11-7z\" />\r\n                <path d=\"M3 3l18 18\" />\r\n                <circle cx=\"12\" cy=\"12\" r=\"3\" />\r\n                </svg>\r\n            ) : (\r\n                <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                className=\"mt-[6px]\"\r\n                width=\"20\"\r\n                height=\"20\"\r\n                viewBox=\"0 0 24 24\"\r\n                fill=\"none\"\r\n                stroke=\"#708090\"\r\n                strokeWidth=\"2\"\r\n                strokeLinecap=\"round\"\r\n                strokeLinejoin=\"round\"\r\n                >\r\n                <path d=\"M1 12s4-7 11-7 11 7 11 7-4 7-11 7-11-7-11-7z\" />\r\n                <circle cx=\"12\" cy=\"12\" r=\"3\" />\r\n                </svg>\r\n            )}\r\n            </div>\r\n        </div>\r\n        {passwordError && (\r\n            <p className=\"text-red-500 text-sm mt-1\">{passwordError}</p>\r\n        )}\r\n    </div>\r\n\r\n    <button\r\n        onClick={registerUser}\r\n        style={{\r\n            background: \"linear-gradient(to right, #3D57BB 0%, #268EEC 100%)\",\r\n            boxShadow: \"0px 4px 5px 0px rgba(0, 0, 0, 0.13)\",\r\n        }}\r\n        className=\"w-full h-[48px] text-white font-semibold text-[20px] rounded-md hover:bg-[#3B5ABF] focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:cursor-not-allowed\"\r\n        >\r\n        Sign Up\r\n    </button>\r\n\r\n    <div className=\"flex items-center mb-4 mt-[24px] mx-auto custom-lg:w-[60%] w-[283px]\">\r\n        <div className=\"flex-grow h-px bg-[#E6E6E6]\" />\r\n        <span className=\"px-3 text-sm text-gray-600 whitespace-nowrap\">\r\n            or continue with\r\n        </span>\r\n        <div className=\"flex-grow h-px bg-[#E6E6E6]\" />\r\n    </div>\r\n\r\n    <div className=\"flex gap-[16px] justify-center items-center mb-[20px]\">\r\n        <button onClick={() => registerGoogle()} className=\"flex items-center justify-center border w-[122px] h-[38px] border-gray-300 py-[7.3px] px-[22px] rounded-full gap-[6px]\">\r\n            <div>\r\n                <img src={imgGoogle} alt=\"\" className=\"w-[19px]\"/>\r\n            </div>\r\n            <p className=\"text-[14px] text-[#333]\">Google</p>\r\n        </button>\r\n        <button onClick={() => registerApple_0()} className=\"flex items-center justify-center w-[122px] h-[38px] border border-gray-300 py-[7.3px] px-[22px] rounded-full gap-[6px]\">\r\n            <div className=\"\">\r\n                <img src={imgApple} alt=\"\" className=\"w-[15px]\" />  \r\n            </div>\r\n            <p className=\"text-[14px] text-[#333]\">Apple</p>\r\n        </button>\r\n    </div>\r\n\r\n    <div className=\"mx-auto mb-[20px] w-fit\">\r\n        <span className=\"text-center text-[14px] text-[#333]\">\r\n            Already have an account?{\" \"}\r\n            <span onClick={() => window.top.location.href = \"/login\"} className=\"text-[#3B5ABF] cursor-pointer font-bold hover:underline\">\r\n            Log In\r\n            </span>\r\n        </span>\r\n    </div>\r\n</section>\r\n);\r\n}\r\n", "\r\nconst testimonialUp = [\r\n{\r\n    name: \"<PERSON>\",\r\n    desc: 'I use AI as a tool to float questions, in order for me to write a narrative…',\r\n    init: 'FE'\r\n},\r\n{\r\n    name: \"<PERSON>\",\r\n    desc: `I feel like I've stumbled into a creative playground of the mind…`,\r\n    init:'DH'\r\n},\r\n{\r\n    name: \"Info TapTech\",\r\n    desc: `It meets expectations and gets better every time I use it. A real-time saver…`,\r\n    init: 'IT'\r\n},\r\n{\r\n    name: \"<PERSON>\",\r\n    desc: `Simple and easy to use. I created a resume after 8 years in less than 2mins.`,\r\n    init: 'TR'\r\n},\r\n]\r\n// Assign specific color per init\r\nconst initToColor = {\r\n  FE: \"bg-[#6A5ACD]\",\r\n  DH: \"bg-[#944D31]\",\r\n  IT: \"bg-[#20B2AA]\",\r\n  TR: \"bg-[#DF4B3D]\",\r\n};\r\n\r\nconst defaultColors = [\r\n  \"bg-[#6A5ACD]\",\r\n  \"bg-[#3CB371]\",\r\n  \"bg-[#FFB347]\",\r\n  \"bg-[#20B2AA]\",\r\n  \"bg-[#6495ED]\",\r\n  \"bg-[#FF69B4]\",\r\n  \"bg-[#944D31]\",\r\n  \"bg-[#DBBDC8]\",\r\n  \"bg-[#AF9D91]\",\r\n  \"bg-[#DF4B3D]\",\r\n];\r\n\r\nconst TestimonialScroller = () => {\r\n  return (\r\n    <div className=\"overflow-hidden whitespace-nowrap w-[94%] relative flex-nowrap inline-flex mb-2\">\r\n      <div className=\"animate-marquee flex items-center justify-center\">\r\n        {[...testimonialUp, ...testimonialUp].map((item, index) => {\r\n          const colorClass =\r\n            initToColor[item.init] || defaultColors[index % defaultColors.length];\r\n\r\n          return (\r\n            <div\r\n              key={index}\r\n              className=\"bg-white rounded-3xl p-[16px] w-[418px] max-w-[418px] border-2 border-[#E7E7E7] inline-block mr-2\"\r\n            >\r\n              <div className=\"flex gap-[20px] items-center\">\r\n                <div\r\n                  className={`rounded-full text-[22px] w-[50px] h-[50px] ${colorClass} text-white flex justify-center items-center`}\r\n                >\r\n                  <p>{item.init}</p>\r\n                </div>\r\n                <div className=\"flex-1 leading-[20px] text-[#616161]\">\r\n                  <p className=\"text-[14px] leading-[16px] break-words whitespace-normal mb-[8px]\">\r\n                    {item.desc}\r\n                  </p>\r\n                  <p className=\"text-[12px] break-words whitespace-normal font-medium\">\r\n                    {item.name}\r\n                  </p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          );\r\n        })}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\nexport default TestimonialScroller;\r\n", "\r\nconst testimonialUp = [\r\n{\r\n    name: \"<PERSON>\",\r\n    desc: `This AI platform has improved my research skills, serving as a tool that helps me discover better ways to access information.`,\r\n    init: 'AH'\r\n},\r\n{\r\n    name: \"<PERSON>\",\r\n    desc: `I used it to write my ideas for my artwork…it has been excellent because I have been able to express my ideas clearly.`,\r\n    init: 'HP'\r\n},\r\n{\r\n    name: \"<PERSON>\",\r\n    desc: `…What I appreciate most about this AI program is its versatility. It truly is only limited by your own imagination.`,\r\n    init: '<PERSON>'\r\n},\r\n{\r\n    name: \"<PERSON> <PERSON>\",\r\n    desc: `…when I am curious about something, I use this chat. It never judges me for my thousands of questions!!`,\r\n    init: 'DG'\r\n},\r\n]\r\n\r\nconst initToColor = {\r\n  AH: \"bg-[#6A5ACD]\",\r\n  HP: \"bg-[#3CB371]\",\r\n  JP: \"bg-[#FFB347]\",\r\n  DG: \"bg-[#944D31]\", // <- DG stays purple\r\n};\r\nconst defaultColors = [\r\n  \"bg-[#6A5ACD]\",\r\n  \"bg-[#3CB371]\",\r\n  \"bg-[#FFB347]\",\r\n  \"bg-[#20B2AA]\",\r\n  \"bg-[#6495ED]\",\r\n  \"bg-[#FF69B4]\",\r\n  \"bg-[#944D31]\",\r\n  \"bg-[#DBBDC8]\",\r\n  \"bg-[#AF9D91]\",\r\n  \"bg-[#DF4B3D]\",\r\n];\r\n\r\nconst TestimonialScrollerReverse = () => {\r\n  return (\r\n    <div className=\"overflow-hidden whitespace-nowrap w-[94%] relative flex-nowrap inline-flex\">\r\n      <div className=\"animate-marqueeReverse flex items-center justify-center md:justify-start\">\r\n        {[...testimonialUp, ...testimonialUp].map((item, index) => {\r\n          const colorClass =\r\n            initToColor[item.init] || defaultColors[index % defaultColors.length];\r\n\r\n          return (\r\n            <div\r\n              key={index}\r\n              className=\"bg-white rounded-3xl p-[16px] w-[418px] max-w-[418px] border-2 border-[#E7E7E7] inline-block mr-2\"\r\n            >\r\n              <div className=\"flex gap-[20px] items-center\">\r\n                <div\r\n                  className={`rounded-full text-[22px] w-[50px] h-[50px] ${colorClass} text-white flex justify-center items-center`}\r\n                >\r\n                  <p>{item.init}</p>\r\n                </div>\r\n                <div className=\"flex-1 leading-[20px] text-[#616161]\">\r\n                  <p className=\"text-[14px] leading-[16px] break-words whitespace-normal mb-[8px]\">\r\n                    {item.desc}\r\n                  </p>\r\n                  <p className=\"text-[12px] break-words whitespace-normal font-medium\">\r\n                    {item.name}\r\n                  </p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          );\r\n        })}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default TestimonialScrollerReverse;\r\n", "import RegPopSignup from './RegPopSignUp';\r\nimport TestimonialScroller from './TestimonialScroller';\r\nimport TestimonialScrollerReverse from './TestimonialScrollerReverse';\r\nimport AiChatbotUser from '../../assets/images/ai-chatbot-user.png'\r\nimport AvailableOnBrowser from '../../assets/images/available-on-browsers.png'\r\n\r\nexport default function RegPopSignupContent({handleRedirect}) {\r\nreturn (\r\n    <div className=\"relative flex-col w-full text-gray-600 bg-transparent text-sm\">\r\n        <div className='flex items-center' style={{zIndex: '1'}}>\r\n            <div className='flex-1 w-[20%] hidden xl:block lg:block md:block sm:block'>\r\n                <div className='flex flex-col items-center justify-center'>\r\n                    <img src={AiChatbotUser} alt=\"\" className=\"w-[308px] h-[193px]\" />\r\n                    <img src={AvailableOnBrowser} alt=\"\" className=\" h-[59px] mb-[1rem]\" />  \r\n                </div>\r\n                <TestimonialScroller />\r\n                <TestimonialScrollerReverse />\r\n                <div className='absolute top-[100px] left-[-93px] w-full h-[100px] z-[-1]'>\r\n                    <img src=\"/assets/bg-grid.png\" alt=\"\" />\r\n                </div>\r\n            </div>\r\n            <div className='flex-1 px-[20px] xl:px-[47px] lg:px-[47px] md:px-[47px] sm:px-[47px] pt-[40px] xl:pt-0 lg:pt-0 md:pt-0 sm:pt-0'>\r\n                <RegPopSignup handleRedirect={handleRedirect} />\r\n            </div>  \r\n        </div>\r\n    </div>\r\n)\r\n}\r\n"], "names": ["RegPopSignup", "_ref", "handleRedirect", "t", "useTranslation", "honeypotRef", "useRef", "email", "setEmail", "useState", "password", "setPassword", "emailOptIn", "emailError", "setEmailError", "passwordError", "setPasswordError", "showPassword", "setShowPassword", "lpC<PERSON>ie", "Get<PERSON><PERSON><PERSON>", "members", "googleUrl", "setGoogleUrl", "appleUrl_0", "setAppleUrl_0", "screenWidth", "window", "screen", "width", "windowFeatures", "height", "redirectWithinIframe", "flow", "chatpdf", "regPop", "top", "location", "href", "hostname", "self", "pricing", "iSplanEnt", "process", "useEffect", "isMounted", "async", "response", "axios", "post", "data", "success", "url", "error", "console", "getGoogleUrl", "reg_num", "headers", "getAppleUrl_0", "_jsxs", "children", "className", "_jsx", "ref", "type", "name", "autoComplete", "value", "onChange", "e", "target", "placeholder", "onClick", "prev", "xmlns", "viewBox", "fill", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "d", "cx", "cy", "r", "registerUser", "_honeypotRef$current", "_document$querySelect", "current", "<PERSON><PERSON><PERSON><PERSON>", "path", "domain", "isEmailPassed", "validateEmail", "isPassed", "test", "isPasswordPassed", "validatePassword", "msg", "ValidatePassword", "document", "querySelector", "classList", "add", "pass_con", "then", "res", "_document$querySelect2", "output", "_output$data", "remove", "toastr", "login_token", "catch", "_document$querySelect3", "_error$response", "status", "style", "background", "boxShadow", "registerGoogle", "open", "log", "src", "imgGoogle", "alt", "registerApple_0", "imgApple", "testimonialUp", "desc", "init", "initToColor", "FE", "DH", "IT", "TR", "defaultColors", "TestimonialScroller", "map", "item", "index", "colorClass", "length", "AH", "HP", "JP", "DG", "TestimonialS<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "RegPopSignupContent", "zIndex", "AiChatbotUser", "AvailableOnBrowser"], "sourceRoot": ""}