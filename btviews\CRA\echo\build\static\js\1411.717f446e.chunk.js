"use strict";(self.webpackChunkv1=self.webpackChunkv1||[]).push([[1411,9044,7250],{72608:(e,_,A)=>{A.d(_,{Z:()=>t});A(72791);const t=A.p+"static/media/AIPRO.0580715cba3cbc2179c46b033d087db9.svg"},27250:(e,_,A)=>{A.r(_),A.d(_,{default:()=>o});var t=A(72791),P=A(72608),r=A(19878),R=A(28891),a=(A(29534),A(80184));const o=function(e){let{hideNavLink:_,auth:o=(0,R.gx)()}=e;const n=window.location.pathname,s=/\/start-chatgpt-go\/?$/.test(n),E=/\/text-to-image\/?$/.test(n),i=/\/start-chatgpt-v2\/?$/.test(n),c=n.includes("/register-auth"),D=!_,l={NODE_ENV:"production",PUBLIC_URL:"/themes/echo",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_API_URL:"http://localhost:9002/api",REACT_APP_BASE_URL:"http://localhost:9002/",REACT_APP_BASIC_ANNUAL_DOWNGRADE_ID:"15",REACT_APP_BASIC_ANNUAL_DOWNGRADE_ID_AED:"45",REACT_APP_BASIC_ANNUAL_DOWNGRADE_ID_BRL:"37",REACT_APP_BASIC_ANNUAL_DOWNGRADE_ID_EUR:"33",REACT_APP_BASIC_ANNUAL_DOWNGRADE_ID_GBP:"29",REACT_APP_BASIC_ANNUAL_DOWNGRADE_ID_SAR:"41",REACT_APP_BASIC_ANNUAL_UPGRADE_ID:"16,20,73,75",REACT_APP_BASIC_ANNUAL_UPGRADE_ID_AED:"46,48,84,85",REACT_APP_BASIC_ANNUAL_UPGRADE_ID_BRL:"38,40,80,81",REACT_APP_BASIC_ANNUAL_UPGRADE_ID_EUR:"34,36,78,79",REACT_APP_BASIC_ANNUAL_UPGRADE_ID_GBP:"30,32,76,77",REACT_APP_BASIC_ANNUAL_UPGRADE_ID_SAR:"42,44,82,83",REACT_APP_BASIC_UPGRADE_ID:"16,19,20,73,75",REACT_APP_BASIC_UPGRADE_ID2:"16,20,73,75",REACT_APP_BASIC_UPGRADE_ID_AED:"46,47,48,84,85",REACT_APP_BASIC_UPGRADE_ID_BRL:"38,39,40,80,81",REACT_APP_BASIC_UPGRADE_ID_EUR:"34,35,36,78,79",REACT_APP_BASIC_UPGRADE_ID_GBP:"30,31,32,76,77",REACT_APP_BASIC_UPGRADE_ID_SAR:"42,43,44,82,83",REACT_APP_BOT_TOKEN:"XXLx73ThdbDdKDmqajsv",REACT_APP_BTUTIL_API_URL:"https://dev.api.ai-pro.org/",REACT_APP_BTUTIL_CSS_URL:"https://dev.api.ai-pro.org/ext-app/css/btutil-regUpgradeModal-v1.min.css?ver=",REACT_APP_CHATHEAD_URL:"https://staging.sitebot.ai-pro.org/chat.js",REACT_APP_DEFAULT_PPG:"12",REACT_APP_DOWNGRADE_ID:"29,30,31,32",REACT_APP_DOWNGRADE_ID_AED:"45,46,47,48",REACT_APP_DOWNGRADE_ID_BRL:"37,38,39,40",REACT_APP_DOWNGRADE_ID_EUR:"33,34,35,36",REACT_APP_DOWNGRADE_ID_GBP:"29,30,31,32",REACT_APP_DOWNGRADE_ID_SAR:"41,42,43,44",REACT_APP_ENTERPRISE_ID:"62",REACT_APP_HOST_ENV:"production",REACT_APP_PROMAX_ANNUAL_DOWNGRADE_ID:"15,16,19,20,73",REACT_APP_PROMAX_ANNUAL_DOWNGRADE_ID_AED:"45,46,47,48,84",REACT_APP_PROMAX_ANNUAL_DOWNGRADE_ID_BRL:"37,38,39,40,80",REACT_APP_PROMAX_ANNUAL_DOWNGRADE_ID_EUR:"33,34,35,36,78",REACT_APP_PROMAX_ANNUAL_DOWNGRADE_ID_GBP:"29,30,31,32,76",REACT_APP_PROMAX_ANNUAL_DOWNGRADE_ID_SAR:"41,42,43,44,82",REACT_APP_PROMAX_ANNUAL_UPGRADE_ID:"62",REACT_APP_PROMAX_DOWNGRADE_ID:"70,74 #PRO YEARLY, PRO MONTHLY",REACT_APP_PROMAX_DOWNGRADE_ID_AED:"48,46 #PRO YEARLY, PRO MONTHLY",REACT_APP_PROMAX_DOWNGRADE_ID_BRL:"40,38 #PRO YEARLY, PRO MONTHLY",REACT_APP_PROMAX_DOWNGRADE_ID_EUR:"36,34 #PRO YEARLY, PRO MONTHLY",REACT_APP_PROMAX_DOWNGRADE_ID_GBP:"32,30 #PRO YEARLY, PRO MONTHLY",REACT_APP_PROMAX_DOWNGRADE_ID_SAR:"44,42 #PRO YEARLY, PRO MONTHLY",REACT_APP_PROMAX_UPGRADE_ID:"75,62  YEARLY, ENTERPRISE",REACT_APP_PROMAX_UPGRADE_ID_BRL:"81 #PROMAX YEARLY",REACT_APP_PROMAX_UPGRADE_ID_EUR:"79 #PROMAX YEARLY",REACT_APP_PROMAX_UPGRADE_ID_GBP:"77 #PROMAX YEARLY",REACT_APP_PROMAX_UPGRADE_ID_SAR:"85 #PROMAX YEARLY",REACT_APP_PRO_ANNUAL_DOWNGRADE_ID:"15,16,19",REACT_APP_PRO_ANNUAL_DOWNGRADE_ID_AED:"45,46,47",REACT_APP_PRO_ANNUAL_DOWNGRADE_ID_BRL:"37,38,39",REACT_APP_PRO_ANNUAL_DOWNGRADE_ID_EUR:"33,34,35",REACT_APP_PRO_ANNUAL_DOWNGRADE_ID_GBP:"29,30,31",REACT_APP_PRO_ANNUAL_DOWNGRADE_ID_SAR:"41,42,43",REACT_APP_PRO_ANNUAL_UPGRADE_ID:"62",REACT_APP_PRO_ANNUAL_UPGRADE_ID_AED:"84,85",REACT_APP_PRO_ANNUAL_UPGRADE_ID_BRL:"80,81",REACT_APP_PRO_ANNUAL_UPGRADE_ID_EUR:"78,79",REACT_APP_PRO_ANNUAL_UPGRADE_ID_GBP:"76,77",REACT_APP_PRO_ANNUAL_UPGRADE_ID_SAR:"82,83",REACT_APP_PRO_DOWNGRADE_BASIC_ID:"15,19",REACT_APP_PRO_DOWNGRADE_BASIC_ID_AED:"45,47",REACT_APP_PRO_DOWNGRADE_BASIC_ID_BRL:"37,39",REACT_APP_PRO_DOWNGRADE_BASIC_ID_EUR:"33,35",REACT_APP_PRO_DOWNGRADE_BASIC_ID_GBP:"29,31",REACT_APP_PRO_DOWNGRADE_BASIC_ID_SAR:"41,43",REACT_APP_PRO_MAX_UPGRADE_ID:"73,75",REACT_APP_PRO_MAX_UPGRADE_ID_AED:"84,85",REACT_APP_PRO_MAX_UPGRADE_ID_BRL:"80,81",REACT_APP_PRO_MAX_UPGRADE_ID_EUR:"78,79",REACT_APP_PRO_MAX_UPGRADE_ID_GBP:"76,77",REACT_APP_PRO_MAX_UPGRADE_ID_SAR:"80,81",REACT_APP_PRO_UPGRADE_ID:"20,73,75",REACT_APP_PRO_UPGRADE_ID_AED:"48,84,85",REACT_APP_PRO_UPGRADE_ID_BRL:"40,80,81",REACT_APP_PRO_UPGRADE_ID_EUR:"36,78,79",REACT_APP_PRO_UPGRADE_ID_GBP:"32,76,77",REACT_APP_PRO_UPGRADE_ID_SAR:"44,82,83",REACT_APP_STRIPE_PUBLIC_KEY_LIVE:"pk_live_51MH0TVLtUaKDxQEZNwiJOL1O8aLIA6fQEyI7cqjDnuSnMXwnOjOtu2JHjRD6PhF6hyoQAfM91dxrxNUEdyoCv9m900CPfMnfSk",REACT_APP_STRIPE_PUBLIC_KEY_TEST:"pk_test_51MH0TVLtUaKDxQEZH5ODSKmyw5TSm1lEVwyKkhVbNPZCv83lu4xL2aK8NbiJkkeG9XJt6td7kRLET8gpby37dKTs00uLTgkXVr",REACT_APP_TOOLS_URL:'{"chatbot":"https://chat.ai-pro.org/chatbot","chat":"https://chat.ai-pro.org/chat","chatpro":"https://chatpro.ai-pro.org/chat","chatgpt":"https://chatgpt.ai-pro.org","chatpdf":"https://chatpdf.ai-pro.org","convert2english":"https://app.ai-pro.org/convert-to-proper-english","interiorgpt":"https://interiorgpt.ai-pro.org","promptlibrary":"https://chatlibrary.ai-pro.org","removebg":"https://clearbg.ai-pro.org","restorephoto":"https://restorephotos.ai-pro.org","stablediffusion":"https://ai-pro.org/ai-tool-stable-diffusion","txt2img":"https://app.ai-pro.org/create-art"}'}.REACT_APP_ShowMaintenanceBanner||"";(0,t.useEffect)(()=>{const e=d(P.Z,"image"),_=d(r,"image");return document.head.append(e,_),c||Promise.all([A.e(7749),A.e(1707)]).then(A.bind(A,51707)),()=>{e.remove(),_.remove()}},[c]);const d=(e,_)=>{const A=document.createElement("link");return A.rel="preload",A.href=e,A.as=_,A};return(0,a.jsxs)(a.Fragment,{children:[l&&(0,a.jsx)("div",{id:"maintenance-container",className:"p-[5px] bg-[#f7a73f] text-center",children:"We are currently undergoing maintenance. We're expecting to be back at 4 AM EST."}),(0,a.jsx)("header",{id:"header",className:"headernav border-gray-500 bg-white text-black w-3/4 mx-auto rounded-3xl mt-3 py-2 z-[8888] "+(l?"top-[60px]":""),children:(0,a.jsxs)("div",{className:"container mx-auto flex justify-between items-center px-4",children:[(0,a.jsxs)("picture",{className:"aiprologo",children:[(0,a.jsx)("source",{type:"image/webp",srcSet:r,width:"150",height:"52",className:"aiprologo"}),(0,a.jsx)("img",{src:P.Z,alt:"AI-Pro Logo",className:"aiprologo"})]}),(s||E||i)&&D&&(0,a.jsx)("nav",{className:"text-xs lg:text-sm block inline-flex",id:"menu",children:(0,a.jsx)("ul",{className:"headnav flex inline-flex",children:(0,a.jsx)("li",{className:"mr-1 md:mr-2 lg:mr-6",children:(0,a.jsx)("a",{href:o?"/my-account":"/login",className:"font-bold","aria-label":o?"my-account":"login",children:o?"My Apps":"LOG IN"})})})})]})})]})}},20869:(e,_,A)=>{A.r(_),A.d(_,{default:()=>E});var t=A(72791),P=A(27250),r=A(28891),R=A(31243),a=A(54270),o=A(95828),n=A.n(o),s=(A(92831),A(80184));const E=function(){(0,t.useEffect)(()=>{n().options={positionClass:"toast-top-center"}},[]);const[e,_]=(0,t.useState)(""),[A,o]=(0,t.useState)(""),[E,i]=(0,t.useState)(),[c,D]=(0,t.useState)(!1),l=(0,r.gx)("/login");if(void 0===l||!1===l)return;return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(a.q,{children:[(0,s.jsx)("title",{children:"AI Pro | Enterprise Account Payment Confirmation"}),(0,s.jsx)("meta",{name:"description",content:"Safely complete your purchase with our secure payment options. Buy now with confidence!"})]}),(0,s.jsx)(P.default,{auth:l}),(0,s.jsx)("div",{className:"Payment bg-gray-100 min-h-[600px] mx-auto",children:(0,s.jsx)("form",{onSubmit:_=>{if(_.preventDefault(),function(){document.querySelector("#amountPaid").classList.remove("border-red-500"),document.querySelector("#referenceNumber").classList.remove("border-red-500");var _=!1;""===A.trim()&&(document.querySelector("#amountPaid").classList.add("border-red-500"),_=!0);""===e.trim()&&(document.querySelector("#referenceNumber").classList.add("border-red-500"),_=!0);return _}())return;var t=E;document.querySelector(".loader-container").classList.add("active");const P=new FormData;P.append("file",t),P.append("reference_number",e),P.append("amount",A),R.Z.post("http://localhost:9002/api/t/send-enterprise-payment-confirm",P,{headers:{"content-type":"multipart/form-data"}}).then(function(e){e.data.success?window.location.href="/payment-confirm":n().error("Email Failed.")}).catch(function(e){document.querySelector(".loader-container").classList.remove("active"),e.response&&429===e.response.status&&(document.querySelector(".loader-container").classList.remove("active"),n().error("Sorry, too many requests. Please try again in a bit!"))})},children:(0,s.jsx)("div",{className:"container mx-auto py-10",children:(0,s.jsxs)("div",{className:"flex flex-col items-center py-8 lg:py-16",children:[(0,s.jsx)("h2",{className:"text-xl font-bold mb-4 py-4 md:py-10 text-center",children:"Enterprise Account Payment Confirmation"}),(0,s.jsxs)("div",{className:"py-2 text-sm text-center",children:[(0,s.jsx)("p",{children:"Please enter the payment reference number below."}),(0,s.jsx)("p",{children:"An account manager will contact you upon confirmation."})]}),(0,s.jsxs)("div",{className:"mx-auto py-4",children:[(0,s.jsx)("input",{className:"w-full mb-2 px-3 py-2 border border-gray-300 rounded fs-exclude",type:"text",id:"amountPaid",name:"amountPaid",placeholder:"Amount Paid*",value:A,onChange:e=>{let _=e.target.value;_=_.replace(/\D/g,""),_=_.slice(0,5),o(_)}}),(0,s.jsx)("input",{className:"w-full mb-2 px-3 py-2 border border-gray-300 rounded fs-exclude",type:"text",id:"referenceNumber",name:"referenceNumber",placeholder:"Reference Number*",value:e,onChange:e=>{let A=e.target.value;_(A)}})]}),(0,s.jsxs)("div",{className:"mx-auto text-center",children:[(0,s.jsx)("label",{className:"text-xs block mb-0 mt-1 text-center",children:"Upload your payment receipt for easier verification."}),(0,s.jsx)("label",{for:"file",class:"font-bold bg-gray-100 block cursor-pointer custom-file-upload border border-gray-300 px-4 py-2 my-2",children:"Upload File"}),(0,s.jsx)("input",{className:"order border-gray-300 px-4 py-2 my-2",id:"file",type:"file",name:"file",onChange:e=>{i(e.target.files[0]),D(!0)}})]}),c?(0,s.jsx)("div",{className:"text-[12px] text-center text-blue-800",children:(0,s.jsxs)("p",{className:"",children:["Filename: ",E.name]})}):null,(0,s.jsx)("div",{children:(0,s.jsx)("button",{className:"mt-6 bg-blue-500 px-4 py-2 rounded text-white",type:"submit",children:"Send Confirmation"})})]})})})})]})}},29534:()=>{},19878:(e,_,A)=>{e.exports=A.p+"static/media/AIPRO.84104dfd05446283b05c.webp"}}]);
//# sourceMappingURL=1411.717f446e.chunk.js.map