"use strict";(self.webpackChunkv1=self.webpackChunkv1||[]).push([[7780,9044],{63566:(e,s,t)=>{t.r(s),t.d(s,{default:()=>f});var a=t(72791),l=t(96347),c=(t(48297),t(27250)),i=t(56355),r=t(63019),n=t(91615),d=t(28891),o=t(74335),m=t(31243),u=t(95828),x=t.n(u),h=(t(92831),t(80184));const f=function(){const e=(0,d.gx)("/register-auth"),[s]=(0,a.useState)(window.view_data?window.view_data:{}),[t]=(0,a.useState)(s.plan?s.plan:null),[u]=(0,a.useState)(s.members?s.members:null),[f,p]=(0,a.useState)(0),[w,b]=(0,a.useState)(""),[j,v]=(0,a.useState)(0);if((0,a.useEffect)(()=>{void 0!==e&&"enterprise"===e.plan&&(v(e.price_per_member),"monthly"===e.interval.toLowerCase()?b("monthly"):b("yearly"))},[e]),(0,a.useEffect)(()=>{p(u*j)},[u,j]),(0,a.useEffect)(()=>{x().options={positionClass:"toast-top-center"}},[]),void 0===e||!1===e)return;const g=(0,o.bG)("access");return(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(c.default,{auth:e}),t?(0,h.jsx)("div",{className:"Payment-upgrade bg-gray-100 md:min-h-[90vh] flex",children:(0,h.jsx)("div",{className:"container mx-auto py-10",children:(0,h.jsx)("div",{className:"flex flex-col items-center py-10 lg:py-16",children:(0,h.jsx)("div",{className:"flex flex-wrap md:flex-wrap justify-center w-full",children:(0,h.jsxs)("div",{className:"pay_right px-4 mb-8 md:w-2/5",children:[(0,h.jsx)("h2",{className:"text-xl font-bold mb-4 py-10",children:"Order Summary"}),(0,h.jsxs)("div",{children:[(0,h.jsx)("div",{className:"block",children:(0,h.jsx)("div",{className:"mb-2 w-full text-sm mr-4",children:(0,h.jsx)("b",{className:"text-md text-uppercase",children:"ENTERPRISE PLAN"})})}),(0,h.jsxs)("div",{className:"flex w-full",children:[(0,h.jsx)("div",{className:"mb-2 w-4/5 text-sm mr-4 left",children:(0,h.jsx)("b",{children:"Total number of member added:"})}),(0,h.jsx)("div",{className:"font-bold w-1/5 text-right",children:u})]}),(0,h.jsxs)("div",{className:"flex w-full",children:[(0,h.jsx)("div",{className:"mb-2 w-4/5 text-sm mr-4 left",children:(0,h.jsx)("b",{children:"Total amount:"})}),(0,h.jsxs)("div",{className:"font-bold w-1/5 text-right",children:["$",f]})]}),(0,h.jsx)("div",{className:"block",children:(0,h.jsxs)("div",{className:"mb-5 w-full text-sm mr-4",children:["Your subscription will renew ",w," until you cancel it."]})})]}),(0,h.jsx)("div",{className:"flex",children:(0,h.jsx)("div",{className:"mb-2 w-full text-sm",children:(0,h.jsx)(l.E.button,{className:"bg-blue-500 text-white font-bold py-3 px-6 rounded-lg w-full my-4 proceed-pmt",whileHover:{backgroundColor:"#5997fd"},whileTap:{scale:.9},onClick:function(){document.querySelector(".loader-container").classList.add("active"),m.Z.post("http://localhost:9002/api/update-subscription-ent",{tk:g,members:u},{headers:{"content-type":"application/x-www-form-urlencoded"}}).then(function(e){let s=e.data;if(s.success)return x().success("Success"),window.mixpanel.track("update_subscription",{additional_members:u}),window.location.href="/thankyou",void document.querySelector(".loader-container").classList.remove("active");s.data&&(s.data.message?x().error(s.data.message):x().error(s.data.msg))})},children:"Complete Purchase"})})}),(0,h.jsxs)("div",{className:"securecont border-t-2 border-gray-300 flex py-5",children:[(0,h.jsxs)("div",{className:"securetext mb-2 text-sm w-1/2",children:[(0,h.jsx)(i.kUi,{className:"inline text-lg mr-1 text-orange-500 text-xs"})," Secure Checkout"]}),(0,h.jsxs)("div",{className:"securelogo mb-2 text-sm w-1/2 flex flex-wrap justify-center items-center",children:[(0,h.jsx)("img",{src:r,alt:"Secure Logo",className:"cclogo inline"}),(0,h.jsxs)("div",{className:"flex items-center justify-center flex-wrap",children:[(0,h.jsx)("img",{src:n,alt:"Authorize",className:"text-center md:text-right py-2 w-20"}),(0,h.jsx)("button",{onClick:()=>{window.open("//www.securitymetrics.com/site_certificate?id=1982469&tk=d41c416c6208f1136426bc8038178d2e","Verification","location=no, toolbar=no, resizable=no, scrollbars=yes, directories=no, status=no,top=100,left=100, width=700, height=600")},title:"SecurityMetrics card safe certification",className:"h-20",children:(0,h.jsx)("img",{loading:"lazy",src:"https://www.securitymetrics.com/portal/app/ngsm/assets/img/GreyContent_Credit_Card_Safe_White_Sqr.png",alt:"SecurityMetrics card safe certification logo",className:"max-h-full",width:"80",height:"80"})})]})]})]})]})})})})}):""]})}}}]);
//# sourceMappingURL=7780.0f67936c.chunk.js.map