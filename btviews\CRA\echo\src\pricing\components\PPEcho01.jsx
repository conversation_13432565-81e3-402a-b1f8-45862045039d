import { useTranslation } from 'react-i18next'
import CheckIcon from '../../assets/images/check-icon.svg'
import { GetCookie } from '../../core/utils/cookies'
import { getPricePlan } from '../../core/utils/main'

const getLanguageModels = () => {
    const models = [
        {
            model: 'GPT-5',
            placeholder: 'openAI',
        },
        {
            model: 'DeepSeek',
        },
        {
            model: 'Grok',
        },
        {
            model: 'Perplexity',
            newBadge: true,
        },
        {
            model: 'o1',
            placeholder: 'openAI',
        },
        {
            model: 'Claude',
        },
    ]

    return updateList(models)
}

const getImageModels = () => {
    const models = [
        {
            model: 'DALL-E 3',
            placeholder: 'openAI',
        },
        {
            model: 'Grok Image',
        },
        {
            model: 'Flux',
        },
        {
            model: 'GPT Image 1',
            placeholder: 'openAI',
        },
        {
            model: 'Gemini Nano Banana',
        },
    ]

    return updateList(models)
}

const getVideoModels = () => {
    const models = [
        {
            model: 'KlingAI',
        },
    ]

    return updateList(models)
}

const getFeatures = (isTrial) => {
    let features = [
        {
            placeholder: 'dialogueLimit',
        },
    ]

    if (isTrial) {
        features.push({
            placeholder: 'trialPeriod',
        })
    }

    return updateList(features)
}

export const PPEcho01Section = ({ plans }) => {
    const hasTrial = plans.some(({ trial_days, trial_price }) => trial_days > 0 && trial_price > 0)

    const pricingDescription = {
        languageModels: getLanguageModels(),
        imageGeneration: getImageModels(),
        videoGeneration: getVideoModels(),
        features: getFeatures(hasTrial),
    }

    return (
        <>
            {Object.entries(pricingDescription).map(([section, sectionValues], index) => {
                const translatedSection = GetTranslation(section)

                return (
                    <>
                        <h3 className={`font-bold mb-4 ${index === 0 ? (hasTrial ? 'mt-[63px]' : '') : 'mt-2'} max-w-768:text-sm md:text-base h-[24px]`}>{translatedSection}</h3>
                        
                    </>
                )
            })}
        </>
    )
}

export const LanguageModelSection = () => {
    const models = getLanguageModels()

    return (
        <ul className="space-y-4 max-w-768:text-sm md:text-base mb-[17px]">
            <DisplayRowHeaders data={models} />
        </ul>
    )
}

export const LanguageModelValues = ({ plan }) => {
    const { plan_type, trial_days, trial_price } = plan
    const isTrial = trial_days > 0 && trial_price > 0

    const claude_limit = {
        Basic: '25k',
        Pro: '50k',
        Advanced: '50k',
    }

    const current_claude_limit = claude_limit[plan_type] ?? null

    const models = getLanguageModels().map((option) => {
        let checked = false

        switch (option.model) {
            case 'GPT-5':
            case 'DeepSeek':
            case 'Grok':
                checked = true
                break
            case 'Perplexity':
                checked = !isBasic(plan_type)
                break
            case 'o1':
                checked = isProMax(plan_type)
                break
            case 'Claude':
                if (!isProMax(plan_type)) {
                    if (current_claude_limit !== null) {
                        return {
                            ...option,
                            placeholder: {
                                format: 'tokensPerMonth',
                                values: {
                                    tokensPerMonth: current_claude_limit,
                                },
                            },
                        }
                    }
                } else {
                    return {
                        ...option,
                        unlimited: true,
                    }
                }
                break
            default:
                break
        }

        return {
            ...option,
            checked,
        }
    })

    return (
        <ul className={`text-sm text-gray-600 space-y-4 mb-[17px] ${isTrial ? 'mt-[63px]' : 'mt-11'}`}>
            <DisplayRowValues data={models} plan={plan} />
        </ul>
    )
}

export const ImageModelSection = () => {
    const models = getImageModels()

    return (
        <ul className="space-y-4 max-w-768:text-sm md:text-base mb-[17px]">
            <DisplayRowHeaders data={models} />
        </ul>
    )
}

export const ImageModelValues = ({ plan }) => {
    const { plan_type } = plan

    const dalle_limit = {
        Basic: 20,
        Pro: 50,
        Advanced: 80,
    }

    const grok_limit = {
        Basic: 20,
        Pro: 50,
        Advanced: 80,
        ProMax: 160,
    }

    const flux_limit = {
        Basic: 15,
        Pro: 30,
        Advanced: 80,
        ProMax: 160,
    }

    const gpt_image_limit = {
        Pro: 30,
        Advanced: 80,
        ProMax: 160,
    }

    const gemini_nano_banana_limit = {
        Basic: 15,
        Pro: 30,
        Advanced: 80,
        ProMax: 160,
    }

    const current_dalle_limit = dalle_limit[plan_type] ?? null
    const current_grok_limit = grok_limit[plan_type] ?? null
    const current_flux_limit = flux_limit[plan_type] ?? null
    const current_gpt_image_limit = gpt_image_limit[plan_type] ?? null
    const current_gemini_nano_banana_limit = gemini_nano_banana_limit[plan_type] ?? null

    const models = getImageModels().map((option) => {
        let checked = false

        switch (option.model) {
            case 'DALL-E 3':
                if (!isProMax(plan_type)) {
                    if (current_dalle_limit !== null) {
                        return {
                            ...option,
                            placeholder: {
                                format: 'imagesPerMonth',
                                values: {
                                    imagesPerMonth: current_dalle_limit,
                                },
                            },
                        }
                    }
                } else {
                    return {
                        ...option,
                        unlimited: true,
                    }
                }
                break
            case 'Grok Image':
                if (current_grok_limit !== null) {
                    return {
                        ...option,
                        placeholder: {
                            format: 'imagesPerMonth',
                            values: {
                                imagesPerMonth: current_grok_limit,
                            },
                        },
                    }
                }
                break
            case 'Flux':
                if (current_flux_limit !== null) {
                    return {
                        ...option,
                        placeholder: {
                            format: 'imagesPerMonth',
                            values: {
                                imagesPerMonth: current_flux_limit,
                            },
                        },
                    }
                }
                break
            case 'GPT Image 1':
                if (current_gpt_image_limit !== null) {
                    return {
                        ...option,
                        placeholder: {
                            format: 'imagesPerMonth',
                            values: {
                                imagesPerMonth: current_gpt_image_limit,
                            },
                        },
                    }
                }
                break
            case 'Gemini Nano Banana':
                if (current_gemini_nano_banana_limit !== null) {
                    return {
                        ...option,
                        placeholder: {
                            format: 'imagesPerMonth',
                            values: {
                                imagesPerMonth: current_gemini_nano_banana_limit,
                            },
                        },
                    }
                }
                break
            default:
                break
        }

        return {
            ...option,
            checked,
        }
    })

    return (
        <ul className="text-sm text-gray-600 space-y-4 mb-[17px] mt-[57px]">
            <DisplayRowValues data={models} plan={plan} />
        </ul>
    )
}

export const VideoModelSection = () => {
    const models = getVideoModels()

    return (
        <ul className="space-y-4 max-w-768:text-sm md:text-base mb-[17px]">
            <DisplayRowHeaders data={models} />
        </ul>
    )
}

export const VideoModelValues = ({ plan }) => {
    const { plan_type } = plan

    const klingai_limit = {
        Pro: 3,
        Advanced: 7,
        ProMax: 15,
    }

    const current_klingai_limit = klingai_limit[plan_type] ?? null

    const models = getVideoModels().map((option) => {
        let checked = false

        switch (option.model) {
            case 'KlingAI':
                if (current_klingai_limit !== null) {
                    return {
                        ...option,
                        placeholder: {
                            format: 'videosPerMonth',
                            values: {
                                videosPerMonth: current_klingai_limit,
                            },
                        },
                    }
                }
                break
            default:
                break
        }

        return {
            ...option,
            checked,
        }
    })

    return (
        <ul className="text-sm text-gray-600 space-y-4 mb-[17px] mt-[57px]">
            <DisplayRowValues data={models} plan={plan} />
        </ul>
    )
}

export const FeatureSection = ({ plans }) => {
    const hasTrial = plans.some(({ trial_days, trial_price }) => trial_days > 0 && trial_price > 0)
    const features = getFeatures(hasTrial)

    return (
        <ul className="space-y-4 max-w-768:text-sm md:text-base">
            <DisplayRowHeaders data={features} />
        </ul>
    )
}

export const FeatureValues = ({ plan }) => {
    const { plan_type, currency, price, trial_days, trial_price } = plan
    const isTrial = trial_days > 0 && trial_price > 0

    const dialogue_limit = {
        Basic: 500000,
        Pro: 1000000,
        Advanced: 2500000,
    }

    const current_dialogue_limit = dialogue_limit[plan_type] ?? null
    const priceText = getPricePlan(currency, price)

    const features = getFeatures(isTrial).map((option) => {
        let checked = false

        switch (option.placeholder) {
            case 'dialogueLimit':
                if (!isProMax(plan_type)) {
                    if (current_dialogue_limit !== null) {
                        return {
                            ...option,
                            placeholder: {
                                format: 'tokensPerMonth',
                                values: {
                                    tokensPerMonth: current_dialogue_limit.toLocaleString('en-US'),
                                },
                            },
                        }
                    }
                } else {
                    return {
                        ...option,
                        unlimited: true,
                    }
                }
                break
            case 'trialPeriod':
                if (isTrial) {
                    return {
                        ...option,
                        placeholder: {
                            format: 'trialText',
                            values: {
                                trialDays: trial_days,
                                priceText,
                            },
                        },
                    }
                }
                break
            default:
                break
        }

        return {
            ...option,
            checked,
        }
    })

    return (
        <ul className="text-sm text-gray-600 space-y-4 mb-[17px] mt-[57px]">
            <DisplayRowValues data={features} plan={plan} />
        </ul>
    )
}

const isBasic = (plan_type) => plan_type === 'Basic'

// const isPro = (plan_type) => plan_type === 'Pro'

// const isAdvanced = (plan_type) => plan_type === 'Advanced'

const isProMax = (plan_type) => plan_type === 'ProMax'

const isEnterprise = (plan_type) => plan_type === 'Enterprise'

const GetTranslation = (key) => {
    const { t } = useTranslation()

    return t(`echo.pricing.pp_echo.${key}`)
}

const replacePlaceholders = (template, data) => {
    return template.replace(/{(.*?)}/g, (_, key) => {
        return key in data ? data[key] : `{${key}}`
    })
}

const updateList = (data) => {
    return data.map((option) => {
        switch (typeof option) {
            case 'object':
                let model = null
                let text = null

                if (option.hasOwnProperty('model')) {
                    model = option.model
                }

                if (option.hasOwnProperty('placeholder')) {
                    const { placeholder } = option
                    let placeholders = []
                    let values = {}

                    if (typeof placeholder === 'string') {
                        placeholders.push(GetTranslation(placeholder))
                    } else if (placeholder.hasOwnProperty('format')) {
                        placeholders.push(GetTranslation(placeholder.format))
                    }

                    if (placeholder.hasOwnProperty('values')) {
                        values = placeholder.values
                    }

                    const with_model_placeholder = placeholders.find((placeholder) => placeholder.includes('{model}'))

                    if (with_model_placeholder && model !== null) {
                        values['model'] = model
                    }

                    if (placeholders.length > 0) {
                        text = replacePlaceholders(placeholders.join(' '), values)
                    }
                } else {
                    if (model !== null) {
                        text = model
                    }
                }

                if (text !== null) {
                    option['text'] = text
                }
                break
            case 'string':
                option = {
                    text: option,
                }
                break
            default:
                break
        }

        return option
    })
}

const DisplayRowHeaders = ({ data }) => {
    return data.map((option, index) => {
        if (typeof option === 'object') {
            if (option.hasOwnProperty('newBadge')) {
                const { newBadge } = option

                option['newBadge'] = newBadge === true ? GetTranslation('newBadge') : false
            }
        }

        return (
            <li key={index} className="h-[24px]">
                {option.text}
                {option.newBadge && <div className="inline px-2 ml-[2px] text-white bg-[#4285F4] rounded-xl text-[12px]">{option.newBadge}</div>}
            </li>
        )
    })
}

const DisplayRowValues = ({ data, plan }) => {
    const { plan_type } = plan
    const locales = GetCookie('locales') ?? 'en'

    if (isEnterprise(plan_type)) return null

    return data.map((option, index) => {
        let { checked, unlimited, text } = option
        let mobile_text = text

        text = null

        if (option.hasOwnProperty('unlimited')) {
            if (unlimited === true) {
                unlimited = GetTranslation('unlimited')
            }
        }

        if (option.hasOwnProperty('placeholder')) {
            const { placeholder } = option

            if (placeholder.hasOwnProperty('format')) {
                const formats = placeholder.format.split(',')
                let placeholders = []
                let values = {}

                if (placeholder.hasOwnProperty('values')) {
                    values = placeholder.values
                }

                for (const format of formats) {
                    let translation = GetTranslation(format)
                    let perMonthTranslation = GetTranslation('perMonth')

                    if (format !== 'trialText') {
                        if (locales !== 'tr') {
                            translation += perMonthTranslation
                        } else {
                            translation = perMonthTranslation + translation
                        }
                    }

                    if (format !== 'openAI') {
                        placeholders.push(translation)
                    }
                }

                if (placeholders.length > 0) {
                    text = replacePlaceholders(placeholders.join(' '), values)
                }
            }
        }

        return (
            <li key={index} className="max-w-768:flex max-w-768:items-center max-w-768:gap-2 max-w-768:justify-center text-[16px] min-h-[24px]">
                {(checked || unlimited || text) && mobile_text && <span className="inline max-w-768:hidden">{mobile_text}: </span>}
                {checked || unlimited ? <span className="inline text-[#3C57BB] font-bold">{checked ? <img src={CheckIcon} alt="Infinite" className="inline mx-auto w-[22px]" /> : unlimited}</span> : <span className="inline">{text || '-'}</span>}
            </li>
        )
    })
}
