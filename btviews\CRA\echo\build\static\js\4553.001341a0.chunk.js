"use strict";(self.webpackChunkv1=self.webpackChunkv1||[]).push([[4553,4127],{39616:(e,s,t)=>{t.r(s),t.d(s,{default:()=>p});var r=t(72791),i=t(96347),o=t(34492),n=t(74335),a=t(19009),l=(t(86375),t(49968)),d=t(39230),c=t(80184);const p=function(e){const s=e.data?e.data:null,t=e.setPricing?e.setPricing:()=>{};var p=(0,n.bG)("ppg")?(0,n.bG)("ppg"):"12",v=(0,n.bG)("pp_ctaclr")?(0,n.bG)("pp_ctaclr"):"1559ED";const[x]=(0,r.useState)((0,n.bG)("appName")?(0,n.bG)("appName"):"ChatBot Pro"),g=!(!["40","48","52","97","101","109","110"].includes(p)||!e.showToggle)&&e.showToggle;var h=!1;"48"===p&&(h=!0);const[m,u]=(0,r.useState)(h?"yearly":"monthly"),b=(0,n.bG)("tp_reviews")?(0,n.bG)("tp_reviews"):"",f=(0,n.bG)("enterprise")?(0,n.bG)("enterprise"):"off",{t:y}=(0,d.$G)(),w=function(e){let s="",t="",r="",i="";return s+="<br/>",s+="<div><strong>ChatGPT-powered tools</strong></div>",s+="<div>4,096 context window</div>",s+="<div>250,000 Token Cap - comprehensive and exhaustive responses</div>",s+="<div>Customizable Response Models - can provide scenario & persona-based responses</div>",s+="<div>Save Chat History - store up to hundreds of research results accessible any time</div>",s+="<br/>",s+="<div><strong>DreamPhoto - Stable Diffusion-powered</strong> AI image generator</div>",s+="<br/>",s+="<div>Export Conversations - Image, Text, CSV or JSON files</div>",s+="<div>Live Support</div>",t+="<br/>",t+="<div><strong>ChatGPT 4-powered tools</strong> to generate any text content you need</div>","promax"===e.plan_type.toLowerCase()?t+="<div>No Response Cap - no word count limit</div>":t+="<div>8,192 context window</div><div>500,000 Token Cap - comprehensive and exhaustive responses</div>",t+="<div>Customizable Response Models - can provide scenario & persona-based responses</div>",t+="<div>Save Chat History - store up to hundreds of research results accessible any time</div>","86"!==e.plan_id&&(t+="<div><strong>ChatPDF</strong> - ask questions based on your PDF document</div>"),t+="<div><strong>Teacher AI</strong> - streamlined learning app for any subject</div>",t+="<br/>",t+="<div><strong>Stable Diffusion-powered tools</strong> to create images</div>",t+="<div><strong>DreamPhoto</strong> - Stable Diffusion-powered AI image generator</div>",t+="<div><strong>Storybook AI</strong> - generate images for stories</div>",t+="<div><strong>Interior AI</strong> - reinvent your rooms with AI</div>",t+="<div><strong>AvatarMaker</strong> - create fantastical digital avatars</div>",t+="<div><strong>RestorePhoto</strong> - preserve and restore old photos</div>",t+="<br/>",t+="<div>Choose Light or Dark Mode - for all screen types</div>",t+="<div>Export Conversations - Image, Text, CSV or JSON files</div>",t+="<div>AI Art Prompt Gallery</div>",t+="<div>Live Support</div>",i+="<br/>",i+="<div><strong>ChatGPT 4-powered tools</strong> to generate any text content you need</div>",i+="<div>128,000 context window</div>",i+="<div>No Response Cap - no word count limit</div>",i+="<div>Customizable Response Models - can provide scenario & persona-based responses</div>",i+="<div>Save Chat History - store up to hundreds of research results accessible any time</div>",i+="<div><strong>ChatPDF</strong> - ask questions based on your PDF document</div>",i+="<div><strong>Teacher AI</strong> - streamlined learning app for any subject</div>",i+="<br/>",i+="<div><strong>Stable Diffusion-powered tools</strong> to create images</div>",i+="<div><strong>DreamPhoto</strong> - Stable Diffusion-powered AI image generator</div>",i+="<div><strong>Storybook AI</strong> - generate images for stories</div>",i+="<div><strong>Interior AI</strong> - reinvent your rooms with AI</div>",i+="<div><strong>AvatarMaker</strong> - create fantastical digital avatars</div>",i+="<div><strong>RestorePhoto</strong> - preserve and restore old photos</div>",i+="<br/>",i+="<div>Choose Light or Dark Mode - for all screen types</div>",i+="<div>Export Conversations - Image, Text, CSV or JSON files</div>",i+="<div>AI Art Prompt Gallery</div>",i+="<div>Live Support</div>",r+="<br/>",r+="<div><strong>Take your AI-powered applications to the next level with our exclusive Enterprise Plan.</strong></div>",r+="<div>Crafted for businesses seeking a tailored approach to harnessing the power of artificial intelligence, <br>this plan offers a suite of features designed to drive innovation and deliver exceptional user experiences.</div>","basic"===e.plan_type.toLowerCase()?"<br/><div><strong>ChatGPT-powered tools</strong></div><div>4,096 context window</div><div>250,000 Token Cap - comprehensive and exhaustive responses</div><div>Customizable Response Models - can provide scenario & persona-based responses</div><div>Save Chat History - store up to hundreds of research results accessible any time</div><br/><div><strong>DreamPhoto - Stable Diffusion-powered</strong> AI image generator</div><br/><div>Export Conversations - Image, Text, CSV or JSON files</div><div>Live Support</div>":"promax"===e.plan_type.toLowerCase()?i:"enterprise"===e.plan_type.toLowerCase()?"<br/><div><strong>Take your AI-powered applications to the next level with our exclusive Enterprise Plan.</strong></div><div>Crafted for businesses seeking a tailored approach to harnessing the power of artificial intelligence, <br>this plan offers a suite of features designed to drive innovation and deliver exceptional user experiences.</div>":"promax"===e.plan_type.toLowerCase()?i:t},j=function(){return(0,c.jsx)("div",{className:"price_col w-full lg:w-[330px] xl:w-[380px] text-center px-4 mb-8 relative md:min-h-[90vh]",children:(0,c.jsx)("div",{className:"bg-white rounded-lg shadow-lg overflow-hidden",children:(0,c.jsxs)("div",{className:"px-6 py-10 price-content  md:pt-[60px]",children:[(0,c.jsx)("h3",{className:"text-xl font-bold mb-4",children:y("echo.pricing.vprice_02.enterprise.title")}),(0,c.jsx)("p",{className:"text-4xl font-bold text-gray-800 "+("yearly"===m&&"48"===p?"":"mb-4"),children:y("echo.pricing.vprice_02.enterprise.price")}),"yearly"===m&&"48"===p?(0,c.jsx)("div",{className:"text-xs mb-4",children:"(billed yearly)"}):"",(0,c.jsx)("div",{className:"py-4",children:(0,c.jsx)(i.E.button,{className:"text-white font-bold py-3 px-3 rounded-lg btn-ent",style:{backgroundColor:(0,a.aI)(v)},whileHover:{scale:1.1,backgroundColor:(0,a.im)(v)},whileTap:{scale:.9},onHoverStart:()=>{console.log("hover:",s),console.log("hover2:",s[0].currency)},onClick:()=>t(62),children:y("echo.pricing.vprice_02.enterprise.cta")})}),(0,c.jsx)("div",{className:"mb-6 plan-description ",children:(0,c.jsx)("ul",{className:"text-sm text-gray-600",children:(0,c.jsxs)("li",{className:"mb-2 text-left",children:[(0,c.jsx)("br",{}),(0,c.jsx)("div",{className:"font-bold",children:y("echo.pricing.vprice_02.enterprise.desc1")}),(0,c.jsx)("div",{children:y("echo.pricing.vprice_02.enterprise.desc2")})]})})})]})})})};return(0,c.jsx)("div",{className:"v-pricing-01 pricing bg-gray-100",children:(0,c.jsx)("div",{className:"pricing_columns container mx-auto py-20 md:py-10",children:(0,c.jsxs)("div",{className:"flex flex-col items-center pt-50px md:pt py-10 lg:py-14",children:[(0,c.jsx)("h1",{className:"text-4xl lg:text-4xl font-bold text-center mb-4 min-h-[40px] text-[#336CEB]",children:"Pricing Plan"}),(0,c.jsxs)("div",{className:"text-normal",children:["345 Unlock the full potential of ",x]}),g?(0,c.jsxs)("div",{className:"p-4",children:[(0,c.jsx)("div",{className:"text-1xl lg:text-1xl font-bold text-center mb-4"}),(0,c.jsx)("div",{className:"flex items-center justify-center w-full mb-8",children:(0,c.jsxs)("label",{for:"toggleB",className:"flex items-center cursor-pointer",children:[(0,c.jsx)("div",{className:("monthly"===m?"text-blue-700 font-bold":"text-gray-700")+" mr-3 uppercase",children:"Monthly"}),(0,c.jsxs)("div",{className:"relative",children:[(0,c.jsx)("input",{type:"checkbox",id:"toggleB",className:"sr-only toggle",onChange:function(){u("monthly"===m?"yearly":"monthly")},defaultChecked:h}),(0,c.jsx)("div",{className:"block bg-gray-400 w-12 h-6 rounded-full"}),(0,c.jsx)("div",{className:"dot absolute left-1 top-1 bg-white w-4 h-4 rounded-full transition"})]}),(0,c.jsx)("div",{className:("yearly"===m?"text-blue-700 font-bold":"text-gray-700")+" ml-3 uppercase",children:"Yearly"})]})})]}):"",g?(0,c.jsxs)("div",{className:"pricing-toggle flex flex-col lg:flex-row justify-center",children:[null==s?void 0:s.map((e,s)=>function(e){return!g||e.payment_interval.toLowerCase()===m}(e)?(0,c.jsxs)("div",{className:"price_col w-full lg:w-[330px] xl:w-[380px] text-center px-4 mb-8 "+(1===s?"relative":""),children:[(0,c.jsx)("div",{className:"bg-white rounded-lg shadow-lg overflow-hidden",children:(0,c.jsxs)("div",{className:"px-6 py-10 price-content",children:[(0,c.jsx)("h3",{className:"text-xl font-bold mb-4",children:e.label}),(0,c.jsx)(o.mD,{plan:e}),(0,c.jsx)("div",{className:"py-4",children:(0,c.jsx)(i.E.button,{className:"text-white font-bold py-3 px-6 rounded-lg",style:{backgroundColor:(0,a.aI)(v)},whileHover:{scale:1.1,backgroundColor:(0,a.im)(v)},whileTap:{scale:.9},onClick:()=>t(e.plan_id),children:"Subscribe"})}),(0,c.jsx)("div",{className:"mb-6 plan-description ",children:(0,c.jsx)("ul",{className:"text-sm text-gray-600",children:e.plan_description?(0,c.jsx)("li",{className:"mb-2",dangerouslySetInnerHTML:{__html:w(e)}}):null})})]})}),"yearly"===m&&g?(0,c.jsx)("div",{className:"absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-blue-100 text-blue-500 font-bold py-1 px-4 text-xs span-highlight",children:(0,c.jsxs)("div",{children:["Up to ",(0,c.jsx)("span",{className:"font-bold underline-offset-1",children:"20% OFF"})," on an annual subscription"]})}):null]},s):""),"on"===f&&"46"!==p&&"yearly"===m&&"USD"===s[0].currency?(0,c.jsx)(c.Fragment,{children:j()}):null]}):(0,c.jsxs)("div",{className:"flex flex-col lg:flex-row justify-center",children:[null==s?void 0:s.map((e,s)=>(0,c.jsxs)("div",{className:"price_col w-full lg:w-[330px] xl:w-[380px] text-center px-4 mb-8 "+(1===s?"relative":""),children:[(0,c.jsx)("div",{className:"bg-white rounded-lg shadow-lg overflow-hidden",children:(0,c.jsxs)("div",{className:"px-6 py-10 price-content",children:[(0,c.jsx)("h3",{className:"text-xl font-bold mb-4",children:e.label}),"enterprise"===e.label.toLowerCase()?(0,c.jsx)("p",{className:"text-3xl md:text-4x1 font-bold text-gray-800 mb-4",children:"Custom Plan"}):(0,c.jsx)(o.mD,{plan:e}),(0,c.jsx)("div",{className:"py-4",children:(0,c.jsx)(i.E.button,{className:"text-white font-bold py-3 px-6 rounded-lg",style:{backgroundColor:(0,a.aI)(v)},whileHover:{scale:1.1,backgroundColor:(0,a.im)(v)},whileTap:{scale:.9},onClick:()=>t(e.plan_id),children:"Subscribe"})}),(0,c.jsx)("div",{className:"mb-6 plan-description ",children:(0,c.jsx)("ul",{className:"text-sm text-gray-600",children:e.plan_description?(0,c.jsx)("li",{className:"mb-2",dangerouslySetInnerHTML:{__html:w(e)}}):null})})]})}),1===s?(0,c.jsx)("div",{className:"absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-blue-100 text-blue-500 font-bold py-1 px-4 text-xs span-highlight",children:(0,c.jsx)("span",{children:"Most Popular"})}):null]},s)),"on"===f&&"46"!==p&&"USD"===s[0].currency?(0,c.jsx)(c.Fragment,{children:j()}):null]}),(0,c.jsx)("p",{className:"text-xs max-w-md text-center leading-relaxed mb-10 lg:mb-12",children:"*The pricing is exclusive of taxes and additional local tax may be collected."}),"on"===b?(0,c.jsx)(c.Fragment,{children:(0,c.jsx)(l.default,{})}):null]})})})}}}]);
//# sourceMappingURL=4553.001341a0.chunk.js.map