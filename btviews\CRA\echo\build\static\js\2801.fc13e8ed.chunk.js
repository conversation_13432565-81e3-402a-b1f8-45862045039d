"use strict";(self.webpackChunkv1=self.webpackChunkv1||[]).push([[2801],{82801:(e,t,r)=>{r.d(t,{u:()=>L});var n=r(72791),l=r(15612),i=r(4510),a=r(79904),o=r(11262),u=r(84705),s=r(22806),c=r(81511),f=r(94159);var d=r(56958);function v(e){for(var t=arguments.length,r=new Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];e&&r.length>0&&e.classList.add(...r)}function h(e){for(var t=arguments.length,r=new Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];e&&r.length>0&&e.classList.remove(...r)}function m(e,t,r,n){let l=r?"enter":"leave",i=(0,d.k)(),o=void 0!==n?function(e){let t={called:!1};return function(){if(!t.called)return t.called=!0,e(...arguments)}}(n):()=>{};"enter"===l&&(e.removeAttribute("hidden"),e.style.display="");let u=(0,a.E)(l,{enter:()=>t.enter,leave:()=>t.leave}),s=(0,a.E)(l,{enter:()=>t.enterTo,leave:()=>t.leaveTo}),c=(0,a.E)(l,{enter:()=>t.enterFrom,leave:()=>t.leaveFrom});return h(e,...t.enter,...t.enterTo,...t.enterFrom,...t.leave,...t.leaveFrom,...t.leaveTo,...t.entered),v(e,...u,...c),i.nextFrame(()=>{h(e,...c),v(e,...s),function(e,t){let r=(0,d.k)();if(!e)return r.dispose;let{transitionDuration:n,transitionDelay:l}=getComputedStyle(e),[i,a]=[n,l].map(e=>{let[t=0]=e.split(",").filter(Boolean).map(e=>e.includes("ms")?parseFloat(e):1e3*parseFloat(e)).sort((e,t)=>t-e);return t}),o=i+a;if(0!==o){r.group(r=>{r.setTimeout(()=>{t(),r.dispose()},o),r.addEventListener(e,"transitionrun",e=>{e.target===e.currentTarget&&r.dispose()})});let n=r.addEventListener(e,"transitionend",e=>{e.target===e.currentTarget&&(t(),n())})}else t();r.add(()=>t()),r.dispose}(e,()=>(h(e,...u),v(e,...t.entered),o()))}),i.dispose}var p=r(5623);var g=r(23654),b=r(48210);function E(){return(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"").split(" ").filter(e=>e.trim().length>1)}let y=(0,n.createContext)(null);y.displayName="TransitionContext";var w,T=((w=T||{}).Visible="visible",w.Hidden="hidden",w);let O=(0,n.createContext)(null);function C(e){return"children"in e?C(e.children):e.current.filter(e=>{let{el:t}=e;return null!==t.current}).filter(e=>{let{state:t}=e;return"visible"===t}).length>0}function F(e,t){let r=(0,s.E)(e),i=(0,n.useRef)([]),u=(0,o.t)(),c=(0,p.G)(),f=(0,g.z)(function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:l.l4.Hidden,n=i.current.findIndex(t=>{let{el:r}=t;return r===e});-1!==n&&((0,a.E)(t,{[l.l4.Unmount](){i.current.splice(n,1)},[l.l4.Hidden](){i.current[n].state="hidden"}}),c.microTask(()=>{var e;!C(i)&&u.current&&(null==(e=r.current)||e.call(r))}))}),d=(0,g.z)(e=>{let t=i.current.find(t=>{let{el:r}=t;return r===e});return t?"visible"!==t.state&&(t.state="visible"):i.current.push({el:e,state:"visible"}),()=>f(e,l.l4.Unmount)}),v=(0,n.useRef)([]),h=(0,n.useRef)(Promise.resolve()),m=(0,n.useRef)({enter:[],leave:[],idle:[]}),b=(0,g.z)((e,r,n)=>{v.current.splice(0),t&&(t.chains.current[r]=t.chains.current[r].filter(t=>{let[r]=t;return r!==e})),null==t||t.chains.current[r].push([e,new Promise(e=>{v.current.push(e)})]),null==t||t.chains.current[r].push([e,new Promise(e=>{Promise.all(m.current[r].map(e=>{let[t,r]=e;return r})).then(()=>e())})]),"enter"===r?h.current=h.current.then(()=>null==t?void 0:t.wait.current).then(()=>n(r)):n(r)}),E=(0,g.z)((e,t,r)=>{Promise.all(m.current[t].splice(0).map(e=>{let[t,r]=e;return r})).then(()=>{var e;null==(e=v.current.shift())||e()}).then(()=>r(t))});return(0,n.useMemo)(()=>({children:i,register:d,unregister:f,onStart:b,onStop:E,wait:h,chains:m}),[d,f,i,b,E,m,h])}function S(){}O.displayName="NestingContext";let A=["beforeEnter","afterEnter","beforeLeave","afterLeave"];function j(e){var t;let r={};for(let n of A)r[n]=null!=(t=e[n])?t:S;return r}let k=l.AN.RenderStrategy;let N=(0,l.yV)(function(e,t){let{show:r,appear:a=!1,unmount:o,...s}=e,d=(0,n.useRef)(null),v=(0,f.T)(d,t);(0,c.H)();let h=(0,i.oJ)();if(void 0===r&&null!==h&&(r=(h&i.ZM.Open)===i.ZM.Open),![!0,!1].includes(r))throw new Error("A <Transition /> is used but it is missing a `show={true | false}` prop.");let[m,p]=(0,n.useState)(r?"visible":"hidden"),b=F(()=>{p("hidden")}),[E,w]=(0,n.useState)(!0),T=(0,n.useRef)([r]);(0,u.e)(()=>{!1!==E&&T.current[T.current.length-1]!==r&&(T.current.push(r),w(!1))},[T,r]);let S=(0,n.useMemo)(()=>({show:r,appear:a,initial:E}),[r,a,E]);(0,n.useEffect)(()=>{if(r)p("visible");else if(C(b)){let e=d.current;if(!e)return;let t=e.getBoundingClientRect();0===t.x&&0===t.y&&0===t.width&&0===t.height&&p("hidden")}else p("hidden")},[r,b]);let A={unmount:o},j=(0,g.z)(()=>{var t;E&&w(!1),null==(t=e.beforeEnter)||t.call(e)}),N=(0,g.z)(()=>{var t;E&&w(!1),null==(t=e.beforeLeave)||t.call(e)});return n.createElement(O.Provider,{value:b},n.createElement(y.Provider,{value:S},(0,l.sY)({ourProps:{...A,as:n.Fragment,children:n.createElement(R,{ref:v,...A,...s,beforeEnter:j,beforeLeave:N})},theirProps:{},defaultTag:n.Fragment,features:k,visible:"visible"===m,name:"Transition"})))}),R=(0,l.yV)(function(e,t){let{beforeEnter:r,afterEnter:v,beforeLeave:h,afterLeave:w,enter:T,enterFrom:S,enterTo:A,entered:N,leave:R,leaveFrom:P,leaveTo:L,...x}=e,H=(0,n.useRef)(null),M=(0,f.T)(H,t),z=x.unmount?l.l4.Unmount:l.l4.Hidden,{show:Z,appear:V,initial:q}=function(){let e=(0,n.useContext)(y);if(null===e)throw new Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}(),[Y,$]=(0,n.useState)(Z?"visible":"hidden"),I=function(){let e=(0,n.useContext)(O);if(null===e)throw new Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}(),{register:U,unregister:B}=I,D=(0,n.useRef)(null);(0,n.useEffect)(()=>U(H),[U,H]),(0,n.useEffect)(()=>{if(z===l.l4.Hidden&&H.current)return Z&&"visible"!==Y?void $("visible"):(0,a.E)(Y,{hidden:()=>B(H),visible:()=>U(H)})},[Y,H,U,B,Z,z]);let G=(0,s.E)({enter:E(T),enterFrom:E(S),enterTo:E(A),entered:E(N),leave:E(R),leaveFrom:E(P),leaveTo:E(L)}),J=function(e){let t=(0,n.useRef)(j(e));return(0,n.useEffect)(()=>{t.current=j(e)},[e]),t}({beforeEnter:r,afterEnter:v,beforeLeave:h,afterLeave:w}),W=(0,c.H)();(0,n.useEffect)(()=>{if(W&&"visible"===Y&&null===H.current)throw new Error("Did you forget to passthrough the `ref` to the actual DOM node?")},[H,Y,W]);let K=q&&!V,Q=!W||K||D.current===Z?"idle":Z?"enter":"leave",X=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,[t,r]=(0,n.useState)(e),l=(0,o.t)(),i=(0,n.useCallback)(e=>{l.current&&r(t=>t|e)},[t,l]),a=(0,n.useCallback)(e=>Boolean(t&e),[t]),u=(0,n.useCallback)(e=>{l.current&&r(t=>t&~e)},[r,l]),s=(0,n.useCallback)(e=>{l.current&&r(t=>t^e)},[r]);return{flags:t,addFlag:i,hasFlag:a,removeFlag:u,toggleFlag:s}}(0),_=(0,g.z)(e=>(0,a.E)(e,{enter:()=>{X.addFlag(i.ZM.Opening),J.current.beforeEnter()},leave:()=>{X.addFlag(i.ZM.Closing),J.current.beforeLeave()},idle:()=>{}})),ee=(0,g.z)(e=>(0,a.E)(e,{enter:()=>{X.removeFlag(i.ZM.Opening),J.current.afterEnter()},leave:()=>{X.removeFlag(i.ZM.Closing),J.current.afterLeave()},idle:()=>{}})),te=F(()=>{$("hidden"),B(H)},I);(function(e){let{container:t,direction:r,classes:n,onStart:l,onStop:i}=e,a=(0,o.t)(),c=(0,p.G)(),f=(0,s.E)(r);(0,u.e)(()=>{let e=(0,d.k)();c.add(e.dispose);let r=t.current;if(r&&"idle"!==f.current&&a.current)return e.dispose(),l.current(f.current),e.add(m(r,n.current,"enter"===f.current,()=>{e.dispose(),i.current(f.current)})),e.dispose},[r])})({container:H,classes:G,direction:Q,onStart:(0,s.E)(e=>{te.onStart(H,e,_)}),onStop:(0,s.E)(e=>{te.onStop(H,e,ee),"leave"===e&&!C(te)&&($("hidden"),B(H))})}),(0,n.useEffect)(()=>{K&&(z===l.l4.Hidden?D.current=null:D.current=Z)},[Z,K,Y]);let re=x,ne={ref:M};return V&&Z&&q&&(re={...re,className:(0,b.A)(x.className,...G.current.enter,...G.current.enterFrom)}),n.createElement(O.Provider,{value:te},n.createElement(i.up,{value:(0,a.E)(Y,{visible:i.ZM.Open,hidden:i.ZM.Closed})|X.flags},(0,l.sY)({ourProps:ne,theirProps:re,defaultTag:"div",features:k,visible:"visible"===Y,name:"Transition.Child"})))}),P=(0,l.yV)(function(e,t){let r=null!==(0,n.useContext)(y),l=null!==(0,i.oJ)();return n.createElement(n.Fragment,null,!r&&l?n.createElement(N,{ref:t,...e}):n.createElement(R,{ref:t,...e}))}),L=Object.assign(N,{Child:P,Root:N})},5623:(e,t,r)=>{r.d(t,{G:()=>i});var n=r(72791),l=r(56958);function i(){let[e]=(0,n.useState)(l.k);return(0,n.useEffect)(()=>()=>e.dispose(),[e]),e}},23654:(e,t,r)=>{r.d(t,{z:()=>i});var n=r(72791),l=r(22806);let i=function(e){let t=(0,l.E)(e);return n.useCallback(function(){return t.current(...arguments)},[t])}},11262:(e,t,r)=>{r.d(t,{t:()=>i});var n=r(72791),l=r(84705);function i(){let e=(0,n.useRef)(!1);return(0,l.e)(()=>(e.current=!0,()=>{e.current=!1}),[]),e}},84705:(e,t,r)=>{r.d(t,{e:()=>i});var n=r(72791),l=r(43784);let i=(e,t)=>{l.O.isServer?(0,n.useEffect)(e,t):(0,n.useLayoutEffect)(e,t)}},22806:(e,t,r)=>{r.d(t,{E:()=>i});var n=r(72791),l=r(84705);function i(e){let t=(0,n.useRef)(e);return(0,l.e)(()=>{t.current=e},[e]),t}},81511:(e,t,r)=>{r.d(t,{H:()=>i});var n=r(72791),l=r(43784);function i(){let[e,t]=(0,n.useState)(l.O.isHandoffComplete);return e&&!1===l.O.isHandoffComplete&&t(!1),(0,n.useEffect)(()=>{!0!==e&&t(!0)},[e]),(0,n.useEffect)(()=>l.O.handoff(),[]),e}},94159:(e,t,r)=>{r.d(t,{T:()=>o,h:()=>a});var n=r(72791),l=r(23654);let i=Symbol();function a(e){let t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];return Object.assign(e,{[i]:t})}function o(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];let a=(0,n.useRef)(t);(0,n.useEffect)(()=>{a.current=t},[t]);let o=(0,l.z)(e=>{for(let t of a.current)null!=t&&("function"==typeof t?t(e):t.current=e)});return t.every(e=>null==e||(null==e?void 0:e[i]))?void 0:o}},4510:(e,t,r)=>{r.d(t,{ZM:()=>a,oJ:()=>o,up:()=>u});var n=r(72791);let l=(0,n.createContext)(null);l.displayName="OpenClosedContext";var i,a=((i=a||{})[i.Open=1]="Open",i[i.Closed=2]="Closed",i[i.Closing=4]="Closing",i[i.Opening=8]="Opening",i);function o(){return(0,n.useContext)(l)}function u(e){let{value:t,children:r}=e;return n.createElement(l.Provider,{value:t},r)}},48210:(e,t,r)=>{function n(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter(Boolean).join(" ")}r.d(t,{A:()=>n})},56958:(e,t,r)=>{r.d(t,{k:()=>l});var n=r(28106);function l(){let e=[],t={addEventListener:(e,r,n,l)=>(e.addEventListener(r,n,l),t.add(()=>e.removeEventListener(r,n,l))),requestAnimationFrame(){let e=requestAnimationFrame(...arguments);return t.add(()=>cancelAnimationFrame(e))},nextFrame(){for(var e=arguments.length,r=new Array(e),n=0;n<e;n++)r[n]=arguments[n];return t.requestAnimationFrame(()=>t.requestAnimationFrame(...r))},setTimeout(){let e=setTimeout(...arguments);return t.add(()=>clearTimeout(e))},microTask(){for(var e=arguments.length,r=new Array(e),l=0;l<e;l++)r[l]=arguments[l];let i={current:!0};return(0,n.Y)(()=>{i.current&&r[0]()}),t.add(()=>{i.current=!1})},style(e,t,r){let n=e.style.getPropertyValue(t);return Object.assign(e.style,{[t]:r}),this.add(()=>{Object.assign(e.style,{[t]:n})})},group(e){let t=l();return e(t),this.add(()=>t.dispose())},add:t=>(e.push(t),()=>{let r=e.indexOf(t);if(r>=0)for(let t of e.splice(r,1))t()}),dispose(){for(let t of e.splice(0))t()}};return t}},43784:(e,t,r)=>{r.d(t,{O:()=>i});var n=Object.defineProperty,l=(e,t,r)=>(((e,t,r)=>{t in e?n(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r})(e,"symbol"!=typeof t?t+"":t,r),r);let i=new class{constructor(){l(this,"current",this.detect()),l(this,"handoffState","pending"),l(this,"currentId",0)}set(e){this.current!==e&&(this.handoffState="pending",this.currentId=0,this.current=e)}reset(){this.set(this.detect())}nextId(){return++this.currentId}get isServer(){return"server"===this.current}get isClient(){return"client"===this.current}detect(){return"undefined"==typeof window||"undefined"==typeof document?"server":"client"}handoff(){"pending"===this.handoffState&&(this.handoffState="complete")}get isHandoffComplete(){return"complete"===this.handoffState}}},79904:(e,t,r)=>{function n(e,t){if(e in t){let n=t[e];for(var r=arguments.length,l=new Array(r>2?r-2:0),i=2;i<r;i++)l[i-2]=arguments[i];return"function"==typeof n?n(...l):n}let a=new Error(`Tried to handle "${e}" but there is no handler defined. Only defined handlers are: ${Object.keys(t).map(e=>`"${e}"`).join(", ")}.`);throw Error.captureStackTrace&&Error.captureStackTrace(a,n),a}r.d(t,{E:()=>n})},28106:(e,t,r)=>{function n(e){"function"==typeof queueMicrotask?queueMicrotask(e):Promise.resolve().then(e).catch(e=>setTimeout(()=>{throw e}))}r.d(t,{Y:()=>n})},15612:(e,t,r)=>{r.d(t,{AN:()=>u,l4:()=>s,sY:()=>c,yV:()=>v});var n,l,i=r(72791),a=r(48210),o=r(79904),u=((l=u||{})[l.None=0]="None",l[l.RenderStrategy=1]="RenderStrategy",l[l.Static=2]="Static",l),s=((n=s||{})[n.Unmount=0]="Unmount",n[n.Hidden=1]="Hidden",n);function c(e){let{ourProps:t,theirProps:r,slot:n,defaultTag:l,features:i,visible:a=!0,name:u}=e,s=d(r,t);if(a)return f(s,n,l,u);let c=null!=i?i:0;if(2&c){let{static:e=!1,...t}=s;if(e)return f(t,n,l,u)}if(1&c){let{unmount:e=!0,...t}=s;return(0,o.E)(e?0:1,{0:()=>null,1:()=>f({...t,hidden:!0,style:{display:"none"}},n,l,u)})}return f(s,n,l,u)}function f(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=arguments.length>2?arguments[2]:void 0,n=arguments.length>3?arguments[3]:void 0,{as:l=r,children:o,refName:u="ref",...s}=m(e,["unmount","static"]),c=void 0!==e.ref?{[u]:e.ref}:{},f="function"==typeof o?o(t):o;"className"in s&&s.className&&"function"==typeof s.className&&(s.className=s.className(t));let v={};if(t){let e=!1,r=[];for(let[n,l]of Object.entries(t))"boolean"==typeof l&&(e=!0),!0===l&&r.push(n);e&&(v["data-headlessui-state"]=r.join(" "))}if(l===i.Fragment&&Object.keys(h(s)).length>0){if(!(0,i.isValidElement)(f)||Array.isArray(f)&&f.length>1)throw new Error(['Passing props on "Fragment"!',"",`The current component <${n} /> is rendering a "Fragment".`,"However we need to passthrough the following props:",Object.keys(s).map(e=>`  - ${e}`).join("\n"),"","You can apply a few solutions:",['Add an `as="..."` prop, to ensure that we render an actual element instead of a "Fragment".',"Render a single element as the child so that we can forward the props onto that element."].map(e=>`  - ${e}`).join("\n")].join("\n"));let e=f.props,t="function"==typeof(null==e?void 0:e.className)?function(){return(0,a.A)(null==e?void 0:e.className(...arguments),s.className)}:(0,a.A)(null==e?void 0:e.className,s.className),r=t?{className:t}:{};return(0,i.cloneElement)(f,Object.assign({},d(f.props,h(m(s,["ref"]))),v,c,function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return{ref:t.every(e=>null==e)?void 0:e=>{for(let r of t)null!=r&&("function"==typeof r?r(e):r.current=e)}}}(f.ref,c.ref),r))}return(0,i.createElement)(l,Object.assign({},m(s,["ref"]),l!==i.Fragment&&c,l!==i.Fragment&&v),f)}function d(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];if(0===t.length)return{};if(1===t.length)return t[0];let n={},l={};for(let e of t)for(let t in e)t.startsWith("on")&&"function"==typeof e[t]?(null!=l[t]||(l[t]=[]),l[t].push(e[t])):n[t]=e[t];if(n.disabled||n["aria-disabled"])return Object.assign(n,Object.fromEntries(Object.keys(l).map(e=>[e,void 0])));for(let e in l)Object.assign(n,{[e](t){let r=l[e];for(var n=arguments.length,i=new Array(n>1?n-1:0),a=1;a<n;a++)i[a-1]=arguments[a];for(let e of r){if((t instanceof Event||(null==t?void 0:t.nativeEvent)instanceof Event)&&t.defaultPrevented)return;e(t,...i)}}});return n}function v(e){var t;return Object.assign((0,i.forwardRef)(e),{displayName:null!=(t=e.displayName)?t:e.name})}function h(e){let t=Object.assign({},e);for(let e in t)void 0===t[e]&&delete t[e];return t}function m(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],r=Object.assign({},e);for(let e of t)e in r&&delete r[e];return r}}}]);
//# sourceMappingURL=2801.fc13e8ed.chunk.js.map