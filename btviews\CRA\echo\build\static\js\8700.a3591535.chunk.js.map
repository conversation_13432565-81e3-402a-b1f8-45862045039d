{"version": 3, "file": "static/js/8700.a3591535.chunk.js", "mappings": "2JA0BA,QAxBA,WAGE,MAAMA,EAAWC,yBAWjB,OAVAC,EAAAA,EAAAA,WAAU,KACR,MAAMC,EAASC,SAASC,cAAc,UACtCF,EAAOG,IAAMN,EAAW,iDACxBG,EAAOI,OAAQ,EACfJ,EAAOK,OAAS,OAIhBJ,SAASK,KAAKC,YAAYP,IACzB,CAACH,KAEFW,EAAAA,EAAAA,KAAAC,EAAAA,SAAA,CAAAC,UACEF,EAAAA,EAAAA,KAAA,UAAQG,UAAW,iHAMzB,C,2GCnBA,MA4CA,EA5C2BC,KACzB,MAAQC,IAASC,EAAAA,EAAAA,UAASC,OAAOC,WACjC,IAAIC,EAAK,KAGT,OAFGJ,EAAKK,QAAOD,EAAKJ,IAGlBM,EAAAA,EAAAA,MAAAV,EAAAA,SAAA,CAAAC,SAAA,EACES,EAAAA,EAAAA,MAACC,EAAAA,EAAM,CAAAV,SAAA,EACLF,EAAAA,EAAAA,KAAA,SAAAE,SAAO,8BACPF,EAAAA,EAAAA,KAAA,QAAMa,KAAK,cAAcC,QAAQ,2QAEnCd,EAAAA,EAAAA,KAACe,EAAAA,QAAM,CAACC,KAAMP,KAEdT,EAAAA,EAAAA,KAAA,OAAKG,UAAU,6CAA4CD,UACzDS,EAAAA,EAAAA,MAAA,OAAKR,UAAU,yDAAwDD,SAAA,EACrEF,EAAAA,EAAAA,KAAA,MAAIG,UAAU,2DAA0DD,SAAC,8DAGzEF,EAAAA,EAAAA,KAAA,OAAKG,UAAU,oDAAmDD,UAChES,EAAAA,EAAAA,MAAA,OAAKR,UAAU,YAAWD,SAAA,EACxBS,EAAAA,EAAAA,MAAA,KAAGR,UAAU,OAAMD,SAAA,CACfO,GAAKT,EAAAA,EAAAA,KAAAC,EAAAA,SAAA,KAAQU,EAAAA,EAAAA,MAAAV,EAAAA,SAAA,CAAAC,SAAA,EACfF,EAAAA,EAAAA,KAAA,KAAGiB,KAAK,iBAAiBd,UAAU,0BAAyBD,SAAC,YAExD,IAAI,SACF,QAEPF,EAAAA,EAAAA,KAAA,KAAGiB,KAAK,SAASd,UAAU,0BAAyBD,SAAC,UAEhD,IAAI,sCAGXF,EAAAA,EAAAA,KAAA,KAAAE,SAAG,kRAOXF,EAAAA,EAAAA,KAACkB,EAAAA,QAAM,CAACF,KAAMP,O", "sources": ["footer/index.jsx", "redirect-account-required/index.jsx"], "sourcesContent": ["import React, { useEffect } from 'react';\r\n\r\nfunction Powered() {\r\n  // const [isHidden, setIsHidden] = useState(false);\r\n\r\n  const base_url = process.env.REACT_APP_BASE_URL;\r\n  useEffect(() => {\r\n    const script = document.createElement('script');\r\n    script.src = base_url + 'snippets/com.global.vuzo/js/com.global.vuzo.js';\r\n    script.async = true;\r\n    script.onload = () => {\r\n      // window.displayGooglePlay();\r\n      // window.displayQR();\r\n    };\r\n    document.body.appendChild(script);\r\n  }, [base_url]);\r\n  return (\r\n    <>\r\n      <footer className={`relative hidden md:block md:absolute bottom-[10px] right-[5px] w-full mt-3 py-12 md:py-2 z-[8888] md:hidden`}>\r\n      {/* <footer className=\"relative hidden md:block md:absolute bottom-[10px] right-[5px] w-full mt-3 py-12 md:py-2 z-[8888]\"> */}\r\n\r\n      </footer>\r\n    </>\r\n  );\r\n}\r\n\r\nexport default Powered;\r\n", "import React, { useState } from 'react';\r\nimport Header from '../header';\r\nimport Footer from '../footer';\r\nimport { Helmet } from 'react-helmet';\r\n\r\nconst PremiumContentPage = () => {\r\n  const [ user ] = useState(window.user_data);\r\n  var tk = null;\r\n  if(user.email) tk = user;\r\n\r\n  return (\r\n    <>\r\n      <Helmet>\r\n        <title>AI Pro | Access Required</title>\r\n        <meta name=\"description\" content=\"Our premium membership options also give you access to all of our AI applications, such as AI Chatbots, text generators, text-to-image generators, and more, so be sure to check them out if you're interested in getting more out of your AI learning experience.\" />\r\n      </Helmet>\r\n      <Header auth={tk}/>\r\n\r\n      <div className=\"container mx-auto py-10 flex pt-10 md:pt-2\">\r\n        <div className=\"bg-white flex flex-col pb-16 p-8 md:m-[150px] lg:pb-16\">\r\n          <h1 className=\"text-3xl font-bold mb-6 text-left font-bold mb-8 lg:mb-8\">\r\n            Access to this application requires a paid subscription.\r\n          </h1>\r\n          <div className=\"container mx-auto flex flex-col md:flex-wrap mb-6\">\r\n            <div className=\"md:w-full\">\r\n              <p className=\"mb-4\">\r\n                { tk ? <></> : <>\r\n                <a href=\"/register-auth\" className=\"text-blue-600 font-bold\">\r\n                  Sign Up\r\n                </a>{' '}\r\n                now or{' '}\r\n                </> }\r\n                <a href=\"/login\" className=\"text-blue-600 font-bold\">\r\n                  Login\r\n                </a>{' '}\r\n                here to access the application.\r\n              </p>\r\n              <p>\r\n                Our premium membership options also give you access to all of our AI applications, such as AI Chatbots, text generators, text-to-image generators, and more, so be sure to check them out if you're interested in getting more out of your AI learning experience.\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <Footer auth={tk} />\r\n    </>\r\n  );\r\n};\r\n\r\nexport default PremiumContentPage;\r\n"], "names": ["base_url", "process", "useEffect", "script", "document", "createElement", "src", "async", "onload", "body", "append<PERSON><PERSON><PERSON>", "_jsx", "_Fragment", "children", "className", "PremiumContentPage", "user", "useState", "window", "user_data", "tk", "email", "_jsxs", "<PERSON><PERSON><PERSON>", "name", "content", "Header", "auth", "href", "Footer"], "sourceRoot": ""}