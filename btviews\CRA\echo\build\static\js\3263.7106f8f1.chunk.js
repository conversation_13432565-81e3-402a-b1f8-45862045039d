"use strict";(self.webpackChunkv1=self.webpackChunkv1||[]).push([[3263],{61101:(e,t,r)=>{r.r(t),r.d(t,{default:()=>s});var n=r(72791),o=r(56355),l=r(80184);const s=e=>{let{isHeaderVisible:t}=e;const[r,s]=(0,n.useState)(!1);(0,n.useEffect)(()=>{const e=()=>{s(window.pageYOffset>300&&!t)},r=()=>{s(!t)};return window.addEventListener("scroll",e),window.addEventListener("resize",r),()=>{window.removeEventListener("scroll",e),window.removeEventListener("resize",r)}},[t]);return(0,l.jsx)("div",{className:"back-to-top "+(r?"visible":""),children:(0,l.jsx)(o.nbd,{onClick:()=>{window.scrollTo({top:0,behavior:"smooth"})}})})}},89983:(e,t,r)=>{r.d(t,{w_:()=>c});var n=r(72791),o={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},l=n.createContext&&n.createContext(o),s=function(){return s=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},s.apply(this,arguments)},i=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]])}return r};function a(e){return e&&e.map(function(e,t){return n.createElement(e.tag,s({key:t},e.attr),a(e.child))})}function c(e){return function(t){return n.createElement(u,s({attr:s({},e.attr)},t),a(e.child))}}function u(e){var t=function(t){var r,o=e.attr,l=e.size,a=e.title,c=i(e,["attr","size","title"]),u=l||t.size||"1em";return t.className&&(r=t.className),e.className&&(r=(r?r+" ":"")+e.className),n.createElement("svg",s({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,o,c,{className:r,style:s(s({color:e.color||t.color},t.style),e.style),height:u,width:u,xmlns:"http://www.w3.org/2000/svg"}),a&&n.createElement("title",null,a),e.children)};return void 0!==l?n.createElement(l.Consumer,null,function(e){return t(e)}):t(o)}}}]);
//# sourceMappingURL=3263.7106f8f1.chunk.js.map