"use strict";(self.webpackChunkv1=self.webpackChunkv1||[]).push([[4870],{54870:(e,t,s)=>{s.r(t),s.d(t,{default:()=>C});var a=s(72791),i=s(28891),l=s(93851),r=s(91843),n=s(65239),o=s(59553),c=s(28946),d=s(55432),m=s(45907),h=s(54033),p=s(30197),x=s(54270),u=s(96347),g=s(74335),f=s(65855),w=s(84791),b=s(64429),j=s(80184);const y=(0,a.lazy)(()=>Promise.all([s.e(6355),s.e(3263)]).then(s.bind(s,61101))),v=(0,a.lazy)(()=>s.e(7719).then(s.bind(s,77719))),N=(0,a.lazy)(()=>Promise.all([s.e(3526),s.e(7749),s.e(1233),s.e(728),s.e(9044)]).then(s.bind(s,10728))),k=(0,a.lazy)(()=>s.e(7250).then(s.bind(s,27250)));const C=function(){const e=(0,i.gx)(),[t,C]=(0,a.useState)(!1),[P,T]=(0,a.useState)("hide"),[G,S]=(0,a.useState)("off"),[A,I]=(0,a.useState)(""),[L,z]=(0,a.useState)("off"),E=(0,a.useRef)(null),[W,_]=(0,a.useState)(!0),q=!(!(0,g.bG)("qW1eMlya")||"on"!==(0,g.bG)("qW1eMlya")),[B,M]=(0,a.useState)(!1);(0,a.useEffect)(()=>{const e=document.cookie.split(";");for(let t=0;t<e.length;t++){const s=e[t].trim();s.startsWith("navmenu=")?T(s.substring(8)):s.startsWith("splash=")?I(s.substring(7)):s.startsWith("ailplogo=")?z(s.substring(9)):s.startsWith("lp_vid=")&&S(s.substring(7))}const t=()=>{C(window.innerWidth<=768)},s=()=>{const e=E.current.getBoundingClientRect().bottom;_(window.pageYOffset<e)};return t(),window.addEventListener("resize",t),window.addEventListener("scroll",s),()=>{window.removeEventListener("resize",t),window.removeEventListener("scroll",s)}},[e]),(0,a.useEffect)(()=>{const e=setTimeout(()=>{s.e(7514).then(s.bind(s,37514)),s.e(1549).then(s.bind(s,61549)),M(!0)},3e3);return()=>clearTimeout(e)},[]);const U=()=>{e?e&&"active"===e.status?window.location.href="/my-account":window.location.href="/pricing":window.location.href="/register-auth"};if(void 0!==e)return B?(0,j.jsx)(j.Fragment,{children:(0,j.jsxs)(a.Suspense,{fallback:(0,j.jsx)(b.g,{}),children:[(0,j.jsxs)(x.q,{children:[(0,j.jsx)("meta",{name:"robots",content:"noindex, nofollow"}),(0,j.jsx)("title",{children:"AI Chat Using GPT-4o Power"}),(0,j.jsx)("meta",{name:"description",content:"Discover ChatGPT and engage in natural, human-like conversations and get answers to your questions. Experience the future of AI-driven communication."})]}),"show"===P?(0,j.jsx)(N,{hideNavLink:q,auth:e}):(0,j.jsx)(k,{hideNavLink:q,auth:e}),(0,j.jsx)("div",{ref:E}),(0,j.jsx)("div",{className:"startchatgpt bg-gray-100",children:(0,j.jsx)("div",{className:"intro container mx-auto pt-0 pb-10 md:pt-20 md:px-20",children:(0,j.jsxs)("div",{className:"flex flex-col md:flex-row pt-16 md:pt-10 lg:pt-16 pb-10 lg:p-[70px] lg:pb-10",children:[(0,j.jsxs)("div",{className:"md:w-1/2 p-8 lg:ml-8 text-center md:text-left",children:[(0,j.jsx)("h4",{className:"text-md font-bold sm:pt-6 lg:pt-8 lg:mt-2",children:"ChatGPT AI is Finally Here"}),(0,j.jsx)("h1",{className:"text-4xl font-black mb-4 sm:pt-4",children:"AI Chat Using GPT-4o Power"}),(0,j.jsxs)("p",{className:"text-[16px] mb-4",children:["Automate your writing, answer questions, write code and much more. Get more creative and free up your time using ChatGPT’s powerful language capabilities, powered by GPT-4.",(0,j.jsx)("br",{}),(0,j.jsx)("br",{}),"The most powerful AI Language ChatBot available today."]}),(0,j.jsx)("button",{className:"ctabtn gradient-hover-effect text-white font-bold py-3 px-6 rounded-lg mx-auto md:mx-0",onClick:"on"===A?()=>{window.location.href="/splash"}:U,children:"Start Now"}),"on"===L?(0,j.jsxs)("picture",{className:"flex justify-center md:justify-start",children:[(0,j.jsx)("source",{type:"image/webp",srcSet:w,width:"140",height:"97"}),(0,j.jsx)("img",{src:f,alt:"openai",width:"140",height:"97"})]}):""]}),(0,j.jsx)("div",{className:"md:w-1/2 banner_img",children:(0,j.jsxs)("video",{muted:!0,loop:!0,playsInline:!0,autoPlay:!0,children:[(0,j.jsx)("source",{src:l,type:"video/webm",className:"w-full shadow"}),(0,j.jsx)("source",{src:r,type:"video/mp4",className:"w-full shadow"})]})})]})})}),(0,j.jsxs)("div",{className:"startchatgpt bg-gray-100",children:[(0,j.jsxs)("div",{className:"features container mx-auto md:px-28",children:[(0,j.jsx)("div",{className:"lg:flex lg:justify-center text-center",children:(0,j.jsxs)("div",{className:"w-full max-w-screen-lg mt-16",children:[(0,j.jsx)("h2",{className:"text-3xl lg:text-4xl font-bold mb-8",children:"For Business, School, and Life"}),(0,j.jsx)("p",{className:"mx-auto max-w-2xl mb-16 text-center",children:"By utilizing ChatGPT‘s AI–powered conversational interface, businesses can improve their workflow, customer service and automate tasks."})]})}),(0,j.jsx)("div",{className:"lg:flex lg:justify-center",children:(0,j.jsxs)("div",{className:"lg:w-full",children:[(0,j.jsxs)("div",{className:"flex flex-col lg:flex-row mb-8",children:[(0,j.jsxs)("div",{className:"lg:w-1/3 mb-4 lg:mb-0 py-5 px-10 text-center",children:[(0,j.jsxs)("h3",{className:"text-md font-bold mb-4",children:[(0,j.jsx)("i",{className:"fa fa-window-restore text-blue-600 pb-4","aria-hidden":"true"})," ","Draft Contracts & Proposals"]}),(0,j.jsx)("p",{className:"text-md mx-auto",children:"ChatGPT can help you create contracts and proposals more easily. This saves you time and effort because you don't have to start from scratch. ChatGPT can also check your drafts for mistakes and make sure they are formatted correctly. Make the process of creating contracts and proposals quicker and easier with ChatGPT."})]}),(0,j.jsxs)("div",{className:"lg:w-1/3 mb-4 lg:mb-0 py-5 px-10 text-center",children:[(0,j.jsxs)("h3",{className:"text-md font-bold mb-4",children:[(0,j.jsx)("i",{className:"fa fa-envelope text-blue-600 pb-4","aria-hidden":"true"})," ","Write Emails & Chat Replies"]}),(0,j.jsx)("p",{className:"text-md mx-auto",children:"ChatGPT can help you save time and effort by writing emails and chat replies for your work or business. ChatGPT can also assist with editing and proofreading the generated responses to ensure they are accurate and professional. Let ChatGPT handle your email and chat communication, freeing you up to focus on more important tasks."})]}),(0,j.jsxs)("div",{className:"lg:w-1/3 mb-4 lg:mb-0 py-5 px-10 text-center",children:[(0,j.jsxs)("h3",{className:"text-md font-bold mb-4",children:[(0,j.jsx)("i",{className:"fa fa-video-camera text-blue-600 pb-4","aria-hidden":"true"})," ","Create Video Scripts"]}),(0,j.jsx)("p",{className:"text-md mx-auto",children:"ChatGPT can assist you in creating video scripts by automating the process of generating draft scripts based on certain prompts or keywords. ChatGPT can also help with editing and proofreading the generated drafts to ensure they are accurate and well-written. Utilize ChatGPT's capabilities to streamline the process of creating video scripts."})]})]}),(0,j.jsxs)("div",{className:"flex flex-col lg:flex-row mb-8",children:[(0,j.jsxs)("div",{className:"lg:w-1/3 mb-4 lg:mb-0 py-5 px-10 text-center",children:[(0,j.jsxs)("h3",{className:"text-md font-bold mb-4",children:[(0,j.jsx)("i",{className:"fa fa-pencil-square text-blue-600 pb-4","aria-hidden":"true"})," ","Write Stories, Poems & Songs"]}),(0,j.jsx)("p",{className:"text-md mx-auto",children:"ChatGPT is a powerful language model that can assist with writing tasks, including songwriting, poetry, stories, and books. Simply provide a prompt and let the model generate creative and original ideas. ChatGPT can write in various styles and tones. Try it out to boost creativity and productivity."})]}),(0,j.jsxs)("div",{className:"lg:w-1/3 mb-4 lg:mb-0 py-5 px-10 text-center",children:[(0,j.jsxs)("h3",{className:"text-md font-bold mb-4",children:[(0,j.jsx)("i",{className:"fa fa-code text-blue-600 pb-4","aria-hidden":"true"})," ","Use Prompts & Commands"]}),(0,j.jsx)("p",{className:"text-md mx-auto",children:"Explore our comprehensive resources and enhance your understanding of AI prompts and commands. From beginner to advanced, our guides will help you improve your skills and strengthen your communication with your AI. Get started now and take your AI capabilities to the next level."})]}),(0,j.jsxs)("div",{className:"lg:w-1/3 mb-4 lg:mb-0 py-5 px-10 text-center",children:[(0,j.jsx)("i",{className:"fa fa-credit-card text-blue-600 pb-4","aria-hidden":"true"}),(0,j.jsx)("h3",{className:"text-md font-bold mb-4",children:"Make Money with AI"}),(0,j.jsx)("p",{className:"text-md mx-auto",children:"Looking to leverage the power of AI to generate income? We will show you how to utilize AI to create new streams of revenue. You'll learn the skills and strategies you need to succeed in the lucrative world of AI. Don't miss out on this opportunity to transform your financial future with AI!"})]})]})]})}),(0,j.jsx)("div",{className:"flex flex-col mx-auto py-6",children:(0,j.jsx)(u.E.button,{onClick:"on"===A?()=>{window.location.href="/splash"}:U,className:"bg-[#084AB4] text-white font-bold py-3 px-6 mb-12 rounded-lg mx-auto ctabtn",whileHover:{scale:1.1},whileTap:{scale:.9},children:"Start Now"})})]}),(0,j.jsx)("div",{className:"gallery mx-auto",children:(0,j.jsx)("div",{className:"flex flex-col items-center lg:py-8 bg-blue-400",children:t?(0,j.jsx)("div",{className:"flex flex-col lg:flex-row justify-center",children:(0,j.jsxs)(p.lr,{className:"py-4",showArrows:!0,emulateTouch:!0,autoPlay:!0,centerSlidePercentage:!0,children:[(0,j.jsxs)("picture",{children:[(0,j.jsx)("source",{type:"image/webp",srcSet:o,width:"658",height:"658"}),(0,j.jsx)("img",{width:"658",height:"658",src:n,alt:"img23",className:"lp_img block md:inline w-full lg:w-1/3 p-2"})]}),(0,j.jsxs)("picture",{children:[(0,j.jsx)("source",{type:"image/webp",srcSet:d,width:"658",height:"658"}),(0,j.jsx)("img",{width:"658",height:"658",src:c,alt:"img24",className:"lp_img block md:inline w-full lg:w-1/3 p-2"})]}),(0,j.jsxs)("picture",{children:[(0,j.jsx)("source",{type:"image/webp",srcSet:h,width:"658",height:"658"}),(0,j.jsx)("img",{width:"658",height:"658",src:m,alt:"img25",className:"lp_img block md:inline w-full lg:w-1/3 p-2"})]})]})}):(0,j.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3",children:[(0,j.jsxs)("picture",{children:[(0,j.jsx)("source",{type:"image/webp",srcSet:o,width:"658",height:"658"}),(0,j.jsx)("img",{width:"658",height:"658",src:n,alt:"img23",className:"lp_img block md:inline w-full lg:w-1/3 p-2"})]}),(0,j.jsxs)("picture",{children:[(0,j.jsx)("source",{type:"image/webp",srcSet:d,width:"658",height:"658"}),(0,j.jsx)("img",{width:"658",height:"658",src:c,alt:"img24",className:"lp_img block md:inline w-full lg:w-1/3 p-2"})]}),(0,j.jsxs)("picture",{children:[(0,j.jsx)("source",{type:"image/webp",srcSet:h,width:"658",height:"658"}),(0,j.jsx)("img",{width:"658",height:"658",src:m,alt:"img25",className:"lp_img block md:inline w-full lg:w-1/3 p-2"})]})]})})}),"on"===G?(0,j.jsx)("div",{className:"vidgallery mx-auto flex justify-center px-4 py-8 sm:px-12 sm:py-24",children:(0,j.jsx)("iframe",{loading:"lazy",width:"900",height:"500",src:"https://www.youtube.com/embed/BbZCYDHjsiU",title:"ChatGPT",frameborder:"0",allow:"accelerometer; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share; fullscreen",allowfullscreen:!0})}):(0,j.jsx)("div",{className:"vidgallery mx-auto"})]}),(0,j.jsx)(y,{isHeaderVisible:W}),(0,j.jsx)(v,{auth:e,hideNavLink:q})]})}):(0,j.jsx)(b.g,{})}},91843:(e,t,s)=>{e.exports=s.p+"static/media/chatgpt_sm.f16b7aa441ec4bc7d6ef.mp4"},93851:(e,t,s)=>{e.exports=s.p+"static/media/chatgpt_sm.07f9594943562ceff05c.webm"},59553:(e,t,s)=>{e.exports=s.p+"static/media/img23.6882c62e98a252de8db4.webp"},55432:(e,t,s)=>{e.exports=s.p+"static/media/img24.cbe3d02eee0a3dc0b646.webp"},54033:(e,t,s)=>{e.exports=s.p+"static/media/img25.4edc9ac930ec076264e9.webp"},84791:(e,t,s)=>{e.exports=s.p+"static/media/openai-03.a8fb6975e6a9db3ad04b.webp"},65239:(e,t,s)=>{e.exports=s.p+"static/media/img23.3581efea5f667aed3864.png"},28946:(e,t,s)=>{e.exports=s.p+"static/media/img24.ea6d64c598fd110ffbc7.png"},45907:(e,t,s)=>{e.exports=s.p+"static/media/img25.047657ba0103a446052a.png"},65855:(e,t,s)=>{e.exports=s.p+"static/media/openai-03.f8a67f3073c134593792.png"}}]);
//# sourceMappingURL=4870.b98bcf75.chunk.js.map