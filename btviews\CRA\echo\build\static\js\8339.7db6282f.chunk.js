"use strict";(self.webpackChunkv1=self.webpackChunkv1||[]).push([[8339],{34492:(e,t,r)=>{r.d(t,{aS:()=>i,mD:()=>g,mW:()=>m,o0:()=>h,oB:()=>f,p6:()=>l,rZ:()=>n,tN:()=>u,x6:()=>c,yt:()=>p});var o=r(74335),a=r(80184);function s(e){return e?"usd"===e.toLowerCase()?"$":"eur"===e.toLowerCase()?"€":"gbp"===e.toLowerCase()?"£":"brl"===e.toLowerCase()||"sar"===e.toLowerCase()?"R$":"":""}function n(e){return e?"usd"===e.toLowerCase()||"eur"===e.toLowerCase()?"US":"gbp"===e.toLowerCase()?"GB":"brl"===e.toLowerCase()?"US":"sar"===e.toLowerCase()?"AE":"chf"===e.toLowerCase()?"CH":"sek"===e.toLowerCase()?"SE":"US":""}function l(e){if(!e)return"";const t=new Date(e.replace(/ /g,"T"));return["January","February","March","April","May","June","July","August","September","October","November","December"][t.getMonth()]+" "+t.getDate()+" "+t.getFullYear()}function i(e){return e.trial_price?e.trial_price:e.price?e.price:"0"}function c(e){return e.toString().replace(/\B(?=(\d{3})+(?!\d))/g,",")}function d(e){const t=parseFloat(e);return c(t%1==0?t.toFixed(0):t.toFixed(2))}function p(e,t){return e&&t?isNaN(t)?"":parseFloat(t)>=0?"sar"===e.toLowerCase()?d(t).toLocaleString("en-US")+"ر.س.":"aed"===e.toLowerCase()?d(t).toLocaleString("en-US")+"AED":"chf"===e.toLowerCase()?d(t).toLocaleString("en-US")+"CHF":"sek"===e.toLowerCase()?d(t).toLocaleString("en-US")+"SEK":"pln"===e.toLowerCase()?d(t).toLocaleString("en-US")+"zł":"ron"===e.toLowerCase()?d(t).toLocaleString("en-US")+"LEI":"czk"===e.toLowerCase()?"Kč"+d(t).toLocaleString("en-US"):"huf"===e.toLowerCase()?d(t).toLocaleString("en-US")+"Ft":"dkk"===e.toLowerCase()?d(t).toLocaleString("en-US")+"kr.":"bgn"===e.toLowerCase()?d(t).toLocaleString("en-US")+"лв":"try"===e.toLowerCase()?d(t).toLocaleString("en-US")+"₺":s(e)+d(t).toLocaleString("en-US"):"-"+s(e)+(-1*d(t)).toLocaleString("en-US"):""}function u(e,t){e=new Date(e);var r=((t=new Date(t)).getTime()-e.getTime())/1e3;return r/=60,Math.abs(Math.round(r))}function h(e){if(!e)return"";const t=new Date(e.replace(/ /g,"T"));return["January","February","March","April","May","June","July","August","September","October","November","December"][t.getMonth()]+" "+t.getDate()+" "+t.getFullYear()+" "+t.getHours()+":"+t.getMinutes()}function m(e){let{plan:t}=e,r="",s="";return"Yearly"===t.payment_interval&&(r=p(t.currency,parseFloat(t.price/365).toFixed(2)),s=(0,a.jsxs)("div",{class:"text-xs mb-4",children:["billed ",p(t.currency,t.price),"/year"]})),"Monthly"===t.payment_interval&&(r=p(t.currency,parseFloat(t.price/30).toFixed(2)),s=(0,a.jsxs)("div",{class:"text-xs mb-4",children:["billed ",p(t.currency,t.price),"/Month"]})),t.trial_price&&(r=p(t.currency,parseFloat(t.trial_price/t.trial_days).toFixed(2)),s=(0,a.jsxs)("div",{class:"text-xs mb-4",children:["billed ",p(t.currency,t.trial_price)]})),(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("p",{className:"text-4xl font-bold text-gray-800 "+(""===(0,o.bG)("p_toggle")?"mb-4":""),children:[r," ",(0,a.jsx)("span",{className:"text-sm",children:" per Day"})]}),s]})}function g(e){let{plan:t}=e;return"on"===(0,o.bG)("daily")?m({plan:t}):t.trial_price?(0,a.jsx)("p",{className:"text-4xl font-bold text-gray-800 mb-4",children:p(t.currency,t.trial_price)}):(0,a.jsxs)("p",{className:"text-4xl font-bold text-gray-800 mb-4",children:[p(t.currency,t.price),(0,a.jsxs)("span",{className:"text-sm",children:["/","Monthly"===t.payment_interval?"month":"year"]})]})}function f(){const e=window.location.href;return e.indexOf("staging")>-1?"staging.":e.indexOf("dev")>-1?"dev.":""}},70572:(e,t,r)=>{r.d(t,{X:()=>a,t:()=>s});var o=r(79378);function a(e){let t="";return e?e.length<6&&(t=o.Z.t("echo.register.validation.passwordCharText")):t=o.Z.t("echo.register.validation.passwordReqText"),t}function s(e,t){let r="";return e!==t&&(r=o.Z.t("echo.register.validation.passwordsDoNotMatch")),r}},23232:(e,t,r)=>{r.d(t,{Z:()=>n});var o=r(72791),a=r(23853),s=r(80184);const n=function(e){let{className:t,pwInputRef:r,setShowPW:n,showPW:l}=e;const i=(0,o.useCallback)(()=>{const e=r.current;n(!l),e.type=l?"password":"text",e.focus()},[n,r,l]);return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("button",{className:`absolute cursor-pointer w-4 h-4 [&_svg]:w-auto [&_svg]:h-auto [&_svg]:max-w-full [&_svg]:max-h-full z-10 fill-[#597291] fill-gray-150 hover:ring-8 hover:ring-slate-300 rounded-full hover:bg-slate-300 transition-all duration-200 ease-linear ${t}`,onClick:i,type:"button",title:(l?"Hide":"Show")+" Password",children:(0,s.jsx)(a.rDJ,{style:{color:"gray"}})}),(0,s.jsx)("span",{className:`absolute z-10 w-[18px] h-[18px] pointer-events-none -rotate-45 mr-[-1px] mt-[-1px] flex items-center justify-center overflow-hidden ${t} after:block after:w-0.5 after:bg-[#597291] after:transition-all after:duration-200 after:ease-in after:h-[18px] ${l?"":"after:-translate-y-full"}`})]})}},69660:(e,t,r)=>{r.r(t),r.d(t,{default:()=>C});var o=r(72791),a=r(28891),s=r(74335),n=r(54270),l=(r(92831),r(95828)),i=r.n(l),c=r(96347),d=r(31243),p=r(23232),u=r(70572),h=r(39230),m=r(80184);const g=e=>{let{handleRedirect:t,setWillRedirect:r,emailError:a,passwordError:n,setEmailError:l,setPasswordError:g,members:f,reg_google:x,reg_apple:b,setEmail:w,setPassword:y,setEmailOptIn:v,email:k,password:j,emailOptIn:C,showPW:E,setShowPW:N,pwInputRef:S,spanErrorRefs:L,smooth_login:A,prefix:F}=e;const{t:I}=(0,h.$G)(),U=(0,o.useRef)(null),R=(0,s.bG)("lp"),O=(e,t)=>{const r=e.target,o=r.value,a="focus"===e.type,s="change"===e.type||"keyup"===e.type;if(o||a||s?r.classList.add("autofilled"):r.classList.remove("autofilled"),"on"!==A||"on"===A&&s)switch(t){case"email":l(""),w(o);break;case"password":g(""),y(o);break;case"emailOptIn":v(r.checked);break;default:13===e.keyCode&&P()}};function P(){var e;null!==(e=U.current)&&void 0!==e&&e.value&&((0,s.I1)("ishoneypot","yes",{path:"/"}),(0,s.I1)("ishoneypot","yes",{domain:".ai-pro.org",path:"/"})),r(!1);var o=W(),a=T();o&&a&&(document.querySelector(".loader-container").classList.add("active"),d.Z.post("http://localhost:9002/api/t/register",{email:k,password:j,pass_con:j,emailOptIn:C},{headers:{"content-type":"application/x-www-form-urlencoded"}}).then(function(e){let r=e.data;if(r.success){try{"on"===(0,s.bG)("emailopt")&&window.mixpanel.track("register",{emailOptIn:C}),window.qp("track","CompleteRegistration"),window.ttq.identify({email:`${k}`}),window.ttq.track("Registration Page",{contents:[{content_id:`${r.data.login_token}`,content_name:`${k}`}],description:"Registration (echo)"})}catch(e){console.error("An error occurred:",e.message)}i().success("Success"),(0,s.I1)("access",r.data.login_token),(0,s.I1)("user_email",r.data.email,{domain:".ai-pro.org",path:"/"});const e=(0,s.bG)("flow"),o=(0,s.bG)("chatpdf");if("chatpdf"===e)"01"===o?"aihub"===R?window.top.location.href="https://"+F+"chat.ai-pro.org":t("/pay"):"aihub"===R?window.top.location.href="https://"+F+"chat.ai-pro.org":t("https://"+F+"chatpdf.ai-pro.org");else if("on"===f)"aihub"===R?window.top.location.href="/my-account":t("/my-account");else{const e=(0,s.bG)("pricing"),r=(0,s.bG)("iSplanEnt");"62"===e||"1"===r?"aihub"===R?window.top.location.href="https://"+F+"chat.ai-pro.org":t("/pay/mcWiDilmgQ"):"aihub"===R?(window.top.location.href="https://"+F+"chat.ai-pro.org",console.log("aihub")):t("/pay")}return}document.querySelector(".loader-container").classList.remove("active"),r.data&&i().error(r.data.msg)}).catch(function(e){e.response&&429===e.response.status&&(document.querySelector(".loader-container").classList.remove("active"),i().error("Sorry, too many requests. Please try again in a bit!"))}))}const W=()=>{let e=!1;return k?/\S+@\S+\.\S+/.test(k)?(l(""),e=!0):l(I("echo.register.form.emailInvalidText")):l(I("echo.register.form.emailReqText")),e},T=()=>{let e=(0,u.X)(j);return!e||(g(e),!1)},z=""!==k.trim()&&""!==j.trim();return"aihub"===R?(0,m.jsxs)(m.Fragment,{children:[(0,m.jsxs)("label",{className:"relative block w-full",children:[(0,m.jsx)("input",{type:"text",ref:U,name:"user_phone",className:"hidden absolute left-[-9999px]",autocomplete:"off"}),(0,m.jsx)("input",{className:"placeholder:text-gray-400 min-h-[50px] block w-full border rounded-md py-2 pl-3 pr-3 shadow-sm focus:outline-none focus:border-blue-500 focus:ring-blue-500 focus:ring-1 sm:text-sm "+(a?"border-[#E28C8C] bg-[#FCF3F3]":"border-gray-200"),placeholder:"Enter email address","aria-label":"Enter email address",type:"email",name:"email",onChange:e=>O(e,"email"),onKeyUp:e=>{l(""),w(e.target.value),13===e.keyCode&&P()}}),a&&(0,m.jsx)("span",{className:"text-red-400 text-xs text-left w-full mb-2",children:a})]}),(0,m.jsxs)("label",{className:"block w-full",children:[(0,m.jsxs)("div",{className:"relative block w-full",children:[(0,m.jsx)("input",{className:"placeholder:text-gray-400 min-h-[50px] block w-full border rounded-md py-2 pl-3 shadow-sm focus:outline-none focus:border-blue-500 focus:ring-blue-500 focus:ring-1 sm:text-sm pr-10 "+(n?"border-[#E28C8C] bg-[#FCF3F3]":"border-gray-200"),placeholder:"Enter password","aria-label":"Enter Password",type:"password",name:"password",onChange:e=>O(e,"password"),onKeyUp:e=>{g(""),y(e.target.value),13===e.keyCode&&P()},ref:S}),(0,m.jsx)(p.Z,{className:"right-3 top-3 "+("aihub"===R?"mt-[5px]":""),pwInputRef:S,setShowPW:N,showPW:E})]}),n&&(0,m.jsx)("span",{className:"text-red-400 text-xs text-left w-full mb-2",children:n})]}),"on"===(0,s.bG)("emailopt")&&(0,m.jsxs)("div",{class:"flex gap-2 items-center",children:[(0,m.jsx)("input",{id:"checked-checkbox",type:"checkbox",name:"emailOptIn",checked:C,onChange:e=>O(e,"emailOptIn"),className:"w-4 h-4 text-blue-500 bg-gray-100 border-gray-200 rounded"}),(0,m.jsx)("label",{for:"checked-checkbox",class:"text-xs text-gray-500",children:I("echo.register.form.emailOptText")})]}),(0,m.jsx)(c.E.button,{onClick:P,disabled:!z,className:"mb-1 font-bold py-3 px-6 my-3 rounded-lg w-full "+(z?"text-white bg-[#3073D5] hover:bg-[#2563eb]":"text-gray-400 bg-[#F1F1F1] cursor-not-allowed"),whileHover:z?{scale:1.02}:{},whileTap:z?{scale:.98}:{},onKeyDown:e=>{"Enter"===e.key&&z&&P()},"aria-label":"register",children:"Continue"}),(0,m.jsxs)("div",{class:"flex items-center justify-center space-x-4 mb-4",children:[(0,m.jsx)("div",{class:"w-24 border-t border-gray-200"}),(0,m.jsx)("span",{class:"text-gray-400 text-sm",children:"or"}),(0,m.jsx)("div",{class:"w-24 border-t border-gray-200"})]})]}):(0,m.jsxs)(m.Fragment,{children:["on"===A?(0,m.jsxs)(m.Fragment,{children:[(0,m.jsxs)("div",{className:"bg-white w-full relative mb-3 sm:text-sm",children:[(0,m.jsx)("input",{type:"text",ref:U,name:"user_phone",className:"hidden absolute left-[-9999px]",autocomplete:"off"}),(0,m.jsx)("input",{className:"peer block border-2 border-slate-300 rounded-md shadow-sm focus:outline-none focus:border-sky-500 focus:ring-sky-500 focus:ring-1  w-full pt-6 pb-2 px-6 placeholder:text-transparent placeholder:[user-select:_none]",placeholder:`${I("echo.register.form.emailText")} *`,type:"email",name:"email",onBlur:e=>O(e,"email"),onChange:e=>O(e,"email"),onFocus:e=>O(e,"email"),onKeyUp:O}),(0,m.jsx)("label",{className:"transition-all text-[#597291] duration-100 ease-in absolute top-1/2 -translate-y-1/2 left-6 pointer-events-none [.autofilled~&]:top-[17px] [.autofilled~&]:text-xs [.autofilled~&]:left-[25px]",for:"email",children:I("echo.register.form.emailText")})]}),(0,m.jsx)("span",{className:"block text-red-500 text-xs text-center w-full h-0 transition-[height] duration-200 ease-in overflow-hidden",ref:e=>L.current[0]=e,children:a}),(0,m.jsxs)("div",{className:"bg-white w-full relative mb-3 sm:text-sm",children:[(0,m.jsx)("input",{className:"peer block border-2 border-slate-300 rounded-md shadow-sm focus:outline-none focus:border-sky-500 focus:ring-sky-500 focus:ring-1  w-full pt-6 pb-2 px-6 pl-6 pr-16 placeholder:text-transparent placeholder:[user-select:_none]",placeholder:`${I("echo.register.form.passwordText")} *`,type:"password",name:"password",onBlur:e=>O(e,"password"),onChange:e=>O(e,"password"),onFocus:e=>O(e,"password"),onKeyUp:O,ref:S}),(0,m.jsx)("label",{className:"transition-all text-[#597291] duration-100 ease-in absolute top-1/2 -translate-y-1/2 left-6 pointer-events-none [.autofilled~&]:top-[17px] [.autofilled~&]:text-xs [.autofilled~&]:left-[25px]",for:"password",children:I("echo.register.form.passwordText")}),(0,m.jsx)(p.Z,{className:"right-6 top-1/2 -translate-y-1/2",pwInputRef:S,setShowPW:N,showPW:E})]}),(0,m.jsx)("span",{className:"block text-red-500 text-xs text-center w-full h-0 transition-[height] duration-200 ease-in overflow-hidden",ref:e=>L.current[1]=e,children:n})]}):(0,m.jsxs)(m.Fragment,{children:[(0,m.jsxs)("label",{className:"relative block w-full",children:[a&&(0,m.jsx)("span",{className:"text-red-500 text-xs text-left w-full mb-2",children:a}),(0,m.jsx)("input",{type:"text",ref:U,name:"user_phone",className:"hidden absolute left-[-9999px]",autocomplete:"off"}),(0,m.jsx)("input",{className:"placeholder:italic placeholder:text-[#597291] block bg-white w-full border border-slate-300 rounded-md mb-3 py-2 pl-3 pr-3 shadow-sm focus:outline-none focus:border-sky-500 focus:ring-sky-500 focus:ring-1 sm:text-sm",placeholder:`${I("echo.register.form.emailAddressText")} *`,"aria-label":"Email Address",type:"email",name:"email",onChange:e=>O(e,"email"),onKeyUp:e=>{l(""),w(e.target.value),13===e.keyCode&&P()}})]}),(0,m.jsxs)("label",{className:"block w-full",children:[n&&(0,m.jsx)("span",{className:"text-red-500 text-xs text-left w-full mb-2",children:n}),(0,m.jsxs)("div",{className:"relative block w-full",children:[(0,m.jsx)("input",{className:"placeholder:italic placeholder:text-[#597291] block bg-white w-full border border-slate-300 rounded-md mb-3 py-2 pl-3 shadow-sm focus:outline-none focus:border-sky-500 focus:ring-sky-500 focus:ring-1 sm:text-sm pr-10",placeholder:`${I("echo.register.form.passwordText")} *`,"aria-label":"Password",type:"password",name:"password",onChange:e=>O(e,"password"),onKeyUp:e=>{g(""),y(e.target.value),13===e.keyCode&&P()},ref:S}),(0,m.jsx)(p.Z,{className:"right-3 top-3",pwInputRef:S,setShowPW:N,showPW:E})]})]})]}),"on"===(0,s.bG)("emailopt")&&(0,m.jsxs)("div",{class:"flex gap-2 items-center",children:[(0,m.jsx)("input",{id:"checked-checkbox",type:"checkbox",name:"emailOptIn",checked:C,onChange:e=>O(e,"emailOptIn"),className:"w-4 h-4 text-gray-600 bg-gray-100 border-gray-300 rounded dark:ring-offset-gray-800 dark:bg-gray-700 dark:border-gray-600"}),(0,m.jsx)("label",{for:"checked-checkbox",class:"text-xs text-blue-600 text-left",children:I("echo.register.form.emailOptText")})]}),(0,m.jsx)(c.E.button,{onClick:P,className:"mb-1 text-white font-bold py-3 px-6 my-3 rounded-lg w-full register-btn "+("02"===x||"02"===b?"bg-black mb-[5px]":"on"===x||"on"===b?"bg-blue-600 mb-[5px]":"bg-blue-600"),whileHover:{scale:1.1},whileTap:{scale:.9},"aria-label":"register",children:I("echo.register.form.continueText")}),(0,m.jsx)(c.E.button,{onClick:()=>t("login"),className:"bg-gray-50 mb-1 text-black py-3 rounded-lg w-full",whileHover:{backgroundColor:"#eee"},whileTap:{scale:.9},"aria-label":"login",children:I("echo.register.form.loginText")}),(0,m.jsxs)("div",{class:"flex items-center justify-center space-x-4 mb-4",children:[(0,m.jsx)("div",{class:"w-24 border-t border-gray-400"}),(0,m.jsx)("span",{class:"text-gray-500 text-sm",children:I("echo.register.form.orText")}),(0,m.jsx)("div",{class:"w-24 border-t border-gray-400"})]})]})};var f=r(53766),x=r(34492),b=r(11067),w=r(85334),y=r(727);const v=(0,o.lazy)(()=>r.e(1875).then(r.bind(r,71875))),k=(0,o.lazy)(()=>r.e(7138).then(r.bind(r,47138))),j=new URLSearchParams(window.location.search);const C=function(){const{t:e}=(0,h.$G)(),[t,r]=(0,o.useState)(!0),l=(0,s.bG)("members")||"",c=(0,s.bG)("reg_google")||"off",d=(0,s.bG)("reg_apple")||"off",p=(0,s.bG)("flow")||"",u=(0,a.gx)(),C=(0,s.bG)("smooth_login")||"off",E=(0,o.useRef)([]),N=(0,o.useRef)(null),[S,L]=(0,o.useState)(!1),[A,F]=(0,o.useState)(!1);let I=window.location.href,U="";const[R,O]=(0,o.useState)(""),[P,W]=(0,o.useState)(""),[T,z]=(0,o.useState)(!1),_=(0,o.useRef)(null),[Z,G]=(0,o.useState)(""),[V,D]=(0,o.useState)(""),[X,J]=(0,o.useState)(""),Y=(0,s.bG)("lp"),[H,K]=(0,o.useState)(!1),M=(0,s.bG)("reg_pop")||new URLSearchParams(window.location.search).get("reg_pop")||"0";(0,o.useEffect)(()=>{const e=()=>{const e=window.innerWidth,t=window.innerHeight;K(e<=415&&t<=753)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]),(0,o.useEffect)(()=>{(0,b.IP)()},[]),I.indexOf("staging")>-1?U="staging.":I.indexOf("dev")>-1&&(U="dev.");const q=e=>{const t="https://"+(0,x.oB)()+"start.ai-pro.org/login";"login"===e?window.top!==window.self?((0,s.I1)("kt8typtb","arcana",{path:"/"}),(0,s.I1)("kt8typtb","arcana",{path:"/",domain:".ai-pro.org"}),window.top.location.href=t):setTimeout(()=>{window.location.href=e},300):window.location.href=e},B=(0,o.useCallback)(()=>{"on"===C&&E.current.forEach((e,t)=>{if(e){var r;const t=e.textContent,o=e.offsetHeight,a=t?0!==o?o:(null!==(r=e.scrollHeight)&&void 0!==r?r:0)+12:0;setTimeout(()=>{e.style.height=`${a}px`},100)}})},[C]);return setTimeout(()=>{const e=N.current,t=navigator.userAgent.includes("Chrome")||navigator.userAgent.includes("Edg");if(e&&t){const t=e.querySelectorAll("input");t&&t.length>0&&t.forEach(e=>{e.matches(":-internal-autofill-selected")&&e.classList.add("autofilled")})}},200),(0,o.useEffect)(()=>{if("on"===C)return B(),window.addEventListener("resize",B),()=>{window.removeEventListener("resize",B)}},[C,B]),(0,o.useEffect)(()=>{"on"===C&&B()},[R,P,C,B]),(0,o.useEffect)(()=>{i().options={positionClass:"toast-top-center"};const e=setInterval(()=>{let e=(0,s.bG)("e")||j.get("e"),t="";null!==e&&""!==e&&(t=f.find(t=>{let{error_code:r}=t;return r===e}),void 0!==t&&(i().error(t.msg),(0,s.Sz)("e",{path:"/"}),(0,s.Sz)("e",{path:"/",domain:".ai-pro.org"})))},500);return()=>clearInterval(e)},[]),(0,o.useEffect)(()=>{const e=(0,s.bG)("reg_google"),r=(0,s.bG)("reg_apple");if("02"!==e&&"02"!==c||L(!0),"02"!==r&&"02"!==d||F(!0),void 0!==u&&t&&!1==!u){if("active"===u.status)return void("aihub"===Y?window.top.location.href="/my-account":q("/my-account"));if("aihub"===Y)try{const e=window.top.location.href;console.log("Parent window URL:",e),e.includes("/create-account")?window.top.location.href="https://"+U+"chat.ai-pro.org":q("/pricing")}catch(e){console.log("Cross-origin error, using default redirect:",e),q("/pricing")}else q("/pricing")}},[u,t,l,p,U,c,d,Y]),(0,o.useEffect)(()=>{const e=setInterval(()=>{"register-auth"===(0,s.bG)("flow")&&(0,s.bG)("user_email")&&("aihub"===Y?window.location.href="/pricing":q("/pricing"),clearInterval(e))},500);return()=>clearInterval(e)},[Y,u]),(0,o.useEffect)(()=>{if("chatapp"===p){const e="https://"+U+"chatpro.ai-pro.org/chat/new",t=document.createElement("link");t.href=e,t.rel="prefetch",t.as="document",t.setAttribute("data-cache-control","max-age=86400"),document.head.appendChild(t)}},[p,U]),(0,o.useEffect)(()=>{i().options={positionClass:"toast-top-center"},"on"===(0,s.bG)("emailopt")&&J(!0)},[]),(0,o.useEffect)(()=>{console.log("document.cookie:",document.cookie);const e=(0,s.bG)("reg_pop"),t=(0,s.bG)("lpCookie");console.log("reg_pop:",e,"| lpCookie:",t)},[]),(0,m.jsxs)(m.Fragment,{children:[(0,m.jsxs)(n.q,{children:[(0,m.jsx)("title",{children:"AI Pro | Account Registration"}),(0,m.jsx)("meta",{name:"description",content:"Join now and register for a free account at AI Pro. Unlock access to cutting-edge AI tools and resources. Sign up today to get started!"})]}),(0,m.jsx)(o.Suspense,{fallback:null,children:"1"===M?(0,m.jsxs)("div",{className:(H?"":"flex items-center")+" fixed inset-0 z-50 justify-center h-screen bg-white",children:[(0,m.jsx)("img",{src:y,alt:"",className:"absolute top-0 left-0 h-inherit xl:w-[34%] hidden xl:block lg:block md:block sm:block",style:{minHeight:"-webkit-fill-available"}}),(0,m.jsx)(v,{handleRedirect:q})]}):"aihub"===Y?(0,m.jsxs)("div",{className:"min-h-screen bg-white flex flex-col items-center justify-center p-4",children:[(0,m.jsx)("div",{className:"max-w-md w-full",children:(0,m.jsxs)("div",{className:"bg-[#FCFCFC] rounded-2xl p-8",children:[(0,m.jsxs)("div",{className:"text-center mb-8",children:[(0,m.jsx)("img",{src:w,alt:"AI Pro Logo",className:"mx-auto mb-2 h-12"}),(0,m.jsx)("h1",{className:"text-[20px] font-bold text-black mb-2",children:"Create Account"})]}),(0,m.jsxs)("div",{className:"space-y-4",children:[(0,m.jsx)(g,{setWillRedirect:r,handleRedirect:q,reg_google:c,reg_apple:d,emailError:R,passwordError:P,setEmailError:O,setPasswordError:W,members:l,setEmail:G,setPassword:D,setEmailOptIn:J,email:Z,password:V,emailOptIn:X,showPW:T,setShowPW:z,pwInputRef:_,spanErrorRefs:E,smooth_login:C,prefix:U,isAiHub:!0}),(0,m.jsx)(k,{reg_google:"aihub"===Y?"on":c,reg_apple:"aihub"===Y?"on":d,handleRedirect:q,regGoogleShown:S,regAppleShown:A,isAiHub:!0})]})]})}),(0,m.jsx)("div",{className:"text-center mt-8 absolute bottom-[15px] left-0 right-0 max-h-600:relative max-h-600:bottom-0",children:(0,m.jsxs)("span",{className:"text-xs text-[#3073D5]",children:[(0,m.jsx)("button",{type:"button",tabIndex:0,className:"cursor-pointer hover:underline hover:text-blue-700 transition-colors duration-200 bg-transparent border-none p-0 text-xs text-[#3073D5]",onClick:e=>{e.preventDefault();try{window.top.open(`https://${U}ai-pro.org/member-tos-page`,"_blank")||(window.top.location.href=`https://${U}ai-pro.org/member-tos-page`)}catch(e){window.top.location.href=`https://${U}ai-pro.org/member-tos-page`}},onKeyDown:e=>{if("Enter"===e.key){e.preventDefault();try{window.top.open(`https://${U}ai-pro.org/member-tos-page`,"_blank")||(window.top.location.href=`https://${U}ai-pro.org/member-tos-page`)}catch(e){window.top.location.href=`https://${U}ai-pro.org/member-tos-page`}}},children:"Terms of Use"})," / ",(0,m.jsx)("button",{type:"button",tabIndex:0,className:"cursor-pointer hover:underline hover:text-blue-700 transition-colors duration-200 bg-transparent border-none p-0 text-xs text-[#3073D5]",onClick:e=>{e.preventDefault();try{window.top.open(`https://${U}ai-pro.org/privacy-policy`,"_blank")||(window.top.location.href=`https://${U}ai-pro.org/privacy-policy`)}catch(e){window.top.location.href=`https://${U}ai-pro.org/privacy-policy`}},onKeyDown:e=>{if("Enter"===e.key){e.preventDefault();try{window.top.open(`https://${U}ai-pro.org/privacy-policy`,"_blank")||(window.top.location.href=`https://${U}ai-pro.org/privacy-policy`)}catch(e){window.top.location.href=`https://${U}ai-pro.org/privacy-policy`}}},children:"Privacy Policy"})]})})]}):(0,m.jsx)("div",{className:"flex vertical-center items-center h-screen",children:(0,m.jsx)("div",{className:"container md:pt-[70px] px-0 md:px-4 sm:px-0 min-w-[330px] w-64 sm:w-80 md:w-96 mx-auto",children:(0,m.jsxs)("div",{className:"reg_col text-center mb-8",children:[(0,m.jsx)("h1",{className:"text-2xl h-[32px] lg:text-2xl font-bold text-center mb-2 lg:mb-8",children:e("echo.register.index.createAccountText")}),(0,m.jsx)("div",{className:"bg-white rounded-lg shadow-lg overflow-hidden",ref:N,children:(0,m.jsxs)("div",{className:"p-6 text-black",children:[(0,m.jsx)(g,{setWillRedirect:r,handleRedirect:q,reg_google:c,reg_apple:d,emailError:R,passwordError:P,setEmailError:O,setPasswordError:W,members:l,setEmail:G,setPassword:D,setEmailOptIn:J,email:Z,password:V,emailOptIn:X,showPW:T,setShowPW:z,pwInputRef:_,spanErrorRefs:E,smooth_login:C,prefix:U,isAiHub:!1}),(0,m.jsx)(k,{reg_google:c,reg_apple:d,handleRedirect:q,regGoogleShown:S,regAppleShown:A,isAiHub:!1})]})})]})})})})]})}},89983:(e,t,r)=>{r.d(t,{w_:()=>c});var o=r(72791),a={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},s=o.createContext&&o.createContext(a),n=function(){return n=Object.assign||function(e){for(var t,r=1,o=arguments.length;r<o;r++)for(var a in t=arguments[r])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},n.apply(this,arguments)},l=function(e,t){var r={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(r[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var a=0;for(o=Object.getOwnPropertySymbols(e);a<o.length;a++)t.indexOf(o[a])<0&&Object.prototype.propertyIsEnumerable.call(e,o[a])&&(r[o[a]]=e[o[a]])}return r};function i(e){return e&&e.map(function(e,t){return o.createElement(e.tag,n({key:t},e.attr),i(e.child))})}function c(e){return function(t){return o.createElement(d,n({attr:n({},e.attr)},t),i(e.child))}}function d(e){var t=function(t){var r,a=e.attr,s=e.size,i=e.title,c=l(e,["attr","size","title"]),d=s||t.size||"1em";return t.className&&(r=t.className),e.className&&(r=(r?r+" ":"")+e.className),o.createElement("svg",n({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,a,c,{className:r,style:n(n({color:e.color||t.color},t.style),e.style),height:d,width:d,xmlns:"http://www.w3.org/2000/svg"}),i&&o.createElement("title",null,i),e.children)};return void 0!==s?o.createElement(s.Consumer,null,function(e){return t(e)}):t(a)}},727:(e,t,r)=>{e.exports=r.p+"static/media/left-rect.b50c5dc799e55107dec2.png"},85334:e=>{e.exports="data:image/png;base64,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"},53766:e=>{e.exports=JSON.parse('[{"error_code":"dbivartanw","msg":"User already exists."},{"error_code":"fbprmyulcd","msg":"Email address blocked."},{"error_code":"aiafttlaob","msg":"Something went wrong. Please try again later."},{"error_code":"pzmzwxjbnp","msg":"Invalid login."}]')}}]);
//# sourceMappingURL=8339.7db6282f.chunk.js.map