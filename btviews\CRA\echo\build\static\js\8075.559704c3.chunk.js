"use strict";(self.webpackChunkv1=self.webpackChunkv1||[]).push([[8075,9044],{34492:(e,t,r)=>{r.d(t,{aS:()=>i,mD:()=>y,mW:()=>x,o0:()=>m,oB:()=>h,p6:()=>c,rZ:()=>o,tN:()=>p,x6:()=>l,yt:()=>u});var a=r(74335),s=r(80184);function n(e){return e?"usd"===e.toLowerCase()?"$":"eur"===e.toLowerCase()?"€":"gbp"===e.toLowerCase()?"£":"brl"===e.toLowerCase()||"sar"===e.toLowerCase()?"R$":"":""}function o(e){return e?"usd"===e.toLowerCase()||"eur"===e.toLowerCase()?"US":"gbp"===e.toLowerCase()?"GB":"brl"===e.toLowerCase()?"US":"sar"===e.toLowerCase()?"AE":"chf"===e.toLowerCase()?"CH":"sek"===e.toLowerCase()?"SE":"US":""}function c(e){if(!e)return"";const t=new Date(e.replace(/ /g,"T"));return["January","February","March","April","May","June","July","August","September","October","November","December"][t.getMonth()]+" "+t.getDate()+" "+t.getFullYear()}function i(e){return e.trial_price?e.trial_price:e.price?e.price:"0"}function l(e){return e.toString().replace(/\B(?=(\d{3})+(?!\d))/g,",")}function d(e){const t=parseFloat(e);return l(t%1==0?t.toFixed(0):t.toFixed(2))}function u(e,t){return e&&t?isNaN(t)?"":parseFloat(t)>=0?"sar"===e.toLowerCase()?d(t).toLocaleString("en-US")+"ر.س.":"aed"===e.toLowerCase()?d(t).toLocaleString("en-US")+"AED":"chf"===e.toLowerCase()?d(t).toLocaleString("en-US")+"CHF":"sek"===e.toLowerCase()?d(t).toLocaleString("en-US")+"SEK":"pln"===e.toLowerCase()?d(t).toLocaleString("en-US")+"zł":"ron"===e.toLowerCase()?d(t).toLocaleString("en-US")+"LEI":"czk"===e.toLowerCase()?"Kč"+d(t).toLocaleString("en-US"):"huf"===e.toLowerCase()?d(t).toLocaleString("en-US")+"Ft":"dkk"===e.toLowerCase()?d(t).toLocaleString("en-US")+"kr.":"bgn"===e.toLowerCase()?d(t).toLocaleString("en-US")+"лв":"try"===e.toLowerCase()?d(t).toLocaleString("en-US")+"₺":n(e)+d(t).toLocaleString("en-US"):"-"+n(e)+(-1*d(t)).toLocaleString("en-US"):""}function p(e,t){e=new Date(e);var r=((t=new Date(t)).getTime()-e.getTime())/1e3;return r/=60,Math.abs(Math.round(r))}function m(e){if(!e)return"";const t=new Date(e.replace(/ /g,"T"));return["January","February","March","April","May","June","July","August","September","October","November","December"][t.getMonth()]+" "+t.getDate()+" "+t.getFullYear()+" "+t.getHours()+":"+t.getMinutes()}function x(e){let{plan:t}=e,r="",n="";return"Yearly"===t.payment_interval&&(r=u(t.currency,parseFloat(t.price/365).toFixed(2)),n=(0,s.jsxs)("div",{class:"text-xs mb-4",children:["billed ",u(t.currency,t.price),"/year"]})),"Monthly"===t.payment_interval&&(r=u(t.currency,parseFloat(t.price/30).toFixed(2)),n=(0,s.jsxs)("div",{class:"text-xs mb-4",children:["billed ",u(t.currency,t.price),"/Month"]})),t.trial_price&&(r=u(t.currency,parseFloat(t.trial_price/t.trial_days).toFixed(2)),n=(0,s.jsxs)("div",{class:"text-xs mb-4",children:["billed ",u(t.currency,t.trial_price)]})),(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("p",{className:"text-4xl font-bold text-gray-800 "+(""===(0,a.bG)("p_toggle")?"mb-4":""),children:[r," ",(0,s.jsx)("span",{className:"text-sm",children:" per Day"})]}),n]})}function y(e){let{plan:t}=e;return"on"===(0,a.bG)("daily")?x({plan:t}):t.trial_price?(0,s.jsx)("p",{className:"text-4xl font-bold text-gray-800 mb-4",children:u(t.currency,t.trial_price)}):(0,s.jsxs)("p",{className:"text-4xl font-bold text-gray-800 mb-4",children:[u(t.currency,t.price),(0,s.jsxs)("span",{className:"text-sm",children:["/","Monthly"===t.payment_interval?"month":"year"]})]})}function h(){const e=window.location.href;return e.indexOf("staging")>-1?"staging.":e.indexOf("dev")>-1?"dev.":""}},30632:(e,t,r)=>{r.r(t),r.d(t,{default:()=>h});var a=r(72791),s=r(96347),n=(r(48297),r(27250)),o=r(56355),c=r(63019),i=r(91615),l=r(28891),d=r(74335),u=r(31243),p=r(34492),m=r(95828),x=r.n(m),y=(r(92831),r(80184));const h=function(){(0,a.useEffect)(()=>{x().options={positionClass:"toast-top-center"}},[]),(0,a.useEffect)(()=>{let e=(0,d.bG)("threed_error");void 0!==e&&""!==e&&setTimeout(function(){(0,d.I1)("threed_error",""),x().error(e)},2e3)},[]);const e=(0,l.gx)("/register-auth"),[t]=(0,a.useState)(window.view_data?window.view_data:{}),[r]=(0,a.useState)(!!t.isUserTrial&&1===t.isUserTrial),[m]=(0,a.useState)(t.plan?t.plan:null);if(void 0===e||!1===e)return;if(!m)return;const h=(0,d.bG)("access"),w=e.merchant.toLowerCase();return(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(n.default,{auth:e}),m?(0,y.jsx)("div",{className:"Payment-upgrade bg-gray-100 min-h-[600px] mt-[50px] flex",children:(0,y.jsx)("div",{className:"container mx-auto py-10",children:(0,y.jsx)("div",{className:"flex flex-col items-center py-10 lg:py-16",children:(0,y.jsx)("div",{className:"flex flex-wrap md:flex-wrap justify-center w-full",children:(0,y.jsxs)("div",{className:"pay_right px-4 mb-8 md:w-2/5",children:[(0,y.jsx)("h2",{className:"text-xl font-bold mb-4 py-10",children:"Order Summary"}),(0,y.jsxs)("div",{className:"flex",children:[(0,y.jsxs)("div",{className:"mb-2 w-4/5 text-sm mr-4",children:[(0,y.jsxs)("b",{className:"text-md text-uppercase",children:[m.plan_type_display," PLAN"]}),(0,y.jsx)("br",{}),r?(0,y.jsxs)("div",{children:["You will be charged ",(0,p.yt)(m.currency,(0,p.aS)(m))," per ",m.payment_interval&&"yearly"===m.payment_interval.toLowerCase()?"year":"month"," after trial ends."]}):(0,y.jsx)(y.Fragment,{}),m.display_txt3?m.display_txt3:"Your subscription will renew monthly until you cancel it."]}),(0,y.jsxs)("div",{className:"font-bold mt-4 w-1/5",children:["Total: ",(0,p.yt)(m.currency,(0,p.aS)(m))]})]}),(0,y.jsx)("div",{className:"flex",children:(0,y.jsx)("div",{className:"mb-2 w-full text-sm",children:(0,y.jsx)(s.E.button,{className:"bg-blue-500 text-white font-bold py-3 px-6 rounded-lg w-full my-4 proceed-pmt",whileHover:{backgroundColor:"#5997fd"},whileTap:{scale:.9},onClick:function(){console.log(w),"paypal"===w?(document.querySelector(".loader-container").classList.add("active"),u.Z.post("http://localhost:9002/api/create-subscription-paypal",{tk:h,plan_id:m.plan_id,upgrade:"1"},{headers:{"content-type":"application/x-www-form-urlencoded"}}).then(function(e){let t=e.data;t.success?window.location.replace(t.data.link):(document.querySelector(".loader-container").classList.remove("active"),t.data&&x().error(t.data.msg))})):(document.querySelector(".loader-container").classList.add("active"),u.Z.post("http://localhost:9002/api/update-subscription",{tk:h,plan_id:m.plan_id},{headers:{"content-type":"application/x-www-form-urlencoded"}}).then(function(e){let r=e.data;if(r.success){if(r.redirect&&""!==r.redirect)return void(window.location=r.redirect);x().success("Success");var a=r.data.old_plan,s=r.data.new_plan;if(window.mixpanel.track("update_subscription",{currency:r.data.currency,action:t.update_action,old_plan:a.plan_type+" - "+a.payment_interval,new_plan:s.plan_type+" - "+s.payment_interval}),"cancel_downgrade"!==t.update_action)return window.location.href="/thankyou",void document.querySelector(".loader-container").classList.remove("active");u.Z.post("http://localhost:9002/api/save-survey",{}).then(e=>{let t=e.data;if(t.success)return x().success("Survey submitted successfully!"),window.location.href="/thankyou",void document.querySelector(".loader-container").classList.remove("active");t.data&&(r.data.message?x().error(r.data.message):x().error(r.data.msg))})}else r.data&&(r.data.message?x().error(r.data.message):x().error(r.data.msg),document.querySelector(".loader-container").classList.remove("active"))}))},children:"Complete Purchase"})})}),(0,y.jsxs)("div",{className:"securecont border-t-2 border-gray-300 flex py-5",children:[(0,y.jsxs)("div",{className:"securetext mb-2 text-sm w-1/2",children:[(0,y.jsx)(o.kUi,{className:"inline text-lg mr-1 text-orange-500 text-xs"})," Secure Checkout"]}),(0,y.jsxs)("div",{className:"securelogo mb-2 text-sm w-1/2 flex flex-wrap justify-center items-center",children:[(0,y.jsx)("img",{src:c,alt:"Secure Logo",className:"cclogo inline"}),(0,y.jsxs)("div",{className:"flex items-center justify-center flex-wrap",children:[(0,y.jsx)("img",{src:i,alt:"Authorize",className:"text-center md:text-right py-2 w-20"}),(0,y.jsx)("button",{onClick:()=>{window.open("//www.securitymetrics.com/site_certificate?id=1982469&tk=d41c416c6208f1136426bc8038178d2e","Verification","location=no, toolbar=no, resizable=no, scrollbars=yes, directories=no, status=no,top=100,left=100, width=700, height=600")},title:"SecurityMetrics card safe certification",className:"h-20",children:(0,y.jsx)("img",{loading:"lazy",src:"https://www.securitymetrics.com/portal/app/ngsm/assets/img/GreyContent_Credit_Card_Safe_White_Sqr.png",alt:"SecurityMetrics card safe certification logo",className:"max-h-full",width:"80",height:"80"})})]})]})]})]})})})})}):""]})}}}]);
//# sourceMappingURL=8075.559704c3.chunk.js.map