{"version": 3, "file": "static/js/2377.811954e0.chunk.js", "mappings": "mHA6FA,MACA,EAAe,IAA0B,yD,+IC7FlC,SAASA,EAAYC,GACxB,OAAIA,EAC0B,QAA3BA,EAASC,cAAgC,IACd,QAA3BD,EAASC,cAAgC,IACd,QAA3BD,EAASC,cAAgC,IACd,QAA3BD,EAASC,eACkB,QAA3BD,EAASC,cADgC,KAEd,QAA3BD,EAASC,cAAgC,KACrC,GAPc,EAQzB,CAEO,SAASC,EAAWF,GACvB,OAAIA,EAC0B,QAA3BA,EAASC,eACkB,QAA3BD,EAASC,cADgC,KAEd,QAA3BD,EAASC,cAAgC,KACd,QAA3BD,EAASC,cAAgC,KACd,QAA3BD,EAASC,cAAgC,KACd,QAA3BD,EAASC,cAAgC,KACd,QAA3BD,EAASC,cAAgC,KACrC,KARc,EASzB,CAEO,SAASE,EAAWC,GACvB,IAAIA,EAAS,MAAO,GACpB,MACMC,EAAO,IAAIC,KAAKF,EAAQG,QAAQ,KAAK,MAE3C,MAHc,CAAC,UAAU,WAAW,QAAQ,QAAQ,MAAM,OAAO,OAAO,SAAS,YAAY,UAAU,WAAW,YAGrGF,EAAKG,YAAc,IAAMH,EAAKI,UAAY,IAAMJ,EAAKK,aACtE,CAEO,SAASC,EAASC,GACrB,OAAGA,EAAKC,YAAoBD,EAAKC,YAC9BD,EAAKE,MAAcF,EAAKE,MACpB,GACX,CAEO,SAASC,EAAiBC,GAC7B,OAAOA,EAAEC,WAAWV,QAAQ,wBAAyB,IACzD,CAEO,SAASW,EAAaC,GAC3B,MAAMC,EAAcC,WAAWF,GAC/B,OACMJ,EADEK,EAAc,GAAM,EACLA,EAAYE,QAAQ,GACpBF,EAAYE,QAAQ,GAC7C,CAEO,SAASC,EAAavB,EAAUc,GACnC,OAAId,GAAac,EAEdU,MAAMV,GAAe,GAErBO,WAAWP,IAAU,EACU,QAA3Bd,EAASC,cACDiB,EAAaJ,GAAOW,eAAe,SAAW,OACpB,QAA3BzB,EAASC,cACRiB,EAAaJ,GAAOW,eAAe,SAAW,MACpB,QAA3BzB,EAASC,cACRiB,EAAaJ,GAAOW,eAAe,SAAW,MACpB,QAA3BzB,EAASC,cACRiB,EAAaJ,GAAOW,eAAe,SAAW,MACpB,QAA3BzB,EAASC,cACRiB,EAAaJ,GAAOW,eAAe,SAAW,KACrB,QAA3BzB,EAASC,cACPiB,EAAaJ,GAAOW,eAAe,SAAW,MACpB,QAA3BzB,EAASC,cACRiB,EAAaJ,GAAOW,eAAe,SAAW,KACpB,QAA3BzB,EAASC,cACRiB,EAAaJ,GAAOW,eAAe,SAAW,MACpB,QAA3BzB,EAASC,cACRiB,EAAaJ,GAAOW,eAAe,SAAW,KACpB,QAA3BzB,EAASC,cACRiB,EAAaJ,GAAOW,eAAe,SAAW,IAElD1B,EAAYC,GAAYkB,EAAaJ,GAAOW,eAAe,SAG/D,IAAM1B,EAAYC,KAAoC,EAAvBkB,EAAaJ,IAAaW,eAAe,SA7BhD,EA8BnC,CAEO,SAASC,EAAQC,EAAKC,GAEzBD,EAAM,IAAIrB,KAAKqB,GAEf,IAAIE,IADJD,EAAM,IAAItB,KAAKsB,IACAE,UAAYH,EAAIG,WAAa,IAE5C,OADAD,GAAQ,GACDE,KAAKC,IAAID,KAAKE,MAAMJ,GAC/B,CAEO,SAASK,EAAe9B,GAC3B,IAAIA,EAAS,MAAO,GACpB,MACMC,EAAO,IAAIC,KAAKF,EAAQG,QAAQ,KAAK,MAE3C,MAHc,CAAC,UAAU,WAAW,QAAQ,QAAQ,MAAM,OAAO,OAAO,SAAS,YAAY,UAAU,WAAW,YAGrGF,EAAKG,YAAc,IAAMH,EAAKI,UAAY,IAAMJ,EAAKK,cAAgB,IAAML,EAAK8B,WAAa,IAAM9B,EAAK+B,YACzH,CAEO,SAASC,EAAUC,GAAU,IAAT,KAACC,GAAKD,EAC3BE,EAAa,GACbC,EAAW,GAgBf,MAf8B,WAA1BF,EAAKG,mBACPF,EAAajB,EAAagB,EAAKvC,SAAUqB,WAAWkB,EAAKzB,MAAQ,KAAKQ,QAAQ,IAC9EmB,GAAWE,EAAAA,EAAAA,MAAA,OAAKC,MAAM,eAAcC,SAAA,CAAC,UAAQtB,EAAagB,EAAKvC,SAAUuC,EAAKzB,OAAO,YAGzD,YAA1ByB,EAAKG,mBACPF,EAAajB,EAAagB,EAAKvC,SAAUqB,WAAWkB,EAAKzB,MAAQ,IAAIQ,QAAQ,IAC7EmB,GAAWE,EAAAA,EAAAA,MAAA,OAAKC,MAAM,eAAcC,SAAA,CAAC,UAAQtB,EAAagB,EAAKvC,SAAUuC,EAAKzB,OAAO,aAGnFyB,EAAK1B,cACP2B,EAAajB,EAAagB,EAAKvC,SAAUqB,WAAWkB,EAAK1B,YAAc0B,EAAKO,YAAYxB,QAAQ,IAChGmB,GAAWE,EAAAA,EAAAA,MAAA,OAAKC,MAAM,eAAcC,SAAA,CAAC,UAAQtB,EAAagB,EAAKvC,SAAUuC,EAAK1B,kBAI9E8B,EAAAA,EAAAA,MAAAI,EAAAA,SAAA,CAAAF,SAAA,EACEF,EAAAA,EAAAA,MAAA,KAAGK,UAAS,oCAAAC,OAAiE,MAA1BC,EAAAA,EAAAA,IAAU,YAAsB,OAAS,IAAKL,SAAA,CAC9FL,EAAW,KAACW,EAAAA,EAAAA,KAAA,QAAMH,UAAU,UAASH,SAAC,gBAExCJ,IAGP,CAEO,SAASW,EAAcC,GAAU,IAAT,KAACd,GAAKc,EACnC,MAA2B,QAAvBH,EAAAA,EAAAA,IAAU,SACLb,EAAW,CAACE,SAEjBA,EAAK1B,aACCsC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,wCAAuCH,SAAEtB,EAAagB,EAAKvC,SAAUuC,EAAK1B,gBAG/F8B,EAAAA,EAAAA,MAAA,KAAGK,UAAU,wCAAuCH,SAAA,CACjDtB,EAAagB,EAAKvC,SAAUuC,EAAKzB,QAClC6B,EAAAA,EAAAA,MAAA,QAAMK,UAAU,UAASH,SAAA,CAAC,IACK,YAA1BN,EAAKG,iBAAiC,QAAU,YAI3D,CAEO,SAASY,EAAqBf,GAAO,IAADgB,EAAAC,EACzC,MAAMC,GAA+B,QAArBF,GAACL,EAAAA,EAAAA,IAAU,kBAAU,IAAAK,EAAAA,EAAI,MAAMtD,cACzCyD,GAA2B,QAAnBF,GAACN,EAAAA,EAAAA,IAAU,gBAAQ,IAAAM,EAAAA,EAAI,OAAOvD,eACtC,WAAE6C,EAAU,iBAAEJ,EAAgB,YAAE7B,EAAW,SAAEb,EAAQ,gBAAE2D,GAAoBpB,EACjF,IAAMqB,aAAcC,EAAmB,MAAE/C,GAAUyB,EAEnD,GAAIO,EAAa,GAAKjC,EAAc,GAAiB,OAAZ4C,EAAkB,CAEzD,IAAIK,EAAShD,EACTiD,EAAa,QAEH,OAAVL,IACFI,EAASzC,WAAWP,GALe,WAArB4B,EAAgC,IAAM,KAKfpB,QAAQ,GAC7CyC,EAAU,kBAAAd,OAAqBU,EAAkB7C,EAAK,KAAAmC,OAAIP,EAAgB,MAG5EmB,GAAmB,QAAAZ,OAAYH,EAAU,0BAAAG,OAAyB1B,EAAavB,EAAU8D,GAAO,SAAAb,OAAQc,EAAU,SACpH,CAEA,OAAOF,CACT,C,sHChGA,QA9DA,SAAmBvB,GAAkC,IAAjC,YAAE0B,EAAW,KAAEC,GAAOC,EAAAA,EAAAA,OAAQ5B,EAChD,MAAM6B,EAAWC,OAAOC,SAASF,SAC3BG,EAAc,yBAAyBC,KAAKJ,GAC5CK,EAAgB,sBAAsBD,KAAKJ,GAC3CM,EAAc,yBAAyBF,KAAKJ,GAC5CO,EAAiBP,EAASQ,SAAS,aACnCC,GAAYZ,GAGlBa,EAAAA,EAAAA,YAAU,KACR,MAAMC,EAAUC,EAAkBC,EAAAA,EAAW,SACvCC,EAAWF,EAAkBG,EAAe,SAMlD,OALAC,SAASC,KAAKC,OAAOP,EAASG,GAEzBP,GACH,yDAEK,KACLI,EAAQQ,SACRL,EAASK,YAEV,CAACZ,IAGJ,MAAMK,EAAoBA,CAACQ,EAAMC,KAC/B,MAAMC,EAAON,SAASO,cAAc,QAIpC,OAHAD,EAAKE,IAAM,UACXF,EAAKF,KAAOA,EACZE,EAAKD,GAAKA,EACHC,GAGT,OACE9C,EAAAA,EAAAA,MAAAI,EAAAA,SAAA,CAAAF,SAAA,CA1B2E,IAgCzEM,EAAAA,EAAAA,KAAA,UAAQyC,GAAG,SAAS5C,UAAS,8FAAAC,OAAuI,IAAKJ,UACvKF,EAAAA,EAAAA,MAAA,OAAKK,UAAU,2DAA0DH,SAAA,EACvEF,EAAAA,EAAAA,MAAA,WAASK,UAAU,YAAWH,SAAA,EAC5BM,EAAAA,EAAAA,KAAA,UAAQ0C,KAAK,aAAaC,OAAQZ,EAAea,MAAM,MAAMC,OAAO,KAAKhD,UAAU,eACnFG,EAAAA,EAAAA,KAAA,OAAK8C,IAAKjB,EAAAA,EAAWkB,IAAI,cAAclD,UAAU,kBAEjDsB,GAAeE,GAAiBC,IAAgBG,IAChDzB,EAAAA,EAAAA,KAAA,OAAKH,UAAU,uCAAuC4C,GAAG,OAAM/C,UAC7DM,EAAAA,EAAAA,KAAA,MAAIH,UAAU,2BAA0BH,UACtCM,EAAAA,EAAAA,KAAA,MAAIH,UAAU,uBAAsBH,UAClCM,EAAAA,EAAAA,KAAA,KAAGoC,KAAMtB,EAAO,cAAgB,SAAUjB,UAAU,YAAY,aAAYiB,EAAO,aAAe,QAAQpB,SACvGoB,EAAO,UAAY,wBAUxC,C", "sources": ["assets/images/AIPRO.svg", "core/utils/main.jsx", "header/headerlogo.jsx"], "sourcesContent": ["var _g, _defs;\nconst _excluded = [\"title\", \"titleId\"];\nfunction _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\nfunction _objectWithoutProperties(e, t) { if (null == e) return {}; var o, r, i = _objectWithoutPropertiesLoose(e, t); if (Object.getOwnPropertySymbols) { var n = Object.getOwnPropertySymbols(e); for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]); } return i; }\nfunction _objectWithoutPropertiesLoose(r, e) { if (null == r) return {}; var t = {}; for (var n in r) if ({}.hasOwnProperty.call(r, n)) { if (-1 !== e.indexOf(n)) continue; t[n] = r[n]; } return t; }\nimport * as React from \"react\";\nfunction SvgAipro(_ref, svgRef) {\n  let {\n      title,\n      titleId\n    } = _ref,\n    props = _objectWithoutProperties(_ref, _excluded);\n  return /*#__PURE__*/React.createElement(\"svg\", _extends({\n    width: 451,\n    height: 157,\n    viewBox: \"0 0 451 157\",\n    fill: \"none\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, _g || (_g = /*#__PURE__*/React.createElement(\"g\", {\n    clipPath: \"url(#clip0_2054_286)\"\n  }, /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M92.717 79.8955C91.7079 79.898 90.8221 79.5666 90.0844 78.8815C89.438 78.2806 88.7889 77.6721 88.2042 77.0144C87.6096 76.3467 86.6769 76.3763 86.107 77.0193C85.6035 77.5881 85.0483 78.1124 84.5007 78.6392C83.2546 79.8361 81.7939 80.17 80.1927 79.5345C78.5839 78.8964 77.7647 77.6277 77.6759 75.9088C77.5969 74.3657 78.5568 72.8595 79.9853 72.1993C81.4707 71.5118 83.1683 71.7739 84.3896 72.909C85.0064 73.4827 85.6159 74.0689 86.1736 74.6996C86.7732 75.3747 87.6441 75.4266 88.3054 74.697C88.8433 74.1034 89.4207 73.5446 90.0029 72.9931C91.1724 71.8876 92.9095 71.5982 94.3554 72.2437C95.8358 72.9065 96.7735 74.4077 96.7265 76.0424C96.6649 78.1989 94.8908 79.9029 92.717 79.898V79.8955Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M67.7897 52.4641C66.3315 52.4716 65.4408 52.1476 64.703 51.4625C64.0418 50.8467 63.4125 50.1963 62.7685 49.5607C61.9938 48.7965 61.3917 48.799 60.6096 49.5805C60.0273 50.1592 59.4575 50.7528 58.8652 51.324C57.6735 52.479 55.9735 52.7807 54.4807 52.1204C52.9607 51.4453 52.0034 49.9465 52.1022 48.2797C52.2008 46.6375 53.0151 45.4208 54.5324 44.7876C56.0894 44.1372 57.555 44.4093 58.8159 45.5469C59.4253 46.0959 60.0125 46.6771 60.5579 47.2904C61.2882 48.114 62.1296 48.1486 62.9068 47.2706C63.4373 46.6722 64.0196 46.1182 64.6019 45.5667C65.7887 44.4414 67.5455 44.1595 69.0036 44.8371C70.4841 45.5246 71.3971 47.0258 71.3305 48.6605C71.2441 50.7874 69.4824 52.4667 67.7873 52.4667L67.7897 52.4641Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M68.2449 83.3704C68.3484 82.7224 68.504 81.7086 68.6792 80.697C68.8543 79.688 68.5286 79.1711 67.5268 78.9733C66.725 78.8125 65.9107 78.7061 65.1113 78.533C63.2213 78.125 61.8889 76.426 61.9703 74.5142C62.0665 72.2217 63.8307 70.9234 65.3531 70.686C68.0179 70.2704 70.3421 72.6297 69.8979 75.2563C69.7722 76.0055 69.6439 76.7548 69.5205 77.5042C69.4884 77.7069 69.4564 77.9123 69.4515 78.1176C69.4317 78.7557 69.7771 79.2354 70.4063 79.3689C71.1268 79.5223 71.857 79.6335 72.5849 79.7448C74.3713 80.0168 75.6544 81.1248 76.0813 82.7794C76.6538 85.0002 75.1831 87.2457 72.9379 87.6885C70.616 88.146 68.1757 86.2887 68.2399 83.3728L68.2449 83.3704Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M89.2851 68.3141C87.6986 68.3438 85.8727 67.2137 85.4114 65.188C84.9944 63.3655 85.8802 61.518 87.558 60.7093C88.3525 60.326 89.1692 59.9946 89.9711 59.6286C90.8741 59.2181 91.0986 58.6493 90.7163 57.7392C90.4128 57.0194 90.0845 56.3122 89.7835 55.59C88.7645 53.1467 90.0994 50.5919 92.6827 50.033C95.1897 49.4889 97.6644 51.7048 97.4003 54.267C97.24 55.8274 96.4528 56.9577 95.0316 57.623C94.289 57.9716 93.5316 58.2858 92.7839 58.6196C91.8512 59.0375 91.6218 59.6088 92.0214 60.5487C92.3275 61.2658 92.6532 61.9781 92.9517 62.6977C93.9337 65.057 92.6654 67.587 90.1931 68.193C89.8971 68.2647 89.5862 68.277 89.2827 68.3141H89.2851Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M80.3604 40.3706C80.2444 41.0903 80.0816 42.1265 79.9089 43.1603C79.7558 44.0629 80.1382 44.6194 81.0462 44.7851C81.9321 44.9458 82.8252 45.0843 83.7085 45.2574C85.5937 45.6309 86.9063 47.2532 86.9014 49.1699C86.8964 51.1014 85.5715 52.6668 83.7085 53.0946C81.4238 53.6189 78.458 51.7147 79.0032 48.3587C79.1315 47.5673 79.2722 46.7784 79.4079 45.987C79.5584 45.1189 79.181 44.5699 78.2952 44.4018C77.4093 44.2361 76.5186 44.0951 75.6329 43.9269C73.7748 43.5757 72.4475 41.993 72.4105 40.0986C72.3734 38.1646 73.6565 36.5447 75.5119 36.102C77.9028 35.5332 80.422 37.3955 80.3604 40.3681V40.3706Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M58.2714 57.0669C60.1491 57.0916 61.7134 58.3331 62.1574 60.1755C62.5794 61.9363 61.7208 63.8159 60.0749 64.6395C59.2682 65.0426 58.4417 65.4135 57.6002 65.7351C56.781 66.0491 56.3443 66.7812 56.8279 67.7803C57.1857 68.5198 57.5114 69.2764 57.8199 70.0382C58.755 72.3357 57.4621 74.8657 55.0663 75.4494C53.191 75.9069 51.2838 74.967 50.4917 73.1939C49.7145 71.4578 50.2722 69.3951 51.829 68.3071C52.13 68.0969 52.4657 67.9336 52.8011 67.7803C53.5093 67.4563 54.2323 67.1645 54.9404 66.838C55.7571 66.4621 55.989 65.8513 55.6362 65.0179C55.2908 64.2041 54.9034 63.4079 54.5777 62.5867C53.6673 60.2991 54.9231 57.8162 57.2991 57.1979C57.6126 57.1163 57.9482 57.1088 58.2714 57.0669Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M69.1035 62.9101C69.111 59.992 71.492 57.6202 74.4036 57.6276C77.3026 57.6351 79.6812 60.034 79.6812 62.9473C79.6812 65.8804 77.2731 68.2719 74.3344 68.257C71.4378 68.2421 69.0937 65.8458 69.1035 62.9101Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M83.8945 39.572C83.8994 37.7988 85.3526 36.3594 87.1168 36.3792C88.8465 36.399 90.2702 37.8408 90.2801 39.5844C90.2899 41.3427 88.8341 42.8068 87.0749 42.8068C85.3107 42.8068 83.8895 41.36 83.8945 39.5745V39.572Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M58.7456 39.5941C58.7456 37.8233 60.1077 36.3865 61.7929 36.3791C63.4929 36.3716 64.9043 37.8505 64.887 39.6238C64.8697 41.3747 63.4756 42.8116 61.7953 42.8042C60.1053 42.7967 58.7482 41.3673 58.7456 39.5941Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M45.4366 62.2305C45.4292 60.5191 46.8256 59.1342 48.58 59.1143C50.3466 59.0945 51.8271 60.5068 51.8222 62.2106C51.8173 63.8973 50.3788 65.2871 48.6317 65.2947C46.8751 65.302 45.4439 63.9294 45.4366 62.2329V62.2305Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M98.659 59.1122C100.401 59.1071 101.852 60.4946 101.869 62.1787C101.886 63.8827 100.421 65.2973 98.6441 65.2899C96.8899 65.2825 95.4785 63.9025 95.481 62.1961C95.4834 60.4946 96.9022 59.1171 98.659 59.1122Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M87.3443 80.8603C89.0837 80.8678 90.5297 82.2699 90.5297 83.9517C90.5297 85.6606 89.059 87.0579 87.2826 87.0381C85.5307 87.0208 84.1341 85.631 84.1441 83.9196C84.1539 82.2229 85.5899 80.8529 87.3468 80.8603H87.3443Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M58.7471 83.9715C58.7422 82.2502 60.1214 80.8529 61.819 80.8603C63.482 80.8678 64.8712 82.2576 64.8859 83.9318C64.9007 85.6234 63.4893 87.0455 61.7968 87.0406C60.1165 87.0357 58.752 85.6631 58.7446 83.9739L58.7471 83.9715Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M17.2184 120.924L17.2141 116.161H67.6866C68.6013 114.482 69.2906 111.828 70.1946 110.17C70.186 110.154 70.169 110.074 70.1245 110.023C68.151 107.75 65.9356 105.851 63.2682 104.656C61.2906 103.772 59.2301 103.403 57.1039 103.581C55.3319 103.728 53.63 104.228 51.9983 105C46.1564 107.764 40.1531 109.997 33.978 111.664C31.2385 112.406 28.482 113.063 25.6873 113.491C23.994 113.75 22.2942 113.972 20.5903 114.141C19.5038 114.248 18.3472 114.336 17.214 114.373C17.1949 114.221 17.2184 113.963 17.2184 113.818C17.2141 111.968 17.2099 110.119 17.2226 108.269C17.2226 108.049 17.2969 107.813 17.3903 107.612C22.5595 96.3247 27.733 85.0422 32.9064 73.7573C33.3351 72.8219 33.7574 71.8795 34.2115 70.8764C34.6974 71.0845 35.1961 71.2974 35.7415 71.5311C31.3425 83.7116 26.9606 95.8407 22.5404 108.073C22.986 108.024 23.3574 107.996 23.7266 107.937C32.7792 106.473 41.4667 103.606 49.872 99.6569C51.8306 98.7357 53.8592 98.0996 55.9749 97.833C59.3828 97.4028 62.655 97.9967 65.7957 99.5026C68.5989 100.85 71.0242 102.802 73.1993 105.154C73.3373 105.304 73.4434 105.489 73.5897 105.692C74.0269 105.239 74.3941 104.853 74.7675 104.474C77.1378 102.054 79.7882 100.129 82.8461 98.9275C85.1823 98.0083 87.595 97.5876 90.0715 97.7209C92.5966 97.8565 95.0221 98.5111 97.3436 99.6102C103.139 102.355 109.098 104.554 115.23 106.169C117.904 106.876 120.599 107.481 123.328 107.876C123.78 107.942 124.23 108.017 124.786 108.103C120.367 95.8711 115.981 83.7303 111.589 71.5686C111.71 71.4961 111.78 71.4399 111.858 71.4072C112.263 71.2272 112.673 71.0541 113.114 70.8647C113.246 71.1757 113.359 71.4563 113.484 71.73C117.185 79.802 120.887 87.874 124.588 95.9436C126.356 99.7973 128.119 103.653 129.891 107.504C130.029 107.806 130.101 108.106 130.099 108.449C130.086 110.163 130.093 111.875 130.093 113.589V114.26C129.885 114.26 129.73 114.265 129.577 114.26C129.153 114.241 128.73 114.204 128.306 114.199C126.326 114.176 124.363 113.935 122.405 113.626C116.578 112.707 110.895 111.129 105.299 109.141C101.925 107.942 98.6148 106.558 95.3553 105.019C93.5409 104.163 91.6545 103.628 89.6725 103.55C86.933 103.443 84.4035 104.236 82.0418 105.73C80.2338 106.873 78.655 108.33 77.2184 109.986C77.1717 110.039 77.1336 110.107 77.1081 110.144C78.0078 111.795 78.7033 114.468 79.6264 116.161H130.093L130.093 120.906H76.3271C75.9431 119.963 75.5569 118.967 75.1325 117.99C74.7059 117.012 74.237 116.058 73.6577 115.104C72.5351 116.961 71.7564 118.934 70.9966 120.924L17.2184 120.92V120.924Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M197.092 84.997L190.692 67.0263C190.51 66.4944 190.275 65.5826 189.989 64.2908C189.703 62.9991 189.404 61.4161 189.092 59.5417C188.753 61.34 188.427 62.961 188.117 64.4047C187.804 65.8231 187.57 66.7729 187.414 67.2542L181.248 84.997H197.092ZM160.72 106.159L182.457 50.5374H196.428L218.477 106.159H204.584L199.94 94.3432H177.814L173.833 106.159H160.72Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M224.722 106.159V50.5374H236.937V106.159H224.722Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M249.347 92.4056V82.8313H271.201V92.4056H249.347Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M295.63 75.6507H297.386C300.716 75.6507 303.135 74.9795 304.645 73.6371C306.153 72.2946 306.908 70.1417 306.908 67.1782C306.908 64.4428 306.153 62.4545 304.645 61.2133C303.135 59.947 300.716 59.3137 297.386 59.3137H295.63V75.6507ZM283.338 106.159V50.5374H297.386C304.879 50.5374 310.407 51.9304 313.971 54.7165C317.562 57.5028 319.358 61.8086 319.358 67.6342C319.358 73.0291 317.55 77.2591 313.933 80.3238C310.343 83.3886 305.36 84.9209 298.986 84.9209H295.63V106.159H283.338Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M369.154 106.159H355.066L340.041 80.1719V106.159H327.825V50.5374H345.309C352.256 50.5374 357.459 51.8544 360.919 54.4886C364.38 57.0975 366.11 61.0361 366.11 66.3044C366.11 70.1291 364.926 73.3965 362.559 76.1066C360.19 78.8168 357.134 80.3998 353.387 80.8557L369.154 106.159ZM340.041 74.245H341.875C346.819 74.245 350.083 73.7257 351.67 72.6873C353.257 71.6235 354.05 69.7871 354.05 67.1782C354.05 64.4428 353.192 62.5052 351.475 61.3654C349.785 60.2002 346.585 59.6176 341.875 59.6176H340.041V74.245Z\",\n    fill: \"black\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M434.793 78.4622C434.793 82.4388 434.026 86.2 432.49 89.746C430.982 93.2921 428.797 96.4075 425.936 99.0923C422.968 101.853 419.626 103.968 415.905 105.437C412.184 106.906 408.335 107.641 404.354 107.641C400.868 107.641 397.447 107.071 394.091 105.931C390.761 104.766 387.703 103.107 384.92 100.954C381.329 98.1678 378.571 94.837 376.647 90.9618C374.747 87.0866 373.797 82.92 373.797 78.4622C373.797 74.4603 374.552 70.7116 376.06 67.2163C377.57 63.6955 379.781 60.5674 382.695 57.832C385.556 55.1218 388.874 53.0195 392.647 51.5251C396.444 50.0308 400.347 49.2836 404.354 49.2836C408.335 49.2836 412.198 50.0308 415.945 51.5251C419.716 53.0195 423.046 55.1218 425.936 57.832C428.823 60.5674 431.02 63.6955 432.53 67.2163C434.038 70.7369 434.793 74.4856 434.793 78.4622ZM404.354 97.0406C409.531 97.0406 413.798 95.2804 417.155 91.7596C420.537 88.2137 422.227 83.7811 422.227 78.4622C422.227 73.1938 420.523 68.7612 417.115 65.1646C413.706 61.5679 409.453 59.7696 404.354 59.7696C399.176 59.7696 394.884 61.5679 391.476 65.1646C388.068 68.7359 386.363 73.1684 386.363 78.4622C386.363 83.8318 388.042 88.2769 391.398 91.7977C394.754 95.293 399.073 97.0406 404.354 97.0406Z\",\n    fill: \"black\"\n  }))), _defs || (_defs = /*#__PURE__*/React.createElement(\"defs\", null, /*#__PURE__*/React.createElement(\"clipPath\", {\n    id: \"clip0_2054_286\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    width: 418,\n    height: 86,\n    fill: \"white\",\n    transform: \"translate(17 36)\"\n  })))));\n}\nconst ForwardRef = /*#__PURE__*/React.forwardRef(SvgAipro);\nexport default __webpack_public_path__ + \"static/media/AIPRO.0580715cba3cbc2179c46b033d087db9.svg\";\nexport { ForwardRef as ReactComponent };", "import { GetCookie } from '../../core/utils/cookies';\r\nexport function getCurrency(currency) {\r\n    if(!currency) return \"\";\r\n    if(currency.toLowerCase() === 'usd') return \"$\";\r\n    if(currency.toLowerCase() === 'eur') return \"€\";\r\n    if(currency.toLowerCase() === 'gbp') return \"£\";\r\n    if(currency.toLowerCase() === 'brl') return \"R$\";\r\n    if(currency.toLowerCase() === 'sar') return \"R$\";\r\n    if(currency.toLowerCase() === 'czk') return \"Kč\";\r\n    return \"\";\r\n}\r\n\r\nexport function getCountry(currency) {\r\n    if(!currency) return \"\";\r\n    if(currency.toLowerCase() === 'usd') return \"US\";\r\n    if(currency.toLowerCase() === 'eur') return \"US\";\r\n    if(currency.toLowerCase() === 'gbp') return \"GB\";\r\n    if(currency.toLowerCase() === 'brl') return \"US\";\r\n    if(currency.toLowerCase() === 'sar') return \"AE\";\r\n    if(currency.toLowerCase() === 'chf') return \"CH\";\r\n    if(currency.toLowerCase() === 'sek') return \"SE\";\r\n    return \"US\";\r\n}\r\n\r\nexport function formatDate(dateStr) {\r\n    if(!dateStr) return \"\";\r\n    const month = [\"January\",\"February\",\"March\",\"April\",\"May\",\"June\",\"July\",\"August\",\"September\",\"October\",\"November\",\"December\"];\r\n    const date = new Date(dateStr.replace(/ /g,\"T\"));\r\n\r\n    return month[date.getMonth()] + \" \" + date.getDate() + \" \" + date.getFullYear();\r\n}\r\n\r\nexport function getPrice(data) {\r\n    if(data.trial_price) return data.trial_price;\r\n    if(data.price) return data.price;\r\n    return \"0\";\r\n}\r\n\r\nexport function numberWithCommas(x) {\r\n    return x.toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, \",\");\r\n}\r\n\r\nexport function formatNumber(number) {\r\n  const floatNumber = parseFloat(number);\r\n  return (floatNumber % 1 === 0)\r\n      ? numberWithCommas(floatNumber.toFixed(0))\r\n      : numberWithCommas(floatNumber.toFixed(2)); \r\n}\r\n\r\nexport function getPricePlan(currency, price) {\r\n    if(!currency || !price) return \"\";\r\n    // Check if price is a number\r\n    if(isNaN(price)) return \"\";\r\n    // Check if price is positive number\r\n    if(parseFloat(price) >= 0) {\r\n        if(currency.toLowerCase() === 'sar') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"ر.س.\";\r\n        } else if(currency.toLowerCase() === 'aed') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"AED\";\r\n        } else if(currency.toLowerCase() === 'chf') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"CHF\";\r\n        } else if(currency.toLowerCase() === 'sek') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"SEK\";\r\n        } else if(currency.toLowerCase() === 'pln') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"zł\";\r\n        }else if(currency.toLowerCase() === 'ron') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"LEI\";\r\n        } else if(currency.toLowerCase() === 'huf') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"Ft\";\r\n        } else if(currency.toLowerCase() === 'dkk') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"kr.\";\r\n        } else if(currency.toLowerCase() === 'bgn') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"лв\";\r\n        } else if(currency.toLowerCase() === 'try') {\r\n            return formatNumber(price).toLocaleString(\"en-US\") + \"₺\";\r\n        }\r\n        return getCurrency(currency) + formatNumber(price).toLocaleString(\"en-US\");\r\n    }\r\n    // Negative number\r\n    return \"-\" + getCurrency(currency) + (formatNumber(price) * -1).toLocaleString(\"en-US\");\r\n}\r\n\r\nexport function diffMin(dt1, dt2)\r\n{\r\n    dt1 = new Date(dt1);\r\n    dt2 = new Date(dt2);\r\n    var diff =(dt2.getTime() - dt1.getTime()) / 1000;\r\n    diff /= 60;\r\n    return Math.abs(Math.round(diff));\r\n}\r\n\r\nexport function formatDateTime(dateStr) {\r\n    if(!dateStr) return \"\";\r\n    const month = [\"January\",\"February\",\"March\",\"April\",\"May\",\"June\",\"July\",\"August\",\"September\",\"October\",\"November\",\"December\"];\r\n    const date = new Date(dateStr.replace(/ /g,\"T\"));\r\n\r\n    return month[date.getMonth()] + \" \" + date.getDate() + \" \" + date.getFullYear() + \" \" + date.getHours() + \":\" + date.getMinutes();\r\n}\r\n\r\nexport function DailyPrice({plan}) {\r\n  let dailyPrice = '';\r\n  let interval = '';\r\n  if (plan.payment_interval === 'Yearly') {\r\n    dailyPrice = getPricePlan(plan.currency, parseFloat(plan.price / 365).toFixed(2));\r\n    interval = <div class=\"text-xs mb-4\">billed {getPricePlan(plan.currency, plan.price)}/year</div>;\r\n  }\r\n\r\n  if (plan.payment_interval === 'Monthly') {\r\n    dailyPrice = getPricePlan(plan.currency, parseFloat(plan.price / 30).toFixed(2));\r\n    interval = <div class=\"text-xs mb-4\">billed {getPricePlan(plan.currency, plan.price)}/Month</div>;\r\n  }\r\n\r\n  if (plan.trial_price) {\r\n    dailyPrice = getPricePlan(plan.currency, parseFloat(plan.trial_price / plan.trial_days).toFixed(2));\r\n    interval = <div class=\"text-xs mb-4\">billed {getPricePlan(plan.currency, plan.trial_price)}</div>;\r\n  }\r\n\r\n  return (\r\n    <>\r\n      <p className={`text-4xl font-bold text-gray-800 ${(GetCookie(\"p_toggle\") === '') ? 'mb-4' : ''}`}>\r\n        {dailyPrice} <span className=\"text-sm\"> per Day</span>\r\n      </p>\r\n      {interval}\r\n    </>\r\n  );\r\n}\r\n\r\nexport function PriceFormatted({plan}) {\r\n  if (GetCookie(\"daily\") === 'on') {\r\n    return DailyPrice({plan});\r\n  } \r\n  if (plan.trial_price) {\r\n    return (<p className=\"text-4xl font-bold text-gray-800 mb-4\">{getPricePlan(plan.currency, plan.trial_price)}</p>)\r\n  } \r\n  return (\r\n    <p className=\"text-4xl font-bold text-gray-800 mb-4\">\r\n      {getPricePlan(plan.currency, plan.price)}\r\n      <span className=\"text-sm\"> \r\n        /{ plan.payment_interval === \"Monthly\" ? \"month\" : \"year\" }\r\n      </span>\r\n    </p>\r\n  )\r\n}\r\n\r\nexport function displayTextFormatted(plan) {\r\n  const locales = (GetCookie('locales') ?? 'en').toLowerCase();\r\n  const daily = (GetCookie('daily') ?? 'off').toLowerCase();\r\n  const { trial_days, payment_interval, trial_price, currency, currency_symbol } = plan;\r\n  let { display_txt2: pricing_description, price } = plan;\r\n\r\n  if (trial_days > 0 && trial_price > 0 && locales === 'en') {\r\n    const per_day = payment_interval === 'Yearly' ? 365 : 30;\r\n    let amount = price;\r\n    let price_text = 'month';\r\n\r\n    if (daily === 'on') {\r\n      amount = parseFloat(price / per_day).toFixed(2);\r\n      price_text = `day<br>(billed ${currency_symbol + price} ${payment_interval})`;\r\n    }\r\n\r\n    pricing_description += `<div>${trial_days}-Day Trial, then only ${getPricePlan(currency, amount)} per ${price_text}</div>`;\r\n  }\r\n\r\n  return pricing_description;\r\n}", "import React, { useEffect } from 'react';\r\nimport aiproLogo from '../assets/images/AIPRO.svg';\r\nimport aiproLogoWebP from '../assets/images/AIPRO.webp';\r\nimport { Auth } from '../core/utils/auth';\r\nimport './style.css';\r\n\r\nfunction HeaderLogo({ hideNavLink, auth = Auth() }) {\r\n  const pathname = window.location.pathname;\r\n  const isChatGptGo = /\\/start-chatgpt-go\\/?$/.test(pathname);\r\n  const isTexttoImage = /\\/text-to-image\\/?$/.test(pathname);\r\n  const isChatGptV2 = /\\/start-chatgpt-v2\\/?$/.test(pathname);\r\n  const isRegisterPage = pathname.includes('/register');\r\n  const showLink = !hideNavLink;\r\n  const showMaintenanceBanner = process.env.REACT_APP_ShowMaintenanceBanner || '';\r\n\r\n  useEffect(() => {\r\n    const linkPNG = createPreloadLink(aiproLogo, 'image');\r\n    const linkWebP = createPreloadLink(aiproLogoWebP, 'image');\r\n    document.head.append(linkPNG, linkWebP);\r\n\r\n    if (!isRegisterPage) {\r\n      import('@fortawesome/fontawesome-free/css/all.css');\r\n    }\r\n    return () => {\r\n      linkPNG.remove();\r\n      linkWebP.remove();\r\n    };\r\n  }, [isRegisterPage]);\r\n\r\n\r\n  const createPreloadLink = (href, as) => {\r\n    const link = document.createElement('link');\r\n    link.rel = 'preload';\r\n    link.href = href;\r\n    link.as = as;\r\n    return link;\r\n  };\r\n\r\n  return (\r\n    <>\r\n      {showMaintenanceBanner && (\r\n        <div id=\"maintenance-container\" className=\"p-[5px] bg-[#f7a73f] text-center\">\r\n          We are currently undergoing maintenance. We're expecting to be back at 4 AM EST.\r\n        </div>\r\n      )}\r\n      <header id=\"header\" className={`headernav border-gray-500 bg-white text-black w-3/4 mx-auto rounded-3xl mt-3 py-2 z-[8888] ${showMaintenanceBanner ? 'top-[60px]' : ''}`}>\r\n        <div className=\"container mx-auto flex justify-between items-center px-4\">\r\n          <picture className=\"aiprologo\">\r\n            <source type=\"image/webp\" srcSet={aiproLogoWebP} width=\"150\" height=\"52\" className=\"aiprologo\" />\r\n            <img src={aiproLogo} alt=\"AI-Pro Logo\" className=\"aiprologo\"/>\r\n          </picture>\r\n          {(isChatGptGo || isTexttoImage || isChatGptV2) && showLink && (\r\n            <nav className=\"text-xs lg:text-sm block inline-flex\" id=\"menu\">\r\n              <ul className=\"headnav flex inline-flex\">\r\n                <li className=\"mr-1 md:mr-2 lg:mr-6\">\r\n                  <a href={auth ? '/my-account' : '/login'} className=\"font-bold\" aria-label={auth ? 'my-account' : 'login'}>\r\n                    {auth ? 'My Apps' : 'LOG IN'}\r\n                  </a>\r\n                </li>\r\n              </ul>\r\n            </nav>\r\n          )}\r\n        </div>\r\n      </header>\r\n    </>\r\n  );\r\n}\r\n\r\nexport default HeaderLogo;\r\n"], "names": ["getCurrency", "currency", "toLowerCase", "getCountry", "formatDate", "dateStr", "date", "Date", "replace", "getMonth", "getDate", "getFullYear", "getPrice", "data", "trial_price", "price", "numberWithCommas", "x", "toString", "formatNumber", "number", "floatNumber", "parseFloat", "toFixed", "getPricePlan", "isNaN", "toLocaleString", "diffMin", "dt1", "dt2", "diff", "getTime", "Math", "abs", "round", "formatDateTime", "getHours", "getMinutes", "DailyPrice", "_ref", "plan", "dailyPrice", "interval", "payment_interval", "_jsxs", "class", "children", "trial_days", "_Fragment", "className", "concat", "Get<PERSON><PERSON><PERSON>", "_jsx", "PriceFormatted", "_ref2", "displayTextFormatted", "_<PERSON><PERSON><PERSON><PERSON>", "_GetCookie2", "locales", "daily", "currency_symbol", "display_txt2", "pricing_description", "amount", "price_text", "hideNavLink", "auth", "<PERSON><PERSON>", "pathname", "window", "location", "isChatGptGo", "test", "isTexttoImage", "isChatGptV2", "isRegisterPage", "includes", "showLink", "useEffect", "linkPNG", "createPreloadLink", "aiproLogo", "linkWebP", "aiproLogoWebP", "document", "head", "append", "remove", "href", "as", "link", "createElement", "rel", "id", "type", "srcSet", "width", "height", "src", "alt"], "sourceRoot": ""}